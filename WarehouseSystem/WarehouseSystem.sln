Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
VisualStudioVersion = 16.0.30114.105
MinimumVisualStudioVersion = 10.0.40219.1
Project("{E24C65DC-7377-472B-9ABA-BC803B73C61A}") = "web", "web\", "{CA02FAC3-1DD8-42D9-80A8-3F670A845C3A}"
	ProjectSection(WebsiteProperties) = preProject
		TargetFrameworkMoniker = ".NETFramework,Version%3Dv4.8"
		Debug.AspNetCompiler.VirtualPath = "/web"
		Debug.AspNetCompiler.PhysicalPath = "web\"
		Debug.AspNetCompiler.TargetPath = "PrecompiledWeb\web\"
		Debug.AspNetCompiler.Updateable = "true"
		Debug.AspNetCompiler.ForceOverwrite = "true"
		Debug.AspNetCompiler.FixedNames = "false"
		Debug.AspNetCompiler.Debug = "True"
		Release.AspNetCompiler.VirtualPath = "/web"
		Release.AspNetCompiler.PhysicalPath = "web\"
		Release.AspNetCompiler.TargetPath = "PrecompiledWeb\web\"
		Release.AspNetCompiler.Updateable = "true"
		Release.AspNetCompiler.ForceOverwrite = "true"
		Release.AspNetCompiler.FixedNames = "false"
		Release.AspNetCompiler.Debug = "False"
		VWDPort = "57203"
		VWDDynamicPort = "true"
		SlnRelativePath = "web\"
		StartServerOnDebug = "true"
	EndProjectSection
	ProjectSection(ProjectDependencies) = postProject
		{2937E36E-94A5-4D96-B769-22FC2575D863} = {2937E36E-94A5-4D96-B769-22FC2575D863}
		{A11A29B4-DF9C-4925-9A5B-FCA40A27219A} = {A11A29B4-DF9C-4925-9A5B-FCA40A27219A}
	EndProjectSection
EndProject
Project("{F184B08F-C81C-45F6-A57F-5ABD9991F28F}") = "Warehouse.Application.Logic", "dll\Warehouse.Application.Logic\Warehouse.Application.Logic.vbproj", "{2937E36E-94A5-4D96-B769-22FC2575D863}"
	ProjectSection(ProjectDependencies) = postProject
		{A11A29B4-DF9C-4925-9A5B-FCA40A27219A} = {A11A29B4-DF9C-4925-9A5B-FCA40A27219A}
	EndProjectSection
EndProject
Project("{F184B08F-C81C-45F6-A57F-5ABD9991F28F}") = "Warehouse.Application.Logic.Database", "dll\Warehouse.Application.Logic.Database\Warehouse.Application.Logic.Database.vbproj", "{A11A29B4-DF9C-4925-9A5B-FCA40A27219A}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|.NET = Debug|.NET
		Debug|Any CPU = Debug|Any CPU
		Debug|Mixed Platforms = Debug|Mixed Platforms
		Release|.NET = Release|.NET
		Release|Any CPU = Release|Any CPU
		Release|Mixed Platforms = Release|Mixed Platforms
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{CA02FAC3-1DD8-42D9-80A8-3F670A845C3A}.Debug|.NET.ActiveCfg = Debug|Any CPU
		{CA02FAC3-1DD8-42D9-80A8-3F670A845C3A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CA02FAC3-1DD8-42D9-80A8-3F670A845C3A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CA02FAC3-1DD8-42D9-80A8-3F670A845C3A}.Debug|Mixed Platforms.ActiveCfg = Debug|Any CPU
		{CA02FAC3-1DD8-42D9-80A8-3F670A845C3A}.Debug|Mixed Platforms.Build.0 = Debug|Any CPU
		{CA02FAC3-1DD8-42D9-80A8-3F670A845C3A}.Release|.NET.ActiveCfg = Debug|Any CPU
		{CA02FAC3-1DD8-42D9-80A8-3F670A845C3A}.Release|Any CPU.ActiveCfg = Debug|Any CPU
		{CA02FAC3-1DD8-42D9-80A8-3F670A845C3A}.Release|Any CPU.Build.0 = Debug|Any CPU
		{CA02FAC3-1DD8-42D9-80A8-3F670A845C3A}.Release|Mixed Platforms.ActiveCfg = Debug|Any CPU
		{CA02FAC3-1DD8-42D9-80A8-3F670A845C3A}.Release|Mixed Platforms.Build.0 = Debug|Any CPU
		{2937E36E-94A5-4D96-B769-22FC2575D863}.Debug|.NET.ActiveCfg = Debug|Any CPU
		{2937E36E-94A5-4D96-B769-22FC2575D863}.Debug|.NET.Build.0 = Debug|Any CPU
		{2937E36E-94A5-4D96-B769-22FC2575D863}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2937E36E-94A5-4D96-B769-22FC2575D863}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2937E36E-94A5-4D96-B769-22FC2575D863}.Debug|Mixed Platforms.ActiveCfg = Debug|Any CPU
		{2937E36E-94A5-4D96-B769-22FC2575D863}.Debug|Mixed Platforms.Build.0 = Debug|Any CPU
		{2937E36E-94A5-4D96-B769-22FC2575D863}.Release|.NET.ActiveCfg = Release|Any CPU
		{2937E36E-94A5-4D96-B769-22FC2575D863}.Release|.NET.Build.0 = Release|Any CPU
		{2937E36E-94A5-4D96-B769-22FC2575D863}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2937E36E-94A5-4D96-B769-22FC2575D863}.Release|Any CPU.Build.0 = Release|Any CPU
		{2937E36E-94A5-4D96-B769-22FC2575D863}.Release|Mixed Platforms.ActiveCfg = Release|Any CPU
		{2937E36E-94A5-4D96-B769-22FC2575D863}.Release|Mixed Platforms.Build.0 = Release|Any CPU
		{A11A29B4-DF9C-4925-9A5B-FCA40A27219A}.Debug|.NET.ActiveCfg = Debug|Any CPU
		{A11A29B4-DF9C-4925-9A5B-FCA40A27219A}.Debug|.NET.Build.0 = Debug|Any CPU
		{A11A29B4-DF9C-4925-9A5B-FCA40A27219A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A11A29B4-DF9C-4925-9A5B-FCA40A27219A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A11A29B4-DF9C-4925-9A5B-FCA40A27219A}.Debug|Mixed Platforms.ActiveCfg = Debug|Any CPU
		{A11A29B4-DF9C-4925-9A5B-FCA40A27219A}.Debug|Mixed Platforms.Build.0 = Debug|Any CPU
		{A11A29B4-DF9C-4925-9A5B-FCA40A27219A}.Release|.NET.ActiveCfg = Release|Any CPU
		{A11A29B4-DF9C-4925-9A5B-FCA40A27219A}.Release|.NET.Build.0 = Release|Any CPU
		{A11A29B4-DF9C-4925-9A5B-FCA40A27219A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A11A29B4-DF9C-4925-9A5B-FCA40A27219A}.Release|Any CPU.Build.0 = Release|Any CPU
		{A11A29B4-DF9C-4925-9A5B-FCA40A27219A}.Release|Mixed Platforms.ActiveCfg = Release|Any CPU
		{A11A29B4-DF9C-4925-9A5B-FCA40A27219A}.Release|Mixed Platforms.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {AD69DA95-B92C-4011-A5FA-048B9DD0CA3A}
	EndGlobalSection
EndGlobal
