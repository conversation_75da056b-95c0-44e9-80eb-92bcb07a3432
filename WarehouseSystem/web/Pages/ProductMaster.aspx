<%@ Page Language="VB" AutoEventWireup="false" CodeFile="ProductMaster.aspx.vb" Inherits="Pages_ProductMaster" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>Барааны бүртгэл - Агуулахын бүртгэлийн систем</title>
    <link href="../css/default.css" rel="stylesheet" type="text/css" />
    <script src="../js/jquery.js" type="text/javascript"></script>
</head>
<body>
    <form id="form1" runat="server">
        <div class="header">
            <h1>Барааны бүртгэл</h1>
            <div class="user-info">
                <asp:Label ID="lblUserName" runat="server" Text="Хэрэглэгч: "></asp:Label>
                <asp:LinkButton ID="lnkBack" runat="server" Text="Буцах" CssClass="logout-link"></asp:LinkButton>
            </div>
        </div>
        
        <div class="main-container">
            <div class="content" style="margin-left: 0;">
                <div class="page-header">
                    <h2>Барааны удирдлага</h2>
                    <asp:Button ID="btnAddNew" runat="server" Text="Шинэ бараа нэмэх" CssClass="btn btn-primary" />
                </div>
                
                <!-- Хайлтын хэсэг -->
                <div class="search-section">
                    <div class="search-row">
                        <asp:Label ID="lblSearch" runat="server" Text="Хайх:" CssClass="search-label"></asp:Label>
                        <asp:TextBox ID="txtSearch" runat="server" CssClass="search-input" placeholder="Барааны код эсвэл нэр..."></asp:TextBox>
                        <asp:Button ID="btnSearch" runat="server" Text="Хайх" CssClass="btn btn-search" />
                        <asp:Button ID="btnClear" runat="server" Text="Цэвэрлэх" CssClass="btn btn-secondary" />
                    </div>
                </div>
                
                <!-- Барааны жагсаалт -->
                <div class="grid-section">
                    <asp:GridView ID="gvProducts" runat="server" CssClass="data-grid" AutoGenerateColumns="false" 
                                  AllowPaging="true" PageSize="20" AllowSorting="true">
                        <Columns>
                            <asp:BoundField DataField="ProductCode" HeaderText="Барааны код" SortExpression="ProductCode" />
                            <asp:BoundField DataField="ProductName" HeaderText="Барааны нэр" SortExpression="ProductName" />
                            <asp:BoundField DataField="CategoryName" HeaderText="Ангилал" />
                            <asp:BoundField DataField="UnitOfMeasure" HeaderText="Хэмжих нэгж" />
                            <asp:BoundField DataField="UnitPrice" HeaderText="Нэгжийн үнэ" DataFormatString="{0:N2}" />
                            <asp:BoundField DataField="MinimumStock" HeaderText="Хамгийн бага үлдэгдэл" />
                            <asp:BoundField DataField="MaximumStock" HeaderText="Хамгийн их үлдэгдэл" />
                            <asp:TemplateField HeaderText="Үйлдэл">
                                <ItemTemplate>
                                    <asp:LinkButton ID="lnkEdit" runat="server" Text="Засах" CssClass="btn-link edit" 
                                                    CommandName="EditProduct" CommandArgument='<%# Eval("ProductID") %>'></asp:LinkButton>
                                    <asp:LinkButton ID="lnkDelete" runat="server" Text="Устгах" CssClass="btn-link delete" 
                                                    CommandName="DeleteProduct" CommandArgument='<%# Eval("ProductID") %>'
                                                    OnClientClick="return confirm('Энэ бараа устгахдаа итгэлтэй байна уу?');"></asp:LinkButton>
                                </ItemTemplate>
                            </asp:TemplateField>
                        </Columns>
                        <PagerStyle CssClass="pager" />
                        <EmptyDataTemplate>
                            <div class="empty-data">Бараа олдсонгүй.</div>
                        </EmptyDataTemplate>
                    </asp:GridView>
                </div>
                
                <!-- Бараа нэмэх/засах Modal -->
                <div id="productModal" class="modal" style="display: none;">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3 id="modalTitle">Шинэ бараа нэмэх</h3>
                            <span class="close" onclick="closeModal()">&times;</span>
                        </div>
                        <div class="modal-body">
                            <asp:HiddenField ID="hfProductID" runat="server" Value="0" />
                            
                            <div class="form-row">
                                <asp:Label ID="lblProductCode" runat="server" Text="Барааны код:" CssClass="form-label"></asp:Label>
                                <asp:TextBox ID="txtProductCode" runat="server" CssClass="form-input" MaxLength="50"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfvProductCode" runat="server" ControlToValidate="txtProductCode" 
                                                          ErrorMessage="Барааны код оруулна уу" CssClass="error-message" Display="Dynamic"></asp:RequiredFieldValidator>
                            </div>
                            
                            <div class="form-row">
                                <asp:Label ID="lblProductName" runat="server" Text="Барааны нэр:" CssClass="form-label"></asp:Label>
                                <asp:TextBox ID="txtProductName" runat="server" CssClass="form-input" MaxLength="200"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfvProductName" runat="server" ControlToValidate="txtProductName" 
                                                          ErrorMessage="Барааны нэр оруулна уу" CssClass="error-message" Display="Dynamic"></asp:RequiredFieldValidator>
                            </div>
                            
                            <div class="form-row">
                                <asp:Label ID="lblCategory" runat="server" Text="Ангилал:" CssClass="form-label"></asp:Label>
                                <asp:DropDownList ID="ddlCategory" runat="server" CssClass="form-select">
                                    <asp:ListItem Value="1" Text="Хүнсний бүтээгдэхүүн"></asp:ListItem>
                                    <asp:ListItem Value="2" Text="Ундаа"></asp:ListItem>
                                    <asp:ListItem Value="3" Text="Гэр ахуйн бараа"></asp:ListItem>
                                    <asp:ListItem Value="4" Text="Цахилгаан бараа"></asp:ListItem>
                                </asp:DropDownList>
                            </div>
                            
                            <div class="form-row">
                                <asp:Label ID="lblUnitOfMeasure" runat="server" Text="Хэмжих нэгж:" CssClass="form-label"></asp:Label>
                                <asp:TextBox ID="txtUnitOfMeasure" runat="server" CssClass="form-input" MaxLength="20"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfvUnitOfMeasure" runat="server" ControlToValidate="txtUnitOfMeasure" 
                                                          ErrorMessage="Хэмжих нэгж оруулна уу" CssClass="error-message" Display="Dynamic"></asp:RequiredFieldValidator>
                            </div>
                            
                            <div class="form-row">
                                <asp:Label ID="lblUnitPrice" runat="server" Text="Нэгжийн үнэ:" CssClass="form-label"></asp:Label>
                                <asp:TextBox ID="txtUnitPrice" runat="server" CssClass="form-input" TextMode="Number"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfvUnitPrice" runat="server" ControlToValidate="txtUnitPrice" 
                                                          ErrorMessage="Нэгжийн үнэ оруулна уу" CssClass="error-message" Display="Dynamic"></asp:RequiredFieldValidator>
                            </div>
                            
                            <div class="form-row">
                                <asp:Label ID="lblMinStock" runat="server" Text="Хамгийн бага үлдэгдэл:" CssClass="form-label"></asp:Label>
                                <asp:TextBox ID="txtMinStock" runat="server" CssClass="form-input" TextMode="Number"></asp:TextBox>
                            </div>
                            
                            <div class="form-row">
                                <asp:Label ID="lblMaxStock" runat="server" Text="Хамгийн их үлдэгдэл:" CssClass="form-label"></asp:Label>
                                <asp:TextBox ID="txtMaxStock" runat="server" CssClass="form-input" TextMode="Number"></asp:TextBox>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <asp:Button ID="btnSave" runat="server" Text="Хадгалах" CssClass="btn btn-primary" />
                            <button type="button" class="btn btn-secondary" onclick="closeModal()">Болих</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
    
    <script type="text/javascript">
        function showModal() {
            document.getElementById('productModal').style.display = 'block';
        }
        
        function closeModal() {
            document.getElementById('productModal').style.display = 'none';
        }
        
        // Modal-ийн гадна дарахад хаах
        window.onclick = function(event) {
            var modal = document.getElementById('productModal');
            if (event.target == modal) {
                modal.style.display = 'none';
            }
        }
    </script>
</body>
</html>
