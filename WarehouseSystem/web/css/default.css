/* Агуулахын бүртгэлийн систем - Үндсэн CSS */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

/* Header */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header h1 {
    font-size: 1.8rem;
    font-weight: 600;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logout-link {
    color: white;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border: 1px solid rgba(255,255,255,0.3);
    border-radius: 4px;
    transition: all 0.3s ease;
}

.logout-link:hover {
    background-color: rgba(255,255,255,0.1);
    text-decoration: none;
}

/* Main Container */
.main-container {
    display: flex;
    min-height: calc(100vh - 80px);
}

/* Sidebar */
.sidebar {
    width: 280px;
    background-color: white;
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
    padding: 2rem 0;
}

.menu-section {
    margin-bottom: 2rem;
}

.menu-section h3 {
    color: #667eea;
    font-size: 1.1rem;
    font-weight: 600;
    padding: 0 1.5rem;
    margin-bottom: 1rem;
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 0.5rem;
}

.menu-list {
    list-style: none;
}

.menu-list li {
    margin-bottom: 0.5rem;
}

.menu-list a {
    display: block;
    padding: 0.8rem 1.5rem;
    color: #555;
    text-decoration: none;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.menu-list a:hover {
    background-color: #f8f9ff;
    color: #667eea;
    border-left-color: #667eea;
    text-decoration: none;
}

/* Content Area */
.content {
    flex: 1;
    padding: 2rem;
    background-color: #f5f5f5;
}

/* Dashboard */
.dashboard h2 {
    color: #333;
    margin-bottom: 2rem;
    font-size: 1.5rem;
    font-weight: 600;
}

/* Dashboard Cards */
.dashboard-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.card {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
}

.card h3 {
    color: #666;
    font-size: 1rem;
    margin-bottom: 1rem;
    font-weight: 500;
}

.card-value {
    font-size: 2.5rem;
    font-weight: bold;
    color: #667eea;
}

/* Recent Activities */
.recent-activities {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.recent-activities h3 {
    color: #333;
    margin-bottom: 1.5rem;
    font-size: 1.2rem;
    font-weight: 600;
}

/* Data Grid */
.data-grid {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

.data-grid th {
    background-color: #667eea;
    color: white;
    padding: 1rem;
    text-align: left;
    font-weight: 600;
}

.data-grid td {
    padding: 0.8rem 1rem;
    border-bottom: 1px solid #eee;
}

.data-grid tr:hover {
    background-color: #f8f9ff;
}

/* Page Header */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f0f0f0;
}

/* Buttons */
.btn {
    padding: 0.8rem 1.5rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: #667eea;
    color: white;
}

.btn-primary:hover {
    background-color: #5a6fd8;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #5a6268;
}

.btn-search {
    background-color: #28a745;
    color: white;
}

.btn-search:hover {
    background-color: #218838;
}

/* Search Section */
.search-section {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.search-row {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.search-label {
    font-weight: 600;
    color: #333;
    min-width: 60px;
}

.search-input {
    flex: 1;
    padding: 0.8rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
}

/* Grid Section */
.grid-section {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* Button Links */
.btn-link {
    padding: 0.4rem 0.8rem;
    margin: 0 0.2rem;
    border-radius: 3px;
    text-decoration: none;
    font-size: 0.8rem;
    font-weight: 500;
}

.btn-link.edit {
    background-color: #17a2b8;
    color: white;
}

.btn-link.edit:hover {
    background-color: #138496;
    text-decoration: none;
}

.btn-link.delete {
    background-color: #dc3545;
    color: white;
}

.btn-link.delete:hover {
    background-color: #c82333;
    text-decoration: none;
}

/* Pager */
.pager {
    text-align: center;
    padding: 1rem;
}

.pager a, .pager span {
    padding: 0.5rem 0.8rem;
    margin: 0 0.2rem;
    border: 1px solid #ddd;
    text-decoration: none;
    color: #667eea;
}

.pager a:hover {
    background-color: #f8f9ff;
}

.pager span {
    background-color: #667eea;
    color: white;
    border-color: #667eea;
}

/* Empty Data */
.empty-data {
    text-align: center;
    padding: 3rem;
    color: #666;
    font-style: italic;
}

/* Modal */
.modal {
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 8px;
    width: 80%;
    max-width: 600px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
}

.modal-header {
    padding: 1.5rem;
    background-color: #667eea;
    color: white;
    border-radius: 8px 8px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.2rem;
}

.close {
    font-size: 1.5rem;
    font-weight: bold;
    cursor: pointer;
    color: white;
}

.close:hover {
    opacity: 0.7;
}

.modal-body {
    padding: 2rem;
}

.modal-footer {
    padding: 1.5rem;
    border-top: 1px solid #eee;
    text-align: right;
}

.modal-footer .btn {
    margin-left: 0.5rem;
}

/* Form Elements */
.form-row {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #333;
}

.form-input, .form-select {
    width: 100%;
    padding: 0.8rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
}

.form-input:focus, .form-select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

/* Error Messages */
.error-message {
    color: #dc3545;
    font-size: 0.8rem;
    margin-top: 0.3rem;
    display: block;
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-container {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        order: 2;
    }

    .content {
        order: 1;
    }

    .header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .dashboard-cards {
        grid-template-columns: 1fr;
    }

    .search-row {
        flex-direction: column;
        align-items: stretch;
    }

    .search-label {
        min-width: auto;
    }

    .page-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .modal-content {
        width: 95%;
        margin: 2% auto;
    }
}
