<%@ Page Language="VB" AutoEventWireup="false" CodeFile="Default.aspx.vb" Inherits="_Default" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>Агуулахын бүртгэлийн систем</title>
    <link href="css/default.css" rel="stylesheet" type="text/css" />
    <script src="js/jquery.js" type="text/javascript"></script>
</head>
<body>
    <form id="form1" runat="server">
        <div class="header">
            <h1>Агуулахын бүртгэлийн систем</h1>
            <div class="user-info">
                <asp:Label ID="lblUserName" runat="server" Text="Хэрэглэгч: "></asp:Label>
                <asp:LinkButton ID="lnkLogout" runat="server" Text="Гарах" CssClass="logout-link"></asp:LinkButton>
            </div>
        </div>
        
        <div class="main-container">
            <div class="sidebar">
                <div class="menu-section">
                    <h3>Үндсэн цэс</h3>
                    <ul class="menu-list">
                        <li><a href="Pages/ProductMaster.aspx">Барааны бүртгэл</a></li>
                        <li><a href="Pages/WarehouseMaster.aspx">Агуулахын бүртгэл</a></li>
                        <li><a href="Pages/SupplierMaster.aspx">Нийлүүлэгчийн бүртгэл</a></li>
                        <li><a href="Pages/CustomerMaster.aspx">Үйлчлүүлэгчийн бүртгэл</a></li>
                    </ul>
                </div>
                
                <div class="menu-section">
                    <h3>Орлого/Гарлага</h3>
                    <ul class="menu-list">
                        <li><a href="Pages/StockIn.aspx">Бараа орлого</a></li>
                        <li><a href="Pages/StockOut.aspx">Бараа гарлага</a></li>
                        <li><a href="Pages/StockTransfer.aspx">Агуулах хооронд шилжүүлэх</a></li>
                        <li><a href="Pages/StockAdjustment.aspx">Тооллого</a></li>
                    </ul>
                </div>
                
                <div class="menu-section">
                    <h3>Тайлан</h3>
                    <ul class="menu-list">
                        <li><a href="Pages/StockReport.aspx">Үлдэгдлийн тайлан</a></li>
                        <li><a href="Pages/MovementReport.aspx">Хөдөлгөөний тайлан</a></li>
                        <li><a href="Pages/ValueReport.aspx">Үнийн дүнгийн тайлан</a></li>
                    </ul>
                </div>
                
                <div class="menu-section">
                    <h3>Систем</h3>
                    <ul class="menu-list">
                        <li><a href="Pages/UserMaster.aspx">Хэрэглэгчийн удирдлага</a></li>
                        <li><a href="Pages/SystemSettings.aspx">Системийн тохиргоо</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="content">
                <div class="dashboard">
                    <h2>Хяналтын самбар</h2>
                    
                    <div class="dashboard-cards">
                        <div class="card">
                            <h3>Нийт бараа</h3>
                            <div class="card-value">
                                <asp:Label ID="lblTotalProducts" runat="server" Text="0"></asp:Label>
                            </div>
                        </div>
                        
                        <div class="card">
                            <h3>Нийт агуулах</h3>
                            <div class="card-value">
                                <asp:Label ID="lblTotalWarehouses" runat="server" Text="0"></asp:Label>
                            </div>
                        </div>
                        
                        <div class="card">
                            <h3>Өнөөдрийн орлого</h3>
                            <div class="card-value">
                                <asp:Label ID="lblTodayStockIn" runat="server" Text="0"></asp:Label>
                            </div>
                        </div>
                        
                        <div class="card">
                            <h3>Өнөөдрийн гарлага</h3>
                            <div class="card-value">
                                <asp:Label ID="lblTodayStockOut" runat="server" Text="0"></asp:Label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="recent-activities">
                        <h3>Сүүлийн үйл ажиллагаа</h3>
                        <asp:GridView ID="gvRecentActivities" runat="server" CssClass="data-grid" AutoGenerateColumns="false">
                            <Columns>
                                <asp:BoundField DataField="ActivityDate" HeaderText="Огноо" DataFormatString="{0:yyyy-MM-dd HH:mm}" />
                                <asp:BoundField DataField="ActivityType" HeaderText="Төрөл" />
                                <asp:BoundField DataField="Description" HeaderText="Тайлбар" />
                                <asp:BoundField DataField="UserName" HeaderText="Хэрэглэгч" />
                            </Columns>
                        </asp:GridView>
                    </div>
                </div>
            </div>
        </div>
    </form>
</body>
</html>
