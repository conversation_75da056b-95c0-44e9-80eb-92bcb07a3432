<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <section name="DBSettings" type="System.Configuration.NameValueSectionHandler" />
  </configSections>
  <DBSettings>
    <add key="ConnectionString" value="Server=localhost;Database=WarehouseDB;Integrated Security=True;" />
  </DBSettings>
  <system.web>
    <sessionState mode="SQLServer" sqlConnectionString="data source=localhost;Integrated Security=True" cookieless="UseDeviceProfile" timeout="1440" />
    <compilation debug="true" targetFramework="4.8">
      <assemblies>
        <add assembly="System, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
        <add assembly="System.Data, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
        <add assembly="System.Xml, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
        <add assembly="System.Web, Version=*******, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
        <add assembly="System.Configuration, Version=*******, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
        <add assembly="System.Transactions, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
        <add assembly="System.Design, Version=*******, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
        <add assembly="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
        <add assembly="System.Drawing.Design, Version=*******, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
        <add assembly="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
        <add assembly="System.Runtime.Serialization, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
        <add assembly="System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
        <add assembly="System.Web.Services, Version=*******, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
        <add assembly="System.Data.Linq, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
        <add assembly="System.ServiceModel.Web, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
        <add assembly="System.Data.Services.Client, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
        <add assembly="System.Data.Services.Design, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
        <add assembly="System.Data.Entity, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
        <add assembly="System.Security, Version=*******, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
        <add assembly="System.Net.Http, Version=4.2.0.0, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
        <add assembly="System.Numerics, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
        <add assembly="System.Web.Routing, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
      </assemblies>
    </compilation>
    <httpRuntime maxRequestLength="100000" executionTimeout="1800" requestValidationMode="2.0" targetFramework="4.8" />
    <pages validateRequest="false" enableEventValidation="false" clientIDMode="AutoID">
      <controls>
        <add tagPrefix="ajaxToolkit" namespace="AjaxControlToolkit" assembly="AjaxControlToolkit" />
        <add tagPrefix="asphaltWebUI" namespace="Dbs.Asphalt.UI" assembly="Dbs.Asphalt.UI" />
      </controls>
    </pages>
  </system.web>
  <system.webServer>
    <security>
      <requestFiltering>
        <requestLimits maxAllowedContentLength="**********" />
      </requestFiltering>
    </security>
  </system.webServer>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="MimeKit" publicKeyToken="bede1c8a46c66814" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.3.0.0" newVersion="4.3.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="MailKit" publicKeyToken="4e064fe7c44a8f1b" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.3.0.0" newVersion="4.3.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Memory" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.1.2" newVersion="4.0.1.2" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Text.Encoding.CodePages" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-7.0.0.0" newVersion="7.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-7.0.0.0" newVersion="7.0.0.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>
