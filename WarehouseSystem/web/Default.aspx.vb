Imports System.Data
Imports Warehouse.Application.Logic

Partial Class _Default
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not IsPostBack Then
            LoadDashboardData()
            LoadRecentActivities()
        End If
    End Sub

    Private Sub LoadDashboardData()
        Try
            ' Барааны тоо
            Dim productLogic As New ProductLogic()
            lblTotalProducts.Text = productLogic.GetTotalProductCount().ToString()

            ' Агуулахын тоо
            Dim warehouseLogic As New WarehouseLogic()
            lblTotalWarehouses.Text = warehouseLogic.GetTotalWarehouseCount().ToString()

            ' Өнөөдрийн орлого
            Dim stockLogic As New StockLogic()
            lblTodayStockIn.Text = stockLogic.GetTodayStockInCount().ToString()

            ' Өнөөдрийн гарлага
            lblTodayStockOut.Text = stockLogic.GetTodayStockOutCount().ToString()

        Catch ex As Exception
            ' Алдааны лог бичих
            Response.Write("<script>alert('Өгөгдөл ачаалахад алдаа гарлаа: " & ex.Message & "');</script>")
        End Try
    End Sub

    Private Sub LoadRecentActivities()
        Try
            Dim activityLogic As New ActivityLogic()
            Dim dt As DataTable = activityLogic.GetRecentActivities(10) ' Сүүлийн 10 үйл ажиллагаа

            gvRecentActivities.DataSource = dt
            gvRecentActivities.DataBind()

        Catch ex As Exception
            ' Алдааны лог бичих
            Response.Write("<script>alert('Үйл ажиллагааны жагсаалт ачаалахад алдаа гарлаа: " & ex.Message & "');</script>")
        End Try
    End Sub

    Protected Sub lnkLogout_Click(sender As Object, e As EventArgs) Handles lnkLogout.Click
        ' Session цэвэрлэх
        Session.Clear()
        Session.Abandon()
        
        ' Нэвтрэх хуудас руу шилжүүлэх
        Response.Redirect("Login.aspx")
    End Sub
End Class
