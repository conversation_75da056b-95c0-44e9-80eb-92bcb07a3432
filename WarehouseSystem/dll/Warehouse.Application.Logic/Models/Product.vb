''' <summary>
''' Барааны модель класс
''' </summary>
Public Class Product

    ''' <summary>
''' Барааны ID
''' </summary>
    Public Property ProductID As Integer

    ''' <summary>
    ''' Барааны код
    ''' </summary>
    Public Property ProductCode As String

    ''' <summary>
    ''' Барааны нэр
    ''' </summary>
    Public Property ProductName As String

    ''' <summary>
    ''' Ангиллын ID
    ''' </summary>
    Public Property CategoryID As Integer

    ''' <summary>
    ''' Хэмжих нэгж
    ''' </summary>
    Public Property UnitOfMeasure As String

    ''' <summary>
    ''' Нэгжийн үнэ
    ''' </summary>
    Public Property UnitPrice As Decimal

    ''' <summary>
    ''' Хамгийн бага үлдэгдэл
    ''' </summary>
    Public Property MinimumStock As Integer

    ''' <summary>
    ''' Хамгийн их үлдэгдэл
    ''' </summary>
    Public Property MaximumStock As Integer

    ''' <summary>
    ''' Идэвхтэй эсэх
    ''' </summary>
    Public Property IsActive As Boolean

    ''' <summary>
    ''' Үүсгэсэн огноо
    ''' </summary>
    Public Property CreatedDate As DateTime

    ''' <summary>
    ''' Үүсгэсэн хэрэглэгч
    ''' </summary>
    Public Property CreatedBy As String

    ''' <summary>
    ''' Өөрчилсөн огноо
    ''' </summary>
    Public Property ModifiedDate As DateTime?

    ''' <summary>
    ''' Өөрчилсөн хэрэглэгч
    ''' </summary>
    Public Property ModifiedBy As String

    ''' <summary>
    ''' Конструктор
    ''' </summary>
    Public Sub New()
        ProductID = 0
        ProductCode = String.Empty
        ProductName = String.Empty
        CategoryID = 0
        UnitOfMeasure = String.Empty
        UnitPrice = 0
        MinimumStock = 0
        MaximumStock = 0
        IsActive = True
        CreatedDate = DateTime.Now
        CreatedBy = String.Empty
        ModifiedDate = Nothing
        ModifiedBy = String.Empty
    End Sub

    ''' <summary>
    ''' Параметртэй конструктор
    ''' </summary>
    Public Sub New(productCode As String, productName As String, categoryId As Integer, 
                   unitOfMeasure As String, unitPrice As Decimal, minimumStock As Integer, 
                   maximumStock As Integer, createdBy As String)
        Me.New()
        Me.ProductCode = productCode
        Me.ProductName = productName
        Me.CategoryID = categoryId
        Me.UnitOfMeasure = unitOfMeasure
        Me.UnitPrice = unitPrice
        Me.MinimumStock = minimumStock
        Me.MaximumStock = maximumStock
        Me.CreatedBy = createdBy
    End Sub

    ''' <summary>
    ''' Барааны мэдээллийг шалгах
    ''' </summary>
    Public Function IsValid() As Boolean
        Return Not String.IsNullOrWhiteSpace(ProductCode) AndAlso
               Not String.IsNullOrWhiteSpace(ProductName) AndAlso
               CategoryID > 0 AndAlso
               Not String.IsNullOrWhiteSpace(UnitOfMeasure) AndAlso
               UnitPrice >= 0 AndAlso
               MinimumStock >= 0 AndAlso
               MaximumStock >= MinimumStock
    End Function

    ''' <summary>
    ''' ToString override
    ''' </summary>
    Public Overrides Function ToString() As String
        Return $"{ProductCode} - {ProductName}"
    End Function

End Class
