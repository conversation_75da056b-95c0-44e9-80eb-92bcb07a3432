''' <summary>
''' Агуулахын модель класс
''' </summary>
Public Class Warehouse

    ''' <summary>
    ''' Агуулахын ID
    ''' </summary>
    Public Property WarehouseID As Integer

    ''' <summary>
    ''' Агуулахын код
    ''' </summary>
    Public Property WarehouseCode As String

    ''' <summary>
    ''' Агуулахын нэр
    ''' </summary>
    Public Property WarehouseName As String

    ''' <summary>
    ''' Хаяг
    ''' </summary>
    Public Property Address As String

    ''' <summary>
    ''' Холбоо барих хүн
    ''' </summary>
    Public Property ContactPerson As String

    ''' <summary>
    ''' Утасны дугаар
    ''' </summary>
    Public Property Phone As String

    ''' <summary>
    ''' И-мэйл хаяг
    ''' </summary>
    Public Property Email As String

    ''' <summary>
    ''' Идэвхтэй эсэх
    ''' </summary>
    Public Property IsActive As Boolean

    ''' <summary>
    ''' Үүсгэсэн огноо
    ''' </summary>
    Public Property CreatedDate As DateTime

    ''' <summary>
    ''' Үүсгэсэн хэрэглэгч
    ''' </summary>
    Public Property CreatedBy As String

    ''' <summary>
    ''' Өөрчилсөн огноо
    ''' </summary>
    Public Property ModifiedDate As DateTime?

    ''' <summary>
    ''' Өөрчилсөн хэрэглэгч
    ''' </summary>
    Public Property ModifiedBy As String

    ''' <summary>
    ''' Конструктор
    ''' </summary>
    Public Sub New()
        WarehouseID = 0
        WarehouseCode = String.Empty
        WarehouseName = String.Empty
        Address = String.Empty
        ContactPerson = String.Empty
        Phone = String.Empty
        Email = String.Empty
        IsActive = True
        CreatedDate = DateTime.Now
        CreatedBy = String.Empty
        ModifiedDate = Nothing
        ModifiedBy = String.Empty
    End Sub

    ''' <summary>
    ''' Агуулахын мэдээллийг шалгах
    ''' </summary>
    Public Function IsValid() As Boolean
        Return Not String.IsNullOrWhiteSpace(WarehouseCode) AndAlso
               Not String.IsNullOrWhiteSpace(WarehouseName)
    End Function

    ''' <summary>
    ''' ToString override
    ''' </summary>
    Public Overrides Function ToString() As String
        Return $"{WarehouseCode} - {WarehouseName}"
    End Function

End Class
