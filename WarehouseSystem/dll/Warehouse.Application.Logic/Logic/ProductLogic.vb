Imports System.Data
Imports Warehouse.Application.Logic.Database

''' <summary>
''' Барааны бизнес логик
''' </summary>
Public Class ProductLogic

    Private ReadOnly _productData As New ProductData()

    ''' <summary>
    ''' Бүх барааны жагсаалт авах
    ''' </summary>
    Public Function GetAllProducts() As DataTable
        Try
            Return _productData.GetAllProducts()
        Catch ex As Exception
            Throw New Exception("Барааны жагсаалт авахад алдаа гарлаа: " & ex.Message, ex)
        End Try
    End Function

    ''' <summary>
    ''' Барааны ID-аар мэдээлэл авах
    ''' </summary>
    Public Function GetProductById(productId As Integer) As Product
        Try
            If productId <= 0 Then
                Throw New ArgumentException("Барааны ID буруу байна.")
            End If

            Dim dt As DataTable = _productData.GetProductById(productId)
            
            If dt.Rows.Count = 0 Then
                Return Nothing
            End If

            Return ConvertDataRowToProduct(dt.Rows(0))
            
        Catch ex As Exception
            Throw New Exception("Барааны мэдээлэл авахад алдаа гарлаа: " & ex.Message, ex)
        End Try
    End Function

    ''' <summary>
    ''' Шинэ бараа нэмэх
    ''' </summary>
    Public Function AddProduct(product As Product) As Integer
        Try
            ' Өгөгдөл шалгах
            If product Is Nothing Then
                Throw New ArgumentNullException("Барааны мэдээлэл хоосон байна.")
            End If

            If Not product.IsValid() Then
                Throw New ArgumentException("Барааны мэдээлэл буруу байна.")
            End If

            ' Барааны код давхардаж байгаа эсэхийг шалгах
            If _productData.IsProductCodeExists(product.ProductCode) Then
                Throw New ArgumentException("Энэ барааны код аль хэдийн бүртгэгдсэн байна.")
            End If

            ' Өгөгдлийн санд хадгалах
            Return _productData.InsertProduct(
                product.ProductCode,
                product.ProductName,
                product.CategoryID,
                product.UnitOfMeasure,
                product.UnitPrice,
                product.MinimumStock,
                product.MaximumStock,
                product.CreatedBy
            )

        Catch ex As Exception
            Throw New Exception("Бараа нэмэхэд алдаа гарлаа: " & ex.Message, ex)
        End Try
    End Function

    ''' <summary>
    ''' Барааны мэдээлэл шинэчлэх
    ''' </summary>
    Public Function UpdateProduct(product As Product) As Boolean
        Try
            ' Өгөгдөл шалгах
            If product Is Nothing Then
                Throw New ArgumentNullException("Барааны мэдээлэл хоосон байна.")
            End If

            If product.ProductID <= 0 Then
                Throw New ArgumentException("Барааны ID буруу байна.")
            End If

            If Not product.IsValid() Then
                Throw New ArgumentException("Барааны мэдээлэл буруу байна.")
            End If

            ' Барааны код давхардаж байгаа эсэхийг шалгах (өөрийгөө эс тооцвол)
            If _productData.IsProductCodeExists(product.ProductCode, product.ProductID) Then
                Throw New ArgumentException("Энэ барааны код аль хэдийн бүртгэгдсэн байна.")
            End If

            ' Өгөгдлийн санд шинэчлэх
            Dim result As Integer = _productData.UpdateProduct(
                product.ProductID,
                product.ProductCode,
                product.ProductName,
                product.CategoryID,
                product.UnitOfMeasure,
                product.UnitPrice,
                product.MinimumStock,
                product.MaximumStock,
                product.ModifiedBy
            )

            Return result > 0

        Catch ex As Exception
            Throw New Exception("Барааны мэдээлэл шинэчлэхэд алдаа гарлаа: " & ex.Message, ex)
        End Try
    End Function

    ''' <summary>
    ''' Бараа устгах
    ''' </summary>
    Public Function DeleteProduct(productId As Integer, modifiedBy As String) As Boolean
        Try
            If productId <= 0 Then
                Throw New ArgumentException("Барааны ID буруу байна.")
            End If

            If String.IsNullOrWhiteSpace(modifiedBy) Then
                Throw New ArgumentException("Устгасан хэрэглэгчийн мэдээлэл хоосон байна.")
            End If

            ' Өгөгдлийн сангаас устгах
            Dim result As Integer = _productData.DeleteProduct(productId, modifiedBy)
            Return result > 0

        Catch ex As Exception
            Throw New Exception("Бараа устгахад алдаа гарлаа: " & ex.Message, ex)
        End Try
    End Function

    ''' <summary>
    ''' Нийт барааны тоо авах
    ''' </summary>
    Public Function GetTotalProductCount() As Integer
        Try
            Return _productData.GetTotalProductCount()
        Catch ex As Exception
            Throw New Exception("Барааны тоо авахад алдаа гарлаа: " & ex.Message, ex)
        End Try
    End Function

    ''' <summary>
    ''' DataRow-г Product объект болгон хөрвүүлэх
    ''' </summary>
    Private Function ConvertDataRowToProduct(row As DataRow) As Product
        Dim product As New Product()
        
        product.ProductID = Convert.ToInt32(row("ProductID"))
        product.ProductCode = row("ProductCode").ToString()
        product.ProductName = row("ProductName").ToString()
        product.CategoryID = Convert.ToInt32(row("CategoryID"))
        product.UnitOfMeasure = row("UnitOfMeasure").ToString()
        product.UnitPrice = Convert.ToDecimal(row("UnitPrice"))
        product.MinimumStock = Convert.ToInt32(row("MinimumStock"))
        product.MaximumStock = Convert.ToInt32(row("MaximumStock"))
        product.IsActive = Convert.ToBoolean(row("IsActive"))
        product.CreatedDate = Convert.ToDateTime(row("CreatedDate"))
        product.CreatedBy = row("CreatedBy").ToString()
        
        If Not IsDBNull(row("ModifiedDate")) Then
            product.ModifiedDate = Convert.ToDateTime(row("ModifiedDate"))
        End If
        
        If Not IsDBNull(row("ModifiedBy")) Then
            product.ModifiedBy = row("ModifiedBy").ToString()
        End If
        
        Return product
    End Function

End Class
