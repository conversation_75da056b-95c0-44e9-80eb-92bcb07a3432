Imports System.Data

''' <summary>
''' Үлдэгдлийн бизнес логик
''' </summary>
Public Class StockLogic

    ''' <summary>
    ''' Өнөөдрийн орлогын тоо авах
    ''' </summary>
    Public Function GetTodayStockInCount() As Integer
        Try
            ' Энд өгөгдлийн сангаас өнөөдрийн орлогын тоог авах логик бичнэ
            ' Одоогоор тест утга буцаана
            Return 5
        Catch ex As Exception
            Throw New Exception("Өнөөдрийн орлогын тоо авахад алдаа гарлаа: " & ex.Message, ex)
        End Try
    End Function

    ''' <summary>
    ''' Өнөөдрийн гарлагын тоо авах
    ''' </summary>
    Public Function GetTodayStockOutCount() As Integer
        Try
            ' Энд өгөгдлийн сангаас өнөөдрийн гарлагын тоог авах логик бичнэ
            ' Одоогоор тест утга буцаана
            Return 3
        Catch ex As Exception
            Throw New Exception("Өнөөдрийн гарлагын тоо авахад алдаа гарлаа: " & ex.Message, ex)
        End Try
    End Function

End Class
