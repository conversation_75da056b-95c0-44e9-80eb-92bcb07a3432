Imports System.Data

''' <summary>
''' Үйл ажиллагааны лог бизнес логик
''' </summary>
Public Class ActivityLogic

    ''' <summary>
    ''' Сүүлийн үйл ажиллагаануудыг авах
    ''' </summary>
    Public Function GetRecentActivities(count As Integer) As DataTable
        Try
            ' Энд өгөгдлийн сангаас сүүлийн үйл ажиллагаануудыг авах логик бичнэ
            ' Одоогоор тест өгөгдөл үүсгэнэ
            Dim dt As New DataTable()
            dt.Columns.Add("ActivityDate", GetType(DateTime))
            dt.Columns.Add("ActivityType", GetType(String))
            dt.Columns.Add("Description", GetType(String))
            dt.Columns.Add("UserName", GetType(String))

            ' Тест өгөгдөл нэмэх
            dt.Rows.Add(DateTime.Now.AddMinutes(-10), "Бараа орлого", "Цагаан талх 50 ширхэг орлого", "admin")
            dt.Rows.Add(DateTime.Now.AddMinutes(-25), "Бараа гарлага", "Сүү 1л 10 лонх гарлага", "user")
            dt.Rows.Add(DateTime.Now.AddHours(-1), "Бараа бүртгэл", "Шинэ бараа нэмэгдлээ", "manager")
            dt.Rows.Add(DateTime.Now.AddHours(-2), "Агуулах бүртгэл", "Шинэ агуулах үүсгэгдлээ", "admin")
            dt.Rows.Add(DateTime.Now.AddHours(-3), "Тооллого", "Агуулахын тооллого хийгдлээ", "manager")

            Return dt
        Catch ex As Exception
            Throw New Exception("Үйл ажиллагааны жагсаалт авахад алдаа гарлаа: " & ex.Message, ex)
        End Try
    End Function

End Class
