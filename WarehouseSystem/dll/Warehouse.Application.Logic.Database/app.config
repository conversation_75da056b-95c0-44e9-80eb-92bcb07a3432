<?xml version="1.0" encoding="utf-8" ?>
<configuration>
    <configSections>
    </configSections>
    <connectionStrings>
        <add name="Warehouse.Application.Logic.Database.My.MySettings.ConnectionString"
            connectionString="Server=localhost;Database=WarehouseDB;Integrated Security=True"
            providerName="System.Data.SqlClient" />
    </connectionStrings>
    <system.diagnostics>
        <sources>
            <!-- このセクションでは、My.Application.Log のログ構成を定義します。-->
            <source name="DefaultSource" switchName="DefaultSwitch">
                <listeners>
                    <add name="FileLog"/>
                    <!-- アプリケーション イベント ログに書き込むには、以下のセクションのコメントを解除します -->
                    <!--<add name="EventLog"/>-->
                </listeners>
            </source>
        </sources>
        <switches>
            <add name="DefaultSwitch" value="Information" />
        </switches>
        <sharedListeners>
            <add name="FileLog"
                 type="Microsoft.VisualBasic.Logging.FileLogTraceListener, Microsoft.VisualBasic, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL"
                 initializeData="FileLogWriter"/>
            <!-- アプリケーション イベント ログに書き込むには、以下のセクションのコメントを解除して、EVENT_LOG を適切なログ名に置き換えます -->
            <!--<add name="EventLog" type="System.Diagnostics.EventLogTraceListener" initializeData="EVENT_LOG"/> -->
        </sharedListeners>
    </system.diagnostics>
</configuration>
