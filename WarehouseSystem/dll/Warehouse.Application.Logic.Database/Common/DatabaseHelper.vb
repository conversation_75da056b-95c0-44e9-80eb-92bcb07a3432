Imports System.Data
Imports System.Data.SqlClient
Imports System.Configuration

''' <summary>
''' Өгөгдлийн сангийн ажиллагааг дэмжих класс
''' </summary>
Public Class DatabaseHelper

    Private Shared ReadOnly ConnectionString As String = GetConnectionString()

    ''' <summary>
    ''' Холболтын мөрийг авах
    ''' </summary>
    Private Shared Function GetConnectionString() As String
        Try
            ' Web.config-аас холболтын мөрийг уншиж авах
            Dim dbSettings = DirectCast(ConfigurationManager.GetSection("DBSettings"), System.Collections.Specialized.NameValueCollection)
            If dbSettings IsNot Nothing Then
                Return dbSettings("ConnectionString")
            Else
                Return "Server=localhost;Database=WarehouseDB;Integrated Security=True;"
            End If
        Catch ex As Exception
            ' Алдаа гарвал анхдагч утгыг буцаах
            Return "Server=localhost;Database=WarehouseDB;Integrated Security=True;"
        End Try
    End Function

    ''' <summary>
    ''' SQL команд гүйцэтгэх (INSERT, UPDATE, DELETE)
    ''' </summary>
    Public Shared Function ExecuteNonQuery(sql As String, Optional parameters As SqlParameter() = Nothing) As Integer
        Try
            Using connection As New SqlConnection(ConnectionString)
                Using command As New SqlCommand(sql, connection)
                    If parameters IsNot Nothing Then
                        command.Parameters.AddRange(parameters)
                    End If
                    
                    connection.Open()
                    Return command.ExecuteNonQuery()
                End Using
            End Using
        Catch ex As Exception
            Throw New Exception("Өгөгдлийн санд өөрчлөлт хийхэд алдаа гарлаа: " & ex.Message, ex)
        End Try
    End Function

    ''' <summary>
    ''' Нэг утга буцаах (COUNT, MAX, MIN гэх мэт)
    ''' </summary>
    Public Shared Function ExecuteScalar(sql As String, Optional parameters As SqlParameter() = Nothing) As Object
        Try
            Using connection As New SqlConnection(ConnectionString)
                Using command As New SqlCommand(sql, connection)
                    If parameters IsNot Nothing Then
                        command.Parameters.AddRange(parameters)
                    End If
                    
                    connection.Open()
                    Return command.ExecuteScalar()
                End Using
            End Using
        Catch ex As Exception
            Throw New Exception("Өгөгдлийн сангаас утга авахад алдаа гарлаа: " & ex.Message, ex)
        End Try
    End Function

    ''' <summary>
    ''' DataTable буцаах (SELECT)
    ''' </summary>
    Public Shared Function ExecuteDataTable(sql As String, Optional parameters As SqlParameter() = Nothing) As DataTable
        Try
            Using connection As New SqlConnection(ConnectionString)
                Using command As New SqlCommand(sql, connection)
                    If parameters IsNot Nothing Then
                        command.Parameters.AddRange(parameters)
                    End If
                    
                    Using adapter As New SqlDataAdapter(command)
                        Dim dataTable As New DataTable()
                        adapter.Fill(dataTable)
                        Return dataTable
                    End Using
                End Using
            End Using
        Catch ex As Exception
            Throw New Exception("Өгөгдлийн сангаас мэдээлэл авахад алдаа гарлаа: " & ex.Message, ex)
        End Try
    End Function

    ''' <summary>
    ''' DataReader буцаах
    ''' </summary>
    Public Shared Function ExecuteReader(sql As String, Optional parameters As SqlParameter() = Nothing) As SqlDataReader
        Try
            Dim connection As New SqlConnection(ConnectionString)
            Dim command As New SqlCommand(sql, connection)
            
            If parameters IsNot Nothing Then
                command.Parameters.AddRange(parameters)
            End If
            
            connection.Open()
            Return command.ExecuteReader(CommandBehavior.CloseConnection)
            
        Catch ex As Exception
            Throw New Exception("Өгөгдлийн сангаас мэдээлэл уншихад алдаа гарлаа: " & ex.Message, ex)
        End Try
    End Function

    ''' <summary>
    ''' Stored Procedure гүйцэтгэх
    ''' </summary>
    Public Shared Function ExecuteStoredProcedure(procedureName As String, Optional parameters As SqlParameter() = Nothing) As DataTable
        Try
            Using connection As New SqlConnection(ConnectionString)
                Using command As New SqlCommand(procedureName, connection)
                    command.CommandType = CommandType.StoredProcedure
                    
                    If parameters IsNot Nothing Then
                        command.Parameters.AddRange(parameters)
                    End If
                    
                    Using adapter As New SqlDataAdapter(command)
                        Dim dataTable As New DataTable()
                        adapter.Fill(dataTable)
                        Return dataTable
                    End Using
                End Using
            End Using
        Catch ex As Exception
            Throw New Exception("Stored Procedure гүйцэтгэхэд алдаа гарлаа: " & ex.Message, ex)
        End Try
    End Function

    ''' <summary>
    ''' Холболт шалгах
    ''' </summary>
    Public Shared Function TestConnection() As Boolean
        Try
            Using connection As New SqlConnection(ConnectionString)
                connection.Open()
                Return True
            End Using
        Catch
            Return False
        End Try
    End Function

    ''' <summary>
    ''' SQL параметр үүсгэх
    ''' </summary>
    Public Shared Function CreateParameter(name As String, value As Object) As SqlParameter
        Return New SqlParameter(name, If(value, DBNull.Value))
    End Function

End Class
