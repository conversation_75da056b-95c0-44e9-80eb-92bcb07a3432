Imports System.Data
Imports System.Data.SqlClient
Imports Warehouse.Application.Logic.Database

''' <summary>
''' Барааны өгөгдлийн ажиллагаа
''' </summary>
Public Class ProductData

    ''' <summary>
    ''' Бүх барааны жагсаалт авах
    ''' </summary>
    Public Function GetAllProducts() As DataTable
        Dim sql As String = "
            SELECT 
                ProductID,
                ProductCode,
                ProductName,
                CategoryID,
                UnitOfMeasure,
                UnitPrice,
                MinimumStock,
                MaximumStock,
                IsActive,
                CreatedDate,
                CreatedBy,
                ModifiedDate,
                ModifiedBy
            FROM Products 
            WHERE IsActive = 1
            ORDER BY ProductName"
        
        Return DatabaseHelper.ExecuteDataTable(sql)
    End Function

    ''' <summary>
    ''' Барааны ID-аар мэдээлэл авах
    ''' </summary>
    Public Function GetProductById(productId As Integer) As DataTable
        Dim sql As String = "
            SELECT 
                ProductID,
                ProductCode,
                ProductName,
                CategoryID,
                UnitOfMeasure,
                UnitPrice,
                MinimumStock,
                MaximumStock,
                IsActive,
                CreatedDate,
                CreatedBy,
                ModifiedDate,
                ModifiedBy
            FROM Products 
            WHERE ProductID = @ProductID"
        
        Dim parameters() As SqlParameter = {
            DatabaseHelper.CreateParameter("@ProductID", productId)
        }
        
        Return DatabaseHelper.ExecuteDataTable(sql, parameters)
    End Function

    ''' <summary>
    ''' Шинэ бараа нэмэх
    ''' </summary>
    Public Function InsertProduct(productCode As String, productName As String, categoryId As Integer, 
                                unitOfMeasure As String, unitPrice As Decimal, minimumStock As Integer, 
                                maximumStock As Integer, createdBy As String) As Integer
        Dim sql As String = "
            INSERT INTO Products (
                ProductCode, ProductName, CategoryID, UnitOfMeasure, 
                UnitPrice, MinimumStock, MaximumStock, IsActive, 
                CreatedDate, CreatedBy
            ) VALUES (
                @ProductCode, @ProductName, @CategoryID, @UnitOfMeasure, 
                @UnitPrice, @MinimumStock, @MaximumStock, 1, 
                GETDATE(), @CreatedBy
            );
            SELECT SCOPE_IDENTITY();"
        
        Dim parameters() As SqlParameter = {
            DatabaseHelper.CreateParameter("@ProductCode", productCode),
            DatabaseHelper.CreateParameter("@ProductName", productName),
            DatabaseHelper.CreateParameter("@CategoryID", categoryId),
            DatabaseHelper.CreateParameter("@UnitOfMeasure", unitOfMeasure),
            DatabaseHelper.CreateParameter("@UnitPrice", unitPrice),
            DatabaseHelper.CreateParameter("@MinimumStock", minimumStock),
            DatabaseHelper.CreateParameter("@MaximumStock", maximumStock),
            DatabaseHelper.CreateParameter("@CreatedBy", createdBy)
        }
        
        Dim result = DatabaseHelper.ExecuteScalar(sql, parameters)
        Return Convert.ToInt32(result)
    End Function

    ''' <summary>
    ''' Барааны мэдээлэл шинэчлэх
    ''' </summary>
    Public Function UpdateProduct(productId As Integer, productCode As String, productName As String, 
                                categoryId As Integer, unitOfMeasure As String, unitPrice As Decimal, 
                                minimumStock As Integer, maximumStock As Integer, modifiedBy As String) As Integer
        Dim sql As String = "
            UPDATE Products SET 
                ProductCode = @ProductCode,
                ProductName = @ProductName,
                CategoryID = @CategoryID,
                UnitOfMeasure = @UnitOfMeasure,
                UnitPrice = @UnitPrice,
                MinimumStock = @MinimumStock,
                MaximumStock = @MaximumStock,
                ModifiedDate = GETDATE(),
                ModifiedBy = @ModifiedBy
            WHERE ProductID = @ProductID"
        
        Dim parameters() As SqlParameter = {
            DatabaseHelper.CreateParameter("@ProductID", productId),
            DatabaseHelper.CreateParameter("@ProductCode", productCode),
            DatabaseHelper.CreateParameter("@ProductName", productName),
            DatabaseHelper.CreateParameter("@CategoryID", categoryId),
            DatabaseHelper.CreateParameter("@UnitOfMeasure", unitOfMeasure),
            DatabaseHelper.CreateParameter("@UnitPrice", unitPrice),
            DatabaseHelper.CreateParameter("@MinimumStock", minimumStock),
            DatabaseHelper.CreateParameter("@MaximumStock", maximumStock),
            DatabaseHelper.CreateParameter("@ModifiedBy", modifiedBy)
        }
        
        Return DatabaseHelper.ExecuteNonQuery(sql, parameters)
    End Function

    ''' <summary>
    ''' Бараа устгах (логик устгалт)
    ''' </summary>
    Public Function DeleteProduct(productId As Integer, modifiedBy As String) As Integer
        Dim sql As String = "
            UPDATE Products SET 
                IsActive = 0,
                ModifiedDate = GETDATE(),
                ModifiedBy = @ModifiedBy
            WHERE ProductID = @ProductID"
        
        Dim parameters() As SqlParameter = {
            DatabaseHelper.CreateParameter("@ProductID", productId),
            DatabaseHelper.CreateParameter("@ModifiedBy", modifiedBy)
        }
        
        Return DatabaseHelper.ExecuteNonQuery(sql, parameters)
    End Function

    ''' <summary>
    ''' Нийт барааны тоо авах
    ''' </summary>
    Public Function GetTotalProductCount() As Integer
        Dim sql As String = "SELECT COUNT(*) FROM Products WHERE IsActive = 1"
        Dim result = DatabaseHelper.ExecuteScalar(sql)
        Return Convert.ToInt32(result)
    End Function

    ''' <summary>
    ''' Барааны код давхардаж байгаа эсэхийг шалгах
    ''' </summary>
    Public Function IsProductCodeExists(productCode As String, Optional excludeProductId As Integer = 0) As Boolean
        Dim sql As String = "SELECT COUNT(*) FROM Products WHERE ProductCode = @ProductCode AND IsActive = 1"
        
        If excludeProductId > 0 Then
            sql += " AND ProductID <> @ProductID"
        End If
        
        Dim parameters As New List(Of SqlParameter)
        parameters.Add(DatabaseHelper.CreateParameter("@ProductCode", productCode))
        
        If excludeProductId > 0 Then
            parameters.Add(DatabaseHelper.CreateParameter("@ProductID", excludeProductId))
        End If
        
        Dim result = DatabaseHelper.ExecuteScalar(sql, parameters.ToArray())
        Return Convert.ToInt32(result) > 0
    End Function

End Class
