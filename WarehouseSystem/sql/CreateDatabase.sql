-- Агуулахын бүртгэлийн систем - Өгөгдлийн сангийн бүтэц
-- Үүсгэсэн: 2024

USE master;
GO

-- Өгөгдлийн сан үүсгэх
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'WarehouseDB')
BEGIN
    CREATE DATABASE WarehouseDB;
END
GO

USE WarehouseDB;
GO

-- 1. Ангиллын хүснэгт
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Categories' AND xtype='U')
BEGIN
    CREATE TABLE Categories (
        CategoryID int IDENTITY(1,1) PRIMARY KEY,
        CategoryCode nvarchar(20) NOT NULL UNIQUE,
        CategoryName nvarchar(100) NOT NULL,
        Description nvarchar(500),
        IsActive bit NOT NULL DEFAULT 1,
        CreatedDate datetime NOT NULL DEFAULT GETDATE(),
        CreatedBy nvarchar(50) NOT NULL,
        ModifiedDate datetime NULL,
        ModifiedBy nvarchar(50) NULL
    );
END
GO

-- 2. Барааны хүснэгт
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Products' AND xtype='U')
BEGIN
    CREATE TABLE Products (
        ProductID int IDENTITY(1,1) PRIMARY KEY,
        ProductCode nvarchar(50) NOT NULL UNIQUE,
        ProductName nvarchar(200) NOT NULL,
        CategoryID int NOT NULL,
        UnitOfMeasure nvarchar(20) NOT NULL,
        UnitPrice decimal(18,2) NOT NULL DEFAULT 0,
        MinimumStock int NOT NULL DEFAULT 0,
        MaximumStock int NOT NULL DEFAULT 0,
        IsActive bit NOT NULL DEFAULT 1,
        CreatedDate datetime NOT NULL DEFAULT GETDATE(),
        CreatedBy nvarchar(50) NOT NULL,
        ModifiedDate datetime NULL,
        ModifiedBy nvarchar(50) NULL,
        FOREIGN KEY (CategoryID) REFERENCES Categories(CategoryID)
    );
END
GO

-- 3. Агуулахын хүснэгт
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Warehouses' AND xtype='U')
BEGIN
    CREATE TABLE Warehouses (
        WarehouseID int IDENTITY(1,1) PRIMARY KEY,
        WarehouseCode nvarchar(20) NOT NULL UNIQUE,
        WarehouseName nvarchar(100) NOT NULL,
        Address nvarchar(500),
        ContactPerson nvarchar(100),
        Phone nvarchar(20),
        Email nvarchar(100),
        IsActive bit NOT NULL DEFAULT 1,
        CreatedDate datetime NOT NULL DEFAULT GETDATE(),
        CreatedBy nvarchar(50) NOT NULL,
        ModifiedDate datetime NULL,
        ModifiedBy nvarchar(50) NULL
    );
END
GO

-- 4. Нийлүүлэгчийн хүснэгт
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Suppliers' AND xtype='U')
BEGIN
    CREATE TABLE Suppliers (
        SupplierID int IDENTITY(1,1) PRIMARY KEY,
        SupplierCode nvarchar(20) NOT NULL UNIQUE,
        SupplierName nvarchar(200) NOT NULL,
        ContactPerson nvarchar(100),
        Address nvarchar(500),
        Phone nvarchar(20),
        Email nvarchar(100),
        TaxNumber nvarchar(20),
        IsActive bit NOT NULL DEFAULT 1,
        CreatedDate datetime NOT NULL DEFAULT GETDATE(),
        CreatedBy nvarchar(50) NOT NULL,
        ModifiedDate datetime NULL,
        ModifiedBy nvarchar(50) NULL
    );
END
GO

-- 5. Үйлчлүүлэгчийн хүснэгт
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Customers' AND xtype='U')
BEGIN
    CREATE TABLE Customers (
        CustomerID int IDENTITY(1,1) PRIMARY KEY,
        CustomerCode nvarchar(20) NOT NULL UNIQUE,
        CustomerName nvarchar(200) NOT NULL,
        ContactPerson nvarchar(100),
        Address nvarchar(500),
        Phone nvarchar(20),
        Email nvarchar(100),
        TaxNumber nvarchar(20),
        IsActive bit NOT NULL DEFAULT 1,
        CreatedDate datetime NOT NULL DEFAULT GETDATE(),
        CreatedBy nvarchar(50) NOT NULL,
        ModifiedDate datetime NULL,
        ModifiedBy nvarchar(50) NULL
    );
END
GO

-- 6. Үлдэгдлийн хүснэгт
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Stock' AND xtype='U')
BEGIN
    CREATE TABLE Stock (
        StockID int IDENTITY(1,1) PRIMARY KEY,
        ProductID int NOT NULL,
        WarehouseID int NOT NULL,
        Quantity int NOT NULL DEFAULT 0,
        ReservedQuantity int NOT NULL DEFAULT 0,
        AvailableQuantity AS (Quantity - ReservedQuantity),
        LastUpdated datetime NOT NULL DEFAULT GETDATE(),
        FOREIGN KEY (ProductID) REFERENCES Products(ProductID),
        FOREIGN KEY (WarehouseID) REFERENCES Warehouses(WarehouseID),
        UNIQUE (ProductID, WarehouseID)
    );
END
GO

-- 7. Хөдөлгөөний хүснэгт
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='StockMovements' AND xtype='U')
BEGIN
    CREATE TABLE StockMovements (
        MovementID int IDENTITY(1,1) PRIMARY KEY,
        MovementType nvarchar(20) NOT NULL, -- IN, OUT, TRANSFER, ADJUSTMENT
        MovementDate datetime NOT NULL DEFAULT GETDATE(),
        ProductID int NOT NULL,
        WarehouseID int NOT NULL,
        Quantity int NOT NULL,
        UnitPrice decimal(18,2) NOT NULL DEFAULT 0,
        TotalAmount AS (Quantity * UnitPrice),
        ReferenceNumber nvarchar(50),
        Notes nvarchar(500),
        SupplierID int NULL,
        CustomerID int NULL,
        CreatedDate datetime NOT NULL DEFAULT GETDATE(),
        CreatedBy nvarchar(50) NOT NULL,
        FOREIGN KEY (ProductID) REFERENCES Products(ProductID),
        FOREIGN KEY (WarehouseID) REFERENCES Warehouses(WarehouseID),
        FOREIGN KEY (SupplierID) REFERENCES Suppliers(SupplierID),
        FOREIGN KEY (CustomerID) REFERENCES Customers(CustomerID)
    );
END
GO

-- 8. Хэрэглэгчийн хүснэгт
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Users' AND xtype='U')
BEGIN
    CREATE TABLE Users (
        UserID int IDENTITY(1,1) PRIMARY KEY,
        Username nvarchar(50) NOT NULL UNIQUE,
        Password nvarchar(255) NOT NULL,
        FullName nvarchar(100) NOT NULL,
        Email nvarchar(100),
        Role nvarchar(20) NOT NULL DEFAULT 'User', -- Admin, Manager, User
        IsActive bit NOT NULL DEFAULT 1,
        LastLogin datetime NULL,
        CreatedDate datetime NOT NULL DEFAULT GETDATE(),
        CreatedBy nvarchar(50) NOT NULL,
        ModifiedDate datetime NULL,
        ModifiedBy nvarchar(50) NULL
    );
END
GO

-- 9. Үйл ажиллагааны лог хүснэгт
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ActivityLogs' AND xtype='U')
BEGIN
    CREATE TABLE ActivityLogs (
        LogID int IDENTITY(1,1) PRIMARY KEY,
        ActivityDate datetime NOT NULL DEFAULT GETDATE(),
        ActivityType nvarchar(50) NOT NULL,
        Description nvarchar(500) NOT NULL,
        UserName nvarchar(50) NOT NULL,
        IPAddress nvarchar(50),
        Details nvarchar(MAX)
    );
END
GO
