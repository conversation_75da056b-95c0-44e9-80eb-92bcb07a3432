-- Агуулахын бүртгэлийн систем - Анхдагч өгөгдөл
-- Үүсгэсэн: 2024

USE WarehouseDB;
GO

-- Анги<PERSON><PERSON>ын анхдагч өгөгдөл
IF NOT EXISTS (SELECT * FROM Categories WHERE CategoryCode = 'FOOD')
BEGIN
    INSERT INTO Categories (CategoryCode, CategoryName, Description, CreatedBy)
    VALUES ('FOOD', 'Хүнсний бүтээгдэхүүн', 'Хүнсний бүх төрлийн бүтээгдэхүүн', 'System');
END

IF NOT EXISTS (SELECT * FROM Categories WHERE CategoryCode = 'BEVERAGE')
BEGIN
    INSERT INTO Categories (CategoryCode, CategoryName, Description, CreatedBy)
    VALUES ('BEVERAGE', 'Ундаа', 'Бүх төрлийн ундаа', 'System');
END

IF NOT EXISTS (SELECT * FROM Categories WHERE CategoryCode = 'HOUSEHOLD')
BEGIN
    INSERT INTO Categories (CategoryCode, CategoryName, Description, CreatedBy)
    VALUES ('HOUSEHOLD', 'Гэр ахуйн бараа', 'Гэр ахуйн хэрэглээний бараа', 'System');
END

IF NOT EXISTS (SELECT * FROM Categories WHERE CategoryCode = 'ELECTRONICS')
BEGIN
    INSERT INTO Categories (CategoryCode, CategoryName, Description, CreatedBy)
    VALUES ('ELECTRONICS', 'Цахилгаан бараа', 'Цахилгаан хэрэгсэл', 'System');
END
GO

-- Агуулахын анхдагч өгөгдөл
IF NOT EXISTS (SELECT * FROM Warehouses WHERE WarehouseCode = 'WH001')
BEGIN
    INSERT INTO Warehouses (WarehouseCode, WarehouseName, Address, ContactPerson, Phone, Email, CreatedBy)
    VALUES ('WH001', 'Төв агуулах', 'Улаанбаатар хот, Сүхбаатар дүүрэг', 'Б.Батбаяр', '99001122', '<EMAIL>', 'System');
END

IF NOT EXISTS (SELECT * FROM Warehouses WHERE WarehouseCode = 'WH002')
BEGIN
    INSERT INTO Warehouses (WarehouseCode, WarehouseName, Address, ContactPerson, Phone, Email, CreatedBy)
    VALUES ('WH002', 'Салбар агуулах №1', 'Улаанбаатар хот, Чингэлтэй дүүрэг', 'Д.Дорж', '99003344', '<EMAIL>', 'System');
END

IF NOT EXISTS (SELECT * FROM Warehouses WHERE WarehouseCode = 'WH003')
BEGIN
    INSERT INTO Warehouses (WarehouseCode, WarehouseName, Address, ContactPerson, Phone, Email, CreatedBy)
    VALUES ('WH003', 'Салбар агуулах №2', 'Улаанбаатар хот, Баянзүрх дүүрэг', 'С.Сайхан', '99005566', '<EMAIL>', 'System');
END
GO

-- Нийлүүлэгчийн анхдагч өгөгдөл
IF NOT EXISTS (SELECT * FROM Suppliers WHERE SupplierCode = 'SUP001')
BEGIN
    INSERT INTO Suppliers (SupplierCode, SupplierName, ContactPerson, Address, Phone, Email, TaxNumber, CreatedBy)
    VALUES ('SUP001', 'Монгол Хүнс ХХК', 'Ж.Жавхлан', 'УБ хот, Сүхбаатар дүүрэг', '70001111', '<EMAIL>', '1234567890', 'System');
END

IF NOT EXISTS (SELECT * FROM Suppliers WHERE SupplierCode = 'SUP002')
BEGIN
    INSERT INTO Suppliers (SupplierCode, SupplierName, ContactPerson, Address, Phone, Email, TaxNumber, CreatedBy)
    VALUES ('SUP002', 'Эрдэнэт Трейд ХХК', 'Б.Болд', 'Эрдэнэт хот', '70352222', '<EMAIL>', '0987654321', 'System');
END
GO

-- Үйлчлүүлэгчийн анхдагч өгөгдөл
IF NOT EXISTS (SELECT * FROM Customers WHERE CustomerCode = 'CUS001')
BEGIN
    INSERT INTO Customers (CustomerCode, CustomerName, ContactPerson, Address, Phone, Email, TaxNumber, CreatedBy)
    VALUES ('CUS001', 'Хангай Маркет ХХК', 'Т.Төмөр', 'УБ хот, Хан-Уул дүүрэг', '99111111', '<EMAIL>', '1111111111', 'System');
END

IF NOT EXISTS (SELECT * FROM Customers WHERE CustomerCode = 'CUS002')
BEGIN
    INSERT INTO Customers (CustomerCode, CustomerName, ContactPerson, Address, Phone, Email, TaxNumber, CreatedBy)
    VALUES ('CUS002', 'Номин Супермаркет ХХК', 'Г.Ганбат', 'УБ хот, Сүхбаатар дүүрэг', '99222222', '<EMAIL>', '2222222222', 'System');
END
GO

-- Хэрэглэгчийн анхдагч өгөгдөл
IF NOT EXISTS (SELECT * FROM Users WHERE Username = 'admin')
BEGIN
    INSERT INTO Users (Username, Password, FullName, Email, Role, CreatedBy)
    VALUES ('admin', 'admin123', 'Системийн админ', '<EMAIL>', 'Admin', 'System');
END

IF NOT EXISTS (SELECT * FROM Users WHERE Username = 'manager')
BEGIN
    INSERT INTO Users (Username, Password, FullName, Email, Role, CreatedBy)
    VALUES ('manager', 'manager123', 'Агуулахын менежер', '<EMAIL>', 'Manager', 'System');
END

IF NOT EXISTS (SELECT * FROM Users WHERE Username = 'user')
BEGIN
    INSERT INTO Users (Username, Password, FullName, Email, Role, CreatedBy)
    VALUES ('user', 'user123', 'Энгийн хэрэглэгч', '<EMAIL>', 'User', 'System');
END
GO

-- Барааны анхдагч өгөгдөл
DECLARE @FoodCategoryID int = (SELECT CategoryID FROM Categories WHERE CategoryCode = 'FOOD');
DECLARE @BeverageCategoryID int = (SELECT CategoryID FROM Categories WHERE CategoryCode = 'BEVERAGE');
DECLARE @HouseholdCategoryID int = (SELECT CategoryID FROM Categories WHERE CategoryCode = 'HOUSEHOLD');

IF NOT EXISTS (SELECT * FROM Products WHERE ProductCode = 'PRD001')
BEGIN
    INSERT INTO Products (ProductCode, ProductName, CategoryID, UnitOfMeasure, UnitPrice, MinimumStock, MaximumStock, CreatedBy)
    VALUES ('PRD001', 'Цагаан талх', @FoodCategoryID, 'ширхэг', 1500.00, 50, 500, 'System');
END

IF NOT EXISTS (SELECT * FROM Products WHERE ProductCode = 'PRD002')
BEGIN
    INSERT INTO Products (ProductCode, ProductName, CategoryID, UnitOfMeasure, UnitPrice, MinimumStock, MaximumStock, CreatedBy)
    VALUES ('PRD002', 'Сүү 1л', @BeverageCategoryID, 'лонх', 2500.00, 20, 200, 'System');
END

IF NOT EXISTS (SELECT * FROM Products WHERE ProductCode = 'PRD003')
BEGIN
    INSERT INTO Products (ProductCode, ProductName, CategoryID, UnitOfMeasure, UnitPrice, MinimumStock, MaximumStock, CreatedBy)
    VALUES ('PRD003', 'Угаалгын нунтаг', @HouseholdCategoryID, 'хайрцаг', 8500.00, 10, 100, 'System');
END
GO

-- Үлдэгдлийн анхдагч өгөгдөл
DECLARE @Product1ID int = (SELECT ProductID FROM Products WHERE ProductCode = 'PRD001');
DECLARE @Product2ID int = (SELECT ProductID FROM Products WHERE ProductCode = 'PRD002');
DECLARE @Product3ID int = (SELECT ProductID FROM Products WHERE ProductCode = 'PRD003');
DECLARE @Warehouse1ID int = (SELECT WarehouseID FROM Warehouses WHERE WarehouseCode = 'WH001');
DECLARE @Warehouse2ID int = (SELECT WarehouseID FROM Warehouses WHERE WarehouseCode = 'WH002');

IF NOT EXISTS (SELECT * FROM Stock WHERE ProductID = @Product1ID AND WarehouseID = @Warehouse1ID)
BEGIN
    INSERT INTO Stock (ProductID, WarehouseID, Quantity, ReservedQuantity)
    VALUES (@Product1ID, @Warehouse1ID, 100, 0);
END

IF NOT EXISTS (SELECT * FROM Stock WHERE ProductID = @Product2ID AND WarehouseID = @Warehouse1ID)
BEGIN
    INSERT INTO Stock (ProductID, WarehouseID, Quantity, ReservedQuantity)
    VALUES (@Product2ID, @Warehouse1ID, 50, 0);
END

IF NOT EXISTS (SELECT * FROM Stock WHERE ProductID = @Product3ID AND WarehouseID = @Warehouse2ID)
BEGIN
    INSERT INTO Stock (ProductID, WarehouseID, Quantity, ReservedQuantity)
    VALUES (@Product3ID, @Warehouse2ID, 25, 0);
END
GO

PRINT 'Анхдагч өгөгдөл амжилттай орууллаа!';
