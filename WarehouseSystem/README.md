# Агуулахын бүртгэлийн систем
## Warehouse Management System

Asphalt framework дээр суурилсан агуулахын удирдлагын веб систем.

### 🏗️ Системийн архитектур

- **Frontend**: ASP.NET Web Forms (.NET Framework 4.8)
- **Backend**: Visual Basic.NET
- **Database**: SQL Server
- **Framework**: Asphalt Framework

### 📁 Төслийн бүтэц

```
WarehouseSystem/
├── WarehouseSystem.sln                    # Solution файл
├── web/                                   # Веб аппликейшн
│   ├── Default.aspx                       # Үндсэн хуудас
│   ├── Web.Config                         # Тохиргооны файл
│   ├── css/                               # CSS файлууд
│   ├── js/                                # JavaScript файлууд
│   └── Pages/                             # Бусад хуудаснууд
├── dll/                                   # Class Library-ууд
│   ├── Warehouse.Application.Logic/       # Бизнес логик давхарга
│   │   ├── Logic/                         # Бизнес логик классууд
│   │   └── Models/                        # Өгөгдлийн модель классууд
│   └── Warehouse.Application.Logic.Database/ # Өгөгдлийн сангийн давхарга
│       ├── Common/                        # Нийтлэг классууд
│       └── Data/                          # Өгөгдлийн хандалтын классууд
├── sql/                                   # SQL скриптүүд
│   ├── CreateDatabase.sql                 # Өгөгдлийн сангийн бүтэц
│   └── InsertSampleData.sql              # Анхдагч өгөгдөл
└── README.md                              # Энэ файл
```

### 🚀 Суулгах заавар

#### 1. Шаардлагатай програм хангамж
- Visual Studio 2019 эсвэл түүнээс дээш
- SQL Server 2017 эсвэл түүнээс дээш
- .NET Framework 4.8
- IIS 10.0 эсвэл түүнээс дээш

#### 2. Өгөгдлийн сан тохируулах
```sql
-- SQL Server Management Studio-д дараах скриптүүдийг ажиллуулна уу:
1. sql/CreateDatabase.sql
2. sql/InsertSampleData.sql
```

#### 3. Холболтын мөр тохируулах
`web/Web.Config` файлд өөрийн SQL Server-ийн мэдээллийг оруулна уу:
```xml
<DBSettings>
    <add key="ConnectionString" value="Server=YOUR_SERVER;Database=WarehouseDB;Integrated Security=True;" />
</DBSettings>
```

#### 4. Төслийг ажиллуулах
1. Visual Studio-д `WarehouseSystem.sln` файлийг нээнэ үү
2. Solution-г Build хийнэ үү
3. Web төслийг Run хийнэ үү

### 👥 Анхдагч хэрэглэгчид

| Хэрэглэгчийн нэр | Нууц үг | Эрх |
|------------------|---------|-----|
| admin | admin123 | Админ |
| manager | manager123 | Менежер |
| user | user123 | Хэрэглэгч |

### 📋 Системийн функцууд

#### 🏪 Үндсэн бүртгэл
- **Барааны бүртгэл**: Бараа нэмэх, засах, устгах
- **Агуулахын бүртгэл**: Агуулах удирдах
- **Нийлүүлэгчийн бүртгэл**: Нийлүүлэгчийн мэдээлэл
- **Үйлчлүүлэгчийн бүртгэл**: Үйлчлүүлэгчийн мэдээлэл

#### 📦 Орлого/Гарлага
- **Бараа орлого**: Агуулахад бараа орлого хийх
- **Бараа гарлага**: Агуулахаас бараа гарлага хийх
- **Агуулах хооронд шилжүүлэх**: Бараа шилжүүлэх
- **Тооллого**: Үлдэгдэл тохируулах

#### 📊 Тайлан системүүд
- **Үлдэгдлийн тайлан**: Агуулах тус бүрийн үлдэгдэл
- **Хөдөлгөөний тайлан**: Барааны орлого/гарлагын түүх
- **Үнийн дүнгийн тайлан**: Агуулахын нийт үнийн дүн

#### ⚙️ Систем удирдлага
- **Хэрэглэгчийн удирдлага**: Хэрэглэгч нэмэх, эрх олгох
- **Системийн тохиргоо**: Ерөнхий тохиргоо

### 🔧 Хөгжүүлэлтийн заавар

#### Шинэ модуль нэмэх
1. `Models` хавтаст шинэ модель класс үүсгэх
2. `Data` хавтаст өгөгдлийн хандалтын класс үүсгэх
3. `Logic` хавтаст бизнес логик класс үүсгэх
4. `Pages` хавтаст веб хуудас үүсгэх

#### Кодын стандарт
- Visual Basic.NET хэл ашиглах
- Монгол хэлээр тайлбар бичих
- Exception handling заавал хийх
- SQL Injection-аас хамгаалах

### 📝 Өөрчлөлтийн түүх

#### v1.0.0 (2024-12-19)
- Анхны хувилбар
- Үндсэн функцууд бүрэн
- Asphalt framework дэмжлэг

### 🤝 Хувь нэмэр оруулах

1. Repository-г fork хийнэ үү
2. Feature branch үүсгэнэ үү
3. Өөрчлөлт хийнэ үү
4. Pull request илгээнэ үү

### 📞 Холбоо барих

Асуулт, санал хүсэлт байвал доорх хаягаар холбогдоно уу:
- Email: <EMAIL>
- Phone: +976-11-123456

### 📄 Лиценз

Энэхүү төсөл нь MIT лицензийн дагуу хуваарилагдсан.

---
**Агуулахын бүртгэлийн систем** - Asphalt Framework дээр суурилсан
