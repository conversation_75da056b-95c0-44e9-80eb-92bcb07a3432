﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="DataSetFunc" targetNamespace="http://tempuri.org/DataSetFunc.xsd" xmlns:mstns="http://tempuri.org/DataSetFunc.xsd" xmlns="http://tempuri.org/DataSetFunc.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections>
          <Connection AppSettingsObjectName="MySettings" AppSettingsPropertyName="ConnectionStringBase" ConnectionStringObject="" IsAppSettingsProperty="true" Modifier="Assembly" Name="ConnectionStringBase (MySettings)" ParameterPrefix="@" PropertyReference="ApplicationSettings.NodeDatabase.My.MySettings.GlobalReference.Default.ConnectionStringBase" Provider="System.Data.SqlClient" />
        </Connections>
        <Tables>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="F0レイアウトHDTableAdapter" GeneratorDataComponentClassName="F0レイアウトHDTableAdapter" Name="F0レイアウトHD" UserDataComponentName="F0レイアウトHDTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionStringBase (MySettings)" DbObjectName="Taxi.dbo.F0レイアウトHD" DbObjectType="Function" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT                      レイアウトCD, レイアウト名,  ファイル形式, 先頭タイトル, 最終ディレクトリ, 登録日時, 更新日時, 更新者名
FROM                         dbo.F0レイアウトHD(@グループCD, @個社CD, @子会社CD) AS F0レイアウトHD_1</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="グループCD" ColumnName="" DataSourceName="" DataTypeServer="varchar(1)" DbType="AnsiString" Direction="Input" ParameterName="@グループCD" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="個社CD" ColumnName="" DataSourceName="" DataTypeServer="varchar(2)" DbType="AnsiString" Direction="Input" ParameterName="@個社CD" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="子会社CD" ColumnName="" DataSourceName="" DataTypeServer="varchar(1)" DbType="AnsiString" Direction="Input" ParameterName="@子会社CD" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="レイアウト名" DataSetColumn="レイアウト名" />
              <Mapping SourceColumn="ファイル形式" DataSetColumn="ファイル形式" />
              <Mapping SourceColumn="先頭タイトル" DataSetColumn="先頭タイトル" />
              <Mapping SourceColumn="最終ディレクトリ" DataSetColumn="最終ディレクトリ" />
              <Mapping SourceColumn="登録日時" DataSetColumn="登録日時" />
              <Mapping SourceColumn="更新日時" DataSetColumn="更新日時" />
              <Mapping SourceColumn="更新者名" DataSetColumn="更新者名" />
              <Mapping SourceColumn="レイアウトCD" DataSetColumn="レイアウトCD" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="F0レイアウト条件TableAdapter" GeneratorDataComponentClassName="F0レイアウト条件TableAdapter" Name="F0レイアウト条件" UserDataComponentName="F0レイアウト条件TableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionStringBase (MySettings)" DbObjectName="Taxiamaru0712.dbo.F0レイアウト条件" DbObjectType="Function" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT                      レイアウトCD,  条件カラム位置, 条件項目値, 符号区分, 結果伝票項目CD, 結果項目値, 結果伝票項目名, 結果伝票項目名表示, 結果キーフラグ, 
                                      符号区分名
FROM                         dbo.F0レイアウト条件(@グループCD, @個社CD, @子会社CD) AS F0レイアウト条件_1</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="グループCD" ColumnName="" DataSourceName="" DataTypeServer="varchar(5)" DbType="AnsiString" Direction="Input" ParameterName="@グループCD" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="個社CD" ColumnName="" DataSourceName="" DataTypeServer="varchar(1)" DbType="AnsiString" Direction="Input" ParameterName="@個社CD" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="子会社CD" ColumnName="" DataSourceName="" DataTypeServer="varchar(2)" DbType="AnsiString" Direction="Input" ParameterName="@子会社CD" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="条件カラム位置" DataSetColumn="条件カラム位置" />
              <Mapping SourceColumn="条件項目値" DataSetColumn="条件項目値" />
              <Mapping SourceColumn="符号区分" DataSetColumn="符号区分" />
              <Mapping SourceColumn="結果項目値" DataSetColumn="結果項目値" />
              <Mapping SourceColumn="結果伝票項目名" DataSetColumn="結果伝票項目名" />
              <Mapping SourceColumn="結果伝票項目名表示" DataSetColumn="結果伝票項目名表示" />
              <Mapping SourceColumn="結果キーフラグ" DataSetColumn="結果キーフラグ" />
              <Mapping SourceColumn="符号区分名" DataSetColumn="符号区分名" />
              <Mapping SourceColumn="レイアウトCD" DataSetColumn="レイアウトCD" />
              <Mapping SourceColumn="結果伝票項目CD" DataSetColumn="結果伝票項目CD" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="F0レイアウトTableAdapter" GeneratorDataComponentClassName="F0レイアウトTableAdapter" Name="F0レイアウト" UserDataComponentName="F0レイアウトTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionStringBase (MySettings)" DbObjectName="Taxiamaru0712.dbo.F0レイアウト" DbObjectType="Function" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT                      レイアウトCD, レイアウト名, ファイル形式, 先頭タイトル, 最終ディレクトリ, 登録日時, 更新日時, 更新者名, 伝票項目コード, カラム位置, 既定値, キーフラグ, 
                                      取込優先順, 伝票項目名, 伝票項目名表示, 表示位置, ファイル形式名
FROM                         dbo.F0レイアウト(@グループCD, @個社CD, @子会社CD) AS F0レイアウト_1</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="グループCD" ColumnName="" DataSourceName="" DataTypeServer="varchar(1)" DbType="AnsiString" Direction="Input" ParameterName="@グループCD" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="個社CD" ColumnName="" DataSourceName="" DataTypeServer="varchar(2)" DbType="AnsiString" Direction="Input" ParameterName="@個社CD" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="子会社CD" ColumnName="" DataSourceName="" DataTypeServer="varchar(1)" DbType="AnsiString" Direction="Input" ParameterName="@子会社CD" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="レイアウトCD" DataSetColumn="レイアウトCD" />
              <Mapping SourceColumn="レイアウト名" DataSetColumn="レイアウト名" />
              <Mapping SourceColumn="ファイル形式" DataSetColumn="ファイル形式" />
              <Mapping SourceColumn="先頭タイトル" DataSetColumn="先頭タイトル" />
              <Mapping SourceColumn="最終ディレクトリ" DataSetColumn="最終ディレクトリ" />
              <Mapping SourceColumn="登録日時" DataSetColumn="登録日時" />
              <Mapping SourceColumn="更新日時" DataSetColumn="更新日時" />
              <Mapping SourceColumn="更新者名" DataSetColumn="更新者名" />
              <Mapping SourceColumn="伝票項目コード" DataSetColumn="伝票項目コード" />
              <Mapping SourceColumn="カラム位置" DataSetColumn="カラム位置" />
              <Mapping SourceColumn="既定値" DataSetColumn="既定値" />
              <Mapping SourceColumn="キーフラグ" DataSetColumn="キーフラグ" />
              <Mapping SourceColumn="取込優先順" DataSetColumn="取込優先順" />
              <Mapping SourceColumn="伝票項目名" DataSetColumn="伝票項目名" />
              <Mapping SourceColumn="伝票項目名表示" DataSetColumn="伝票項目名表示" />
              <Mapping SourceColumn="表示位置" DataSetColumn="表示位置" />
              <Mapping SourceColumn="ファイル形式名" DataSetColumn="ファイル形式名" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="F0シフト一覧TableAdapter" GeneratorDataComponentClassName="F0シフト一覧TableAdapter" Name="F0シフト一覧" UserDataComponentName="F0シフト一覧TableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionStringBase (MySettings)" DbObjectName="Taxi.dbo.F0シフト一覧" DbObjectType="Function" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT                      F0シフト一覧_1.*
FROM                         dbo.F0シフト一覧( @グループCD,@個社CD,@子会社CD,@日付F,@日付T) AS F0シフト一覧_1</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="グループCD" ColumnName="" DataSourceName="" DataTypeServer="varchar(1)" DbType="AnsiString" Direction="Input" ParameterName="@グループCD" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="個社CD" ColumnName="" DataSourceName="" DataTypeServer="varchar(2)" DbType="AnsiString" Direction="Input" ParameterName="@個社CD" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="子会社CD" ColumnName="" DataSourceName="" DataTypeServer="varchar(1)" DbType="AnsiString" Direction="Input" ParameterName="@子会社CD" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="日付F" ColumnName="" DataSourceName="" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@日付F" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="日付T" ColumnName="" DataSourceName="" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@日付T" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="グループCD" DataSetColumn="グループCD" />
              <Mapping SourceColumn="個社CD" DataSetColumn="個社CD" />
              <Mapping SourceColumn="子会社CD" DataSetColumn="子会社CD" />
              <Mapping SourceColumn="日付" DataSetColumn="日付" />
              <Mapping SourceColumn="シフト区分" DataSetColumn="シフト区分" />
              <Mapping SourceColumn="区分名" DataSetColumn="区分名" />
              <Mapping SourceColumn="乗務員CD" DataSetColumn="乗務員CD" />
              <Mapping SourceColumn="シフト" DataSetColumn="シフト" />
              <Mapping SourceColumn="基準日数" DataSetColumn="基準日数" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="F0シフト一覧全体TableAdapter" GeneratorDataComponentClassName="F0シフト一覧全体TableAdapter" Name="F0シフト一覧全体" UserDataComponentName="F0シフト一覧全体TableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionStringBase (MySettings)" DbObjectName="Taxi.dbo.F0シフト一覧全体" DbObjectType="Function" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT                      グループCD, 個社CD, 子会社CD, シフト区分, 区分名, 日付, シフト
FROM                         dbo.F0シフト一覧全体(@グループCD, @個社CD, @子会社CD, @日付F, @日付T) AS F0シフト一覧全体_1</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="グループCD" ColumnName="" DataSourceName="" DataTypeServer="varchar(1)" DbType="AnsiString" Direction="Input" ParameterName="@グループCD" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="個社CD" ColumnName="" DataSourceName="" DataTypeServer="varchar(2)" DbType="AnsiString" Direction="Input" ParameterName="@個社CD" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="子会社CD" ColumnName="" DataSourceName="" DataTypeServer="varchar(1)" DbType="AnsiString" Direction="Input" ParameterName="@子会社CD" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="日付F" ColumnName="" DataSourceName="" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@日付F" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="日付T" ColumnName="" DataSourceName="" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@日付T" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="グループCD" DataSetColumn="グループCD" />
              <Mapping SourceColumn="個社CD" DataSetColumn="個社CD" />
              <Mapping SourceColumn="子会社CD" DataSetColumn="子会社CD" />
              <Mapping SourceColumn="シフト区分" DataSetColumn="シフト区分" />
              <Mapping SourceColumn="区分名" DataSetColumn="区分名" />
              <Mapping SourceColumn="日付" DataSetColumn="日付" />
              <Mapping SourceColumn="シフト" DataSetColumn="シフト" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="F0乗務員シフト区分TableAdapter" GeneratorDataComponentClassName="F0乗務員シフト区分TableAdapter" Name="F0乗務員シフト区分" UserDataComponentName="F0乗務員シフト区分TableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionStringBase (MySettings)" DbObjectName="Taxi.dbo.F0乗務員シフト区分" DbObjectType="Function" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT                      グループCD, 個社CD, 子会社CD, 乗務員CD, 乗務員名, 勤務区分, シフト区分, 社員NO
FROM                         dbo.F0乗務員シフト区分(@グループCD, @個社CD, @子会社CD, @区分) AS F0乗務員シフト区分_1</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="グループCD" ColumnName="" DataSourceName="" DataTypeServer="varchar(1)" DbType="AnsiString" Direction="Input" ParameterName="@グループCD" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="個社CD" ColumnName="" DataSourceName="" DataTypeServer="varchar(2)" DbType="AnsiString" Direction="Input" ParameterName="@個社CD" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="子会社CD" ColumnName="" DataSourceName="" DataTypeServer="varchar(1)" DbType="AnsiString" Direction="Input" ParameterName="@子会社CD" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="区分" ColumnName="" DataSourceName="" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@区分" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="グループCD" DataSetColumn="グループCD" />
              <Mapping SourceColumn="個社CD" DataSetColumn="個社CD" />
              <Mapping SourceColumn="子会社CD" DataSetColumn="子会社CD" />
              <Mapping SourceColumn="乗務員CD" DataSetColumn="乗務員CD" />
              <Mapping SourceColumn="乗務員名" DataSetColumn="乗務員名" />
              <Mapping SourceColumn="勤務区分" DataSetColumn="勤務区分" />
              <Mapping SourceColumn="シフト区分" DataSetColumn="シフト区分" />
              <Mapping SourceColumn="社員NO" DataSetColumn="社員NO" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="F0月別輸送実績TableAdapter" GeneratorDataComponentClassName="F0月別輸送実績TableAdapter" Name="F0月別輸送実績" UserDataComponentName="F0月別輸送実績TableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionStringBase (MySettings)" DbObjectName="Taxi.dbo.F0月別輸送実績" DbObjectType="Function" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT                      グループCD, 個社CD, 子会社CD, 車種CD, 延実在車両数, 月末車両数, 運送収入, 車両番号のカウント, 走行kmの合計, 実車kmの合計, 輸送回数の合計, 
                                      輸送人員の合計, 迎車回数の合計, 税込運送収入の合計
FROM                         dbo.F0月別輸送実績(@グループCD, @個社CD, @子会社CD, @日付F, @日付T) AS F0月別輸送実績_1</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="グループCD" ColumnName="" DataSourceName="" DataTypeServer="varchar(1)" DbType="AnsiString" Direction="Input" ParameterName="@グループCD" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="個社CD" ColumnName="" DataSourceName="" DataTypeServer="varchar(2)" DbType="AnsiString" Direction="Input" ParameterName="@個社CD" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="子会社CD" ColumnName="" DataSourceName="" DataTypeServer="varchar(1)" DbType="AnsiString" Direction="Input" ParameterName="@子会社CD" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="日付F" ColumnName="" DataSourceName="" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@日付F" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="日付T" ColumnName="" DataSourceName="" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@日付T" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="グループCD" DataSetColumn="グループCD" />
              <Mapping SourceColumn="個社CD" DataSetColumn="個社CD" />
              <Mapping SourceColumn="子会社CD" DataSetColumn="子会社CD" />
              <Mapping SourceColumn="車種CD" DataSetColumn="車種CD" />
              <Mapping SourceColumn="延実在車両数" DataSetColumn="延実在車両数" />
              <Mapping SourceColumn="月末車両数" DataSetColumn="月末車両数" />
              <Mapping SourceColumn="運送収入" DataSetColumn="運送収入" />
              <Mapping SourceColumn="車両番号のカウント" DataSetColumn="車両番号のカウント" />
              <Mapping SourceColumn="走行kmの合計" DataSetColumn="走行kmの合計" />
              <Mapping SourceColumn="実車kmの合計" DataSetColumn="実車kmの合計" />
              <Mapping SourceColumn="輸送回数の合計" DataSetColumn="輸送回数の合計" />
              <Mapping SourceColumn="輸送人員の合計" DataSetColumn="輸送人員の合計" />
              <Mapping SourceColumn="迎車回数の合計" DataSetColumn="迎車回数の合計" />
              <Mapping SourceColumn="税込運送収入の合計" DataSetColumn="税込運送収入の合計" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="F0出退勤情報出力TableAdapter" GeneratorDataComponentClassName="F0出退勤情報出力TableAdapter" Name="F0出退勤情報出力" UserDataComponentName="F0出退勤情報出力TableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionStringBase (MySettings)" DbObjectName="Taxi.dbo.F0出退勤情報出力" DbObjectType="Function" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT                      グループCD, 個社CD, 子会社CD, 社員NO, 有給, 欠勤
FROM                         dbo.F0出退勤情報出力(@グループCD, @個社CD, @子会社CD, @日付F, @日付T) AS F0出退勤情報出力_1</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="グループCD" ColumnName="" DataSourceName="" DataTypeServer="varchar(1)" DbType="AnsiString" Direction="Input" ParameterName="@グループCD" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="個社CD" ColumnName="" DataSourceName="" DataTypeServer="varchar(2)" DbType="AnsiString" Direction="Input" ParameterName="@個社CD" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="子会社CD" ColumnName="" DataSourceName="" DataTypeServer="varchar(1)" DbType="AnsiString" Direction="Input" ParameterName="@子会社CD" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="日付F" ColumnName="" DataSourceName="" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@日付F" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="日付T" ColumnName="" DataSourceName="" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@日付T" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="グループCD" DataSetColumn="グループCD" />
              <Mapping SourceColumn="個社CD" DataSetColumn="個社CD" />
              <Mapping SourceColumn="子会社CD" DataSetColumn="子会社CD" />
              <Mapping SourceColumn="社員NO" DataSetColumn="社員NO" />
              <Mapping SourceColumn="有給" DataSetColumn="有給" />
              <Mapping SourceColumn="欠勤" DataSetColumn="欠勤" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="F0売上照会TableAdapter" GeneratorDataComponentClassName="F0売上照会TableAdapter" Name="F0売上照会" UserDataComponentName="F0売上照会TableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionStringBase (MySettings)" DbObjectName="Taxi.dbo.F0売上照会" DbObjectType="Function" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT                      グループCD, 個社CD, 子会社CD, 日報日付, 車両番号, 社員NO, 勤務区分, 納金, 営収, 現金A, その他
FROM                         dbo.F0売上照会(@グループCD, @個社CD, @子会社CD, @日付F, @日付T, @車両NO, @社員NO, @勤務区分, @集計項目_日付, @集計項目_車番, @集計項目_乗務員, 
                                      @集計項目_勤務区分, @集計項目_個社, @集計項目_子会社) AS F0売上照会_1</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="グループCD" ColumnName="" DataSourceName="" DataTypeServer="varchar(1)" DbType="AnsiString" Direction="Input" ParameterName="@グループCD" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="個社CD" ColumnName="" DataSourceName="" DataTypeServer="varchar(2)" DbType="AnsiString" Direction="Input" ParameterName="@個社CD" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="子会社CD" ColumnName="" DataSourceName="" DataTypeServer="varchar(1)" DbType="AnsiString" Direction="Input" ParameterName="@子会社CD" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="日付F" ColumnName="" DataSourceName="" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@日付F" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="日付T" ColumnName="" DataSourceName="" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@日付T" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="車両NO" ColumnName="" DataSourceName="" DataTypeServer="varchar(6)" DbType="AnsiString" Direction="Input" ParameterName="@車両NO" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="社員NO" ColumnName="" DataSourceName="" DataTypeServer="varchar(6)" DbType="AnsiString" Direction="Input" ParameterName="@社員NO" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="勤務区分" ColumnName="" DataSourceName="" DataTypeServer="varchar(2)" DbType="AnsiString" Direction="Input" ParameterName="@勤務区分" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="集計項目_日付" ColumnName="" DataSourceName="" DataTypeServer="varchar(1)" DbType="AnsiString" Direction="Input" ParameterName="@集計項目_日付" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="集計項目_車番" ColumnName="" DataSourceName="" DataTypeServer="varchar(1)" DbType="AnsiString" Direction="Input" ParameterName="@集計項目_車番" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="集計項目_乗務員" ColumnName="" DataSourceName="" DataTypeServer="varchar(1)" DbType="AnsiString" Direction="Input" ParameterName="@集計項目_乗務員" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="集計項目_勤務区分" ColumnName="" DataSourceName="" DataTypeServer="varchar(1)" DbType="AnsiString" Direction="Input" ParameterName="@集計項目_勤務区分" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="集計項目_個社" ColumnName="" DataSourceName="" DataTypeServer="unknown" DbType="AnsiString" Direction="Input" ParameterName="@集計項目_個社" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="集計項目_子会社" ColumnName="" DataSourceName="" DataTypeServer="unknown" DbType="AnsiString" Direction="Input" ParameterName="@集計項目_子会社" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="グループCD" DataSetColumn="グループCD" />
              <Mapping SourceColumn="個社CD" DataSetColumn="個社CD" />
              <Mapping SourceColumn="子会社CD" DataSetColumn="子会社CD" />
              <Mapping SourceColumn="日報日付" DataSetColumn="日報日付" />
              <Mapping SourceColumn="車両番号" DataSetColumn="車両番号" />
              <Mapping SourceColumn="社員NO" DataSetColumn="社員NO" />
              <Mapping SourceColumn="勤務区分" DataSetColumn="勤務区分" />
              <Mapping SourceColumn="納金" DataSetColumn="納金" />
              <Mapping SourceColumn="営収" DataSetColumn="営収" />
              <Mapping SourceColumn="現金A" DataSetColumn="現金A" />
              <Mapping SourceColumn="その他" DataSetColumn="その他" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="F0乗務員予定実績TableAdapter" GeneratorDataComponentClassName="F0乗務員予定実績TableAdapter" Name="F0乗務員予定実績" UserDataComponentName="F0乗務員予定実績TableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionStringBase (MySettings)" DbObjectName="Taxi1010.dbo.F0乗務員予定実績" DbObjectType="Function" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT            グループCD, 個社CD, 子会社CD, 乗務員CD, 乗務員名, 区分ID, 区分名, 日付, 予定, 実績
FROM              dbo.F0乗務員予定実績(@グループCD, @個社CD, @子会社CD, @日付F, @日付T) AS F0乗務員予定実績_1</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="グループCD" ColumnName="" DataSourceName="" DataTypeServer="varchar(1)" DbType="AnsiString" Direction="Input" ParameterName="@グループCD" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="個社CD" ColumnName="" DataSourceName="" DataTypeServer="varchar(2)" DbType="AnsiString" Direction="Input" ParameterName="@個社CD" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="子会社CD" ColumnName="" DataSourceName="" DataTypeServer="varchar(1)" DbType="AnsiString" Direction="Input" ParameterName="@子会社CD" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="日付F" ColumnName="" DataSourceName="" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@日付F" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="日付T" ColumnName="" DataSourceName="" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@日付T" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="グループCD" DataSetColumn="グループCD" />
              <Mapping SourceColumn="個社CD" DataSetColumn="個社CD" />
              <Mapping SourceColumn="子会社CD" DataSetColumn="子会社CD" />
              <Mapping SourceColumn="乗務員CD" DataSetColumn="乗務員CD" />
              <Mapping SourceColumn="乗務員名" DataSetColumn="乗務員名" />
              <Mapping SourceColumn="区分ID" DataSetColumn="区分ID" />
              <Mapping SourceColumn="区分名" DataSetColumn="区分名" />
              <Mapping SourceColumn="日付" DataSetColumn="日付" />
              <Mapping SourceColumn="予定" DataSetColumn="予定" />
              <Mapping SourceColumn="実績" DataSetColumn="実績" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="F0休暇日一覧TableAdapter" GeneratorDataComponentClassName="F0休暇日一覧TableAdapter" Name="F0休暇日一覧" UserDataComponentName="F0休暇日一覧TableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionStringBase (MySettings)" DbObjectName="Taxi.dbo.F0休暇日一覧" DbObjectType="Function" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT                      グループCD, 個社CD, 子会社CD, 乗務員CD, 日付, 休暇区分, 事由, 申請日, 登録ユーザーCD, 登録ユーザー名, 登録日時, 更新ユーザーCD, 更新ユーザー名, 
                                      更新日時
FROM                         dbo.F0休暇日一覧(@グループCD, @個社CD, @子会社CD, @乗務員CD, @日付) AS F0休暇日一覧_1</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="グループCD" ColumnName="" DataSourceName="" DataTypeServer="varchar(1)" DbType="AnsiString" Direction="Input" ParameterName="@グループCD" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="個社CD" ColumnName="" DataSourceName="" DataTypeServer="varchar(2)" DbType="AnsiString" Direction="Input" ParameterName="@個社CD" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="子会社CD" ColumnName="" DataSourceName="" DataTypeServer="varchar(1)" DbType="AnsiString" Direction="Input" ParameterName="@子会社CD" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="乗務員CD" ColumnName="" DataSourceName="" DataTypeServer="varchar(6)" DbType="AnsiString" Direction="Input" ParameterName="@乗務員CD" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="日付" ColumnName="" DataSourceName="" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@日付" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="グループCD" DataSetColumn="グループCD" />
              <Mapping SourceColumn="個社CD" DataSetColumn="個社CD" />
              <Mapping SourceColumn="子会社CD" DataSetColumn="子会社CD" />
              <Mapping SourceColumn="乗務員CD" DataSetColumn="乗務員CD" />
              <Mapping SourceColumn="日付" DataSetColumn="日付" />
              <Mapping SourceColumn="休暇区分" DataSetColumn="休暇区分" />
              <Mapping SourceColumn="事由" DataSetColumn="事由" />
              <Mapping SourceColumn="申請日" DataSetColumn="申請日" />
              <Mapping SourceColumn="登録ユーザーCD" DataSetColumn="登録ユーザーCD" />
              <Mapping SourceColumn="登録ユーザー名" DataSetColumn="登録ユーザー名" />
              <Mapping SourceColumn="登録日時" DataSetColumn="登録日時" />
              <Mapping SourceColumn="更新ユーザーCD" DataSetColumn="更新ユーザーCD" />
              <Mapping SourceColumn="更新ユーザー名" DataSetColumn="更新ユーザー名" />
              <Mapping SourceColumn="更新日時" DataSetColumn="更新日時" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="F0売上データTableAdapter" GeneratorDataComponentClassName="F0売上データTableAdapter" Name="F0売上データ" UserDataComponentName="F0売上データTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionStringBase (MySettings)" DbObjectName="Taxi.dbo.F0売上データ" DbObjectType="Function" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT                      グループCD, 個社CD, 子会社CD, 日報年月, 年度, 月, 週番号, 勤務区分, 乗務員CD, 乗務員名, 稼働数, 売上合計, 予測基準額
FROM                         dbo.F0売上データ(@グループCD, @個社CD, @子会社CD, @inputDate) AS F0売上データ_1</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="グループCD" ColumnName="" DataSourceName="" DataTypeServer="varchar(1)" DbType="AnsiString" Direction="Input" ParameterName="@グループCD" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="個社CD" ColumnName="" DataSourceName="" DataTypeServer="varchar(2)" DbType="AnsiString" Direction="Input" ParameterName="@個社CD" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="子会社CD" ColumnName="" DataSourceName="" DataTypeServer="varchar(1)" DbType="AnsiString" Direction="Input" ParameterName="@子会社CD" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="inputDate" ColumnName="" DataSourceName="" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@inputDate" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="グループCD" DataSetColumn="グループCD" />
              <Mapping SourceColumn="個社CD" DataSetColumn="個社CD" />
              <Mapping SourceColumn="子会社CD" DataSetColumn="子会社CD" />
              <Mapping SourceColumn="日報年月" DataSetColumn="日報年月" />
              <Mapping SourceColumn="年度" DataSetColumn="年度" />
              <Mapping SourceColumn="月" DataSetColumn="月" />
              <Mapping SourceColumn="週番号" DataSetColumn="週番号" />
              <Mapping SourceColumn="勤務区分" DataSetColumn="勤務区分" />
              <Mapping SourceColumn="乗務員CD" DataSetColumn="乗務員CD" />
              <Mapping SourceColumn="乗務員名" DataSetColumn="乗務員名" />
              <Mapping SourceColumn="稼働数" DataSetColumn="稼働数" />
              <Mapping SourceColumn="売上合計" DataSetColumn="売上合計" />
              <Mapping SourceColumn="予測基準額" DataSetColumn="予測基準額" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="F0健康診断受診履歴照会TableAdapter" GeneratorDataComponentClassName="F0健康診断受診履歴照会TableAdapter" Name="F0健康診断受診履歴照会" UserDataComponentName="F0健康診断受診履歴照会TableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionStringBase (MySettings)" DbObjectName="Taxi1010.dbo.F0健康診断受診履歴照会" DbObjectType="Function" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT            グループCD, 個社CD, 子会社CD, 社員名, 社員NO, 勤務区分, 日付, メモ
FROM              dbo.F0健康診断受診履歴照会(@グループCD, @個社CD, @子会社CD, @社員NOF, @社員NOT, @日付F, @日付T, @区分) AS F0健康診断受診履歴照会_1</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="グループCD" ColumnName="" DataSourceName="" DataTypeServer="varchar(1)" DbType="AnsiString" Direction="Input" ParameterName="@グループCD" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="個社CD" ColumnName="" DataSourceName="" DataTypeServer="varchar(2)" DbType="AnsiString" Direction="Input" ParameterName="@個社CD" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="子会社CD" ColumnName="" DataSourceName="" DataTypeServer="varchar(1)" DbType="AnsiString" Direction="Input" ParameterName="@子会社CD" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="社員NOF" ColumnName="" DataSourceName="" DataTypeServer="varchar(5)" DbType="AnsiString" Direction="Input" ParameterName="@社員NOF" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="社員NOT" ColumnName="" DataSourceName="" DataTypeServer="varchar(5)" DbType="AnsiString" Direction="Input" ParameterName="@社員NOT" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="日付F" ColumnName="" DataSourceName="" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@日付F" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="日付T" ColumnName="" DataSourceName="" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@日付T" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="区分" ColumnName="" DataSourceName="" DataTypeServer="varchar(1)" DbType="AnsiString" Direction="Input" ParameterName="@区分" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="グループCD" DataSetColumn="グループCD" />
              <Mapping SourceColumn="個社CD" DataSetColumn="個社CD" />
              <Mapping SourceColumn="子会社CD" DataSetColumn="子会社CD" />
              <Mapping SourceColumn="社員名" DataSetColumn="社員名" />
              <Mapping SourceColumn="社員NO" DataSetColumn="社員NO" />
              <Mapping SourceColumn="勤務区分" DataSetColumn="勤務区分" />
              <Mapping SourceColumn="日付" DataSetColumn="日付" />
              <Mapping SourceColumn="メモ" DataSetColumn="メモ" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="F0消化日データ照会TableAdapter" GeneratorDataComponentClassName="F0消化日データ照会TableAdapter" Name="F0消化日データ照会" UserDataComponentName="F0消化日データ照会TableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionStringBase (MySettings)" DbObjectName="Taxi1010.dbo.F0消化日データ照会" DbObjectType="Function" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT            グループCD, 個社CD, 子会社CD, 乗務員CD, 乗務員名, 社員NO, 休暇区分, 区分名, 日付, 事由
FROM              dbo.F0消化日データ照会(@グループCD, @個社CD, @子会社CD, @社員NOF, @社員NOT, @日付F, @日付T, @区分) AS F0消化日データ照会_1</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="グループCD" ColumnName="" DataSourceName="" DataTypeServer="varchar(1)" DbType="AnsiString" Direction="Input" ParameterName="@グループCD" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="個社CD" ColumnName="" DataSourceName="" DataTypeServer="varchar(2)" DbType="AnsiString" Direction="Input" ParameterName="@個社CD" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="子会社CD" ColumnName="" DataSourceName="" DataTypeServer="varchar(1)" DbType="AnsiString" Direction="Input" ParameterName="@子会社CD" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="社員NOF" ColumnName="" DataSourceName="" DataTypeServer="varchar(5)" DbType="AnsiString" Direction="Input" ParameterName="@社員NOF" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="社員NOT" ColumnName="" DataSourceName="" DataTypeServer="varchar(5)" DbType="AnsiString" Direction="Input" ParameterName="@社員NOT" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="日付F" ColumnName="" DataSourceName="" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@日付F" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="日付T" ColumnName="" DataSourceName="" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@日付T" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="区分" ColumnName="" DataSourceName="" DataTypeServer="varchar(1)" DbType="AnsiString" Direction="Input" ParameterName="@区分" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="グループCD" DataSetColumn="グループCD" />
              <Mapping SourceColumn="個社CD" DataSetColumn="個社CD" />
              <Mapping SourceColumn="子会社CD" DataSetColumn="子会社CD" />
              <Mapping SourceColumn="乗務員CD" DataSetColumn="乗務員CD" />
              <Mapping SourceColumn="乗務員名" DataSetColumn="乗務員名" />
              <Mapping SourceColumn="社員NO" DataSetColumn="社員NO" />
              <Mapping SourceColumn="休暇区分" DataSetColumn="休暇区分" />
              <Mapping SourceColumn="区分名" DataSetColumn="区分名" />
              <Mapping SourceColumn="日付" DataSetColumn="日付" />
              <Mapping SourceColumn="事由" DataSetColumn="事由" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="F0乗務記録実績TableAdapter" GeneratorDataComponentClassName="F0乗務記録実績TableAdapter" Name="F0乗務記録実績" UserDataComponentName="F0乗務記録実績TableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionStringBase (MySettings)" DbObjectName="Taxi1027.dbo.F0乗務記録実績" DbObjectType="Function" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT            グループCD, 個社CD, 子会社CD, 取込日付, 乗務記録NO, 日報日付, 車両番号, 車種CD, 乗務員CD, 乗務員名, 勤務区分, 輸送人員, 走行km, 営業km, 実車回数, 爾後回数, 迎車回数, 予約回数, 実車金額, 
                       爾後金額, 迎車金額, 予約金額, 金額計, 遠割額, 定額差額A, 空転A, 納金, TAX, 営収, チケットA, クーポン四社A, クーポン東旅協A, 福祉券A, カードA, iDA, 交通系ICA, キャブカードA, プリペイドカードA, 
                       WAONA, その他A, 障割A, 現金A, ETC実車A, ETC乗務員A, ETC会社A, 出庫時刻, 帰庫時刻, 総勤務時間, 総休憩時間, 総回送時間, 総空車停止時間, 信販控除除外
FROM              dbo.F0乗務記録実績(@グループCD, @個社CD, @子会社CD, @日付F, @日付T, @データフラグ) AS F0乗務記録実績_1</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="グループCD" ColumnName="" DataSourceName="" DataTypeServer="varchar(1)" DbType="AnsiString" Direction="Input" ParameterName="@グループCD" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="個社CD" ColumnName="" DataSourceName="" DataTypeServer="varchar(2)" DbType="AnsiString" Direction="Input" ParameterName="@個社CD" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="子会社CD" ColumnName="" DataSourceName="" DataTypeServer="varchar(1)" DbType="AnsiString" Direction="Input" ParameterName="@子会社CD" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="日付F" ColumnName="" DataSourceName="" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@日付F" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="日付T" ColumnName="" DataSourceName="" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@日付T" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="データフラグ" ColumnName="" DataSourceName="" DataTypeServer="varchar(1)" DbType="AnsiString" Direction="Input" ParameterName="@データフラグ" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="グループCD" DataSetColumn="グループCD" />
              <Mapping SourceColumn="個社CD" DataSetColumn="個社CD" />
              <Mapping SourceColumn="子会社CD" DataSetColumn="子会社CD" />
              <Mapping SourceColumn="取込日付" DataSetColumn="取込日付" />
              <Mapping SourceColumn="乗務記録NO" DataSetColumn="乗務記録NO" />
              <Mapping SourceColumn="日報日付" DataSetColumn="日報日付" />
              <Mapping SourceColumn="車両番号" DataSetColumn="車両番号" />
              <Mapping SourceColumn="車種CD" DataSetColumn="車種CD" />
              <Mapping SourceColumn="乗務員CD" DataSetColumn="乗務員CD" />
              <Mapping SourceColumn="乗務員名" DataSetColumn="乗務員名" />
              <Mapping SourceColumn="勤務区分" DataSetColumn="勤務区分" />
              <Mapping SourceColumn="輸送人員" DataSetColumn="輸送人員" />
              <Mapping SourceColumn="走行km" DataSetColumn="走行km" />
              <Mapping SourceColumn="営業km" DataSetColumn="営業km" />
              <Mapping SourceColumn="実車回数" DataSetColumn="実車回数" />
              <Mapping SourceColumn="爾後回数" DataSetColumn="爾後回数" />
              <Mapping SourceColumn="迎車回数" DataSetColumn="迎車回数" />
              <Mapping SourceColumn="予約回数" DataSetColumn="予約回数" />
              <Mapping SourceColumn="実車金額" DataSetColumn="実車金額" />
              <Mapping SourceColumn="爾後金額" DataSetColumn="爾後金額" />
              <Mapping SourceColumn="迎車金額" DataSetColumn="迎車金額" />
              <Mapping SourceColumn="予約金額" DataSetColumn="予約金額" />
              <Mapping SourceColumn="金額計" DataSetColumn="金額計" />
              <Mapping SourceColumn="遠割額" DataSetColumn="遠割額" />
              <Mapping SourceColumn="定額差額A" DataSetColumn="定額差額A" />
              <Mapping SourceColumn="空転A" DataSetColumn="空転A" />
              <Mapping SourceColumn="納金" DataSetColumn="納金" />
              <Mapping SourceColumn="TAX" DataSetColumn="TAX" />
              <Mapping SourceColumn="営収" DataSetColumn="営収" />
              <Mapping SourceColumn="チケットA" DataSetColumn="チケットA" />
              <Mapping SourceColumn="クーポン四社A" DataSetColumn="クーポン四社A" />
              <Mapping SourceColumn="クーポン東旅協A" DataSetColumn="クーポン東旅協A" />
              <Mapping SourceColumn="福祉券A" DataSetColumn="福祉券A" />
              <Mapping SourceColumn="カードA" DataSetColumn="カードA" />
              <Mapping SourceColumn="iDA" DataSetColumn="iDA" />
              <Mapping SourceColumn="交通系ICA" DataSetColumn="交通系ICA" />
              <Mapping SourceColumn="キャブカードA" DataSetColumn="キャブカードA" />
              <Mapping SourceColumn="プリペイドカードA" DataSetColumn="プリペイドカードA" />
              <Mapping SourceColumn="WAONA" DataSetColumn="WAONA" />
              <Mapping SourceColumn="その他A" DataSetColumn="その他A" />
              <Mapping SourceColumn="障割A" DataSetColumn="障割A" />
              <Mapping SourceColumn="現金A" DataSetColumn="現金A" />
              <Mapping SourceColumn="ETC実車A" DataSetColumn="ETC実車A" />
              <Mapping SourceColumn="ETC乗務員A" DataSetColumn="ETC乗務員A" />
              <Mapping SourceColumn="ETC会社A" DataSetColumn="ETC会社A" />
              <Mapping SourceColumn="出庫時刻" DataSetColumn="出庫時刻" />
              <Mapping SourceColumn="帰庫時刻" DataSetColumn="帰庫時刻" />
              <Mapping SourceColumn="総勤務時間" DataSetColumn="総勤務時間" />
              <Mapping SourceColumn="総休憩時間" DataSetColumn="総休憩時間" />
              <Mapping SourceColumn="総回送時間" DataSetColumn="総回送時間" />
              <Mapping SourceColumn="総空車停止時間" DataSetColumn="総空車停止時間" />
              <Mapping SourceColumn="信販控除除外" DataSetColumn="信販控除除外" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="F0給与基準データTableAdapter" GeneratorDataComponentClassName="F0給与基準データTableAdapter" Name="F0給与基準データ" UserDataComponentName="F0給与基準データTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionStringBase (MySettings)" DbObjectName="Taxi.dbo.F0給与基準データ" DbObjectType="Function" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT                      グループCD, 個社CD, 子会社CD, 乗務員CD, 氏名, シフト区分, 区分名, シフト変更, 基準乗務数, 通常, 公出, 実乗務数_合計, 差引数, 有給, 欠勤, 合計, 営収の合計, 
                                      信販の合計, 前月繰越
FROM                         dbo.F0給与基準データ(@グループCD, @個社CD, @子会社CD, @日付F, @日付T) AS F0給与基準データ_1</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="グループCD" ColumnName="" DataSourceName="" DataTypeServer="varchar(1)" DbType="AnsiString" Direction="Input" ParameterName="@グループCD" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="個社CD" ColumnName="" DataSourceName="" DataTypeServer="varchar(2)" DbType="AnsiString" Direction="Input" ParameterName="@個社CD" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="子会社CD" ColumnName="" DataSourceName="" DataTypeServer="varchar(1)" DbType="AnsiString" Direction="Input" ParameterName="@子会社CD" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="日付F" ColumnName="" DataSourceName="" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@日付F" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="日付T" ColumnName="" DataSourceName="" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@日付T" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="グループCD" DataSetColumn="グループCD" />
              <Mapping SourceColumn="個社CD" DataSetColumn="個社CD" />
              <Mapping SourceColumn="子会社CD" DataSetColumn="子会社CD" />
              <Mapping SourceColumn="乗務員CD" DataSetColumn="乗務員CD" />
              <Mapping SourceColumn="氏名" DataSetColumn="氏名" />
              <Mapping SourceColumn="シフト区分" DataSetColumn="シフト区分" />
              <Mapping SourceColumn="区分名" DataSetColumn="区分名" />
              <Mapping SourceColumn="シフト変更" DataSetColumn="シフト変更" />
              <Mapping SourceColumn="基準乗務数" DataSetColumn="基準乗務数" />
              <Mapping SourceColumn="通常" DataSetColumn="通常" />
              <Mapping SourceColumn="公出" DataSetColumn="公出" />
              <Mapping SourceColumn="実乗務数_合計" DataSetColumn="実乗務数_合計" />
              <Mapping SourceColumn="差引数" DataSetColumn="差引数" />
              <Mapping SourceColumn="有給" DataSetColumn="有給" />
              <Mapping SourceColumn="欠勤" DataSetColumn="欠勤" />
              <Mapping SourceColumn="合計" DataSetColumn="合計" />
              <Mapping SourceColumn="営収の合計" DataSetColumn="営収の合計" />
              <Mapping SourceColumn="信販の合計" DataSetColumn="信販の合計" />
              <Mapping SourceColumn="前月繰越" DataSetColumn="前月繰越" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="F0乗務記録HDTableAdapter" GeneratorDataComponentClassName="F0乗務記録HDTableAdapter" Name="F0乗務記録HD" UserDataComponentName="F0乗務記録HDTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionStringBase (MySettings)" DbObjectName="TaxiSystem.dbo.F0乗務記録HD" DbObjectType="Function" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT                      F0乗務記録HD_1.*
FROM                         dbo.F0乗務記録HD() AS F0乗務記録HD_1</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="グループCD" DataSetColumn="グループCD" />
              <Mapping SourceColumn="個社CD" DataSetColumn="個社CD" />
              <Mapping SourceColumn="子会社CD" DataSetColumn="子会社CD" />
              <Mapping SourceColumn="日報日付" DataSetColumn="日報日付" />
              <Mapping SourceColumn="日報年月" DataSetColumn="日報年月" />
              <Mapping SourceColumn="出庫時刻" DataSetColumn="出庫時刻" />
              <Mapping SourceColumn="入庫時刻" DataSetColumn="入庫時刻" />
              <Mapping SourceColumn="乗務員CD" DataSetColumn="乗務員CD" />
              <Mapping SourceColumn="車両番号" DataSetColumn="車両番号" />
              <Mapping SourceColumn="乗務記録NO" DataSetColumn="乗務記録NO" />
              <Mapping SourceColumn="取込日付" DataSetColumn="取込日付" />
              <Mapping SourceColumn="営業所CD" DataSetColumn="営業所CD" />
              <Mapping SourceColumn="部門CD" DataSetColumn="部門CD" />
              <Mapping SourceColumn="勤務CD" DataSetColumn="勤務CD" />
              <Mapping SourceColumn="車種CD" DataSetColumn="車種CD" />
              <Mapping SourceColumn="天気" DataSetColumn="天気" />
              <Mapping SourceColumn="累計全走行" DataSetColumn="累計全走行" />
              <Mapping SourceColumn="累計実車走行" DataSetColumn="累計実車走行" />
              <Mapping SourceColumn="累計迎車走行" DataSetColumn="累計迎車走行" />
              <Mapping SourceColumn="累計営業回数" DataSetColumn="累計営業回数" />
              <Mapping SourceColumn="累計以後回数" DataSetColumn="累計以後回数" />
              <Mapping SourceColumn="累計迎車回数" DataSetColumn="累計迎車回数" />
              <Mapping SourceColumn="一日全走行" DataSetColumn="一日全走行" />
              <Mapping SourceColumn="一日実車走行" DataSetColumn="一日実車走行" />
              <Mapping SourceColumn="一日迎車走行" DataSetColumn="一日迎車走行" />
              <Mapping SourceColumn="一日営業回数" DataSetColumn="一日営業回数" />
              <Mapping SourceColumn="一日以後回数" DataSetColumn="一日以後回数" />
              <Mapping SourceColumn="一日迎車回数" DataSetColumn="一日迎車回数" />
              <Mapping SourceColumn="累計運賃" DataSetColumn="累計運賃" />
              <Mapping SourceColumn="累計料金" DataSetColumn="累計料金" />
              <Mapping SourceColumn="累計身割回数" DataSetColumn="累計身割回数" />
              <Mapping SourceColumn="累計身割額" DataSetColumn="累計身割額" />
              <Mapping SourceColumn="累計ワゴン回数" DataSetColumn="累計ワゴン回数" />
              <Mapping SourceColumn="累計早朝回数" DataSetColumn="累計早朝回数" />
              <Mapping SourceColumn="累計予約回数" DataSetColumn="累計予約回数" />
              <Mapping SourceColumn="累計遠割回数" DataSetColumn="累計遠割回数" />
              <Mapping SourceColumn="累計遠割額" DataSetColumn="累計遠割額" />
              <Mapping SourceColumn="累計貸切指数" DataSetColumn="累計貸切指数" />
              <Mapping SourceColumn="累計貸切割引額" DataSetColumn="累計貸切割引額" />
              <Mapping SourceColumn="累計貸切料金" DataSetColumn="累計貸切料金" />
              <Mapping SourceColumn="累計貸切時間" DataSetColumn="累計貸切時間" />
              <Mapping SourceColumn="累計貸切走行" DataSetColumn="累計貸切走行" />
              <Mapping SourceColumn="累計固定料金キャンセル回数" DataSetColumn="累計固定料金キャンセル回数" />
              <Mapping SourceColumn="累計迎車キャンセル回数" DataSetColumn="累計迎車キャンセル回数" />
              <Mapping SourceColumn="累計待回数" DataSetColumn="累計待回数" />
              <Mapping SourceColumn="累計待加算回数" DataSetColumn="累計待加算回数" />
              <Mapping SourceColumn="累計早朝キャンセル回数" DataSetColumn="累計早朝キャンセル回数" />
              <Mapping SourceColumn="累計高齢割回数" DataSetColumn="累計高齢割回数" />
              <Mapping SourceColumn="累計高齢割額" DataSetColumn="累計高齢割額" />
              <Mapping SourceColumn="累計幼児割回数" DataSetColumn="累計幼児割回数" />
              <Mapping SourceColumn="累計幼児割額" DataSetColumn="累計幼児割額" />
              <Mapping SourceColumn="累計リセット回数" DataSetColumn="累計リセット回数" />
              <Mapping SourceColumn="累計待回数P" DataSetColumn="累計待回数P" />
              <Mapping SourceColumn="一日運賃" DataSetColumn="一日運賃" />
              <Mapping SourceColumn="一日料金" DataSetColumn="一日料金" />
              <Mapping SourceColumn="一日身割回数" DataSetColumn="一日身割回数" />
              <Mapping SourceColumn="一日身割額" DataSetColumn="一日身割額" />
              <Mapping SourceColumn="一日身割現収" DataSetColumn="一日身割現収" />
              <Mapping SourceColumn="一日ワゴン回数" DataSetColumn="一日ワゴン回数" />
              <Mapping SourceColumn="一日早朝回数" DataSetColumn="一日早朝回数" />
              <Mapping SourceColumn="一日予約回数" DataSetColumn="一日予約回数" />
              <Mapping SourceColumn="一日遠割回数" DataSetColumn="一日遠割回数" />
              <Mapping SourceColumn="一日遠割額" DataSetColumn="一日遠割額" />
              <Mapping SourceColumn="一日貸切指数" DataSetColumn="一日貸切指数" />
              <Mapping SourceColumn="一日貸切割引" DataSetColumn="一日貸切割引" />
              <Mapping SourceColumn="一日貸切料金" DataSetColumn="一日貸切料金" />
              <Mapping SourceColumn="一日貸切時間" DataSetColumn="一日貸切時間" />
              <Mapping SourceColumn="一日貸切走行" DataSetColumn="一日貸切走行" />
              <Mapping SourceColumn="一日固定料金キャンセル回数" DataSetColumn="一日固定料金キャンセル回数" />
              <Mapping SourceColumn="一日迎車キャンセル回数" DataSetColumn="一日迎車キャンセル回数" />
              <Mapping SourceColumn="一日待ち回数" DataSetColumn="一日待ち回数" />
              <Mapping SourceColumn="一日待ち加算回数" DataSetColumn="一日待ち加算回数" />
              <Mapping SourceColumn="一日早朝キャンセル回数" DataSetColumn="一日早朝キャンセル回数" />
              <Mapping SourceColumn="一日高齢割回数" DataSetColumn="一日高齢割回数" />
              <Mapping SourceColumn="一日高齢割額" DataSetColumn="一日高齢割額" />
              <Mapping SourceColumn="一日幼児割回数" DataSetColumn="一日幼児割回数" />
              <Mapping SourceColumn="一日幼児割額" DataSetColumn="一日幼児割額" />
              <Mapping SourceColumn="一日リセット回数" DataSetColumn="一日リセット回数" />
              <Mapping SourceColumn="一日待ち回数P" DataSetColumn="一日待ち回数P" />
              <Mapping SourceColumn="基本料金" DataSetColumn="基本料金" />
              <Mapping SourceColumn="以後料金" DataSetColumn="以後料金" />
              <Mapping SourceColumn="固定料金" DataSetColumn="固定料金" />
              <Mapping SourceColumn="月間営収" DataSetColumn="月間営収" />
              <Mapping SourceColumn="営収" DataSetColumn="営収" />
              <Mapping SourceColumn="男" DataSetColumn="男" />
              <Mapping SourceColumn="女" DataSetColumn="女" />
              <Mapping SourceColumn="現金" DataSetColumn="現金" />
              <Mapping SourceColumn="未収" DataSetColumn="未収" />
              <Mapping SourceColumn="クレジット" DataSetColumn="クレジット" />
              <Mapping SourceColumn="カード" DataSetColumn="カード" />
              <Mapping SourceColumn="ID" DataSetColumn="ID" />
              <Mapping SourceColumn="交通系IC" DataSetColumn="交通系IC" />
              <Mapping SourceColumn="キャブカード" DataSetColumn="キャブカード" />
              <Mapping SourceColumn="プリペイドカード" DataSetColumn="プリペイドカード" />
              <Mapping SourceColumn="WAON" DataSetColumn="WAON" />
              <Mapping SourceColumn="その他" DataSetColumn="その他" />
              <Mapping SourceColumn="障割" DataSetColumn="障割" />
              <Mapping SourceColumn="メーター外料金" DataSetColumn="メーター外料金" />
              <Mapping SourceColumn="クレジット回数" DataSetColumn="クレジット回数" />
              <Mapping SourceColumn="貸切回数" DataSetColumn="貸切回数" />
              <Mapping SourceColumn="空車_ETC料金" DataSetColumn="空車_ETC料金" />
              <Mapping SourceColumn="実車_ETC料金" DataSetColumn="実車_ETC料金" />
              <Mapping SourceColumn="燃料合計" DataSetColumn="燃料合計" />
              <Mapping SourceColumn="総勤務時間" DataSetColumn="総勤務時間" />
              <Mapping SourceColumn="総休憩時間" DataSetColumn="総休憩時間" />
              <Mapping SourceColumn="総回送時間" DataSetColumn="総回送時間" />
              <Mapping SourceColumn="総空車停止時間" DataSetColumn="総空車停止時間" />
              <Mapping SourceColumn="リセット待回数" DataSetColumn="リセット待回数" />
              <Mapping SourceColumn="出庫_親メーター" DataSetColumn="出庫_親メーター" />
              <Mapping SourceColumn="入庫_親メーター" DataSetColumn="入庫_親メーター" />
              <Mapping SourceColumn="空車_最高速度" DataSetColumn="空車_最高速度" />
              <Mapping SourceColumn="実車_最高速度" DataSetColumn="実車_最高速度" />
              <Mapping SourceColumn="最高速度" DataSetColumn="最高速度" />
              <Mapping SourceColumn="削除フラグ" DataSetColumn="削除フラグ" />
              <Mapping SourceColumn="定額差額A" DataSetColumn="定額差額A" />
              <Mapping SourceColumn="チケットA" DataSetColumn="チケットA" />
              <Mapping SourceColumn="クーポン四社A" DataSetColumn="クーポン四社A" />
              <Mapping SourceColumn="クーポン東旅協A" DataSetColumn="クーポン東旅協A" />
              <Mapping SourceColumn="福祉券A" DataSetColumn="福祉券A" />
              <Mapping SourceColumn="カードA" DataSetColumn="カードA" />
              <Mapping SourceColumn="iDA" DataSetColumn="iDA" />
              <Mapping SourceColumn="交通系ICA" DataSetColumn="交通系ICA" />
              <Mapping SourceColumn="キャブカードA" DataSetColumn="キャブカードA" />
              <Mapping SourceColumn="プリペイドカードA" DataSetColumn="プリペイドカードA" />
              <Mapping SourceColumn="WAONA" DataSetColumn="WAONA" />
              <Mapping SourceColumn="その他A" DataSetColumn="その他A" />
              <Mapping SourceColumn="障割A" DataSetColumn="障割A" />
              <Mapping SourceColumn="現金A" DataSetColumn="現金A" />
              <Mapping SourceColumn="空転A" DataSetColumn="空転A" />
              <Mapping SourceColumn="信販控除除外" DataSetColumn="信販控除除外" />
              <Mapping SourceColumn="ETC実車A" DataSetColumn="ETC実車A" />
              <Mapping SourceColumn="ETC乗務員A" DataSetColumn="ETC乗務員A" />
              <Mapping SourceColumn="ETC会社A" DataSetColumn="ETC会社A" />
              <Mapping SourceColumn="登録ユーザーCD" DataSetColumn="登録ユーザーCD" />
              <Mapping SourceColumn="登録ユーザー名" DataSetColumn="登録ユーザー名" />
              <Mapping SourceColumn="登録日時" DataSetColumn="登録日時" />
              <Mapping SourceColumn="更新ユーザーCD" DataSetColumn="更新ユーザーCD" />
              <Mapping SourceColumn="更新ユーザー名" DataSetColumn="更新ユーザー名" />
              <Mapping SourceColumn="更新日時" DataSetColumn="更新日時" />
              <Mapping SourceColumn="回数料金" DataSetColumn="回数料金" />
              <Mapping SourceColumn="爾後料金" DataSetColumn="爾後料金" />
              <Mapping SourceColumn="迎車料金" DataSetColumn="迎車料金" />
              <Mapping SourceColumn="予約料金" DataSetColumn="予約料金" />
              <Mapping SourceColumn="料金合計" DataSetColumn="料金合計" />
              <Mapping SourceColumn="料金内訳合計" DataSetColumn="料金内訳合計" />
              <Mapping SourceColumn="ETC合計" DataSetColumn="ETC合計" />
              <Mapping SourceColumn="回数単価" DataSetColumn="回数単価" />
              <Mapping SourceColumn="爾後単価" DataSetColumn="爾後単価" />
              <Mapping SourceColumn="迎車単価" DataSetColumn="迎車単価" />
              <Mapping SourceColumn="予約単価" DataSetColumn="予約単価" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="F0事故登録照会TableAdapter" GeneratorDataComponentClassName="F0事故登録照会TableAdapter" Name="F0事故登録照会" UserDataComponentName="F0事故登録照会TableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionStringBase (MySettings)" DbObjectName="Taxi240215.dbo.F0事故登録照会" DbObjectType="Function" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT                      グループCD, 個社CD, 子会社CD, 社員名, 社員NO, 事故区分, 日付, 自社損害金, 相手損害金, その他
FROM                         dbo.F0事故登録照会(@グループCD, @個社CD, @子会社CD, @社員NOF, @社員NOT, @日付F, @日付T, @区分) AS F0事故登録照会_1</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="グループCD" ColumnName="" DataSourceName="" DataTypeServer="varchar(1)" DbType="AnsiString" Direction="Input" ParameterName="@グループCD" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="個社CD" ColumnName="" DataSourceName="" DataTypeServer="varchar(2)" DbType="AnsiString" Direction="Input" ParameterName="@個社CD" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="子会社CD" ColumnName="" DataSourceName="" DataTypeServer="varchar(1)" DbType="AnsiString" Direction="Input" ParameterName="@子会社CD" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="社員NOF" ColumnName="" DataSourceName="" DataTypeServer="varchar(5)" DbType="AnsiString" Direction="Input" ParameterName="@社員NOF" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="社員NOT" ColumnName="" DataSourceName="" DataTypeServer="varchar(5)" DbType="AnsiString" Direction="Input" ParameterName="@社員NOT" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="日付F" ColumnName="" DataSourceName="" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@日付F" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="日付T" ColumnName="" DataSourceName="" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@日付T" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="区分" ColumnName="" DataSourceName="" DataTypeServer="varchar(1)" DbType="AnsiString" Direction="Input" ParameterName="@区分" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="グループCD" DataSetColumn="グループCD" />
              <Mapping SourceColumn="個社CD" DataSetColumn="個社CD" />
              <Mapping SourceColumn="子会社CD" DataSetColumn="子会社CD" />
              <Mapping SourceColumn="社員名" DataSetColumn="社員名" />
              <Mapping SourceColumn="社員NO" DataSetColumn="社員NO" />
              <Mapping SourceColumn="事故区分" DataSetColumn="事故区分" />
              <Mapping SourceColumn="日付" DataSetColumn="日付" />
              <Mapping SourceColumn="自社損害金" DataSetColumn="自社損害金" />
              <Mapping SourceColumn="相手損害金" DataSetColumn="相手損害金" />
              <Mapping SourceColumn="その他" DataSetColumn="その他" />
            </Mappings>
            <Sources />
          </TableAdapter>
        </Tables>
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="DataSetFunc" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:EnableTableAdapterManager="true" msprop:Generator_DataSetName="DataSetFunc" msprop:Generator_UserDSName="DataSetFunc">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="F0レイアウトHD" msprop:Generator_TableClassName="F0レイアウトHDDataTable" msprop:Generator_TableVarName="tableF0レイアウトHD" msprop:Generator_TablePropName="F0レイアウトHD" msprop:Generator_RowDeletingName="F0レイアウトHDRowDeleting" msprop:Generator_RowChangingName="F0レイアウトHDRowChanging" msprop:Generator_RowEvHandlerName="F0レイアウトHDRowChangeEventHandler" msprop:Generator_RowDeletedName="F0レイアウトHDRowDeleted" msprop:Generator_UserTableName="F0レイアウトHD" msprop:Generator_RowChangedName="F0レイアウトHDRowChanged" msprop:Generator_RowEvArgName="F0レイアウトHDRowChangeEvent" msprop:Generator_RowClassName="F0レイアウトHDRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="レイアウト名" msprop:Generator_ColumnVarNameInTable="columnレイアウト名" msprop:Generator_ColumnPropNameInRow="レイアウト名" msprop:Generator_ColumnPropNameInTable="レイアウト名Column" msprop:Generator_UserColumnName="レイアウト名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="60" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ファイル形式" msprop:Generator_ColumnVarNameInTable="columnファイル形式" msprop:Generator_ColumnPropNameInRow="ファイル形式" msprop:Generator_ColumnPropNameInTable="ファイル形式Column" msprop:Generator_UserColumnName="ファイル形式" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="先頭タイトル" msprop:Generator_ColumnVarNameInTable="column先頭タイトル" msprop:Generator_ColumnPropNameInRow="先頭タイトル" msprop:Generator_ColumnPropNameInTable="先頭タイトルColumn" msprop:Generator_UserColumnName="先頭タイトル" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="最終ディレクトリ" msprop:Generator_ColumnVarNameInTable="column最終ディレクトリ" msprop:Generator_ColumnPropNameInRow="最終ディレクトリ" msprop:Generator_ColumnPropNameInTable="最終ディレクトリColumn" msprop:Generator_UserColumnName="最終ディレクトリ" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="255" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="登録日時" msprop:Generator_ColumnVarNameInTable="column登録日時" msprop:Generator_ColumnPropNameInRow="登録日時" msprop:Generator_ColumnPropNameInTable="登録日時Column" msprop:Generator_UserColumnName="登録日時" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="19" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="更新日時" msprop:Generator_ColumnVarNameInTable="column更新日時" msprop:Generator_ColumnPropNameInRow="更新日時" msprop:Generator_ColumnPropNameInTable="更新日時Column" msprop:Generator_UserColumnName="更新日時" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="19" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="更新者名" msprop:Generator_ColumnVarNameInTable="column更新者名" msprop:Generator_ColumnPropNameInRow="更新者名" msprop:Generator_ColumnPropNameInTable="更新者名Column" msprop:Generator_UserColumnName="更新者名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="60" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="レイアウトCD" msprop:Generator_ColumnVarNameInTable="columnレイアウトCD" msprop:Generator_ColumnPropNameInRow="レイアウトCD" msprop:Generator_ColumnPropNameInTable="レイアウトCDColumn" msprop:Generator_UserColumnName="レイアウトCD">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="3" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="F0レイアウト条件" msprop:Generator_TableClassName="F0レイアウト条件DataTable" msprop:Generator_TableVarName="tableF0レイアウト条件" msprop:Generator_TablePropName="F0レイアウト条件" msprop:Generator_RowDeletingName="F0レイアウト条件RowDeleting" msprop:Generator_RowChangingName="F0レイアウト条件RowChanging" msprop:Generator_RowEvHandlerName="F0レイアウト条件RowChangeEventHandler" msprop:Generator_RowDeletedName="F0レイアウト条件RowDeleted" msprop:Generator_UserTableName="F0レイアウト条件" msprop:Generator_RowChangedName="F0レイアウト条件RowChanged" msprop:Generator_RowEvArgName="F0レイアウト条件RowChangeEvent" msprop:Generator_RowClassName="F0レイアウト条件Row">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="条件カラム位置" msprop:Generator_ColumnVarNameInTable="column条件カラム位置" msprop:Generator_ColumnPropNameInRow="条件カラム位置" msprop:Generator_ColumnPropNameInTable="条件カラム位置Column" msprop:Generator_UserColumnName="条件カラム位置" type="xs:decimal" minOccurs="0" />
              <xs:element name="条件項目値" msprop:Generator_ColumnVarNameInTable="column条件項目値" msprop:Generator_ColumnPropNameInRow="条件項目値" msprop:Generator_ColumnPropNameInTable="条件項目値Column" msprop:Generator_UserColumnName="条件項目値" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="4000" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="符号区分" msprop:Generator_ColumnVarNameInTable="column符号区分" msprop:Generator_ColumnPropNameInRow="符号区分" msprop:Generator_ColumnPropNameInTable="符号区分Column" msprop:Generator_UserColumnName="符号区分" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="結果項目値" msprop:Generator_ColumnVarNameInTable="column結果項目値" msprop:Generator_ColumnPropNameInRow="結果項目値" msprop:Generator_ColumnPropNameInTable="結果項目値Column" msprop:Generator_UserColumnName="結果項目値" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="4000" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="結果伝票項目名" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column結果伝票項目名" msprop:Generator_ColumnPropNameInRow="結果伝票項目名" msprop:Generator_ColumnPropNameInTable="結果伝票項目名Column" msprop:Generator_UserColumnName="結果伝票項目名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="30" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="結果伝票項目名表示" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column結果伝票項目名表示" msprop:Generator_ColumnPropNameInRow="結果伝票項目名表示" msprop:Generator_ColumnPropNameInTable="結果伝票項目名表示Column" msprop:Generator_UserColumnName="結果伝票項目名表示" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="30" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="結果キーフラグ" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column結果キーフラグ" msprop:Generator_ColumnPropNameInRow="結果キーフラグ" msprop:Generator_ColumnPropNameInTable="結果キーフラグColumn" msprop:Generator_UserColumnName="結果キーフラグ" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="符号区分名" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column符号区分名" msprop:Generator_ColumnPropNameInRow="符号区分名" msprop:Generator_ColumnPropNameInTable="符号区分名Column" msprop:Generator_UserColumnName="符号区分名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="レイアウトCD" msprop:Generator_ColumnVarNameInTable="columnレイアウトCD" msprop:Generator_ColumnPropNameInRow="レイアウトCD" msprop:Generator_ColumnPropNameInTable="レイアウトCDColumn" msprop:Generator_UserColumnName="レイアウトCD" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="3" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="結果伝票項目CD" msprop:Generator_ColumnVarNameInTable="column結果伝票項目CD" msprop:Generator_ColumnPropNameInRow="結果伝票項目CD" msprop:Generator_ColumnPropNameInTable="結果伝票項目CDColumn" msprop:Generator_UserColumnName="結果伝票項目CD" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="3" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="F0レイアウト" msprop:Generator_TableClassName="F0レイアウトDataTable" msprop:Generator_TableVarName="tableF0レイアウト" msprop:Generator_TablePropName="F0レイアウト" msprop:Generator_RowDeletingName="F0レイアウトRowDeleting" msprop:Generator_RowChangingName="F0レイアウトRowChanging" msprop:Generator_RowEvHandlerName="F0レイアウトRowChangeEventHandler" msprop:Generator_RowDeletedName="F0レイアウトRowDeleted" msprop:Generator_UserTableName="F0レイアウト" msprop:Generator_RowChangedName="F0レイアウトRowChanged" msprop:Generator_RowEvArgName="F0レイアウトRowChangeEvent" msprop:Generator_RowClassName="F0レイアウトRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="レイアウトCD" msprop:Generator_ColumnVarNameInTable="columnレイアウトCD" msprop:Generator_ColumnPropNameInRow="レイアウトCD" msprop:Generator_ColumnPropNameInTable="レイアウトCDColumn" msprop:Generator_UserColumnName="レイアウトCD">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="3" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="レイアウト名" msprop:Generator_ColumnVarNameInTable="columnレイアウト名" msprop:Generator_ColumnPropNameInRow="レイアウト名" msprop:Generator_ColumnPropNameInTable="レイアウト名Column" msprop:Generator_UserColumnName="レイアウト名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="60" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ファイル形式" msprop:Generator_ColumnVarNameInTable="columnファイル形式" msprop:Generator_ColumnPropNameInRow="ファイル形式" msprop:Generator_ColumnPropNameInTable="ファイル形式Column" msprop:Generator_UserColumnName="ファイル形式" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="先頭タイトル" msprop:Generator_ColumnVarNameInTable="column先頭タイトル" msprop:Generator_ColumnPropNameInRow="先頭タイトル" msprop:Generator_ColumnPropNameInTable="先頭タイトルColumn" msprop:Generator_UserColumnName="先頭タイトル" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="最終ディレクトリ" msprop:Generator_ColumnVarNameInTable="column最終ディレクトリ" msprop:Generator_ColumnPropNameInRow="最終ディレクトリ" msprop:Generator_ColumnPropNameInTable="最終ディレクトリColumn" msprop:Generator_UserColumnName="最終ディレクトリ" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="255" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="登録日時" msprop:Generator_ColumnVarNameInTable="column登録日時" msprop:Generator_ColumnPropNameInRow="登録日時" msprop:Generator_ColumnPropNameInTable="登録日時Column" msprop:Generator_UserColumnName="登録日時" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="19" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="更新日時" msprop:Generator_ColumnVarNameInTable="column更新日時" msprop:Generator_ColumnPropNameInRow="更新日時" msprop:Generator_ColumnPropNameInTable="更新日時Column" msprop:Generator_UserColumnName="更新日時" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="19" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="更新者名" msprop:Generator_ColumnVarNameInTable="column更新者名" msprop:Generator_ColumnPropNameInRow="更新者名" msprop:Generator_ColumnPropNameInTable="更新者名Column" msprop:Generator_UserColumnName="更新者名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="60" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="伝票項目コード" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column伝票項目コード" msprop:Generator_ColumnPropNameInRow="伝票項目コード" msprop:Generator_ColumnPropNameInTable="伝票項目コードColumn" msprop:Generator_UserColumnName="伝票項目コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="3" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="カラム位置" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnカラム位置" msprop:Generator_ColumnPropNameInRow="カラム位置" msprop:Generator_ColumnPropNameInTable="カラム位置Column" msprop:Generator_UserColumnName="カラム位置" type="xs:decimal" minOccurs="0" />
              <xs:element name="既定値" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column既定値" msprop:Generator_ColumnPropNameInRow="既定値" msprop:Generator_ColumnPropNameInTable="既定値Column" msprop:Generator_UserColumnName="既定値" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="255" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="キーフラグ" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnキーフラグ" msprop:Generator_ColumnPropNameInRow="キーフラグ" msprop:Generator_ColumnPropNameInTable="キーフラグColumn" msprop:Generator_UserColumnName="キーフラグ" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="取込優先順" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column取込優先順" msprop:Generator_ColumnPropNameInRow="取込優先順" msprop:Generator_ColumnPropNameInTable="取込優先順Column" msprop:Generator_UserColumnName="取込優先順" type="xs:decimal" minOccurs="0" />
              <xs:element name="伝票項目名" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column伝票項目名" msprop:Generator_ColumnPropNameInRow="伝票項目名" msprop:Generator_ColumnPropNameInTable="伝票項目名Column" msprop:Generator_UserColumnName="伝票項目名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="30" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="伝票項目名表示" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column伝票項目名表示" msprop:Generator_ColumnPropNameInRow="伝票項目名表示" msprop:Generator_ColumnPropNameInTable="伝票項目名表示Column" msprop:Generator_UserColumnName="伝票項目名表示" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="30" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="表示位置" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column表示位置" msprop:Generator_ColumnPropNameInRow="表示位置" msprop:Generator_ColumnPropNameInTable="表示位置Column" msprop:Generator_UserColumnName="表示位置" type="xs:decimal" minOccurs="0" />
              <xs:element name="ファイル形式名" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnファイル形式名" msprop:Generator_ColumnPropNameInRow="ファイル形式名" msprop:Generator_ColumnPropNameInTable="ファイル形式名Column" msprop:Generator_UserColumnName="ファイル形式名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="F0シフト一覧" msprop:Generator_TableClassName="F0シフト一覧DataTable" msprop:Generator_TableVarName="tableF0シフト一覧" msprop:Generator_RowChangedName="F0シフト一覧RowChanged" msprop:Generator_TablePropName="F0シフト一覧" msprop:Generator_RowDeletingName="F0シフト一覧RowDeleting" msprop:Generator_RowChangingName="F0シフト一覧RowChanging" msprop:Generator_RowEvHandlerName="F0シフト一覧RowChangeEventHandler" msprop:Generator_RowDeletedName="F0シフト一覧RowDeleted" msprop:Generator_RowClassName="F0シフト一覧Row" msprop:Generator_UserTableName="F0シフト一覧" msprop:Generator_RowEvArgName="F0シフト一覧RowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="グループCD" msprop:Generator_ColumnVarNameInTable="columnグループCD" msprop:Generator_ColumnPropNameInRow="グループCD" msprop:Generator_ColumnPropNameInTable="グループCDColumn" msprop:Generator_UserColumnName="グループCD">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="個社CD" msprop:Generator_ColumnVarNameInTable="column個社CD" msprop:Generator_ColumnPropNameInRow="個社CD" msprop:Generator_ColumnPropNameInTable="個社CDColumn" msprop:Generator_UserColumnName="個社CD">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="子会社CD" msprop:Generator_ColumnVarNameInTable="column子会社CD" msprop:Generator_ColumnPropNameInRow="子会社CD" msprop:Generator_ColumnPropNameInTable="子会社CDColumn" msprop:Generator_UserColumnName="子会社CD">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="日付" msprop:Generator_ColumnVarNameInTable="column日付" msprop:Generator_ColumnPropNameInRow="日付" msprop:Generator_ColumnPropNameInTable="日付Column" msprop:Generator_UserColumnName="日付">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="19" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="シフト区分" msprop:Generator_ColumnVarNameInTable="columnシフト区分" msprop:Generator_ColumnPropNameInRow="シフト区分" msprop:Generator_ColumnPropNameInTable="シフト区分Column" msprop:Generator_UserColumnName="シフト区分">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="区分名" msprop:Generator_ColumnVarNameInTable="column区分名" msprop:Generator_ColumnPropNameInRow="区分名" msprop:Generator_ColumnPropNameInTable="区分名Column" msprop:Generator_UserColumnName="区分名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="乗務員CD" msprop:Generator_ColumnVarNameInTable="column乗務員CD" msprop:Generator_ColumnPropNameInRow="乗務員CD" msprop:Generator_ColumnPropNameInTable="乗務員CDColumn" msprop:Generator_UserColumnName="乗務員CD">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="5" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="シフト" msprop:Generator_ColumnVarNameInTable="columnシフト" msprop:Generator_ColumnPropNameInRow="シフト" msprop:Generator_ColumnPropNameInTable="シフトColumn" msprop:Generator_UserColumnName="シフト" type="xs:decimal" minOccurs="0" />
              <xs:element name="基準日数" msprop:Generator_ColumnVarNameInTable="column基準日数" msprop:Generator_ColumnPropNameInRow="基準日数" msprop:Generator_ColumnPropNameInTable="基準日数Column" msprop:Generator_UserColumnName="基準日数" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="F0シフト一覧全体" msprop:Generator_TableClassName="F0シフト一覧全体DataTable" msprop:Generator_TableVarName="tableF0シフト一覧全体" msprop:Generator_TablePropName="F0シフト一覧全体" msprop:Generator_RowDeletingName="F0シフト一覧全体RowDeleting" msprop:Generator_RowChangingName="F0シフト一覧全体RowChanging" msprop:Generator_RowEvHandlerName="F0シフト一覧全体RowChangeEventHandler" msprop:Generator_RowDeletedName="F0シフト一覧全体RowDeleted" msprop:Generator_UserTableName="F0シフト一覧全体" msprop:Generator_RowChangedName="F0シフト一覧全体RowChanged" msprop:Generator_RowEvArgName="F0シフト一覧全体RowChangeEvent" msprop:Generator_RowClassName="F0シフト一覧全体Row">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="グループCD" msprop:Generator_ColumnVarNameInTable="columnグループCD" msprop:Generator_ColumnPropNameInRow="グループCD" msprop:Generator_ColumnPropNameInTable="グループCDColumn" msprop:Generator_UserColumnName="グループCD" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="個社CD" msprop:Generator_ColumnVarNameInTable="column個社CD" msprop:Generator_ColumnPropNameInRow="個社CD" msprop:Generator_ColumnPropNameInTable="個社CDColumn" msprop:Generator_UserColumnName="個社CD" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="子会社CD" msprop:Generator_ColumnVarNameInTable="column子会社CD" msprop:Generator_ColumnPropNameInRow="子会社CD" msprop:Generator_ColumnPropNameInTable="子会社CDColumn" msprop:Generator_UserColumnName="子会社CD" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="シフト区分" msprop:Generator_ColumnVarNameInTable="columnシフト区分" msprop:Generator_ColumnPropNameInRow="シフト区分" msprop:Generator_ColumnPropNameInTable="シフト区分Column" msprop:Generator_UserColumnName="シフト区分">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="区分名" msprop:Generator_ColumnVarNameInTable="column区分名" msprop:Generator_ColumnPropNameInRow="区分名" msprop:Generator_ColumnPropNameInTable="区分名Column" msprop:Generator_UserColumnName="区分名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="日付" msprop:Generator_ColumnVarNameInTable="column日付" msprop:Generator_ColumnPropNameInRow="日付" msprop:Generator_ColumnPropNameInTable="日付Column" msprop:Generator_UserColumnName="日付" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="8000" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="シフト" msprop:Generator_ColumnVarNameInTable="columnシフト" msprop:Generator_ColumnPropNameInRow="シフト" msprop:Generator_ColumnPropNameInTable="シフトColumn" msprop:Generator_UserColumnName="シフト" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="4000" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="F0乗務員シフト区分" msprop:Generator_TableClassName="F0乗務員シフト区分DataTable" msprop:Generator_TableVarName="tableF0乗務員シフト区分" msprop:Generator_RowChangedName="F0乗務員シフト区分RowChanged" msprop:Generator_TablePropName="F0乗務員シフト区分" msprop:Generator_RowDeletingName="F0乗務員シフト区分RowDeleting" msprop:Generator_RowChangingName="F0乗務員シフト区分RowChanging" msprop:Generator_RowEvHandlerName="F0乗務員シフト区分RowChangeEventHandler" msprop:Generator_RowDeletedName="F0乗務員シフト区分RowDeleted" msprop:Generator_RowClassName="F0乗務員シフト区分Row" msprop:Generator_UserTableName="F0乗務員シフト区分" msprop:Generator_RowEvArgName="F0乗務員シフト区分RowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="グループCD" msprop:Generator_ColumnVarNameInTable="columnグループCD" msprop:Generator_ColumnPropNameInRow="グループCD" msprop:Generator_ColumnPropNameInTable="グループCDColumn" msprop:Generator_UserColumnName="グループCD">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="個社CD" msprop:Generator_ColumnVarNameInTable="column個社CD" msprop:Generator_ColumnPropNameInRow="個社CD" msprop:Generator_ColumnPropNameInTable="個社CDColumn" msprop:Generator_UserColumnName="個社CD">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="子会社CD" msprop:Generator_ColumnVarNameInTable="column子会社CD" msprop:Generator_ColumnPropNameInRow="子会社CD" msprop:Generator_ColumnPropNameInTable="子会社CDColumn" msprop:Generator_UserColumnName="子会社CD">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="乗務員CD" msprop:Generator_ColumnVarNameInTable="column乗務員CD" msprop:Generator_ColumnPropNameInRow="乗務員CD" msprop:Generator_ColumnPropNameInTable="乗務員CDColumn" msprop:Generator_UserColumnName="乗務員CD">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="乗務員名" msprop:Generator_ColumnVarNameInTable="column乗務員名" msprop:Generator_ColumnPropNameInRow="乗務員名" msprop:Generator_ColumnPropNameInTable="乗務員名Column" msprop:Generator_UserColumnName="乗務員名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="勤務区分" msprop:Generator_ColumnVarNameInTable="column勤務区分" msprop:Generator_ColumnPropNameInRow="勤務区分" msprop:Generator_ColumnPropNameInTable="勤務区分Column" msprop:Generator_UserColumnName="勤務区分" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="シフト区分" msprop:Generator_ColumnVarNameInTable="columnシフト区分" msprop:Generator_ColumnPropNameInRow="シフト区分" msprop:Generator_ColumnPropNameInTable="シフト区分Column" msprop:Generator_UserColumnName="シフト区分">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="社員NO" msprop:Generator_ColumnVarNameInTable="column社員NO" msprop:Generator_ColumnPropNameInRow="社員NO" msprop:Generator_ColumnPropNameInTable="社員NOColumn" msprop:Generator_UserColumnName="社員NO" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="F0月別輸送実績" msprop:Generator_TableClassName="F0月別輸送実績DataTable" msprop:Generator_TableVarName="tableF0月別輸送実績" msprop:Generator_RowChangedName="F0月別輸送実績RowChanged" msprop:Generator_TablePropName="F0月別輸送実績" msprop:Generator_RowDeletingName="F0月別輸送実績RowDeleting" msprop:Generator_RowChangingName="F0月別輸送実績RowChanging" msprop:Generator_RowEvHandlerName="F0月別輸送実績RowChangeEventHandler" msprop:Generator_RowDeletedName="F0月別輸送実績RowDeleted" msprop:Generator_RowClassName="F0月別輸送実績Row" msprop:Generator_UserTableName="F0月別輸送実績" msprop:Generator_RowEvArgName="F0月別輸送実績RowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="グループCD" msprop:Generator_ColumnVarNameInTable="columnグループCD" msprop:Generator_ColumnPropNameInRow="グループCD" msprop:Generator_ColumnPropNameInTable="グループCDColumn" msprop:Generator_UserColumnName="グループCD">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="個社CD" msprop:Generator_ColumnVarNameInTable="column個社CD" msprop:Generator_ColumnPropNameInRow="個社CD" msprop:Generator_ColumnPropNameInTable="個社CDColumn" msprop:Generator_UserColumnName="個社CD">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="子会社CD" msprop:Generator_ColumnVarNameInTable="column子会社CD" msprop:Generator_ColumnPropNameInRow="子会社CD" msprop:Generator_ColumnPropNameInTable="子会社CDColumn" msprop:Generator_UserColumnName="子会社CD">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="車種CD" msprop:Generator_ColumnVarNameInTable="column車種CD" msprop:Generator_ColumnPropNameInRow="車種CD" msprop:Generator_ColumnPropNameInTable="車種CDColumn" msprop:Generator_UserColumnName="車種CD" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="延実在車両数" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column延実在車両数" msprop:Generator_ColumnPropNameInRow="延実在車両数" msprop:Generator_ColumnPropNameInTable="延実在車両数Column" msprop:Generator_UserColumnName="延実在車両数" type="xs:int" minOccurs="0" />
              <xs:element name="月末車両数" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column月末車両数" msprop:Generator_ColumnPropNameInRow="月末車両数" msprop:Generator_ColumnPropNameInTable="月末車両数Column" msprop:Generator_UserColumnName="月末車両数" type="xs:int" minOccurs="0" />
              <xs:element name="運送収入" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column運送収入" msprop:Generator_ColumnPropNameInRow="運送収入" msprop:Generator_ColumnPropNameInTable="運送収入Column" msprop:Generator_UserColumnName="運送収入" type="xs:decimal" minOccurs="0" />
              <xs:element name="車両番号のカウント" msprop:Generator_ColumnVarNameInTable="column車両番号のカウント" msprop:Generator_ColumnPropNameInRow="車両番号のカウント" msprop:Generator_ColumnPropNameInTable="車両番号のカウントColumn" msprop:Generator_UserColumnName="車両番号のカウント" type="xs:int" minOccurs="0" />
              <xs:element name="走行kmの合計" msprop:Generator_ColumnVarNameInTable="column走行kmの合計" msprop:Generator_ColumnPropNameInRow="走行kmの合計" msprop:Generator_ColumnPropNameInTable="走行kmの合計Column" msprop:Generator_UserColumnName="走行kmの合計" type="xs:decimal" minOccurs="0" />
              <xs:element name="実車kmの合計" msprop:Generator_ColumnVarNameInTable="column実車kmの合計" msprop:Generator_ColumnPropNameInRow="実車kmの合計" msprop:Generator_ColumnPropNameInTable="実車kmの合計Column" msprop:Generator_UserColumnName="実車kmの合計" type="xs:decimal" minOccurs="0" />
              <xs:element name="輸送回数の合計" msprop:Generator_ColumnVarNameInTable="column輸送回数の合計" msprop:Generator_ColumnPropNameInRow="輸送回数の合計" msprop:Generator_ColumnPropNameInTable="輸送回数の合計Column" msprop:Generator_UserColumnName="輸送回数の合計" type="xs:decimal" minOccurs="0" />
              <xs:element name="輸送人員の合計" msprop:Generator_ColumnVarNameInTable="column輸送人員の合計" msprop:Generator_ColumnPropNameInRow="輸送人員の合計" msprop:Generator_ColumnPropNameInTable="輸送人員の合計Column" msprop:Generator_UserColumnName="輸送人員の合計" type="xs:decimal" minOccurs="0" />
              <xs:element name="迎車回数の合計" msprop:Generator_ColumnVarNameInTable="column迎車回数の合計" msprop:Generator_ColumnPropNameInRow="迎車回数の合計" msprop:Generator_ColumnPropNameInTable="迎車回数の合計Column" msprop:Generator_UserColumnName="迎車回数の合計" type="xs:decimal" minOccurs="0" />
              <xs:element name="税込運送収入の合計" msprop:Generator_ColumnVarNameInTable="column税込運送収入の合計" msprop:Generator_ColumnPropNameInRow="税込運送収入の合計" msprop:Generator_ColumnPropNameInTable="税込運送収入の合計Column" msprop:Generator_UserColumnName="税込運送収入の合計" type="xs:decimal" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="F0出退勤情報出力" msprop:Generator_TableClassName="F0出退勤情報出力DataTable" msprop:Generator_TableVarName="tableF0出退勤情報出力" msprop:Generator_TablePropName="F0出退勤情報出力" msprop:Generator_RowDeletingName="F0出退勤情報出力RowDeleting" msprop:Generator_RowChangingName="F0出退勤情報出力RowChanging" msprop:Generator_RowEvHandlerName="F0出退勤情報出力RowChangeEventHandler" msprop:Generator_RowDeletedName="F0出退勤情報出力RowDeleted" msprop:Generator_UserTableName="F0出退勤情報出力" msprop:Generator_RowChangedName="F0出退勤情報出力RowChanged" msprop:Generator_RowEvArgName="F0出退勤情報出力RowChangeEvent" msprop:Generator_RowClassName="F0出退勤情報出力Row">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="グループCD" msprop:Generator_ColumnVarNameInTable="columnグループCD" msprop:Generator_ColumnPropNameInRow="グループCD" msprop:Generator_ColumnPropNameInTable="グループCDColumn" msprop:Generator_UserColumnName="グループCD">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="個社CD" msprop:Generator_ColumnVarNameInTable="column個社CD" msprop:Generator_ColumnPropNameInRow="個社CD" msprop:Generator_ColumnPropNameInTable="個社CDColumn" msprop:Generator_UserColumnName="個社CD">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="子会社CD" msprop:Generator_ColumnVarNameInTable="column子会社CD" msprop:Generator_ColumnPropNameInRow="子会社CD" msprop:Generator_ColumnPropNameInTable="子会社CDColumn" msprop:Generator_UserColumnName="子会社CD">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="社員NO" msprop:Generator_ColumnVarNameInTable="column社員NO" msprop:Generator_ColumnPropNameInRow="社員NO" msprop:Generator_ColumnPropNameInTable="社員NOColumn" msprop:Generator_UserColumnName="社員NO" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="有給" msprop:Generator_ColumnVarNameInTable="column有給" msprop:Generator_ColumnPropNameInRow="有給" msprop:Generator_ColumnPropNameInTable="有給Column" msprop:Generator_UserColumnName="有給" type="xs:int" minOccurs="0" />
              <xs:element name="欠勤" msprop:Generator_ColumnVarNameInTable="column欠勤" msprop:Generator_ColumnPropNameInRow="欠勤" msprop:Generator_ColumnPropNameInTable="欠勤Column" msprop:Generator_UserColumnName="欠勤" type="xs:int" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="F0売上照会" msprop:Generator_TableClassName="F0売上照会DataTable" msprop:Generator_TableVarName="tableF0売上照会" msprop:Generator_RowChangedName="F0売上照会RowChanged" msprop:Generator_TablePropName="F0売上照会" msprop:Generator_RowDeletingName="F0売上照会RowDeleting" msprop:Generator_RowChangingName="F0売上照会RowChanging" msprop:Generator_RowEvHandlerName="F0売上照会RowChangeEventHandler" msprop:Generator_RowDeletedName="F0売上照会RowDeleted" msprop:Generator_RowClassName="F0売上照会Row" msprop:Generator_UserTableName="F0売上照会" msprop:Generator_RowEvArgName="F0売上照会RowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="グループCD" msprop:Generator_ColumnVarNameInTable="columnグループCD" msprop:Generator_ColumnPropNameInRow="グループCD" msprop:Generator_ColumnPropNameInTable="グループCDColumn" msprop:Generator_UserColumnName="グループCD">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="個社CD" msprop:Generator_ColumnVarNameInTable="column個社CD" msprop:Generator_ColumnPropNameInRow="個社CD" msprop:Generator_ColumnPropNameInTable="個社CDColumn" msprop:Generator_UserColumnName="個社CD" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="子会社CD" msprop:Generator_ColumnVarNameInTable="column子会社CD" msprop:Generator_ColumnPropNameInRow="子会社CD" msprop:Generator_ColumnPropNameInTable="子会社CDColumn" msprop:Generator_UserColumnName="子会社CD" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="日報日付" msprop:Generator_ColumnVarNameInTable="column日報日付" msprop:Generator_ColumnPropNameInRow="日報日付" msprop:Generator_ColumnPropNameInTable="日報日付Column" msprop:Generator_UserColumnName="日報日付" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="車両番号" msprop:Generator_ColumnVarNameInTable="column車両番号" msprop:Generator_ColumnPropNameInRow="車両番号" msprop:Generator_ColumnPropNameInTable="車両番号Column" msprop:Generator_UserColumnName="車両番号" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="4" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="社員NO" msprop:Generator_ColumnVarNameInTable="column社員NO" msprop:Generator_ColumnPropNameInRow="社員NO" msprop:Generator_ColumnPropNameInTable="社員NOColumn" msprop:Generator_UserColumnName="社員NO" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="勤務区分" msprop:Generator_ColumnVarNameInTable="column勤務区分" msprop:Generator_ColumnPropNameInRow="勤務区分" msprop:Generator_ColumnPropNameInTable="勤務区分Column" msprop:Generator_UserColumnName="勤務区分" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="納金" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column納金" msprop:Generator_ColumnPropNameInRow="納金" msprop:Generator_ColumnPropNameInTable="納金Column" msprop:Generator_UserColumnName="納金" type="xs:decimal" minOccurs="0" />
              <xs:element name="営収" msprop:Generator_ColumnVarNameInTable="column営収" msprop:Generator_ColumnPropNameInRow="営収" msprop:Generator_ColumnPropNameInTable="営収Column" msprop:Generator_UserColumnName="営収" type="xs:decimal" minOccurs="0" />
              <xs:element name="現金A" msprop:Generator_ColumnVarNameInTable="column現金A" msprop:Generator_ColumnPropNameInRow="現金A" msprop:Generator_ColumnPropNameInTable="現金AColumn" msprop:Generator_UserColumnName="現金A" type="xs:decimal" minOccurs="0" />
              <xs:element name="その他" msprop:Generator_ColumnVarNameInTable="columnその他" msprop:Generator_ColumnPropNameInRow="その他" msprop:Generator_ColumnPropNameInTable="その他Column" msprop:Generator_UserColumnName="その他" type="xs:decimal" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="F0乗務員予定実績" msprop:Generator_TableClassName="F0乗務員予定実績DataTable" msprop:Generator_TableVarName="tableF0乗務員予定実績" msprop:Generator_RowChangedName="F0乗務員予定実績RowChanged" msprop:Generator_TablePropName="F0乗務員予定実績" msprop:Generator_RowDeletingName="F0乗務員予定実績RowDeleting" msprop:Generator_RowChangingName="F0乗務員予定実績RowChanging" msprop:Generator_RowEvHandlerName="F0乗務員予定実績RowChangeEventHandler" msprop:Generator_RowDeletedName="F0乗務員予定実績RowDeleted" msprop:Generator_RowClassName="F0乗務員予定実績Row" msprop:Generator_UserTableName="F0乗務員予定実績" msprop:Generator_RowEvArgName="F0乗務員予定実績RowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="グループCD" msprop:Generator_ColumnVarNameInTable="columnグループCD" msprop:Generator_ColumnPropNameInRow="グループCD" msprop:Generator_ColumnPropNameInTable="グループCDColumn" msprop:Generator_UserColumnName="グループCD">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="個社CD" msprop:Generator_ColumnVarNameInTable="column個社CD" msprop:Generator_ColumnPropNameInRow="個社CD" msprop:Generator_ColumnPropNameInTable="個社CDColumn" msprop:Generator_UserColumnName="個社CD">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="子会社CD" msprop:Generator_ColumnVarNameInTable="column子会社CD" msprop:Generator_ColumnPropNameInRow="子会社CD" msprop:Generator_ColumnPropNameInTable="子会社CDColumn" msprop:Generator_UserColumnName="子会社CD">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="乗務員CD" msprop:Generator_ColumnVarNameInTable="column乗務員CD" msprop:Generator_ColumnPropNameInRow="乗務員CD" msprop:Generator_ColumnPropNameInTable="乗務員CDColumn" msprop:Generator_UserColumnName="乗務員CD">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="乗務員名" msprop:Generator_ColumnVarNameInTable="column乗務員名" msprop:Generator_ColumnPropNameInRow="乗務員名" msprop:Generator_ColumnPropNameInTable="乗務員名Column" msprop:Generator_UserColumnName="乗務員名">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="12" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="区分ID" msprop:Generator_ColumnVarNameInTable="column区分ID" msprop:Generator_ColumnPropNameInRow="区分ID" msprop:Generator_ColumnPropNameInTable="区分IDColumn" msprop:Generator_UserColumnName="区分ID" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="区分名" msprop:Generator_ColumnVarNameInTable="column区分名" msprop:Generator_ColumnPropNameInRow="区分名" msprop:Generator_ColumnPropNameInTable="区分名Column" msprop:Generator_UserColumnName="区分名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="日付" msprop:Generator_ColumnVarNameInTable="column日付" msprop:Generator_ColumnPropNameInRow="日付" msprop:Generator_ColumnPropNameInTable="日付Column" msprop:Generator_UserColumnName="日付" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="8000" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="予定" msprop:Generator_ColumnVarNameInTable="column予定" msprop:Generator_ColumnPropNameInRow="予定" msprop:Generator_ColumnPropNameInTable="予定Column" msprop:Generator_UserColumnName="予定" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="4000" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="実績" msprop:Generator_ColumnVarNameInTable="column実績" msprop:Generator_ColumnPropNameInRow="実績" msprop:Generator_ColumnPropNameInTable="実績Column" msprop:Generator_UserColumnName="実績" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="4000" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="F0休暇日一覧" msprop:Generator_TableClassName="F0休暇日一覧DataTable" msprop:Generator_TableVarName="tableF0休暇日一覧" msprop:Generator_TablePropName="F0休暇日一覧" msprop:Generator_RowDeletingName="F0休暇日一覧RowDeleting" msprop:Generator_RowChangingName="F0休暇日一覧RowChanging" msprop:Generator_RowEvHandlerName="F0休暇日一覧RowChangeEventHandler" msprop:Generator_RowDeletedName="F0休暇日一覧RowDeleted" msprop:Generator_UserTableName="F0休暇日一覧" msprop:Generator_RowChangedName="F0休暇日一覧RowChanged" msprop:Generator_RowEvArgName="F0休暇日一覧RowChangeEvent" msprop:Generator_RowClassName="F0休暇日一覧Row">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="グループCD" msprop:Generator_ColumnVarNameInTable="columnグループCD" msprop:Generator_ColumnPropNameInRow="グループCD" msprop:Generator_ColumnPropNameInTable="グループCDColumn" msprop:Generator_UserColumnName="グループCD">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="個社CD" msprop:Generator_ColumnVarNameInTable="column個社CD" msprop:Generator_ColumnPropNameInRow="個社CD" msprop:Generator_ColumnPropNameInTable="個社CDColumn" msprop:Generator_UserColumnName="個社CD">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="子会社CD" msprop:Generator_ColumnVarNameInTable="column子会社CD" msprop:Generator_ColumnPropNameInRow="子会社CD" msprop:Generator_ColumnPropNameInTable="子会社CDColumn" msprop:Generator_UserColumnName="子会社CD">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="乗務員CD" msprop:Generator_ColumnVarNameInTable="column乗務員CD" msprop:Generator_ColumnPropNameInRow="乗務員CD" msprop:Generator_ColumnPropNameInTable="乗務員CDColumn" msprop:Generator_UserColumnName="乗務員CD">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="日付" msprop:Generator_ColumnVarNameInTable="column日付" msprop:Generator_ColumnPropNameInRow="日付" msprop:Generator_ColumnPropNameInTable="日付Column" msprop:Generator_UserColumnName="日付">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="休暇区分" msprop:Generator_ColumnVarNameInTable="column休暇区分" msprop:Generator_ColumnPropNameInRow="休暇区分" msprop:Generator_ColumnPropNameInTable="休暇区分Column" msprop:Generator_UserColumnName="休暇区分" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="事由" msprop:Generator_ColumnVarNameInTable="column事由" msprop:Generator_ColumnPropNameInRow="事由" msprop:Generator_ColumnPropNameInTable="事由Column" msprop:Generator_UserColumnName="事由" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="申請日" msprop:Generator_ColumnVarNameInTable="column申請日" msprop:Generator_ColumnPropNameInRow="申請日" msprop:Generator_ColumnPropNameInTable="申請日Column" msprop:Generator_UserColumnName="申請日" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="登録ユーザーCD" msprop:Generator_ColumnVarNameInTable="column登録ユーザーCD" msprop:Generator_ColumnPropNameInRow="登録ユーザーCD" msprop:Generator_ColumnPropNameInTable="登録ユーザーCDColumn" msprop:Generator_UserColumnName="登録ユーザーCD" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="5" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="登録ユーザー名" msprop:Generator_ColumnVarNameInTable="column登録ユーザー名" msprop:Generator_ColumnPropNameInRow="登録ユーザー名" msprop:Generator_ColumnPropNameInTable="登録ユーザー名Column" msprop:Generator_UserColumnName="登録ユーザー名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="登録日時" msprop:Generator_ColumnVarNameInTable="column登録日時" msprop:Generator_ColumnPropNameInRow="登録日時" msprop:Generator_ColumnPropNameInTable="登録日時Column" msprop:Generator_UserColumnName="登録日時" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="更新ユーザーCD" msprop:Generator_ColumnVarNameInTable="column更新ユーザーCD" msprop:Generator_ColumnPropNameInRow="更新ユーザーCD" msprop:Generator_ColumnPropNameInTable="更新ユーザーCDColumn" msprop:Generator_UserColumnName="更新ユーザーCD" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="5" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="更新ユーザー名" msprop:Generator_ColumnVarNameInTable="column更新ユーザー名" msprop:Generator_ColumnPropNameInRow="更新ユーザー名" msprop:Generator_ColumnPropNameInTable="更新ユーザー名Column" msprop:Generator_UserColumnName="更新ユーザー名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="更新日時" msprop:Generator_ColumnVarNameInTable="column更新日時" msprop:Generator_ColumnPropNameInRow="更新日時" msprop:Generator_ColumnPropNameInTable="更新日時Column" msprop:Generator_UserColumnName="更新日時" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="F0売上データ" msprop:Generator_TableClassName="F0売上データDataTable" msprop:Generator_TableVarName="tableF0売上データ" msprop:Generator_TablePropName="F0売上データ" msprop:Generator_RowDeletingName="F0売上データRowDeleting" msprop:Generator_RowChangingName="F0売上データRowChanging" msprop:Generator_RowEvHandlerName="F0売上データRowChangeEventHandler" msprop:Generator_RowDeletedName="F0売上データRowDeleted" msprop:Generator_UserTableName="F0売上データ" msprop:Generator_RowChangedName="F0売上データRowChanged" msprop:Generator_RowEvArgName="F0売上データRowChangeEvent" msprop:Generator_RowClassName="F0売上データRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="グループCD" msprop:Generator_ColumnVarNameInTable="columnグループCD" msprop:Generator_ColumnPropNameInRow="グループCD" msprop:Generator_ColumnPropNameInTable="グループCDColumn" msprop:Generator_UserColumnName="グループCD">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="個社CD" msprop:Generator_ColumnVarNameInTable="column個社CD" msprop:Generator_ColumnPropNameInRow="個社CD" msprop:Generator_ColumnPropNameInTable="個社CDColumn" msprop:Generator_UserColumnName="個社CD">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="子会社CD" msprop:Generator_ColumnVarNameInTable="column子会社CD" msprop:Generator_ColumnPropNameInRow="子会社CD" msprop:Generator_ColumnPropNameInTable="子会社CDColumn" msprop:Generator_UserColumnName="子会社CD">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="日報年月" msprop:Generator_ColumnVarNameInTable="column日報年月" msprop:Generator_ColumnPropNameInRow="日報年月" msprop:Generator_ColumnPropNameInTable="日報年月Column" msprop:Generator_UserColumnName="日報年月">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="年度" msprop:Generator_ColumnVarNameInTable="column年度" msprop:Generator_ColumnPropNameInRow="年度" msprop:Generator_ColumnPropNameInTable="年度Column" msprop:Generator_UserColumnName="年度" type="xs:int" minOccurs="0" />
              <xs:element name="月" msprop:Generator_ColumnVarNameInTable="column月" msprop:Generator_ColumnPropNameInRow="月" msprop:Generator_ColumnPropNameInTable="月Column" msprop:Generator_UserColumnName="月" type="xs:int" minOccurs="0" />
              <xs:element name="週番号" msprop:Generator_ColumnVarNameInTable="column週番号" msprop:Generator_ColumnPropNameInRow="週番号" msprop:Generator_ColumnPropNameInTable="週番号Column" msprop:Generator_UserColumnName="週番号" type="xs:int" minOccurs="0" />
              <xs:element name="勤務区分" msprop:Generator_ColumnVarNameInTable="column勤務区分" msprop:Generator_ColumnPropNameInRow="勤務区分" msprop:Generator_ColumnPropNameInTable="勤務区分Column" msprop:Generator_UserColumnName="勤務区分" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="乗務員CD" msprop:Generator_ColumnVarNameInTable="column乗務員CD" msprop:Generator_ColumnPropNameInRow="乗務員CD" msprop:Generator_ColumnPropNameInTable="乗務員CDColumn" msprop:Generator_UserColumnName="乗務員CD">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="乗務員名" msprop:Generator_ColumnVarNameInTable="column乗務員名" msprop:Generator_ColumnPropNameInRow="乗務員名" msprop:Generator_ColumnPropNameInTable="乗務員名Column" msprop:Generator_UserColumnName="乗務員名">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="13" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="稼働数" msprop:Generator_ColumnVarNameInTable="column稼働数" msprop:Generator_ColumnPropNameInRow="稼働数" msprop:Generator_ColumnPropNameInTable="稼働数Column" msprop:Generator_UserColumnName="稼働数" type="xs:int" minOccurs="0" />
              <xs:element name="売上合計" msprop:Generator_ColumnVarNameInTable="column売上合計" msprop:Generator_ColumnPropNameInRow="売上合計" msprop:Generator_ColumnPropNameInTable="売上合計Column" msprop:Generator_UserColumnName="売上合計" type="xs:decimal" minOccurs="0" />
              <xs:element name="予測基準額" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column予測基準額" msprop:Generator_ColumnPropNameInRow="予測基準額" msprop:Generator_ColumnPropNameInTable="予測基準額Column" msprop:Generator_UserColumnName="予測基準額" type="xs:decimal" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="F0健康診断受診履歴照会" msprop:Generator_TableClassName="F0健康診断受診履歴照会DataTable" msprop:Generator_TableVarName="tableF0健康診断受診履歴照会" msprop:Generator_TablePropName="F0健康診断受診履歴照会" msprop:Generator_RowDeletingName="F0健康診断受診履歴照会RowDeleting" msprop:Generator_RowChangingName="F0健康診断受診履歴照会RowChanging" msprop:Generator_RowEvHandlerName="F0健康診断受診履歴照会RowChangeEventHandler" msprop:Generator_RowDeletedName="F0健康診断受診履歴照会RowDeleted" msprop:Generator_UserTableName="F0健康診断受診履歴照会" msprop:Generator_RowChangedName="F0健康診断受診履歴照会RowChanged" msprop:Generator_RowEvArgName="F0健康診断受診履歴照会RowChangeEvent" msprop:Generator_RowClassName="F0健康診断受診履歴照会Row">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="グループCD" msprop:Generator_ColumnVarNameInTable="columnグループCD" msprop:Generator_ColumnPropNameInRow="グループCD" msprop:Generator_ColumnPropNameInTable="グループCDColumn" msprop:Generator_UserColumnName="グループCD">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="個社CD" msprop:Generator_ColumnVarNameInTable="column個社CD" msprop:Generator_ColumnPropNameInRow="個社CD" msprop:Generator_ColumnPropNameInTable="個社CDColumn" msprop:Generator_UserColumnName="個社CD">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="子会社CD" msprop:Generator_ColumnVarNameInTable="column子会社CD" msprop:Generator_ColumnPropNameInRow="子会社CD" msprop:Generator_ColumnPropNameInTable="子会社CDColumn" msprop:Generator_UserColumnName="子会社CD">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="社員名" msprop:Generator_ColumnVarNameInTable="column社員名" msprop:Generator_ColumnPropNameInRow="社員名" msprop:Generator_ColumnPropNameInTable="社員名Column" msprop:Generator_UserColumnName="社員名">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="12" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="社員NO" msprop:Generator_ColumnVarNameInTable="column社員NO" msprop:Generator_ColumnPropNameInRow="社員NO" msprop:Generator_ColumnPropNameInTable="社員NOColumn" msprop:Generator_UserColumnName="社員NO" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="勤務区分" msprop:Generator_ColumnVarNameInTable="column勤務区分" msprop:Generator_ColumnPropNameInRow="勤務区分" msprop:Generator_ColumnPropNameInTable="勤務区分Column" msprop:Generator_UserColumnName="勤務区分" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="日付" msprop:Generator_ColumnVarNameInTable="column日付" msprop:Generator_ColumnPropNameInRow="日付" msprop:Generator_ColumnPropNameInTable="日付Column" msprop:Generator_UserColumnName="日付" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="8000" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="メモ" msprop:Generator_ColumnVarNameInTable="columnメモ" msprop:Generator_ColumnPropNameInRow="メモ" msprop:Generator_ColumnPropNameInTable="メモColumn" msprop:Generator_UserColumnName="メモ" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="8000" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="F0消化日データ照会" msprop:Generator_TableClassName="F0消化日データ照会DataTable" msprop:Generator_TableVarName="tableF0消化日データ照会" msprop:Generator_TablePropName="F0消化日データ照会" msprop:Generator_RowDeletingName="F0消化日データ照会RowDeleting" msprop:Generator_RowChangingName="F0消化日データ照会RowChanging" msprop:Generator_RowEvHandlerName="F0消化日データ照会RowChangeEventHandler" msprop:Generator_RowDeletedName="F0消化日データ照会RowDeleted" msprop:Generator_UserTableName="F0消化日データ照会" msprop:Generator_RowChangedName="F0消化日データ照会RowChanged" msprop:Generator_RowEvArgName="F0消化日データ照会RowChangeEvent" msprop:Generator_RowClassName="F0消化日データ照会Row">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="グループCD" msprop:Generator_ColumnVarNameInTable="columnグループCD" msprop:Generator_ColumnPropNameInRow="グループCD" msprop:Generator_ColumnPropNameInTable="グループCDColumn" msprop:Generator_UserColumnName="グループCD">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="個社CD" msprop:Generator_ColumnVarNameInTable="column個社CD" msprop:Generator_ColumnPropNameInRow="個社CD" msprop:Generator_ColumnPropNameInTable="個社CDColumn" msprop:Generator_UserColumnName="個社CD">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="子会社CD" msprop:Generator_ColumnVarNameInTable="column子会社CD" msprop:Generator_ColumnPropNameInRow="子会社CD" msprop:Generator_ColumnPropNameInTable="子会社CDColumn" msprop:Generator_UserColumnName="子会社CD">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="乗務員CD" msprop:Generator_ColumnVarNameInTable="column乗務員CD" msprop:Generator_ColumnPropNameInRow="乗務員CD" msprop:Generator_ColumnPropNameInTable="乗務員CDColumn" msprop:Generator_UserColumnName="乗務員CD">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="5" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="乗務員名" msprop:Generator_ColumnVarNameInTable="column乗務員名" msprop:Generator_ColumnPropNameInRow="乗務員名" msprop:Generator_ColumnPropNameInTable="乗務員名Column" msprop:Generator_UserColumnName="乗務員名">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="12" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="社員NO" msprop:Generator_ColumnVarNameInTable="column社員NO" msprop:Generator_ColumnPropNameInRow="社員NO" msprop:Generator_ColumnPropNameInTable="社員NOColumn" msprop:Generator_UserColumnName="社員NO" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="休暇区分" msprop:Generator_ColumnVarNameInTable="column休暇区分" msprop:Generator_ColumnPropNameInRow="休暇区分" msprop:Generator_ColumnPropNameInTable="休暇区分Column" msprop:Generator_UserColumnName="休暇区分" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="区分名" msprop:Generator_ColumnVarNameInTable="column区分名" msprop:Generator_ColumnPropNameInRow="区分名" msprop:Generator_ColumnPropNameInTable="区分名Column" msprop:Generator_UserColumnName="区分名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="日付" msprop:Generator_ColumnVarNameInTable="column日付" msprop:Generator_ColumnPropNameInRow="日付" msprop:Generator_ColumnPropNameInTable="日付Column" msprop:Generator_UserColumnName="日付" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="8000" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="事由" msprop:Generator_ColumnVarNameInTable="column事由" msprop:Generator_ColumnPropNameInRow="事由" msprop:Generator_ColumnPropNameInTable="事由Column" msprop:Generator_UserColumnName="事由" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="8000" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="F0乗務記録実績" msprop:Generator_TableClassName="F0乗務記録実績DataTable" msprop:Generator_TableVarName="tableF0乗務記録実績" msprop:Generator_RowChangedName="F0乗務記録実績RowChanged" msprop:Generator_TablePropName="F0乗務記録実績" msprop:Generator_RowDeletingName="F0乗務記録実績RowDeleting" msprop:Generator_RowChangingName="F0乗務記録実績RowChanging" msprop:Generator_RowEvHandlerName="F0乗務記録実績RowChangeEventHandler" msprop:Generator_RowDeletedName="F0乗務記録実績RowDeleted" msprop:Generator_RowClassName="F0乗務記録実績Row" msprop:Generator_UserTableName="F0乗務記録実績" msprop:Generator_RowEvArgName="F0乗務記録実績RowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="グループCD" msprop:Generator_ColumnVarNameInTable="columnグループCD" msprop:Generator_ColumnPropNameInRow="グループCD" msprop:Generator_ColumnPropNameInTable="グループCDColumn" msprop:Generator_UserColumnName="グループCD">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="個社CD" msprop:Generator_ColumnVarNameInTable="column個社CD" msprop:Generator_ColumnPropNameInRow="個社CD" msprop:Generator_ColumnPropNameInTable="個社CDColumn" msprop:Generator_UserColumnName="個社CD">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="子会社CD" msprop:Generator_ColumnVarNameInTable="column子会社CD" msprop:Generator_ColumnPropNameInRow="子会社CD" msprop:Generator_ColumnPropNameInTable="子会社CDColumn" msprop:Generator_UserColumnName="子会社CD">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="取込日付" msprop:Generator_ColumnVarNameInTable="column取込日付" msprop:Generator_ColumnPropNameInRow="取込日付" msprop:Generator_ColumnPropNameInTable="取込日付Column" msprop:Generator_UserColumnName="取込日付" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="乗務記録NO" msprop:Generator_ColumnVarNameInTable="column乗務記録NO" msprop:Generator_ColumnPropNameInRow="乗務記録NO" msprop:Generator_ColumnPropNameInTable="乗務記録NOColumn" msprop:Generator_UserColumnName="乗務記録NO" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="255" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="日報日付" msprop:Generator_ColumnVarNameInTable="column日報日付" msprop:Generator_ColumnPropNameInRow="日報日付" msprop:Generator_ColumnPropNameInTable="日報日付Column" msprop:Generator_UserColumnName="日報日付">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="車両番号" msprop:Generator_ColumnVarNameInTable="column車両番号" msprop:Generator_ColumnPropNameInRow="車両番号" msprop:Generator_ColumnPropNameInTable="車両番号Column" msprop:Generator_UserColumnName="車両番号">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="5" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="車種CD" msprop:Generator_ColumnVarNameInTable="column車種CD" msprop:Generator_ColumnPropNameInRow="車種CD" msprop:Generator_ColumnPropNameInTable="車種CDColumn" msprop:Generator_UserColumnName="車種CD" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="乗務員CD" msprop:Generator_ColumnVarNameInTable="column乗務員CD" msprop:Generator_ColumnPropNameInRow="乗務員CD" msprop:Generator_ColumnPropNameInTable="乗務員CDColumn" msprop:Generator_UserColumnName="乗務員CD">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="乗務員名" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column乗務員名" msprop:Generator_ColumnPropNameInRow="乗務員名" msprop:Generator_ColumnPropNameInTable="乗務員名Column" msprop:Generator_UserColumnName="乗務員名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="12" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="勤務区分" msprop:Generator_ColumnVarNameInTable="column勤務区分" msprop:Generator_ColumnPropNameInRow="勤務区分" msprop:Generator_ColumnPropNameInTable="勤務区分Column" msprop:Generator_UserColumnName="勤務区分" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="輸送人員" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column輸送人員" msprop:Generator_ColumnPropNameInRow="輸送人員" msprop:Generator_ColumnPropNameInTable="輸送人員Column" msprop:Generator_UserColumnName="輸送人員" type="xs:decimal" minOccurs="0" />
              <xs:element name="走行km" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column走行km" msprop:Generator_ColumnPropNameInRow="走行km" msprop:Generator_ColumnPropNameInTable="走行kmColumn" msprop:Generator_UserColumnName="走行km" type="xs:decimal" minOccurs="0" />
              <xs:element name="営業km" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column営業km" msprop:Generator_ColumnPropNameInRow="営業km" msprop:Generator_ColumnPropNameInTable="営業kmColumn" msprop:Generator_UserColumnName="営業km" type="xs:decimal" minOccurs="0" />
              <xs:element name="実車回数" msprop:Generator_ColumnVarNameInTable="column実車回数" msprop:Generator_ColumnPropNameInRow="実車回数" msprop:Generator_ColumnPropNameInTable="実車回数Column" msprop:Generator_UserColumnName="実車回数" type="xs:decimal" minOccurs="0" />
              <xs:element name="爾後回数" msprop:Generator_ColumnVarNameInTable="column爾後回数" msprop:Generator_ColumnPropNameInRow="爾後回数" msprop:Generator_ColumnPropNameInTable="爾後回数Column" msprop:Generator_UserColumnName="爾後回数" type="xs:decimal" minOccurs="0" />
              <xs:element name="迎車回数" msprop:Generator_ColumnVarNameInTable="column迎車回数" msprop:Generator_ColumnPropNameInRow="迎車回数" msprop:Generator_ColumnPropNameInTable="迎車回数Column" msprop:Generator_UserColumnName="迎車回数" type="xs:decimal" minOccurs="0" />
              <xs:element name="予約回数" msprop:Generator_ColumnVarNameInTable="column予約回数" msprop:Generator_ColumnPropNameInRow="予約回数" msprop:Generator_ColumnPropNameInTable="予約回数Column" msprop:Generator_UserColumnName="予約回数" type="xs:decimal" minOccurs="0" />
              <xs:element name="実車金額" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column実車金額" msprop:Generator_ColumnPropNameInRow="実車金額" msprop:Generator_ColumnPropNameInTable="実車金額Column" msprop:Generator_UserColumnName="実車金額" type="xs:decimal" minOccurs="0" />
              <xs:element name="爾後金額" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column爾後金額" msprop:Generator_ColumnPropNameInRow="爾後金額" msprop:Generator_ColumnPropNameInTable="爾後金額Column" msprop:Generator_UserColumnName="爾後金額" type="xs:decimal" minOccurs="0" />
              <xs:element name="迎車金額" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column迎車金額" msprop:Generator_ColumnPropNameInRow="迎車金額" msprop:Generator_ColumnPropNameInTable="迎車金額Column" msprop:Generator_UserColumnName="迎車金額" type="xs:decimal" minOccurs="0" />
              <xs:element name="予約金額" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column予約金額" msprop:Generator_ColumnPropNameInRow="予約金額" msprop:Generator_ColumnPropNameInTable="予約金額Column" msprop:Generator_UserColumnName="予約金額" type="xs:decimal" minOccurs="0" />
              <xs:element name="金額計" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column金額計" msprop:Generator_ColumnPropNameInRow="金額計" msprop:Generator_ColumnPropNameInTable="金額計Column" msprop:Generator_UserColumnName="金額計" type="xs:decimal" minOccurs="0" />
              <xs:element name="遠割額" msprop:Generator_ColumnVarNameInTable="column遠割額" msprop:Generator_ColumnPropNameInRow="遠割額" msprop:Generator_ColumnPropNameInTable="遠割額Column" msprop:Generator_UserColumnName="遠割額" type="xs:decimal" minOccurs="0" />
              <xs:element name="定額差額A" msprop:Generator_ColumnVarNameInTable="column定額差額A" msprop:Generator_ColumnPropNameInRow="定額差額A" msprop:Generator_ColumnPropNameInTable="定額差額AColumn" msprop:Generator_UserColumnName="定額差額A" type="xs:decimal" minOccurs="0" />
              <xs:element name="空転A" msprop:Generator_ColumnVarNameInTable="column空転A" msprop:Generator_ColumnPropNameInRow="空転A" msprop:Generator_ColumnPropNameInTable="空転AColumn" msprop:Generator_UserColumnName="空転A" type="xs:decimal" minOccurs="0" />
              <xs:element name="納金" msprop:Generator_ColumnVarNameInTable="column納金" msprop:Generator_ColumnPropNameInRow="納金" msprop:Generator_ColumnPropNameInTable="納金Column" msprop:Generator_UserColumnName="納金" type="xs:decimal" minOccurs="0" />
              <xs:element name="TAX" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnTAX" msprop:Generator_ColumnPropNameInRow="TAX" msprop:Generator_ColumnPropNameInTable="TAXColumn" msprop:Generator_UserColumnName="TAX" type="xs:decimal" minOccurs="0" />
              <xs:element name="営収" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column営収" msprop:Generator_ColumnPropNameInRow="営収" msprop:Generator_ColumnPropNameInTable="営収Column" msprop:Generator_UserColumnName="営収" type="xs:decimal" minOccurs="0" />
              <xs:element name="チケットA" msprop:Generator_ColumnVarNameInTable="columnチケットA" msprop:Generator_ColumnPropNameInRow="チケットA" msprop:Generator_ColumnPropNameInTable="チケットAColumn" msprop:Generator_UserColumnName="チケットA" type="xs:decimal" minOccurs="0" />
              <xs:element name="クーポン四社A" msprop:Generator_ColumnVarNameInTable="columnクーポン四社A" msprop:Generator_ColumnPropNameInRow="クーポン四社A" msprop:Generator_ColumnPropNameInTable="クーポン四社AColumn" msprop:Generator_UserColumnName="クーポン四社A" type="xs:decimal" minOccurs="0" />
              <xs:element name="クーポン東旅協A" msprop:Generator_ColumnVarNameInTable="columnクーポン東旅協A" msprop:Generator_ColumnPropNameInRow="クーポン東旅協A" msprop:Generator_ColumnPropNameInTable="クーポン東旅協AColumn" msprop:Generator_UserColumnName="クーポン東旅協A" type="xs:decimal" minOccurs="0" />
              <xs:element name="福祉券A" msprop:Generator_ColumnVarNameInTable="column福祉券A" msprop:Generator_ColumnPropNameInRow="福祉券A" msprop:Generator_ColumnPropNameInTable="福祉券AColumn" msprop:Generator_UserColumnName="福祉券A" type="xs:decimal" minOccurs="0" />
              <xs:element name="カードA" msprop:Generator_ColumnVarNameInTable="columnカードA" msprop:Generator_ColumnPropNameInRow="カードA" msprop:Generator_ColumnPropNameInTable="カードAColumn" msprop:Generator_UserColumnName="カードA" type="xs:decimal" minOccurs="0" />
              <xs:element name="iDA" msprop:Generator_ColumnVarNameInTable="columniDA" msprop:Generator_ColumnPropNameInRow="iDA" msprop:Generator_ColumnPropNameInTable="iDAColumn" msprop:Generator_UserColumnName="iDA" type="xs:decimal" minOccurs="0" />
              <xs:element name="交通系ICA" msprop:Generator_ColumnVarNameInTable="column交通系ICA" msprop:Generator_ColumnPropNameInRow="交通系ICA" msprop:Generator_ColumnPropNameInTable="交通系ICAColumn" msprop:Generator_UserColumnName="交通系ICA" type="xs:decimal" minOccurs="0" />
              <xs:element name="キャブカードA" msprop:Generator_ColumnVarNameInTable="columnキャブカードA" msprop:Generator_ColumnPropNameInRow="キャブカードA" msprop:Generator_ColumnPropNameInTable="キャブカードAColumn" msprop:Generator_UserColumnName="キャブカードA" type="xs:decimal" minOccurs="0" />
              <xs:element name="プリペイドカードA" msprop:Generator_ColumnVarNameInTable="columnプリペイドカードA" msprop:Generator_ColumnPropNameInRow="プリペイドカードA" msprop:Generator_ColumnPropNameInTable="プリペイドカードAColumn" msprop:Generator_UserColumnName="プリペイドカードA" type="xs:decimal" minOccurs="0" />
              <xs:element name="WAONA" msprop:Generator_ColumnVarNameInTable="columnWAONA" msprop:Generator_ColumnPropNameInRow="WAONA" msprop:Generator_ColumnPropNameInTable="WAONAColumn" msprop:Generator_UserColumnName="WAONA" type="xs:decimal" minOccurs="0" />
              <xs:element name="その他A" msprop:Generator_ColumnVarNameInTable="columnその他A" msprop:Generator_ColumnPropNameInRow="その他A" msprop:Generator_ColumnPropNameInTable="その他AColumn" msprop:Generator_UserColumnName="その他A" type="xs:decimal" minOccurs="0" />
              <xs:element name="障割A" msprop:Generator_ColumnVarNameInTable="column障割A" msprop:Generator_ColumnPropNameInRow="障割A" msprop:Generator_ColumnPropNameInTable="障割AColumn" msprop:Generator_UserColumnName="障割A" type="xs:decimal" minOccurs="0" />
              <xs:element name="現金A" msprop:Generator_ColumnVarNameInTable="column現金A" msprop:Generator_ColumnPropNameInRow="現金A" msprop:Generator_ColumnPropNameInTable="現金AColumn" msprop:Generator_UserColumnName="現金A" type="xs:decimal" minOccurs="0" />
              <xs:element name="ETC実車A" msprop:Generator_ColumnVarNameInTable="columnETC実車A" msprop:Generator_ColumnPropNameInRow="ETC実車A" msprop:Generator_ColumnPropNameInTable="ETC実車AColumn" msprop:Generator_UserColumnName="ETC実車A" type="xs:decimal" minOccurs="0" />
              <xs:element name="ETC乗務員A" msprop:Generator_ColumnVarNameInTable="columnETC乗務員A" msprop:Generator_ColumnPropNameInRow="ETC乗務員A" msprop:Generator_ColumnPropNameInTable="ETC乗務員AColumn" msprop:Generator_UserColumnName="ETC乗務員A" type="xs:decimal" minOccurs="0" />
              <xs:element name="ETC会社A" msprop:Generator_ColumnVarNameInTable="columnETC会社A" msprop:Generator_ColumnPropNameInRow="ETC会社A" msprop:Generator_ColumnPropNameInTable="ETC会社AColumn" msprop:Generator_UserColumnName="ETC会社A" type="xs:decimal" minOccurs="0" />
              <xs:element name="出庫時刻" msprop:Generator_ColumnVarNameInTable="column出庫時刻" msprop:Generator_ColumnPropNameInRow="出庫時刻" msprop:Generator_ColumnPropNameInTable="出庫時刻Column" msprop:Generator_UserColumnName="出庫時刻">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="帰庫時刻" msprop:Generator_ColumnVarNameInTable="column帰庫時刻" msprop:Generator_ColumnPropNameInRow="帰庫時刻" msprop:Generator_ColumnPropNameInTable="帰庫時刻Column" msprop:Generator_UserColumnName="帰庫時刻">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="総勤務時間" msprop:Generator_ColumnVarNameInTable="column総勤務時間" msprop:Generator_ColumnPropNameInRow="総勤務時間" msprop:Generator_ColumnPropNameInTable="総勤務時間Column" msprop:Generator_UserColumnName="総勤務時間" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="総休憩時間" msprop:Generator_ColumnVarNameInTable="column総休憩時間" msprop:Generator_ColumnPropNameInRow="総休憩時間" msprop:Generator_ColumnPropNameInTable="総休憩時間Column" msprop:Generator_UserColumnName="総休憩時間" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="総回送時間" msprop:Generator_ColumnVarNameInTable="column総回送時間" msprop:Generator_ColumnPropNameInRow="総回送時間" msprop:Generator_ColumnPropNameInTable="総回送時間Column" msprop:Generator_UserColumnName="総回送時間" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="総空車停止時間" msprop:Generator_ColumnVarNameInTable="column総空車停止時間" msprop:Generator_ColumnPropNameInRow="総空車停止時間" msprop:Generator_ColumnPropNameInTable="総空車停止時間Column" msprop:Generator_UserColumnName="総空車停止時間" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="信販控除除外" msprop:Generator_ColumnVarNameInTable="column信販控除除外" msprop:Generator_ColumnPropNameInRow="信販控除除外" msprop:Generator_ColumnPropNameInTable="信販控除除外Column" msprop:Generator_UserColumnName="信販控除除外" type="xs:decimal" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="F0給与基準データ" msprop:Generator_TableClassName="F0給与基準データDataTable" msprop:Generator_TableVarName="tableF0給与基準データ" msprop:Generator_TablePropName="F0給与基準データ" msprop:Generator_RowDeletingName="F0給与基準データRowDeleting" msprop:Generator_RowChangingName="F0給与基準データRowChanging" msprop:Generator_RowEvHandlerName="F0給与基準データRowChangeEventHandler" msprop:Generator_RowDeletedName="F0給与基準データRowDeleted" msprop:Generator_UserTableName="F0給与基準データ" msprop:Generator_RowChangedName="F0給与基準データRowChanged" msprop:Generator_RowEvArgName="F0給与基準データRowChangeEvent" msprop:Generator_RowClassName="F0給与基準データRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="グループCD" msprop:Generator_ColumnVarNameInTable="columnグループCD" msprop:Generator_ColumnPropNameInRow="グループCD" msprop:Generator_ColumnPropNameInTable="グループCDColumn" msprop:Generator_UserColumnName="グループCD">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="個社CD" msprop:Generator_ColumnVarNameInTable="column個社CD" msprop:Generator_ColumnPropNameInRow="個社CD" msprop:Generator_ColumnPropNameInTable="個社CDColumn" msprop:Generator_UserColumnName="個社CD">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="子会社CD" msprop:Generator_ColumnVarNameInTable="column子会社CD" msprop:Generator_ColumnPropNameInRow="子会社CD" msprop:Generator_ColumnPropNameInTable="子会社CDColumn" msprop:Generator_UserColumnName="子会社CD">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="乗務員CD" msprop:Generator_ColumnVarNameInTable="column乗務員CD" msprop:Generator_ColumnPropNameInRow="乗務員CD" msprop:Generator_ColumnPropNameInTable="乗務員CDColumn" msprop:Generator_UserColumnName="乗務員CD">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="氏名" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column氏名" msprop:Generator_ColumnPropNameInRow="氏名" msprop:Generator_ColumnPropNameInTable="氏名Column" msprop:Generator_UserColumnName="氏名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="12" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="シフト区分" msprop:Generator_ColumnVarNameInTable="columnシフト区分" msprop:Generator_ColumnPropNameInRow="シフト区分" msprop:Generator_ColumnPropNameInTable="シフト区分Column" msprop:Generator_UserColumnName="シフト区分" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="区分名" msprop:Generator_ColumnVarNameInTable="column区分名" msprop:Generator_ColumnPropNameInRow="区分名" msprop:Generator_ColumnPropNameInTable="区分名Column" msprop:Generator_UserColumnName="区分名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="シフト変更" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnシフト変更" msprop:Generator_ColumnPropNameInRow="シフト変更" msprop:Generator_ColumnPropNameInTable="シフト変更Column" msprop:Generator_UserColumnName="シフト変更" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="4" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="基準乗務数" msprop:Generator_ColumnVarNameInTable="column基準乗務数" msprop:Generator_ColumnPropNameInRow="基準乗務数" msprop:Generator_ColumnPropNameInTable="基準乗務数Column" msprop:Generator_UserColumnName="基準乗務数" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="通常" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column通常" msprop:Generator_ColumnPropNameInRow="通常" msprop:Generator_ColumnPropNameInTable="通常Column" msprop:Generator_UserColumnName="通常" type="xs:int" minOccurs="0" />
              <xs:element name="公出" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column公出" msprop:Generator_ColumnPropNameInRow="公出" msprop:Generator_ColumnPropNameInTable="公出Column" msprop:Generator_UserColumnName="公出" type="xs:int" minOccurs="0" />
              <xs:element name="実乗務数_合計" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column実乗務数_合計" msprop:Generator_ColumnPropNameInRow="実乗務数_合計" msprop:Generator_ColumnPropNameInTable="実乗務数_合計Column" msprop:Generator_UserColumnName="実乗務数_合計" type="xs:int" minOccurs="0" />
              <xs:element name="差引数" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column差引数" msprop:Generator_ColumnPropNameInRow="差引数" msprop:Generator_ColumnPropNameInTable="差引数Column" msprop:Generator_UserColumnName="差引数" type="xs:int" minOccurs="0" />
              <xs:element name="有給" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column有給" msprop:Generator_ColumnPropNameInRow="有給" msprop:Generator_ColumnPropNameInTable="有給Column" msprop:Generator_UserColumnName="有給" type="xs:int" minOccurs="0" />
              <xs:element name="欠勤" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column欠勤" msprop:Generator_ColumnPropNameInRow="欠勤" msprop:Generator_ColumnPropNameInTable="欠勤Column" msprop:Generator_UserColumnName="欠勤" type="xs:int" minOccurs="0" />
              <xs:element name="合計" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column合計" msprop:Generator_ColumnPropNameInRow="合計" msprop:Generator_ColumnPropNameInTable="合計Column" msprop:Generator_UserColumnName="合計" type="xs:int" minOccurs="0" />
              <xs:element name="営収の合計" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column営収の合計" msprop:Generator_ColumnPropNameInRow="営収の合計" msprop:Generator_ColumnPropNameInTable="営収の合計Column" msprop:Generator_UserColumnName="営収の合計" type="xs:decimal" minOccurs="0" />
              <xs:element name="信販の合計" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column信販の合計" msprop:Generator_ColumnPropNameInRow="信販の合計" msprop:Generator_ColumnPropNameInTable="信販の合計Column" msprop:Generator_UserColumnName="信販の合計" type="xs:decimal" minOccurs="0" />
              <xs:element name="前月繰越" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column前月繰越" msprop:Generator_ColumnPropNameInRow="前月繰越" msprop:Generator_ColumnPropNameInTable="前月繰越Column" msprop:Generator_UserColumnName="前月繰越" type="xs:int" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="F0乗務記録HD" msprop:Generator_TableClassName="F0乗務記録HDDataTable" msprop:Generator_TableVarName="tableF0乗務記録HD" msprop:Generator_RowChangedName="F0乗務記録HDRowChanged" msprop:Generator_TablePropName="F0乗務記録HD" msprop:Generator_RowDeletingName="F0乗務記録HDRowDeleting" msprop:Generator_RowChangingName="F0乗務記録HDRowChanging" msprop:Generator_RowEvHandlerName="F0乗務記録HDRowChangeEventHandler" msprop:Generator_RowDeletedName="F0乗務記録HDRowDeleted" msprop:Generator_RowClassName="F0乗務記録HDRow" msprop:Generator_UserTableName="F0乗務記録HD" msprop:Generator_RowEvArgName="F0乗務記録HDRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="グループCD" msprop:Generator_ColumnVarNameInTable="columnグループCD" msprop:Generator_ColumnPropNameInRow="グループCD" msprop:Generator_ColumnPropNameInTable="グループCDColumn" msprop:Generator_UserColumnName="グループCD">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="個社CD" msprop:Generator_ColumnVarNameInTable="column個社CD" msprop:Generator_ColumnPropNameInRow="個社CD" msprop:Generator_ColumnPropNameInTable="個社CDColumn" msprop:Generator_UserColumnName="個社CD">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="子会社CD" msprop:Generator_ColumnVarNameInTable="column子会社CD" msprop:Generator_ColumnPropNameInRow="子会社CD" msprop:Generator_ColumnPropNameInTable="子会社CDColumn" msprop:Generator_UserColumnName="子会社CD">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="日報日付" msprop:Generator_ColumnVarNameInTable="column日報日付" msprop:Generator_ColumnPropNameInRow="日報日付" msprop:Generator_ColumnPropNameInTable="日報日付Column" msprop:Generator_UserColumnName="日報日付">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="日報年月" msprop:Generator_ColumnVarNameInTable="column日報年月" msprop:Generator_ColumnPropNameInRow="日報年月" msprop:Generator_ColumnPropNameInTable="日報年月Column" msprop:Generator_UserColumnName="日報年月" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="出庫時刻" msprop:Generator_ColumnVarNameInTable="column出庫時刻" msprop:Generator_ColumnPropNameInRow="出庫時刻" msprop:Generator_ColumnPropNameInTable="出庫時刻Column" msprop:Generator_UserColumnName="出庫時刻">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="入庫時刻" msprop:Generator_ColumnVarNameInTable="column入庫時刻" msprop:Generator_ColumnPropNameInRow="入庫時刻" msprop:Generator_ColumnPropNameInTable="入庫時刻Column" msprop:Generator_UserColumnName="入庫時刻">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="乗務員CD" msprop:Generator_ColumnVarNameInTable="column乗務員CD" msprop:Generator_ColumnPropNameInRow="乗務員CD" msprop:Generator_ColumnPropNameInTable="乗務員CDColumn" msprop:Generator_UserColumnName="乗務員CD">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="車両番号" msprop:Generator_ColumnVarNameInTable="column車両番号" msprop:Generator_ColumnPropNameInRow="車両番号" msprop:Generator_ColumnPropNameInTable="車両番号Column" msprop:Generator_UserColumnName="車両番号">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="5" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="乗務記録NO" msprop:Generator_ColumnVarNameInTable="column乗務記録NO" msprop:Generator_ColumnPropNameInRow="乗務記録NO" msprop:Generator_ColumnPropNameInTable="乗務記録NOColumn" msprop:Generator_UserColumnName="乗務記録NO" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="255" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="取込日付" msprop:Generator_ColumnVarNameInTable="column取込日付" msprop:Generator_ColumnPropNameInRow="取込日付" msprop:Generator_ColumnPropNameInTable="取込日付Column" msprop:Generator_UserColumnName="取込日付" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="営業所CD" msprop:Generator_ColumnVarNameInTable="column営業所CD" msprop:Generator_ColumnPropNameInRow="営業所CD" msprop:Generator_ColumnPropNameInTable="営業所CDColumn" msprop:Generator_UserColumnName="営業所CD" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="部門CD" msprop:Generator_ColumnVarNameInTable="column部門CD" msprop:Generator_ColumnPropNameInRow="部門CD" msprop:Generator_ColumnPropNameInTable="部門CDColumn" msprop:Generator_UserColumnName="部門CD" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="勤務CD" msprop:Generator_ColumnVarNameInTable="column勤務CD" msprop:Generator_ColumnPropNameInRow="勤務CD" msprop:Generator_ColumnPropNameInTable="勤務CDColumn" msprop:Generator_UserColumnName="勤務CD" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="車種CD" msprop:Generator_ColumnVarNameInTable="column車種CD" msprop:Generator_ColumnPropNameInRow="車種CD" msprop:Generator_ColumnPropNameInTable="車種CDColumn" msprop:Generator_UserColumnName="車種CD" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="天気" msprop:Generator_ColumnVarNameInTable="column天気" msprop:Generator_ColumnPropNameInRow="天気" msprop:Generator_ColumnPropNameInTable="天気Column" msprop:Generator_UserColumnName="天気" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="255" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="累計全走行" msprop:Generator_ColumnVarNameInTable="column累計全走行" msprop:Generator_ColumnPropNameInRow="累計全走行" msprop:Generator_ColumnPropNameInTable="累計全走行Column" msprop:Generator_UserColumnName="累計全走行" type="xs:decimal" minOccurs="0" />
              <xs:element name="累計実車走行" msprop:Generator_ColumnVarNameInTable="column累計実車走行" msprop:Generator_ColumnPropNameInRow="累計実車走行" msprop:Generator_ColumnPropNameInTable="累計実車走行Column" msprop:Generator_UserColumnName="累計実車走行" type="xs:decimal" minOccurs="0" />
              <xs:element name="累計迎車走行" msprop:Generator_ColumnVarNameInTable="column累計迎車走行" msprop:Generator_ColumnPropNameInRow="累計迎車走行" msprop:Generator_ColumnPropNameInTable="累計迎車走行Column" msprop:Generator_UserColumnName="累計迎車走行" type="xs:decimal" minOccurs="0" />
              <xs:element name="累計営業回数" msprop:Generator_ColumnVarNameInTable="column累計営業回数" msprop:Generator_ColumnPropNameInRow="累計営業回数" msprop:Generator_ColumnPropNameInTable="累計営業回数Column" msprop:Generator_UserColumnName="累計営業回数" type="xs:decimal" minOccurs="0" />
              <xs:element name="累計以後回数" msprop:Generator_ColumnVarNameInTable="column累計以後回数" msprop:Generator_ColumnPropNameInRow="累計以後回数" msprop:Generator_ColumnPropNameInTable="累計以後回数Column" msprop:Generator_UserColumnName="累計以後回数" type="xs:decimal" minOccurs="0" />
              <xs:element name="累計迎車回数" msprop:Generator_ColumnVarNameInTable="column累計迎車回数" msprop:Generator_ColumnPropNameInRow="累計迎車回数" msprop:Generator_ColumnPropNameInTable="累計迎車回数Column" msprop:Generator_UserColumnName="累計迎車回数" type="xs:decimal" minOccurs="0" />
              <xs:element name="一日全走行" msprop:Generator_ColumnVarNameInTable="column一日全走行" msprop:Generator_ColumnPropNameInRow="一日全走行" msprop:Generator_ColumnPropNameInTable="一日全走行Column" msprop:Generator_UserColumnName="一日全走行" type="xs:decimal" minOccurs="0" />
              <xs:element name="一日実車走行" msprop:Generator_ColumnVarNameInTable="column一日実車走行" msprop:Generator_ColumnPropNameInRow="一日実車走行" msprop:Generator_ColumnPropNameInTable="一日実車走行Column" msprop:Generator_UserColumnName="一日実車走行" type="xs:decimal" minOccurs="0" />
              <xs:element name="一日迎車走行" msprop:Generator_ColumnVarNameInTable="column一日迎車走行" msprop:Generator_ColumnPropNameInRow="一日迎車走行" msprop:Generator_ColumnPropNameInTable="一日迎車走行Column" msprop:Generator_UserColumnName="一日迎車走行" type="xs:decimal" minOccurs="0" />
              <xs:element name="一日営業回数" msprop:Generator_ColumnVarNameInTable="column一日営業回数" msprop:Generator_ColumnPropNameInRow="一日営業回数" msprop:Generator_ColumnPropNameInTable="一日営業回数Column" msprop:Generator_UserColumnName="一日営業回数" type="xs:decimal" minOccurs="0" />
              <xs:element name="一日以後回数" msprop:Generator_ColumnVarNameInTable="column一日以後回数" msprop:Generator_ColumnPropNameInRow="一日以後回数" msprop:Generator_ColumnPropNameInTable="一日以後回数Column" msprop:Generator_UserColumnName="一日以後回数" type="xs:decimal" minOccurs="0" />
              <xs:element name="一日迎車回数" msprop:Generator_ColumnVarNameInTable="column一日迎車回数" msprop:Generator_ColumnPropNameInRow="一日迎車回数" msprop:Generator_ColumnPropNameInTable="一日迎車回数Column" msprop:Generator_UserColumnName="一日迎車回数" type="xs:decimal" minOccurs="0" />
              <xs:element name="累計運賃" msprop:Generator_ColumnVarNameInTable="column累計運賃" msprop:Generator_ColumnPropNameInRow="累計運賃" msprop:Generator_ColumnPropNameInTable="累計運賃Column" msprop:Generator_UserColumnName="累計運賃" type="xs:decimal" minOccurs="0" />
              <xs:element name="累計料金" msprop:Generator_ColumnVarNameInTable="column累計料金" msprop:Generator_ColumnPropNameInRow="累計料金" msprop:Generator_ColumnPropNameInTable="累計料金Column" msprop:Generator_UserColumnName="累計料金" type="xs:decimal" minOccurs="0" />
              <xs:element name="累計身割回数" msprop:Generator_ColumnVarNameInTable="column累計身割回数" msprop:Generator_ColumnPropNameInRow="累計身割回数" msprop:Generator_ColumnPropNameInTable="累計身割回数Column" msprop:Generator_UserColumnName="累計身割回数" type="xs:decimal" minOccurs="0" />
              <xs:element name="累計身割額" msprop:Generator_ColumnVarNameInTable="column累計身割額" msprop:Generator_ColumnPropNameInRow="累計身割額" msprop:Generator_ColumnPropNameInTable="累計身割額Column" msprop:Generator_UserColumnName="累計身割額" type="xs:decimal" minOccurs="0" />
              <xs:element name="累計ワゴン回数" msprop:Generator_ColumnVarNameInTable="column累計ワゴン回数" msprop:Generator_ColumnPropNameInRow="累計ワゴン回数" msprop:Generator_ColumnPropNameInTable="累計ワゴン回数Column" msprop:Generator_UserColumnName="累計ワゴン回数" type="xs:decimal" minOccurs="0" />
              <xs:element name="累計早朝回数" msprop:Generator_ColumnVarNameInTable="column累計早朝回数" msprop:Generator_ColumnPropNameInRow="累計早朝回数" msprop:Generator_ColumnPropNameInTable="累計早朝回数Column" msprop:Generator_UserColumnName="累計早朝回数" type="xs:decimal" minOccurs="0" />
              <xs:element name="累計予約回数" msprop:Generator_ColumnVarNameInTable="column累計予約回数" msprop:Generator_ColumnPropNameInRow="累計予約回数" msprop:Generator_ColumnPropNameInTable="累計予約回数Column" msprop:Generator_UserColumnName="累計予約回数" type="xs:decimal" minOccurs="0" />
              <xs:element name="累計遠割回数" msprop:Generator_ColumnVarNameInTable="column累計遠割回数" msprop:Generator_ColumnPropNameInRow="累計遠割回数" msprop:Generator_ColumnPropNameInTable="累計遠割回数Column" msprop:Generator_UserColumnName="累計遠割回数" type="xs:decimal" minOccurs="0" />
              <xs:element name="累計遠割額" msprop:Generator_ColumnVarNameInTable="column累計遠割額" msprop:Generator_ColumnPropNameInRow="累計遠割額" msprop:Generator_ColumnPropNameInTable="累計遠割額Column" msprop:Generator_UserColumnName="累計遠割額" type="xs:decimal" minOccurs="0" />
              <xs:element name="累計貸切指数" msprop:Generator_ColumnVarNameInTable="column累計貸切指数" msprop:Generator_ColumnPropNameInRow="累計貸切指数" msprop:Generator_ColumnPropNameInTable="累計貸切指数Column" msprop:Generator_UserColumnName="累計貸切指数" type="xs:decimal" minOccurs="0" />
              <xs:element name="累計貸切割引額" msprop:Generator_ColumnVarNameInTable="column累計貸切割引額" msprop:Generator_ColumnPropNameInRow="累計貸切割引額" msprop:Generator_ColumnPropNameInTable="累計貸切割引額Column" msprop:Generator_UserColumnName="累計貸切割引額" type="xs:decimal" minOccurs="0" />
              <xs:element name="累計貸切料金" msprop:Generator_ColumnVarNameInTable="column累計貸切料金" msprop:Generator_ColumnPropNameInRow="累計貸切料金" msprop:Generator_ColumnPropNameInTable="累計貸切料金Column" msprop:Generator_UserColumnName="累計貸切料金" type="xs:decimal" minOccurs="0" />
              <xs:element name="累計貸切時間" msprop:Generator_ColumnVarNameInTable="column累計貸切時間" msprop:Generator_ColumnPropNameInRow="累計貸切時間" msprop:Generator_ColumnPropNameInTable="累計貸切時間Column" msprop:Generator_UserColumnName="累計貸切時間" type="xs:decimal" minOccurs="0" />
              <xs:element name="累計貸切走行" msprop:Generator_ColumnVarNameInTable="column累計貸切走行" msprop:Generator_ColumnPropNameInRow="累計貸切走行" msprop:Generator_ColumnPropNameInTable="累計貸切走行Column" msprop:Generator_UserColumnName="累計貸切走行" type="xs:decimal" minOccurs="0" />
              <xs:element name="累計固定料金キャンセル回数" msprop:Generator_ColumnVarNameInTable="column累計固定料金キャンセル回数" msprop:Generator_ColumnPropNameInRow="累計固定料金キャンセル回数" msprop:Generator_ColumnPropNameInTable="累計固定料金キャンセル回数Column" msprop:Generator_UserColumnName="累計固定料金キャンセル回数" type="xs:decimal" minOccurs="0" />
              <xs:element name="累計迎車キャンセル回数" msprop:Generator_ColumnVarNameInTable="column累計迎車キャンセル回数" msprop:Generator_ColumnPropNameInRow="累計迎車キャンセル回数" msprop:Generator_ColumnPropNameInTable="累計迎車キャンセル回数Column" msprop:Generator_UserColumnName="累計迎車キャンセル回数" type="xs:decimal" minOccurs="0" />
              <xs:element name="累計待回数" msprop:Generator_ColumnVarNameInTable="column累計待回数" msprop:Generator_ColumnPropNameInRow="累計待回数" msprop:Generator_ColumnPropNameInTable="累計待回数Column" msprop:Generator_UserColumnName="累計待回数" type="xs:decimal" minOccurs="0" />
              <xs:element name="累計待加算回数" msprop:Generator_ColumnVarNameInTable="column累計待加算回数" msprop:Generator_ColumnPropNameInRow="累計待加算回数" msprop:Generator_ColumnPropNameInTable="累計待加算回数Column" msprop:Generator_UserColumnName="累計待加算回数" type="xs:decimal" minOccurs="0" />
              <xs:element name="累計早朝キャンセル回数" msprop:Generator_ColumnVarNameInTable="column累計早朝キャンセル回数" msprop:Generator_ColumnPropNameInRow="累計早朝キャンセル回数" msprop:Generator_ColumnPropNameInTable="累計早朝キャンセル回数Column" msprop:Generator_UserColumnName="累計早朝キャンセル回数" type="xs:decimal" minOccurs="0" />
              <xs:element name="累計高齢割回数" msprop:Generator_ColumnVarNameInTable="column累計高齢割回数" msprop:Generator_ColumnPropNameInRow="累計高齢割回数" msprop:Generator_ColumnPropNameInTable="累計高齢割回数Column" msprop:Generator_UserColumnName="累計高齢割回数" type="xs:decimal" minOccurs="0" />
              <xs:element name="累計高齢割額" msprop:Generator_ColumnVarNameInTable="column累計高齢割額" msprop:Generator_ColumnPropNameInRow="累計高齢割額" msprop:Generator_ColumnPropNameInTable="累計高齢割額Column" msprop:Generator_UserColumnName="累計高齢割額" type="xs:decimal" minOccurs="0" />
              <xs:element name="累計幼児割回数" msprop:Generator_ColumnVarNameInTable="column累計幼児割回数" msprop:Generator_ColumnPropNameInRow="累計幼児割回数" msprop:Generator_ColumnPropNameInTable="累計幼児割回数Column" msprop:Generator_UserColumnName="累計幼児割回数" type="xs:decimal" minOccurs="0" />
              <xs:element name="累計幼児割額" msprop:Generator_ColumnVarNameInTable="column累計幼児割額" msprop:Generator_ColumnPropNameInRow="累計幼児割額" msprop:Generator_ColumnPropNameInTable="累計幼児割額Column" msprop:Generator_UserColumnName="累計幼児割額" type="xs:decimal" minOccurs="0" />
              <xs:element name="累計リセット回数" msprop:Generator_ColumnVarNameInTable="column累計リセット回数" msprop:Generator_ColumnPropNameInRow="累計リセット回数" msprop:Generator_ColumnPropNameInTable="累計リセット回数Column" msprop:Generator_UserColumnName="累計リセット回数" type="xs:decimal" minOccurs="0" />
              <xs:element name="累計待回数P" msprop:Generator_ColumnVarNameInTable="column累計待回数P" msprop:Generator_ColumnPropNameInRow="累計待回数P" msprop:Generator_ColumnPropNameInTable="累計待回数PColumn" msprop:Generator_UserColumnName="累計待回数P" type="xs:decimal" minOccurs="0" />
              <xs:element name="一日運賃" msprop:Generator_ColumnVarNameInTable="column一日運賃" msprop:Generator_ColumnPropNameInRow="一日運賃" msprop:Generator_ColumnPropNameInTable="一日運賃Column" msprop:Generator_UserColumnName="一日運賃" type="xs:decimal" minOccurs="0" />
              <xs:element name="一日料金" msprop:Generator_ColumnVarNameInTable="column一日料金" msprop:Generator_ColumnPropNameInRow="一日料金" msprop:Generator_ColumnPropNameInTable="一日料金Column" msprop:Generator_UserColumnName="一日料金" type="xs:decimal" minOccurs="0" />
              <xs:element name="一日身割回数" msprop:Generator_ColumnVarNameInTable="column一日身割回数" msprop:Generator_ColumnPropNameInRow="一日身割回数" msprop:Generator_ColumnPropNameInTable="一日身割回数Column" msprop:Generator_UserColumnName="一日身割回数" type="xs:decimal" minOccurs="0" />
              <xs:element name="一日身割額" msprop:Generator_ColumnVarNameInTable="column一日身割額" msprop:Generator_ColumnPropNameInRow="一日身割額" msprop:Generator_ColumnPropNameInTable="一日身割額Column" msprop:Generator_UserColumnName="一日身割額" type="xs:decimal" minOccurs="0" />
              <xs:element name="一日身割現収" msprop:Generator_ColumnVarNameInTable="column一日身割現収" msprop:Generator_ColumnPropNameInRow="一日身割現収" msprop:Generator_ColumnPropNameInTable="一日身割現収Column" msprop:Generator_UserColumnName="一日身割現収" type="xs:decimal" minOccurs="0" />
              <xs:element name="一日ワゴン回数" msprop:Generator_ColumnVarNameInTable="column一日ワゴン回数" msprop:Generator_ColumnPropNameInRow="一日ワゴン回数" msprop:Generator_ColumnPropNameInTable="一日ワゴン回数Column" msprop:Generator_UserColumnName="一日ワゴン回数" type="xs:decimal" minOccurs="0" />
              <xs:element name="一日早朝回数" msprop:Generator_ColumnVarNameInTable="column一日早朝回数" msprop:Generator_ColumnPropNameInRow="一日早朝回数" msprop:Generator_ColumnPropNameInTable="一日早朝回数Column" msprop:Generator_UserColumnName="一日早朝回数" type="xs:decimal" minOccurs="0" />
              <xs:element name="一日予約回数" msprop:Generator_ColumnVarNameInTable="column一日予約回数" msprop:Generator_ColumnPropNameInRow="一日予約回数" msprop:Generator_ColumnPropNameInTable="一日予約回数Column" msprop:Generator_UserColumnName="一日予約回数" type="xs:decimal" minOccurs="0" />
              <xs:element name="一日遠割回数" msprop:Generator_ColumnVarNameInTable="column一日遠割回数" msprop:Generator_ColumnPropNameInRow="一日遠割回数" msprop:Generator_ColumnPropNameInTable="一日遠割回数Column" msprop:Generator_UserColumnName="一日遠割回数" type="xs:decimal" minOccurs="0" />
              <xs:element name="一日遠割額" msprop:Generator_ColumnVarNameInTable="column一日遠割額" msprop:Generator_ColumnPropNameInRow="一日遠割額" msprop:Generator_ColumnPropNameInTable="一日遠割額Column" msprop:Generator_UserColumnName="一日遠割額" type="xs:decimal" minOccurs="0" />
              <xs:element name="一日貸切指数" msprop:Generator_ColumnVarNameInTable="column一日貸切指数" msprop:Generator_ColumnPropNameInRow="一日貸切指数" msprop:Generator_ColumnPropNameInTable="一日貸切指数Column" msprop:Generator_UserColumnName="一日貸切指数" type="xs:decimal" minOccurs="0" />
              <xs:element name="一日貸切割引" msprop:Generator_ColumnVarNameInTable="column一日貸切割引" msprop:Generator_ColumnPropNameInRow="一日貸切割引" msprop:Generator_ColumnPropNameInTable="一日貸切割引Column" msprop:Generator_UserColumnName="一日貸切割引" type="xs:decimal" minOccurs="0" />
              <xs:element name="一日貸切料金" msprop:Generator_ColumnVarNameInTable="column一日貸切料金" msprop:Generator_ColumnPropNameInRow="一日貸切料金" msprop:Generator_ColumnPropNameInTable="一日貸切料金Column" msprop:Generator_UserColumnName="一日貸切料金" type="xs:decimal" minOccurs="0" />
              <xs:element name="一日貸切時間" msprop:Generator_ColumnVarNameInTable="column一日貸切時間" msprop:Generator_ColumnPropNameInRow="一日貸切時間" msprop:Generator_ColumnPropNameInTable="一日貸切時間Column" msprop:Generator_UserColumnName="一日貸切時間" type="xs:decimal" minOccurs="0" />
              <xs:element name="一日貸切走行" msprop:Generator_ColumnVarNameInTable="column一日貸切走行" msprop:Generator_ColumnPropNameInRow="一日貸切走行" msprop:Generator_ColumnPropNameInTable="一日貸切走行Column" msprop:Generator_UserColumnName="一日貸切走行" type="xs:decimal" minOccurs="0" />
              <xs:element name="一日固定料金キャンセル回数" msprop:Generator_ColumnVarNameInTable="column一日固定料金キャンセル回数" msprop:Generator_ColumnPropNameInRow="一日固定料金キャンセル回数" msprop:Generator_ColumnPropNameInTable="一日固定料金キャンセル回数Column" msprop:Generator_UserColumnName="一日固定料金キャンセル回数" type="xs:decimal" minOccurs="0" />
              <xs:element name="一日迎車キャンセル回数" msprop:Generator_ColumnVarNameInTable="column一日迎車キャンセル回数" msprop:Generator_ColumnPropNameInRow="一日迎車キャンセル回数" msprop:Generator_ColumnPropNameInTable="一日迎車キャンセル回数Column" msprop:Generator_UserColumnName="一日迎車キャンセル回数" type="xs:decimal" minOccurs="0" />
              <xs:element name="一日待ち回数" msprop:Generator_ColumnVarNameInTable="column一日待ち回数" msprop:Generator_ColumnPropNameInRow="一日待ち回数" msprop:Generator_ColumnPropNameInTable="一日待ち回数Column" msprop:Generator_UserColumnName="一日待ち回数" type="xs:decimal" minOccurs="0" />
              <xs:element name="一日待ち加算回数" msprop:Generator_ColumnVarNameInTable="column一日待ち加算回数" msprop:Generator_ColumnPropNameInRow="一日待ち加算回数" msprop:Generator_ColumnPropNameInTable="一日待ち加算回数Column" msprop:Generator_UserColumnName="一日待ち加算回数" type="xs:decimal" minOccurs="0" />
              <xs:element name="一日早朝キャンセル回数" msprop:Generator_ColumnVarNameInTable="column一日早朝キャンセル回数" msprop:Generator_ColumnPropNameInRow="一日早朝キャンセル回数" msprop:Generator_ColumnPropNameInTable="一日早朝キャンセル回数Column" msprop:Generator_UserColumnName="一日早朝キャンセル回数" type="xs:decimal" minOccurs="0" />
              <xs:element name="一日高齢割回数" msprop:Generator_ColumnVarNameInTable="column一日高齢割回数" msprop:Generator_ColumnPropNameInRow="一日高齢割回数" msprop:Generator_ColumnPropNameInTable="一日高齢割回数Column" msprop:Generator_UserColumnName="一日高齢割回数" type="xs:decimal" minOccurs="0" />
              <xs:element name="一日高齢割額" msprop:Generator_ColumnVarNameInTable="column一日高齢割額" msprop:Generator_ColumnPropNameInRow="一日高齢割額" msprop:Generator_ColumnPropNameInTable="一日高齢割額Column" msprop:Generator_UserColumnName="一日高齢割額" type="xs:decimal" minOccurs="0" />
              <xs:element name="一日幼児割回数" msprop:Generator_ColumnVarNameInTable="column一日幼児割回数" msprop:Generator_ColumnPropNameInRow="一日幼児割回数" msprop:Generator_ColumnPropNameInTable="一日幼児割回数Column" msprop:Generator_UserColumnName="一日幼児割回数" type="xs:decimal" minOccurs="0" />
              <xs:element name="一日幼児割額" msprop:Generator_ColumnVarNameInTable="column一日幼児割額" msprop:Generator_ColumnPropNameInRow="一日幼児割額" msprop:Generator_ColumnPropNameInTable="一日幼児割額Column" msprop:Generator_UserColumnName="一日幼児割額" type="xs:decimal" minOccurs="0" />
              <xs:element name="一日リセット回数" msprop:Generator_ColumnVarNameInTable="column一日リセット回数" msprop:Generator_ColumnPropNameInRow="一日リセット回数" msprop:Generator_ColumnPropNameInTable="一日リセット回数Column" msprop:Generator_UserColumnName="一日リセット回数" type="xs:decimal" minOccurs="0" />
              <xs:element name="一日待ち回数P" msprop:Generator_ColumnVarNameInTable="column一日待ち回数P" msprop:Generator_ColumnPropNameInRow="一日待ち回数P" msprop:Generator_ColumnPropNameInTable="一日待ち回数PColumn" msprop:Generator_UserColumnName="一日待ち回数P" type="xs:decimal" minOccurs="0" />
              <xs:element name="基本料金" msprop:Generator_ColumnVarNameInTable="column基本料金" msprop:Generator_ColumnPropNameInRow="基本料金" msprop:Generator_ColumnPropNameInTable="基本料金Column" msprop:Generator_UserColumnName="基本料金" type="xs:decimal" minOccurs="0" />
              <xs:element name="以後料金" msprop:Generator_ColumnVarNameInTable="column以後料金" msprop:Generator_ColumnPropNameInRow="以後料金" msprop:Generator_ColumnPropNameInTable="以後料金Column" msprop:Generator_UserColumnName="以後料金" type="xs:decimal" minOccurs="0" />
              <xs:element name="固定料金" msprop:Generator_ColumnVarNameInTable="column固定料金" msprop:Generator_ColumnPropNameInRow="固定料金" msprop:Generator_ColumnPropNameInTable="固定料金Column" msprop:Generator_UserColumnName="固定料金" type="xs:decimal" minOccurs="0" />
              <xs:element name="月間営収" msprop:Generator_ColumnVarNameInTable="column月間営収" msprop:Generator_ColumnPropNameInRow="月間営収" msprop:Generator_ColumnPropNameInTable="月間営収Column" msprop:Generator_UserColumnName="月間営収" type="xs:decimal" minOccurs="0" />
              <xs:element name="営収" msprop:Generator_ColumnVarNameInTable="column営収" msprop:Generator_ColumnPropNameInRow="営収" msprop:Generator_ColumnPropNameInTable="営収Column" msprop:Generator_UserColumnName="営収" type="xs:decimal" minOccurs="0" />
              <xs:element name="男" msprop:Generator_ColumnVarNameInTable="column男" msprop:Generator_ColumnPropNameInRow="男" msprop:Generator_ColumnPropNameInTable="男Column" msprop:Generator_UserColumnName="男" type="xs:decimal" minOccurs="0" />
              <xs:element name="女" msprop:Generator_ColumnVarNameInTable="column女" msprop:Generator_ColumnPropNameInRow="女" msprop:Generator_ColumnPropNameInTable="女Column" msprop:Generator_UserColumnName="女" type="xs:decimal" minOccurs="0" />
              <xs:element name="現金" msprop:Generator_ColumnVarNameInTable="column現金" msprop:Generator_ColumnPropNameInRow="現金" msprop:Generator_ColumnPropNameInTable="現金Column" msprop:Generator_UserColumnName="現金" type="xs:decimal" minOccurs="0" />
              <xs:element name="未収" msprop:Generator_ColumnVarNameInTable="column未収" msprop:Generator_ColumnPropNameInRow="未収" msprop:Generator_ColumnPropNameInTable="未収Column" msprop:Generator_UserColumnName="未収" type="xs:decimal" minOccurs="0" />
              <xs:element name="クレジット" msprop:Generator_ColumnVarNameInTable="columnクレジット" msprop:Generator_ColumnPropNameInRow="クレジット" msprop:Generator_ColumnPropNameInTable="クレジットColumn" msprop:Generator_UserColumnName="クレジット" type="xs:decimal" minOccurs="0" />
              <xs:element name="カード" msprop:Generator_ColumnVarNameInTable="columnカード" msprop:Generator_ColumnPropNameInRow="カード" msprop:Generator_ColumnPropNameInTable="カードColumn" msprop:Generator_UserColumnName="カード" type="xs:decimal" minOccurs="0" />
              <xs:element name="ID" msprop:Generator_ColumnVarNameInTable="columnID" msprop:Generator_ColumnPropNameInRow="ID" msprop:Generator_ColumnPropNameInTable="IDColumn" msprop:Generator_UserColumnName="ID" type="xs:decimal" minOccurs="0" />
              <xs:element name="交通系IC" msprop:Generator_ColumnVarNameInTable="column交通系IC" msprop:Generator_ColumnPropNameInRow="交通系IC" msprop:Generator_ColumnPropNameInTable="交通系ICColumn" msprop:Generator_UserColumnName="交通系IC" type="xs:decimal" minOccurs="0" />
              <xs:element name="キャブカード" msprop:Generator_ColumnVarNameInTable="columnキャブカード" msprop:Generator_ColumnPropNameInRow="キャブカード" msprop:Generator_ColumnPropNameInTable="キャブカードColumn" msprop:Generator_UserColumnName="キャブカード" type="xs:decimal" minOccurs="0" />
              <xs:element name="プリペイドカード" msprop:Generator_ColumnVarNameInTable="columnプリペイドカード" msprop:Generator_ColumnPropNameInRow="プリペイドカード" msprop:Generator_ColumnPropNameInTable="プリペイドカードColumn" msprop:Generator_UserColumnName="プリペイドカード" type="xs:decimal" minOccurs="0" />
              <xs:element name="WAON" msprop:Generator_ColumnVarNameInTable="columnWAON" msprop:Generator_ColumnPropNameInRow="WAON" msprop:Generator_ColumnPropNameInTable="WAONColumn" msprop:Generator_UserColumnName="WAON" type="xs:decimal" minOccurs="0" />
              <xs:element name="その他" msprop:Generator_ColumnVarNameInTable="columnその他" msprop:Generator_ColumnPropNameInRow="その他" msprop:Generator_ColumnPropNameInTable="その他Column" msprop:Generator_UserColumnName="その他" type="xs:decimal" minOccurs="0" />
              <xs:element name="障割" msprop:Generator_ColumnVarNameInTable="column障割" msprop:Generator_ColumnPropNameInRow="障割" msprop:Generator_ColumnPropNameInTable="障割Column" msprop:Generator_UserColumnName="障割" type="xs:decimal" minOccurs="0" />
              <xs:element name="メーター外料金" msprop:Generator_ColumnVarNameInTable="columnメーター外料金" msprop:Generator_ColumnPropNameInRow="メーター外料金" msprop:Generator_ColumnPropNameInTable="メーター外料金Column" msprop:Generator_UserColumnName="メーター外料金" type="xs:decimal" minOccurs="0" />
              <xs:element name="クレジット回数" msprop:Generator_ColumnVarNameInTable="columnクレジット回数" msprop:Generator_ColumnPropNameInRow="クレジット回数" msprop:Generator_ColumnPropNameInTable="クレジット回数Column" msprop:Generator_UserColumnName="クレジット回数" type="xs:decimal" minOccurs="0" />
              <xs:element name="貸切回数" msprop:Generator_ColumnVarNameInTable="column貸切回数" msprop:Generator_ColumnPropNameInRow="貸切回数" msprop:Generator_ColumnPropNameInTable="貸切回数Column" msprop:Generator_UserColumnName="貸切回数" type="xs:decimal" minOccurs="0" />
              <xs:element name="空車_ETC料金" msprop:Generator_ColumnVarNameInTable="column空車_ETC料金" msprop:Generator_ColumnPropNameInRow="空車_ETC料金" msprop:Generator_ColumnPropNameInTable="空車_ETC料金Column" msprop:Generator_UserColumnName="空車_ETC料金" type="xs:decimal" minOccurs="0" />
              <xs:element name="実車_ETC料金" msprop:Generator_ColumnVarNameInTable="column実車_ETC料金" msprop:Generator_ColumnPropNameInRow="実車_ETC料金" msprop:Generator_ColumnPropNameInTable="実車_ETC料金Column" msprop:Generator_UserColumnName="実車_ETC料金" type="xs:decimal" minOccurs="0" />
              <xs:element name="燃料合計" msprop:Generator_ColumnVarNameInTable="column燃料合計" msprop:Generator_ColumnPropNameInRow="燃料合計" msprop:Generator_ColumnPropNameInTable="燃料合計Column" msprop:Generator_UserColumnName="燃料合計" type="xs:decimal" minOccurs="0" />
              <xs:element name="総勤務時間" msprop:Generator_ColumnVarNameInTable="column総勤務時間" msprop:Generator_ColumnPropNameInRow="総勤務時間" msprop:Generator_ColumnPropNameInTable="総勤務時間Column" msprop:Generator_UserColumnName="総勤務時間" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="総休憩時間" msprop:Generator_ColumnVarNameInTable="column総休憩時間" msprop:Generator_ColumnPropNameInRow="総休憩時間" msprop:Generator_ColumnPropNameInTable="総休憩時間Column" msprop:Generator_UserColumnName="総休憩時間" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="総回送時間" msprop:Generator_ColumnVarNameInTable="column総回送時間" msprop:Generator_ColumnPropNameInRow="総回送時間" msprop:Generator_ColumnPropNameInTable="総回送時間Column" msprop:Generator_UserColumnName="総回送時間" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="総空車停止時間" msprop:Generator_ColumnVarNameInTable="column総空車停止時間" msprop:Generator_ColumnPropNameInRow="総空車停止時間" msprop:Generator_ColumnPropNameInTable="総空車停止時間Column" msprop:Generator_UserColumnName="総空車停止時間" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="リセット待回数" msprop:Generator_ColumnVarNameInTable="columnリセット待回数" msprop:Generator_ColumnPropNameInRow="リセット待回数" msprop:Generator_ColumnPropNameInTable="リセット待回数Column" msprop:Generator_UserColumnName="リセット待回数" type="xs:decimal" minOccurs="0" />
              <xs:element name="出庫_親メーター" msprop:Generator_ColumnVarNameInTable="column出庫_親メーター" msprop:Generator_ColumnPropNameInRow="出庫_親メーター" msprop:Generator_ColumnPropNameInTable="出庫_親メーターColumn" msprop:Generator_UserColumnName="出庫_親メーター" type="xs:decimal" minOccurs="0" />
              <xs:element name="入庫_親メーター" msprop:Generator_ColumnVarNameInTable="column入庫_親メーター" msprop:Generator_ColumnPropNameInRow="入庫_親メーター" msprop:Generator_ColumnPropNameInTable="入庫_親メーターColumn" msprop:Generator_UserColumnName="入庫_親メーター" type="xs:decimal" minOccurs="0" />
              <xs:element name="空車_最高速度" msprop:Generator_ColumnVarNameInTable="column空車_最高速度" msprop:Generator_ColumnPropNameInRow="空車_最高速度" msprop:Generator_ColumnPropNameInTable="空車_最高速度Column" msprop:Generator_UserColumnName="空車_最高速度" type="xs:decimal" minOccurs="0" />
              <xs:element name="実車_最高速度" msprop:Generator_ColumnVarNameInTable="column実車_最高速度" msprop:Generator_ColumnPropNameInRow="実車_最高速度" msprop:Generator_ColumnPropNameInTable="実車_最高速度Column" msprop:Generator_UserColumnName="実車_最高速度" type="xs:decimal" minOccurs="0" />
              <xs:element name="最高速度" msprop:Generator_ColumnVarNameInTable="column最高速度" msprop:Generator_ColumnPropNameInRow="最高速度" msprop:Generator_ColumnPropNameInTable="最高速度Column" msprop:Generator_UserColumnName="最高速度" type="xs:decimal" minOccurs="0" />
              <xs:element name="削除フラグ" msprop:Generator_ColumnVarNameInTable="column削除フラグ" msprop:Generator_ColumnPropNameInRow="削除フラグ" msprop:Generator_ColumnPropNameInTable="削除フラグColumn" msprop:Generator_UserColumnName="削除フラグ" type="xs:decimal" minOccurs="0" />
              <xs:element name="定額差額A" msprop:Generator_ColumnVarNameInTable="column定額差額A" msprop:Generator_ColumnPropNameInRow="定額差額A" msprop:Generator_ColumnPropNameInTable="定額差額AColumn" msprop:Generator_UserColumnName="定額差額A" type="xs:decimal" minOccurs="0" />
              <xs:element name="チケットA" msprop:Generator_ColumnVarNameInTable="columnチケットA" msprop:Generator_ColumnPropNameInRow="チケットA" msprop:Generator_ColumnPropNameInTable="チケットAColumn" msprop:Generator_UserColumnName="チケットA" type="xs:decimal" minOccurs="0" />
              <xs:element name="クーポン四社A" msprop:Generator_ColumnVarNameInTable="columnクーポン四社A" msprop:Generator_ColumnPropNameInRow="クーポン四社A" msprop:Generator_ColumnPropNameInTable="クーポン四社AColumn" msprop:Generator_UserColumnName="クーポン四社A" type="xs:decimal" minOccurs="0" />
              <xs:element name="クーポン東旅協A" msprop:Generator_ColumnVarNameInTable="columnクーポン東旅協A" msprop:Generator_ColumnPropNameInRow="クーポン東旅協A" msprop:Generator_ColumnPropNameInTable="クーポン東旅協AColumn" msprop:Generator_UserColumnName="クーポン東旅協A" type="xs:decimal" minOccurs="0" />
              <xs:element name="福祉券A" msprop:Generator_ColumnVarNameInTable="column福祉券A" msprop:Generator_ColumnPropNameInRow="福祉券A" msprop:Generator_ColumnPropNameInTable="福祉券AColumn" msprop:Generator_UserColumnName="福祉券A" type="xs:decimal" minOccurs="0" />
              <xs:element name="カードA" msprop:Generator_ColumnVarNameInTable="columnカードA" msprop:Generator_ColumnPropNameInRow="カードA" msprop:Generator_ColumnPropNameInTable="カードAColumn" msprop:Generator_UserColumnName="カードA" type="xs:decimal" minOccurs="0" />
              <xs:element name="iDA" msprop:Generator_ColumnVarNameInTable="columniDA" msprop:Generator_ColumnPropNameInRow="iDA" msprop:Generator_ColumnPropNameInTable="iDAColumn" msprop:Generator_UserColumnName="iDA" type="xs:decimal" minOccurs="0" />
              <xs:element name="交通系ICA" msprop:Generator_ColumnVarNameInTable="column交通系ICA" msprop:Generator_ColumnPropNameInRow="交通系ICA" msprop:Generator_ColumnPropNameInTable="交通系ICAColumn" msprop:Generator_UserColumnName="交通系ICA" type="xs:decimal" minOccurs="0" />
              <xs:element name="キャブカードA" msprop:Generator_ColumnVarNameInTable="columnキャブカードA" msprop:Generator_ColumnPropNameInRow="キャブカードA" msprop:Generator_ColumnPropNameInTable="キャブカードAColumn" msprop:Generator_UserColumnName="キャブカードA" type="xs:decimal" minOccurs="0" />
              <xs:element name="プリペイドカードA" msprop:Generator_ColumnVarNameInTable="columnプリペイドカードA" msprop:Generator_ColumnPropNameInRow="プリペイドカードA" msprop:Generator_ColumnPropNameInTable="プリペイドカードAColumn" msprop:Generator_UserColumnName="プリペイドカードA" type="xs:decimal" minOccurs="0" />
              <xs:element name="WAONA" msprop:Generator_ColumnVarNameInTable="columnWAONA" msprop:Generator_ColumnPropNameInRow="WAONA" msprop:Generator_ColumnPropNameInTable="WAONAColumn" msprop:Generator_UserColumnName="WAONA" type="xs:decimal" minOccurs="0" />
              <xs:element name="その他A" msprop:Generator_ColumnVarNameInTable="columnその他A" msprop:Generator_ColumnPropNameInRow="その他A" msprop:Generator_ColumnPropNameInTable="その他AColumn" msprop:Generator_UserColumnName="その他A" type="xs:decimal" minOccurs="0" />
              <xs:element name="障割A" msprop:Generator_ColumnVarNameInTable="column障割A" msprop:Generator_ColumnPropNameInRow="障割A" msprop:Generator_ColumnPropNameInTable="障割AColumn" msprop:Generator_UserColumnName="障割A" type="xs:decimal" minOccurs="0" />
              <xs:element name="現金A" msprop:Generator_ColumnVarNameInTable="column現金A" msprop:Generator_ColumnPropNameInRow="現金A" msprop:Generator_ColumnPropNameInTable="現金AColumn" msprop:Generator_UserColumnName="現金A" type="xs:decimal" minOccurs="0" />
              <xs:element name="空転A" msprop:Generator_ColumnVarNameInTable="column空転A" msprop:Generator_ColumnPropNameInRow="空転A" msprop:Generator_ColumnPropNameInTable="空転AColumn" msprop:Generator_UserColumnName="空転A" type="xs:decimal" minOccurs="0" />
              <xs:element name="信販控除除外" msprop:Generator_ColumnVarNameInTable="column信販控除除外" msprop:Generator_ColumnPropNameInRow="信販控除除外" msprop:Generator_ColumnPropNameInTable="信販控除除外Column" msprop:Generator_UserColumnName="信販控除除外" type="xs:decimal" minOccurs="0" />
              <xs:element name="ETC実車A" msprop:Generator_ColumnVarNameInTable="columnETC実車A" msprop:Generator_ColumnPropNameInRow="ETC実車A" msprop:Generator_ColumnPropNameInTable="ETC実車AColumn" msprop:Generator_UserColumnName="ETC実車A" type="xs:decimal" minOccurs="0" />
              <xs:element name="ETC乗務員A" msprop:Generator_ColumnVarNameInTable="columnETC乗務員A" msprop:Generator_ColumnPropNameInRow="ETC乗務員A" msprop:Generator_ColumnPropNameInTable="ETC乗務員AColumn" msprop:Generator_UserColumnName="ETC乗務員A" type="xs:decimal" minOccurs="0" />
              <xs:element name="ETC会社A" msprop:Generator_ColumnVarNameInTable="columnETC会社A" msprop:Generator_ColumnPropNameInRow="ETC会社A" msprop:Generator_ColumnPropNameInTable="ETC会社AColumn" msprop:Generator_UserColumnName="ETC会社A" type="xs:decimal" minOccurs="0" />
              <xs:element name="登録ユーザーCD" msprop:Generator_ColumnVarNameInTable="column登録ユーザーCD" msprop:Generator_ColumnPropNameInRow="登録ユーザーCD" msprop:Generator_ColumnPropNameInTable="登録ユーザーCDColumn" msprop:Generator_UserColumnName="登録ユーザーCD" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="5" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="登録ユーザー名" msprop:Generator_ColumnVarNameInTable="column登録ユーザー名" msprop:Generator_ColumnPropNameInRow="登録ユーザー名" msprop:Generator_ColumnPropNameInTable="登録ユーザー名Column" msprop:Generator_UserColumnName="登録ユーザー名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="登録日時" msprop:Generator_ColumnVarNameInTable="column登録日時" msprop:Generator_ColumnPropNameInRow="登録日時" msprop:Generator_ColumnPropNameInTable="登録日時Column" msprop:Generator_UserColumnName="登録日時" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="19" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="更新ユーザーCD" msprop:Generator_ColumnVarNameInTable="column更新ユーザーCD" msprop:Generator_ColumnPropNameInRow="更新ユーザーCD" msprop:Generator_ColumnPropNameInTable="更新ユーザーCDColumn" msprop:Generator_UserColumnName="更新ユーザーCD" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="5" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="更新ユーザー名" msprop:Generator_ColumnVarNameInTable="column更新ユーザー名" msprop:Generator_ColumnPropNameInRow="更新ユーザー名" msprop:Generator_ColumnPropNameInTable="更新ユーザー名Column" msprop:Generator_UserColumnName="更新ユーザー名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="更新日時" msprop:Generator_ColumnVarNameInTable="column更新日時" msprop:Generator_ColumnPropNameInRow="更新日時" msprop:Generator_ColumnPropNameInTable="更新日時Column" msprop:Generator_UserColumnName="更新日時" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="19" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="回数料金" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column回数料金" msprop:Generator_ColumnPropNameInRow="回数料金" msprop:Generator_ColumnPropNameInTable="回数料金Column" msprop:Generator_UserColumnName="回数料金" type="xs:decimal" minOccurs="0" />
              <xs:element name="爾後料金" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column爾後料金" msprop:Generator_ColumnPropNameInRow="爾後料金" msprop:Generator_ColumnPropNameInTable="爾後料金Column" msprop:Generator_UserColumnName="爾後料金" type="xs:decimal" minOccurs="0" />
              <xs:element name="迎車料金" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column迎車料金" msprop:Generator_ColumnPropNameInRow="迎車料金" msprop:Generator_ColumnPropNameInTable="迎車料金Column" msprop:Generator_UserColumnName="迎車料金" type="xs:decimal" minOccurs="0" />
              <xs:element name="予約料金" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column予約料金" msprop:Generator_ColumnPropNameInRow="予約料金" msprop:Generator_ColumnPropNameInTable="予約料金Column" msprop:Generator_UserColumnName="予約料金" type="xs:decimal" minOccurs="0" />
              <xs:element name="料金合計" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column料金合計" msprop:Generator_ColumnPropNameInRow="料金合計" msprop:Generator_ColumnPropNameInTable="料金合計Column" msprop:Generator_UserColumnName="料金合計" type="xs:decimal" minOccurs="0" />
              <xs:element name="料金内訳合計" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column料金内訳合計" msprop:Generator_ColumnPropNameInRow="料金内訳合計" msprop:Generator_ColumnPropNameInTable="料金内訳合計Column" msprop:Generator_UserColumnName="料金内訳合計" type="xs:decimal" minOccurs="0" />
              <xs:element name="ETC合計" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnETC合計" msprop:Generator_ColumnPropNameInRow="ETC合計" msprop:Generator_ColumnPropNameInTable="ETC合計Column" msprop:Generator_UserColumnName="ETC合計" type="xs:decimal" minOccurs="0" />
              <xs:element name="回数単価" msprop:Generator_ColumnVarNameInTable="column回数単価" msprop:Generator_ColumnPropNameInRow="回数単価" msprop:Generator_ColumnPropNameInTable="回数単価Column" msprop:Generator_UserColumnName="回数単価" type="xs:decimal" minOccurs="0" />
              <xs:element name="爾後単価" msprop:Generator_ColumnVarNameInTable="column爾後単価" msprop:Generator_ColumnPropNameInRow="爾後単価" msprop:Generator_ColumnPropNameInTable="爾後単価Column" msprop:Generator_UserColumnName="爾後単価" type="xs:decimal" minOccurs="0" />
              <xs:element name="迎車単価" msprop:Generator_ColumnVarNameInTable="column迎車単価" msprop:Generator_ColumnPropNameInRow="迎車単価" msprop:Generator_ColumnPropNameInTable="迎車単価Column" msprop:Generator_UserColumnName="迎車単価" type="xs:decimal" minOccurs="0" />
              <xs:element name="予約単価" msprop:Generator_ColumnVarNameInTable="column予約単価" msprop:Generator_ColumnPropNameInRow="予約単価" msprop:Generator_ColumnPropNameInTable="予約単価Column" msprop:Generator_UserColumnName="予約単価" type="xs:decimal" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="F0事故登録照会" msprop:Generator_TableClassName="F0事故登録照会DataTable" msprop:Generator_TableVarName="tableF0事故登録照会" msprop:Generator_TablePropName="F0事故登録照会" msprop:Generator_RowDeletingName="F0事故登録照会RowDeleting" msprop:Generator_RowChangingName="F0事故登録照会RowChanging" msprop:Generator_RowEvHandlerName="F0事故登録照会RowChangeEventHandler" msprop:Generator_RowDeletedName="F0事故登録照会RowDeleted" msprop:Generator_UserTableName="F0事故登録照会" msprop:Generator_RowChangedName="F0事故登録照会RowChanged" msprop:Generator_RowEvArgName="F0事故登録照会RowChangeEvent" msprop:Generator_RowClassName="F0事故登録照会Row">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="グループCD" msprop:Generator_ColumnVarNameInTable="columnグループCD" msprop:Generator_ColumnPropNameInRow="グループCD" msprop:Generator_ColumnPropNameInTable="グループCDColumn" msprop:Generator_UserColumnName="グループCD">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="個社CD" msprop:Generator_ColumnVarNameInTable="column個社CD" msprop:Generator_ColumnPropNameInRow="個社CD" msprop:Generator_ColumnPropNameInTable="個社CDColumn" msprop:Generator_UserColumnName="個社CD">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="子会社CD" msprop:Generator_ColumnVarNameInTable="column子会社CD" msprop:Generator_ColumnPropNameInRow="子会社CD" msprop:Generator_ColumnPropNameInTable="子会社CDColumn" msprop:Generator_UserColumnName="子会社CD">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="社員名" msprop:Generator_ColumnVarNameInTable="column社員名" msprop:Generator_ColumnPropNameInRow="社員名" msprop:Generator_ColumnPropNameInTable="社員名Column" msprop:Generator_UserColumnName="社員名">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="12" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="社員NO" msprop:Generator_ColumnVarNameInTable="column社員NO" msprop:Generator_ColumnPropNameInRow="社員NO" msprop:Generator_ColumnPropNameInTable="社員NOColumn" msprop:Generator_UserColumnName="社員NO" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="事故区分" msprop:Generator_ColumnVarNameInTable="column事故区分" msprop:Generator_ColumnPropNameInRow="事故区分" msprop:Generator_ColumnPropNameInTable="事故区分Column" msprop:Generator_UserColumnName="事故区分" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="日付" msprop:Generator_ColumnVarNameInTable="column日付" msprop:Generator_ColumnPropNameInRow="日付" msprop:Generator_ColumnPropNameInTable="日付Column" msprop:Generator_UserColumnName="日付" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="8000" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="自社損害金" msprop:Generator_ColumnVarNameInTable="column自社損害金" msprop:Generator_ColumnPropNameInRow="自社損害金" msprop:Generator_ColumnPropNameInTable="自社損害金Column" msprop:Generator_UserColumnName="自社損害金" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="8000" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="相手損害金" msprop:Generator_ColumnVarNameInTable="column相手損害金" msprop:Generator_ColumnPropNameInRow="相手損害金" msprop:Generator_ColumnPropNameInTable="相手損害金Column" msprop:Generator_UserColumnName="相手損害金" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="8000" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="その他" msprop:Generator_ColumnVarNameInTable="columnその他" msprop:Generator_ColumnPropNameInRow="その他" msprop:Generator_ColumnPropNameInTable="その他Column" msprop:Generator_UserColumnName="その他" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="8000" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
    <xs:unique name="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:F0シフト一覧" />
      <xs:field xpath="mstns:グループCD" />
      <xs:field xpath="mstns:個社CD" />
      <xs:field xpath="mstns:子会社CD" />
      <xs:field xpath="mstns:日付" />
      <xs:field xpath="mstns:シフト区分" />
      <xs:field xpath="mstns:乗務員CD" />
    </xs:unique>
    <xs:unique name="F0乗務員シフト区分_Constraint1" msdata:ConstraintName="Constraint1">
      <xs:selector xpath=".//mstns:F0乗務員シフト区分" />
      <xs:field xpath="mstns:グループCD" />
      <xs:field xpath="mstns:個社CD" />
      <xs:field xpath="mstns:子会社CD" />
      <xs:field xpath="mstns:乗務員CD" />
    </xs:unique>
    <xs:unique name="F0乗務員予定実績_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:F0乗務員予定実績" />
      <xs:field xpath="mstns:グループCD" />
      <xs:field xpath="mstns:個社CD" />
      <xs:field xpath="mstns:子会社CD" />
      <xs:field xpath="mstns:乗務員CD" />
    </xs:unique>
    <xs:unique name="F0休暇日一覧_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:F0休暇日一覧" />
      <xs:field xpath="mstns:グループCD" />
      <xs:field xpath="mstns:個社CD" />
      <xs:field xpath="mstns:子会社CD" />
      <xs:field xpath="mstns:乗務員CD" />
      <xs:field xpath="mstns:日付" />
    </xs:unique>
    <xs:unique name="F0乗務記録実績_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:F0乗務記録実績" />
      <xs:field xpath="mstns:グループCD" />
      <xs:field xpath="mstns:個社CD" />
      <xs:field xpath="mstns:子会社CD" />
      <xs:field xpath="mstns:日報日付" />
      <xs:field xpath="mstns:車両番号" />
      <xs:field xpath="mstns:乗務員CD" />
      <xs:field xpath="mstns:出庫時刻" />
      <xs:field xpath="mstns:帰庫時刻" />
    </xs:unique>
    <xs:unique name="F0給与基準データ_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:F0給与基準データ" />
      <xs:field xpath="mstns:グループCD" />
      <xs:field xpath="mstns:個社CD" />
      <xs:field xpath="mstns:子会社CD" />
      <xs:field xpath="mstns:乗務員CD" />
    </xs:unique>
    <xs:unique name="F0乗務記録HD_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:F0乗務記録HD" />
      <xs:field xpath="mstns:グループCD" />
      <xs:field xpath="mstns:個社CD" />
      <xs:field xpath="mstns:子会社CD" />
      <xs:field xpath="mstns:日報日付" />
      <xs:field xpath="mstns:出庫時刻" />
      <xs:field xpath="mstns:入庫時刻" />
      <xs:field xpath="mstns:乗務員CD" />
      <xs:field xpath="mstns:車両番号" />
    </xs:unique>
  </xs:element>
</xs:schema>