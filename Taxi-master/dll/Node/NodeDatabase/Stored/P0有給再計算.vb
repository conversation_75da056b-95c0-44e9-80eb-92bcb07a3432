﻿Imports System.Data
Imports System.Data.SqlClient

Namespace DataSetStoredTableAdapters
    Partial Public Class P0有給再計算TableAdapter
        '''========================================================================================
        ''' <SUMMARY>ﾚｺｰﾄﾞの検索</SUMMARY>   
        ''' <PARAM name="Conditions">抽出条件</PARAM>   
        ''' <RETURNS>検索したﾚｺｰﾄﾞｾｯﾄ</RETURNS>
        '''========================================================================================
        Public Function Execute(ByVal グループCD As String, ByVal 個社CD As String, ByVal 子会社CD As String, ByVal 乗務員CD As String, ByVal 日付 As String) As Integer
            '------------------------------------------------------------------
            ' 変数定義
            '------------------------------------------------------------------
            Dim sqlCmd As New SqlClient.SqlCommand((New NodeDatabase.DataSetStored.P0有給再計算DataTable).TableName, Me.Connection)

            '------------------------------------------------------------------
            ' 引き渡しﾊﾟﾗﾒｰﾀを追加
            '------------------------------------------------------------------
            sqlCmd.Parameters.Add("@グループCD", SqlDbType.VarChar).Value = グループCD
            sqlCmd.Parameters.Add("@個社CD", SqlDbType.VarChar).Value = 個社CD
            sqlCmd.Parameters.Add("@子会社CD", SqlDbType.VarChar).Value = 子会社CD
            sqlCmd.Parameters.Add("@乗務員CD", SqlDbType.VarChar).Value = 乗務員CD
            sqlCmd.Parameters.Add("@日付", SqlDbType.VarChar).Value = 日付

            '------------------------------------------------------------------
            ' 実行
            '------------------------------------------------------------------
            Return BaseDatabase.Adapter.StoredExecute(sqlCmd)
        End Function
    End Class
End Namespace
