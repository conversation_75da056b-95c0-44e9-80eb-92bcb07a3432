﻿<?xml version="1.0" encoding="utf-8"?>
<!--<autogenerated>
     This code was generated by a tool to store the dataset designer's layout information.
     Changes to this file may cause incorrect behavior and will be lost if
     the code is regenerated.
</autogenerated>-->
<DiagramLayout xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" ex:showrelationlabel="False" ViewPortX="0" ViewPortY="0" xmlns:ex="urn:schemas-microsoft-com:xml-msdatasource-layout-extended" xmlns="urn:schemas-microsoft-com:xml-msdatasource-layout">
  <Shapes>
    <Shape ID="DesignTable:P1伝票作成一括" ZOrder="7" X="202" Y="83" Height="96" Width="226" AdapterExpanded="true" DataTableExpanded="true" OldAdapterHeight="0" OldDataTableHeight="0" SplitterPosition="46" />
    <Shape ID="DesignTable:P0伝票削除一括" ZOrder="6" X="189" Y="309" Height="96" Width="228" AdapterExpanded="true" DataTableExpanded="true" OldAdapterHeight="0" OldDataTableHeight="0" SplitterPosition="46" />
    <Shape ID="DesignTable:P1伝票作成" ZOrder="5" X="190" Y="191" Height="96" Width="230" AdapterExpanded="true" DataTableExpanded="true" OldAdapterHeight="0" OldDataTableHeight="0" SplitterPosition="46" />
    <Shape ID="DesignTable:P0乗務記録作成一括" ZOrder="4" X="442" Y="84" Height="96" Width="253" AdapterExpanded="true" DataTableExpanded="true" OldAdapterHeight="0" OldDataTableHeight="0" SplitterPosition="46" />
    <Shape ID="DesignTable:P0シフト予定作成" ZOrder="3" X="440" Y="311" Height="96" Width="300" AdapterExpanded="true" DataTableExpanded="true" OldAdapterHeight="0" OldDataTableHeight="0" SplitterPosition="46" />
    <Shape ID="DesignTable:P0給与基本データ集計" ZOrder="2" X="441" Y="417" Height="96" Width="300" AdapterExpanded="true" DataTableExpanded="true" OldAdapterHeight="0" OldDataTableHeight="0" SplitterPosition="46" />
    <Shape ID="DesignTable:P0有給再計算" ZOrder="1" X="454" Y="197" Height="96" Width="300" AdapterExpanded="true" DataTableExpanded="true" OldAdapterHeight="0" OldDataTableHeight="0" SplitterPosition="46" />
  </Shapes>
  <Connectors />
</DiagramLayout>