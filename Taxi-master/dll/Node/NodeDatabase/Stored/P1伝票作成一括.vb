﻿Imports System.Data
Imports System.Data.SqlClient

Namespace DataSetStoredTableAdapters
    Partial Public Class P1伝票作成一括TableAdapter
        '''========================================================================================
        ''' <SUMMARY>ﾚｺｰﾄﾞの検索</SUMMARY>   
        ''' <PARAM name="Conditions">抽出条件</PARAM>   
        ''' <RETURNS>検索したﾚｺｰﾄﾞｾｯﾄ</RETURNS>
        '''========================================================================================
        Public Function Execute(ByVal セッションID As String) As Integer
            '------------------------------------------------------------------
            ' 変数定義
            '------------------------------------------------------------------
            Dim sqlCmd As New SqlClient.SqlCommand((New NodeDatabase.DataSetStored.P1伝票作成一括DataTable).TableName, Me.Connection)

            '------------------------------------------------------------------
            ' 引き渡しﾊﾟﾗﾒｰﾀを追加
            '------------------------------------------------------------------
            sqlCmd.Parameters.Add("@セッションID", SqlDbType.VarChar).Value = セッションID

            '------------------------------------------------------------------
            ' 実行
            '------------------------------------------------------------------
            Return BaseDatabase.Adapter.StoredExecute(sqlCmd)
        End Function
    End Class
End Namespace
