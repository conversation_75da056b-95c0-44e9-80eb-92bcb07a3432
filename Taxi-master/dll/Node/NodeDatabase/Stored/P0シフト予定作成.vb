﻿Imports System.Data
Imports System.Data.SqlClient

Namespace DataSetStoredTableAdapters
    Partial Public Class P0シフト予定作成TableAdapter
        '''========================================================================================
        ''' <SUMMARY>ﾚｺｰﾄﾞの検索</SUMMARY>   
        ''' <PARAM name="Conditions">抽出条件</PARAM>   
        ''' <RETURNS>検索したﾚｺｰﾄﾞｾｯﾄ</RETURNS>
        '''========================================================================================
        Public Function Execute(ByVal グループCD As String, ByVal 個社CD As String, ByVal 子会社CD As String, ByVal ユーザCD As String, ByVal ユーザ名 As String, ByVal 日付F As String, ByVal 日付T As String) As Integer
            '------------------------------------------------------------------
            ' 変数定義
            '------------------------------------------------------------------
            Dim sqlCmd As New SqlClient.SqlCommand((New NodeDatabase.DataSetStored.P0シフト予定作成DataTable).TableName, Me.Connection)

            '------------------------------------------------------------------
            ' 引き渡しﾊﾟﾗﾒｰﾀを追加
            '------------------------------------------------------------------
            sqlCmd.Parameters.Add("@グループCD", SqlDbType.VarChar).Value = グループCD
            sqlCmd.Parameters.Add("@個社CD", SqlDbType.VarChar).Value = 個社CD
            sqlCmd.Parameters.Add("@子会社CD", SqlDbType.VarChar).Value = 子会社CD
            sqlCmd.Parameters.Add("@currentUserID", SqlDbType.VarChar).Value = ユーザCD
            sqlCmd.Parameters.Add("@currentUserNm", SqlDbType.VarChar).Value = ユーザ名
            sqlCmd.Parameters.Add("@startDate", SqlDbType.VarChar).Value = 日付F
            sqlCmd.Parameters.Add("@endDate", SqlDbType.VarChar).Value = 日付T

            '------------------------------------------------------------------
            ' 実行
            '------------------------------------------------------------------
            Return BaseDatabase.Adapter.StoredExecute(sqlCmd)
        End Function
    End Class
End Namespace
