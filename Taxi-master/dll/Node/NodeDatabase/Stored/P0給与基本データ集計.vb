﻿Imports System.Data
Imports System.Data.SqlClient

Namespace DataSetStoredTableAdapters
    Partial Public Class P0給与基本データ集計TableAdapter
        '''========================================================================================
        ''' <SUMMARY>ﾚｺｰﾄﾞの検索</SUMMARY>   
        ''' <PARAM name="Conditions">抽出条件</PARAM>   
        ''' <RETURNS>検索したﾚｺｰﾄﾞｾｯﾄ</RETURNS>
        '''========================================================================================
        Public Function Execute(ByVal グループCD As String, ByVal 個社CD As String, ByVal 子会社CD As String, ByVal 日付F As String, ByVal 日付T As String, ByVal YM As String) As Integer
            '------------------------------------------------------------------
            ' 変数定義
            '------------------------------------------------------------------
            Dim sqlCmd As New SqlClient.SqlCommand((New NodeDatabase.DataSetStored.P0給与基本データ集計DataTable).TableName, Me.Connection)

            '------------------------------------------------------------------
            ' 引き渡しﾊﾟﾗﾒｰﾀを追加
            '------------------------------------------------------------------
            sqlCmd.Parameters.Add("@グループCD", SqlDbType.VarChar).Value = グループCD
            sqlCmd.Parameters.Add("@個社CD", SqlDbType.VarChar).Value = 個社CD
            sqlCmd.Parameters.Add("@子会社CD", SqlDbType.VarChar).Value = 子会社CD
            sqlCmd.Parameters.Add("@日付F", SqlDbType.VarChar).Value = 日付F
            sqlCmd.Parameters.Add("@日付T", SqlDbType.VarChar).Value = 日付T
            sqlCmd.Parameters.Add("@YM", SqlDbType.VarChar).Value = YM

            '------------------------------------------------------------------
            ' 実行
            '------------------------------------------------------------------
            Return BaseDatabase.Adapter.StoredExecute(sqlCmd)
        End Function
    End Class
End Namespace
