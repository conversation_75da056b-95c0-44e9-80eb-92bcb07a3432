﻿Imports System.Data
Imports System.Data.SqlClient

Namespace DataSetStoredTableAdapters
    Partial Public Class P1伝票作成TableAdapter
        '''========================================================================================
        ''' <SUMMARY>ﾚｺｰﾄﾞの検索</SUMMARY>   
        ''' <PARAM name="Conditions">抽出条件</PARAM>   
        ''' <RETURNS>検索したﾚｺｰﾄﾞｾｯﾄ</RETURNS>
        '''========================================================================================
        Public Function Execute(ByVal セッションID As String, ByVal 権限外部 As String) As Integer
            '------------------------------------------------------------------
            ' 変数定義
            '------------------------------------------------------------------
            Dim sqlCmd As New SqlClient.SqlCommand((New NodeDatabase.DataSetStored.P1伝票作成DataTable).TableName, Me.Connection)

            '------------------------------------------------------------------
            ' 引き渡しﾊﾟﾗﾒｰﾀを追加
            '------------------------------------------------------------------
            sqlCmd.Parameters.Add("@セッションID", SqlDbType.VarChar).Value = セッションID
            sqlCmd.Parameters.Add("@権限外部", SqlDbType.VarChar).Value = 権限外部

            '------------------------------------------------------------------
            ' 実行
            '------------------------------------------------------------------
            Return BaseDatabase.Adapter.StoredExecute(sqlCmd)
        End Function
    End Class
End Namespace
