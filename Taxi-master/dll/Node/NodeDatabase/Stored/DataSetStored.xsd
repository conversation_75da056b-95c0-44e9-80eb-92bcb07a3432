﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="DataSetStored" targetNamespace="http://tempuri.org/DataSetStored.xsd" xmlns:mstns="http://tempuri.org/DataSetStored.xsd" xmlns="http://tempuri.org/DataSetStored.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" GeneratorFunctionsComponentClassName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" UserFunctionsComponentName="QueriesTableAdapter" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections>
          <Connection AppSettingsObjectName="MySettings" AppSettingsPropertyName="ConnectionStringBase" ConnectionStringObject="" IsAppSettingsProperty="true" Modifier="Assembly" Name="ConnectionStringBase (MySettings)" ParameterPrefix="@" PropertyReference="ApplicationSettings.NodeDatabase.My.MySettings.GlobalReference.Default.ConnectionStringBase" Provider="System.Data.SqlClient" />
        </Connections>
        <Tables>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="P1伝票作成一括TableAdapter" GeneratorDataComponentClassName="P1伝票作成一括TableAdapter" Name="P1伝票作成一括" UserDataComponentName="P1伝票作成一括TableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionStringBase (MySettings)" DbObjectName="Taxi.dbo.P1伝票作成一括" DbObjectType="StoredProcedure" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <InsertCommand>
                  <DbCommand CommandType="StoredProcedure" ModifiedByUser="false">
                    <CommandText>dbo.P1伝票作成一括</CommandText>
                    <Parameters />
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="StoredProcedure" ModifiedByUser="false">
                    <CommandText>dbo.P1伝票作成一括</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="ReturnValue" ParameterName="@RETURN_VALUE" Precision="10" ProviderType="Int" Scale="0" Size="4" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="varchar" DbType="AnsiString" Direction="Input" ParameterName="@セッションID" Precision="0" ProviderType="VarChar" Scale="0" Size="255" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings />
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="P0伝票削除一括TableAdapter" GeneratorDataComponentClassName="P0伝票削除一括TableAdapter" Name="P0伝票削除一括" UserDataComponentName="P0伝票削除一括TableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionStringBase (MySettings)" DbObjectName="Taxi.dbo.P0伝票削除一括" DbObjectType="StoredProcedure" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="StoredProcedure" ModifiedByUser="false">
                    <CommandText>dbo.P0伝票削除一括</CommandText>
                    <Parameters />
                  </DbCommand>
                </DeleteCommand>
                <SelectCommand>
                  <DbCommand CommandType="StoredProcedure" ModifiedByUser="false">
                    <CommandText>dbo.P0伝票削除一括</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="ReturnValue" ParameterName="@RETURN_VALUE" Precision="10" ProviderType="Int" Scale="0" Size="4" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="varchar" DbType="AnsiString" Direction="Input" ParameterName="@セッションID" Precision="0" ProviderType="VarChar" Scale="0" Size="255" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings />
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="P1伝票作成TableAdapter" GeneratorDataComponentClassName="P1伝票作成TableAdapter" Name="P1伝票作成" UserDataComponentName="P1伝票作成TableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionStringBase (MySettings)" DbObjectName="Taxi.dbo.P1伝票作成" DbObjectType="StoredProcedure" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <InsertCommand>
                  <DbCommand CommandType="StoredProcedure" ModifiedByUser="false">
                    <CommandText>dbo.P1伝票作成</CommandText>
                    <Parameters />
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="StoredProcedure" ModifiedByUser="false">
                    <CommandText>dbo.P1伝票作成</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="ReturnValue" ParameterName="@RETURN_VALUE" Precision="10" ProviderType="Int" Scale="0" Size="4" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="varchar" DbType="AnsiString" Direction="Input" ParameterName="@セッションID" Precision="0" ProviderType="VarChar" Scale="0" Size="255" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="varchar" DbType="AnsiString" Direction="Input" ParameterName="@権限外部" Precision="0" ProviderType="VarChar" Scale="0" Size="2" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings />
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="P0乗務記録作成一括TableAdapter" GeneratorDataComponentClassName="P0乗務記録作成一括TableAdapter" Name="P0乗務記録作成一括" UserDataComponentName="P0乗務記録作成一括TableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionStringBase (MySettings)" DbObjectName="Taxiamaru0712.dbo.P0乗務記録作成一括" DbObjectType="StoredProcedure" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <InsertCommand>
                  <DbCommand CommandType="StoredProcedure" ModifiedByUser="false">
                    <CommandText>dbo.P0乗務記録作成一括</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="ReturnValue" ParameterName="@RETURN_VALUE" Precision="10" ProviderType="Int" Scale="0" Size="4" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="varchar" DbType="AnsiString" Direction="Input" ParameterName="@セッションID" Precision="0" ProviderType="VarChar" Scale="0" Size="255" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="StoredProcedure" ModifiedByUser="false">
                    <CommandText>dbo.P0乗務記録作成一括</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="ReturnValue" ParameterName="@RETURN_VALUE" Precision="10" ProviderType="Int" Scale="0" Size="4" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="varchar" DbType="AnsiString" Direction="Input" ParameterName="@セッションID" Precision="0" ProviderType="VarChar" Scale="0" Size="255" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="StoredProcedure" ModifiedByUser="false">
                    <CommandText>dbo.P0乗務記録作成一括</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="ReturnValue" ParameterName="@RETURN_VALUE" Precision="10" ProviderType="Int" Scale="0" Size="4" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="varchar" DbType="AnsiString" Direction="Input" ParameterName="@セッションID" Precision="0" ProviderType="VarChar" Scale="0" Size="255" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings />
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="P0シフト予定作成TableAdapter" GeneratorDataComponentClassName="P0シフト予定作成TableAdapter" Name="P0シフト予定作成" UserDataComponentName="P0シフト予定作成TableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionStringBase (MySettings)" DbObjectName="Taxi.dbo.P0シフト予定作成" DbObjectType="StoredProcedure" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="StoredProcedure" ModifiedByUser="false">
                    <CommandText>dbo.P0シフト予定作成</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="ReturnValue" ParameterName="@RETURN_VALUE" Precision="10" ProviderType="Int" Scale="0" Size="4" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="varchar" DbType="AnsiString" Direction="Input" ParameterName="@グループCD" Precision="0" ProviderType="VarChar" Scale="0" Size="1" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="varchar" DbType="AnsiString" Direction="Input" ParameterName="@個社CD" Precision="0" ProviderType="VarChar" Scale="0" Size="2" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="varchar" DbType="AnsiString" Direction="Input" ParameterName="@子会社CD" Precision="0" ProviderType="VarChar" Scale="0" Size="1" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="varchar" DbType="AnsiString" Direction="Input" ParameterName="@currentUserID" Precision="0" ProviderType="VarChar" Scale="0" Size="50" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="varchar" DbType="AnsiString" Direction="Input" ParameterName="@currentUserNm" Precision="0" ProviderType="VarChar" Scale="0" Size="50" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="varchar" DbType="AnsiString" Direction="Input" ParameterName="@startDate" Precision="0" ProviderType="VarChar" Scale="0" Size="10" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="varchar" DbType="AnsiString" Direction="Input" ParameterName="@endDate" Precision="0" ProviderType="VarChar" Scale="0" Size="10" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings />
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="P0給与基本データ集計TableAdapter" GeneratorDataComponentClassName="P0給与基本データ集計TableAdapter" Name="P0給与基本データ集計" UserDataComponentName="P0給与基本データ集計TableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionStringBase (MySettings)" DbObjectName="Taxi.dbo.P0給与基本データ集計" DbObjectType="StoredProcedure" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <InsertCommand>
                  <DbCommand CommandType="StoredProcedure" ModifiedByUser="false">
                    <CommandText>dbo.P0給与基本データ集計</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="ReturnValue" ParameterName="@RETURN_VALUE" Precision="10" ProviderType="Int" Scale="0" Size="4" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="varchar" DbType="AnsiString" Direction="Input" ParameterName="@グループCD" Precision="0" ProviderType="VarChar" Scale="0" Size="1" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="varchar" DbType="AnsiString" Direction="Input" ParameterName="@個社CD" Precision="0" ProviderType="VarChar" Scale="0" Size="2" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="varchar" DbType="AnsiString" Direction="Input" ParameterName="@子会社CD" Precision="0" ProviderType="VarChar" Scale="0" Size="1" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="varchar" DbType="AnsiString" Direction="Input" ParameterName="@日付F" Precision="0" ProviderType="VarChar" Scale="0" Size="10" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="varchar" DbType="AnsiString" Direction="Input" ParameterName="@日付T" Precision="0" ProviderType="VarChar" Scale="0" Size="10" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="varchar" DbType="AnsiString" Direction="Input" ParameterName="@YM" Precision="0" ProviderType="VarChar" Scale="0" Size="7" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="StoredProcedure" ModifiedByUser="false">
                    <CommandText>dbo.P0給与基本データ集計</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="ReturnValue" ParameterName="@RETURN_VALUE" Precision="10" ProviderType="Int" Scale="0" Size="4" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="varchar" DbType="AnsiString" Direction="Input" ParameterName="@グループCD" Precision="0" ProviderType="VarChar" Scale="0" Size="1" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="varchar" DbType="AnsiString" Direction="Input" ParameterName="@個社CD" Precision="0" ProviderType="VarChar" Scale="0" Size="2" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="varchar" DbType="AnsiString" Direction="Input" ParameterName="@子会社CD" Precision="0" ProviderType="VarChar" Scale="0" Size="1" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="varchar" DbType="AnsiString" Direction="Input" ParameterName="@日付F" Precision="0" ProviderType="VarChar" Scale="0" Size="10" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="varchar" DbType="AnsiString" Direction="Input" ParameterName="@日付T" Precision="0" ProviderType="VarChar" Scale="0" Size="10" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="varchar" DbType="AnsiString" Direction="Input" ParameterName="@YM" Precision="0" ProviderType="VarChar" Scale="0" Size="7" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings />
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="P0有給再計算TableAdapter" GeneratorDataComponentClassName="P0有給再計算TableAdapter" Name="P0有給再計算" UserDataComponentName="P0有給再計算TableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionStringBase (MySettings)" DbObjectName="Taxi.dbo.P0有給再計算" DbObjectType="StoredProcedure" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <InsertCommand>
                  <DbCommand CommandType="StoredProcedure" ModifiedByUser="false">
                    <CommandText>dbo.P0有給再計算</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="ReturnValue" ParameterName="@RETURN_VALUE" Precision="10" ProviderType="Int" Scale="0" Size="4" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="varchar" DbType="AnsiString" Direction="Input" ParameterName="@グループCD" Precision="0" ProviderType="VarChar" Scale="0" Size="1" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="varchar" DbType="AnsiString" Direction="Input" ParameterName="@個社CD" Precision="0" ProviderType="VarChar" Scale="0" Size="2" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="varchar" DbType="AnsiString" Direction="Input" ParameterName="@子会社CD" Precision="0" ProviderType="VarChar" Scale="0" Size="1" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="varchar" DbType="AnsiString" Direction="Input" ParameterName="@乗務員CD" Precision="0" ProviderType="VarChar" Scale="0" Size="6" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="varchar" DbType="AnsiString" Direction="Input" ParameterName="@日付" Precision="0" ProviderType="VarChar" Scale="0" Size="10" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="StoredProcedure" ModifiedByUser="false">
                    <CommandText>dbo.P0有給再計算</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="ReturnValue" ParameterName="@RETURN_VALUE" Precision="10" ProviderType="Int" Scale="0" Size="4" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="varchar" DbType="AnsiString" Direction="Input" ParameterName="@グループCD" Precision="0" ProviderType="VarChar" Scale="0" Size="1" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="varchar" DbType="AnsiString" Direction="Input" ParameterName="@個社CD" Precision="0" ProviderType="VarChar" Scale="0" Size="2" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="varchar" DbType="AnsiString" Direction="Input" ParameterName="@子会社CD" Precision="0" ProviderType="VarChar" Scale="0" Size="1" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="varchar" DbType="AnsiString" Direction="Input" ParameterName="@乗務員CD" Precision="0" ProviderType="VarChar" Scale="0" Size="6" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="varchar" DbType="AnsiString" Direction="Input" ParameterName="@日付" Precision="0" ProviderType="VarChar" Scale="0" Size="10" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="StoredProcedure" ModifiedByUser="false">
                    <CommandText>dbo.P0有給再計算</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="ReturnValue" ParameterName="@RETURN_VALUE" Precision="10" ProviderType="Int" Scale="0" Size="4" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="varchar" DbType="AnsiString" Direction="Input" ParameterName="@グループCD" Precision="0" ProviderType="VarChar" Scale="0" Size="1" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="varchar" DbType="AnsiString" Direction="Input" ParameterName="@個社CD" Precision="0" ProviderType="VarChar" Scale="0" Size="2" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="varchar" DbType="AnsiString" Direction="Input" ParameterName="@子会社CD" Precision="0" ProviderType="VarChar" Scale="0" Size="1" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="varchar" DbType="AnsiString" Direction="Input" ParameterName="@乗務員CD" Precision="0" ProviderType="VarChar" Scale="0" Size="6" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="varchar" DbType="AnsiString" Direction="Input" ParameterName="@日付" Precision="0" ProviderType="VarChar" Scale="0" Size="10" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings />
            <Sources />
          </TableAdapter>
        </Tables>
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="DataSetStored" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:EnableTableAdapterManager="true" msprop:Generator_DataSetName="DataSetStored" msprop:Generator_UserDSName="DataSetStored">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="P1伝票作成一括" msprop:Generator_TableClassName="P1伝票作成一括DataTable" msprop:Generator_TableVarName="tableP1伝票作成一括" msprop:Generator_RowChangedName="P1伝票作成一括RowChanged" msprop:Generator_TablePropName="P1伝票作成一括" msprop:Generator_RowDeletingName="P1伝票作成一括RowDeleting" msprop:Generator_RowChangingName="P1伝票作成一括RowChanging" msprop:Generator_RowEvHandlerName="P1伝票作成一括RowChangeEventHandler" msprop:Generator_RowDeletedName="P1伝票作成一括RowDeleted" msprop:Generator_RowClassName="P1伝票作成一括Row" msprop:Generator_UserTableName="P1伝票作成一括" msprop:Generator_RowEvArgName="P1伝票作成一括RowChangeEvent">
          <xs:complexType>
          </xs:complexType>
        </xs:element>
        <xs:element name="P0伝票削除一括" msprop:Generator_TableClassName="P0伝票削除一括DataTable" msprop:Generator_TableVarName="tableP0伝票削除一括" msprop:Generator_RowChangedName="P0伝票削除一括RowChanged" msprop:Generator_TablePropName="P0伝票削除一括" msprop:Generator_RowDeletingName="P0伝票削除一括RowDeleting" msprop:Generator_RowChangingName="P0伝票削除一括RowChanging" msprop:Generator_RowEvHandlerName="P0伝票削除一括RowChangeEventHandler" msprop:Generator_RowDeletedName="P0伝票削除一括RowDeleted" msprop:Generator_RowClassName="P0伝票削除一括Row" msprop:Generator_UserTableName="P0伝票削除一括" msprop:Generator_RowEvArgName="P0伝票削除一括RowChangeEvent">
          <xs:complexType>
          </xs:complexType>
        </xs:element>
        <xs:element name="P1伝票作成" msprop:Generator_TableClassName="P1伝票作成DataTable" msprop:Generator_TableVarName="tableP1伝票作成" msprop:Generator_RowChangedName="P1伝票作成RowChanged" msprop:Generator_TablePropName="P1伝票作成" msprop:Generator_RowDeletingName="P1伝票作成RowDeleting" msprop:Generator_RowChangingName="P1伝票作成RowChanging" msprop:Generator_RowEvHandlerName="P1伝票作成RowChangeEventHandler" msprop:Generator_RowDeletedName="P1伝票作成RowDeleted" msprop:Generator_RowClassName="P1伝票作成Row" msprop:Generator_UserTableName="P1伝票作成" msprop:Generator_RowEvArgName="P1伝票作成RowChangeEvent">
          <xs:complexType>
          </xs:complexType>
        </xs:element>
        <xs:element name="P0乗務記録作成一括" msprop:Generator_TableClassName="P0乗務記録作成一括DataTable" msprop:Generator_TableVarName="tableP0乗務記録作成一括" msprop:Generator_TablePropName="P0乗務記録作成一括" msprop:Generator_RowDeletingName="P0乗務記録作成一括RowDeleting" msprop:Generator_RowChangingName="P0乗務記録作成一括RowChanging" msprop:Generator_RowEvHandlerName="P0乗務記録作成一括RowChangeEventHandler" msprop:Generator_RowDeletedName="P0乗務記録作成一括RowDeleted" msprop:Generator_UserTableName="P0乗務記録作成一括" msprop:Generator_RowChangedName="P0乗務記録作成一括RowChanged" msprop:Generator_RowEvArgName="P0乗務記録作成一括RowChangeEvent" msprop:Generator_RowClassName="P0乗務記録作成一括Row">
          <xs:complexType>
          </xs:complexType>
        </xs:element>
        <xs:element name="P0シフト予定作成" msprop:Generator_TableClassName="P0シフト予定作成DataTable" msprop:Generator_TableVarName="tableP0シフト予定作成" msprop:Generator_TablePropName="P0シフト予定作成" msprop:Generator_RowDeletingName="P0シフト予定作成RowDeleting" msprop:Generator_RowChangingName="P0シフト予定作成RowChanging" msprop:Generator_RowEvHandlerName="P0シフト予定作成RowChangeEventHandler" msprop:Generator_RowDeletedName="P0シフト予定作成RowDeleted" msprop:Generator_UserTableName="P0シフト予定作成" msprop:Generator_RowChangedName="P0シフト予定作成RowChanged" msprop:Generator_RowEvArgName="P0シフト予定作成RowChangeEvent" msprop:Generator_RowClassName="P0シフト予定作成Row">
          <xs:complexType>
          </xs:complexType>
        </xs:element>
        <xs:element name="P0給与基本データ集計" msprop:Generator_TableClassName="P0給与基本データ集計DataTable" msprop:Generator_TableVarName="tableP0給与基本データ集計" msprop:Generator_RowChangedName="P0給与基本データ集計RowChanged" msprop:Generator_TablePropName="P0給与基本データ集計" msprop:Generator_RowDeletingName="P0給与基本データ集計RowDeleting" msprop:Generator_RowChangingName="P0給与基本データ集計RowChanging" msprop:Generator_RowEvHandlerName="P0給与基本データ集計RowChangeEventHandler" msprop:Generator_RowDeletedName="P0給与基本データ集計RowDeleted" msprop:Generator_RowClassName="P0給与基本データ集計Row" msprop:Generator_UserTableName="P0給与基本データ集計" msprop:Generator_RowEvArgName="P0給与基本データ集計RowChangeEvent">
          <xs:complexType>
          </xs:complexType>
        </xs:element>
        <xs:element name="P0有給再計算" msprop:Generator_TableClassName="P0有給再計算DataTable" msprop:Generator_TableVarName="tableP0有給再計算" msprop:Generator_TablePropName="P0有給再計算" msprop:Generator_RowDeletingName="P0有給再計算RowDeleting" msprop:Generator_RowChangingName="P0有給再計算RowChanging" msprop:Generator_RowEvHandlerName="P0有給再計算RowChangeEventHandler" msprop:Generator_RowDeletedName="P0有給再計算RowDeleted" msprop:Generator_UserTableName="P0有給再計算" msprop:Generator_RowChangedName="P0有給再計算RowChanged" msprop:Generator_RowEvArgName="P0有給再計算RowChangeEvent" msprop:Generator_RowClassName="P0有給再計算Row">
          <xs:complexType>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>