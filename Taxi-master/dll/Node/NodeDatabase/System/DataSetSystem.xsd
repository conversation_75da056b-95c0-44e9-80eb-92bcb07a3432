﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="DataSetSystem" targetNamespace="http://tempuri.org/DataSetSystem.xsd" xmlns:mstns="http://tempuri.org/DataSetSystem.xsd" xmlns="http://tempuri.org/DataSetSystem.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections>
          <Connection AppSettingsObjectName="MySettings" AppSettingsPropertyName="ConnectionStringBase" ConnectionStringObject="" IsAppSettingsProperty="true" Modifier="Assembly" Name="ConnectionStringBase (MySettings)" ParameterPrefix="@" PropertyReference="ApplicationSettings.NodeDatabase.My.MySettings.GlobalReference.Default.ConnectionStringBase" Provider="System.Data.SqlClient" />
        </Connections>
        <Tables>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="S画面状態TableAdapter" GeneratorDataComponentClassName="S画面状態TableAdapter" Name="S画面状態" UserDataComponentName="S画面状態TableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionStringBase (MySettings)" DbObjectName="SignProject.dbo.S画面状態" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [S画面状態] WHERE (([画面ID] = @Original_画面ID) AND ([共有単位] = @Original_共有単位) AND ([コントロールID] = @Original_コントロールID) AND ((@IsNull_値 = 1 AND [値] IS NULL) OR ([値] = @Original_値)) AND ((@IsNull_更新日時 = 1 AND [更新日時] IS NULL) OR ([更新日時] = @Original_更新日時)) AND ((@IsNull_更新者CD = 1 AND [更新者CD] IS NULL) OR ([更新者CD] = @Original_更新者CD)))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_画面ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="画面ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_共有単位" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="共有単位" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_コントロールID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="コントロールID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_値" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="値" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_値" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="値" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_更新日時" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="更新日時" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_更新日時" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="更新日時" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_更新者CD" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="更新者CD" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_更新者CD" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="更新者CD" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [S画面状態] ([画面ID], [共有単位], [コントロールID], [値], [更新日時], [更新者CD]) VALUES (@画面ID, @共有単位, @コントロールID, @値, @更新日時, @更新者CD);
SELECT 画面ID, 共有単位, コントロールID, 値, 更新日時, 更新者CD FROM S画面状態 WHERE (コントロールID = @コントロールID) AND (共有単位 = @共有単位) AND (画面ID = @画面ID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@画面ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="画面ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@共有単位" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="共有単位" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@コントロールID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="コントロールID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@値" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="値" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@更新日時" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="更新日時" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@更新者CD" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="更新者CD" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT            S画面状態.*
FROM              S画面状態</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [S画面状態] SET [画面ID] = @画面ID, [共有単位] = @共有単位, [コントロールID] = @コントロールID, [値] = @値, [更新日時] = @更新日時, [更新者CD] = @更新者CD WHERE (([画面ID] = @Original_画面ID) AND ([共有単位] = @Original_共有単位) AND ([コントロールID] = @Original_コントロールID) AND ((@IsNull_値 = 1 AND [値] IS NULL) OR ([値] = @Original_値)) AND ((@IsNull_更新日時 = 1 AND [更新日時] IS NULL) OR ([更新日時] = @Original_更新日時)) AND ((@IsNull_更新者CD = 1 AND [更新者CD] IS NULL) OR ([更新者CD] = @Original_更新者CD)));
SELECT 画面ID, 共有単位, コントロールID, 値, 更新日時, 更新者CD FROM S画面状態 WHERE (コントロールID = @コントロールID) AND (共有単位 = @共有単位) AND (画面ID = @画面ID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@画面ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="画面ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@共有単位" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="共有単位" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@コントロールID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="コントロールID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@値" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="値" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@更新日時" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="更新日時" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@更新者CD" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="更新者CD" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_画面ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="画面ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_共有単位" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="共有単位" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_コントロールID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="コントロールID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_値" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="値" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_値" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="値" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_更新日時" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="更新日時" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_更新日時" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="更新日時" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_更新者CD" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="更新者CD" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_更新者CD" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="更新者CD" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="画面ID" DataSetColumn="画面ID" />
              <Mapping SourceColumn="共有単位" DataSetColumn="共有単位" />
              <Mapping SourceColumn="コントロールID" DataSetColumn="コントロールID" />
              <Mapping SourceColumn="値" DataSetColumn="値" />
              <Mapping SourceColumn="更新日時" DataSetColumn="更新日時" />
              <Mapping SourceColumn="更新者CD" DataSetColumn="更新者CD" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="S製造工程TableAdapter" GeneratorDataComponentClassName="S製造工程TableAdapter" Name="S製造工程" UserDataComponentName="S製造工程TableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionStringBase (MySettings)" DbObjectName="SignProject.dbo.S製造工程" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [S製造工程] WHERE (([製造工程CD] = @Original_製造工程CD) AND ((@IsNull_製造工程名 = 1 AND [製造工程名] IS NULL) OR ([製造工程名] = @Original_製造工程名)) AND ((@IsNull_見積計算区分ID = 1 AND [見積計算区分ID] IS NULL) OR ([見積計算区分ID] = @Original_見積計算区分ID)) AND ((@IsNull_見積分類区分ID = 1 AND [見積分類区分ID] IS NULL) OR ([見積分類区分ID] = @Original_見積分類区分ID)) AND ((@IsNull_内製評価率 = 1 AND [内製評価率] IS NULL) OR ([内製評価率] = @Original_内製評価率)) AND ((@IsNull_外製評価率 = 1 AND [外製評価率] IS NULL) OR ([外製評価率] = @Original_外製評価率)))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_製造工程CD" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="製造工程CD" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_製造工程名" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="製造工程名" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_製造工程名" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="製造工程名" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_見積計算区分ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="見積計算区分ID" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_見積計算区分ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="見積計算区分ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_見積分類区分ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="見積分類区分ID" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_見積分類区分ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="見積分類区分ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_内製評価率" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="内製評価率" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_内製評価率" Precision="3" ProviderType="Decimal" Scale="0" Size="0" SourceColumn="内製評価率" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_外製評価率" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="外製評価率" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_外製評価率" Precision="3" ProviderType="Decimal" Scale="0" Size="0" SourceColumn="外製評価率" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [S製造工程] ([製造工程CD], [製造工程名], [見積計算区分ID], [見積分類区分ID], [内製評価率], [外製評価率]) VALUES (@製造工程CD, @製造工程名, @見積計算区分ID, @見積分類区分ID, @内製評価率, @外製評価率);
SELECT 製造工程CD, 製造工程名, 見積計算区分ID, 見積分類区分ID, 内製評価率, 外製評価率 FROM S製造工程 WHERE (製造工程CD = @製造工程CD)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@製造工程CD" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="製造工程CD" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@製造工程名" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="製造工程名" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@見積計算区分ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="見積計算区分ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@見積分類区分ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="見積分類区分ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@内製評価率" Precision="3" ProviderType="Decimal" Scale="0" Size="0" SourceColumn="内製評価率" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@外製評価率" Precision="3" ProviderType="Decimal" Scale="0" Size="0" SourceColumn="外製評価率" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT         S製造工程.*
FROM           S製造工程</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [S製造工程] SET [製造工程CD] = @製造工程CD, [製造工程名] = @製造工程名, [見積計算区分ID] = @見積計算区分ID, [見積分類区分ID] = @見積分類区分ID, [内製評価率] = @内製評価率, [外製評価率] = @外製評価率 WHERE (([製造工程CD] = @Original_製造工程CD) AND ((@IsNull_製造工程名 = 1 AND [製造工程名] IS NULL) OR ([製造工程名] = @Original_製造工程名)) AND ((@IsNull_見積計算区分ID = 1 AND [見積計算区分ID] IS NULL) OR ([見積計算区分ID] = @Original_見積計算区分ID)) AND ((@IsNull_見積分類区分ID = 1 AND [見積分類区分ID] IS NULL) OR ([見積分類区分ID] = @Original_見積分類区分ID)) AND ((@IsNull_内製評価率 = 1 AND [内製評価率] IS NULL) OR ([内製評価率] = @Original_内製評価率)) AND ((@IsNull_外製評価率 = 1 AND [外製評価率] IS NULL) OR ([外製評価率] = @Original_外製評価率)));
SELECT 製造工程CD, 製造工程名, 見積計算区分ID, 見積分類区分ID, 内製評価率, 外製評価率 FROM S製造工程 WHERE (製造工程CD = @製造工程CD)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@製造工程CD" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="製造工程CD" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@製造工程名" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="製造工程名" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@見積計算区分ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="見積計算区分ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@見積分類区分ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="見積分類区分ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@内製評価率" Precision="3" ProviderType="Decimal" Scale="0" Size="0" SourceColumn="内製評価率" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@外製評価率" Precision="3" ProviderType="Decimal" Scale="0" Size="0" SourceColumn="外製評価率" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_製造工程CD" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="製造工程CD" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_製造工程名" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="製造工程名" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_製造工程名" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="製造工程名" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_見積計算区分ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="見積計算区分ID" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_見積計算区分ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="見積計算区分ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_見積分類区分ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="見積分類区分ID" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_見積分類区分ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="見積分類区分ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_内製評価率" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="内製評価率" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_内製評価率" Precision="3" ProviderType="Decimal" Scale="0" Size="0" SourceColumn="内製評価率" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_外製評価率" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="外製評価率" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_外製評価率" Precision="3" ProviderType="Decimal" Scale="0" Size="0" SourceColumn="外製評価率" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="製造工程CD" DataSetColumn="製造工程CD" />
              <Mapping SourceColumn="製造工程名" DataSetColumn="製造工程名" />
              <Mapping SourceColumn="見積計算区分ID" DataSetColumn="見積計算区分ID" />
              <Mapping SourceColumn="見積分類区分ID" DataSetColumn="見積分類区分ID" />
              <Mapping SourceColumn="内製評価率" DataSetColumn="内製評価率" />
              <Mapping SourceColumn="外製評価率" DataSetColumn="外製評価率" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="S操作不可TableAdapter" GeneratorDataComponentClassName="S操作不可TableAdapter" Name="S操作不可" UserDataComponentName="S操作不可TableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionStringBase (MySettings)" DbObjectName="Taxiamaru0712.dbo.S操作不可" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [S操作不可] WHERE (([プロジェクトID] = @Original_プロジェクトID) AND ([ページID] = @Original_ページID) AND ([権限区分] = @Original_権限区分) AND ([アクションID] = @Original_アクションID))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_プロジェクトID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="プロジェクトID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_ページID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ページID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_権限区分" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="権限区分" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_アクションID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="アクションID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [S操作不可] ([プロジェクトID], [ページID], [権限区分], [アクションID]) VALUES (@プロジェクトID, @ページID, @権限区分, @アクションID);
SELECT プロジェクトID, ページID, 権限区分, アクションID FROM S操作不可 WHERE (アクションID = @アクションID) AND (プロジェクトID = @プロジェクトID) AND (ページID = @ページID) AND (権限区分 = @権限区分)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@プロジェクトID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="プロジェクトID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@ページID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ページID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@権限区分" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="権限区分" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@アクションID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="アクションID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT                      S操作不可.*
FROM                         S操作不可</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [S操作不可] SET [プロジェクトID] = @プロジェクトID, [ページID] = @ページID, [権限区分] = @権限区分, [アクションID] = @アクションID WHERE (([プロジェクトID] = @Original_プロジェクトID) AND ([ページID] = @Original_ページID) AND ([権限区分] = @Original_権限区分) AND ([アクションID] = @Original_アクションID));
SELECT プロジェクトID, ページID, 権限区分, アクションID FROM S操作不可 WHERE (アクションID = @アクションID) AND (プロジェクトID = @プロジェクトID) AND (ページID = @ページID) AND (権限区分 = @権限区分)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@プロジェクトID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="プロジェクトID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@ページID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ページID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@権限区分" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="権限区分" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@アクションID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="アクションID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_プロジェクトID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="プロジェクトID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_ページID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ページID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_権限区分" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="権限区分" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_アクションID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="アクションID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="プロジェクトID" DataSetColumn="プロジェクトID" />
              <Mapping SourceColumn="ページID" DataSetColumn="ページID" />
              <Mapping SourceColumn="権限区分" DataSetColumn="権限区分" />
              <Mapping SourceColumn="アクションID" DataSetColumn="アクションID" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="SメニューTableAdapter" GeneratorDataComponentClassName="SメニューTableAdapter" Name="Sメニュー" UserDataComponentName="SメニューTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionStringBase (MySettings)" DbObjectName="Taxi.dbo.Sメニュー" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [Sメニュー] WHERE (([表示順] = @Original_表示順) AND ((@IsNull_表示順親 = 1 AND [表示順親] IS NULL) OR ([表示順親] = @Original_表示順親)) AND ((@IsNull_イメージ = 1 AND [イメージ] IS NULL) OR ([イメージ] = @Original_イメージ)) AND ((@IsNull_ディレクトリ = 1 AND [ディレクトリ] IS NULL) OR ([ディレクトリ] = @Original_ディレクトリ)) AND ((@IsNull_ページID = 1 AND [ページID] IS NULL) OR ([ページID] = @Original_ページID)) AND ((@IsNull_タイトル = 1 AND [タイトル] IS NULL) OR ([タイトル] = @Original_タイトル)) AND ((@IsNull_ロール1 = 1 AND [ロール1] IS NULL) OR ([ロール1] = @Original_ロール1)) AND ((@IsNull_ロール2 = 1 AND [ロール2] IS NULL) OR ([ロール2] = @Original_ロール2)) AND ((@IsNull_ロール3 = 1 AND [ロール3] IS NULL) OR ([ロール3] = @Original_ロール3)) AND ((@IsNull_ロール4 = 1 AND [ロール4] IS NULL) OR ([ロール4] = @Original_ロール4)) AND ((@IsNull_ロール5 = 1 AND [ロール5] IS NULL) OR ([ロール5] = @Original_ロール5)) AND ((@IsNull_ロール6 = 1 AND [ロール6] IS NULL) OR ([ロール6] = @Original_ロール6)) AND ((@IsNull_ロール7 = 1 AND [ロール7] IS NULL) OR ([ロール7] = @Original_ロール7)) AND ((@IsNull_ロール8 = 1 AND [ロール8] IS NULL) OR ([ロール8] = @Original_ロール8)) AND ((@IsNull_ロール9 = 1 AND [ロール9] IS NULL) OR ([ロール9] = @Original_ロール9)) AND ((@IsNull_ロール10 = 1 AND [ロール10] IS NULL) OR ([ロール10] = @Original_ロール10)) AND ((@IsNull_ロール11 = 1 AND [ロール11] IS NULL) OR ([ロール11] = @Original_ロール11)) AND ((@IsNull_ロール12 = 1 AND [ロール12] IS NULL) OR ([ロール12] = @Original_ロール12)) AND ((@IsNull_ロール13 = 1 AND [ロール13] IS NULL) OR ([ロール13] = @Original_ロール13)) AND ((@IsNull_ロール14 = 1 AND [ロール14] IS NULL) OR ([ロール14] = @Original_ロール14)) AND ((@IsNull_ロール15 = 1 AND [ロール15] IS NULL) OR ([ロール15] = @Original_ロール15)) AND ((@IsNull_ロール16 = 1 AND [ロール16] IS NULL) OR ([ロール16] = @Original_ロール16)) AND ((@IsNull_ロール17 = 1 AND [ロール17] IS NULL) OR ([ロール17] = @Original_ロール17)) AND ((@IsNull_ロール18 = 1 AND [ロール18] IS NULL) OR ([ロール18] = @Original_ロール18)) AND ((@IsNull_ロール19 = 1 AND [ロール19] IS NULL) OR ([ロール19] = @Original_ロール19)) AND ((@IsNull_ロール20 = 1 AND [ロール20] IS NULL) OR ([ロール20] = @Original_ロール20)) AND ((@IsNull_セパレータ = 1 AND [セパレータ] IS NULL) OR ([セパレータ] = @Original_セパレータ)) AND ((@IsNull_パラメータ = 1 AND [パラメータ] IS NULL) OR ([パラメータ] = @Original_パラメータ)))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_表示順" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="表示順" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_表示順親" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="表示順親" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_表示順親" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="表示順親" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_イメージ" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="イメージ" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_イメージ" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="イメージ" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ディレクトリ" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ディレクトリ" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_ディレクトリ" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ディレクトリ" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ページID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ページID" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_ページID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ページID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_タイトル" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="タイトル" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_タイトル" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="タイトル" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ロール1" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ロール1" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_ロール1" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール1" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ロール2" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ロール2" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_ロール2" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール2" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ロール3" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ロール3" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_ロール3" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール3" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ロール4" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ロール4" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_ロール4" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール4" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ロール5" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ロール5" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_ロール5" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール5" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ロール6" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ロール6" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_ロール6" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール6" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ロール7" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ロール7" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_ロール7" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール7" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ロール8" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ロール8" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_ロール8" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール8" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ロール9" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ロール9" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_ロール9" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール9" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ロール10" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ロール10" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_ロール10" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール10" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ロール11" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ロール11" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_ロール11" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール11" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ロール12" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ロール12" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_ロール12" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール12" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ロール13" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ロール13" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_ロール13" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール13" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ロール14" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ロール14" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_ロール14" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール14" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ロール15" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ロール15" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_ロール15" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール15" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ロール16" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ロール16" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_ロール16" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール16" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ロール17" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ロール17" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_ロール17" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール17" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ロール18" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ロール18" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_ロール18" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール18" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ロール19" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ロール19" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_ロール19" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール19" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ロール20" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ロール20" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_ロール20" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール20" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_セパレータ" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="セパレータ" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_セパレータ" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="セパレータ" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_パラメータ" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="パラメータ" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_パラメータ" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="パラメータ" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [Sメニュー] ([表示順], [表示順親], [イメージ], [ディレクトリ], [ページID], [タイトル], [ロール1], [ロール2], [ロール3], [ロール4], [ロール5], [ロール6], [ロール7], [ロール8], [ロール9], [ロール10], [ロール11], [ロール12], [ロール13], [ロール14], [ロール15], [ロール16], [ロール17], [ロール18], [ロール19], [ロール20], [セパレータ], [パラメータ]) VALUES (@表示順, @表示順親, @イメージ, @ディレクトリ, @ページID, @タイトル, @ロール1, @ロール2, @ロール3, @ロール4, @ロール5, @ロール6, @ロール7, @ロール8, @ロール9, @ロール10, @ロール11, @ロール12, @ロール13, @ロール14, @ロール15, @ロール16, @ロール17, @ロール18, @ロール19, @ロール20, @セパレータ, @パラメータ);
SELECT 表示順, 表示順親, イメージ, ディレクトリ, ページID, タイトル, ロール1, ロール2, ロール3, ロール4, ロール5, ロール6, ロール7, ロール8, ロール9, ロール10, ロール11, ロール12, ロール13, ロール14, ロール15, ロール16, ロール17, ロール18, ロール19, ロール20, セパレータ, パラメータ FROM Sメニュー WHERE (表示順 = @表示順)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@表示順" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="表示順" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@表示順親" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="表示順親" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@イメージ" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="イメージ" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@ディレクトリ" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ディレクトリ" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@ページID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ページID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@タイトル" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="タイトル" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@ロール1" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール1" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@ロール2" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール2" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@ロール3" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール3" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@ロール4" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール4" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@ロール5" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール5" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@ロール6" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール6" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@ロール7" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール7" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@ロール8" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール8" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@ロール9" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール9" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@ロール10" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール10" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@ロール11" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール11" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@ロール12" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール12" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@ロール13" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール13" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@ロール14" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール14" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@ロール15" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール15" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@ロール16" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール16" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@ロール17" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール17" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@ロール18" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール18" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@ロール19" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール19" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@ロール20" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール20" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@セパレータ" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="セパレータ" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@パラメータ" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="パラメータ" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT                      Sメニュー.*
FROM                         Sメニュー</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [Sメニュー] SET [表示順] = @表示順, [表示順親] = @表示順親, [イメージ] = @イメージ, [ディレクトリ] = @ディレクトリ, [ページID] = @ページID, [タイトル] = @タイトル, [ロール1] = @ロール1, [ロール2] = @ロール2, [ロール3] = @ロール3, [ロール4] = @ロール4, [ロール5] = @ロール5, [ロール6] = @ロール6, [ロール7] = @ロール7, [ロール8] = @ロール8, [ロール9] = @ロール9, [ロール10] = @ロール10, [ロール11] = @ロール11, [ロール12] = @ロール12, [ロール13] = @ロール13, [ロール14] = @ロール14, [ロール15] = @ロール15, [ロール16] = @ロール16, [ロール17] = @ロール17, [ロール18] = @ロール18, [ロール19] = @ロール19, [ロール20] = @ロール20, [セパレータ] = @セパレータ, [パラメータ] = @パラメータ WHERE (([表示順] = @Original_表示順) AND ((@IsNull_表示順親 = 1 AND [表示順親] IS NULL) OR ([表示順親] = @Original_表示順親)) AND ((@IsNull_イメージ = 1 AND [イメージ] IS NULL) OR ([イメージ] = @Original_イメージ)) AND ((@IsNull_ディレクトリ = 1 AND [ディレクトリ] IS NULL) OR ([ディレクトリ] = @Original_ディレクトリ)) AND ((@IsNull_ページID = 1 AND [ページID] IS NULL) OR ([ページID] = @Original_ページID)) AND ((@IsNull_タイトル = 1 AND [タイトル] IS NULL) OR ([タイトル] = @Original_タイトル)) AND ((@IsNull_ロール1 = 1 AND [ロール1] IS NULL) OR ([ロール1] = @Original_ロール1)) AND ((@IsNull_ロール2 = 1 AND [ロール2] IS NULL) OR ([ロール2] = @Original_ロール2)) AND ((@IsNull_ロール3 = 1 AND [ロール3] IS NULL) OR ([ロール3] = @Original_ロール3)) AND ((@IsNull_ロール4 = 1 AND [ロール4] IS NULL) OR ([ロール4] = @Original_ロール4)) AND ((@IsNull_ロール5 = 1 AND [ロール5] IS NULL) OR ([ロール5] = @Original_ロール5)) AND ((@IsNull_ロール6 = 1 AND [ロール6] IS NULL) OR ([ロール6] = @Original_ロール6)) AND ((@IsNull_ロール7 = 1 AND [ロール7] IS NULL) OR ([ロール7] = @Original_ロール7)) AND ((@IsNull_ロール8 = 1 AND [ロール8] IS NULL) OR ([ロール8] = @Original_ロール8)) AND ((@IsNull_ロール9 = 1 AND [ロール9] IS NULL) OR ([ロール9] = @Original_ロール9)) AND ((@IsNull_ロール10 = 1 AND [ロール10] IS NULL) OR ([ロール10] = @Original_ロール10)) AND ((@IsNull_ロール11 = 1 AND [ロール11] IS NULL) OR ([ロール11] = @Original_ロール11)) AND ((@IsNull_ロール12 = 1 AND [ロール12] IS NULL) OR ([ロール12] = @Original_ロール12)) AND ((@IsNull_ロール13 = 1 AND [ロール13] IS NULL) OR ([ロール13] = @Original_ロール13)) AND ((@IsNull_ロール14 = 1 AND [ロール14] IS NULL) OR ([ロール14] = @Original_ロール14)) AND ((@IsNull_ロール15 = 1 AND [ロール15] IS NULL) OR ([ロール15] = @Original_ロール15)) AND ((@IsNull_ロール16 = 1 AND [ロール16] IS NULL) OR ([ロール16] = @Original_ロール16)) AND ((@IsNull_ロール17 = 1 AND [ロール17] IS NULL) OR ([ロール17] = @Original_ロール17)) AND ((@IsNull_ロール18 = 1 AND [ロール18] IS NULL) OR ([ロール18] = @Original_ロール18)) AND ((@IsNull_ロール19 = 1 AND [ロール19] IS NULL) OR ([ロール19] = @Original_ロール19)) AND ((@IsNull_ロール20 = 1 AND [ロール20] IS NULL) OR ([ロール20] = @Original_ロール20)) AND ((@IsNull_セパレータ = 1 AND [セパレータ] IS NULL) OR ([セパレータ] = @Original_セパレータ)) AND ((@IsNull_パラメータ = 1 AND [パラメータ] IS NULL) OR ([パラメータ] = @Original_パラメータ)));
SELECT 表示順, 表示順親, イメージ, ディレクトリ, ページID, タイトル, ロール1, ロール2, ロール3, ロール4, ロール5, ロール6, ロール7, ロール8, ロール9, ロール10, ロール11, ロール12, ロール13, ロール14, ロール15, ロール16, ロール17, ロール18, ロール19, ロール20, セパレータ, パラメータ FROM Sメニュー WHERE (表示順 = @表示順)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@表示順" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="表示順" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@表示順親" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="表示順親" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@イメージ" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="イメージ" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@ディレクトリ" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ディレクトリ" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@ページID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ページID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@タイトル" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="タイトル" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@ロール1" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール1" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@ロール2" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール2" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@ロール3" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール3" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@ロール4" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール4" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@ロール5" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール5" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@ロール6" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール6" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@ロール7" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール7" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@ロール8" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール8" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@ロール9" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール9" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@ロール10" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール10" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@ロール11" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール11" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@ロール12" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール12" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@ロール13" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール13" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@ロール14" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール14" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@ロール15" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール15" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@ロール16" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール16" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@ロール17" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール17" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@ロール18" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール18" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@ロール19" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール19" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@ロール20" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール20" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@セパレータ" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="セパレータ" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@パラメータ" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="パラメータ" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_表示順" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="表示順" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_表示順親" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="表示順親" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_表示順親" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="表示順親" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_イメージ" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="イメージ" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_イメージ" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="イメージ" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ディレクトリ" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ディレクトリ" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_ディレクトリ" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ディレクトリ" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ページID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ページID" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_ページID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ページID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_タイトル" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="タイトル" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_タイトル" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="タイトル" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ロール1" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ロール1" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_ロール1" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール1" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ロール2" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ロール2" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_ロール2" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール2" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ロール3" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ロール3" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_ロール3" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール3" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ロール4" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ロール4" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_ロール4" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール4" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ロール5" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ロール5" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_ロール5" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール5" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ロール6" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ロール6" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_ロール6" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール6" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ロール7" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ロール7" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_ロール7" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール7" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ロール8" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ロール8" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_ロール8" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール8" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ロール9" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ロール9" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_ロール9" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール9" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ロール10" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ロール10" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_ロール10" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール10" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ロール11" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ロール11" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_ロール11" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール11" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ロール12" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ロール12" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_ロール12" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール12" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ロール13" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ロール13" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_ロール13" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール13" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ロール14" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ロール14" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_ロール14" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール14" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ロール15" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ロール15" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_ロール15" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール15" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ロール16" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ロール16" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_ロール16" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール16" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ロール17" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ロール17" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_ロール17" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール17" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ロール18" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ロール18" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_ロール18" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール18" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ロール19" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ロール19" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_ロール19" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール19" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ロール20" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ロール20" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_ロール20" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ロール20" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_セパレータ" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="セパレータ" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_セパレータ" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="セパレータ" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_パラメータ" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="パラメータ" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_パラメータ" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="パラメータ" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="表示順" DataSetColumn="表示順" />
              <Mapping SourceColumn="表示順親" DataSetColumn="表示順親" />
              <Mapping SourceColumn="イメージ" DataSetColumn="イメージ" />
              <Mapping SourceColumn="ディレクトリ" DataSetColumn="ディレクトリ" />
              <Mapping SourceColumn="ページID" DataSetColumn="ページID" />
              <Mapping SourceColumn="タイトル" DataSetColumn="タイトル" />
              <Mapping SourceColumn="ロール1" DataSetColumn="ロール1" />
              <Mapping SourceColumn="ロール2" DataSetColumn="ロール2" />
              <Mapping SourceColumn="ロール3" DataSetColumn="ロール3" />
              <Mapping SourceColumn="ロール4" DataSetColumn="ロール4" />
              <Mapping SourceColumn="ロール5" DataSetColumn="ロール5" />
              <Mapping SourceColumn="ロール6" DataSetColumn="ロール6" />
              <Mapping SourceColumn="ロール7" DataSetColumn="ロール7" />
              <Mapping SourceColumn="ロール8" DataSetColumn="ロール8" />
              <Mapping SourceColumn="ロール9" DataSetColumn="ロール9" />
              <Mapping SourceColumn="ロール10" DataSetColumn="ロール10" />
              <Mapping SourceColumn="ロール11" DataSetColumn="ロール11" />
              <Mapping SourceColumn="ロール12" DataSetColumn="ロール12" />
              <Mapping SourceColumn="ロール13" DataSetColumn="ロール13" />
              <Mapping SourceColumn="ロール14" DataSetColumn="ロール14" />
              <Mapping SourceColumn="ロール15" DataSetColumn="ロール15" />
              <Mapping SourceColumn="ロール16" DataSetColumn="ロール16" />
              <Mapping SourceColumn="ロール17" DataSetColumn="ロール17" />
              <Mapping SourceColumn="ロール18" DataSetColumn="ロール18" />
              <Mapping SourceColumn="ロール19" DataSetColumn="ロール19" />
              <Mapping SourceColumn="ロール20" DataSetColumn="ロール20" />
              <Mapping SourceColumn="セパレータ" DataSetColumn="セパレータ" />
              <Mapping SourceColumn="パラメータ" DataSetColumn="パラメータ" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="SシステムTableAdapter" GeneratorDataComponentClassName="SシステムTableAdapter" Name="Sシステム" UserDataComponentName="SシステムTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionStringBase (MySettings)" DbObjectName="TaxiSystem.dbo.Sシステム" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [Sシステム] WHERE (([ID] = @Original_ID) AND ((@IsNull_Dirテンポラリ = 1 AND [Dirテンポラリ] IS NULL) OR ([Dirテンポラリ] = @Original_Dirテンポラリ)) AND ((@IsNull_Dirテンプレート = 1 AND [Dirテンプレート] IS NULL) OR ([Dirテンプレート] = @Original_Dirテンプレート)) AND ((@IsNull_Dirハンコ = 1 AND [Dirハンコ] IS NULL) OR ([Dirハンコ] = @Original_Dirハンコ)) AND ((@IsNull_決算月 = 1 AND [決算月] IS NULL) OR ([決算月] = @Original_決算月)) AND ((@IsNull_実働時間 = 1 AND [実働時間] IS NULL) OR ([実働時間] = @Original_実働時間)) AND ((@IsNull_日当 = 1 AND [日当] IS NULL) OR ([日当] = @Original_日当)) AND ((@IsNull_トップ達成率 = 1 AND [トップ達成率] IS NULL) OR ([トップ達成率] = @Original_トップ達成率)) AND ((@IsNull_トップ見積期限日数 = 1 AND [トップ見積期限日数] IS NULL) OR ([トップ見積期限日数] = @Original_トップ見積期限日数)) AND ((@IsNull_トップ不一致月数 = 1 AND [トップ不一致月数] IS NULL) OR ([トップ不一致月数] = @Original_トップ不一致月数)) AND ((@IsNull_有給付与開始月数 = 1 AND [有給付与開始月数] IS NULL) OR ([有給付与開始月数] = @Original_有給付与開始月数)) AND ((@IsNull_税率 = 1 AND [税率] IS NULL) OR ([税率] = @Original_税率)))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int64" Direction="Input" ParameterName="@Original_ID" Precision="0" ProviderType="BigInt" Scale="0" Size="0" SourceColumn="ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Dirテンポラリ" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Dirテンポラリ" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Dirテンポラリ" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Dirテンポラリ" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Dirテンプレート" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Dirテンプレート" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Dirテンプレート" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Dirテンプレート" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Dirハンコ" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Dirハンコ" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Dirハンコ" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Dirハンコ" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_決算月" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="決算月" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_決算月" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="決算月" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_実働時間" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="実働時間" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_実働時間" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="実働時間" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_日当" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="日当" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_日当" Precision="6" ProviderType="Decimal" Scale="0" Size="0" SourceColumn="日当" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_トップ達成率" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="トップ達成率" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_トップ達成率" Precision="6" ProviderType="Decimal" Scale="0" Size="0" SourceColumn="トップ達成率" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_トップ見積期限日数" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="トップ見積期限日数" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_トップ見積期限日数" Precision="6" ProviderType="Decimal" Scale="0" Size="0" SourceColumn="トップ見積期限日数" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_トップ不一致月数" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="トップ不一致月数" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_トップ不一致月数" Precision="6" ProviderType="Decimal" Scale="0" Size="0" SourceColumn="トップ不一致月数" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_有給付与開始月数" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="有給付与開始月数" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_有給付与開始月数" Precision="2" ProviderType="Decimal" Scale="0" Size="0" SourceColumn="有給付与開始月数" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_税率" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="税率" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_税率" Precision="3" ProviderType="Decimal" Scale="1" Size="0" SourceColumn="税率" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [Sシステム] ([Dirテンポラリ], [Dirテンプレート], [Dirハンコ], [決算月], [実働時間], [日当], [トップ達成率], [トップ見積期限日数], [トップ不一致月数], [有給付与開始月数], [税率]) VALUES (@Dirテンポラリ, @Dirテンプレート, @Dirハンコ, @決算月, @実働時間, @日当, @トップ達成率, @トップ見積期限日数, @トップ不一致月数, @有給付与開始月数, @税率);
SELECT ID, Dirテンポラリ, Dirテンプレート, Dirハンコ, 決算月, 実働時間, 日当, トップ達成率, トップ見積期限日数, トップ不一致月数, 有給付与開始月数, 税率 FROM Sシステム WHERE (ID = SCOPE_IDENTITY())</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Dirテンポラリ" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Dirテンポラリ" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Dirテンプレート" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Dirテンプレート" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Dirハンコ" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Dirハンコ" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@決算月" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="決算月" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@実働時間" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="実働時間" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@日当" Precision="6" ProviderType="Decimal" Scale="0" Size="0" SourceColumn="日当" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@トップ達成率" Precision="6" ProviderType="Decimal" Scale="0" Size="0" SourceColumn="トップ達成率" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@トップ見積期限日数" Precision="6" ProviderType="Decimal" Scale="0" Size="0" SourceColumn="トップ見積期限日数" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@トップ不一致月数" Precision="6" ProviderType="Decimal" Scale="0" Size="0" SourceColumn="トップ不一致月数" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@有給付与開始月数" Precision="2" ProviderType="Decimal" Scale="0" Size="0" SourceColumn="有給付与開始月数" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@税率" Precision="3" ProviderType="Decimal" Scale="1" Size="0" SourceColumn="税率" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT                      Sシステム.*
FROM                         Sシステム</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [Sシステム] SET [Dirテンポラリ] = @Dirテンポラリ, [Dirテンプレート] = @Dirテンプレート, [Dirハンコ] = @Dirハンコ, [決算月] = @決算月, [実働時間] = @実働時間, [日当] = @日当, [トップ達成率] = @トップ達成率, [トップ見積期限日数] = @トップ見積期限日数, [トップ不一致月数] = @トップ不一致月数, [有給付与開始月数] = @有給付与開始月数, [税率] = @税率 WHERE (([ID] = @Original_ID) AND ((@IsNull_Dirテンポラリ = 1 AND [Dirテンポラリ] IS NULL) OR ([Dirテンポラリ] = @Original_Dirテンポラリ)) AND ((@IsNull_Dirテンプレート = 1 AND [Dirテンプレート] IS NULL) OR ([Dirテンプレート] = @Original_Dirテンプレート)) AND ((@IsNull_Dirハンコ = 1 AND [Dirハンコ] IS NULL) OR ([Dirハンコ] = @Original_Dirハンコ)) AND ((@IsNull_決算月 = 1 AND [決算月] IS NULL) OR ([決算月] = @Original_決算月)) AND ((@IsNull_実働時間 = 1 AND [実働時間] IS NULL) OR ([実働時間] = @Original_実働時間)) AND ((@IsNull_日当 = 1 AND [日当] IS NULL) OR ([日当] = @Original_日当)) AND ((@IsNull_トップ達成率 = 1 AND [トップ達成率] IS NULL) OR ([トップ達成率] = @Original_トップ達成率)) AND ((@IsNull_トップ見積期限日数 = 1 AND [トップ見積期限日数] IS NULL) OR ([トップ見積期限日数] = @Original_トップ見積期限日数)) AND ((@IsNull_トップ不一致月数 = 1 AND [トップ不一致月数] IS NULL) OR ([トップ不一致月数] = @Original_トップ不一致月数)) AND ((@IsNull_有給付与開始月数 = 1 AND [有給付与開始月数] IS NULL) OR ([有給付与開始月数] = @Original_有給付与開始月数)) AND ((@IsNull_税率 = 1 AND [税率] IS NULL) OR ([税率] = @Original_税率)));
SELECT ID, Dirテンポラリ, Dirテンプレート, Dirハンコ, 決算月, 実働時間, 日当, トップ達成率, トップ見積期限日数, トップ不一致月数, 有給付与開始月数, 税率 FROM Sシステム WHERE (ID = @ID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Dirテンポラリ" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Dirテンポラリ" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Dirテンプレート" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Dirテンプレート" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Dirハンコ" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Dirハンコ" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@決算月" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="決算月" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@実働時間" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="実働時間" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@日当" Precision="6" ProviderType="Decimal" Scale="0" Size="0" SourceColumn="日当" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@トップ達成率" Precision="6" ProviderType="Decimal" Scale="0" Size="0" SourceColumn="トップ達成率" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@トップ見積期限日数" Precision="6" ProviderType="Decimal" Scale="0" Size="0" SourceColumn="トップ見積期限日数" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@トップ不一致月数" Precision="6" ProviderType="Decimal" Scale="0" Size="0" SourceColumn="トップ不一致月数" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@有給付与開始月数" Precision="2" ProviderType="Decimal" Scale="0" Size="0" SourceColumn="有給付与開始月数" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@税率" Precision="3" ProviderType="Decimal" Scale="1" Size="0" SourceColumn="税率" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int64" Direction="Input" ParameterName="@Original_ID" Precision="0" ProviderType="BigInt" Scale="0" Size="0" SourceColumn="ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Dirテンポラリ" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Dirテンポラリ" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Dirテンポラリ" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Dirテンポラリ" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Dirテンプレート" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Dirテンプレート" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Dirテンプレート" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Dirテンプレート" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Dirハンコ" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Dirハンコ" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Dirハンコ" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Dirハンコ" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_決算月" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="決算月" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_決算月" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="決算月" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_実働時間" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="実働時間" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_実働時間" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="実働時間" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_日当" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="日当" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_日当" Precision="6" ProviderType="Decimal" Scale="0" Size="0" SourceColumn="日当" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_トップ達成率" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="トップ達成率" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_トップ達成率" Precision="6" ProviderType="Decimal" Scale="0" Size="0" SourceColumn="トップ達成率" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_トップ見積期限日数" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="トップ見積期限日数" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_トップ見積期限日数" Precision="6" ProviderType="Decimal" Scale="0" Size="0" SourceColumn="トップ見積期限日数" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_トップ不一致月数" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="トップ不一致月数" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_トップ不一致月数" Precision="6" ProviderType="Decimal" Scale="0" Size="0" SourceColumn="トップ不一致月数" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_有給付与開始月数" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="有給付与開始月数" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_有給付与開始月数" Precision="2" ProviderType="Decimal" Scale="0" Size="0" SourceColumn="有給付与開始月数" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_税率" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="税率" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_税率" Precision="3" ProviderType="Decimal" Scale="1" Size="0" SourceColumn="税率" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ID" ColumnName="ID" DataSourceName="TaxiSystem.dbo.Sシステム" DataTypeServer="bigint" DbType="Int64" Direction="Input" ParameterName="@ID" Precision="0" ProviderType="BigInt" Scale="0" Size="8" SourceColumn="ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="ID" DataSetColumn="ID" />
              <Mapping SourceColumn="Dirテンポラリ" DataSetColumn="Dirテンポラリ" />
              <Mapping SourceColumn="Dirテンプレート" DataSetColumn="Dirテンプレート" />
              <Mapping SourceColumn="Dirハンコ" DataSetColumn="Dirハンコ" />
              <Mapping SourceColumn="決算月" DataSetColumn="決算月" />
              <Mapping SourceColumn="実働時間" DataSetColumn="実働時間" />
              <Mapping SourceColumn="日当" DataSetColumn="日当" />
              <Mapping SourceColumn="トップ達成率" DataSetColumn="トップ達成率" />
              <Mapping SourceColumn="トップ見積期限日数" DataSetColumn="トップ見積期限日数" />
              <Mapping SourceColumn="トップ不一致月数" DataSetColumn="トップ不一致月数" />
              <Mapping SourceColumn="有給付与開始月数" DataSetColumn="有給付与開始月数" />
              <Mapping SourceColumn="税率" DataSetColumn="税率" />
            </Mappings>
            <Sources />
          </TableAdapter>
        </Tables>
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="DataSetSystem" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:EnableTableAdapterManager="true" msprop:Generator_DataSetName="DataSetSystem" msprop:Generator_UserDSName="DataSetSystem">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="S画面状態" msprop:Generator_TableClassName="S画面状態DataTable" msprop:Generator_RowEvArgName="S画面状態RowChangeEvent" msprop:Generator_TableVarName="tableS画面状態" msprop:Generator_TablePropName="S画面状態" msprop:Generator_RowDeletingName="S画面状態RowDeleting" msprop:Generator_RowChangingName="S画面状態RowChanging" msprop:Generator_RowEvHandlerName="S画面状態RowChangeEventHandler" msprop:Generator_RowDeletedName="S画面状態RowDeleted" msprop:Generator_RowChangedName="S画面状態RowChanged" msprop:Generator_UserTableName="S画面状態" msprop:Generator_RowClassName="S画面状態Row">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="画面ID" msprop:Generator_ColumnVarNameInTable="column画面ID" msprop:Generator_ColumnPropNameInRow="画面ID" msprop:Generator_ColumnPropNameInTable="画面IDColumn" msprop:Generator_UserColumnName="画面ID">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="255" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="共有単位" msprop:Generator_ColumnVarNameInTable="column共有単位" msprop:Generator_ColumnPropNameInRow="共有単位" msprop:Generator_ColumnPropNameInTable="共有単位Column" msprop:Generator_UserColumnName="共有単位">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="255" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="コントロールID" msprop:Generator_ColumnVarNameInTable="columnコントロールID" msprop:Generator_ColumnPropNameInRow="コントロールID" msprop:Generator_ColumnPropNameInTable="コントロールIDColumn" msprop:Generator_UserColumnName="コントロールID">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="255" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="値" msprop:Generator_ColumnVarNameInTable="column値" msprop:Generator_ColumnPropNameInRow="値" msprop:Generator_ColumnPropNameInTable="値Column" msprop:Generator_UserColumnName="値" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2000" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="更新日時" msprop:Generator_ColumnVarNameInTable="column更新日時" msprop:Generator_ColumnPropNameInRow="更新日時" msprop:Generator_ColumnPropNameInTable="更新日時Column" msprop:Generator_UserColumnName="更新日時" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="19" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="更新者CD" msprop:Generator_ColumnVarNameInTable="column更新者CD" msprop:Generator_ColumnPropNameInRow="更新者CD" msprop:Generator_ColumnPropNameInTable="更新者CDColumn" msprop:Generator_UserColumnName="更新者CD" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="30" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="S製造工程" msprop:Generator_TableClassName="S製造工程DataTable" msprop:Generator_RowEvArgName="S製造工程RowChangeEvent" msprop:Generator_TableVarName="tableS製造工程" msprop:Generator_TablePropName="S製造工程" msprop:Generator_RowDeletingName="S製造工程RowDeleting" msprop:Generator_RowChangingName="S製造工程RowChanging" msprop:Generator_RowEvHandlerName="S製造工程RowChangeEventHandler" msprop:Generator_RowDeletedName="S製造工程RowDeleted" msprop:Generator_RowChangedName="S製造工程RowChanged" msprop:Generator_UserTableName="S製造工程" msprop:Generator_RowClassName="S製造工程Row">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="製造工程CD" msprop:Generator_ColumnVarNameInTable="column製造工程CD" msprop:Generator_ColumnPropNameInRow="製造工程CD" msprop:Generator_ColumnPropNameInTable="製造工程CDColumn" msprop:Generator_UserColumnName="製造工程CD">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="製造工程名" msprop:Generator_ColumnVarNameInTable="column製造工程名" msprop:Generator_ColumnPropNameInRow="製造工程名" msprop:Generator_ColumnPropNameInTable="製造工程名Column" msprop:Generator_UserColumnName="製造工程名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="見積計算区分ID" msprop:Generator_ColumnVarNameInTable="column見積計算区分ID" msprop:Generator_ColumnPropNameInRow="見積計算区分ID" msprop:Generator_ColumnPropNameInTable="見積計算区分IDColumn" msprop:Generator_UserColumnName="見積計算区分ID" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="見積分類区分ID" msprop:Generator_ColumnVarNameInTable="column見積分類区分ID" msprop:Generator_ColumnPropNameInRow="見積分類区分ID" msprop:Generator_ColumnPropNameInTable="見積分類区分IDColumn" msprop:Generator_UserColumnName="見積分類区分ID" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="内製評価率" msprop:Generator_ColumnVarNameInTable="column内製評価率" msprop:Generator_ColumnPropNameInRow="内製評価率" msprop:Generator_ColumnPropNameInTable="内製評価率Column" msprop:Generator_UserColumnName="内製評価率" type="xs:decimal" minOccurs="0" />
              <xs:element name="外製評価率" msprop:Generator_ColumnVarNameInTable="column外製評価率" msprop:Generator_ColumnPropNameInRow="外製評価率" msprop:Generator_ColumnPropNameInTable="外製評価率Column" msprop:Generator_UserColumnName="外製評価率" type="xs:decimal" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="S操作不可" msprop:Generator_TableClassName="S操作不可DataTable" msprop:Generator_RowEvArgName="S操作不可RowChangeEvent" msprop:Generator_TableVarName="tableS操作不可" msprop:Generator_TablePropName="S操作不可" msprop:Generator_RowDeletingName="S操作不可RowDeleting" msprop:Generator_RowChangingName="S操作不可RowChanging" msprop:Generator_RowEvHandlerName="S操作不可RowChangeEventHandler" msprop:Generator_RowDeletedName="S操作不可RowDeleted" msprop:Generator_RowChangedName="S操作不可RowChanged" msprop:Generator_UserTableName="S操作不可" msprop:Generator_RowClassName="S操作不可Row">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="プロジェクトID" msprop:Generator_ColumnVarNameInTable="columnプロジェクトID" msprop:Generator_ColumnPropNameInRow="プロジェクトID" msprop:Generator_ColumnPropNameInTable="プロジェクトIDColumn" msprop:Generator_UserColumnName="プロジェクトID">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="255" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ページID" msprop:Generator_ColumnVarNameInTable="columnページID" msprop:Generator_ColumnPropNameInRow="ページID" msprop:Generator_ColumnPropNameInTable="ページIDColumn" msprop:Generator_UserColumnName="ページID">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="255" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="権限区分" msprop:Generator_ColumnVarNameInTable="column権限区分" msprop:Generator_ColumnPropNameInRow="権限区分" msprop:Generator_ColumnPropNameInTable="権限区分Column" msprop:Generator_UserColumnName="権限区分">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="アクションID" msprop:Generator_ColumnVarNameInTable="columnアクションID" msprop:Generator_ColumnPropNameInRow="アクションID" msprop:Generator_ColumnPropNameInTable="アクションIDColumn" msprop:Generator_UserColumnName="アクションID">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Sメニュー" msprop:Generator_TableClassName="SメニューDataTable" msprop:Generator_TableVarName="tableSメニュー" msprop:Generator_RowChangedName="SメニューRowChanged" msprop:Generator_TablePropName="Sメニュー" msprop:Generator_RowDeletingName="SメニューRowDeleting" msprop:Generator_RowChangingName="SメニューRowChanging" msprop:Generator_RowEvHandlerName="SメニューRowChangeEventHandler" msprop:Generator_RowDeletedName="SメニューRowDeleted" msprop:Generator_RowClassName="SメニューRow" msprop:Generator_UserTableName="Sメニュー" msprop:Generator_RowEvArgName="SメニューRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="表示順" msprop:Generator_ColumnVarNameInTable="column表示順" msprop:Generator_ColumnPropNameInRow="表示順" msprop:Generator_ColumnPropNameInTable="表示順Column" msprop:Generator_UserColumnName="表示順" type="xs:int" />
              <xs:element name="表示順親" msprop:Generator_ColumnVarNameInTable="column表示順親" msprop:Generator_ColumnPropNameInRow="表示順親" msprop:Generator_ColumnPropNameInTable="表示順親Column" msprop:Generator_UserColumnName="表示順親" type="xs:int" minOccurs="0" />
              <xs:element name="イメージ" msprop:Generator_ColumnVarNameInTable="columnイメージ" msprop:Generator_ColumnPropNameInRow="イメージ" msprop:Generator_ColumnPropNameInTable="イメージColumn" msprop:Generator_UserColumnName="イメージ" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="255" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ディレクトリ" msprop:Generator_ColumnVarNameInTable="columnディレクトリ" msprop:Generator_ColumnPropNameInRow="ディレクトリ" msprop:Generator_ColumnPropNameInTable="ディレクトリColumn" msprop:Generator_UserColumnName="ディレクトリ" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="255" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ページID" msprop:Generator_ColumnVarNameInTable="columnページID" msprop:Generator_ColumnPropNameInRow="ページID" msprop:Generator_ColumnPropNameInTable="ページIDColumn" msprop:Generator_UserColumnName="ページID" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="255" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="タイトル" msprop:Generator_ColumnVarNameInTable="columnタイトル" msprop:Generator_ColumnPropNameInRow="タイトル" msprop:Generator_ColumnPropNameInTable="タイトルColumn" msprop:Generator_UserColumnName="タイトル" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ロール1" msprop:Generator_ColumnVarNameInTable="columnロール1" msprop:Generator_ColumnPropNameInRow="ロール1" msprop:Generator_ColumnPropNameInTable="ロール1Column" msprop:Generator_UserColumnName="ロール1" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ロール2" msprop:Generator_ColumnVarNameInTable="columnロール2" msprop:Generator_ColumnPropNameInRow="ロール2" msprop:Generator_ColumnPropNameInTable="ロール2Column" msprop:Generator_UserColumnName="ロール2" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ロール3" msprop:Generator_ColumnVarNameInTable="columnロール3" msprop:Generator_ColumnPropNameInRow="ロール3" msprop:Generator_ColumnPropNameInTable="ロール3Column" msprop:Generator_UserColumnName="ロール3" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ロール4" msprop:Generator_ColumnVarNameInTable="columnロール4" msprop:Generator_ColumnPropNameInRow="ロール4" msprop:Generator_ColumnPropNameInTable="ロール4Column" msprop:Generator_UserColumnName="ロール4" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ロール5" msprop:Generator_ColumnVarNameInTable="columnロール5" msprop:Generator_ColumnPropNameInRow="ロール5" msprop:Generator_ColumnPropNameInTable="ロール5Column" msprop:Generator_UserColumnName="ロール5" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ロール6" msprop:Generator_ColumnVarNameInTable="columnロール6" msprop:Generator_ColumnPropNameInRow="ロール6" msprop:Generator_ColumnPropNameInTable="ロール6Column" msprop:Generator_UserColumnName="ロール6" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ロール7" msprop:Generator_ColumnVarNameInTable="columnロール7" msprop:Generator_ColumnPropNameInRow="ロール7" msprop:Generator_ColumnPropNameInTable="ロール7Column" msprop:Generator_UserColumnName="ロール7" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ロール8" msprop:Generator_ColumnVarNameInTable="columnロール8" msprop:Generator_ColumnPropNameInRow="ロール8" msprop:Generator_ColumnPropNameInTable="ロール8Column" msprop:Generator_UserColumnName="ロール8" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ロール9" msprop:Generator_ColumnVarNameInTable="columnロール9" msprop:Generator_ColumnPropNameInRow="ロール9" msprop:Generator_ColumnPropNameInTable="ロール9Column" msprop:Generator_UserColumnName="ロール9" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ロール10" msprop:Generator_ColumnVarNameInTable="columnロール10" msprop:Generator_ColumnPropNameInRow="ロール10" msprop:Generator_ColumnPropNameInTable="ロール10Column" msprop:Generator_UserColumnName="ロール10" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ロール11" msprop:Generator_ColumnVarNameInTable="columnロール11" msprop:Generator_ColumnPropNameInRow="ロール11" msprop:Generator_ColumnPropNameInTable="ロール11Column" msprop:Generator_UserColumnName="ロール11" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ロール12" msprop:Generator_ColumnVarNameInTable="columnロール12" msprop:Generator_ColumnPropNameInRow="ロール12" msprop:Generator_ColumnPropNameInTable="ロール12Column" msprop:Generator_UserColumnName="ロール12" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ロール13" msprop:Generator_ColumnVarNameInTable="columnロール13" msprop:Generator_ColumnPropNameInRow="ロール13" msprop:Generator_ColumnPropNameInTable="ロール13Column" msprop:Generator_UserColumnName="ロール13" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ロール14" msprop:Generator_ColumnVarNameInTable="columnロール14" msprop:Generator_ColumnPropNameInRow="ロール14" msprop:Generator_ColumnPropNameInTable="ロール14Column" msprop:Generator_UserColumnName="ロール14" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ロール15" msprop:Generator_ColumnVarNameInTable="columnロール15" msprop:Generator_ColumnPropNameInRow="ロール15" msprop:Generator_ColumnPropNameInTable="ロール15Column" msprop:Generator_UserColumnName="ロール15" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ロール16" msprop:Generator_ColumnVarNameInTable="columnロール16" msprop:Generator_ColumnPropNameInRow="ロール16" msprop:Generator_ColumnPropNameInTable="ロール16Column" msprop:Generator_UserColumnName="ロール16" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ロール17" msprop:Generator_ColumnVarNameInTable="columnロール17" msprop:Generator_ColumnPropNameInRow="ロール17" msprop:Generator_ColumnPropNameInTable="ロール17Column" msprop:Generator_UserColumnName="ロール17" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ロール18" msprop:Generator_ColumnVarNameInTable="columnロール18" msprop:Generator_ColumnPropNameInRow="ロール18" msprop:Generator_ColumnPropNameInTable="ロール18Column" msprop:Generator_UserColumnName="ロール18" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ロール19" msprop:Generator_ColumnVarNameInTable="columnロール19" msprop:Generator_ColumnPropNameInRow="ロール19" msprop:Generator_ColumnPropNameInTable="ロール19Column" msprop:Generator_UserColumnName="ロール19" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ロール20" msprop:Generator_ColumnVarNameInTable="columnロール20" msprop:Generator_ColumnPropNameInRow="ロール20" msprop:Generator_ColumnPropNameInTable="ロール20Column" msprop:Generator_UserColumnName="ロール20" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="セパレータ" msprop:Generator_ColumnVarNameInTable="columnセパレータ" msprop:Generator_ColumnPropNameInRow="セパレータ" msprop:Generator_ColumnPropNameInTable="セパレータColumn" msprop:Generator_UserColumnName="セパレータ" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="パラメータ" msprop:Generator_ColumnVarNameInTable="columnパラメータ" msprop:Generator_ColumnPropNameInRow="パラメータ" msprop:Generator_ColumnPropNameInTable="パラメータColumn" msprop:Generator_UserColumnName="パラメータ" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="255" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Sシステム" msprop:Generator_TableClassName="SシステムDataTable" msprop:Generator_TableVarName="tableSシステム" msprop:Generator_TablePropName="Sシステム" msprop:Generator_RowDeletingName="SシステムRowDeleting" msprop:Generator_RowChangingName="SシステムRowChanging" msprop:Generator_RowEvHandlerName="SシステムRowChangeEventHandler" msprop:Generator_RowDeletedName="SシステムRowDeleted" msprop:Generator_UserTableName="Sシステム" msprop:Generator_RowChangedName="SシステムRowChanged" msprop:Generator_RowEvArgName="SシステムRowChangeEvent" msprop:Generator_RowClassName="SシステムRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ID" msdata:ReadOnly="true" msdata:AutoIncrement="true" msdata:AutoIncrementSeed="-1" msdata:AutoIncrementStep="-1" msprop:Generator_ColumnVarNameInTable="columnID" msprop:Generator_ColumnPropNameInRow="ID" msprop:Generator_ColumnPropNameInTable="IDColumn" msprop:Generator_UserColumnName="ID" type="xs:long" />
              <xs:element name="Dirテンポラリ" msprop:Generator_ColumnVarNameInTable="columnDirテンポラリ" msprop:Generator_ColumnPropNameInRow="Dirテンポラリ" msprop:Generator_ColumnPropNameInTable="DirテンポラリColumn" msprop:Generator_UserColumnName="Dirテンポラリ" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="255" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Dirテンプレート" msprop:Generator_ColumnVarNameInTable="columnDirテンプレート" msprop:Generator_ColumnPropNameInRow="Dirテンプレート" msprop:Generator_ColumnPropNameInTable="DirテンプレートColumn" msprop:Generator_UserColumnName="Dirテンプレート" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="255" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Dirハンコ" msprop:Generator_ColumnVarNameInTable="columnDirハンコ" msprop:Generator_ColumnPropNameInRow="Dirハンコ" msprop:Generator_ColumnPropNameInTable="DirハンコColumn" msprop:Generator_UserColumnName="Dirハンコ" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="255" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="決算月" msprop:Generator_ColumnVarNameInTable="column決算月" msprop:Generator_ColumnPropNameInRow="決算月" msprop:Generator_ColumnPropNameInTable="決算月Column" msprop:Generator_UserColumnName="決算月" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="実働時間" msprop:Generator_ColumnVarNameInTable="column実働時間" msprop:Generator_ColumnPropNameInRow="実働時間" msprop:Generator_ColumnPropNameInTable="実働時間Column" msprop:Generator_UserColumnName="実働時間" type="xs:int" minOccurs="0" />
              <xs:element name="日当" msprop:Generator_ColumnVarNameInTable="column日当" msprop:Generator_ColumnPropNameInRow="日当" msprop:Generator_ColumnPropNameInTable="日当Column" msprop:Generator_UserColumnName="日当" type="xs:decimal" minOccurs="0" />
              <xs:element name="トップ達成率" msprop:Generator_ColumnVarNameInTable="columnトップ達成率" msprop:Generator_ColumnPropNameInRow="トップ達成率" msprop:Generator_ColumnPropNameInTable="トップ達成率Column" msprop:Generator_UserColumnName="トップ達成率" type="xs:decimal" minOccurs="0" />
              <xs:element name="トップ見積期限日数" msprop:Generator_ColumnVarNameInTable="columnトップ見積期限日数" msprop:Generator_ColumnPropNameInRow="トップ見積期限日数" msprop:Generator_ColumnPropNameInTable="トップ見積期限日数Column" msprop:Generator_UserColumnName="トップ見積期限日数" type="xs:decimal" minOccurs="0" />
              <xs:element name="トップ不一致月数" msprop:Generator_ColumnVarNameInTable="columnトップ不一致月数" msprop:Generator_ColumnPropNameInRow="トップ不一致月数" msprop:Generator_ColumnPropNameInTable="トップ不一致月数Column" msprop:Generator_UserColumnName="トップ不一致月数" type="xs:decimal" minOccurs="0" />
              <xs:element name="有給付与開始月数" msprop:Generator_ColumnVarNameInTable="column有給付与開始月数" msprop:Generator_ColumnPropNameInRow="有給付与開始月数" msprop:Generator_ColumnPropNameInTable="有給付与開始月数Column" msprop:Generator_UserColumnName="有給付与開始月数" type="xs:decimal" minOccurs="0" />
              <xs:element name="税率" msprop:Generator_ColumnVarNameInTable="column税率" msprop:Generator_ColumnPropNameInRow="税率" msprop:Generator_ColumnPropNameInTable="税率Column" msprop:Generator_UserColumnName="税率" type="xs:decimal" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
    <xs:unique name="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:S画面状態" />
      <xs:field xpath="mstns:画面ID" />
      <xs:field xpath="mstns:共有単位" />
      <xs:field xpath="mstns:コントロールID" />
    </xs:unique>
    <xs:unique name="S製造工程_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:S製造工程" />
      <xs:field xpath="mstns:製造工程CD" />
    </xs:unique>
    <xs:unique name="S操作不可_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:S操作不可" />
      <xs:field xpath="mstns:プロジェクトID" />
      <xs:field xpath="mstns:ページID" />
      <xs:field xpath="mstns:権限区分" />
      <xs:field xpath="mstns:アクションID" />
    </xs:unique>
    <xs:unique name="Sメニュー_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Sメニュー" />
      <xs:field xpath="mstns:表示順" />
    </xs:unique>
    <xs:unique name="Sシステム_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Sシステム" />
      <xs:field xpath="mstns:ID" />
    </xs:unique>
  </xs:element>
</xs:schema>