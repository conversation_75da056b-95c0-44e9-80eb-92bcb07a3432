﻿Namespace Frame.Other
    Partial Public Class TicketList
        Inherits NodeCore.Common.Frame

#Region "ﾌｨｰﾙﾄﾞ定義"
        Public Class Header
            ''' ----------------------------------------------------------------------------------------
            ''' <SUMMARY>列項目(基本)</SUMMARY>
            ''' ----------------------------------------------------------------------------------------
            Public グループCD As BaseCore.Common.Field.ItemData
            Public 個社CD As BaseCore.Common.Field.ItemData
            Public 子会社CD As BaseCore.Common.Field.ItemData
            Public 日付F As BaseCore.Common.Field.ItemData
            Public 日付T As BaseCore.Common.Field.ItemData
            Public 得意先CD As BaseCore.Common.Field.ItemData

            '''========================================================================================
            ''' <SUMMARY>ｺﾝｽﾄﾗｸﾀ</SUMMARY>
            '''========================================================================================
            Public Sub New(ByVal Config As NodeCore.Common.Config)
                Dim tbl As New NodeDatabase.DataSetView.V0チケット一覧DataTable

                '----------------------------------------------------------------------
                ' ｲﾝｽﾀﾝｽ(基本) 
                '----------------------------------------------------------------------
                Me.グループCD = New BaseCore.Common.Field.ItemData(tbl.グループCDColumn)
                Me.個社CD = New BaseCore.Common.Field.ItemData(tbl.個社CDColumn)
                Me.子会社CD = New BaseCore.Common.Field.ItemData(tbl.子会社CDColumn)
                Me.日付F = New BaseCore.Common.Field.ItemData(tbl.日付Column)
                Me.日付T = New BaseCore.Common.Field.ItemData(tbl.日付Column)
                Me.得意先CD = New BaseCore.Common.Field.ItemData(tbl.得意先CDColumn)

            End Sub
        End Class
#End Region

#Region "変数定義"
        Public Class Paramater
            Public Header As Header
        End Class

        '''========================================================================================
        ''' <summary>ﾍｯﾀﾞ</summary>
        '''========================================================================================
        Private _Fields As New Paramater
        Public Property Fields() As Paramater
            Get
                Return _Fields
            End Get

            Set(ByVal value As Paramater)
                _Fields = value
            End Set
        End Property
#End Region

#Region "ｺﾝｽﾄﾗｸﾀ"
        '''========================================================================================
        ''' <SUMMARY>ｺﾝｽﾄﾗｸﾀ</SUMMARY>
        ''' <PARAM name="Security">ｾｷｭﾘﾃｨ ｸﾗｽ</PARAM>
        ''' <PARAM name="Config">ｺﾝﾌｨｸﾞ ｸﾗｽ</PARAM>
        ''' <PARAM name="PagePath">ﾍﾟｰｼﾞﾊﾟｽ</PARAM>
        '''========================================================================================
        Public Sub New(ByVal Security As NodeCore.Common.Security, ByVal Config As NodeCore.Common.Config, ByVal PagePath As String)
            '----------------------------------------------------------------------
            ' ｾｷｭﾘﾃｨ 設定
            '----------------------------------------------------------------------
            MyBase.New(Security, Config, PagePath)

            Me.Fields.Header = New Header(Config)
        End Sub
#End Region

#Region "ﾍｯﾀﾞ"
#Region "Clear"
        '''========================================================================================
        ''' <SUMMARY>ｸﾘｱ</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Clear_Header(Optional ByVal KeyClear As Boolean = False) As Boolean
            '----------------------------------------------------------------------
            ' ｷｰ部ｸﾘｱ
            '----------------------------------------------------------------------
            If KeyClear Then
                Me.Fields.Header.グループCD.Value = Nothing
                Me.Fields.Header.個社CD.Value = Nothing
                Me.Fields.Header.子会社CD.Value = Nothing
            End If

            '----------------------------------------------------------------------
            ' ﾃﾞｰﾀ部ｸﾘｱ
            '----------------------------------------------------------------------

            Me.Fields.Header.日付F.Value = Nothing
            Me.Fields.Header.日付T.Value = Nothing
            Me.Fields.Header.得意先CD.Value = Nothing

            '----------------------------------------------------------------------
            ' ﾃﾞｰﾀ部ｸﾘｱ
            '----------------------------------------------------------------------
            'Me.Read()

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "MakeDataTable"
        '''========================================================================================
        ''' <SUMMARY>ﾃﾞｰﾀﾃｰﾌﾞﾙ生成</SUMMARY>
        ''' <RETURNS>ﾃﾞｰﾀﾃｰﾌﾞﾙ</RETURNS>
        '''========================================================================================
        Public Function MakeDataTable() As DataTable
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetViewTableAdapters.V0チケット一覧TableAdapter
            Dim tbl As New NodeDatabase.DataSetView.V0チケット一覧DataTable
            Dim qry As New Collection

            '----------------------------------------------------------------------
            ' 事前準備
            '----------------------------------------------------------------------

            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, Me.Fields.Header.グループCD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, Me.Fields.Header.個社CD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.子会社CDColumn.ColumnName, Me.Fields.Header.子会社CD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.日付Column.ColumnName, Me.Fields.Header.日付F.Value, BaseDatabase.Contents.Compare.GreaterEqual))
            qry.Add(New BaseDatabase.Condition(tbl.日付Column.ColumnName, Me.Fields.Header.日付T.Value, BaseDatabase.Contents.Compare.LessEqual))
            qry.Add(New BaseDatabase.Condition(tbl.得意先CDColumn.ColumnName, Me.Fields.Header.得意先CD.Value, BaseDatabase.Contents.Compare.Equal, BaseDatabase.Contents.EmptyHandle.Skip))

            tbl = ada.SelectByCommon(qry)


            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return tbl
        End Function
#End Region

#Region "Execute1:Excel"
        '''========================================================================================
        ''' <SUMMARY>実行</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Execute1_Header() As Boolean
            '----------------------------------------------------------------------
            ' ﾃﾞｰﾀ抽出
            '----------------------------------------------------------------------
            Dim tbl As NodeDatabase.DataSetView.V0チケット一覧DataTable = Me.MakeDataTable()

            '----------------------------------------------------------------------
            ' 必要ｶﾗﾑ以外を削除
            '----------------------------------------------------------------------
            Dim colRefresh As New BaseCore.Common.DataTable.ColumnRefresh

            colRefresh.Add(tbl.グループCDColumn.ColumnName, "グループCD")
            colRefresh.Add(tbl.個社CDColumn.ColumnName, "個社CD")
            colRefresh.Add(tbl.子会社CDColumn.ColumnName, "子会社CD")
            colRefresh.Add(tbl.IDColumn.ColumnName, "ID")
            colRefresh.Add(tbl.日付Column.ColumnName, "日付")
            colRefresh.Add(tbl.得意先CDColumn.ColumnName, "取引先CD")
            colRefresh.Add(tbl.得意先名Column.ColumnName, "取引先名")
            colRefresh.Add(tbl.無線番号Column.ColumnName, "無線番号")
            colRefresh.Add(tbl.チケット番号Column.ColumnName, "チケット番号")
            colRefresh.Add(tbl.料金Column.ColumnName, "料金")
            colRefresh.Add(tbl.備考Column.ColumnName, "備考")
            colRefresh.Execute(tbl)

            '----------------------------------------------------------------------
            ' ﾃﾞｰﾀ加工(前)
            '----------------------------------------------------------------------
            tbl.TableName = "チケット一覧"

            '----------------------------------------------------------------------
            ' ｴｸｽﾎﾟｰﾄ
            '----------------------------------------------------------------------
            Dim Excel As New BaseCore.Common.Excel
            If Not Excel.ExcelExport(tbl, Me.Path1) Then
                Me.LastError = Excel.LastError
                Return False
            End If

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "Execute:Delete"
        '''========================================================================================
        ''' <SUMMARY>実行</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Execute_Header() As Boolean

            Dim strNow As String = Format(Now, "yyyy/MM/dd HH:mm:ss")
            Dim ada As New NodeDatabase.DataSetTranTableAdapters.TチケットTableAdapter
            Dim tbl As New NodeDatabase.DataSetTran.TチケットDataTable
            Dim qry As New Collection
            '----------------------------------------------------------------------
            ' 削除処理
            '----------------------------------------------------------------------
            If Me.CheckMark1 Is Nothing Then
                Me.LastError = "削除するデータを選択してください。"
                Return False
            End If

            If Not Me.CheckMark1.Count > 0 Then
                Me.LastError = "削除するデータを選択してください。"
                Return False
            End If

            For Each Key As String In Me.CheckMark1
                '----------------------------------------------------------------------
                ' 訂正
                '----------------------------------------------------------------------
                qry.Clear()
                qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, Me.Fields.Header.グループCD.Value, BaseDatabase.Contents.Compare.Equal))
                qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, Me.Fields.Header.個社CD.Value, BaseDatabase.Contents.Compare.Equal))
                qry.Add(New BaseDatabase.Condition(tbl.子会社CDColumn.ColumnName, Me.Fields.Header.子会社CD.Value, BaseDatabase.Contents.Compare.Equal))
                'TODO key add to qry

                Try
                    'tbl(0).Item(tbl.削除フラグ.ColumnName) = 1
                    ada.Update(tbl)

                Catch ex As Exception
                    MyBase.LastError = "以下のエラーのため、更新できませんでした。" & vbCrLf & ex.Message
                    Return False
                End Try
            Next
            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#End Region


    End Class
End Namespace
