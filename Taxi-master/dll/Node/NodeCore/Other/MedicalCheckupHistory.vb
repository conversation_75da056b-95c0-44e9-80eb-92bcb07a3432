﻿Imports System.Globalization

Namespace Frame.Other
    Partial Public Class MedicalCheckupHistory

        Inherits NodeCore.Common.Frame

#Region "ｺﾝｽﾀﾝﾄ"
        Public Class ConstantExcel
            Enum DAYS
                月
                火
                水
                木
                金
                土
                日
            End Enum 'DAYS

            Public Class Header
                Public Const SheetName = "健康診断受診履歴データ"
                Public Const ColTitle = "受診日"
                Public Const ColTitleMemo = "メモ"
                Public Const RowTop = 1  '2行目スタート

                Public Class 列数
                    Public Const 社員番号 = 0
                    Public Const 社員名 = 1
                    Public Const 勤務区分 = 2
                    Public Const 受診日 = 3
                End Class

            End Class

        End Class


#End Region

#Region "ﾌｨｰﾙﾄﾞ定義"
        Public Class Header
            ''' ----------------------------------------------------------------------------------------
            ''' <SUMMARY>列項目(基本)</SUMMARY>
            ''' ----------------------------------------------------------------------------------------
            Public グループCD As BaseCore.Common.Field.ItemData
            Public 個社CD As BaseCore.Common.Field.ItemData
            Public 子会社CD As BaseCore.Common.Field.ItemData
            Public 健康診断日F As BaseCore.Common.Field.ItemData
            Public 健康診断日T As BaseCore.Common.Field.ItemData
            Public 社員番号F As BaseCore.Common.Field.ItemData
            Public 社員番号T As BaseCore.Common.Field.ItemData
            Public 勤務区分 As BaseCore.Common.Field.ItemData


            '''========================================================================================
            ''' <SUMMARY>ｺﾝｽﾄﾗｸﾀ</SUMMARY>
            '''========================================================================================
            Public Sub New(ByVal Config As NodeCore.Common.Config)
                Dim tbl As New NodeDatabase.DataSetFunc.F0健康診断受診履歴照会DataTable

                '----------------------------------------------------------------------
                ' ｲﾝｽﾀﾝｽ(基本) 
                '----------------------------------------------------------------------
                Me.グループCD = New BaseCore.Common.Field.ItemData(tbl.グループCDColumn)
                Me.個社CD = New BaseCore.Common.Field.ItemData(tbl.個社CDColumn)
                Me.子会社CD = New BaseCore.Common.Field.ItemData(tbl.子会社CDColumn)
                Me.健康診断日F = New BaseCore.Common.Field.ItemData(tbl.日付Column)
                Me.健康診断日T = New BaseCore.Common.Field.ItemData(tbl.日付Column)
                Me.社員番号F = New BaseCore.Common.Field.ItemData(tbl.社員NOColumn)
                Me.社員番号T = New BaseCore.Common.Field.ItemData(tbl.社員NOColumn)
                Me.勤務区分 = New BaseCore.Common.Field.ItemData(tbl.勤務区分Column)

            End Sub
        End Class
#End Region

#Region "変数定義"
        Public Class Paramater
            Public Header As Header
        End Class

        '''========================================================================================
        ''' <summary>ﾍｯﾀﾞ</summary>
        '''========================================================================================
        Private _Fields As New Paramater
        Public Property Fields() As Paramater
            Get
                Return _Fields
            End Get

            Set(ByVal value As Paramater)
                _Fields = value
            End Set
        End Property
#End Region

#Region "ｺﾝｽﾄﾗｸﾀ"
        '''========================================================================================
        ''' <SUMMARY>ｺﾝｽﾄﾗｸﾀ</SUMMARY>
        ''' <PARAM name="Security">ｾｷｭﾘﾃｨ ｸﾗｽ</PARAM>
        ''' <PARAM name="Config">ｺﾝﾌｨｸﾞ ｸﾗｽ</PARAM>
        ''' <PARAM name="PagePath">ﾍﾟｰｼﾞﾊﾟｽ</PARAM>
        '''========================================================================================
        Public Sub New(ByVal Security As NodeCore.Common.Security, ByVal Config As NodeCore.Common.Config, ByVal PagePath As String)
            '----------------------------------------------------------------------
            ' ｾｷｭﾘﾃｨ 設定
            '----------------------------------------------------------------------
            MyBase.New(Security, Config, PagePath)

            Me.Fields.Header = New Header(Config)
        End Sub
#End Region

#Region "ﾍｯﾀﾞ"

#Region "Clear"
        '''========================================================================================
        ''' <SUMMARY>ｸﾘｱ</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Clear_Header(Optional ByVal KeyClear As Boolean = False) As Boolean
            '----------------------------------------------------------------------
            ' ｷｰ部ｸﾘｱ
            '----------------------------------------------------------------------
            If KeyClear Then
                Me.Fields.Header.グループCD.Value = Nothing
                Me.Fields.Header.個社CD.Value = Nothing
                Me.Fields.Header.子会社CD.Value = Nothing
            End If

            '----------------------------------------------------------------------
            ' ﾃﾞｰﾀ部ｸﾘｱ
            '----------------------------------------------------------------------

            Me.Fields.Header.健康診断日F.Value = Nothing
            Me.Fields.Header.健康診断日T.Value = Nothing
            Me.Fields.Header.社員番号F.Value = Nothing
            Me.Fields.Header.社員番号T.Value = Nothing
            Me.Fields.Header.勤務区分.Value = Nothing

            '----------------------------------------------------------------------
            ' ﾃﾞｰﾀ部ｸﾘｱ
            '----------------------------------------------------------------------
            'Me.Read()

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "MakeDataTable"
        '''========================================================================================
        ''' <SUMMARY>ﾃﾞｰﾀﾃｰﾌﾞﾙ生成</SUMMARY>
        ''' <RETURNS>ﾃﾞｰﾀﾃｰﾌﾞﾙ</RETURNS>
        '''========================================================================================
        Public Function MakeDataTable() As DataTable
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetFuncTableAdapters.F0健康診断受診履歴照会TableAdapter
            Dim tbl As New NodeDatabase.DataSetFunc.F0健康診断受診履歴照会DataTable
            Dim qry As New Collection

            '----------------------------------------------------------------------
            ' 事前準備
            '----------------------------------------------------------------------

            qry.Clear()
            qry.Add(New BaseDatabase.Condition(NodeDatabase.DataSetFuncTableAdapters.F0健康診断受診履歴照会TableAdapter.Contents.グループCD, Me.Security.グループCD, BaseDatabase.Contents.Compare.Parameter))
            qry.Add(New BaseDatabase.Condition(NodeDatabase.DataSetFuncTableAdapters.F0健康診断受診履歴照会TableAdapter.Contents.個社CD, Me.Security.個社CD, BaseDatabase.Contents.Compare.Parameter))
            qry.Add(New BaseDatabase.Condition(NodeDatabase.DataSetFuncTableAdapters.F0健康診断受診履歴照会TableAdapter.Contents.子会社CD, Me.Security.子会社CD, BaseDatabase.Contents.Compare.Parameter))
            qry.Add(New BaseDatabase.Condition(NodeDatabase.DataSetFuncTableAdapters.F0健康診断受診履歴照会TableAdapter.Contents.日付F, Me.Fields.Header.健康診断日F.Value, BaseDatabase.Contents.Compare.Parameter))
            qry.Add(New BaseDatabase.Condition(NodeDatabase.DataSetFuncTableAdapters.F0健康診断受診履歴照会TableAdapter.Contents.日付T, Me.Fields.Header.健康診断日T.Value, BaseDatabase.Contents.Compare.Parameter))
            qry.Add(New BaseDatabase.Condition(NodeDatabase.DataSetFuncTableAdapters.F0健康診断受診履歴照会TableAdapter.Contents.社員NOF, Me.Fields.Header.社員番号F.Value, BaseDatabase.Contents.Compare.Parameter))
            qry.Add(New BaseDatabase.Condition(NodeDatabase.DataSetFuncTableAdapters.F0健康診断受診履歴照会TableAdapter.Contents.社員NOT, Me.Fields.Header.社員番号T.Value, BaseDatabase.Contents.Compare.Parameter))
            qry.Add(New BaseDatabase.Condition(NodeDatabase.DataSetFuncTableAdapters.F0健康診断受診履歴照会TableAdapter.Contents.区分, Me.Fields.Header.勤務区分.Value, BaseDatabase.Contents.Compare.Parameter))

            tbl = ada.SelectByCommon(qry)


            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return tbl
        End Function
#End Region

#Region "Execute"
        '''========================================================================================
        ''' <SUMMARY>実行</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Execute_Header() As Boolean

            Me.LastError = Nothing
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim xls As New BaseCore.Common.Excel

            Try
                '----------------------------------------------------------------------
                ' ﾃﾞｰﾀ抽出
                '----------------------------------------------------------------------
                Dim tbl As DataTable = Me.MakeDataTable()

                ''----------------------------------------------------------------------
                '' ｾﾞﾛ件
                ''----------------------------------------------------------------------
                If tbl.Rows.Count = 0 Then
                    Me.LastError = "指定された条件に合致するデータはありません。"
                    Return False
                End If

                '----------------------------------------------------------------------
                ' ｵｰﾌﾟﾝ
                '----------------------------------------------------------------------
                If Not xls.Open(Me.Path1) Then
                    Me.LastError = "テンプレートが見つかりません。(" & Me.Path1 & ")"
                    Return False
                End If

                '------------------------------------------------------------------------
                ' 作成
                '------------------------------------------------------------------------
                xls.SheetSelect(ConstantExcel.Header.SheetName)       'ｱｸﾃｨﾌﾞｼｰﾄ

                If Not MakeExcelData(tbl, xls) Then
                    MyBase.LastError = Me.LastError
                    Return False
                End If

                '----------------------------------------------------------------------
                ' 先頭へｶｰｿﾙ移動
                '----------------------------------------------------------------------
                xls.CellGet(0, 0)

                xls.Close(Me.Path1)
            Catch ex As Exception
                Me.LastError = ex.Message
                Return False
            Finally
            End Try


            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "エクセルデータ作成"
        '''========================================================================================
        ''' <SUMMARY>抽出したﾃｰﾌﾞﾙに編集を加える</SUMMARY>   
        '''========================================================================================
        Public Function MakeExcelData(ByRef tbl As NodeDatabase.DataSetFunc.F0健康診断受診履歴照会DataTable, ByRef xls As BaseCore.Common.Excel) As Boolean
            '------------------------------------------------------------------------
            ' 変数定義
            '------------------------------------------------------------------------
            Dim intDtlCnt As Integer = ConstantExcel.Header.RowTop

            Try

                xls.InsertRow(ConstantExcel.Header.SheetName, intDtlCnt + 2, tbl.Rows.Count - 1)

                For Each row As NodeDatabase.DataSetFunc.F0健康診断受診履歴照会Row In tbl.Rows

                    Dim intColCnt As Integer = 3
                    Dim i As Integer = 0

                    '------------------------------------------------------------------------
                    ' 値設定 明細
                    '-----------------------------------------------------------------------
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.社員番号, BaseCore.Common.Text.Nz(row.Item(tbl.社員NOColumn.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.社員名, BaseCore.Common.Text.Nz(row.Item(tbl.社員名Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.勤務区分, BaseCore.Common.Text.Nz(row.Item(tbl.勤務区分Column.ColumnName), ""))

                    Dim Str1 As String() = Split(BaseCore.Common.Text.Nz(row.Item(tbl.メモColumn.ColumnName), ""), ",")
                    For Each str As String In Split(BaseCore.Common.Text.Nz(row.Item(tbl.日付Column.ColumnName), ""), ",")
                        xls.CellSetValue(0, intColCnt, ConstantExcel.Header.ColTitle)
                        xls.CellSetValue(intDtlCnt, intColCnt, str)
                        xls.CellSetValue(0, intColCnt + 1, ConstantExcel.Header.ColTitleMemo)
                        xls.CellSetValue(intDtlCnt, intColCnt + 1, Str1(i))
                        intColCnt += 2
                        i += 1
                    Next


                    intDtlCnt += 1
                Next

            Catch ex As Exception
                Me.LastError = ex.Message
                Return False
            Finally
            End Try
            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region


#End Region


    End Class
End Namespace
