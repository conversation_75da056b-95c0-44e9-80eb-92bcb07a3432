﻿Imports System.Globalization

Namespace Frame.Monthly
    Partial Public Class UriageShyokai
        Inherits NodeCore.Common.Frame



#Region "ﾌｨｰﾙﾄﾞ定義"
        Public Class Header
            ''' ----------------------------------------------------------------------------------------
            ''' <SUMMARY>列項目(基本)</SUMMARY>
            ''' ----------------------------------------------------------------------------------------
            Public グループCD As BaseCore.Common.Field.ItemData
            Public 個社CD As BaseCore.Common.Field.ItemData
            Public 子会社CD As BaseCore.Common.Field.ItemData
            Public 日報日付F As BaseCore.Common.Field.ItemData
            Public 日報日付T As BaseCore.Common.Field.ItemData
            Public 車番 As BaseCore.Common.Field.ItemData
            Public 社員NO As BaseCore.Common.Field.ItemData
            Public 勤務区分 As BaseCore.Common.Field.ItemData
            Public 集計項目 As BaseCore.Common.Field.ItemData

            '''========================================================================================
            ''' <SUMMARY>ｺﾝｽﾄﾗｸﾀ</SUMMARY>
            '''========================================================================================
            Public Sub New(ByVal Config As NodeCore.Common.Config)
                Dim tbl As New NodeDatabase.DataSetFunc.F0売上照会DataTable

                '----------------------------------------------------------------------
                ' ｲﾝｽﾀﾝｽ(基本) 
                '----------------------------------------------------------------------
                Me.グループCD = New BaseCore.Common.Field.ItemData(tbl.グループCDColumn)
                Me.個社CD = New BaseCore.Common.Field.ItemData(tbl.個社CDColumn)
                Me.子会社CD = New BaseCore.Common.Field.ItemData(tbl.子会社CDColumn)
                Me.日報日付F = New BaseCore.Common.Field.ItemData(tbl.日報日付Column)
                Me.日報日付T = New BaseCore.Common.Field.ItemData(tbl.日報日付Column)
                Me.車番 = New BaseCore.Common.Field.ItemData(tbl.車両番号Column)
                Me.社員NO = New BaseCore.Common.Field.ItemData(tbl.社員NOColumn)
                Me.勤務区分 = New BaseCore.Common.Field.ItemData(tbl.勤務区分Column)
                Me.集計項目 = New BaseCore.Common.Field.ItemData("optMode", 1)

            End Sub
        End Class
#End Region

#Region "変数定義"
        Public Class Paramater
            Public Header As Header
        End Class

        '''========================================================================================
        ''' <summary>ﾍｯﾀﾞ</summary>
        '''========================================================================================
        Private _Fields As New Paramater
        Public Property Fields() As Paramater
            Get
                Return _Fields
            End Get

            Set(ByVal value As Paramater)
                _Fields = value
            End Set
        End Property
#End Region

#Region "ｺﾝｽﾄﾗｸﾀ"
        '''========================================================================================
        ''' <SUMMARY>ｺﾝｽﾄﾗｸﾀ</SUMMARY>
        ''' <PARAM name="Security">ｾｷｭﾘﾃｨ ｸﾗｽ</PARAM>
        ''' <PARAM name="Config">ｺﾝﾌｨｸﾞ ｸﾗｽ</PARAM>
        ''' <PARAM name="PagePath">ﾍﾟｰｼﾞﾊﾟｽ</PARAM>
        '''========================================================================================
        Public Sub New(ByVal Security As NodeCore.Common.Security, ByVal Config As NodeCore.Common.Config, ByVal PagePath As String)
            '----------------------------------------------------------------------
            ' ｾｷｭﾘﾃｨ 設定
            '----------------------------------------------------------------------
            MyBase.New(Security, Config, PagePath)

            Me.Fields.Header = New Header(Config)
        End Sub
#End Region

#Region "ﾍｯﾀﾞ"

#Region "Clear"
        '''========================================================================================
        ''' <SUMMARY>ｸﾘｱ</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Clear_Header(Optional ByVal KeyClear As Boolean = False) As Boolean
            '----------------------------------------------------------------------
            ' ｷｰ部ｸﾘｱ
            '----------------------------------------------------------------------
            If KeyClear Then
            End If

            '----------------------------------------------------------------------
            ' ﾃﾞｰﾀ部ｸﾘｱ
            '----------------------------------------------------------------------


            Me.Fields.Header.個社CD.Value = Nothing
            Me.Fields.Header.子会社CD.Value = Nothing
            Me.Fields.Header.日報日付F.Value = Nothing
            Me.Fields.Header.日報日付T.Value = Nothing
            Me.Fields.Header.車番.Value = Nothing
            Me.Fields.Header.社員NO.Value = Nothing
            Me.Fields.Header.勤務区分.Value = Nothing
            Me.Fields.Header.集計項目.Value = Nothing

            '----------------------------------------------------------------------
            ' ﾃﾞｰﾀ部ｸﾘｱ
            '----------------------------------------------------------------------
            'Me.Read()

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region



#End Region


    End Class
End Namespace
