﻿Imports System.Globalization

Namespace Frame.Monthly
    Partial Public Class ShiftYoteiJisseki
        Inherits NodeCore.Common.Frame

        Public intervalDays As Integer = 30
        Public sheetNameYotei As String = "予定表"
        Public sheetNameJisseki As String = "実績表"

#Region "ﾌｨｰﾙﾄﾞ定義"
        Public Class Header
            ''' ----------------------------------------------------------------------------------------
            ''' <SUMMARY>列項目(基本)</SUMMARY>
            ''' ----------------------------------------------------------------------------------------
            Public グループCD As BaseCore.Common.Field.ItemData
            Public 個社CD As BaseCore.Common.Field.ItemData
            Public 子会社CD As BaseCore.Common.Field.ItemData
            Public 日付 As BaseCore.Common.Field.ItemData
            Public 日付T As BaseCore.Common.Field.ItemData
            Public データ(31) As String
            Public 登録日時 As BaseCore.Common.Field.ItemData
            Public 登録ユーザーCD As BaseCore.Common.Field.ItemData
            Public 登録ユーザー名 As BaseCore.Common.Field.ItemData
            Public 更新日時 As BaseCore.Common.Field.ItemData
            Public 更新ユーザーCD As BaseCore.Common.Field.ItemData
            Public 更新ユーザー名 As BaseCore.Common.Field.ItemData

            '''========================================================================================
            ''' <SUMMARY>ｺﾝｽﾄﾗｸﾀ</SUMMARY>
            '''========================================================================================
            Public Sub New(ByVal Config As NodeCore.Common.Config)
                Dim tbl As New NodeDatabase.DataSetTran.TシフトDataTable

                '----------------------------------------------------------------------
                ' ｲﾝｽﾀﾝｽ(基本) 
                '----------------------------------------------------------------------
                Me.グループCD = New BaseCore.Common.Field.ItemData(tbl.グループCDColumn)
                Me.個社CD = New BaseCore.Common.Field.ItemData(tbl.個社CDColumn)
                Me.子会社CD = New BaseCore.Common.Field.ItemData(tbl.子会社CDColumn)
                Me.日付 = New BaseCore.Common.Field.ItemData(tbl.日付Column)
                Me.日付T = New BaseCore.Common.Field.ItemData(tbl.日付Column)
                Me.登録日時 = New BaseCore.Common.Field.ItemData(tbl.登録年月日Column)
                Me.登録ユーザーCD = New BaseCore.Common.Field.ItemData(tbl.登録ユーザーCDColumn)
                Me.登録ユーザー名 = New BaseCore.Common.Field.ItemData(tbl.登録ユーザー名Column)
                Me.更新日時 = New BaseCore.Common.Field.ItemData(tbl.更新年月日Column)
                Me.更新ユーザーCD = New BaseCore.Common.Field.ItemData(tbl.更新ユーザーCDColumn)
                Me.更新ユーザー名 = New BaseCore.Common.Field.ItemData(tbl.更新ユーザー名Column)
                Me.データ = New String(31) {}
                For i As Integer = 0 To 30
                    Me.データ(i) = ""
                Next
            End Sub
        End Class
#End Region

#Region "明細(ﾃﾞｰﾀ項目)1"
        Public Class Detail1
            Public 乗務員CD As BaseCore.Common.Field.ItemData
            Public 乗務員名 As BaseCore.Common.Field.ItemData
            Public シフト区分 As BaseCore.Common.Field.ItemData
            Public シフト区分名 As BaseCore.Common.Field.ItemData
            Public 日付データ() As BaseCore.Common.Field.ItemData
            Public 予定データ() As BaseCore.Common.Field.ItemData
            Public 実績データ() As BaseCore.Common.Field.ItemData
            Public 隔勤 As BaseCore.Common.Field.ItemData
            Public 昼勤 As BaseCore.Common.Field.ItemData
            Public 夜勤 As BaseCore.Common.Field.ItemData
            Public 行合計 As BaseCore.Common.Field.ItemData

            '''========================================================================================
            ''' <SUMMARY>ｺﾝｽﾄﾗｸﾀ</SUMMARY>
            '''========================================================================================
            Public Sub New(ByVal Config As NodeCore.Common.Config)
                Dim tbl As New NodeDatabase.DataSetFunc.F0乗務員予定実績DataTable

                Me.乗務員CD = New BaseCore.Common.Field.ItemData(tbl.乗務員CDColumn, TypeCode.String)
                Me.乗務員名 = New BaseCore.Common.Field.ItemData(tbl.乗務員名Column, TypeCode.String)
                Me.シフト区分 = New BaseCore.Common.Field.ItemData(tbl.区分IDColumn, TypeCode.String)
                Me.シフト区分名 = New BaseCore.Common.Field.ItemData(tbl.区分名Column, TypeCode.String)
                Me.隔勤 = New BaseCore.Common.Field.ItemData("隔勤", TypeCode.UInt64)
                Me.昼勤 = New BaseCore.Common.Field.ItemData("昼勤", TypeCode.UInt64)
                Me.夜勤 = New BaseCore.Common.Field.ItemData("夜勤", TypeCode.UInt64)
                Me.行合計 = New BaseCore.Common.Field.ItemData("行合計", TypeCode.UInt64)

                Me.日付データ = New BaseCore.Common.Field.ItemData(31) {}
                For i As Integer = 0 To 30
                    Me.日付データ(i) = New BaseCore.Common.Field.ItemData("日付", TypeCode.String)
                Next
                Me.予定データ = New BaseCore.Common.Field.ItemData(31) {}
                For i As Integer = 0 To 30
                    Me.予定データ(i) = New BaseCore.Common.Field.ItemData("予定", TypeCode.String)
                Next
                Me.実績データ = New BaseCore.Common.Field.ItemData(31) {}
                For i As Integer = 0 To 30
                    Me.実績データ(i) = New BaseCore.Common.Field.ItemData("実績", TypeCode.String)
                Next


            End Sub
        End Class
#End Region

#Region "明細(ﾃﾞｰﾀ項目)2"
        Public Class Detail2
            Public 乗務員CD As BaseCore.Common.Field.ItemData
            Public 乗務員名 As BaseCore.Common.Field.ItemData
            Public シフト区分 As BaseCore.Common.Field.ItemData
            Public シフト区分名 As BaseCore.Common.Field.ItemData
            Public 日付データ() As BaseCore.Common.Field.ItemData
            Public 予定データ() As BaseCore.Common.Field.ItemData
            Public 実績データ() As BaseCore.Common.Field.ItemData
            Public 隔勤 As BaseCore.Common.Field.ItemData
            Public 昼勤 As BaseCore.Common.Field.ItemData
            Public 夜勤 As BaseCore.Common.Field.ItemData
            Public 行合計 As BaseCore.Common.Field.ItemData
            Public 公出 As BaseCore.Common.Field.ItemData
            Public 変更 As BaseCore.Common.Field.ItemData
            Public 欠勤 As BaseCore.Common.Field.ItemData
            Public 有給 As BaseCore.Common.Field.ItemData

            '''========================================================================================
            ''' <SUMMARY>ｺﾝｽﾄﾗｸﾀ</SUMMARY>
            '''========================================================================================
            Public Sub New(ByVal Config As NodeCore.Common.Config)
                Dim tbl As New NodeDatabase.DataSetFunc.F0乗務員予定実績DataTable

                Me.乗務員CD = New BaseCore.Common.Field.ItemData(tbl.乗務員CDColumn, TypeCode.String)
                Me.乗務員名 = New BaseCore.Common.Field.ItemData(tbl.乗務員名Column, TypeCode.String)
                Me.シフト区分 = New BaseCore.Common.Field.ItemData(tbl.区分IDColumn, TypeCode.String)
                Me.シフト区分名 = New BaseCore.Common.Field.ItemData(tbl.区分名Column, TypeCode.String)
                Me.隔勤 = New BaseCore.Common.Field.ItemData("隔勤", TypeCode.UInt64)
                Me.昼勤 = New BaseCore.Common.Field.ItemData("昼勤", TypeCode.UInt64)
                Me.夜勤 = New BaseCore.Common.Field.ItemData("夜勤", TypeCode.UInt64)
                Me.行合計 = New BaseCore.Common.Field.ItemData("行合計", TypeCode.UInt64)
                Me.公出 = New BaseCore.Common.Field.ItemData("公出", TypeCode.UInt64)
                Me.変更 = New BaseCore.Common.Field.ItemData("変更", TypeCode.UInt64)
                Me.欠勤 = New BaseCore.Common.Field.ItemData("欠勤", TypeCode.UInt64)
                Me.有給 = New BaseCore.Common.Field.ItemData("有給", TypeCode.UInt64)

                Me.日付データ = New BaseCore.Common.Field.ItemData(31) {}
                For i As Integer = 0 To 30
                    Me.日付データ(i) = New BaseCore.Common.Field.ItemData("日付", TypeCode.String)
                Next
                Me.予定データ = New BaseCore.Common.Field.ItemData(31) {}
                For i As Integer = 0 To 30
                    Me.予定データ(i) = New BaseCore.Common.Field.ItemData("予定", TypeCode.String)
                Next
                Me.実績データ = New BaseCore.Common.Field.ItemData(31) {}
                For i As Integer = 0 To 30
                    Me.実績データ(i) = New BaseCore.Common.Field.ItemData("実績", TypeCode.String)
                Next


            End Sub
        End Class
#End Region

#Region "変数定義"
        Public Class Paramater
            Public Header As Header
            Public Detail1 As New List(Of Detail1)
            Public Detail2 As New List(Of Detail2)
        End Class

        '''========================================================================================
        ''' <summary>ﾍｯﾀﾞ</summary>
        '''========================================================================================
        Private _Fields As New Paramater
        Public Property Fields() As Paramater
            Get
                Return _Fields
            End Get

            Set(ByVal value As Paramater)
                _Fields = value
            End Set
        End Property
#End Region

#Region "ｺﾝｽﾄﾗｸﾀ"
        '''========================================================================================
        ''' <SUMMARY>ｺﾝｽﾄﾗｸﾀ</SUMMARY>
        ''' <PARAM name="Security">ｾｷｭﾘﾃｨ ｸﾗｽ</PARAM>
        ''' <PARAM name="Config">ｺﾝﾌｨｸﾞ ｸﾗｽ</PARAM>
        ''' <PARAM name="PagePath">ﾍﾟｰｼﾞﾊﾟｽ</PARAM>
        '''========================================================================================
        Public Sub New(ByVal Security As NodeCore.Common.Security, ByVal Config As NodeCore.Common.Config, ByVal PagePath As String)
            '----------------------------------------------------------------------
            ' ｾｷｭﾘﾃｨ 設定
            '----------------------------------------------------------------------
            MyBase.New(Security, Config, PagePath)

            Me.Fields.Header = New Header(Config)
        End Sub
#End Region

#Region "ﾍｯﾀﾞ"

#Region "Clear"
        '''========================================================================================
        ''' <SUMMARY>ｸﾘｱ</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Clear_Header(Optional ByVal KeyClear As Boolean = False) As Boolean
            '----------------------------------------------------------------------
            ' ｷｰ部ｸﾘｱ
            '----------------------------------------------------------------------
            If KeyClear Then
            End If

            '----------------------------------------------------------------------
            ' ﾃﾞｰﾀ部ｸﾘｱ
            '----------------------------------------------------------------------
            'Me.Fields.Header.日付.Value = Nothing

            '----------------------------------------------------------------------
            ' ﾃﾞｰﾀ部ｸﾘｱ
            '----------------------------------------------------------------------
            'Me.Read()

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#End Region

#Region "明細:詳細"
#Region "Clear"
        '''========================================================================================
        ''' <SUMMARY>ｸﾘｱ</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Clear_Detail1() As Boolean
            '----------------------------------------------------------------------
            ' ｸﾘｱ
            '----------------------------------------------------------------------
            For i As Integer = 0 To Me.Fields.Detail1.Count - 1
                Me.Clear_Detail1(i)
            Next

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function

        Public Overrides Function Clear_Detail1(ByVal Index As Integer) As Boolean

            Me.Fields.Detail1(Index).乗務員CD.Value = Nothing
            Me.Fields.Detail1(Index).乗務員名.Value = Nothing
            Me.Fields.Detail1(Index).シフト区分.Value = Nothing
            Me.Fields.Detail1(Index).シフト区分名.Value = Nothing
            Me.Fields.Detail1(Index).夜勤.Value = Nothing
            Me.Fields.Detail1(Index).昼勤.Value = Nothing
            Me.Fields.Detail1(Index).隔勤.Value = Nothing
            Me.Fields.Detail1(Index).行合計.Value = Nothing
            For intCol = 0 To 30
                Me.Fields.Detail1(Index).日付データ(intCol).Value = Nothing
                Me.Fields.Detail1(Index).予定データ(intCol).Value = Nothing
                Me.Fields.Detail1(Index).実績データ(intCol).Value = Nothing
            Next

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function

        '''========================================================================================
        ''' <SUMMARY>ｸﾘｱ</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Clear_Detail2() As Boolean
            '----------------------------------------------------------------------
            ' ｸﾘｱ
            '----------------------------------------------------------------------
            For i As Integer = 0 To Me.Fields.Detail2.Count - 1
                Me.Clear_Detail2(i)
            Next

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function

        Public Overrides Function Clear_Detail2(ByVal Index As Integer) As Boolean

            Me.Fields.Detail2(Index).乗務員CD.Value = Nothing
            Me.Fields.Detail2(Index).乗務員名.Value = Nothing
            Me.Fields.Detail2(Index).シフト区分.Value = Nothing
            Me.Fields.Detail2(Index).シフト区分名.Value = Nothing
            Me.Fields.Detail2(Index).夜勤.Value = Nothing
            Me.Fields.Detail2(Index).昼勤.Value = Nothing
            Me.Fields.Detail2(Index).隔勤.Value = Nothing
            Me.Fields.Detail2(Index).行合計.Value = Nothing
            For intCol = 0 To 30
                Me.Fields.Detail2(Index).日付データ(intCol).Value = Nothing
                Me.Fields.Detail2(Index).予定データ(intCol).Value = Nothing
                Me.Fields.Detail2(Index).実績データ(intCol).Value = Nothing
            Next

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "Read"
        '''========================================================================================
        ''' <SUMMARY>読込</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Read_Detail1() As Boolean

            If Me.Fields.Header.日付.Value = "" Then
                Return False
            End If

            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetFuncTableAdapters.F0乗務員予定実績TableAdapter
            Dim tbl As New NodeDatabase.DataSetFunc.F0乗務員予定実績DataTable
            Dim qry As New Collection

            '----------------------------------------------------------------------
            ' 事前準備
            '----------------------------------------------------------------------
            qry.Clear()
            Dim dt As Date = Date.Parse(Me.Fields.Header.日付.Value & "/" & Me.Security.事業開始日)
            Dim strDateF = dt.ToString("yyyy/MM/dd")
            Dim strDateT = Me.Fields.Header.日付T.Value

            qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, Me.Fields.Header.グループCD.Value, BaseDatabase.Contents.Compare.Parameter))
            qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, Me.Fields.Header.個社CD.Value, BaseDatabase.Contents.Compare.Parameter))
            qry.Add(New BaseDatabase.Condition(tbl.子会社CDColumn.ColumnName, Me.Fields.Header.子会社CD.Value, BaseDatabase.Contents.Compare.Parameter))
            qry.Add(New BaseDatabase.Condition("日付F", strDateF, BaseDatabase.Contents.Compare.Parameter))
            qry.Add(New BaseDatabase.Condition("日付T", strDateT, BaseDatabase.Contents.Compare.Parameter))

            Dim strSort As String = ""

            strSort &= tbl.乗務員CDColumn.ColumnName & " ASC"

            tbl = ada.SelectByCommon(qry, strSort)

            '----------------------------------------------------------------------
            ' ﾌﾚｰﾑ作成
            '----------------------------------------------------------------------
            Me.FrameMaker_Detail1(tbl)

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function

        Protected Overrides Function Read_Detail2() As Boolean

            If Me.Fields.Header.日付.Value = "" Then
                Return False
            End If

            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetFuncTableAdapters.F0乗務員予定実績TableAdapter
            Dim tbl As New NodeDatabase.DataSetFunc.F0乗務員予定実績DataTable
            Dim qry As New Collection

            '----------------------------------------------------------------------
            ' 事前準備
            '----------------------------------------------------------------------
            qry.Clear()
            Dim dt As Date = Date.Parse(Me.Fields.Header.日付.Value & "/" & Me.Security.事業開始日)
            Dim strDateF = dt.ToString("yyyy/MM/dd")
            Dim strDateT = Me.Fields.Header.日付T.Value

            qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, Me.Fields.Header.グループCD.Value, BaseDatabase.Contents.Compare.Parameter))
            qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, Me.Fields.Header.個社CD.Value, BaseDatabase.Contents.Compare.Parameter))
            qry.Add(New BaseDatabase.Condition(tbl.子会社CDColumn.ColumnName, Me.Fields.Header.子会社CD.Value, BaseDatabase.Contents.Compare.Parameter))
            qry.Add(New BaseDatabase.Condition("日付F", strDateF, BaseDatabase.Contents.Compare.Parameter))
            qry.Add(New BaseDatabase.Condition("日付T", strDateT, BaseDatabase.Contents.Compare.Parameter))

            Dim strSort As String = ""

            strSort &= tbl.乗務員CDColumn.ColumnName & " ASC"

            tbl = ada.SelectByCommon(qry, strSort)

            '----------------------------------------------------------------------
            ' ﾌﾚｰﾑ作成
            '----------------------------------------------------------------------
            Me.FrameMaker_Detail2(tbl)

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "Valid_Write"
        '''========================================================================================
        ''' <summary>検査</summary>
        ''' <RETURNS>True:正常, False:不正</RETURNS>
        '''========================================================================================
        Protected Overrides Function Valid_Write_Detail1_Common() As NodeContents.ActionButton.ErrorLevel
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim strMsg As String = ""
            Dim Validator As New BaseCore.Common.Validator

            '------------------------------------------------------------------
            ' ｴﾗｰｸﾘｱ
            '------------------------------------------------------------------
            For i As Integer = 0 To Me.Fields.Detail1.Count - 1
                'Me.Fields.Detail1(i).シフト区分.IsError = False
                'Me.Fields.Detail1(i)..IsError = False
                'Me.Fields.Detail1(i).JANCD.IsError = False
                'Me.Fields.Detail1(i).商品名.IsError = False
                'Me.Fields.Detail1(i).定価.IsError = False
                'Me.Fields.Detail1(i).数量.IsError = False
                'Me.Fields.Detail1(i).仕入単価.IsError = False
                'Me.Fields.Detail1(i).仕入金額.IsError = False
                'Me.Fields.Detail1(i).消費税額.IsError = False
                'Me.Fields.Detail1(i).消費税率.IsError = False
                'Me.Fields.Detail1(i).入数.IsError = False
            Next

            '------------------------------------------------------------------
            ' 共通検査
            '------------------------------------------------------------------
            For i As Integer = 0 To Me.Fields.Detail1.Count - 1
                If Not Me.Empty_Detail1(Me.Fields.Detail1(i)) Then
                    'If Not Validator.BaseChecker(Me.Fields.Detail1(i).削除) Then : Me.Fields.Detail1(i).削除.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                    'If Not Validator.BaseChecker(Me.Fields.Detail1(i).行NO) Then : Me.Fields.Detail1(i).行NO.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                    'If Not Validator.BaseChecker(Me.Fields.Detail1(i).JANCD) Then : Me.Fields.Detail1(i).JANCD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                    'If Not Validator.BaseChecker(Me.Fields.Detail1(i).商品名) Then : Me.Fields.Detail1(i).商品名.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                    'If Not Validator.BaseChecker(Me.Fields.Detail1(i).定価) Then : Me.Fields.Detail1(i).定価.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                    'If Not Validator.BaseChecker(Me.Fields.Detail1(i).数量) Then : Me.Fields.Detail1(i).数量.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                    'If Not Validator.BaseChecker(Me.Fields.Detail1(i).仕入単価) Then : Me.Fields.Detail1(i).仕入単価.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                    'If Not Validator.BaseChecker(Me.Fields.Detail1(i).仕入金額) Then : Me.Fields.Detail1(i).仕入金額.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                    'If Not Validator.BaseChecker(Me.Fields.Detail1(i).消費税額) Then : Me.Fields.Detail1(i).消費税額.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                    'If Not Validator.BaseChecker(Me.Fields.Detail1(i).消費税率) Then : Me.Fields.Detail1(i).消費税率.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                    'If Not Validator.BaseChecker(Me.Fields.Detail1(i).入数) Then : Me.Fields.Detail1(i).入数.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                End If
            Next

            '------------------------------------------------------------------
            ' 返却
            '------------------------------------------------------------------
            MyBase.LastError = strMsg
            Select Case True
                Case strMsg = ""
                    Return NodeContents.ActionButton.ErrorLevel.None
                Case Else
                    Return NodeContents.ActionButton.ErrorLevel.Fatal
            End Select
        End Function


#End Region

#Region "Valid_Delete"
        '''========================================================================================
        ''' <SUMMARY>検査(共通)</SUMMARY>
        ''' <RETURNS>True:正常, False:不正</RETURNS>
        '''========================================================================================
        Protected Overrides Function Valid_Delete_Detail1_Common() As NodeContents.ActionButton.ErrorLevel
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim strMsg As String = ""
            Dim Validator As New BaseCore.Common.Validator

            '------------------------------------------------------------------
            ' ｴﾗｰｸﾘｱ
            '------------------------------------------------------------------

            '------------------------------------------------------------------
            ' 共通検査
            '------------------------------------------------------------------

            '------------------------------------------------------------------
            ' 返却
            '------------------------------------------------------------------
            MyBase.LastError = strMsg
            Select Case True
                Case strMsg = ""
                    Return NodeContents.ActionButton.ErrorLevel.None
                Case Else
                    Return NodeContents.ActionButton.ErrorLevel.Fatal
            End Select
        End Function

        '''========================================================================================
        ''' <SUMMARY>検査(個別)</SUMMARY>
        ''' <RETURNS>True:正常, False:不正</RETURNS>
        '''========================================================================================
        Protected Overrides Function Valid_Delete_Detail1_Another() As NodeContents.ActionButton.ErrorLevel
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim strMsg As String = ""
            Dim Validator As New BaseCore.Common.Validator

            '------------------------------------------------------------------
            ' 返却
            '------------------------------------------------------------------
            MyBase.LastError = strMsg
            Select Case True
                Case strMsg = ""
                    Return NodeContents.ActionButton.ErrorLevel.None
                Case Else
                    Return NodeContents.ActionButton.ErrorLevel.Fatal
            End Select
        End Function
#End Region

#Region "Write"
        '''========================================================================================
        ''' <SUMMARY>書込</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Write_Detail1() As Boolean
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim strNow As String = Format(Now, NodeContents.Constant.System.DATE_TIME_FORMAT)

            Dim ada As New NodeDatabase.DataSetTranTableAdapters.T乗務予定実績TableAdapter
            Dim tbl As New NodeDatabase.DataSetTran.T乗務予定実績DataTable
            Dim qry As New Collection

            '----------------------------------------------------------------------
            ' 事前準備
            '----------------------------------------------------------------------
            Me.Fields.Header.更新日時.Value = strNow
            Me.Fields.Header.更新ユーザーCD.Value = Me.Security.ユーザーCD
            Me.Fields.Header.更新ユーザー名.Value = Me.Security.ユーザー名

            Try
                Using scope As New System.Transactions.TransactionScope(System.Transactions.TransactionScopeOption.Required, TimeSpan.Zero)
                    '----------------------------------------------------------------------
                    ' 保存
                    '----------------------------------------------------------------------
                    'Try

                    For i As Integer = 0 To Fields.Detail1.Count - 1

                        Dim dtl As Detail1 = Me.Fields.Detail1(i)

                        For j As Integer = 0 To dtl.予定データ.Count - 1

                            If (dtl.予定データ(j) Is Nothing) Then
                                Continue For
                            End If


                            '----------------------------------------------------------------------
                            ' 条件設定
                            '----------------------------------------------------------------------
                            qry.Clear()
                            qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, Me.Fields.Header.グループCD.Value, BaseDatabase.Contents.Compare.Equal))
                            qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, Me.Fields.Header.個社CD.Value, BaseDatabase.Contents.Compare.Equal))
                            qry.Add(New BaseDatabase.Condition(tbl.子会社CDColumn.ColumnName, Me.Fields.Header.子会社CD.Value, BaseDatabase.Contents.Compare.Equal))
                            qry.Add(New BaseDatabase.Condition(tbl.乗務員CDColumn.ColumnName, dtl.乗務員CD.Value, BaseDatabase.Contents.Compare.Equal))
                            qry.Add(New BaseDatabase.Condition(tbl.日付Column.ColumnName, Me.Fields.Header.データ(j), BaseDatabase.Contents.Compare.Equal))


                            '----------------------------------------------------------------------
                            ' 読込
                            '----------------------------------------------------------------------
                            tbl = ada.SelectByCommon(qry)


                            '----------------------------------------------------------------------
                            ' 保存
                            '----------------------------------------------------------------------
                            If tbl.Count = 0 Then
                                '----------------------------------------------------------------------
                                ' 新規
                                '----------------------------------------------------------------------
                                Me.Fields.Header.登録日時.Value = strNow
                                Me.Fields.Header.登録ユーザーCD.Value = Me.Security.ユーザーCD
                                Me.Fields.Header.登録ユーザー名.Value = Me.Security.ユーザー名

                                ada.Insert(Me.Fields.Header.グループCD.Value _
                                             , Me.Fields.Header.個社CD.Value _
                                             , Me.Fields.Header.子会社CD.Value _
                                             , Me.Fields.Header.データ(j) _
                                             , dtl.乗務員CD.Value _
                                             , dtl.乗務員名.Value _
                                             , dtl.シフト区分.Value _
                                             , dtl.シフト区分名.Value _
                                             , Val(dtl.予定データ(j).Value) _
                                             , Val(dtl.予定データ(j).Value) _
                                             , Me.Fields.Header.登録日時.Value _
                                             , Me.Fields.Header.登録ユーザーCD.Value _
                                             , Me.Fields.Header.登録ユーザー名.Value _
                                             , Me.Fields.Header.更新日時.Value _
                                             , Me.Fields.Header.更新ユーザーCD.Value _
                                             , Me.Fields.Header.更新ユーザー名.Value
                                            )


                            Else
                                '----------------------------------------------------------------------
                                ' 訂正
                                '----------------------------------------------------------------------
                                tbl(0).Item(tbl.予定Column.ColumnName) = Val(dtl.予定データ(j).Value)
                                tbl(0).Item(tbl.実績Column.ColumnName) = Val(dtl.予定データ(j).Value)
                                tbl(0).Item(tbl.更新年月日Column.ColumnName) = Me.Fields.Header.更新日時.Value
                                tbl(0).Item(tbl.更新ユーザーCDColumn.ColumnName) = Me.Fields.Header.更新ユーザーCD.Value
                                tbl(0).Item(tbl.更新ユーザー名Column.ColumnName) = Me.Fields.Header.更新ユーザー名.Value

                                ada.Update(tbl)
                            End If


                        Next
                    Next

                    'Catch ex As Exception
                    '    MyBase.LastError = "以下のエラーのため、追加できませんでした。" & vbCrLf & ex.Message
                    '    Return False
                    'End Try
                    scope.Complete()
                End Using


            Catch ex As Exception
                Me.LastError = "以下のエラーのため、保存できませんでした。" & vbCrLf & ex.Message
                Return False
            End Try


            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function


        Protected Overrides Function Write_Detail2() As Boolean
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim strNow As String = Format(Now, NodeContents.Constant.System.DATE_TIME_FORMAT)

            Dim ada As New NodeDatabase.DataSetTranTableAdapters.T乗務予定実績TableAdapter
            Dim tbl As New NodeDatabase.DataSetTran.T乗務予定実績DataTable
            Dim qry As New Collection

            '----------------------------------------------------------------------
            ' 事前準備
            '----------------------------------------------------------------------
            Me.Fields.Header.更新日時.Value = strNow
            Me.Fields.Header.更新ユーザーCD.Value = Me.Security.ユーザーCD
            Me.Fields.Header.更新ユーザー名.Value = Me.Security.ユーザー名

            ' todo del log
            For i As Integer = 0 To Me.Fields.Detail1.Count - 1
                Dim dtl As Detail1 = Me.Fields.Detail1(i)
                For j As Integer = 0 To dtl.実績データ.Count - 1
                    If (dtl.実績データ(j) Is Nothing) Then
                        Continue For
                    End If
                    System.Diagnostics.Debug.WriteLine(" WRITE DTL: " & j.ToString & "," & Me.Fields.Header.データ(j) & "," & dtl.実績データ(j).Value.ToString)
                Next
            Next

            Try
                Using scope As New System.Transactions.TransactionScope(System.Transactions.TransactionScopeOption.Required, TimeSpan.Zero)
                    '----------------------------------------------------------------------
                    ' 保存
                    '----------------------------------------------------------------------
                    'Try

                    For i As Integer = 0 To Fields.Detail2.Count - 1

                        Dim dtl As Detail2 = Me.Fields.Detail2(i)

                        For j As Integer = 0 To dtl.実績データ.Count - 1

                            If (dtl.実績データ(j) Is Nothing) Then
                                Continue For
                            End If

                            '----------------------------------------------------------------------
                            ' 条件設定
                            '----------------------------------------------------------------------
                            qry.Clear()
                            qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, Me.Fields.Header.グループCD.Value, BaseDatabase.Contents.Compare.Equal))
                            qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, Me.Fields.Header.個社CD.Value, BaseDatabase.Contents.Compare.Equal))
                            qry.Add(New BaseDatabase.Condition(tbl.子会社CDColumn.ColumnName, Me.Fields.Header.子会社CD.Value, BaseDatabase.Contents.Compare.Equal))
                            qry.Add(New BaseDatabase.Condition(tbl.乗務員CDColumn.ColumnName, dtl.乗務員CD.Value, BaseDatabase.Contents.Compare.Equal))
                            qry.Add(New BaseDatabase.Condition(tbl.日付Column.ColumnName, Me.Fields.Header.データ(j), BaseDatabase.Contents.Compare.Equal))


                            '----------------------------------------------------------------------
                            ' 読込
                            '----------------------------------------------------------------------
                            tbl = ada.SelectByCommon(qry)


                            '----------------------------------------------------------------------
                            ' 保存
                            '----------------------------------------------------------------------
                            If tbl.Count = 0 Then
                                '----------------------------------------------------------------------
                                ' 新規
                                '----------------------------------------------------------------------
                                Me.Fields.Header.登録日時.Value = strNow
                                Me.Fields.Header.登録ユーザーCD.Value = Me.Security.ユーザーCD
                                Me.Fields.Header.登録ユーザー名.Value = Me.Security.ユーザー名


                                ada.Insert(Me.Fields.Header.グループCD.Value _
                                             , Me.Fields.Header.個社CD.Value _
                                             , Me.Fields.Header.子会社CD.Value _
                                             , Me.Fields.Header.データ(j) _
                                             , dtl.乗務員CD.Value _
                                             , dtl.乗務員名.Value _
                                             , dtl.シフト区分.Value _
                                             , dtl.シフト区分名.Value _
                                             , Val(dtl.実績データ(j).Value) _
                                             , Val(dtl.実績データ(j).Value) _
                                             , Me.Fields.Header.登録日時.Value _
                                             , Me.Fields.Header.登録ユーザーCD.Value _
                                             , Me.Fields.Header.登録ユーザー名.Value _
                                             , Me.Fields.Header.更新日時.Value _
                                             , Me.Fields.Header.更新ユーザーCD.Value _
                                             , Me.Fields.Header.更新ユーザー名.Value
                                            )
                            Else
                                '----------------------------------------------------------------------
                                ' 訂正
                                '----------------------------------------------------------------------
                                tbl(0).Item(tbl.実績Column.ColumnName) = Val(dtl.実績データ(j).Value)
                                tbl(0).Item(tbl.更新年月日Column.ColumnName) = Me.Fields.Header.更新日時.Value
                                tbl(0).Item(tbl.更新ユーザーCDColumn.ColumnName) = Me.Fields.Header.更新ユーザーCD.Value
                                tbl(0).Item(tbl.更新ユーザー名Column.ColumnName) = Me.Fields.Header.更新ユーザー名.Value

                                ada.Update(tbl)
                            End If


                            '有給の場合は有給情報追加,有給＝５
                            If Val(dtl.実績データ(j).Value) = 5 Then

                                '----------------------------------------------------------------------
                                ' 変数定義
                                '----------------------------------------------------------------------
                                Dim ada休暇 As New NodeDatabase.DataSetTranTableAdapters.T休暇TableAdapter
                                Dim tbl休暇 As New NodeDatabase.DataSetTran.T休暇DataTable
                                Dim qry休暇 As New Collection

                                '----------------------------------------------------------------------
                                ' 条件設定
                                '----------------------------------------------------------------------
                                qry休暇.Clear()
                                qry休暇.Add(New BaseDatabase.Condition(tbl休暇.グループCDColumn.ColumnName, Me.Fields.Header.グループCD.Value, BaseDatabase.Contents.Compare.Equal))
                                qry休暇.Add(New BaseDatabase.Condition(tbl休暇.個社CDColumn.ColumnName, Me.Fields.Header.個社CD.Value, BaseDatabase.Contents.Compare.Equal))
                                qry休暇.Add(New BaseDatabase.Condition(tbl休暇.子会社CDColumn.ColumnName, Me.Fields.Header.子会社CD.Value, BaseDatabase.Contents.Compare.Equal))
                                qry休暇.Add(New BaseDatabase.Condition(tbl休暇.乗務員CDColumn.ColumnName, dtl.乗務員CD.Value, BaseDatabase.Contents.Compare.Equal))
                                qry休暇.Add(New BaseDatabase.Condition(tbl休暇.日付Column.ColumnName, Me.Fields.Header.データ(j), BaseDatabase.Contents.Compare.Equal))
                                qry休暇.Add(New BaseDatabase.Condition(tbl休暇.休暇区分Column.ColumnName, 1, BaseDatabase.Contents.Compare.Equal))

                                '----------------------------------------------------------------------
                                ' 読込
                                '----------------------------------------------------------------------
                                tbl休暇 = ada休暇.SelectByCommon(qry休暇)

                                '----------------------------------------------------------------------
                                ' 事前準備
                                '----------------------------------------------------------------------
                                Me.Fields.Header.更新日時.Value = strNow
                                Me.Fields.Header.更新ユーザーCD.Value = Me.Security.ユーザーCD
                                Me.Fields.Header.更新ユーザー名.Value = Me.Security.ユーザー名

                                '----------------------------------------------------------------------
                                ' 保存
                                '----------------------------------------------------------------------
                                If tbl休暇.Count = 0 Then
                                    '----------------------------------------------------------------------
                                    ' 新規
                                    '----------------------------------------------------------------------
                                    Me.Fields.Header.登録日時.Value = strNow
                                    Me.Fields.Header.登録ユーザーCD.Value = Me.Security.ユーザーCD
                                    Me.Fields.Header.登録ユーザー名.Value = Me.Security.ユーザー名
                                    Try
                                        ada休暇.Insert(Me.Fields.Header.グループCD.Value _
                                                        , Me.Fields.Header.個社CD.Value _
                                                        , Me.Fields.Header.子会社CD.Value _
                                                        , dtl.乗務員CD.Value _
                                                        , Me.Fields.Header.データ(j) _
                                                        , 1 _
                                                        , "予定実績にて変更" _
                                                        , "" _
                                                        , Me.Fields.Header.登録ユーザーCD.Value _
                                                        , Me.Fields.Header.登録ユーザー名.Value _
                                                        , Me.Fields.Header.登録日時.Value _
                                                        , Me.Fields.Header.更新ユーザーCD.Value _
                                                        , Me.Fields.Header.更新ユーザー名.Value _
                                                        , Me.Fields.Header.更新日時.Value
                                                        )

                                    Catch ex As Exception
                                        MyBase.LastError = "以下のエラーのため、追加できませんでした。" & vbCrLf & ex.Message
                                        Return False
                                    End Try
                                    'Else
                                    '    '----------------------------------------------------------------------
                                    '    ' 訂正
                                    '    '----------------------------------------------------------------------
                                    '    tbl休暇(0).Item(tbl休暇.休暇区分Column.ColumnName) = "1"
                                    '    tbl休暇(0).Item(tbl休暇.日付Column.ColumnName) = Val(Me.Fields.Header.データ(j))
                                    '    tbl休暇(0).Item(tbl休暇.更新日時Column.ColumnName) = Me.Fields.Header.更新日時.Value
                                    '    tbl休暇(0).Item(tbl休暇.更新ユーザーCDColumn.ColumnName) = Me.Fields.Header.更新ユーザーCD.Value
                                    '    tbl休暇(0).Item(tbl休暇.更新ユーザー名Column.ColumnName) = Me.Fields.Header.更新ユーザー名.Value

                                    '    ada休暇.Update(tbl休暇)

                                End If

                                ''----------------------------------------------------------------------
                                '' ｽﾄｱﾄﾞ実行（残数再計算)
                                ''----------------------------------------------------------------------
                                'Dim adaStored As New NodeDatabase.DataSetStoredTableAdapters.P0有給再計算TableAdapter
                                'Dim intStatus As Integer

                                'intStatus = adaStored.Execute(Me.Fields.Header.グループCD.Value, Me.Fields.Header.個社CD.Value, Me.Fields.Header.子会社CD.Value, dtl.乗務員CD.Value, Me.Fields.Header.データ(j))

                                ''----------------------------------------------------------------------
                                '' ｴﾗｰ処理
                                ''----------------------------------------------------------------------
                                'If intStatus <> 0 Then
                                '    Me.LastError = "残数の再計算処理されませんでした。"
                                'End If

                            End If

                        Next
                    Next

                    'Catch ex As Exception
                    '    MyBase.LastError = "以下のエラーのため、追加できませんでした。" & vbCrLf & ex.Message
                    '    Return False
                    'End Try
                    scope.Complete()
                End Using


            Catch ex As Exception
                Me.LastError = "以下のエラーのため、保存できませんでした。" & vbCrLf & ex.Message
                Return False
            End Try


            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "Delete"
        '''========================================================================================
        ''' <SUMMARY>削除</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Delete_Detail1() As Boolean
            ''----------------------------------------------------------------------
            '' 変数定義
            ''----------------------------------------------------------------------
            'Dim ada As New NodeDatabase.DataSetTranTableAdapters.T納品TableAdapter
            'Dim tbl As New NodeDatabase.DataSetTran.T納品DataTable
            'Dim qry As New Collection

            ''----------------------------------------------------------------------
            '' T納品
            ''----------------------------------------------------------------------
            'qry.Clear()
            'qry.Add(New BaseDatabase.Condition(tbl.仕入先CDColumn.ColumnName, Me.Fields.Header.仕入先CD.Value, BaseDatabase.Contents.Compare.Equal))
            'qry.Add(New BaseDatabase.Condition(tbl.店舗CDColumn.ColumnName, Me.Fields.Header.店舗CD.Value, BaseDatabase.Contents.Compare.Equal))
            'qry.Add(New BaseDatabase.Condition(tbl.納品日付Column.ColumnName, Me.Fields.Header.納品日付.Value, BaseDatabase.Contents.Compare.Equal))
            'qry.Add(New BaseDatabase.Condition(tbl.伝票番号Column.ColumnName, Me.Fields.Header.伝票番号.Value, BaseDatabase.Contents.Compare.Equal))

            'Try
            '    Using scope As New System.Transactions.TransactionScope(System.Transactions.TransactionScopeOption.Required, TimeSpan.Zero)
            '        ada.DeleteByCommon(qry)
            '        scope.Complete()
            '    End Using
            'Catch ex As Exception
            '    MyBase.LastError = "以下のエラーのため、削除できませんでした。" & vbCrLf & ex.Message
            '    Return False
            'End Try

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "MakeDataTable"
        '''========================================================================================
        ''' <SUMMARY>ﾃﾞｰﾀﾃｰﾌﾞﾙ生成</SUMMARY>
        ''' <RETURNS>ﾃﾞｰﾀﾃｰﾌﾞﾙ</RETURNS>
        '''========================================================================================
        Public Function MakeDataTable() As DataTable

            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetFuncTableAdapters.F0乗務員予定実績TableAdapter
            Dim tbl As New NodeDatabase.DataSetFunc.F0乗務員予定実績DataTable
            Dim qry As New Collection

            '----------------------------------------------------------------------
            ' 事前準備
            '----------------------------------------------------------------------
            qry.Clear()
            Dim dt As Date = Date.Parse(Me.Fields.Header.日付.Value & "/" & Me.Security.事業開始日)
            Dim strDateF = dt.ToString("yyyy/MM/dd")
            Dim strDateT = Me.Fields.Header.日付T.Value

            qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, Me.Fields.Header.グループCD.Value, BaseDatabase.Contents.Compare.Parameter))
            qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, Me.Fields.Header.個社CD.Value, BaseDatabase.Contents.Compare.Parameter))
            qry.Add(New BaseDatabase.Condition(tbl.子会社CDColumn.ColumnName, Me.Fields.Header.子会社CD.Value, BaseDatabase.Contents.Compare.Parameter))
            qry.Add(New BaseDatabase.Condition("日付F", strDateF, BaseDatabase.Contents.Compare.Parameter))
            qry.Add(New BaseDatabase.Condition("日付T", strDateT, BaseDatabase.Contents.Compare.Parameter))

            Dim strSort As String = ""

            strSort &= tbl.乗務員CDColumn.ColumnName & " ASC"

            tbl = ada.SelectByCommon(qry, strSort)

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return tbl
        End Function
#End Region

#Region "エクセルデータ作成"

        '''========================================================================================
        ''' <SUMMARY>抽出したﾃｰﾌﾞﾙに編集を加える</SUMMARY>   
        '''========================================================================================
        Public Function MakeExcelData(ByRef tbl As NodeDatabase.DataSetFunc.F0乗務員予定実績DataTable, ByRef xls As BaseCore.Common.Excel) As Boolean
            '------------------------------------------------------------------------
            ' 変数定義
            '------------------------------------------------------------------------
            Dim intStartCol As Integer = 4
            Dim intStartRow As Integer = 3

            Try
                xls.SheetSelect(sheetNameYotei)

                For d As Integer = 0 To Me.Fields.Header.データ.Length - 1
                    If Me.Fields.Header.データ(d) IsNot Nothing Then
                        xls.CellSetValue(1, 4 + d, Me.Fields.Header.データ(d))
                    End If
                Next

                For i As Integer = 0 To tbl.Count - 2
                    xls.CopyAndPaste(sheetNameYotei, intStartCol, 1, intStartCol, 40, sheetNameYotei, intStartRow + 2 + i, 1)
                Next

                For i As Integer = 0 To tbl.Count - 1
                    xls.CellSetValue(intStartRow + i, 1, tbl(i).Item(tbl.乗務員CDColumn.ColumnName))
                    xls.CellSetValue(intStartRow + i, 2, tbl(i).Item(tbl.乗務員名Column.ColumnName))

                    If tbl(i).Item(tbl.区分名Column.ColumnName) IsNot DBNull.Value Then
                        xls.CellSetValue(intStartRow + i, 3, tbl(i).Item(tbl.区分名Column.ColumnName))
                    End If

                    Dim strVal As String = tbl(i).Item(tbl.日付Column.ColumnName).ToString()

                    If strVal <> "" Then
                        Dim tmpDates() As String = tbl(i).Item(tbl.日付Column.ColumnName).ToString.Split(",")
                        Dim tmpYotei() As String = tbl(i).Item(tbl.予定Column.ColumnName).ToString.Split(",")

                        For d As Integer = 0 To Me.Fields.Header.データ.Length - 1

                            If Me.Fields.Header.データ(d) Is Nothing Then
                                Continue For
                            End If

                            For j As Integer = 0 To tmpDates.Length - 1

                                If (j < tmpYotei.Length And Me.Fields.Header.データ(d) = tmpDates(j) And tmpYotei(j) <> "") Then
                                    Select Case Val(tmpYotei(j))
                                        Case 1
                                            xls.CellSetValue(intStartRow + i, intStartCol + d, "隔")
                                        Case 2
                                            xls.CellSetValue(intStartRow + i, intStartCol + d, "昼")
                                        Case 3
                                            xls.CellSetValue(intStartRow + i, intStartCol + d, "夜")
                                    End Select
                                End If

                            Next

                        Next
                    End If

                Next

            Catch ex As Exception
                Me.LastError = ex.Message
                Return False
            Finally
            End Try
            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "実績エクセルデータ作成"

        '''========================================================================================
        ''' <SUMMARY>抽出したﾃｰﾌﾞﾙに編集を加える</SUMMARY>   
        '''========================================================================================
        Public Function MakeExcelDataJisseki(ByRef tbl As NodeDatabase.DataSetFunc.F0乗務員予定実績DataTable, ByRef xls As BaseCore.Common.Excel) As Boolean
            '------------------------------------------------------------------------
            ' 変数定義
            '------------------------------------------------------------------------
            Dim intStartCol As Integer = 4
            Dim intStartRow As Integer = 3

            Try
                xls.SheetSelect(sheetNameJisseki)
                For d As Integer = 0 To Me.Fields.Header.データ.Length - 1
                    If Me.Fields.Header.データ(d) IsNot Nothing Then
                        xls.CellSetValue(1, intStartCol + d, Me.Fields.Header.データ(d))
                    End If
                Next

                For i As Integer = 0 To tbl.Count - 2
                    xls.CopyAndPaste(sheetNameJisseki, intStartCol, 1, intStartCol, 40, sheetNameJisseki, intStartRow + 2 + i, 1)
                Next

                For i As Integer = 0 To tbl.Count - 1

                    xls.CellSetValue(intStartRow + i, 1, tbl(i).Item(tbl.乗務員CDColumn.ColumnName))
                    xls.CellSetValue(intStartRow + i, 2, tbl(i).Item(tbl.乗務員名Column.ColumnName))
                    If tbl(i).Item(tbl.区分名Column.ColumnName) IsNot DBNull.Value Then
                        xls.CellSetValue(intStartRow + i, 3, tbl(i).Item(tbl.区分名Column.ColumnName))
                    End If

                    Dim strVal As String = tbl(i).Item(tbl.日付Column.ColumnName).ToString()

                    If strVal <> "" Then
                        Dim tmpDates() As String = tbl(i).Item(tbl.日付Column.ColumnName).ToString.Split(",")
                        Dim tmpJisseki() As String = tbl(i).Item(tbl.実績Column.ColumnName).ToString.Split(",")

                        For d As Integer = 0 To Me.Fields.Header.データ.Length - 1
                            If Me.Fields.Header.データ(d) Is Nothing Then
                                Continue For
                            End If

                            For j As Integer = 0 To tmpDates.Length - 1

                                If (j < tmpJisseki.Length And Me.Fields.Header.データ(d) = tmpDates(j) And tmpJisseki(j) <> "") Then

                                    Select Case Val(tmpJisseki(j))
                                        Case 1
                                            xls.CellSetValue(intStartRow + i, intStartCol + d, "隔")
                                        Case 2
                                            xls.CellSetValue(intStartRow + i, intStartCol + d, "昼")
                                        Case 3
                                            xls.CellSetValue(intStartRow + i, intStartCol + d, "夜")
                                        Case 4
                                            xls.CellSetValue(intStartRow + i, intStartCol + d, "欠")
                                        Case 5
                                            xls.CellSetValue(intStartRow + i, intStartCol + d, "有")
                                    End Select

                                    'Select Case Val(tmpJisseki(j))
                                    '    Case 1
                                    '        xls.CellSetValue(intStartRow + i, intStartCol + j, "昼")
                                    '    Case 2
                                    '        xls.CellSetValue(intStartRow + i, intStartCol + j, "夜")
                                    '    Case 3
                                    '        xls.CellSetValue(intStartRow + i, intStartCol + j, "隔")
                                    '    Case 4
                                    '        xls.CellSetValue(intStartRow + i, intStartCol + j, "欠")
                                    '    Case 5
                                    '        xls.CellSetValue(intStartRow + i, intStartCol + j, "有")
                                    'End Select
                                End If


                            Next
                        Next

                    End If

                Next

            Catch ex As Exception
                Me.LastError = ex.Message
                Return False
            Finally
            End Try
            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "Execute1:Excel"
        '''========================================================================================
        ''' <SUMMARY>実行</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Execute1_Header() As Boolean

            Me.LastError = Nothing
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim xls As New BaseCore.Common.Excel

            Try
                '----------------------------------------------------------------------
                ' ﾃﾞｰﾀ抽出
                '----------------------------------------------------------------------
                Dim tbl As DataTable = Me.MakeDataTable()

                ''----------------------------------------------------------------------
                '' ｾﾞﾛ件
                ''----------------------------------------------------------------------
                If tbl.Rows.Count = 0 Then
                    Me.LastError = "指定された条件に合致するデータはありません。"
                    Return False
                End If

                '----------------------------------------------------------------------
                ' ｵｰﾌﾟﾝ
                '----------------------------------------------------------------------
                If Not xls.Open(Me.Path1) Then
                    Me.LastError = "テンプレートが見つかりません。(" & Me.Path1 & ")"
                    Return False
                End If

                '------------------------------------------------------------------------
                ' 作成
                '------------------------------------------------------------------------
                xls.SheetSelect(sheetNameYotei)       'ｱｸﾃｨﾌﾞｼｰﾄ

                If Not MakeExcelData(tbl, xls) Then
                    MyBase.LastError = Me.LastError
                    Return False
                End If

                xls.SheetSelect(sheetNameJisseki)       'ｱｸﾃｨﾌﾞｼｰﾄ
                If Not MakeExcelDataJisseki(tbl, xls) Then
                    MyBase.LastError = Me.LastError
                    Return False
                End If
                '----------------------------------------------------------------------
                ' 先頭へｶｰｿﾙ移動
                '----------------------------------------------------------------------
                xls.CellGet(0, 0)

                xls.Close(Me.Path1)
            Catch ex As Exception
                Me.LastError = ex.Message
                Return False
            Finally
            End Try

            ''----------------------------------------------------------------------
            '' 正常終了
            ''----------------------------------------------------------------------

            Return True
        End Function
#End Region

#Region "Execute2:予定作成"
        '''========================================================================================
        ''' <SUMMARY>実行</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Execute2_Header() As Boolean

            Me.LastError = Nothing
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            'Dim strNow As String = Format(Now, NodeContents.Constant.System.DATE_TIME_FORMAT)

            'Dim ada As New NodeDatabase.DataSetTranTableAdapters.T乗務予定実績TableAdapter
            'Dim tbl As New NodeDatabase.DataSetTran.T乗務予定実績DataTable
            'Dim qry As New Collection



            '----------------------------------------------------------------------
            ' ｽﾄｱﾄﾞ実行
            '----------------------------------------------------------------------
            Try
                Dim adaStored As New NodeDatabase.DataSetStoredTableAdapters.P0シフト予定作成TableAdapter
                Dim intStatus As Integer

                Using scope As New System.Transactions.TransactionScope(System.Transactions.TransactionScopeOption.Required, TimeSpan.Zero)
                    intStatus = adaStored.Execute(Fields.Header.グループCD.Value, Fields.Header.個社CD.Value, Fields.Header.子会社CD.Value, Me.Security.ユーザーCD, Me.Security.ユーザー名, Fields.Header.日付.Value & "/" & Me.Security.事業開始日, Fields.Header.日付T.Value)

                    '----------------------------------------------------------------------
                    ' ｴﾗｰ処理
                    '----------------------------------------------------------------------
                    If intStatus <> 0 Then
                        Me.LastError = "予定を作成出来ませんでした！"
                        Return False
                    End If

                    scope.Complete()
                End Using

            Catch ex As Exception
                MyBase.LastError = "以下のエラーの為、処理を続行できません。" & vbCrLf & ex.Message
                Return False
            End Try

            ''----------------------------------------------------------------------
            '' 事前準備
            ''----------------------------------------------------------------------
            'Me.Fields.Header.更新日時.Value = strNow
            'Me.Fields.Header.更新ユーザーCD.Value = Me.Security.ユーザーCD
            'Me.Fields.Header.更新ユーザー名.Value = Me.Security.ユーザー名

            'Try
            '    Using scope As New System.Transactions.TransactionScope(System.Transactions.TransactionScopeOption.Required, TimeSpan.Zero)
            '        '----------------------------------------------------------------------
            '        ' 保存
            '        '----------------------------------------------------------------------
            '        'Try

            '        For i As Integer = 0 To Fields.Detail1.Count - 1

            '            Dim dtl As Detail1 = Me.Fields.Detail1(i)

            '            For j As Integer = 0 To dtl.予定データ.Count - 1

            '                If (dtl.予定データ(j) Is Nothing Or dtl.予定データ(j).Value = "") Then
            '                    Continue For
            '                End If

            '                '----------------------------------------------------------------------
            '                ' 条件設定
            '                '----------------------------------------------------------------------
            '                qry.Clear()
            '                qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, Me.Fields.Header.グループCD.Value, BaseDatabase.Contents.Compare.Equal))
            '                qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, Me.Fields.Header.個社CD.Value, BaseDatabase.Contents.Compare.Equal))
            '                qry.Add(New BaseDatabase.Condition(tbl.子会社CDColumn.ColumnName, Me.Fields.Header.子会社CD.Value, BaseDatabase.Contents.Compare.Equal))
            '                qry.Add(New BaseDatabase.Condition(tbl.乗務員CDColumn.ColumnName, dtl.乗務員CD.Value, BaseDatabase.Contents.Compare.Equal))
            '                qry.Add(New BaseDatabase.Condition(tbl.日付Column.ColumnName, Me.Fields.Header.データ(j), BaseDatabase.Contents.Compare.Equal))

            '                '----------------------------------------------------------------------
            '                ' 読込
            '                '----------------------------------------------------------------------
            '                tbl = ada.SelectByCommon(qry)


            '                '----------------------------------------------------------------------
            '                ' 保存
            '                '----------------------------------------------------------------------
            '                If tbl.Count = 0 Then
            '                    '----------------------------------------------------------------------
            '                    ' 新規
            '                    '----------------------------------------------------------------------
            '                    Me.Fields.Header.登録日時.Value = strNow
            '                    Me.Fields.Header.登録ユーザーCD.Value = Me.Security.ユーザーCD
            '                    Me.Fields.Header.登録ユーザー名.Value = Me.Security.ユーザー名

            '                    ada.Insert(Me.Fields.Header.グループCD.Value _
            '                                 , Me.Fields.Header.個社CD.Value _
            '                                 , Me.Fields.Header.子会社CD.Value _
            '                                 , Me.Fields.Header.データ(j) _
            '                                 , dtl.乗務員CD.Value _
            '                                 , dtl.乗務員名.Value _
            '                                 , dtl.シフト区分.Value _
            '                                 , dtl.シフト区分名.Value _
            '                                 , Val(dtl.予定データ(j).Value) _
            '                                 , Val(dtl.予定データ(j).Value) _
            '                                 , Me.Fields.Header.登録日時.Value _
            '                                 , Me.Fields.Header.登録ユーザーCD.Value _
            '                                 , Me.Fields.Header.登録ユーザー名.Value _
            '                                 , Me.Fields.Header.更新日時.Value _
            '                                 , Me.Fields.Header.更新ユーザーCD.Value _
            '                                 , Me.Fields.Header.更新ユーザー名.Value
            '                                )
            '                Else
            '                    '----------------------------------------------------------------------
            '                    ' 訂正
            '                    '----------------------------------------------------------------------
            '                    tbl(0).Item(tbl.予定Column.ColumnName) = Val(dtl.予定データ(j).Value)
            '                    tbl(0).Item(tbl.実績Column.ColumnName) = Val(dtl.予定データ(j).Value)
            '                    tbl(0).Item(tbl.更新年月日Column.ColumnName) = Me.Fields.Header.更新日時.Value
            '                    tbl(0).Item(tbl.更新ユーザーCDColumn.ColumnName) = Me.Fields.Header.更新ユーザーCD.Value
            '                    tbl(0).Item(tbl.更新ユーザー名Column.ColumnName) = Me.Fields.Header.更新ユーザー名.Value

            '                    ada.Update(tbl)
            '                End If


            '            Next
            '        Next

            '        'Catch ex As Exception
            '        '    MyBase.LastError = "以下のエラーのため、追加できませんでした。" & vbCrLf & ex.Message
            '        '    Return False
            '        'End Try
            '        scope.Complete()
            '    End Using


            'Catch ex As Exception
            '    Me.LastError = "以下のエラーのため、保存できませんでした。" & vbCrLf & ex.Message
            '    Return False
            'End Try


            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "ﾌﾚｰﾑ関連"
#Region "ﾌﾚｰﾑ作成"
        '''========================================================================================
        ''' <SUMMARY>ﾌﾚｰﾑ作成</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Function FrameMaker_Detail1(ByVal tbl As NodeDatabase.DataSetFunc.F0乗務員予定実績DataTable) As Boolean
            '----------------------------------------------------------------------
            ' ﾌﾚｰﾑ生成
            '----------------------------------------------------------------------
            Me.Add_Detail1(tbl.Count)

            '----------------------------------------------------------------------
            ' 値設定
            '----------------------------------------------------------------------
            For i As Integer = 0 To tbl.Count - 1
                Me.Fields.Detail1(i).乗務員CD.Value = tbl(i).Item(tbl.乗務員CDColumn.ColumnName)
                Me.Fields.Detail1(i).乗務員名.Value = tbl(i).Item(tbl.乗務員名Column.ColumnName)
                Me.Fields.Detail1(i).シフト区分.Value = tbl(i).Item(tbl.区分IDColumn.ColumnName)
                Me.Fields.Detail1(i).シフト区分名.Value = tbl(i).Item(tbl.区分名Column.ColumnName)

                Dim strVal As String = tbl(i).Item(tbl.日付Column.ColumnName).ToString()

                If strVal <> "" Then
                    Dim tmpDates() As String = tbl(i).Item(tbl.日付Column.ColumnName).ToString.Split(",")
                    Dim tmpValues() As String = tbl(i).Item(tbl.予定Column.ColumnName).ToString.Split(",")

                    Dim strDateF As Date = Date.Parse(Me.Fields.Header.日付.Value & "/" & Me.Security.事業開始日)
                    Dim strDateT = Me.Fields.Header.日付T.Value
                    Dim daysDifference As Integer = DateDiff(DateInterval.Day, strDateF, strDateT)


                    For j As Integer = 0 To daysDifference - 1

                        For jj = 0 To tmpDates.Length - 1
                            If Fields.Header.データ(j) = tmpDates(jj) Then
                                Me.Fields.Detail1(i).予定データ(j).Value = Val(tmpValues(jj))
                            End If
                        Next

                    Next
                End If

            Next

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function

        '''========================================================================================
        ''' <SUMMARY>ﾌﾚｰﾑ作成</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Function FrameMaker_Detail2(ByVal tbl As NodeDatabase.DataSetFunc.F0乗務員予定実績DataTable) As Boolean
            '----------------------------------------------------------------------
            ' ﾌﾚｰﾑ生成
            '----------------------------------------------------------------------
            Me.Add_Detail2(tbl.Count)

            '----------------------------------------------------------------------
            ' 値設定
            '----------------------------------------------------------------------
            For i As Integer = 0 To tbl.Count - 1
                Me.Fields.Detail2(i).乗務員CD.Value = tbl(i).Item(tbl.乗務員CDColumn.ColumnName)
                Me.Fields.Detail2(i).乗務員名.Value = tbl(i).Item(tbl.乗務員名Column.ColumnName)
                Me.Fields.Detail2(i).シフト区分.Value = tbl(i).Item(tbl.区分IDColumn.ColumnName)
                Me.Fields.Detail2(i).シフト区分名.Value = tbl(i).Item(tbl.区分名Column.ColumnName)

                Dim strVal As String = tbl(i).Item(tbl.日付Column.ColumnName).ToString()

                If strVal <> "" Then
                    Dim tmpDates() As String = tbl(i).Item(tbl.日付Column.ColumnName).ToString.Split(",")
                    Dim tmpValues() As String = tbl(i).Item(tbl.実績Column.ColumnName).ToString.Split(",")

                    Dim strDateF As Date = Date.Parse(Me.Fields.Header.日付.Value & "/" & Me.Security.事業開始日)
                    Dim strDateT = Me.Fields.Header.日付T.Value
                    Dim daysDifference As Integer = DateDiff(DateInterval.Day, strDateF, strDateT)


                    For j As Integer = 0 To daysDifference - 1

                        For jj = 0 To tmpDates.Length - 1
                            If Fields.Header.データ(j) = tmpDates(jj) Then
                                Me.Fields.Detail2(i).実績データ(j).Value = Val(tmpValues(jj))
                            End If
                        Next

                    Next
                End If

            Next

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "行挿入"

        '''========================================================================================
        ''' <summary>複数行挿入</summary>
        '''========================================================================================
        Public Overrides Sub Insert_Detail1(ByVal Position As Integer, ByVal Count As Integer)
            '----------------------------------------------------------------------
            ' 行挿入 & 行ｸﾘｱ
            '----------------------------------------------------------------------
            For i As Integer = Position To Position + Count - 1
                Me.Clear_Detail1(Position)
            Next

            '----------------------------------------------------------------------
            ' 行NOの再付番
            '----------------------------------------------------------------------
            Me.ReNum_Detail1()
        End Sub
#End Region

#Region "行追加"
        '''========================================================================================
        ''' <summary>単一行追加</summary>
        '''========================================================================================
        Public Overrides Sub Add_Detail1()
            Me.Fields.Detail1.Add(New Detail1(Me.Config))
            Me.Clear_Detail1(Me.Fields.Detail1.Count - 1)
        End Sub

        '''========================================================================================
        ''' <summary>一括行追加</summary>
        ''' <param name="AddCount">追加行数</param>
        '''========================================================================================
        Public Overrides Sub Add_Detail1(ByVal AddCount As Integer)
            Me.Fields.Detail1.Clear()

            For i As Integer = 0 To AddCount - 1
                Me.Add_Detail1()
            Next
        End Sub

        '''========================================================================================
        ''' <summary>単一行追加</summary>
        '''========================================================================================
        Public Overrides Sub Add_Detail2()
            Me.Fields.Detail2.Add(New Detail2(Me.Config))
            Me.Clear_Detail2(Me.Fields.Detail2.Count - 1)
        End Sub

        '''========================================================================================
        ''' <summary>一括行追加</summary>
        ''' <param name="AddCount">追加行数</param>
        '''========================================================================================
        Public Overrides Sub Add_Detail2(ByVal AddCount As Integer)
            Me.Fields.Detail2.Clear()

            For i As Integer = 0 To AddCount - 1
                Me.Add_Detail2()
            Next
        End Sub
#End Region

#Region "行削除"
        '''========================================================================================
        ''' <summary>一括行削除</summary>
        '''========================================================================================
        Public Overrides Sub Del_Detail1()
            Me.Fields.Detail1.Clear()
        End Sub

        Public Overrides Sub Del_Detail2()
            Me.Fields.Detail2.Clear()
        End Sub
#End Region

#End Region

#Region "無効行判定"
        '''========================================================================================
        ''' <summary>無効行の判定</summary>
        ''' <param name="Detail">明細行</param>
        ''' <returns>True:無効行, False:有効行</returns>
        '''========================================================================================
        Protected Overrides Function Empty_Detail1(ByVal Detail As Object) As Boolean
            '------------------------------------------------------------------
            ' 変数定義
            '------------------------------------------------------------------
            'Dim row As SiftList.Detail1 = CType(Detail, DeliveryInput.Detail1)

            ''------------------------------------------------------------------
            '' 入力有は有効行
            ''------------------------------------------------------------------
            'Select Case True
            '    Case row.削除.Value = "1" : Return True
            '    Case Not BaseCore.Common.Text.IsEmptyOrZero(row.JANCD.Value) : Return False
            'End Select

            '------------------------------------------------------------------
            ' 無効行
            '------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "再計算"
        ''========================================================================================
        '' 合計値計算（明細）
        ''========================================================================================
        Private Function Calc_Detail()
            '----------------------------------------------------------------------
            ' 変数宣言
            '----------------------------------------------------------------------

            '----------------------------------------------------------------------
            ' 税率 取得
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetMasterTableAdapters.M区分TableAdapter
            Dim tbl As New NodeDatabase.DataSetMaster.M区分DataTable
            Dim qry As New Collection

            For i As Integer = 0 To Me.Fields.Detail1.Count - 1
                If Not Me.Empty_Detail1(Me.Fields.Detail1(i)) Then

                    'Me.Fields.Detail1(i).仕入金額.Value = NodeCore.Common.Logic.FormatPrice(BaseCore.Common.Text.CVal(Me.Fields.Detail1(i).仕入単価.Value) * BaseCore.Common.Text.CVal(Me.Fields.Detail1(i).数量.Value))

                    ''----------------------------------------------------------------------
                    '' 消費税額　算出
                    ''----------------------------------------------------------------------
                    'qry.Clear()
                    'qry.Add(New BaseDatabase.Condition(tbl.区分種別Column.ColumnName, NodeContents.Constant.CodeType.軽減税率対応区分, BaseDatabase.Contents.Compare.Equal))
                    'qry.Add(New BaseDatabase.Condition(tbl.区分IDColumn.ColumnName, Me.Fields.Detail1(i).軽減税率区分.Value, BaseDatabase.Contents.Compare.Equal))
                    'tbl = ada.SelectByCommon(qry)

                    'If tbl.Count > 0 Then
                    '    Dim strRate As Decimal = BaseCore.Common.Text.CVal(tbl(0).Item(tbl.汎用項目1Column.ColumnName))

                    '    Select Case True
                    '        Case Me.Fields.Detail1(i).消費税区分.Value = NodeContents.Constant.CodeValue.消費税区分.外税
                    '            Me.Fields.Detail1(i).消費税額.Value = NodeCore.Common.Logic.FormatPrice(BaseCore.Common.Text.CVal(Me.Fields.Detail1(i).仕入金額.Value) * BaseCore.Common.Text.CVal(strRate))
                    '            Me.Fields.Detail1(i).消費税率.Value = NodeCore.Common.Logic.FormatPrice(BaseCore.Common.Text.CVal(strRate) * 100)
                    '        Case Else
                    '            Me.Fields.Detail1(i).消費税額.Value = 0
                    '            Me.Fields.Detail1(i).消費税率.Value = 0
                    '    End Select
                    'End If
                End If
            Next

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#End Region

    End Class
End Namespace
