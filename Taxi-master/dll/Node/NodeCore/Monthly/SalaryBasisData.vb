﻿Namespace Frame.Monthly
    Partial Public Class SalaryBasisData
        Inherits NodeCore.Common.Frame

#Region "ﾌｨｰﾙﾄﾞ定義"
        Public Class Header
            ''' ----------------------------------------------------------------------------------------
            ''' <SUMMARY>列項目(基本)</SUMMARY>
            ''' ----------------------------------------------------------------------------------------
            Public グループCD As BaseCore.Common.Field.ItemData
            Public 個社CD As BaseCore.Common.Field.ItemData
            Public 子会社CD As BaseCore.Common.Field.ItemData
            Public 年月 As BaseCore.Common.Field.ItemData
            'Public 乗務員CD As BaseCore.Common.Field.ItemData
            'Public 乗務員名 As BaseCore.Common.Field.ItemData
            'Public シフト区分 As BaseCore.Common.Field.ItemData
            'Public シフト名 As BaseCore.Common.Field.ItemData
            'Public 基準乗務数 As BaseCore.Common.Field.ItemData
            'Public 公出 As BaseCore.Common.Field.ItemData
            'Public 実乗務数_合計 As BaseCore.Common.Field.ItemData
            'Public 差引数 As BaseCore.Common.Field.ItemData
            'Public 有給 As BaseCore.Common.Field.ItemData
            'Public 欠勤 As BaseCore.Common.Field.ItemData
            'Public 合計 As BaseCore.Common.Field.ItemData
            'Public 営収の合計 As BaseCore.Common.Field.ItemData
            'Public 信販の合計 As BaseCore.Common.Field.ItemData
            'Public 前月繰越 As BaseCore.Common.Field.ItemData
            Public 日付F As BaseCore.Common.Field.ItemData
            Public 日付T As BaseCore.Common.Field.ItemData

            '''========================================================================================
            ''' <SUMMARY>ｺﾝｽﾄﾗｸﾀ</SUMMARY>
            '''========================================================================================
            Public Sub New(ByVal Config As NodeCore.Common.Config)
                Dim tbl As New NodeDatabase.DataSetView.V0給与基準データDataTable

                '----------------------------------------------------------------------
                ' ｲﾝｽﾀﾝｽ(基本)
                '----------------------------------------------------------------------
                Me.グループCD = New BaseCore.Common.Field.ItemData(tbl.グループCDColumn)
                Me.個社CD = New BaseCore.Common.Field.ItemData(tbl.個社CDColumn)
                Me.子会社CD = New BaseCore.Common.Field.ItemData(tbl.子会社CDColumn)
                Me.年月 = New BaseCore.Common.Field.ItemData("年月", TypeCode.String, 7)
                'Me.乗務員CD = New BaseCore.Common.Field.ItemData(tbl.乗務員CDColumn)
                'Me.乗務員名 = New BaseCore.Common.Field.ItemData(tbl.乗務員名Column)
                'Me.シフト区分 = New BaseCore.Common.Field.ItemData(tbl.シフト区分Column)
                'Me.シフト名 = New BaseCore.Common.Field.ItemData(tbl.シフト名Column)
                'Me.基準乗務数 = New BaseCore.Common.Field.ItemData(tbl.基準乗務数Column)
                'Me.公出 = New BaseCore.Common.Field.ItemData(tbl.公出Column)
                'Me.実乗務数_合計 = New BaseCore.Common.Field.ItemData(tbl.実乗務数_合計Column)
                'Me.差引数 = New BaseCore.Common.Field.ItemData(tbl.差引数Column)
                'Me.有給 = New BaseCore.Common.Field.ItemData(tbl.有給Column)
                'Me.欠勤 = New BaseCore.Common.Field.ItemData(tbl.欠勤Column)
                'Me.合計 = New BaseCore.Common.Field.ItemData(tbl.合計Column)
                'Me.営収の合計 = New BaseCore.Common.Field.ItemData(tbl.営収の合計Column)
                'Me.信販の合計 = New BaseCore.Common.Field.ItemData(tbl.信販の合計Column)
                'Me.前月繰越 = New BaseCore.Common.Field.ItemData(tbl.前月繰越Column)
                Me.日付F = New BaseCore.Common.Field.ItemData("日付F", TypeCode.String, 10)
                Me.日付T = New BaseCore.Common.Field.ItemData("日付T", TypeCode.String, 10)
            End Sub
        End Class
#End Region

#Region "変数定義"
        Public Class Paramater
            Public Header As Header
        End Class

        '''========================================================================================
        ''' <summary>ﾍｯﾀﾞ</summary>
        '''========================================================================================
        Private _Fields As New Paramater
        Public Property Fields() As Paramater
            Get
                Return _Fields
            End Get

            Set(ByVal value As Paramater)
                _Fields = value
            End Set
        End Property
#End Region

#Region "ｺﾝｽﾄﾗｸﾀ"
        '''========================================================================================
        ''' <SUMMARY>ｺﾝｽﾄﾗｸﾀ</SUMMARY>
        ''' <PARAM name="Security">ｾｷｭﾘﾃｨ ｸﾗｽ</PARAM>
        ''' <PARAM name="Config">ｺﾝﾌｨｸﾞ ｸﾗｽ</PARAM>
        ''' <PARAM name="PagePath">ﾍﾟｰｼﾞﾊﾟｽ</PARAM>
        '''========================================================================================
        Public Sub New(ByVal Security As NodeCore.Common.Security, ByVal Config As NodeCore.Common.Config, ByVal PagePath As String)
            '----------------------------------------------------------------------
            ' ｾｷｭﾘﾃｨ 設定
            '----------------------------------------------------------------------
            MyBase.New(Security, Config, PagePath)

            Me.Fields.Header = New Header(Config)
        End Sub
#End Region

#Region "ﾍｯﾀﾞ"

#Region "Clear"
        '''========================================================================================
        ''' <SUMMARY>ｸﾘｱ</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Clear_Header(Optional ByVal KeyClear As Boolean = False) As Boolean
            '----------------------------------------------------------------------
            ' ｷｰ部ｸﾘｱ
            '----------------------------------------------------------------------
            If KeyClear Then
            End If

            '----------------------------------------------------------------------
            ' ﾃﾞｰﾀ部ｸﾘｱ
            '----------------------------------------------------------------------
            Me.Fields.Header.年月.Value = Format(Now.AddMonths(-1), "yyyy/MM")

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "MakeDataTable"
        '''========================================================================================
        ''' <SUMMARY>ﾃﾞｰﾀﾃｰﾌﾞﾙ生成</SUMMARY>
        ''' <RETURNS>ﾃﾞｰﾀﾃｰﾌﾞﾙ</RETURNS>
        '''========================================================================================
        Public Function MakeDataTable() As DataTable
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetFuncTableAdapters.F0給与基準データTableAdapter
            Dim tbl As New NodeDatabase.DataSetFunc.F0給与基準データDataTable
            Dim qry As New Collection

            '----------------------------------------------------------------------
            ' 事前準備
            '----------------------------------------------------------------------

            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, Me.Security.グループCD, BaseDatabase.Contents.Compare.Parameter))
            qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, Me.Security.個社CD, BaseDatabase.Contents.Compare.Parameter))
            qry.Add(New BaseDatabase.Condition(tbl.子会社CDColumn.ColumnName, Me.Security.子会社CD, BaseDatabase.Contents.Compare.Parameter))
            qry.Add(New BaseDatabase.Condition("日付F", Fields.Header.日付F.Value, BaseDatabase.Contents.Compare.Parameter))
            qry.Add(New BaseDatabase.Condition("日付T", Fields.Header.日付T.Value, BaseDatabase.Contents.Compare.Parameter))

            tbl = ada.SelectByCommon(qry)


            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return tbl
        End Function
#End Region

#Region "Execute:集計"
        '''========================================================================================
        ''' <SUMMARY>実行</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Execute_Header() As Boolean
            Me.LastError = Nothing


            '----------------------------------------------------------------------
            ' ｽﾄｱﾄﾞ実行
            '----------------------------------------------------------------------
            Try
                Dim adaStored As New NodeDatabase.DataSetStoredTableAdapters.P0給与基本データ集計TableAdapter
                Dim intStatus As Integer

                Using scope As New System.Transactions.TransactionScope(System.Transactions.TransactionScopeOption.Required, TimeSpan.Zero)
                    intStatus = adaStored.Execute(Fields.Header.グループCD.Value, Fields.Header.個社CD.Value, Fields.Header.子会社CD.Value, Fields.Header.日付F.Value, Fields.Header.日付T.Value, Fields.Header.日付F.Value.ToString().Substring(0, 7).Replace("/", ""))

                    '----------------------------------------------------------------------
                    ' ｴﾗｰ処理
                    '----------------------------------------------------------------------
                    If intStatus <> 0 Then
                        Me.LastError = "集計処理実行出来ませんでした！"
                        Return False
                    End If

                    scope.Complete()
                End Using

            Catch ex As Exception
                MyBase.LastError = "以下のエラーの為、処理を続行できません。" & vbCrLf & ex.Message
                Return False
            End Try

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "Execute1:Excel"
        '''========================================================================================
        ''' <SUMMARY>実行</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Execute1_Header() As Boolean
            '----------------------------------------------------------------------
            ' ﾃﾞｰﾀ抽出
            '----------------------------------------------------------------------
            Dim tbl As NodeDatabase.DataSetFunc.F0給与基準データDataTable = Me.MakeDataTable()

            '----------------------------------------------------------------------
            ' 必要ｶﾗﾑ以外を削除
            '----------------------------------------------------------------------
            Dim colRefresh As New BaseCore.Common.DataTable.ColumnRefresh

            colRefresh.Add(tbl.乗務員CDColumn.ColumnName, "乗務員CD")
            colRefresh.Add(tbl.氏名Column.ColumnName, "氏名")
            colRefresh.Add(tbl.シフト区分Column.ColumnName, "シフト区分")
            colRefresh.Add(tbl.区分名Column.ColumnName, "区分名")
            colRefresh.Add(tbl.シフト変更Column.ColumnName, "シフト変更")
            colRefresh.Add(tbl.基準乗務数Column.ColumnName, "基準乗務数")
            colRefresh.Add(tbl.通常Column.ColumnName, "通常")
            colRefresh.Add(tbl.公出Column.ColumnName, "公出")
            colRefresh.Add(tbl.実乗務数_合計Column.ColumnName, "実乗務数_合計")
            colRefresh.Add(tbl.差引数Column.ColumnName, "差引数")
            colRefresh.Add(tbl.有給Column.ColumnName, "有給")
            colRefresh.Add(tbl.欠勤Column.ColumnName, "欠勤")
            colRefresh.Add(tbl.合計Column.ColumnName, "合計")
            colRefresh.Add(tbl.前月繰越Column.ColumnName, "前月繰越")
            colRefresh.Add(tbl.営収の合計Column.ColumnName, "営収の合計")
            colRefresh.Add(tbl.信販の合計Column.ColumnName, "信販の合計")
            colRefresh.Execute(tbl)


            '----------------------------------------------------------------------
            ' ﾃﾞｰﾀ加工(前)
            '----------------------------------------------------------------------
            tbl.TableName = "給与基準データ"


            '----------------------------------------------------------------------
            ' ｴｸｽﾎﾟｰﾄ
            '----------------------------------------------------------------------
            Dim Excel As New BaseCore.Common.Excel
            If Not Excel.ExcelExport(tbl, Me.Path1) Then
                Me.LastError = Excel.LastError
                Return False
            End If

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region


#End Region
    End Class
End Namespace
