﻿Imports System.Globalization

Namespace Frame.Monthly
    Partial Public Class ShiftList
        Inherits NodeCore.Common.Frame

        Public intervalDays As Integer = 30
        Public sheetName As String = "シフト表"
        Public tplSheetName As String = "TPL"
        Public jyomuinSheetName As String = "乗務員データ"



#Region "ﾌｨｰﾙﾄﾞ定義"
        Public Class Header
            ''' ----------------------------------------------------------------------------------------
            ''' <SUMMARY>列項目(基本)</SUMMARY>
            ''' ----------------------------------------------------------------------------------------
            Public グループCD As BaseCore.Common.Field.ItemData
            Public 個社CD As BaseCore.Common.Field.ItemData
            Public 子会社CD As BaseCore.Common.Field.ItemData
            Public 日付 As BaseCore.Common.Field.ItemData
            Public 日付T As BaseCore.Common.Field.ItemData
            Public データ(31) As String
            Public 登録日時 As BaseCore.Common.Field.ItemData
            Public 登録ユーザーCD As BaseCore.Common.Field.ItemData
            Public 登録ユーザー名 As BaseCore.Common.Field.ItemData
            Public 更新日時 As BaseCore.Common.Field.ItemData
            Public 更新ユーザーCD As BaseCore.Common.Field.ItemData
            Public 更新ユーザー名 As BaseCore.Common.Field.ItemData

            'Public 日付1 As BaseCore.Common.Field.ItemData
            'Public 日付2 As BaseCore.Common.Field.ItemData
            'Public 日付3 As BaseCore.Common.Field.ItemData
            'Public 日付4 As BaseCore.Common.Field.ItemData
            'Public 日付5 As BaseCore.Common.Field.ItemData
            'Public 日付6 As BaseCore.Common.Field.ItemData
            'Public 日付7 As BaseCore.Common.Field.ItemData
            'Public 日付8 As BaseCore.Common.Field.ItemData
            'Public 日付9 As BaseCore.Common.Field.ItemData
            'Public 日付10 As BaseCore.Common.Field.ItemData
            'Public 日付11 As BaseCore.Common.Field.ItemData
            'Public 日付12 As BaseCore.Common.Field.ItemData
            'Public 日付13 As BaseCore.Common.Field.ItemData
            'Public 日付14 As BaseCore.Common.Field.ItemData
            'Public 日付15 As BaseCore.Common.Field.ItemData
            'Public 日付16 As BaseCore.Common.Field.ItemData
            'Public 日付17 As BaseCore.Common.Field.ItemData
            'Public 日付18 As BaseCore.Common.Field.ItemData
            'Public 日付19 As BaseCore.Common.Field.ItemData
            'Public 日付20 As BaseCore.Common.Field.ItemData
            'Public 日付21 As BaseCore.Common.Field.ItemData
            'Public 日付22 As BaseCore.Common.Field.ItemData
            'Public 日付23 As BaseCore.Common.Field.ItemData
            'Public 日付24 As BaseCore.Common.Field.ItemData
            'Public 日付25 As BaseCore.Common.Field.ItemData
            'Public 日付26 As BaseCore.Common.Field.ItemData
            'Public 日付27 As BaseCore.Common.Field.ItemData
            'Public 日付28 As BaseCore.Common.Field.ItemData
            'Public 日付29 As BaseCore.Common.Field.ItemData
            'Public 日付30 As BaseCore.Common.Field.ItemData
            'Public 日付31 As BaseCore.Common.Field.ItemData
            '''========================================================================================
            ''' <SUMMARY>ｺﾝｽﾄﾗｸﾀ</SUMMARY>
            '''========================================================================================
            Public Sub New(ByVal Config As NodeCore.Common.Config)
                Dim tbl As New NodeDatabase.DataSetTran.TシフトDataTable

                '----------------------------------------------------------------------
                ' ｲﾝｽﾀﾝｽ(基本) 
                '----------------------------------------------------------------------
                Me.グループCD = New BaseCore.Common.Field.ItemData(tbl.グループCDColumn)
                Me.個社CD = New BaseCore.Common.Field.ItemData(tbl.個社CDColumn)
                Me.子会社CD = New BaseCore.Common.Field.ItemData(tbl.子会社CDColumn)
                Me.日付 = New BaseCore.Common.Field.ItemData(tbl.日付Column)
                Me.日付T = New BaseCore.Common.Field.ItemData(tbl.日付Column)
                Me.登録日時 = New BaseCore.Common.Field.ItemData(tbl.登録年月日Column)
                Me.登録ユーザーCD = New BaseCore.Common.Field.ItemData(tbl.登録ユーザーCDColumn)
                Me.登録ユーザー名 = New BaseCore.Common.Field.ItemData(tbl.登録ユーザー名Column)
                Me.更新日時 = New BaseCore.Common.Field.ItemData(tbl.更新年月日Column)
                Me.更新ユーザーCD = New BaseCore.Common.Field.ItemData(tbl.更新ユーザーCDColumn)
                Me.更新ユーザー名 = New BaseCore.Common.Field.ItemData(tbl.更新ユーザー名Column)
                Me.データ = New String(31) {}
                For i As Integer = 0 To 30
                    Me.データ(i) = ""
                Next

                'Me.日付1 = New BaseCore.Common.Field.ItemData("日付1", TypeCode.String, 10)
                'Me.日付2 = New BaseCore.Common.Field.ItemData("日付2", TypeCode.String, 10)
                'Me.日付3 = New BaseCore.Common.Field.ItemData("日付3", TypeCode.String, 10)
                'Me.日付4 = New BaseCore.Common.Field.ItemData("日付4", TypeCode.String, 10)
                'Me.日付5 = New BaseCore.Common.Field.ItemData("日付5", TypeCode.String, 10)
                'Me.日付6 = New BaseCore.Common.Field.ItemData("日付6", TypeCode.String, 10)
                'Me.日付7 = New BaseCore.Common.Field.ItemData("日付7", TypeCode.String, 10)
                'Me.日付8 = New BaseCore.Common.Field.ItemData("日付8", TypeCode.String, 10)
                'Me.日付9 = New BaseCore.Common.Field.ItemData("日付9", TypeCode.String, 10)
                'Me.日付10 = New BaseCore.Common.Field.ItemData("日付10", TypeCode.String, 10)
                'Me.日付11 = New BaseCore.Common.Field.ItemData("日付11", TypeCode.String, 10)
                'Me.日付12 = New BaseCore.Common.Field.ItemData("日付12", TypeCode.String, 10)
                'Me.日付13 = New BaseCore.Common.Field.ItemData("日付13", TypeCode.String, 10)
                'Me.日付14 = New BaseCore.Common.Field.ItemData("日付14", TypeCode.String, 10)
                'Me.日付15 = New BaseCore.Common.Field.ItemData("日付15", TypeCode.String, 10)
                'Me.日付16 = New BaseCore.Common.Field.ItemData("日付16", TypeCode.String, 10)
                'Me.日付17 = New BaseCore.Common.Field.ItemData("日付17", TypeCode.String, 10)
                'Me.日付18 = New BaseCore.Common.Field.ItemData("日付18", TypeCode.String, 10)
                'Me.日付19 = New BaseCore.Common.Field.ItemData("日付19", TypeCode.String, 10)
                'Me.日付20 = New BaseCore.Common.Field.ItemData("日付20", TypeCode.String, 10)
                'Me.日付21 = New BaseCore.Common.Field.ItemData("日付21", TypeCode.String, 10)
                'Me.日付22 = New BaseCore.Common.Field.ItemData("日付22", TypeCode.String, 10)
                'Me.日付23 = New BaseCore.Common.Field.ItemData("日付23", TypeCode.String, 10)
                'Me.日付24 = New BaseCore.Common.Field.ItemData("日付24", TypeCode.String, 10)
                'Me.日付25 = New BaseCore.Common.Field.ItemData("日付25", TypeCode.String, 10)
                'Me.日付26 = New BaseCore.Common.Field.ItemData("日付26", TypeCode.String, 10)
                'Me.日付27 = New BaseCore.Common.Field.ItemData("日付27", TypeCode.String, 10)
                'Me.日付28 = New BaseCore.Common.Field.ItemData("日付28", TypeCode.String, 10)
                'Me.日付29 = New BaseCore.Common.Field.ItemData("日付29", TypeCode.String, 10)
                'Me.日付30 = New BaseCore.Common.Field.ItemData("日付30", TypeCode.String, 10)
                'Me.日付31 = New BaseCore.Common.Field.ItemData("日付31", TypeCode.String, 10)
            End Sub
        End Class
#End Region

#Region "明細(ﾃﾞｰﾀ項目)"
        Public Class Detail1
            Public シフト区分 As BaseCore.Common.Field.ItemData
            Public シフト区分名 As BaseCore.Common.Field.ItemData
            Public 行合計 As BaseCore.Common.Field.ItemData
            Public シフトデータ() As BaseCore.Common.Field.ItemData

            'Public 日付1 As BaseCore.Common.Field.ItemData
            'Public 日付2 As BaseCore.Common.Field.ItemData
            'Public 日付3 As BaseCore.Common.Field.ItemData
            'Public 日付4 As BaseCore.Common.Field.ItemData
            'Public 日付5 As BaseCore.Common.Field.ItemData
            'Public 日付6 As BaseCore.Common.Field.ItemData
            'Public 日付7 As BaseCore.Common.Field.ItemData
            'Public 日付8 As BaseCore.Common.Field.ItemData
            'Public 日付9 As BaseCore.Common.Field.ItemData
            'Public 日付10 As BaseCore.Common.Field.ItemData
            'Public 日付11 As BaseCore.Common.Field.ItemData
            'Public 日付12 As BaseCore.Common.Field.ItemData
            'Public 日付13 As BaseCore.Common.Field.ItemData
            'Public 日付14 As BaseCore.Common.Field.ItemData
            'Public 日付15 As BaseCore.Common.Field.ItemData
            'Public 日付16 As BaseCore.Common.Field.ItemData
            'Public 日付17 As BaseCore.Common.Field.ItemData
            'Public 日付18 As BaseCore.Common.Field.ItemData
            'Public 日付19 As BaseCore.Common.Field.ItemData
            'Public 日付20 As BaseCore.Common.Field.ItemData
            'Public 日付21 As BaseCore.Common.Field.ItemData
            'Public 日付22 As BaseCore.Common.Field.ItemData
            'Public 日付23 As BaseCore.Common.Field.ItemData
            'Public 日付24 As BaseCore.Common.Field.ItemData
            'Public 日付25 As BaseCore.Common.Field.ItemData
            'Public 日付26 As BaseCore.Common.Field.ItemData
            'Public 日付27 As BaseCore.Common.Field.ItemData
            'Public 日付28 As BaseCore.Common.Field.ItemData
            'Public 日付29 As BaseCore.Common.Field.ItemData
            'Public 日付30 As BaseCore.Common.Field.ItemData
            'Public 日付31 As BaseCore.Common.Field.ItemData
            '''========================================================================================
            ''' <SUMMARY>ｺﾝｽﾄﾗｸﾀ</SUMMARY>
            '''========================================================================================
            Public Sub New(ByVal Config As NodeCore.Common.Config)
                Dim tbl As New NodeDatabase.DataSetFunc.F0シフト一覧DataTable

                Me.シフト区分 = New BaseCore.Common.Field.ItemData(tbl.シフト区分Column, TypeCode.String)
                Me.シフト区分名 = New BaseCore.Common.Field.ItemData(tbl.区分名Column, TypeCode.String)
                Me.行合計 = New BaseCore.Common.Field.ItemData("行合計", TypeCode.UInt64)
                Me.シフトデータ = New BaseCore.Common.Field.ItemData(31) {}
                For i As Integer = 0 To 30
                    Me.シフトデータ(i) = New BaseCore.Common.Field.ItemData("SHIFT", TypeCode.String)
                Next

                'Me.日付1 = New BaseCore.Common.Field.ItemData("日付1", TypeCode.String, 10)
                'Me.日付2 = New BaseCore.Common.Field.ItemData("日付2", TypeCode.String, 10)
                'Me.日付3 = New BaseCore.Common.Field.ItemData("日付3", TypeCode.String, 10)
                'Me.日付4 = New BaseCore.Common.Field.ItemData("日付4", TypeCode.String, 10)
                'Me.日付5 = New BaseCore.Common.Field.ItemData("日付5", TypeCode.String, 10)
                'Me.日付6 = New BaseCore.Common.Field.ItemData("日付6", TypeCode.String, 10)
                'Me.日付7 = New BaseCore.Common.Field.ItemData("日付7", TypeCode.String, 10)
                'Me.日付8 = New BaseCore.Common.Field.ItemData("日付8", TypeCode.String, 10)
                'Me.日付9 = New BaseCore.Common.Field.ItemData("日付9", TypeCode.String, 10)
                'Me.日付10 = New BaseCore.Common.Field.ItemData("日付10", TypeCode.String, 10)
                'Me.日付11 = New BaseCore.Common.Field.ItemData("日付11", TypeCode.String, 10)
                'Me.日付12 = New BaseCore.Common.Field.ItemData("日付12", TypeCode.String, 10)
                'Me.日付13 = New BaseCore.Common.Field.ItemData("日付13", TypeCode.String, 10)
                'Me.日付14 = New BaseCore.Common.Field.ItemData("日付14", TypeCode.String, 10)
                'Me.日付15 = New BaseCore.Common.Field.ItemData("日付15", TypeCode.String, 10)
                'Me.日付16 = New BaseCore.Common.Field.ItemData("日付16", TypeCode.String, 10)
                'Me.日付17 = New BaseCore.Common.Field.ItemData("日付17", TypeCode.String, 10)
                'Me.日付18 = New BaseCore.Common.Field.ItemData("日付18", TypeCode.String, 10)
                'Me.日付19 = New BaseCore.Common.Field.ItemData("日付19", TypeCode.String, 10)
                'Me.日付20 = New BaseCore.Common.Field.ItemData("日付20", TypeCode.String, 10)
                'Me.日付21 = New BaseCore.Common.Field.ItemData("日付21", TypeCode.String, 10)
                'Me.日付22 = New BaseCore.Common.Field.ItemData("日付22", TypeCode.String, 10)
                'Me.日付23 = New BaseCore.Common.Field.ItemData("日付23", TypeCode.String, 10)
                'Me.日付24 = New BaseCore.Common.Field.ItemData("日付24", TypeCode.String, 10)
                'Me.日付25 = New BaseCore.Common.Field.ItemData("日付25", TypeCode.String, 10)
                'Me.日付26 = New BaseCore.Common.Field.ItemData("日付26", TypeCode.String, 10)
                'Me.日付27 = New BaseCore.Common.Field.ItemData("日付27", TypeCode.String, 10)
                'Me.日付28 = New BaseCore.Common.Field.ItemData("日付28", TypeCode.String, 10)
                'Me.日付29 = New BaseCore.Common.Field.ItemData("日付29", TypeCode.String, 10)
                'Me.日付30 = New BaseCore.Common.Field.ItemData("日付30", TypeCode.String, 10)
                'Me.日付31 = New BaseCore.Common.Field.ItemData("日付31", TypeCode.String, 10)
            End Sub
        End Class
#End Region

#Region "変数定義"
        Public Class Paramater
            Public Header As Header
            Public Detail1 As New List(Of Detail1)
        End Class

        '''========================================================================================
        ''' <summary>ﾍｯﾀﾞ</summary>
        '''========================================================================================
        Private _Fields As New Paramater
        Public Property Fields() As Paramater
            Get
                Return _Fields
            End Get

            Set(ByVal value As Paramater)
                _Fields = value
            End Set
        End Property
#End Region

#Region "ｺﾝｽﾄﾗｸﾀ"
        '''========================================================================================
        ''' <SUMMARY>ｺﾝｽﾄﾗｸﾀ</SUMMARY>
        ''' <PARAM name="Security">ｾｷｭﾘﾃｨ ｸﾗｽ</PARAM>
        ''' <PARAM name="Config">ｺﾝﾌｨｸﾞ ｸﾗｽ</PARAM>
        ''' <PARAM name="PagePath">ﾍﾟｰｼﾞﾊﾟｽ</PARAM>
        '''========================================================================================
        Public Sub New(ByVal Security As NodeCore.Common.Security, ByVal Config As NodeCore.Common.Config, ByVal PagePath As String)
            '----------------------------------------------------------------------
            ' ｾｷｭﾘﾃｨ 設定
            '----------------------------------------------------------------------
            MyBase.New(Security, Config, PagePath)

            Me.Fields.Header = New Header(Config)
        End Sub
#End Region

#Region "ﾍｯﾀﾞ"

#Region "Clear"
        '''========================================================================================
        ''' <SUMMARY>ｸﾘｱ</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Clear_Header(Optional ByVal KeyClear As Boolean = False) As Boolean
            '----------------------------------------------------------------------
            ' ｷｰ部ｸﾘｱ
            '----------------------------------------------------------------------
            If KeyClear Then
            End If

            '----------------------------------------------------------------------
            ' ﾃﾞｰﾀ部ｸﾘｱ
            '----------------------------------------------------------------------

            'Me.Fields.Header.日付.Value = Nothing

            '----------------------------------------------------------------------
            ' ﾃﾞｰﾀ部ｸﾘｱ
            '----------------------------------------------------------------------
            'Me.Read()

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#End Region

#Region "明細:詳細"
#Region "Clear"
        '''========================================================================================
        ''' <SUMMARY>ｸﾘｱ</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Clear_Detail1() As Boolean
            '----------------------------------------------------------------------
            ' ｸﾘｱ
            '----------------------------------------------------------------------
            For i As Integer = 0 To Me.Fields.Detail1.Count - 1
                Me.Clear_Detail1(i)
            Next

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function

        Public Overrides Function Clear_Detail1(ByVal Index As Integer) As Boolean

            Me.Fields.Detail1(Index).シフト区分.Value = Nothing
            Me.Fields.Detail1(Index).シフト区分名.Value = Nothing
            For intCol = 0 To 30
                Me.Fields.Detail1(Index).シフトデータ(intCol).Value = Nothing
            Next
            Me.Fields.Detail1(Index).行合計.Value = Nothing

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "Read"
        '''========================================================================================
        ''' <SUMMARY>読込</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Read_Detail1() As Boolean

            If Me.Fields.Header.日付.Value = "" Then
                Return False
            End If

            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetFuncTableAdapters.F0シフト一覧全体TableAdapter
            Dim tbl As New NodeDatabase.DataSetFunc.F0シフト一覧全体DataTable
            Dim qry As New Collection

            '----------------------------------------------------------------------
            ' 事前準備
            '----------------------------------------------------------------------
            qry.Clear()
            Dim dt As Date = Date.Parse(Me.Fields.Header.日付.Value & "/" & Me.Security.事業開始日).AddMonths(-1)
            Dim strDateF = dt.ToString("yyyy/MM/dd")
            Dim strDateT = dt.AddMonths(1).AddDays(-1).ToString("yyyy/MM/dd") 'Me.Fields.Header.日付T.Value

            qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, Me.Fields.Header.グループCD.Value, BaseDatabase.Contents.Compare.Parameter))
            qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, Me.Fields.Header.個社CD.Value, BaseDatabase.Contents.Compare.Parameter))
            qry.Add(New BaseDatabase.Condition(tbl.子会社CDColumn.ColumnName, Me.Fields.Header.子会社CD.Value, BaseDatabase.Contents.Compare.Parameter))
            qry.Add(New BaseDatabase.Condition("日付F", strDateF, BaseDatabase.Contents.Compare.Parameter))
            qry.Add(New BaseDatabase.Condition("日付T", strDateT, BaseDatabase.Contents.Compare.Parameter))

            Dim strSort As String = ""

            strSort &= tbl.シフト区分Column.ColumnName & " ASC"

            tbl = ada.SelectByCommon(qry, strSort)

            '----------------------------------------------------------------------
            ' ﾌﾚｰﾑ作成
            '----------------------------------------------------------------------
            Me.FrameMaker_Detail1(tbl)

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "Valid_Write"


#Region "Valid_Write"
        '''========================================================================================
        ''' <SUMMARY>検査(共通)</SUMMARY>
        ''' <RETURNS>True:正常, False:不正</RETURNS>
        '''========================================================================================
        Protected Overrides Function Valid_Write_Header_Common() As NodeContents.ActionButton.ErrorLevel
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim strMsg As String = ""
            Dim Validator As New BaseCore.Common.Validator

            '------------------------------------------------------------------
            ' ｴﾗｰｸﾘｱ
            '------------------------------------------------------------------
            'Me.Fields.Header.グループCD.IsError = False
            'Me.Fields.Header.個社CD.IsError = False
            'Me.Fields.Header.子会社CD.IsError = False
            'Me.Fields.Header.日付.IsError = False
            'Me.Fields.Header.日付T.IsError = False


            '------------------------------------------------------------------
            ' 共通検査
            '------------------------------------------------------------------
            'If Not Validator.BaseChecker(Me.Fields.Header.グループCD) Then : Me.Fields.Header.グループCD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            'If Not Validator.BaseChecker(Me.Fields.Header.個社CD) Then : Me.Fields.Header.個社CD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            'If Not Validator.BaseChecker(Me.Fields.Header.子会社CD) Then : Me.Fields.Header.子会社CD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            'If Not Validator.BaseChecker(Me.Fields.Header.日付) Then : Me.Fields.Header.日付.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            'If Not Validator.BaseChecker(Me.Fields.Header.日付T) Then : Me.Fields.Header.日付T.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If


            '------------------------------------------------------------------
            ' 返却
            '------------------------------------------------------------------
            MyBase.LastError = strMsg
            Select Case True
                Case strMsg = ""
                    Return NodeContents.ActionButton.ErrorLevel.None
                Case Else
                    Return NodeContents.ActionButton.ErrorLevel.Fatal
            End Select
        End Function
#End Region


        '''========================================================================================
        ''' <summary>検査</summary>
        ''' <RETURNS>True:正常, False:不正</RETURNS>
        '''========================================================================================
        Protected Overrides Function Valid_Write_Detail1_Common() As NodeContents.ActionButton.ErrorLevel
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim strMsg As String = ""
            Dim Validator As New BaseCore.Common.Validator

            '------------------------------------------------------------------
            ' ｴﾗｰｸﾘｱ
            '------------------------------------------------------------------
            For i As Integer = 0 To Me.Fields.Detail1.Count - 1
                'Me.Fields.Detail1(i).シフト区分.IsError = False
                'Me.Fields.Detail1(i)..IsError = False
                'Me.Fields.Detail1(i).JANCD.IsError = False
                'Me.Fields.Detail1(i).商品名.IsError = False
                'Me.Fields.Detail1(i).定価.IsError = False
                'Me.Fields.Detail1(i).数量.IsError = False
                'Me.Fields.Detail1(i).仕入単価.IsError = False
                'Me.Fields.Detail1(i).仕入金額.IsError = False
                'Me.Fields.Detail1(i).消費税額.IsError = False
                'Me.Fields.Detail1(i).消費税率.IsError = False
                'Me.Fields.Detail1(i).入数.IsError = False
            Next

            '------------------------------------------------------------------
            ' 共通検査
            '------------------------------------------------------------------
            For i As Integer = 0 To Me.Fields.Detail1.Count - 1
                If Not Me.Empty_Detail1(Me.Fields.Detail1(i)) Then
                    'If Not Validator.BaseChecker(Me.Fields.Detail1(i).削除) Then : Me.Fields.Detail1(i).削除.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                    'If Not Validator.BaseChecker(Me.Fields.Detail1(i).行NO) Then : Me.Fields.Detail1(i).行NO.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                    'If Not Validator.BaseChecker(Me.Fields.Detail1(i).JANCD) Then : Me.Fields.Detail1(i).JANCD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                    'If Not Validator.BaseChecker(Me.Fields.Detail1(i).商品名) Then : Me.Fields.Detail1(i).商品名.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                    'If Not Validator.BaseChecker(Me.Fields.Detail1(i).定価) Then : Me.Fields.Detail1(i).定価.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                    'If Not Validator.BaseChecker(Me.Fields.Detail1(i).数量) Then : Me.Fields.Detail1(i).数量.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                    'If Not Validator.BaseChecker(Me.Fields.Detail1(i).仕入単価) Then : Me.Fields.Detail1(i).仕入単価.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                    'If Not Validator.BaseChecker(Me.Fields.Detail1(i).仕入金額) Then : Me.Fields.Detail1(i).仕入金額.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                    'If Not Validator.BaseChecker(Me.Fields.Detail1(i).消費税額) Then : Me.Fields.Detail1(i).消費税額.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                    'If Not Validator.BaseChecker(Me.Fields.Detail1(i).消費税率) Then : Me.Fields.Detail1(i).消費税率.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                    'If Not Validator.BaseChecker(Me.Fields.Detail1(i).入数) Then : Me.Fields.Detail1(i).入数.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                End If
            Next

            '------------------------------------------------------------------
            ' 返却
            '------------------------------------------------------------------
            MyBase.LastError = strMsg
            Select Case True
                Case strMsg = ""
                    Return NodeContents.ActionButton.ErrorLevel.None
                Case Else
                    Return NodeContents.ActionButton.ErrorLevel.Fatal
            End Select
        End Function


#End Region

#Region "Valid_Delete"
        '''========================================================================================
        ''' <SUMMARY>検査(共通)</SUMMARY>
        ''' <RETURNS>True:正常, False:不正</RETURNS>
        '''========================================================================================
        Protected Overrides Function Valid_Delete_Detail1_Common() As NodeContents.ActionButton.ErrorLevel
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim strMsg As String = ""
            Dim Validator As New BaseCore.Common.Validator

            '------------------------------------------------------------------
            ' ｴﾗｰｸﾘｱ
            '------------------------------------------------------------------

            '------------------------------------------------------------------
            ' 共通検査
            '------------------------------------------------------------------

            '------------------------------------------------------------------
            ' 返却
            '------------------------------------------------------------------
            MyBase.LastError = strMsg
            Select Case True
                Case strMsg = ""
                    Return NodeContents.ActionButton.ErrorLevel.None
                Case Else
                    Return NodeContents.ActionButton.ErrorLevel.Fatal
            End Select
        End Function

        '''========================================================================================
        ''' <SUMMARY>検査(個別)</SUMMARY>
        ''' <RETURNS>True:正常, False:不正</RETURNS>
        '''========================================================================================
        Protected Overrides Function Valid_Delete_Detail1_Another() As NodeContents.ActionButton.ErrorLevel
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim strMsg As String = ""
            Dim Validator As New BaseCore.Common.Validator

            '------------------------------------------------------------------
            ' 返却
            '------------------------------------------------------------------
            MyBase.LastError = strMsg
            Select Case True
                Case strMsg = ""
                    Return NodeContents.ActionButton.ErrorLevel.None
                Case Else
                    Return NodeContents.ActionButton.ErrorLevel.Fatal
            End Select
        End Function
#End Region

#Region "Write"
        '''========================================================================================
        ''' <SUMMARY>書込</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Write_Detail1() As Boolean
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim strNow As String = Format(Now, NodeContents.Constant.System.DATE_TIME_FORMAT)

            Dim ada As New NodeDatabase.DataSetTranTableAdapters.TシフトTableAdapter
            Dim tbl As New NodeDatabase.DataSetTran.TシフトDataTable
            Dim qry As New Collection

            '----------------------------------------------------------------------
            ' 事前準備
            '----------------------------------------------------------------------
            Me.Fields.Header.更新日時.Value = strNow
            Me.Fields.Header.更新ユーザーCD.Value = Me.Security.ユーザーCD
            Me.Fields.Header.更新ユーザー名.Value = Me.Security.ユーザー名

            ' todo del log
            For i As Integer = 0 To Me.Fields.Detail1.Count - 1
                Dim dtl As Detail1 = Me.Fields.Detail1(i)
                For j As Integer = 0 To dtl.シフトデータ.Count - 1
                    If (dtl.シフトデータ(j) Is Nothing) Then
                        Continue For
                    End If
                    System.Diagnostics.Debug.WriteLine(" WRITE DTL: " & j.ToString & "," & Me.Fields.Header.データ(j) & "," & dtl.シフトデータ(j).Value.ToString)
                Next
            Next

            Try
                Using scope As New System.Transactions.TransactionScope(System.Transactions.TransactionScopeOption.Required, TimeSpan.Zero)
                    '----------------------------------------------------------------------
                    ' 保存
                    '----------------------------------------------------------------------
                    Try

                        For i As Integer = 0 To Me.Fields.Detail1.Count - 1

                            Dim dtl As Detail1 = Me.Fields.Detail1(i)

                            For j As Integer = 0 To dtl.シフトデータ.Count - 1

                                If (dtl.シフトデータ(j) Is Nothing) Then
                                    Continue For
                                End If

                                '----------------------------------------------------------------------
                                ' 条件設定
                                '----------------------------------------------------------------------
                                qry.Clear()
                                qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, Me.Fields.Header.グループCD.Value, BaseDatabase.Contents.Compare.Equal))
                                qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, Me.Fields.Header.個社CD.Value, BaseDatabase.Contents.Compare.Equal))
                                qry.Add(New BaseDatabase.Condition(tbl.子会社CDColumn.ColumnName, Me.Fields.Header.子会社CD.Value, BaseDatabase.Contents.Compare.Equal))
                                qry.Add(New BaseDatabase.Condition(tbl.シフト区分Column.ColumnName, dtl.シフト区分.Value, BaseDatabase.Contents.Compare.Equal))
                                qry.Add(New BaseDatabase.Condition(tbl.乗務員CDColumn.ColumnName, "0", BaseDatabase.Contents.Compare.Equal))
                                qry.Add(New BaseDatabase.Condition(tbl.日付Column.ColumnName, Me.Fields.Header.データ(j), BaseDatabase.Contents.Compare.Equal))


                                '----------------------------------------------------------------------
                                ' 読込
                                '----------------------------------------------------------------------
                                tbl = ada.SelectByCommon(qry)


                                '----------------------------------------------------------------------
                                ' 保存
                                '----------------------------------------------------------------------
                                If tbl.Count = 0 Then
                                    '----------------------------------------------------------------------
                                    ' 新規
                                    '----------------------------------------------------------------------
                                    Me.Fields.Header.登録日時.Value = strNow
                                    Me.Fields.Header.登録ユーザーCD.Value = Me.Security.ユーザーCD
                                    Me.Fields.Header.登録ユーザー名.Value = Me.Security.ユーザー名

                                    ada.Insert(Me.Fields.Header.グループCD.Value _
                                             , Me.Fields.Header.個社CD.Value _
                                             , Me.Fields.Header.子会社CD.Value _
                                             , Me.Fields.Header.データ(j) _
                                             , dtl.シフト区分.Value _
                                             , "0" _
                                             , "0" _
                                             , Val(dtl.シフトデータ(j).Value) _
                                             , Me.Fields.Header.登録日時.Value _
                                             , Me.Fields.Header.登録ユーザーCD.Value _
                                             , Me.Fields.Header.登録ユーザー名.Value _
                                             , Me.Fields.Header.更新日時.Value _
                                             , Me.Fields.Header.更新ユーザーCD.Value _
                                             , Me.Fields.Header.更新ユーザー名.Value
                                            )
                                Else
                                    '----------------------------------------------------------------------
                                    ' 訂正
                                    '----------------------------------------------------------------------
                                    tbl(0).Item(tbl.シフトColumn.ColumnName) = Val(dtl.シフトデータ(j).Value)
                                    tbl(0).Item(tbl.更新年月日Column.ColumnName) = Me.Fields.Header.更新日時.Value
                                    tbl(0).Item(tbl.更新ユーザーCDColumn.ColumnName) = Me.Fields.Header.更新ユーザーCD.Value
                                    tbl(0).Item(tbl.更新ユーザー名Column.ColumnName) = Me.Fields.Header.更新ユーザー名.Value

                                    ada.Update(tbl)
                                End If


                            Next
                        Next

                    Catch ex As Exception
                        MyBase.LastError = "以下のエラーのため、追加できませんでした。" & vbCrLf & ex.Message
                        Return False
            End Try
            scope.Complete()
            End Using


            Catch ex As Exception
                MyBase.LastError = "以下のエラーのため、保存できませんでした。" & vbCrLf & ex.Message
                Return False
            End Try


            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "Delete"
        '''========================================================================================
        ''' <SUMMARY>削除</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Delete_Detail1() As Boolean
            ''----------------------------------------------------------------------
            '' 変数定義
            ''----------------------------------------------------------------------
            'Dim ada As New NodeDatabase.DataSetTranTableAdapters.T納品TableAdapter
            'Dim tbl As New NodeDatabase.DataSetTran.T納品DataTable
            'Dim qry As New Collection

            ''----------------------------------------------------------------------
            '' T納品
            ''----------------------------------------------------------------------
            'qry.Clear()
            'qry.Add(New BaseDatabase.Condition(tbl.仕入先CDColumn.ColumnName, Me.Fields.Header.仕入先CD.Value, BaseDatabase.Contents.Compare.Equal))
            'qry.Add(New BaseDatabase.Condition(tbl.店舗CDColumn.ColumnName, Me.Fields.Header.店舗CD.Value, BaseDatabase.Contents.Compare.Equal))
            'qry.Add(New BaseDatabase.Condition(tbl.納品日付Column.ColumnName, Me.Fields.Header.納品日付.Value, BaseDatabase.Contents.Compare.Equal))
            'qry.Add(New BaseDatabase.Condition(tbl.伝票番号Column.ColumnName, Me.Fields.Header.伝票番号.Value, BaseDatabase.Contents.Compare.Equal))

            'Try
            '    Using scope As New System.Transactions.TransactionScope(System.Transactions.TransactionScopeOption.Required, TimeSpan.Zero)
            '        ada.DeleteByCommon(qry)
            '        scope.Complete()
            '    End Using
            'Catch ex As Exception
            '    MyBase.LastError = "以下のエラーのため、削除できませんでした。" & vbCrLf & ex.Message
            '    Return False
            'End Try

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "MakeDataTable"
        '''========================================================================================
        ''' <SUMMARY>ﾃﾞｰﾀﾃｰﾌﾞﾙ生成</SUMMARY>
        ''' <RETURNS>ﾃﾞｰﾀﾃｰﾌﾞﾙ</RETURNS>
        '''========================================================================================
        Public Function MakeDataTable() As DataTable

            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetFuncTableAdapters.F0シフト一覧全体TableAdapter
            Dim tbl As New NodeDatabase.DataSetFunc.F0シフト一覧全体DataTable
            Dim qry As New Collection

            '----------------------------------------------------------------------
            ' 事前準備
            '----------------------------------------------------------------------
            qry.Clear()
            'Dim dt As Date = Date.Parse(Me.Fields.Header.日付.Value & "/" & Me.Security.事業開始日)
            'Dim strDateF = dt.ToString("yyyy/MM/dd")
            'Dim strDateT = Me.Fields.Header.日付T.Value

            Dim dt As Date = Date.Parse(Me.Fields.Header.日付.Value & "/" & Me.Security.事業開始日).AddMonths(-1)
            Dim strDateF = dt.ToString("yyyy/MM/dd")
            Dim strDateT = dt.AddMonths(1).AddDays(-1).ToString("yyyy/MM/dd") 'Me.Fields.Header.日付T.Value

            qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, Me.Fields.Header.グループCD.Value, BaseDatabase.Contents.Compare.Parameter))
            qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, Me.Fields.Header.個社CD.Value, BaseDatabase.Contents.Compare.Parameter))
            qry.Add(New BaseDatabase.Condition(tbl.子会社CDColumn.ColumnName, Me.Fields.Header.子会社CD.Value, BaseDatabase.Contents.Compare.Parameter))
            qry.Add(New BaseDatabase.Condition("日付F", strDateF, BaseDatabase.Contents.Compare.Parameter))
            qry.Add(New BaseDatabase.Condition("日付T", strDateT, BaseDatabase.Contents.Compare.Parameter))

            Dim strSort As String = ""

            strSort &= tbl.シフト区分Column.ColumnName & " ASC"

            tbl = ada.SelectByCommon(qry, strSort)

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return tbl
        End Function
#End Region

#Region "エクセルデータ作成"

        Public Function MakeJouminData(ByRef xls As BaseCore.Common.Excel, ByRef posRow As Integer, ByRef posCol As Integer, ByRef kubunId As String, ByRef kubunName As String) As Boolean

            Dim ada乗務員 As New NodeDatabase.DataSetFuncTableAdapters.F0乗務員シフト区分TableAdapter
            Dim tbl乗務員 As New NodeDatabase.DataSetFunc.F0乗務員シフト区分DataTable
            Dim qry乗務員 As New Collection

            qry乗務員.Clear()
            qry乗務員.Add(New BaseDatabase.Condition(tbl乗務員.グループCDColumn.ColumnName, Me.Fields.Header.グループCD.Value, BaseDatabase.Contents.Compare.Parameter))
            qry乗務員.Add(New BaseDatabase.Condition(tbl乗務員.個社CDColumn.ColumnName, Me.Fields.Header.個社CD.Value, BaseDatabase.Contents.Compare.Parameter))
            qry乗務員.Add(New BaseDatabase.Condition(tbl乗務員.子会社CDColumn.ColumnName, Me.Fields.Header.子会社CD.Value, BaseDatabase.Contents.Compare.Parameter))
            qry乗務員.Add(New BaseDatabase.Condition("区分", kubunId, BaseDatabase.Contents.Compare.Parameter))

            tbl乗務員 = ada乗務員.SelectByCommon(qry乗務員)

            xls.CopyAndPaste(tplSheetName, 1, 1, 43, 6, jyomuinSheetName, posRow, posCol)
            xls.SheetSelect(jyomuinSheetName)

            xls.CellSetValue(posRow + 1, posCol, kubunName)

            If tbl乗務員.Count > 0 Then
                Dim i As Integer = 0
                For Each r As NodeDatabase.DataSetFunc.F0乗務員シフト区分Row In tbl乗務員.Rows
                    xls.CellSetValue(posRow + i + 1, posCol + 2, r.社員NO)
                    xls.CellSetValue(posRow + i + 1, posCol + 4, r.乗務員名)
                    i += 1
                Next
            End If

            Return True
        End Function


        '''========================================================================================
        ''' <SUMMARY>抽出したﾃｰﾌﾞﾙに編集を加える</SUMMARY>   
        '''========================================================================================
        Public Function MakeExcelData(ByRef tbl As NodeDatabase.DataSetFunc.F0シフト一覧全体DataTable, ByRef xls As BaseCore.Common.Excel) As Boolean
            '------------------------------------------------------------------------
            ' 変数定義
            '------------------------------------------------------------------------
            Dim intStartCol As Integer = 3
            Dim intStartRow As Integer = 5

            Dim intStartRowJoumuIn As Integer = 1
            Dim intStartColJoumuIn As Integer = 1

            Try
                xls.InsertRow(sheetName, 6, tbl.Count - 1)
                For i As Integer = 0 To tbl.Count - 2
                    xls.CopyAndPaste(sheetName, intStartRow, 1, intStartRow, 35, sheetName, intStartRow + 1 + i, 1)
                Next

                For i As Integer = 0 To tbl.Count - 1

                    MakeJouminData(xls, intStartRowJoumuIn, intStartColJoumuIn, tbl(i).Item(tbl.シフト区分Column.ColumnName), tbl(i).Item(tbl.区分名Column.ColumnName))
                    intStartColJoumuIn += 7
                    xls.SheetSelect(sheetName)


                    xls.CellSetValue(4 + i, 1, tbl(i).Item(tbl.区分名Column.ColumnName))

                    Dim strVal As String = tbl(i).Item(tbl.日付Column.ColumnName).ToString()

                    If strVal <> "" Then
                        Dim tmpDates() As String = tbl(i).Item(tbl.日付Column.ColumnName).ToString.Split(",")
                        Dim tmpValues() As String = tbl(i).Item(tbl.シフトColumn.ColumnName).ToString.Split(",")

                        'Dim strDateF As Date = Date.Parse(Me.Fields.Header.日付.Value & "/" & Me.Security.事業開始日)
                        'Dim strDateT = Me.Fields.Header.日付T.Value
                        'Dim daysDifference As Integer = DateDiff(DateInterval.Day, strDateF, strDateT)

                        For j As Integer = 0 To tmpDates.Length - 1

                            If i = 0 Then
                                xls.CellSetValue(2, 3 + j, tmpDates(j))
                            End If

                            Select Case Val(tmpValues(j))
                                Case 1
                                    xls.CellSetValue(intStartRow - 1 + i, intStartCol + j, "◎")
                                Case 2
                                    xls.CellSetValue(intStartRow - 1 + i, intStartCol + j, "●")
                                Case 3
                                    xls.CellSetValue(intStartRow - 1 + i, intStartCol + j, "×")
                                Case Else
                                    xls.CellSetValue(intStartRow - 1 + i, intStartCol + j, "")
                            End Select

                        Next
                    End If

                Next

            Catch ex As Exception
                Me.LastError = ex.Message
                Return False
            Finally
            End Try
            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "Execute1:Excel"
        '''========================================================================================
        ''' <SUMMARY>実行</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Execute1_Header() As Boolean

            Me.LastError = Nothing
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim xls As New BaseCore.Common.Excel

            Try
                '----------------------------------------------------------------------
                ' ﾃﾞｰﾀ抽出
                '----------------------------------------------------------------------
                Dim tbl As DataTable = Me.MakeDataTable()

                ''----------------------------------------------------------------------
                '' ｾﾞﾛ件
                ''----------------------------------------------------------------------
                If tbl.Rows.Count = 0 Then
                    Me.LastError = "指定された条件に合致するデータはありません。"
                    Return False
                End If

                '----------------------------------------------------------------------
                ' ｵｰﾌﾟﾝ
                '----------------------------------------------------------------------
                If Not xls.Open(Me.Path1) Then
                    Me.LastError = "テンプレートが見つかりません。(" & Me.Path1 & ")"
                    Return False
                End If

                '------------------------------------------------------------------------
                ' 作成
                '------------------------------------------------------------------------
                xls.SheetSelect(sheetName)       'ｱｸﾃｨﾌﾞｼｰﾄ

                If Not MakeExcelData(tbl, xls) Then
                    MyBase.LastError = Me.LastError
                    Return False
                End If

                '----------------------------------------------------------------------
                ' 先頭へｶｰｿﾙ移動
                '----------------------------------------------------------------------
                xls.CellGet(0, 0)

                xls.SheetDelete("TPL")
                xls.SheetSelect(sheetName)

                If (xls.Sheet.Range("AH3").Text = "") Then
                    xls.Sheet.Range("AH:AH").Delete()
                End If
                If (xls.Sheet.Range("AG3").Text = "") Then
                    xls.Sheet.Range("AG:AG").Delete()
                End If
                If (xls.Sheet.Range("AF3").Text = "") Then
                    xls.Sheet.Range("AF:AF").Delete()
                End If

                xls.Close(Me.Path1)
            Catch ex As Exception
                Me.LastError = ex.Message
                Return False
            Finally
            End Try

            ''----------------------------------------------------------------------
            '' 正常終了
            ''----------------------------------------------------------------------

            Return True
        End Function
#End Region

#Region "ﾌﾚｰﾑ関連"
#Region "ﾌﾚｰﾑ作成"
        '''========================================================================================
        ''' <SUMMARY>ﾌﾚｰﾑ作成</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Function FrameMaker_Detail1(ByVal tbl As NodeDatabase.DataSetFunc.F0シフト一覧全体DataTable) As Boolean
            '----------------------------------------------------------------------
            ' ﾌﾚｰﾑ生成
            '----------------------------------------------------------------------
            Me.Add_Detail1(tbl.Count)

            '----------------------------------------------------------------------
            ' 値設定
            '----------------------------------------------------------------------
            For i As Integer = 0 To tbl.Count - 1
                Me.Fields.Detail1(i).シフト区分.Value = tbl(i).Item(tbl.シフト区分Column.ColumnName)
                Me.Fields.Detail1(i).シフト区分名.Value = tbl(i).Item(tbl.区分名Column.ColumnName)

                Dim strVal As String = tbl(i).Item(tbl.日付Column.ColumnName).ToString()

                If strVal <> "" Then
                    Dim tmpDates() As String = tbl(i).Item(tbl.日付Column.ColumnName).ToString.Split(",")
                    Dim tmpValues() As String = tbl(i).Item(tbl.シフトColumn.ColumnName).ToString.Split(",")

                    Dim strDateF As Date = Date.Parse(Me.Fields.Header.日付.Value & "/" & Me.Security.事業開始日).AddMonths(-1)
                    Dim strDateT As Date = strDateF.AddMonths(1).AddDays(-1).ToString("yyyy/MM/dd") 'Me.Fields.Header.日付T.Value
                    'Dim strDateT = Me.Fields.Header.日付T.Value

                    Dim daysDifference As Integer = DateDiff(DateInterval.Day, strDateF, strDateT)


                    For j As Integer = 0 To daysDifference

                        For jj = 0 To tmpDates.Length - 1
                            If Fields.Header.データ(j) = tmpDates(jj) Then
                                Me.Fields.Detail1(i).シフトデータ(j).Value = Val(tmpValues(jj))
                            End If
                        Next

                    Next
                End If

            Next

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "行挿入"

        '''========================================================================================
        ''' <summary>複数行挿入</summary>
        '''========================================================================================
        Public Overrides Sub Insert_Detail1(ByVal Position As Integer, ByVal Count As Integer)
            '----------------------------------------------------------------------
            ' 行挿入 & 行ｸﾘｱ
            '----------------------------------------------------------------------
            For i As Integer = Position To Position + Count - 1
                'Me.Fields.Detail1.Insert(Position, New DeliveryInput.Detail1(Me.Config))
                Me.Clear_Detail1(Position)
            Next

            '----------------------------------------------------------------------
            ' 行NOの再付番
            '----------------------------------------------------------------------
            Me.ReNum_Detail1()
        End Sub
#End Region

#Region "行追加"
        '''========================================================================================
        ''' <summary>単一行追加</summary>
        '''========================================================================================
        Public Overrides Sub Add_Detail1()
            Me.Fields.Detail1.Add(New Detail1(Me.Config))
            Me.Clear_Detail1(Me.Fields.Detail1.Count - 1)
        End Sub

        '''========================================================================================
        ''' <summary>一括行追加</summary>
        ''' <param name="AddCount">追加行数</param>
        '''========================================================================================
        Public Overrides Sub Add_Detail1(ByVal AddCount As Integer)
            Me.Fields.Detail1.Clear()

            For i As Integer = 0 To AddCount - 1
                Me.Add_Detail1()
            Next
        End Sub
#End Region

#Region "行削除"
        '''========================================================================================
        ''' <summary>一括行削除</summary>
        '''========================================================================================
        Public Overrides Sub Del_Detail1()
            Me.Fields.Detail1.Clear()
        End Sub
#End Region

#End Region

#Region "無効行判定"
        '''========================================================================================
        ''' <summary>無効行の判定</summary>
        ''' <param name="Detail">明細行</param>
        ''' <returns>True:無効行, False:有効行</returns>
        '''========================================================================================
        Protected Overrides Function Empty_Detail1(ByVal Detail As Object) As Boolean
            '------------------------------------------------------------------
            ' 変数定義
            '------------------------------------------------------------------
            'Dim row As SiftList.Detail1 = CType(Detail, DeliveryInput.Detail1)

            ''------------------------------------------------------------------
            '' 入力有は有効行
            ''------------------------------------------------------------------
            'Select Case True
            '    Case row.削除.Value = "1" : Return True
            '    Case Not BaseCore.Common.Text.IsEmptyOrZero(row.JANCD.Value) : Return False
            'End Select

            '------------------------------------------------------------------
            ' 無効行
            '------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "再計算"
        ''========================================================================================
        '' 合計値計算（明細）
        ''========================================================================================
        Private Function Calc_Detail()
            '----------------------------------------------------------------------
            ' 変数宣言
            '----------------------------------------------------------------------

            '----------------------------------------------------------------------
            ' 税率 取得
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetMasterTableAdapters.M区分TableAdapter
            Dim tbl As New NodeDatabase.DataSetMaster.M区分DataTable
            Dim qry As New Collection

            For i As Integer = 0 To Me.Fields.Detail1.Count - 1
                If Not Me.Empty_Detail1(Me.Fields.Detail1(i)) Then

                    'Me.Fields.Detail1(i).仕入金額.Value = NodeCore.Common.Logic.FormatPrice(BaseCore.Common.Text.CVal(Me.Fields.Detail1(i).仕入単価.Value) * BaseCore.Common.Text.CVal(Me.Fields.Detail1(i).数量.Value))

                    ''----------------------------------------------------------------------
                    '' 消費税額　算出
                    ''----------------------------------------------------------------------
                    'qry.Clear()
                    'qry.Add(New BaseDatabase.Condition(tbl.区分種別Column.ColumnName, NodeContents.Constant.CodeType.軽減税率対応区分, BaseDatabase.Contents.Compare.Equal))
                    'qry.Add(New BaseDatabase.Condition(tbl.区分IDColumn.ColumnName, Me.Fields.Detail1(i).軽減税率区分.Value, BaseDatabase.Contents.Compare.Equal))
                    'tbl = ada.SelectByCommon(qry)

                    'If tbl.Count > 0 Then
                    '    Dim strRate As Decimal = BaseCore.Common.Text.CVal(tbl(0).Item(tbl.汎用項目1Column.ColumnName))

                    '    Select Case True
                    '        Case Me.Fields.Detail1(i).消費税区分.Value = NodeContents.Constant.CodeValue.消費税区分.外税
                    '            Me.Fields.Detail1(i).消費税額.Value = NodeCore.Common.Logic.FormatPrice(BaseCore.Common.Text.CVal(Me.Fields.Detail1(i).仕入金額.Value) * BaseCore.Common.Text.CVal(strRate))
                    '            Me.Fields.Detail1(i).消費税率.Value = NodeCore.Common.Logic.FormatPrice(BaseCore.Common.Text.CVal(strRate) * 100)
                    '        Case Else
                    '            Me.Fields.Detail1(i).消費税額.Value = 0
                    '            Me.Fields.Detail1(i).消費税率.Value = 0
                    '    End Select
                    'End If
                End If
            Next

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#End Region

    End Class
End Namespace
