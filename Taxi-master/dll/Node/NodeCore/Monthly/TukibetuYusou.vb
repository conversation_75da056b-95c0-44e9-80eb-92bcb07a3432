﻿Imports System.Globalization

Namespace Frame.Monthly
    Partial Public Class TukibetuYusou
        Inherits NodeCore.Common.Frame

        Public sheetName As String = "入力用"
        Public sheetNameEx As String = "提出用"



#Region "ﾌｨｰﾙﾄﾞ定義"
        Public Class Header
            ''' ----------------------------------------------------------------------------------------
            ''' <SUMMARY>列項目(基本)</SUMMARY>
            ''' ----------------------------------------------------------------------------------------
            Public グループCD As BaseCore.Common.Field.ItemData
            Public 個社CD As BaseCore.Common.Field.ItemData
            Public 子会社CD As BaseCore.Common.Field.ItemData
            Public 日付 As BaseCore.Common.Field.ItemData
            Public 日付T As BaseCore.Common.Field.ItemData
            Public 登録日時 As BaseCore.Common.Field.ItemData
            Public 登録ユーザーCD As BaseCore.Common.Field.ItemData
            Public 登録ユーザー名 As BaseCore.Common.Field.ItemData
            Public 更新日時 As BaseCore.Common.Field.ItemData
            Public 更新ユーザーCD As BaseCore.Common.Field.ItemData
            Public 更新ユーザー名 As BaseCore.Common.Field.ItemData
            '''========================================================================================
            ''' <SUMMARY>ｺﾝｽﾄﾗｸﾀ</SUMMARY>
            '''========================================================================================
            Public Sub New(ByVal Config As NodeCore.Common.Config)
                Dim tbl As New NodeDatabase.DataSetTran.T乗務記録HDDataTable

                '----------------------------------------------------------------------
                ' ｲﾝｽﾀﾝｽ(基本) 
                '----------------------------------------------------------------------
                Me.グループCD = New BaseCore.Common.Field.ItemData(tbl.グループCDColumn)
                Me.個社CD = New BaseCore.Common.Field.ItemData(tbl.個社CDColumn)
                Me.子会社CD = New BaseCore.Common.Field.ItemData(tbl.子会社CDColumn, 1)
                Me.日付 = New BaseCore.Common.Field.ItemData(tbl.日報日付Column)
                Me.日付T = New BaseCore.Common.Field.ItemData(tbl.日報日付Column)
                Me.登録日時 = New BaseCore.Common.Field.ItemData(tbl.登録日時Column)
                Me.登録ユーザーCD = New BaseCore.Common.Field.ItemData(tbl.登録ユーザーCDColumn)
                Me.登録ユーザー名 = New BaseCore.Common.Field.ItemData(tbl.登録ユーザー名Column)
                Me.更新日時 = New BaseCore.Common.Field.ItemData(tbl.更新日時Column)
                Me.更新ユーザーCD = New BaseCore.Common.Field.ItemData(tbl.更新ユーザーCDColumn)
                Me.更新ユーザー名 = New BaseCore.Common.Field.ItemData(tbl.更新ユーザー名Column)

            End Sub
        End Class
#End Region

#Region "変数定義"
        Public Class Paramater
            Public Header As Header
        End Class

        '''========================================================================================
        ''' <summary>ﾍｯﾀﾞ</summary>
        '''========================================================================================
        Private _Fields As New Paramater
        Public Property Fields() As Paramater
            Get
                Return _Fields
            End Get

            Set(ByVal value As Paramater)
                _Fields = value
            End Set
        End Property
#End Region

#Region "ｺﾝｽﾄﾗｸﾀ"
        '''========================================================================================
        ''' <SUMMARY>ｺﾝｽﾄﾗｸﾀ</SUMMARY>
        ''' <PARAM name="Security">ｾｷｭﾘﾃｨ ｸﾗｽ</PARAM>
        ''' <PARAM name="Config">ｺﾝﾌｨｸﾞ ｸﾗｽ</PARAM>
        ''' <PARAM name="PagePath">ﾍﾟｰｼﾞﾊﾟｽ</PARAM>
        '''========================================================================================
        Public Sub New(ByVal Security As NodeCore.Common.Security, ByVal Config As NodeCore.Common.Config, ByVal PagePath As String)
            '----------------------------------------------------------------------
            ' ｾｷｭﾘﾃｨ 設定
            '----------------------------------------------------------------------
            MyBase.New(Security, Config, PagePath)

            Me.Fields.Header = New Header(Config)
        End Sub
#End Region

#Region "ﾍｯﾀﾞ"

#Region "Clear"
        '''========================================================================================
        ''' <SUMMARY>ｸﾘｱ</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Clear_Header(Optional ByVal KeyClear As Boolean = False) As Boolean
            '----------------------------------------------------------------------
            ' ｷｰ部ｸﾘｱ
            '----------------------------------------------------------------------
            If KeyClear Then
            End If

            '----------------------------------------------------------------------
            ' ﾃﾞｰﾀ部ｸﾘｱ
            '----------------------------------------------------------------------

            'Me.Fields.Header.日付.Value = Nothing

            '----------------------------------------------------------------------
            ' ﾃﾞｰﾀ部ｸﾘｱ
            '----------------------------------------------------------------------
            'Me.Read()

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "MakeDataTable"
        '''========================================================================================
        ''' <SUMMARY>ﾃﾞｰﾀﾃｰﾌﾞﾙ生成</SUMMARY>
        ''' <RETURNS>ﾃﾞｰﾀﾃｰﾌﾞﾙ</RETURNS>
        '''========================================================================================
        Public Function MakeDataTable() As DataTable
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetFuncTableAdapters.F0月別輸送実績TableAdapter
            Dim tbl As New NodeDatabase.DataSetFunc.F0月別輸送実績DataTable
            Dim qry As New Collection

            '----------------------------------------------------------------------
            ' 事前準備
            '----------------------------------------------------------------------

            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, Me.Security.グループCD, BaseDatabase.Contents.Compare.Parameter))
            qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, Me.Security.個社CD, BaseDatabase.Contents.Compare.Parameter))
            qry.Add(New BaseDatabase.Condition(tbl.子会社CDColumn.ColumnName, Me.Security.子会社CD, BaseDatabase.Contents.Compare.Parameter))
            qry.Add(New BaseDatabase.Condition("日付F", Me.Fields.Header.日付.Value, BaseDatabase.Contents.Compare.Parameter))
            qry.Add(New BaseDatabase.Condition("日付T", Me.Fields.Header.日付T.Value, BaseDatabase.Contents.Compare.Parameter))

            tbl = ada.SelectByCommon(qry)


            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return tbl
        End Function
#End Region

#Region "エクセルデータ作成"

        '''========================================================================================
        ''' <SUMMARY>抽出したﾃｰﾌﾞﾙに編集を加える</SUMMARY>   
        '''========================================================================================
        Public Function MakeExcelData(ByRef tbl As NodeDatabase.DataSetFunc.F0月別輸送実績DataTable, ByRef xls As BaseCore.Common.Excel) As Boolean
            '------------------------------------------------------------------------
            ' 変数定義
            '------------------------------------------------------------------------

            Try



                '----------------------------------------------------------------------
                ' 条件設定
                '----------------------------------------------------------------------
                Dim ada子会社 As New NodeDatabase.DataSetViewTableAdapters.V0子会社TableAdapter
                Dim tbl子会社 As New NodeDatabase.DataSetView.V0子会社DataTable
                Dim qry子会社 As New Collection

                qry子会社.Clear()
                qry子会社.Add(New BaseDatabase.Condition(tbl子会社.グループCDColumn.ColumnName, Me.Fields.Header.グループCD.Value, BaseDatabase.Contents.Compare.Equal))
                qry子会社.Add(New BaseDatabase.Condition(tbl子会社.個社CDColumn.ColumnName, Me.Fields.Header.個社CD.Value, BaseDatabase.Contents.Compare.Equal))
                qry子会社.Add(New BaseDatabase.Condition(tbl子会社.子会社CDColumn.ColumnName, Me.Fields.Header.子会社CD.Value, BaseDatabase.Contents.Compare.Equal))

                '----------------------------------------------------------------------
                ' 読込
                '----------------------------------------------------------------------
                Dim strJigyouCD = ""
                Dim strCompanyName = ""
                tbl子会社 = ada子会社.SelectByCommon(qry子会社)
                If tbl子会社.Count > 0 Then
                    strCompanyName = tbl子会社(0).Item(tbl子会社.子会社名Column.ColumnName)
                    strJigyouCD = tbl子会社(0).Item(tbl子会社.事業者CDColumn.ColumnName)
                End If

                '----------------------------------------------------------------------
                ' XLS書込
                '----------------------------------------------------------------------

                xls.SheetSelect(sheetName)

                Dim initDt As Date = Date.Parse(Me.Fields.Header.日付.Value)
                xls.Sheet.Range("H3").Value = initDt.ToString("yyyy")
                xls.Sheet.Range("J3").Value = initDt.ToString("MM")
                xls.Sheet.Range("A9").Value = strCompanyName
                xls.Sheet.Range("N2").Value = strJigyouCD
                xls.Sheet.Range("N4").Value = strCompanyName
                xls.Sheet.Range("A15").Value = strCompanyName & "（介護）"

                If tbl.Count > 0 Then
                    xls.Sheet.Range("D9").Value = tbl(0).Item(tbl.延実在車両数Column.ColumnName)
                    xls.Sheet.Range("E9").Value = tbl(0).Item(tbl.車両番号のカウントColumn.ColumnName)
                    xls.Sheet.Range("G9").Value = tbl(0).Item(tbl.実車kmの合計Column.ColumnName)
                    xls.Sheet.Range("H9").Value = tbl(0).Item(tbl.走行kmの合計Column.ColumnName)
                    xls.Sheet.Range("J9").Value = tbl(0).Item(tbl.輸送回数の合計Column.ColumnName)
                    xls.Sheet.Range("K9").Value = tbl(0).Item(tbl.輸送人員の合計Column.ColumnName)
                    xls.Sheet.Range("L9").Value = tbl(0).Item(tbl.運送収入Column.ColumnName)
                    xls.Sheet.Range("M9").Value = tbl(0).Item(tbl.迎車回数の合計Column.ColumnName)
                    xls.Sheet.Range("P9").Value = tbl(0).Item(tbl.月末車両数Column.ColumnName)
                End If


                If tbl.Count > 1 Then
                    xls.Sheet.Range("D10").Value = tbl(1).Item(tbl.延実在車両数Column.ColumnName)
                    xls.Sheet.Range("E10").Value = tbl(1).Item(tbl.車両番号のカウントColumn.ColumnName)
                    xls.Sheet.Range("G10").Value = tbl(1).Item(tbl.実車kmの合計Column.ColumnName)
                    xls.Sheet.Range("H10").Value = tbl(1).Item(tbl.走行kmの合計Column.ColumnName)
                    xls.Sheet.Range("J10").Value = tbl(1).Item(tbl.輸送回数の合計Column.ColumnName)
                    xls.Sheet.Range("K10").Value = tbl(1).Item(tbl.輸送人員の合計Column.ColumnName)
                    xls.Sheet.Range("L10").Value = tbl(1).Item(tbl.運送収入Column.ColumnName)
                    xls.Sheet.Range("M10").Value = tbl(1).Item(tbl.迎車回数の合計Column.ColumnName)
                    xls.Sheet.Range("P10").Value = tbl(1).Item(tbl.月末車両数Column.ColumnName)
                End If


            Catch ex As Exception
                Me.LastError = ex.Message
                Return False
            Finally
            End Try
            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "Execute1:Excel"
        '''========================================================================================
        ''' <SUMMARY>実行</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================


        Protected Overrides Function Execute1_Header() As Boolean

            Me.LastError = Nothing
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim xls As New BaseCore.Common.Excel

            Try
                '----------------------------------------------------------------------
                ' ﾃﾞｰﾀ抽出
                '----------------------------------------------------------------------
                Dim tbl As NodeDatabase.DataSetFunc.F0月別輸送実績DataTable = Me.MakeDataTable()

                ''----------------------------------------------------------------------
                '' ｾﾞﾛ件
                ''----------------------------------------------------------------------
                If tbl.Rows.Count = 0 Then
                    Me.LastError = "指定された条件に合致するデータはありません。"
                    Return False
                End If

                '----------------------------------------------------------------------
                ' ｵｰﾌﾟﾝ
                '----------------------------------------------------------------------
                If Not xls.Open(Me.Path1) Then
                    Me.LastError = "テンプレートが見つかりません。(" & Me.Path1 & ")"
                    Return False
                End If

                '------------------------------------------------------------------------
                ' 作成
                '------------------------------------------------------------------------
                xls.SheetSelect(sheetName)       'ｱｸﾃｨﾌﾞｼｰﾄ

                If Not MakeExcelData(tbl, xls) Then
                    MyBase.LastError = Me.LastError
                    Return False
                End If

                '----------------------------------------------------------------------
                ' 先頭へｶｰｿﾙ移動
                '----------------------------------------------------------------------
                xls.CellGet(0, 0)

                xls.Close(Me.Path1)
            Catch ex As Exception
                Me.LastError = ex.Message
                Return False
            Finally
            End Try

            ''----------------------------------------------------------------------
            '' 正常終了
            ''----------------------------------------------------------------------

            Return True
        End Function

#End Region

#End Region


    End Class
End Namespace
