﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{774B7DBE-68A2-404F-B984-0569B2C3D200}</ProjectGuid>
    <OutputType>Library</OutputType>
    <RootNamespace>NodeCore</RootNamespace>
    <AssemblyName>NodeCore</AssemblyName>
    <FileAlignment>512</FileAlignment>
    <MyType>Windows</MyType>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <Deterministic>true</Deterministic>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <OutputPath>bin\Debug\</OutputPath>
    <DocumentationFile>NodeCore.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <Optimize>false</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DocumentationFile>NodeCore.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <PropertyGroup>
    <OptionExplicit>On</OptionExplicit>
  </PropertyGroup>
  <PropertyGroup>
    <OptionCompare>Binary</OptionCompare>
  </PropertyGroup>
  <PropertyGroup>
    <OptionStrict>Off</OptionStrict>
  </PropertyGroup>
  <PropertyGroup>
    <OptionInfer>On</OptionInfer>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="BaseContents, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\web\Bin\BaseContents.dll</HintPath>
    </Reference>
    <Reference Include="BaseCore, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\web\Bin\BaseCore.dll</HintPath>
    </Reference>
    <Reference Include="BaseDatabase, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\web\Bin\BaseDatabase.dll</HintPath>
    </Reference>
    <Reference Include="SpreadsheetGear2017.Core, Version=8.3.5.102, Culture=neutral, PublicKeyToken=39c186f5904944ec, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\..\..\Program Files (x86)\SpreadsheetGear\SpreadsheetGear 2017 for .NET\DotNet40\Bin\SpreadsheetGear2017.Core.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Transactions" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Net.Http" />
  </ItemGroup>
  <ItemGroup>
    <Import Include="Microsoft.VisualBasic" />
    <Import Include="System" />
    <Import Include="System.Collections" />
    <Import Include="System.Collections.Generic" />
    <Import Include="System.Data" />
    <Import Include="System.Diagnostics" />
    <Import Include="System.Linq" />
    <Import Include="System.Xml.Linq" />
    <Import Include="System.Threading.Tasks" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Common\Numbering.vb" />
    <Compile Include="Daily\BindLayout.vb" />
    <Compile Include="Daily\CrewDailyExport.vb" />
    <Compile Include="Daily\CrewRecord.vb" />
    <Compile Include="Daily\Home.vb" />
    <Compile Include="Daily\CrewSummaryExport.vb" />
    <Compile Include="Daily\DeliveryImport.vb" />
    <Compile Include="Daily\AccidentInfo.vb" />
    <Compile Include="Daily\MedicalCheckup.vb" />
    <Compile Include="Master\Company.vb" />
    <Compile Include="Daily\CrewResult.vb" />
    <Compile Include="Master\Kosya.vb" />
    <Compile Include="Master\Kubun.vb" />
    <Compile Include="Master\Car.vb" />
    <Compile Include="Master\Crew.vb" />
    <Compile Include="Master\Group.vb" />
    <Compile Include="Master\Tokuisaki.vb" />
    <Compile Include="Master\User.vb" />
    <Compile Include="Common\Config.vb" />
    <Compile Include="Common\Exist.vb" />
    <Compile Include="Common\FileIO.vb" />
    <Compile Include="Common\Frame.vb" />
    <Compile Include="Common\JanCode.vb" />
    <Compile Include="Common\JavaScript.vb" />
    <Compile Include="Common\Logic.vb" />
    <Compile Include="Common\Security.vb" />
    <Compile Include="Common\WebConfig.vb" />
    <Compile Include="Monthly\SalaryBasisData.vb" />
    <Compile Include="Monthly\ShutuTaikin.vb" />
    <Compile Include="Monthly\TukibetuYusou.vb" />
    <Compile Include="Monthly\ShiftYoteiJisseki.vb" />
    <Compile Include="Monthly\ShiftList.vb" />
    <Compile Include="Monthly\UriageShyokai.vb" />
    <Compile Include="Monthly\UriageShyokaiGroup.vb" />
    <Compile Include="My Project\AssemblyInfo.vb" />
    <Compile Include="My Project\Application.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Application.myapp</DependentUpon>
    </Compile>
    <Compile Include="My Project\Resources.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="My Project\Settings.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <Compile Include="Other\HealthCheckInput.vb" />
    <Compile Include="Other\AccidentHistory.vb" />
    <Compile Include="Other\MedicalCheckupHistory.vb" />
    <Compile Include="Other\PaidConsumption.vb" />
    <Compile Include="Other\PaidVacationDetail.vb" />
    <Compile Include="Other\TicketList.vb" />
    <Compile Include="Other\Ticket.vb" />
    <Compile Include="Other\index.vb" />
    <Compile Include="Other\PaidVacation.vb" />
    <Compile Include="Weekly\TaxiSales.vb" />
    <Compile Include="Weekly\WeeklyReportExport.vb" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="My Project\Resources.resx">
      <Generator>VbMyResourcesResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.vb</LastGenOutput>
      <CustomToolNamespace>My.Resources</CustomToolNamespace>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="My Project\Application.myapp">
      <Generator>MyApplicationCodeGenerator</Generator>
      <LastGenOutput>Application.Designer.vb</LastGenOutput>
    </None>
    <None Include="My Project\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <CustomToolNamespace>My</CustomToolNamespace>
      <LastGenOutput>Settings.Designer.vb</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="..\NodeContents\NodeContents.vbproj">
      <Project>{2ab0b14a-d7f7-4e9c-9a64-7bdd1a73f8ae}</Project>
      <Name>NodeContents</Name>
    </ProjectReference>
    <ProjectReference Include="..\NodeDatabase\NodeDatabase.vbproj">
      <Project>{6d42738c-eb63-4680-baf4-972c14a0d61a}</Project>
      <Name>NodeDatabase</Name>
    </ProjectReference>
    <ProjectReference Include="..\NodeReport\NodeReport.vbproj">
      <Project>{ff8624c5-bd9e-402b-bb3d-59a5848ed3e8}</Project>
      <Name>NodeReport</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.VisualBasic.targets" />
</Project>