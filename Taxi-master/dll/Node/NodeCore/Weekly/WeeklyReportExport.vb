﻿Imports System.Globalization

Namespace Frame.Weekly
    Partial Public Class WeeklyReportExport
        Inherits NodeCore.Common.Frame

#Region "ｺﾝｽﾀﾝﾄ"
        Public Class ConstantExcel
            Enum DAYS
                月
                火
                水
                木
                金
                土
                日
            End Enum 'DAYS

            Public Class Header
                Public Const SheetName0 = "週次実績"
                Public Const SheetName = "実績データ"
                Public Const SheetNameShift = "シフトデータ"
                Public Const RowTop = 1  '三行目スタート
                Public Const 年セル = "$B$2"
                Public Const 月セル = "$D$2"
                Public Const 日セル = "$F$2"

                Public Class 実績データ
                    Public Const 年度 = 0
                    Public Const 月 = 1
                    Public Const 週番号 = 2
                    Public Const 勤務区分 = 3
                    Public Const 乗務員CD = 4
                    Public Const 稼働数 = 5
                    Public Const 売上合計 = 6

                End Class

                Public Class シフトデータ
                    Public Const 年度 = 0
                    Public Const 月 = 1
                    Public Const 週番号 = 2
                    Public Const 勤務区分 = 3
                    Public Const 乗務員CD = 4
                    Public Const 予定稼働数 = 5
                    Public Const 前週稼働数 = 6
                    Public Const 前週売上合計 = 7

                End Class

            End Class

        End Class


#End Region

#Region "ﾌｨｰﾙﾄﾞ定義"
        Public Class Header
            ''' ----------------------------------------------------------------------------------------
            ''' <SUMMARY>列項目(基本)</SUMMARY>
            ''' ----------------------------------------------------------------------------------------
            Public グループCD As BaseCore.Common.Field.ItemData
            Public 個社CD As BaseCore.Common.Field.ItemData
            Public 子会社CD As BaseCore.Common.Field.ItemData
            Public 日付 As BaseCore.Common.Field.ItemData
            Public 年 As BaseCore.Common.Field.ItemData
            Public 週 As BaseCore.Common.Field.ItemData

            '''========================================================================================
            ''' <SUMMARY>ｺﾝｽﾄﾗｸﾀ</SUMMARY>
            '''========================================================================================
            Public Sub New(ByVal Config As NodeCore.Common.Config)
                Dim tbl As New NodeDatabase.DataSetView.V0乗務記録HDDataTable

                '----------------------------------------------------------------------
                ' ｲﾝｽﾀﾝｽ(基本) 
                '----------------------------------------------------------------------
                Me.グループCD = New BaseCore.Common.Field.ItemData(tbl.グループCDColumn)
                Me.個社CD = New BaseCore.Common.Field.ItemData(tbl.個社CDColumn)
                Me.子会社CD = New BaseCore.Common.Field.ItemData(tbl.子会社CDColumn, 1)
                Me.日付 = New BaseCore.Common.Field.ItemData(tbl.日報日付Column)
                Me.年 = New BaseCore.Common.Field.ItemData(tbl.日報日付Column)
                Me.週 = New BaseCore.Common.Field.ItemData(tbl.日報日付Column)

            End Sub
        End Class
#End Region

#Region "変数定義"
        Public Class Paramater
            Public Header As Header
        End Class

        '''========================================================================================
        ''' <summary>ﾍｯﾀﾞ</summary>
        '''========================================================================================
        Private _Fields As New Paramater
        Public Property Fields() As Paramater
            Get
                Return _Fields
            End Get

            Set(ByVal value As Paramater)
                _Fields = value
            End Set
        End Property
#End Region

#Region "ｺﾝｽﾄﾗｸﾀ"
        '''========================================================================================
        ''' <SUMMARY>ｺﾝｽﾄﾗｸﾀ</SUMMARY>
        ''' <PARAM name="Security">ｾｷｭﾘﾃｨ ｸﾗｽ</PARAM>
        ''' <PARAM name="Config">ｺﾝﾌｨｸﾞ ｸﾗｽ</PARAM>
        ''' <PARAM name="PagePath">ﾍﾟｰｼﾞﾊﾟｽ</PARAM>
        '''========================================================================================
        Public Sub New(ByVal Security As NodeCore.Common.Security, ByVal Config As NodeCore.Common.Config, ByVal PagePath As String)
            '----------------------------------------------------------------------
            ' ｾｷｭﾘﾃｨ 設定
            '----------------------------------------------------------------------
            MyBase.New(Security, Config, PagePath)

            Me.Fields.Header = New Header(Config)
        End Sub
#End Region

#Region "ﾍｯﾀﾞ"

#Region "Clear"
        '''========================================================================================
        ''' <SUMMARY>ｸﾘｱ</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Clear_Header(Optional ByVal KeyClear As Boolean = False) As Boolean
            '----------------------------------------------------------------------
            ' ｷｰ部ｸﾘｱ
            '----------------------------------------------------------------------
            If KeyClear Then
            End If

            '----------------------------------------------------------------------
            ' ﾃﾞｰﾀ部ｸﾘｱ
            '----------------------------------------------------------------------
            Me.Fields.Header.グループCD.Value = Nothing
            Me.Fields.Header.個社CD.Value = Nothing
            Me.Fields.Header.子会社CD.Value = Nothing
            Me.Fields.Header.日付.Value = Nothing
            Me.Fields.Header.年.Value = Nothing
            Me.Fields.Header.週.Value = Nothing
            '----------------------------------------------------------------------
            ' ﾃﾞｰﾀ部ｸﾘｱ
            '----------------------------------------------------------------------
            'Me.Read()

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "MakeDataTable"
        '''========================================================================================
        ''' <SUMMARY>ﾃﾞｰﾀﾃｰﾌﾞﾙ生成</SUMMARY>
        ''' <RETURNS>ﾃﾞｰﾀﾃｰﾌﾞﾙ</RETURNS>
        '''========================================================================================
        Public Function MakeDataTable() As DataTable
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetViewTableAdapters.V1週次実績_実績データTableAdapter
            Dim tbl As New NodeDatabase.DataSetView.V1週次実績_実績データDataTable
            Dim qry As New Collection

            '----------------------------------------------------------------------
            ' 事前準備
            '----------------------------------------------------------------------

            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, Me.Fields.Header.グループCD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, Me.Fields.Header.個社CD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.子会社CDColumn.ColumnName, Me.Fields.Header.子会社CD.Value, BaseDatabase.Contents.Compare.Equal))
            'qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, Me.Security.グループCD, BaseDatabase.Contents.Compare.Equal))
            'qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, Me.Security.個社CD, BaseDatabase.Contents.Compare.Equal))
            'qry.Add(New BaseDatabase.Condition(tbl.子会社CDColumn.ColumnName, Me.Security.子会社CD, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.年度Column.ColumnName, Val(Me.Fields.Header.年.Value) - 1, BaseDatabase.Contents.Compare.GreaterEqual))
            qry.Add(New BaseDatabase.Condition(tbl.年度Column.ColumnName, Me.Fields.Header.年.Value, BaseDatabase.Contents.Compare.LessEqual))
            qry.Add(New BaseDatabase.Condition(tbl.週番号Column.ColumnName, Me.Fields.Header.週.Value, BaseDatabase.Contents.Compare.LessEqual))
            qry.Add(New BaseDatabase.Condition(tbl.週番号Column.ColumnName, Val(Me.Fields.Header.週.Value) - 2, BaseDatabase.Contents.Compare.GreaterEqual))

            'qry.Add(New BaseDatabase.Condition(tbl.年度Column.ColumnName, DatePart("yyyy", DateAdd("m", -3, DateAdd("d", -14, Me.Fields.Header.日付.Value))), BaseDatabase.Contents.Compare.Equal))
            'qry.Add(New BaseDatabase.Condition(tbl.週番号Column.ColumnName, Get_Week_Number(DateAdd("d", -14, Me.Fields.Header.日付.Value)), BaseDatabase.Contents.Compare.Equal))

            'qry.Add(New BaseDatabase.Condition(tbl.年度Column.ColumnName, DatePart("yyyy", DateAdd("m", -3, DateAdd("d", -7, Me.Fields.Header.日付.Value))), BaseDatabase.Contents.Compare.Equal))
            'qry.Add(New BaseDatabase.Condition(tbl.週番号Column.ColumnName, Get_Week_Number(DateAdd("d", -7, Me.Fields.Header.日付.Value)), BaseDatabase.Contents.Compare.Equal))

            'qry.Add(New BaseDatabase.Condition(tbl.年度Column.ColumnName, DatePart("yyyy", DateAdd("m", -3, Me.Fields.Header.日付.Value)), BaseDatabase.Contents.Compare.Equal))
            'qry.Add(New BaseDatabase.Condition(tbl.週番号Column.ColumnName, Get_Week_Number(Me.Fields.Header.日付.Value), BaseDatabase.Contents.Compare.Equal))

            'qry.Add(New BaseDatabase.Condition(tbl.年度Column.ColumnName, DatePart("yyyy", DateAdd("m", -15, DateAdd("d", -14, Me.Fields.Header.日付.Value))), BaseDatabase.Contents.Compare.Equal))
            'qry.Add(New BaseDatabase.Condition(tbl.週番号Column.ColumnName, Get_Week_Number(DateAdd("y", -1, DateAdd("d", -14, Me.Fields.Header.日付.Value))), BaseDatabase.Contents.Compare.Equal))

            'qry.Add(New BaseDatabase.Condition(tbl.年度Column.ColumnName, DatePart("yyyy", DateAdd("m", -15, DateAdd("d", -7, Me.Fields.Header.日付.Value))), BaseDatabase.Contents.Compare.Equal))
            'qry.Add(New BaseDatabase.Condition(tbl.週番号Column.ColumnName, Get_Week_Number(DateAdd("y", -1, DateAdd("d", -7, Me.Fields.Header.日付.Value))), BaseDatabase.Contents.Compare.Equal))

            'qry.Add(New BaseDatabase.Condition(tbl.年度Column.ColumnName, DatePart("yyyy", DateAdd("m", -3, Me.Fields.Header.日付.Value)), BaseDatabase.Contents.Compare.Equal))
            'qry.Add(New BaseDatabase.Condition(tbl.週番号Column.ColumnName, Get_Week_Number(DateAdd("y", -1, Me.Fields.Header.日付.Value)), BaseDatabase.Contents.Compare.Equal))

            tbl = ada.SelectByCommon(qry)


            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return tbl
        End Function

        Public Function MakeDataTableShift() As DataTable
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetViewTableAdapters.V1週次実績_シフトデータTableAdapter
            Dim tbl As New NodeDatabase.DataSetView.V1週次実績_シフトデータDataTable
            Dim qry As New Collection

            '----------------------------------------------------------------------
            ' 事前準備
            '----------------------------------------------------------------------

            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, Me.Fields.Header.グループCD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, Me.Fields.Header.個社CD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.子会社CDColumn.ColumnName, Me.Fields.Header.子会社CD.Value, BaseDatabase.Contents.Compare.Equal))
            'qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, Me.Security.グループCD, BaseDatabase.Contents.Compare.Equal))
            'qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, Me.Security.個社CD, BaseDatabase.Contents.Compare.Equal))
            'qry.Add(New BaseDatabase.Condition(tbl.子会社CDColumn.ColumnName, Me.Security.子会社CD, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.年度Column.ColumnName, Val(Me.Fields.Header.年.Value) - 1, BaseDatabase.Contents.Compare.GreaterEqual))
            qry.Add(New BaseDatabase.Condition(tbl.年度Column.ColumnName, Me.Fields.Header.年.Value, BaseDatabase.Contents.Compare.LessEqual))
            qry.Add(New BaseDatabase.Condition(tbl.週番号Column.ColumnName, Me.Fields.Header.週.Value, BaseDatabase.Contents.Compare.LessEqual))
            qry.Add(New BaseDatabase.Condition(tbl.週番号Column.ColumnName, Val(Me.Fields.Header.週.Value) - 2, BaseDatabase.Contents.Compare.GreaterEqual))
            'qry.Add(New BaseDatabase.Condition(tbl.年度Column.ColumnName, DatePart("yyyy", DateAdd("m", -3, DateAdd("d", -14, Me.Fields.Header.日付.Value))), BaseDatabase.Contents.Compare.Equal))
            'qry.Add(New BaseDatabase.Condition(tbl.週番号Column.ColumnName, Get_Week_Number(DateAdd("d", -14, Me.Fields.Header.日付.Value)), BaseDatabase.Contents.Compare.Equal))

            'qry.Add(New BaseDatabase.Condition(tbl.年度Column.ColumnName, DatePart("yyyy", DateAdd("m", -3, DateAdd("d", -7, Me.Fields.Header.日付.Value))), BaseDatabase.Contents.Compare.Equal))
            'qry.Add(New BaseDatabase.Condition(tbl.週番号Column.ColumnName, Get_Week_Number(DateAdd("d", -7, Me.Fields.Header.日付.Value)), BaseDatabase.Contents.Compare.Equal))

            'qry.Add(New BaseDatabase.Condition(tbl.年度Column.ColumnName, DatePart("yyyy", DateAdd("m", -3, Me.Fields.Header.日付.Value)), BaseDatabase.Contents.Compare.Equal))
            'qry.Add(New BaseDatabase.Condition(tbl.週番号Column.ColumnName, Get_Week_Number(Me.Fields.Header.日付.Value), BaseDatabase.Contents.Compare.Equal))

            tbl = ada.SelectByCommon(qry)

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return tbl
        End Function
#End Region

#Region "Execute"
        '''========================================================================================
        ''' <SUMMARY>実行</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Execute_Header() As Boolean

            Me.LastError = Nothing
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim xls As New BaseCore.Common.Excel

            Try
                '----------------------------------------------------------------------
                ' ﾃﾞｰﾀ抽出
                '----------------------------------------------------------------------
                Dim tbl As DataTable = Me.MakeDataTable()

                ''----------------------------------------------------------------------
                '' ｾﾞﾛ件
                ''----------------------------------------------------------------------
                If tbl.Rows.Count = 0 Then
                    Me.LastError = "指定された条件に合致するデータはありません。"
                    Return False
                End If

                '----------------------------------------------------------------------
                ' ｵｰﾌﾟﾝ
                '----------------------------------------------------------------------
                If Not xls.Open(Me.Path1) Then
                    Me.LastError = "テンプレートが見つかりません。(" & Me.Path1 & ")"
                    Return False
                End If

                '------------------------------------------------------------------------
                ' 作成
                '------------------------------------------------------------------------
                xls.SheetSelect(ConstantExcel.Header.SheetName0)

                xls.CellSetValue(2, 0, Me.Fields.Header.日付.Value)
                xls.CellSetValue(1, 4, DatePart("yyyy", DateAdd("m", -3, DateAdd("d", -14, Me.Fields.Header.日付.Value))))
                xls.CellSetValue(1, 5, Get_Week_Number(DateAdd("d", -14, Me.Fields.Header.日付.Value)))
                xls.CellSetValue(1, 11, DatePart("yyyy", DateAdd("m", -3, DateAdd("d", -7, Me.Fields.Header.日付.Value))))
                xls.CellSetValue(1, 12, Get_Week_Number(DateAdd("d", -7, Me.Fields.Header.日付.Value)))
                xls.CellSetValue(1, 16, DatePart("yyyy", DateAdd("m", -3, Me.Fields.Header.日付.Value)))
                xls.CellSetValue(1, 17, Get_Week_Number(Me.Fields.Header.日付.Value))

                xls.SheetSelect(ConstantExcel.Header.SheetName)       'ｱｸﾃｨﾌﾞｼｰﾄ

                If Not MakeExcelData(tbl, xls) Then
                    MyBase.LastError = Me.LastError
                    Return False
                End If

                Dim tblShift As DataTable = Me.MakeDataTableShift()

                ''----------------------------------------------------------------------
                '' ｾﾞﾛ件
                ''----------------------------------------------------------------------
                If tblShift.Rows.Count > 0 Then

                    '------------------------------------------------------------------------
                    ' 作成
                    '------------------------------------------------------------------------
                    xls.SheetSelect(ConstantExcel.Header.SheetNameShift)       'ｱｸﾃｨﾌﾞｼｰﾄ

                    If Not MakeExcelDataShift(tblShift, xls) Then
                        MyBase.LastError = Me.LastError
                        Return False
                    End If

                    '----------------------------------------------------------------------
                    ' 先頭へｶｰｿﾙ移動
                    '----------------------------------------------------------------------
                    xls.CellGet(0, 0)

                    'Me.LastError = "指定された条件に合致するシフトデータはありません。"
                    'Return False
                End If



                xls.Close(Me.Path1)
            Catch ex As Exception
                Me.LastError = ex.Message
                Return False
            Finally
            End Try


            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "エクセルデータ作成"
        '''========================================================================================
        ''' <SUMMARY>抽出したﾃｰﾌﾞﾙに編集を加える</SUMMARY>   
        '''========================================================================================
        Public Function MakeExcelData(ByRef tbl As NodeDatabase.DataSetView.V1週次実績_実績データDataTable, ByRef xls As BaseCore.Common.Excel) As Boolean
            '------------------------------------------------------------------------
            ' 変数定義

            '------------------------------------------------------------------------
            Dim intDtlCnt As Integer = ConstantExcel.Header.RowTop

            Try
                ' xls.InsertRow(ConstantExcel.Header.SheetName, intDtlCnt + 2, tbl.Rows.Count - 1)

                For Each row As NodeDatabase.DataSetView.V1週次実績_実績データRow In tbl.Rows


                    '------------------------------------------------------------------------
                    ' 値設定 明細
                    '-----------------------------------------------------------------------
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.実績データ.年度, BaseCore.Common.Text.Nz(row.Item(tbl.年度Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.実績データ.月, BaseCore.Common.Text.Nz(row.Item(tbl.月Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.実績データ.週番号, BaseCore.Common.Text.Nz(row.Item(tbl.週番号Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.実績データ.勤務区分, BaseCore.Common.Text.Nz(row.Item(tbl.勤務区分Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.実績データ.乗務員CD, BaseCore.Common.Text.Nz(row.Item(tbl.乗務員CDColumn.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.実績データ.稼働数, BaseCore.Common.Text.Nz(row.Item(tbl.稼働数Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.実績データ.売上合計, BaseCore.Common.Text.Nz(row.Item(tbl.売上合計Column.ColumnName), ""))

                    intDtlCnt += 1
                Next

            Catch ex As Exception
                Me.LastError = ex.Message
                Return False
            Finally
            End Try
            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function

        Public Function MakeExcelDataShift(ByRef tbl As NodeDatabase.DataSetView.V1週次実績_シフトデータDataTable, ByRef xls As BaseCore.Common.Excel) As Boolean
            '------------------------------------------------------------------------
            ' 変数定義

            '------------------------------------------------------------------------
            Dim intDtlCnt As Integer = ConstantExcel.Header.RowTop

            Try

                xls.InsertRow(ConstantExcel.Header.SheetName, intDtlCnt + 2, tbl.Rows.Count - 1)

                For Each row As NodeDatabase.DataSetView.V1週次実績_シフトデータRow In tbl.Rows


                    '------------------------------------------------------------------------
                    ' 値設定 明細
                    '-----------------------------------------------------------------------
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.シフトデータ.年度, BaseCore.Common.Text.Nz(row.Item(tbl.年度Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.シフトデータ.月, BaseCore.Common.Text.Nz(row.Item(tbl.月Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.シフトデータ.週番号, BaseCore.Common.Text.Nz(row.Item(tbl.週番号Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.シフトデータ.勤務区分, BaseCore.Common.Text.Nz(row.Item(tbl.勤務区分Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.シフトデータ.乗務員CD, BaseCore.Common.Text.Nz(row.Item(tbl.乗務員CDColumn.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.シフトデータ.予定稼働数, BaseCore.Common.Text.Nz(row.Item(tbl.予定稼働数Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.シフトデータ.前週稼働数, BaseCore.Common.Text.Nz(row.Item(tbl.前週稼働数Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.シフトデータ.前週売上合計, BaseCore.Common.Text.Nz(row.Item(tbl.前週売上合計Column.ColumnName), ""))

                    intDtlCnt += 1
                Next

            Catch ex As Exception
                Me.LastError = ex.Message
                Return False
            Finally
            End Try
            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region


#End Region

#Region "CommonFunctions"
        Public Function Get_Week_Number(ByVal inputDate As DateTime) As Integer
            Dim cal As New System.Globalization.JapaneseCalendar()
            Dim week As Integer = cal.GetWeekOfYear(inputDate, System.Globalization.CalendarWeekRule.FirstDay, DayOfWeek.Sunday)
            Return week
        End Function
#End Region


    End Class
End Namespace
