﻿Imports System.Globalization

Namespace Frame.Weekly
    Partial Public Class TaxiSales
        Inherits NodeCore.Common.Frame

#Region "ｺﾝｽﾀﾝﾄ"
        Public Class ConstantExcel
            Enum DAYS
                月
                火
                水
                木
                金
                土
                日
            End Enum 'DAYS

            Public Class Header
                Public Const SheetName = "集計データ"
                Public Const RowTop = 1  '三行目スタート
                Public Const 年セル = "$B$2"
                Public Const 月セル = "$D$2"
                Public Const 日セル = "$F$2"

                Public Class 列数
                    Public Const 日報年月 = 0
                    Public Const 年度 = 1
                    Public Const 月 = 2
                    Public Const 週番号 = 3
                    Public Const 勤務区分 = 4
                    Public Const 乗務員CD = 5
                    Public Const 乗務員名 = 6
                    Public Const 稼働数 = 7
                    Public Const 売上合計 = 8
                    Public Const 予測基準額 = 9

                End Class

            End Class

        End Class


#End Region

#Region "ﾌｨｰﾙﾄﾞ定義"
        Public Class Header
            ''' ----------------------------------------------------------------------------------------
            ''' <SUMMARY>列項目(基本)</SUMMARY>
            ''' ----------------------------------------------------------------------------------------
            Public グループCD As BaseCore.Common.Field.ItemData
            Public 個社CD As BaseCore.Common.Field.ItemData
            Public 子会社CD As BaseCore.Common.Field.ItemData
            Public 日付 As BaseCore.Common.Field.ItemData
            Public 年 As BaseCore.Common.Field.ItemData

            '''========================================================================================
            ''' <SUMMARY>ｺﾝｽﾄﾗｸﾀ</SUMMARY>
            '''========================================================================================
            Public Sub New(ByVal Config As NodeCore.Common.Config)
                Dim tbl As New NodeDatabase.DataSetView.V0乗務記録HDDataTable

                '----------------------------------------------------------------------
                ' ｲﾝｽﾀﾝｽ(基本) 
                '----------------------------------------------------------------------
                Me.グループCD = New BaseCore.Common.Field.ItemData(tbl.グループCDColumn)
                Me.個社CD = New BaseCore.Common.Field.ItemData(tbl.個社CDColumn)
                Me.子会社CD = New BaseCore.Common.Field.ItemData(tbl.子会社CDColumn, 1)
                Me.日付 = New BaseCore.Common.Field.ItemData(tbl.日報日付Column)
                Me.年 = New BaseCore.Common.Field.ItemData(tbl.日報日付Column)

            End Sub
        End Class
#End Region

#Region "変数定義"
        Public Class Paramater
            Public Header As Header
        End Class

        '''========================================================================================
        ''' <summary>ﾍｯﾀﾞ</summary>
        '''========================================================================================
        Private _Fields As New Paramater
        Public Property Fields() As Paramater
            Get
                Return _Fields
            End Get

            Set(ByVal value As Paramater)
                _Fields = value
            End Set
        End Property
#End Region

#Region "ｺﾝｽﾄﾗｸﾀ"
        '''========================================================================================
        ''' <SUMMARY>ｺﾝｽﾄﾗｸﾀ</SUMMARY>
        ''' <PARAM name="Security">ｾｷｭﾘﾃｨ ｸﾗｽ</PARAM>
        ''' <PARAM name="Config">ｺﾝﾌｨｸﾞ ｸﾗｽ</PARAM>
        ''' <PARAM name="PagePath">ﾍﾟｰｼﾞﾊﾟｽ</PARAM>
        '''========================================================================================
        Public Sub New(ByVal Security As NodeCore.Common.Security, ByVal Config As NodeCore.Common.Config, ByVal PagePath As String)
            '----------------------------------------------------------------------
            ' ｾｷｭﾘﾃｨ 設定
            '----------------------------------------------------------------------
            MyBase.New(Security, Config, PagePath)

            Me.Fields.Header = New Header(Config)
        End Sub
#End Region

#Region "ﾍｯﾀﾞ"

#Region "Clear"
        '''========================================================================================
        ''' <SUMMARY>ｸﾘｱ</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Clear_Header(Optional ByVal KeyClear As Boolean = False) As Boolean
            '----------------------------------------------------------------------
            ' ｷｰ部ｸﾘｱ
            '----------------------------------------------------------------------
            If KeyClear Then
                Me.Fields.Header.グループCD.Value = Nothing
                Me.Fields.Header.個社CD.Value = Nothing
                Me.Fields.Header.子会社CD.Value = Nothing

            End If

            '----------------------------------------------------------------------
            ' ﾃﾞｰﾀ部ｸﾘｱ
            '----------------------------------------------------------------------

            Me.Fields.Header.日付.Value = Nothing
            Me.Fields.Header.年.Value = Nothing
            '----------------------------------------------------------------------
            ' ﾃﾞｰﾀ部ｸﾘｱ
            '----------------------------------------------------------------------
            'Me.Read()

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "MakeDataTable"
        '''========================================================================================
        ''' <SUMMARY>ﾃﾞｰﾀﾃｰﾌﾞﾙ生成</SUMMARY>
        ''' <RETURNS>ﾃﾞｰﾀﾃｰﾌﾞﾙ</RETURNS>
        '''========================================================================================
        Public Function MakeDataTable() As DataTable
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetFuncTableAdapters.F0売上データTableAdapter
            Dim tbl As New NodeDatabase.DataSetFunc.F0売上データDataTable
            Dim qry As New Collection

            '----------------------------------------------------------------------
            ' 事前準備
            '----------------------------------------------------------------------

            qry.Clear()
            qry.Add(New BaseDatabase.Condition("グループCD", Me.Security.グループCD, BaseDatabase.Contents.Compare.Parameter))
            qry.Add(New BaseDatabase.Condition("個社CD", Me.Security.個社CD, BaseDatabase.Contents.Compare.Parameter))
            qry.Add(New BaseDatabase.Condition("子会社CD", Me.Security.子会社CD, BaseDatabase.Contents.Compare.Parameter))
            qry.Add(New BaseDatabase.Condition("inputDate", Me.Fields.Header.日付.Value, BaseDatabase.Contents.Compare.Parameter))

            tbl = ada.SelectByCommon(qry)


            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return tbl
        End Function
#End Region

#Region "Execute"
        '''========================================================================================
        ''' <SUMMARY>実行</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Execute_Header() As Boolean

            Me.LastError = Nothing
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim xls As New BaseCore.Common.Excel

            Try
                '----------------------------------------------------------------------
                ' ﾃﾞｰﾀ抽出
                '----------------------------------------------------------------------
                Dim tbl As DataTable = Me.MakeDataTable()

                ''----------------------------------------------------------------------
                '' ｾﾞﾛ件
                ''----------------------------------------------------------------------
                If tbl.Rows.Count = 0 Then
                    Me.LastError = "指定された条件に合致するデータはありません。"
                    Return False
                End If

                '----------------------------------------------------------------------
                ' ｵｰﾌﾟﾝ
                '----------------------------------------------------------------------
                If Not xls.Open(Me.Path1) Then
                    Me.LastError = "テンプレートが見つかりません。(" & Me.Path1 & ")"
                    Return False
                End If

                '------------------------------------------------------------------------
                ' 作成
                '------------------------------------------------------------------------
                xls.SheetSelect(ConstantExcel.Header.SheetName)       'ｱｸﾃｨﾌﾞｼｰﾄ

                If Not MakeExcelData(tbl, xls) Then
                    MyBase.LastError = Me.LastError
                    Return False
                End If

                '----------------------------------------------------------------------
                ' 先頭へｶｰｿﾙ移動
                '----------------------------------------------------------------------
                xls.CellGet(0, 0)

                xls.Close(Me.Path1)
            Catch ex As Exception
                Me.LastError = ex.Message
                Return False
            Finally
            End Try


            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "エクセルデータ作成"
        '''========================================================================================
        ''' <SUMMARY>抽出したﾃｰﾌﾞﾙに編集を加える</SUMMARY>   
        '''========================================================================================
        Public Function MakeExcelData(ByRef tbl As NodeDatabase.DataSetFunc.F0売上データDataTable, ByRef xls As BaseCore.Common.Excel) As Boolean
            '------------------------------------------------------------------------
            ' 変数定義

            '------------------------------------------------------------------------
            Dim intDtlCnt As Integer = ConstantExcel.Header.RowTop

            Try

                xls.InsertRow(ConstantExcel.Header.SheetName, intDtlCnt + 2, tbl.Rows.Count - 1)

                For Each row As NodeDatabase.DataSetFunc.F0売上データRow In tbl.Rows


                    '------------------------------------------------------------------------
                    ' 値設定 明細
                    '-----------------------------------------------------------------------
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.日報年月, BaseCore.Common.Text.Nz(row.Item(tbl.日報年月Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.年度, BaseCore.Common.Text.Nz(row.Item(tbl.年度Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.月, BaseCore.Common.Text.Nz(row.Item(tbl.月Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.週番号, BaseCore.Common.Text.Nz(row.Item(tbl.週番号Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.勤務区分, BaseCore.Common.Text.Nz(row.Item(tbl.勤務区分Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.乗務員CD, row.Item(tbl.乗務員CDColumn.ColumnName))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.乗務員名, row.Item(tbl.乗務員名Column.ColumnName))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.稼働数, BaseCore.Common.Text.CVal(row.Item(tbl.稼働数Column.ColumnName)))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.売上合計, BaseCore.Common.Text.CVal(row.Item(tbl.売上合計Column.ColumnName)))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.予測基準額, BaseCore.Common.Text.CVal(row.Item(tbl.予測基準額Column.ColumnName)))

                    intDtlCnt += 1
                Next

            Catch ex As Exception
                Me.LastError = ex.Message
                Return False
            Finally
            End Try
            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region


#End Region


    End Class
End Namespace
