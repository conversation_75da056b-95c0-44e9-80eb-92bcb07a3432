﻿Imports System.Globalization

Namespace Frame.Daily
    Partial Public Class CrewDailyExport
        Inherits NodeCore.Common.Frame

#Region "ｺﾝｽﾀﾝﾄ"
        Public Class ConstantExcel
            Enum DAYS
                月
                火
                水
                木
                金
                土
                日
            End Enum 'DAYS

            Public Class Header
                Public Const SheetName = "乗務日計表"
                Public Const RowTop = 4  '五行目スタート
                Public Const 年セル = "$B$2"
                Public Const 月セル = "$D$2"
                Public Const 日セル = "$F$2"

                Public Class 列数
                    Public Const 車番 = 0
                    Public Const 乗務員 = 1
                    Public Const 社員NO = 2
                    Public Const 走行 = 3
                    Public Const 営業 = 4
                    Public Const 輸送 = 5
                    Public Const 回数 = 6
                    Public Const 爾後 = 7
                    Public Const 迎車 = 8
                    Public Const 予約 = 9
                    Public Const 計 = 10
                    Public Const 遠割 = 11
                    Public Const 定差額 = 12
                    Public Const 空転 = 13
                    Public Const 納金 = 14
                    Public Const TAX = 15
                    Public Const 営収 = 16
                    Public Const チケット = 17
                    Public Const クーポン四社 = 18
                    Public Const クーポン東旅協 = 19
                    Public Const 福祉券 = 20
                    Public Const カード = 21
                    Public Const ID = 22
                    Public Const 交通IC = 23
                    Public Const キャブカ = 24
                    Public Const プリカ = 25
                    Public Const WAON = 26
                    Public Const その他 = 27
                    Public Const 障割 = 28
                    Public Const 現金 = 29
                    Public Const ETCお客様 = 30
                    Public Const ETC乗務員 = 31
                    Public Const ETC会社 = 32
                    Public Const 出庫時間 = 33
                    Public Const 帰庫時間 = 34
                    Public Const 稼動時間 = 35
                    Public Const 金額修正日時 = 36
                    Public Const 信販 = 37

                End Class

            End Class

        End Class


#End Region

#Region "ﾌｨｰﾙﾄﾞ定義"
        Public Class Header
            ''' ----------------------------------------------------------------------------------------
            ''' <SUMMARY>列項目(基本)</SUMMARY>
            ''' ----------------------------------------------------------------------------------------

            Public グループCD As BaseCore.Common.Field.ItemData
            Public 個社CD As BaseCore.Common.Field.ItemData
            Public 子会社CD As BaseCore.Common.Field.ItemData
            Public 日報日付 As BaseCore.Common.Field.ItemData

            '''========================================================================================
            ''' <SUMMARY>ｺﾝｽﾄﾗｸﾀ</SUMMARY>
            '''========================================================================================
            Public Sub New(ByVal Config As NodeCore.Common.Config)
                Dim tbl As New NodeDatabase.DataSetView.V0乗務記録HDDataTable

                '----------------------------------------------------------------------
                ' ｲﾝｽﾀﾝｽ(基本) 
                '----------------------------------------------------------------------
                Me.グループCD = New BaseCore.Common.Field.ItemData(tbl.グループCDColumn)
                Me.個社CD = New BaseCore.Common.Field.ItemData(tbl.個社CDColumn)
                Me.子会社CD = New BaseCore.Common.Field.ItemData(tbl.子会社CDColumn, 1)
                Me.日報日付 = New BaseCore.Common.Field.ItemData(tbl.日報日付Column)

            End Sub
        End Class
#End Region

#Region "変数定義"
        Public Class Paramater
            Public Header As Header
        End Class

        '''========================================================================================
        ''' <summary>ﾍｯﾀﾞ</summary>
        '''========================================================================================
        Private _Fields As New Paramater
        Public Property Fields() As Paramater
            Get
                Return _Fields
            End Get

            Set(ByVal value As Paramater)
                _Fields = value
            End Set
        End Property
#End Region

#Region "ｺﾝｽﾄﾗｸﾀ"
        '''========================================================================================
        ''' <SUMMARY>ｺﾝｽﾄﾗｸﾀ</SUMMARY>
        ''' <PARAM name="Security">ｾｷｭﾘﾃｨ ｸﾗｽ</PARAM>
        ''' <PARAM name="Config">ｺﾝﾌｨｸﾞ ｸﾗｽ</PARAM>
        ''' <PARAM name="PagePath">ﾍﾟｰｼﾞﾊﾟｽ</PARAM>
        '''========================================================================================
        Public Sub New(ByVal Security As NodeCore.Common.Security, ByVal Config As NodeCore.Common.Config, ByVal PagePath As String)
            '----------------------------------------------------------------------
            ' ｾｷｭﾘﾃｨ 設定
            '----------------------------------------------------------------------
            MyBase.New(Security, Config, PagePath)

            Me.Fields.Header = New Header(Config)
        End Sub
#End Region

#Region "ﾍｯﾀﾞ"

#Region "Clear"
        '''========================================================================================
        ''' <SUMMARY>ｸﾘｱ</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Clear_Header(Optional ByVal KeyClear As Boolean = False) As Boolean
            '----------------------------------------------------------------------
            ' ｷｰ部ｸﾘｱ
            '----------------------------------------------------------------------
            If KeyClear Then
            End If

            '----------------------------------------------------------------------
            ' ﾃﾞｰﾀ部ｸﾘｱ
            '----------------------------------------------------------------------

            Me.Fields.Header.グループCD.Value = Nothing
            Me.Fields.Header.個社CD.Value = Nothing
            Me.Fields.Header.子会社CD.Value = Nothing
            Me.Fields.Header.日報日付.Value = Nothing

            '----------------------------------------------------------------------
            ' ﾃﾞｰﾀ部ｸﾘｱ
            '----------------------------------------------------------------------
            'Me.Read()

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "MakeDataTable"
        '''========================================================================================
        ''' <SUMMARY>ﾃﾞｰﾀﾃｰﾌﾞﾙ生成</SUMMARY>
        ''' <RETURNS>ﾃﾞｰﾀﾃｰﾌﾞﾙ</RETURNS>
        '''========================================================================================
        Public Function MakeDataTable() As DataTable
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetViewTableAdapters.V0乗務記録実績TableAdapter
            Dim tbl As New NodeDatabase.DataSetView.V0乗務記録実績DataTable
            Dim qry As New Collection

            '----------------------------------------------------------------------
            ' 事前準備
            '----------------------------------------------------------------------

            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, Me.Fields.Header.グループCD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, Me.Fields.Header.個社CD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.子会社CDColumn.ColumnName, Me.Fields.Header.子会社CD.Value, BaseDatabase.Contents.Compare.Equal))

            'qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, Me.Security.グループCD, BaseDatabase.Contents.Compare.Equal))
            'qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, Me.Security.個社CD, BaseDatabase.Contents.Compare.Equal))
            'qry.Add(New BaseDatabase.Condition(tbl.子会社CDColumn.ColumnName, Me.Security.子会社CD, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.日報日付Column.ColumnName, Me.Fields.Header.日報日付.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.削除フラグColumn.ColumnName, "0", BaseDatabase.Contents.Compare.Equal))

            tbl = ada.SelectByCommon(qry)


            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return tbl
        End Function
#End Region

#Region "Execute"
        '''========================================================================================
        ''' <SUMMARY>実行</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Execute_Header() As Boolean

            Me.LastError = Nothing
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim xls As New BaseCore.Common.Excel

            Try
                '----------------------------------------------------------------------
                ' ﾃﾞｰﾀ抽出
                '----------------------------------------------------------------------
                Dim tbl As DataTable = Me.MakeDataTable()

                ''----------------------------------------------------------------------
                '' ｾﾞﾛ件
                ''----------------------------------------------------------------------
                If tbl.Rows.Count = 0 Then
                    Me.LastError = "指定された条件に合致するデータはありません。"
                    Return False
                End If

                '----------------------------------------------------------------------
                ' ｵｰﾌﾟﾝ
                '----------------------------------------------------------------------
                If Not xls.Open(Me.Path1) Then
                    Me.LastError = "テンプレートが見つかりません。(" & Me.Path1 & ")"
                    Return False
                End If

                '------------------------------------------------------------------------
                ' 作成
                '------------------------------------------------------------------------
                xls.SheetSelect(ConstantExcel.Header.SheetName)       'ｱｸﾃｨﾌﾞｼｰﾄ

                If Not MakeExcelData(tbl, xls) Then
                    MyBase.LastError = Me.LastError
                    Return False
                End If

                '----------------------------------------------------------------------
                ' 先頭へｶｰｿﾙ移動
                '----------------------------------------------------------------------
                xls.CellGet(0, 0)

                xls.Close(Me.Path1)
            Catch ex As Exception
                Me.LastError = ex.Message
                Return False
            Finally
            End Try


            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "エクセルデータ作成"
        '''========================================================================================
        ''' <SUMMARY>抽出したﾃｰﾌﾞﾙに編集を加える</SUMMARY>   
        '''========================================================================================
        Public Function MakeExcelData(ByRef tbl As NodeDatabase.DataSetView.V0乗務記録実績DataTable, ByRef xls As BaseCore.Common.Excel) As Boolean
            '------------------------------------------------------------------------
            ' 変数定義
            '------------------------------------------------------------------------
            Dim intDtlCnt As Integer = ConstantExcel.Header.RowTop

            Try

                Dim myCal As JapaneseCalendar = New JapaneseCalendar()

                Dim tDate As Date = Date.Parse(Fields.Header.日報日付.Value)

                xls.CellSetValue(1, 1, (tDate.Year - 2018).ToString())
                xls.CellSetValue(1, 3, tDate.Month.ToString())
                xls.CellSetValue(1, 5, tDate.Day.ToString())
                xls.CellSetValue(1, 7, [Enum].GetName(GetType(ConstantExcel.DAYS), myCal.GetDayOfWeek(tDate) - 1))

                xls.InsertRow(ConstantExcel.Header.SheetName, intDtlCnt + 2, tbl.Rows.Count - 1)

                For Each row As NodeDatabase.DataSetView.V0乗務記録実績Row In tbl.Rows


                    '------------------------------------------------------------------------
                    ' 値設定 明細
                    '-----------------------------------------------------------------------
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.車番, BaseCore.Common.Text.Nz(row.Item(tbl.車両番号Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.乗務員, BaseCore.Common.Text.Nz(row.Item(tbl.乗務員名Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.社員NO, BaseCore.Common.Text.Nz(row.Item(tbl.乗務員CDColumn.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.走行, BaseCore.Common.Text.CVal(row.Item(tbl.走行kmColumn.ColumnName)))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.営業, BaseCore.Common.Text.CVal(row.Item(tbl.営業kmColumn.ColumnName)))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.輸送, BaseCore.Common.Text.CVal(row.Item(tbl.輸送人員Column.ColumnName)))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.回数, BaseCore.Common.Text.CVal(row.Item(tbl.実車回数Column.ColumnName)))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.爾後, BaseCore.Common.Text.CVal(row.Item(tbl.爾後回数Column.ColumnName)))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.迎車, BaseCore.Common.Text.CVal(row.Item(tbl.迎車回数Column.ColumnName)))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.予約, BaseCore.Common.Text.CVal(row.Item(tbl.予約回数Column.ColumnName)))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.計, BaseCore.Common.Text.CVal(row.Item(tbl.金額計Column.ColumnName)))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.遠割, BaseCore.Common.Text.CVal(row.Item(tbl.遠割額Column.ColumnName)))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.定差額, BaseCore.Common.Text.CVal(row.Item(tbl.定額差額AColumn.ColumnName)))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.空転, BaseCore.Common.Text.CVal(row.Item(tbl.空転AColumn.ColumnName)))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.納金, BaseCore.Common.Text.CVal(row.Item(tbl.納金Column.ColumnName)))

                    Dim taxVal As Decimal = Math.Round((BaseCore.Common.Text.CVal(row.Item(tbl.納金Column.ColumnName)) / (Me.Config.税率 + 100)) * Me.Config.税率, 0)
                    taxVal = Math.Round(taxVal / 10, 0) * 10
                    Dim eiVal As Decimal = Math.Floor(BaseCore.Common.Text.CVal(row.Item(tbl.納金Column.ColumnName)) - taxVal)

                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.TAX, taxVal)
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.営収, eiVal)
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.チケット, BaseCore.Common.Text.CVal(row.Item(tbl.チケットAColumn.ColumnName)))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.クーポン四社, BaseCore.Common.Text.CVal(row.Item(tbl.クーポン四社AColumn.ColumnName)))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.クーポン東旅協, BaseCore.Common.Text.CVal(row.Item(tbl.クーポン東旅協AColumn.ColumnName)))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.福祉券, BaseCore.Common.Text.CVal(row.Item(tbl.福祉券AColumn.ColumnName)))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.カード, BaseCore.Common.Text.CVal(row.Item(tbl.カードAColumn.ColumnName)))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.ID, BaseCore.Common.Text.CVal(row.Item(tbl.iDAColumn.ColumnName)))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.交通IC, BaseCore.Common.Text.CVal(row.Item(tbl.交通系ICAColumn.ColumnName)))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.キャブカ, BaseCore.Common.Text.CVal(row.Item(tbl.キャブカードAColumn.ColumnName)))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.プリカ, BaseCore.Common.Text.CVal(row.Item(tbl.プリペイドカードAColumn.ColumnName)))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.WAON, BaseCore.Common.Text.CVal(row.Item(tbl.WAONAColumn.ColumnName)))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.その他, BaseCore.Common.Text.CVal(row.Item(tbl.その他AColumn.ColumnName)))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.障割, BaseCore.Common.Text.CVal(row.Item(tbl.障割AColumn.ColumnName)))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.現金, BaseCore.Common.Text.CVal(row.Item(tbl.現金AColumn.ColumnName)))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.ETCお客様, BaseCore.Common.Text.CVal(row.Item(tbl.ETC実車AColumn.ColumnName)))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.ETC乗務員, BaseCore.Common.Text.CVal(row.Item(tbl.ETC乗務員AColumn.ColumnName)))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.ETC会社, BaseCore.Common.Text.CVal(row.Item(tbl.ETC会社AColumn.ColumnName)))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.出庫時間, BaseCore.Common.Text.Nz(row.Item(tbl.出庫時刻Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.帰庫時間, BaseCore.Common.Text.Nz(row.Item(tbl.帰庫時刻Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.稼動時間, BaseCore.Common.Text.Nz(row.Item(tbl.総勤務時間Column.ColumnName), ""))
                    'xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.金額修正日時, BaseCore.Common.Text.Nz(row.Item(tbl. .車両番号Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.信販, BaseCore.Common.Text.CVal(row.Item(tbl.信販控除除外Column.ColumnName)))

                    intDtlCnt += 1
                Next

            Catch ex As Exception
                Me.LastError = ex.Message
                Return False
            Finally
            End Try
            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region


#End Region


    End Class
End Namespace
