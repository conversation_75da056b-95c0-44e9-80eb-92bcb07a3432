﻿Namespace Frame.Daily
    Partial Public Class Home
        Inherits NodeCore.Common.Frame

#Region "ﾌｨｰﾙﾄﾞ定義"
        Public Class Header
            ''' ----------------------------------------------------------------------------------------
            ''' <SUMMARY>列項目(基本)</SUMMARY>
            ''' ----------------------------------------------------------------------------------------

            Public 車検アラート As BaseCore.Common.Field.ItemData
            Public 健康診断アラート As BaseCore.Common.Field.ItemData

            '''========================================================================================
            ''' <SUMMARY>ｺﾝｽﾄﾗｸﾀ</SUMMARY>
            '''========================================================================================
            Public Sub New(ByVal Config As NodeCore.Common.Config)

                '----------------------------------------------------------------------
                ' ｲﾝｽﾀﾝｽ(基本) 
                '----------------------------------------------------------------------
                Me.車検アラート = New BaseCore.Common.Field.ItemData("車検アラート", 1)
                Me.健康診断アラート = New BaseCore.Common.Field.ItemData("健康診断アラート", 1)

            End Sub
        End Class
#End Region

#Region "変数定義"
        Public Class Paramater
            Public Header As Header
        End Class

        '''========================================================================================
        ''' <summary>ﾍｯﾀﾞ</summary>
        '''========================================================================================
        Private _Fields As New Paramater
        Public Property Fields() As Paramater
            Get
                Return _Fields
            End Get

            Set(ByVal value As Paramater)
                _Fields = value
            End Set
        End Property
#End Region

#Region "ｺﾝｽﾄﾗｸﾀ"
        '''========================================================================================
        ''' <SUMMARY>ｺﾝｽﾄﾗｸﾀ</SUMMARY>
        ''' <PARAM name="Security">ｾｷｭﾘﾃｨ ｸﾗｽ</PARAM>
        ''' <PARAM name="Config">ｺﾝﾌｨｸﾞ ｸﾗｽ</PARAM>
        ''' <PARAM name="PagePath">ﾍﾟｰｼﾞﾊﾟｽ</PARAM>
        '''========================================================================================
        Public Sub New(ByVal Security As NodeCore.Common.Security, ByVal Config As NodeCore.Common.Config, ByVal PagePath As String)
            '----------------------------------------------------------------------
            ' ｾｷｭﾘﾃｨ 設定
            '----------------------------------------------------------------------
            MyBase.New(Security, Config, PagePath)

            Me.Fields.Header = New Header(Config)
        End Sub
#End Region

#Region "ﾍｯﾀﾞ"

#Region "MakeHealthDataTable"
        '''========================================================================================
        ''' <SUMMARY>ﾃﾞｰﾀﾃｰﾌﾞﾙ生成</SUMMARY>
        ''' <RETURNS>ﾃﾞｰﾀﾃｰﾌﾞﾙ</RETURNS>
        '''========================================================================================
        Public Function MakeHealthDataTable() As DataTable
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetViewTableAdapters.V0健康診断アラートTableAdapter
            Dim tbl As New NodeDatabase.DataSetView.V0健康診断アラートDataTable
            Dim qry As New Collection

            '----------------------------------------------------------------------
            ' 条件設定
            '----------------------------------------------------------------------
            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, Me.Security.グループCD, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, Me.Security.個社CD, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.子会社CDColumn.ColumnName, Me.Security.子会社CD, BaseDatabase.Contents.Compare.Equal))

            '----------------------------------------------------------------------
            ' 読込
            '----------------------------------------------------------------------
            tbl = ada.SelectByCommon(qry)


            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return tbl
        End Function
#End Region

#Region "MakeCarExamDataTable"
        '''========================================================================================
        ''' <SUMMARY>ﾃﾞｰﾀﾃｰﾌﾞﾙ生成</SUMMARY>
        ''' <RETURNS>ﾃﾞｰﾀﾃｰﾌﾞﾙ</RETURNS>
        '''========================================================================================
        Public Function MakeCarExamDataTable() As DataTable
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetViewTableAdapters.V0車検アラートTableAdapter
            Dim tbl As New NodeDatabase.DataSetView.V0車検アラートDataTable
            Dim qry As New Collection

            '----------------------------------------------------------------------
            ' 条件設定
            '----------------------------------------------------------------------
            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, Me.Security.グループCD, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, Me.Security.個社CD, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.子会社CDColumn.ColumnName, Me.Security.子会社CD, BaseDatabase.Contents.Compare.Equal))

            '----------------------------------------------------------------------
            ' 読込
            '----------------------------------------------------------------------

            tbl = ada.SelectByCommon(qry)


            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return tbl
        End Function
#End Region

#Region "Execute1:Excel"
        '''========================================================================================
        ''' <SUMMARY>実行</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Execute1_Header() As Boolean
            '----------------------------------------------------------------------
            ' ﾃﾞｰﾀ抽出
            '----------------------------------------------------------------------
            Dim tbl As NodeDatabase.DataSetView.V0健康診断アラートDataTable = Me.MakeHealthDataTable()
            Dim colRefresh As New BaseCore.Common.DataTable.ColumnRefresh

            'colRefresh.Add(tbl.グループCDColumn.ColumnName, "グループCD")
            'colRefresh.Add(tbl.個社CDColumn.ColumnName, "個社CD")
            'colRefresh.Add(tbl.子会社CDColumn.ColumnName, "子会社CD")

            colRefresh.Add(tbl.乗務員CDColumn.ColumnName, "乗務員CD")
            colRefresh.Add(tbl.乗務員名Column.ColumnName, "乗務員名")
            colRefresh.Add(tbl.社員NOColumn.ColumnName, "社員NO")
            colRefresh.Add(tbl.勤務区分Column.ColumnName, "勤務区分")
            colRefresh.Add(tbl.健康診断日Column.ColumnName, "健康診断日")
            colRefresh.Execute(tbl)

            '----------------------------------------------------------------------
            ' ﾃﾞｰﾀ加工(前)
            '----------------------------------------------------------------------
            tbl.TableName = "健康診断アラート"

            '----------------------------------------------------------------------
            ' ｴｸｽﾎﾟｰﾄ
            '----------------------------------------------------------------------
            Dim Excel As New BaseCore.Common.Excel
            If Not Excel.ExcelExport(tbl, Me.Path1) Then
                Me.LastError = Excel.LastError
                Return False
            End If

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region


#Region "Execute2:Excel"
        '''========================================================================================
        ''' <SUMMARY>実行</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Execute2_Header() As Boolean
            '----------------------------------------------------------------------
            ' ﾃﾞｰﾀ抽出
            '----------------------------------------------------------------------
            Dim tbl As NodeDatabase.DataSetView.V0車検アラートDataTable = Me.MakeCarExamDataTable()
            Dim colRefresh As New BaseCore.Common.DataTable.ColumnRefresh

            'colRefresh.Add(tbl.グループCDColumn.ColumnName, "グループCD")
            'colRefresh.Add(tbl.個社CDColumn.ColumnName, "個社CD")
            'colRefresh.Add(tbl.子会社CDColumn.ColumnName, "子会社CD")

            colRefresh.Add(tbl.車両番号Column.ColumnName, "車両番号")
            colRefresh.Add(tbl.車名Column.ColumnName, "車名")
            colRefresh.Add(tbl.車検満了日付Column.ColumnName, "車検満了日付")
            colRefresh.Add(tbl.車種CDColumn.ColumnName, "車種CD")
            colRefresh.Execute(tbl)

            '----------------------------------------------------------------------
            ' ﾃﾞｰﾀ加工(前)
            '----------------------------------------------------------------------

            tbl.DefaultView.Sort = "車検満了日付 ASC"
            Dim dtResult As DataTable = tbl.DefaultView.ToTable()
            dtResult.TableName = "車検アラート"
            '----------------------------------------------------------------------
            ' ｴｸｽﾎﾟｰﾄ
            '----------------------------------------------------------------------
            Dim Excel As New BaseCore.Common.Excel
            If Not Excel.ExcelExport(dtResult, Me.Path1) Then
                Me.LastError = Excel.LastError
                Return False
            End If

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region


#End Region


    End Class
End Namespace
