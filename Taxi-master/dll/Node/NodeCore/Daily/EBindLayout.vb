﻿Namespace Frame.Daily
    Partial Public Class EBindLayout
        Inherits NodeCore.Common.Frame

#Region "ﾌｨｰﾙﾄﾞ定義"
#Region "ﾍｯﾀﾞ"
        Public Class Header
            Public レイアウトCD As BaseCore.Common.Field.ItemData
            Public レイアウト名 As BaseCore.Common.Field.ItemData
            Public 伝票タイプ As BaseCore.Common.Field.ItemData
            Public 荷主CD内部 As BaseCore.Common.Field.ItemData
            Public ファイル形式 As BaseCore.Common.Field.ItemData
            Public 先頭タイトル As BaseCore.Common.Field.ItemData

            Public レイアウトCD削除 As BaseCore.Common.Field.ItemData
            Public ファイル名 As BaseCore.Common.Field.ItemData

            '''========================================================================================
            ''' <SUMMARY>ｺﾝｽﾄﾗｸﾀ</SUMMARY>
            '''========================================================================================
            Public Sub New(ByVal Config As NodeCore.Common.Config)
                Dim tbl As New NodeDatabase.DataSetMaster.MレイアウトHDDataTable

                Me.レイアウトCD = New BaseCore.Common.Field.ItemData(tbl.レイアウトCDColumn)
                Me.レイアウト名 = New BaseCore.Common.Field.ItemData(tbl.レイアウト名Column)
                Me.伝票タイプ = New BaseCore.Common.Field.ItemData(tbl.伝票タイプColumn)
                Me.荷主CD内部 = New BaseCore.Common.Field.ItemData(tbl.荷主CD内部Column)
                Me.ファイル形式 = New BaseCore.Common.Field.ItemData(tbl.ファイル形式Column)
                Me.先頭タイトル = New BaseCore.Common.Field.ItemData(tbl.先頭タイトルColumn)

                Me.レイアウトCD削除 = New BaseCore.Common.Field.ItemData(tbl.レイアウトCDColumn)
                Me.ファイル名 = New BaseCore.Common.Field.ItemData("ファイル名", TypeCode.String)
            End Sub
        End Class
#End Region

#Region "明細(ﾃﾞｰﾀ項目)"
        Public Class Detail1
            Public 行数 As BaseCore.Common.Field.ItemData
            Public 伝票項目CD As BaseCore.Common.Field.ItemData
            Public 伝票項目名 As BaseCore.Common.Field.ItemData
            Public 伝票項目名表示 As BaseCore.Common.Field.ItemData
            Public カラム位置 As BaseCore.Common.Field.ItemData
            Public キーフラグ As BaseCore.Common.Field.ItemData
            Public 取込優先順 As BaseCore.Common.Field.ItemData
            Public タイトル As BaseCore.Common.Field.ItemData
            Public 項目値 As BaseCore.Common.Field.ItemData

            '''========================================================================================
            ''' <SUMMARY>ｺﾝｽﾄﾗｸﾀ</SUMMARY>
            '''========================================================================================
            Public Sub New(ByVal Config As NodeCore.Common.Config)
                Dim tbl As New NodeDatabase.DataSetView.V0レイアウトDataTable

                Me.行数 = New BaseCore.Common.Field.ItemData("行数", TypeCode.Int16)
                Me.伝票項目CD = New BaseCore.Common.Field.ItemData(tbl.伝票項目CDColumn, TypeCode.String)
                Me.伝票項目名 = New BaseCore.Common.Field.ItemData(tbl.伝票項目名Column, TypeCode.String)
                Me.伝票項目名表示 = New BaseCore.Common.Field.ItemData(tbl.伝票項目名表示Column, TypeCode.String)
                Me.カラム位置 = New BaseCore.Common.Field.ItemData(tbl.カラム位置Column, TypeCode.String)
                Me.キーフラグ = New BaseCore.Common.Field.ItemData(tbl.キーフラグColumn, TypeCode.String)
                Me.取込優先順 = New BaseCore.Common.Field.ItemData(tbl.取込優先順Column, 1, 0)
                Me.タイトル = New BaseCore.Common.Field.ItemData("タイトル", TypeCode.String)
                Me.項目値 = New BaseCore.Common.Field.ItemData("項目値", TypeCode.String)
            End Sub
        End Class
#End Region

#Region "明細(伝票項目)"
        Public Class Detail2
            Public 行数 As BaseCore.Common.Field.ItemData
            Public 伝票項目CD As BaseCore.Common.Field.ItemData
            Public 伝票項目名 As BaseCore.Common.Field.ItemData
            Public 伝票項目名表示 As BaseCore.Common.Field.ItemData
            Public 表示位置 As BaseCore.Common.Field.ItemData
            Public キーフラグ As BaseCore.Common.Field.ItemData
            Public 必須フラグ As BaseCore.Common.Field.ItemData
            Public 既定値 As BaseCore.Common.Field.ItemData

            Public 紐付済みフラグ As BaseCore.Common.Field.ItemData

            '''========================================================================================
            ''' <SUMMARY>ｺﾝｽﾄﾗｸﾀ</SUMMARY>
            '''========================================================================================
            Public Sub New(ByVal Config As NodeCore.Common.Config)
                Dim tbl As New NodeDatabase.DataSetMaster.M伝票項目DataTable
                Dim tblレイ As New NodeDatabase.DataSetMaster.MレイアウトDTDataTable

                Me.行数 = New BaseCore.Common.Field.ItemData("行数", TypeCode.Int16)
                Me.伝票項目CD = New BaseCore.Common.Field.ItemData(tbl.伝票項目CDColumn)
                Me.伝票項目名 = New BaseCore.Common.Field.ItemData(tbl.伝票項目名Column)
                Me.伝票項目名表示 = New BaseCore.Common.Field.ItemData(tbl.伝票項目名表示Column)
                Me.表示位置 = New BaseCore.Common.Field.ItemData(tbl.表示位置Column)
                Me.キーフラグ = New BaseCore.Common.Field.ItemData(tbl.キーフラグColumn)
                Me.必須フラグ = New BaseCore.Common.Field.ItemData(tbl.必須フラグColumn)
                Me.既定値 = New BaseCore.Common.Field.ItemData(tblレイ.既定値Column)

                Me.紐付済みフラグ = New BaseCore.Common.Field.ItemData("紐付済みフラグ", TypeCode.String)
            End Sub
        End Class
#End Region

#Region "明細(変換ﾏｽﾀ)"
        Public Class Detail3
            Public 行数 As BaseCore.Common.Field.ItemData
            Public 削除 As BaseCore.Common.Field.ItemData
            Public レイアウトCD As BaseCore.Common.Field.ItemData
            Public 条件カラム位置 As BaseCore.Common.Field.ItemData
            Public 条件項目値 As BaseCore.Common.Field.ItemData
            Public 符号区分 As BaseCore.Common.Field.ItemData
            Public 結果伝票項目CD As BaseCore.Common.Field.ItemData
            Public 結果項目値 As BaseCore.Common.Field.ItemData

            Public 符号区分名 As BaseCore.Common.Field.ItemData

            '''========================================================================================
            ''' <SUMMARY>ｺﾝｽﾄﾗｸﾀ</SUMMARY>
            '''========================================================================================
            Public Sub New(ByVal Config As NodeCore.Common.Config)
                Dim tbl As New NodeDatabase.DataSetView.V0レイアウト条件DataTable

                Me.行数 = New BaseCore.Common.Field.ItemData("行数", TypeCode.Int16)
                Me.削除 = New BaseCore.Common.Field.ItemData("削除", TypeCode.Int16)
                Me.レイアウトCD = New BaseCore.Common.Field.ItemData(tbl.レイアウトCDColumn)
                Me.条件カラム位置 = New BaseCore.Common.Field.ItemData(tbl.条件カラム位置Column, 15, 3)
                Me.条件項目値 = New BaseCore.Common.Field.ItemData(tbl.条件項目値Column)
                Me.符号区分 = New BaseCore.Common.Field.ItemData(tbl.符号区分Column)
                Me.結果伝票項目CD = New BaseCore.Common.Field.ItemData(tbl.結果伝票項目CDColumn)
                Me.結果項目値 = New BaseCore.Common.Field.ItemData(tbl.条件項目値Column)

                Me.符号区分名 = New BaseCore.Common.Field.ItemData(tbl.符号区分名Column)
            End Sub
        End Class
#End Region
#End Region

#Region "共通"
#Region "変数定義"
        Public Class Paramater
            Public Header As Header
            Public Detail1 As New List(Of Detail1)
            Public Detail2 As New List(Of Detail2)
            Public Detail3 As New List(Of Detail3)
        End Class

        '''========================================================================================
        ''' <summary>ﾍｯﾀﾞ</summary>
        '''========================================================================================
        Private _Fields As New Paramater
        Public Property Fields() As Paramater
            Get
                Return _Fields
            End Get

            Set(ByVal value As Paramater)
                _Fields = value
            End Set
        End Property
#End Region

#Region "ｺﾝｽﾄﾗｸﾀ"
        '''========================================================================================
        ''' <SUMMARY>ｺﾝｽﾄﾗｸﾀ</SUMMARY>
        ''' <PARAM name="Security">ｾｷｭﾘﾃｨ ｸﾗｽ</PARAM>
        ''' <PARAM name="Config">ｺﾝﾌｨｸﾞ ｸﾗｽ</PARAM>
        ''' <PARAM name="PagePath">ﾍﾟｰｼﾞﾊﾟｽ</PARAM>
        '''========================================================================================
        Public Sub New(ByVal Security As NodeCore.Common.Security, ByVal Config As NodeCore.Common.Config, ByVal PagePath As String)
            '----------------------------------------------------------------------
            ' ｾｷｭﾘﾃｨ 設定
            '----------------------------------------------------------------------
            MyBase.New(Security, Config, PagePath)

            Me.Fields.Header = New Header(Config)
        End Sub
#End Region
#End Region

#Region "ﾍｯﾀﾞ"
#Region "Clear"
        '''========================================================================================
        ''' <SUMMARY>ｸﾘｱ</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Clear_Header(Optional ByVal KeyClear As Boolean = False) As Boolean
            '----------------------------------------------------------------------
            ' ｷｰ部ｸﾘｱ
            '----------------------------------------------------------------------
            If KeyClear Then
                Me.Fields.Header.レイアウトCD.Value = Nothing
                Me.Fields.Header.レイアウト名.Value = Nothing
                Me.Fields.Header.レイアウトCD削除.Value = Nothing
            End If

            '----------------------------------------------------------------------
            ' ﾃﾞｰﾀ部ｸﾘｱ
            '----------------------------------------------------------------------
            Me.Fields.Header.荷主CD内部.Value = Nothing
            Me.Fields.Header.ファイル形式.Value = NodeContents.Constant.CodeValue.ファイル形式.Excel
            Me.Fields.Header.先頭タイトル.Value = ""

            Me.Fields.Header.ファイル名.Value = Nothing

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "Read"
        '''========================================================================================
        ''' <SUMMARY>読込</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Read_Header() As Boolean
            '----------------------------------------------------------------------
            ' 読み込み
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetViewTableAdapters.V0レイアウトTableAdapter
            Dim tbl As New NodeDatabase.DataSetView.V0レイアウトDataTable
            Dim qry As New Collection

            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl.レイアウトCDColumn.ColumnName, Me.Fields.Header.レイアウトCD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.荷主CD内部Column.ColumnName, Me.Fields.Header.荷主CD内部.Value, BaseDatabase.Contents.Compare.Equal))

            Dim strSort As String = ""
            Dim strDelim As String = ""

            strSort &= strDelim & tbl.レイアウトCDColumn.ColumnName & " ASC" : strDelim = ","

            tbl = ada.SelectByCommon(qry, strSort)
            If tbl.Count > 0 Then
                Me.Fields.Header.レイアウト名.Value = tbl(0).レイアウト名
                Me.Fields.Header.伝票タイプ.Value = tbl(0).伝票タイプ
                Me.Fields.Header.ファイル形式.Value = tbl(0).ファイル形式
                Me.Fields.Header.先頭タイトル.Value = tbl(0).先頭タイトル

                '荷主ｺｰﾄﾞ内部有はOuter側で作成されたという判断の為、ﾘｰﾄﾞする
                'Me.Fields.Header.荷主CD内部.Value = tbl(0).荷主CD内部
                'Me.Fields.Header.荷主CD.Value = tbl(0).荷主CD
                'Me.Fields.Header.荷主名.Value = tbl(0).荷主名1
            End If

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "Execute[先頭行取込]"
#Region "制御"
        '''========================================================================================
        ''' <SUMMARY>先頭行取込</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Execute_Header() As Boolean
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim strNow As String = Format(Now, "yyyy/MM/dd HH:mm:ss")

            '----------------------------------------------------------------------
            ' ﾌｧｲﾙﾀｲﾌﾟ別処理
            '----------------------------------------------------------------------
            Select Case True
                Case Me.Fields.Header.ファイル形式.Value = NodeContents.Constant.CodeValue.ファイル形式.Excel
                    If Not Excel_Top(strNow) Then
                        Return False
                    End If

                Case Me.Fields.Header.ファイル形式.Value = NodeContents.Constant.CodeValue.ファイル形式.Csv
                    If Not Csv_Top(strNow, NodeContents.Constant.CodeValue.ファイル形式.Csv) Then
                        Return False
                    End If
            End Select

            '----------------------------------------------------------------------
            ' 返却
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "Excel"
        '''========================================================================================
        ''' <SUMMARY>Excel</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Function Excel_Top(ByVal strNow As String) As Boolean
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetWorkTableAdapters.W先頭行TableAdapter
            Dim tbl As New NodeDatabase.DataSetWork.W先頭行DataTable
            Dim qry As New Collection

            Dim Excel As New BaseCore.Common.Excel

            '----------------------------------------------------------------------
            ' ｵｰﾌﾟﾝ
            '----------------------------------------------------------------------
            If Not Excel.Open(Me.Path1) Then
                Me.LastError = "正しいファイルを選択してください。"
                Return False
            End If

            '----------------------------------------------------------------------
            ' 事前削除
            '----------------------------------------------------------------------
            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl.セッションIDColumn.ColumnName, Me.Security.セッションID, BaseDatabase.Contents.Compare.Equal))

            Try
                ada.DeleteByCommon(qry)
            Catch ex As Exception
                Me.LastError = ex.Message
                Return False
            End Try

            '----------------------------------------------------------------------
            ' ｱｸﾃｨﾌﾞｼｰﾄ
            '----------------------------------------------------------------------
            Excel.SheetSelect(1)

            Dim RowMax As Long = Excel.LastRowIndex             '最大行
            Dim ClmMax As Long = Excel.LastColIndex             '最大列

            Try
                For i As Long = 1 To RowMax
                    '------------------------------------------------------------------
                    ' 追加
                    '------------------------------------------------------------------
                    If Not Me.Fields.Header.先頭タイトル.Value = NodeContents.Constant.CodeValue.フラグID.オフ Then
                        If i = 1 Then
                            For cnt As Integer = 1 To ClmMax
                                ada.Insert(MyBase.Security.セッションID _
                                         , BaseCore.Common.Text.Nz(Excel.CellGet(i, cnt).Value, "") _
                                         , "" _
                                         , cnt
                                          )
                            Next
                        Else
                            For cnt As Integer = 1 To ClmMax

                                qry.Clear()
                                qry.Add(New BaseDatabase.Condition(tbl.セッションIDColumn.ColumnName, Me.Security.セッションID, BaseDatabase.Contents.Compare.Equal))
                                qry.Add(New BaseDatabase.Condition(tbl.カラム位置Column.ColumnName, cnt, BaseDatabase.Contents.Compare.Equal))

                                tbl = ada.SelectByCommon(qry)
                                If tbl.Count > 0 Then
                                    tbl(0).項目値 = BaseCore.Common.Text.Nz(Excel.CellGet(i, cnt).Value, "")
                                    ada.Update(tbl)
                                Else
                                    ada.Insert(MyBase.Security.セッションID _
                                             , "" _
                                             , BaseCore.Common.Text.Nz(Excel.CellGet(i, cnt).Value, "") _
                                             , cnt
                                              )
                                End If
                            Next

                            'ﾀｲﾄﾙ有り 終了
                            Return True
                        End If
                    Else
                        For cnt As Integer = 1 To ClmMax
                            ada.Insert(MyBase.Security.セッションID _
                                     , "" _
                                     , BaseCore.Common.Text.Nz(Excel.CellGet(i, cnt).Value, "") _
                                     , cnt
                                      )
                        Next

                        'ﾀｲﾄﾙ無し 終了
                        Return True
                    End If
                Next
            Finally
                Excel.Close(Me.Path1)
            End Try

        End Function
#End Region

#Region "Csv/Text"
        Protected Function Csv_Top(ByVal strNow As String, ByVal FileType As String) As Boolean
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetWorkTableAdapters.W先頭行TableAdapter
            Dim tbl As New NodeDatabase.DataSetWork.W先頭行DataTable
            Dim qry As New Collection

            Dim i As Long

            '----------------------------------------------------------------------
            ' ﾌｧｲﾙの読み込み
            '----------------------------------------------------------------------
            Dim psrCsv As New Microsoft.VisualBasic.FileIO.TextFieldParser(Me.Path1, System.Text.Encoding.GetEncoding("Shift_JIS"))
            psrCsv.TextFieldType = Microsoft.VisualBasic.FileIO.FieldType.Delimited

            If FileType = NodeContents.Constant.CodeValue.ファイル形式.Csv Then
                psrCsv.SetDelimiters(",")       '区切り文字はｶﾝﾏ
            Else
                psrCsv.SetDelimiters(vbTab)     '区切り文字はﾀﾌﾞ
            End If

            '----------------------------------------------------------------------
            ' 事前削除
            '----------------------------------------------------------------------
            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl.セッションIDColumn.ColumnName, Me.Security.セッションID, BaseDatabase.Contents.Compare.Equal))

            Try
                ada.DeleteByCommon(qry)
            Catch ex As Exception
                Me.LastError = ex.Message
                Return False
            End Try

            '----------------------------------------------------------------------
            ' ｲﾝﾎﾟｰﾄ
            '----------------------------------------------------------------------
            Try
                Do While Not psrCsv.EndOfData
                    '------------------------------------------------------------------
                    ' 1行読み込み
                    '------------------------------------------------------------------
                    Dim arrCSV As String() = psrCsv.ReadFields()

                    '------------------------------------------------------------------
                    ' ｶｳﾝﾄｱｯﾌﾟ
                    '------------------------------------------------------------------
                    i += 1

                    '------------------------------------------------------------------
                    ' 追加
                    '------------------------------------------------------------------
                    If Not Me.Fields.Header.先頭タイトル.Value = NodeContents.Constant.CodeValue.フラグID.オフ Then
                        If i = 1 Then
                            For cnt As Integer = 0 To arrCSV.Length - 1
                                ada.Insert(MyBase.Security.セッションID _
                                         , BaseCore.Common.Text.Nz(arrCSV.GetValue(cnt), "") _
                                         , "" _
                                         , cnt
                                          )
                            Next
                        Else
                            For cnt As Integer = 0 To arrCSV.Length - 1

                                qry.Clear()
                                qry.Add(New BaseDatabase.Condition(tbl.セッションIDColumn.ColumnName, Me.Security.セッションID, BaseDatabase.Contents.Compare.Equal))
                                qry.Add(New BaseDatabase.Condition(tbl.カラム位置Column.ColumnName, cnt, BaseDatabase.Contents.Compare.Equal))

                                tbl = ada.SelectByCommon(qry)
                                If tbl.Count > 0 Then
                                    tbl(0).項目値 = BaseCore.Common.Text.Nz(arrCSV.GetValue(cnt), "")
                                    ada.Update(tbl)
                                Else
                                    ada.Insert(MyBase.Security.セッションID _
                                             , "" _
                                             , BaseCore.Common.Text.Nz(arrCSV.GetValue(cnt), "") _
                                             , cnt
                                              )
                                End If
                            Next

                            'ﾀｲﾄﾙ有り 終了
                            Return True
                        End If
                    Else
                        For cnt As Integer = 0 To arrCSV.Length - 1
                            ada.Insert(MyBase.Security.セッションID _
                                     , "" _
                                     , BaseCore.Common.Text.Nz(arrCSV.GetValue(cnt), "") _
                                     , cnt
                                      )
                        Next

                        'ﾀｲﾄﾙ無し 終了
                        Return True
                    End If
                Loop
            Finally
                psrCsv.Close()
            End Try
        End Function
#End Region

#Region "Valid_Execute(先頭行取込)"
        '''========================================================================================
        ''' <SUMMARY>ﾍｯﾀﾞ(共通)</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Valid_Execute_Header_Common() As NodeContents.ActionButton.ErrorLevel
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim strMsg As String = ""
            Dim Validator As New BaseCore.Common.Validator

            '------------------------------------------------------------------
            ' ｴﾗｰｸﾘｱ
            '------------------------------------------------------------------
            Me.Fields.Header.レイアウトCD.IsError = False
            Me.Fields.Header.レイアウト名.IsError = False
            Me.Fields.Header.伝票タイプ.IsError = False
            Me.Fields.Header.荷主CD内部.IsError = False
            Me.Fields.Header.ファイル形式.IsError = False
            Me.Fields.Header.先頭タイトル.IsError = False
            Me.Fields.Header.ファイル名.IsError = False

            '------------------------------------------------------------------
            ' 共通検査
            '------------------------------------------------------------------
            If Not Validator.BaseChecker(Me.Fields.Header.レイアウトCD) Then : Me.Fields.Header.レイアウトCD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.レイアウト名) Then : Me.Fields.Header.レイアウト名.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.伝票タイプ) Then : Me.Fields.Header.伝票タイプ.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.荷主CD内部) Then : Me.Fields.Header.荷主CD内部.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.ファイル形式) Then : Me.Fields.Header.ファイル形式.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.先頭タイトル) Then : Me.Fields.Header.先頭タイトル.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.ファイル名) Then : Me.Fields.Header.ファイル名.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If

            '------------------------------------------------------------------
            ' 返却
            '------------------------------------------------------------------
            MyBase.LastError = strMsg
            Select Case True
                Case strMsg = ""
                    Return NodeContents.ActionButton.ErrorLevel.None
                Case Else
                    Return NodeContents.ActionButton.ErrorLevel.Fatal
            End Select
        End Function

        '''========================================================================================
        ''' <SUMMARY>検査(個別)</SUMMARY>
        ''' <RETURNS>True:正常, False:不正</RETURNS>
        '''========================================================================================
        Protected Overrides Function Valid_Execute_Header_Another() As NodeContents.ActionButton.ErrorLevel
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim strMsg As String = ""
            Dim Validator As New BaseCore.Common.Validator

            '------------------------------------------------------------------
            ' 個別検査
            '------------------------------------------------------------------
            Dim str変換拡張子 As String = System.IO.Path.GetExtension(Me.Fields.Header.ファイル名.Value).ToLower()
            '------------------------------------
            ' ﾌｧｲﾙ形式検査
            '------------------------------------
            Select Case Me.Fields.Header.ファイル形式.Value
                Case NodeContents.Constant.CodeValue.ファイル形式.Excel
                    If Not Me.Fields.Header.ファイル名.Value Like "*.xls" _
                   And Not Me.Fields.Header.ファイル名.Value Like "*.xlsx" Then
                        strMsg &= "選択されたファイル形式と対象のファイル形式が一致しません。"
                    End If
                Case NodeContents.Constant.CodeValue.ファイル形式.Csv
                    If Not str変換拡張子 = ".csv" Then
                        strMsg &= "選択されたファイル形式と対象のファイル形式が一致しません。"
                    End If
                Case Else
            End Select

            '------------------------------------------------------------------
            ' 返却
            '------------------------------------------------------------------
            MyBase.LastError = strMsg
            Select Case True
                Case strMsg = ""
                    Return NodeContents.ActionButton.ErrorLevel.None
                Case Else
                    Return NodeContents.ActionButton.ErrorLevel.Fatal
            End Select
        End Function
#End Region
#End Region

#Region "Write"
#Region "ﾚｲｱｳﾄ保存"
        Public Function Write_Layout(ByRef strレイアウトCD As String) As Boolean
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim adaHD As New NodeDatabase.DataSetMasterTableAdapters.MレイアウトHDTableAdapter
            Dim tblHD As New NodeDatabase.DataSetMaster.MレイアウトHDDataTable
            Dim qryHD As New Collection

            Dim adaDT As New NodeDatabase.DataSetMasterTableAdapters.MレイアウトDTTableAdapter
            Dim tblDT As New NodeDatabase.DataSetMaster.MレイアウトDTDataTable
            Dim qryDT As New Collection

            Dim ada条件 As New NodeDatabase.DataSetMasterTableAdapters.Mレイアウト条件TableAdapter
            Dim tbl条件 As New NodeDatabase.DataSetMaster.Mレイアウト条件DataTable
            Dim qry条件 As New Collection

            Dim ada項目 As New NodeDatabase.DataSetMasterTableAdapters.M伝票項目TableAdapter
            Dim tbl項目 As New NodeDatabase.DataSetMaster.M伝票項目DataTable
            Dim qry項目 As New Collection

            Dim ada今回 As New NodeDatabase.DataSetMasterTableAdapters.Mレイアウト今回TableAdapter
            Dim tbl今回 As New NodeDatabase.DataSetMaster.Mレイアウト今回DataTable
            Dim qry今回 As New Collection

            Dim strNow As String = Format(Now, "yyyy/MM/dd HH:mm:ss")

            '----------------------------------------------------------------------
            ' 抽出
            '----------------------------------------------------------------------
            strレイアウトCD = Me.Fields.Header.レイアウトCD.Value

            qryHD.Clear()
            qryHD.Add(New BaseDatabase.Condition(tblHD.レイアウトCDColumn.ColumnName, Me.Fields.Header.レイアウトCD.Value, BaseDatabase.Contents.Compare.Equal))
            qryHD.Add(New BaseDatabase.Condition(tblHD.荷主CD内部Column.ColumnName, Me.Fields.Header.荷主CD内部.Value, BaseDatabase.Contents.Compare.Equal))

            tblHD = adaHD.SelectByCommon(qryHD)

            Try
                Using scope As New System.Transactions.TransactionScope(System.Transactions.TransactionScopeOption.Required, TimeSpan.Zero)
                    'ﾍｯﾀﾞ
                    If tblHD.Count > 0 Then
                        '更新
                        tblHD(0).レイアウト名 = Me.Fields.Header.レイアウト名.Value
                        tblHD(0).荷主CD内部 = Me.Fields.Header.荷主CD内部.Value
                        tblHD(0).ファイル形式 = Me.Fields.Header.ファイル形式.Value
                        tblHD(0).先頭タイトル = Me.Fields.Header.先頭タイトル.Value
                        tblHD(0).更新日時 = strNow
                        tblHD(0).更新者名 = MyBase.Security.ユーザー名

                        Try
                            adaHD.Update(tblHD)
                        Catch ex As Exception
                            MyBase.LastError = "以下のエラーのため、更新できませんでした。" & vbCrLf & ex.Message
                            Return False
                        End Try
                    Else
                        '--------------------------------
                        ' 採番
                        '--------------------------------
                        Try
                            Dim Numbering As New NodeCore.Common.Numbering
                            If Not Numbering.Execute(NodeCore.Common.Numbering.ModeContents.レイアウト) Then
                                Me.LastError = Numbering.LastError
                                Return False
                            End If

                            strレイアウトCD = Numbering.Value

                            '新規
                            adaHD.Insert(strレイアウトCD _
                                           , Me.Fields.Header.レイアウト名.Value _
                                           , Me.Fields.Header.荷主CD内部.Value _
                                           , Me.Fields.Header.伝票タイプ.Value _
                                           , Me.Fields.Header.ファイル形式.Value _
                                           , Me.Fields.Header.先頭タイトル.Value _
                                           , "" _
                                           , strNow _
                                           , strNow _
                                           , MyBase.Security.ユーザー名
                                            )
                        Catch ex As Exception
                            MyBase.LastError = "以下のエラーのため、追加できませんでした。" & vbCrLf & ex.Message
                            Return False
                        End Try
                    End If

                    '明細
                    '----------------------------------------------------------------------
                    ' 一旦削除
                    '----------------------------------------------------------------------
                    qryDT.Clear()
                    qryDT.Add(New BaseDatabase.Condition(tblDT.レイアウトCDColumn.ColumnName, Me.Fields.Header.レイアウトCD.Value, BaseDatabase.Contents.Compare.Equal))
                    qryDT.Add(New BaseDatabase.Condition(tblDT.荷主CD内部Column.ColumnName, Me.Fields.Header.荷主CD内部.Value, BaseDatabase.Contents.Compare.Equal))

                    Try
                        adaDT.DeleteByCommon(qryDT)
                    Catch ex As Exception
                        Me.LastError = ex.Message
                        Return False
                    End Try

                    Try
                        '紐付け
                        For i As Integer = 0 To Me.Fields.Detail1.Count - 1
                            If BaseCore.Common.Text.Nz(Me.Fields.Detail1(i).伝票項目CD.Value, "") <> "" Then

                                Dim str伝票項目CD() As String = Me.Fields.Detail1(i).伝票項目CD.Value.ToString.Split(",")

                                For j As Integer = 0 To str伝票項目CD.Length - 1
                                    qry項目.Clear()
                                    qry項目.Add(New BaseDatabase.Condition(tbl項目.伝票タイプColumn.ColumnName, Me.Fields.Header.伝票タイプ.Value, BaseDatabase.Contents.Compare.Equal))
                                    qry項目.Add(New BaseDatabase.Condition(tbl項目.伝票項目CDColumn.ColumnName, str伝票項目CD(j), BaseDatabase.Contents.Compare.Equal))

                                    tbl項目 = ada項目.SelectByCommon(qry項目)
                                    If tbl項目.Count > 0 Then
                                        adaDT.Insert(strレイアウトCD _
                                                       , Me.Fields.Header.荷主CD内部.Value _
                                                       , str伝票項目CD(j) _
                                                       , BaseCore.Common.Text.CVal(Me.Fields.Detail1(i).カラム位置.Value) _
                                                       , "" _
                                                       , tbl項目(0).Item(tbl項目.キーフラグColumn.ColumnName).ToString _
                                                       , BaseCore.Common.Text.CVal(Me.Fields.Detail1(i).取込優先順.Value)
                                                        )
                                    End If
                                Next
                            End If
                        Next

                        '既定値
                        For i As Integer = 0 To Me.Fields.Detail2.Count - 1
                            If BaseCore.Common.Text.Nz(Me.Fields.Detail2(i).既定値.Value, "") <> "" Then
                                qryDT.Clear()
                                qryDT.Add(New BaseDatabase.Condition(tblDT.レイアウトCDColumn.ColumnName, Me.Fields.Header.レイアウトCD.Value, BaseDatabase.Contents.Compare.Equal))
                                qryDT.Add(New BaseDatabase.Condition(tblDT.荷主CD内部Column.ColumnName, Me.Fields.Header.荷主CD内部.Value, BaseDatabase.Contents.Compare.Equal))
                                qryDT.Add(New BaseDatabase.Condition(tblDT.伝票項目CDColumn.ColumnName, Me.Fields.Detail2(i).伝票項目CD.Value, BaseDatabase.Contents.Compare.Equal))

                                tblDT = adaDT.SelectByCommon(qryDT)
                                If tblDT.Count > 0 Then
                                    For Each rowDT As NodeDatabase.DataSetMaster.MレイアウトDTRow In tblDT.Rows
                                        rowDT.既定値 = Me.Fields.Detail2(i).既定値.Value
                                    Next
                                    adaDT.Update(tblDT)
                                Else
                                    adaDT.Insert(strレイアウトCD _
                                                   , Me.Fields.Header.荷主CD内部.Value _
                                                   , Me.Fields.Detail2(i).伝票項目CD.Value _
                                                   , 9999 _
                                                   , Me.Fields.Detail2(i).既定値.Value _
                                                   , Me.Fields.Detail2(i).キーフラグ.Value _
                                                   , 0
                                                    )
                                End If
                            End If
                        Next
                    Catch ex As Exception
                        Me.LastError = ex.Message
                        Return False
                    End Try

                    '条件
                    '----------------------------------------------------------------------
                    ' 一旦削除
                    '----------------------------------------------------------------------
                    qry条件.Clear()
                    qry条件.Add(New BaseDatabase.Condition(tbl条件.レイアウトCDColumn.ColumnName, Me.Fields.Header.レイアウトCD.Value, BaseDatabase.Contents.Compare.Equal))

                    Try
                        ada条件.DeleteByCommon(qry条件)
                    Catch ex As Exception
                        Me.LastError = ex.Message
                        Return False
                    End Try

                    '条件
                    For i As Integer = 0 To Me.Fields.Detail3.Count - 1
                        If Not Empty_Detail3(Me.Fields.Detail3(i)) Then
                            ada条件.Insert(strレイアウトCD _
                                             , Me.Fields.Header.荷主CD内部.Value _
                                             , Me.Fields.Detail3(i).条件カラム位置.Value _
                                             , Me.Fields.Detail3(i).条件項目値.Value _
                                             , Me.Fields.Detail3(i).符号区分.Value _
                                             , Me.Fields.Detail3(i).結果伝票項目CD.Value _
                                             , Me.Fields.Detail3(i).結果項目値.Value
                                            )
                        End If
                    Next

                    '今回
                    '----------------------------------------------------------------------
                    ' 一旦削除
                    '----------------------------------------------------------------------
                    qry今回.Clear()
                    qry今回.Add(New BaseDatabase.Condition(tbl今回.レイアウトCDColumn.ColumnName, Me.Fields.Header.レイアウトCD.Value, BaseDatabase.Contents.Compare.Equal))
                    qry今回.Add(New BaseDatabase.Condition(tbl今回.荷主CD内部Column.ColumnName, Me.Fields.Header.荷主CD内部.Value, BaseDatabase.Contents.Compare.Equal))

                    Try
                        ada今回.DeleteByCommon(qry今回)
                    Catch ex As Exception
                        Me.LastError = ex.Message
                        Return False
                    End Try

                    '条件
                    For i As Integer = 0 To Me.Fields.Detail1.Count - 1
                        ada今回.Insert(strレイアウトCD _
                                         , Me.Fields.Header.荷主CD内部.Value _
                                         , Me.Fields.Detail1(i).カラム位置.Value _
                                         , Me.Fields.Detail1(i).タイトル.Value _
                                         , Me.Fields.Detail1(i).項目値.Value _
                                         , Me.Fields.Detail1(i).伝票項目CD.Value _
                                         , Me.Fields.Detail1(i).取込優先順.Value
                                        )
                    Next

                    scope.Complete()
                End Using
            Catch ex As Exception
                MyBase.LastError = "以下のエラーのため、処理を継続できません。" & vbCrLf & ex.Message
                Return False
            End Try

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "Valid_Write"
        '''========================================================================================
        ''' <SUMMARY>検査(共通)</SUMMARY>
        ''' <RETURNS>True:正常, False:不正</RETURNS>
        '''========================================================================================
        Protected Overrides Function Valid_Write_Header_Common() As NodeContents.ActionButton.ErrorLevel
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim strMsg As String = ""
            Dim Validator As New BaseCore.Common.Validator

            '------------------------------------------------------------------
            ' ｴﾗｰｸﾘｱ
            '------------------------------------------------------------------
            Me.Fields.Header.レイアウトCD.IsError = False
            Me.Fields.Header.レイアウト名.IsError = False
            Me.Fields.Header.伝票タイプ.IsError = False
            Me.Fields.Header.荷主CD内部.IsError = False
            Me.Fields.Header.ファイル形式.IsError = False
            Me.Fields.Header.先頭タイトル.IsError = False
            Me.Fields.Header.ファイル名.IsError = False

            '------------------------------------------------------------------
            ' 共通検査
            '------------------------------------------------------------------
            If Not Validator.BaseChecker(Me.Fields.Header.レイアウトCD) Then : Me.Fields.Header.レイアウトCD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.レイアウト名) Then : Me.Fields.Header.レイアウト名.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.伝票タイプ) Then : Me.Fields.Header.伝票タイプ.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.荷主CD内部) Then : Me.Fields.Header.荷主CD内部.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.ファイル形式) Then : Me.Fields.Header.ファイル形式.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.先頭タイトル) Then : Me.Fields.Header.先頭タイトル.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.ファイル名) Then : Me.Fields.Header.ファイル名.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If

            '------------------------------------------------------------------
            ' 返却
            '------------------------------------------------------------------
            MyBase.LastError = strMsg
            Select Case True
                Case strMsg = ""
                    Return NodeContents.ActionButton.ErrorLevel.None
                Case Else
                    Return NodeContents.ActionButton.ErrorLevel.Fatal
            End Select
        End Function

        '''========================================================================================
        ''' <SUMMARY>検査(個別)</SUMMARY>
        ''' <RETURNS>True:正常, False:不正</RETURNS>
        '''========================================================================================
        Protected Overrides Function Valid_Write_Header_Another() As NodeContents.ActionButton.ErrorLevel
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim strMsg As String = ""
            Dim Validator As New BaseCore.Common.Validator

            Dim ada As New NodeDatabase.DataSetMasterTableAdapters.M伝票項目TableAdapter
            Dim tbl As New NodeDatabase.DataSetMaster.M伝票項目DataTable
            Dim qry As New Collection

            '------------------------------------------------------------------
            ' 個別検査
            '------------------------------------------------------------------
            '------------------------------------
            ' 紐付必須検査
            '------------------------------------
            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl.使用フラグ倉庫Column.ColumnName, NodeContents.Constant.CodeValue.フラグID.オン, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.伝票タイプColumn.ColumnName, Me.Fields.Header.伝票タイプ.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.必須フラグColumn.ColumnName, NodeContents.Constant.CodeValue.フラグID.オン, BaseDatabase.Contents.Compare.Equal))

            tbl = ada.SelectByCommon(qry)
            For Each row As NodeDatabase.DataSetMaster.M伝票項目Row In tbl.Rows
                Dim blnErr As Boolean = True

                '------------------------------------
                ' 紐付
                '------------------------------------
                For i As Integer = 0 To Me.Fields.Detail1.Count - 1
                    Select Case True
                        Case blnErr = False
                            Continue For

                        Case Me.Fields.Detail1(i).伝票項目CD.Value.ToString = ""
                            Continue For

                        Case row.Item(tbl.伝票項目CDColumn.ColumnName) = Me.Fields.Detail1(i).伝票項目CD.Value
                            blnErr = False
                    End Select
                Next

                '------------------------------------
                ' 既定値
                '------------------------------------
                If blnErr Then
                    For i As Integer = 0 To Me.Fields.Detail2.Count - 1
                        Select Case True
                            Case blnErr = False
                                Continue For

                            Case row.Item(tbl.伝票項目CDColumn.ColumnName) = Me.Fields.Detail2(i).伝票項目CD.Value And Me.Fields.Detail2(i).既定値.Value <> ""
                                blnErr = False
                        End Select
                    Next
                End If

                If blnErr Then
                    strMsg &= "【" & row.Item(tbl.伝票項目名表示Column.ColumnName) & "】に対して紐付けを設定、又は既定値を入力してください。" & vbCrLf
                End If
            Next

            '------------------------------------------------------------------
            ' 返却
            '------------------------------------------------------------------
            MyBase.LastError = strMsg
            Select Case True
                Case strMsg = ""
                    Return NodeContents.ActionButton.ErrorLevel.None
                Case Else
                    Return NodeContents.ActionButton.ErrorLevel.Fatal
            End Select
        End Function
#End Region
#End Region

#Region "Delete"
        '''========================================================================================
        ''' <SUMMARY>削除</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Delete_Header() As Boolean
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim adaHD As New NodeDatabase.DataSetMasterTableAdapters.MレイアウトHDTableAdapter
            Dim tblHD As New NodeDatabase.DataSetMaster.MレイアウトHDDataTable
            Dim qryHD As New Collection

            Dim adaDT As New NodeDatabase.DataSetMasterTableAdapters.MレイアウトDTTableAdapter
            Dim tblDT As New NodeDatabase.DataSetMaster.MレイアウトDTDataTable
            Dim qryDT As New Collection

            Dim ada条件 As New NodeDatabase.DataSetMasterTableAdapters.Mレイアウト条件TableAdapter
            Dim tbl条件 As New NodeDatabase.DataSetMaster.Mレイアウト条件DataTable
            Dim qry条件 As New Collection

            Dim ada今回 As New NodeDatabase.DataSetMasterTableAdapters.Mレイアウト今回TableAdapter
            Dim tbl今回 As New NodeDatabase.DataSetMaster.Mレイアウト今回DataTable
            Dim qry今回 As New Collection

            Try
                Using scope As New System.Transactions.TransactionScope(System.Transactions.TransactionScopeOption.Required, TimeSpan.Zero)
                    '----------------------------------------------------------------------
                    ' ﾍｯﾀﾞ
                    '----------------------------------------------------------------------
                    qryHD.Clear()
                    qryHD.Add(New BaseDatabase.Condition(tblHD.レイアウトCDColumn.ColumnName, Me.Fields.Header.レイアウトCD削除.Value, BaseDatabase.Contents.Compare.Equal))
                    qryHD.Add(New BaseDatabase.Condition(tblHD.荷主CD内部Column.ColumnName, Me.Fields.Header.荷主CD内部.Value, BaseDatabase.Contents.Compare.Equal))

                    Try
                        adaHD.DeleteByCommon(qryHD)
                    Catch ex As Exception
                        Me.LastError = ex.Message
                        Return False
                    End Try

                    '----------------------------------------------------------------------
                    ' 明細
                    '----------------------------------------------------------------------
                    qryDT.Clear()
                    qryDT.Add(New BaseDatabase.Condition(tblDT.レイアウトCDColumn.ColumnName, Me.Fields.Header.レイアウトCD削除.Value, BaseDatabase.Contents.Compare.Equal))
                    qryDT.Add(New BaseDatabase.Condition(tblDT.荷主CD内部Column.ColumnName, Me.Fields.Header.荷主CD内部.Value, BaseDatabase.Contents.Compare.Equal))

                    Try
                        adaDT.DeleteByCommon(qryDT)
                    Catch ex As Exception
                        Me.LastError = ex.Message
                        Return False
                    End Try

                    '----------------------------------------------------------------------
                    ' 条件
                    '----------------------------------------------------------------------
                    qry条件.Clear()
                    qry条件.Add(New BaseDatabase.Condition(tbl条件.レイアウトCDColumn.ColumnName, Me.Fields.Header.レイアウトCD削除.Value, BaseDatabase.Contents.Compare.Equal))

                    Try
                        ada条件.DeleteByCommon(qry条件)
                    Catch ex As Exception
                        Me.LastError = ex.Message
                        Return False
                    End Try

                    '----------------------------------------------------------------------
                    ' 今回
                    '----------------------------------------------------------------------
                    qry今回.Clear()
                    qry今回.Add(New BaseDatabase.Condition(tbl今回.レイアウトCDColumn.ColumnName, Me.Fields.Header.レイアウトCD削除.Value, BaseDatabase.Contents.Compare.Equal))
                    qry今回.Add(New BaseDatabase.Condition(tbl今回.荷主CD内部Column.ColumnName, Me.Fields.Header.荷主CD内部.Value, BaseDatabase.Contents.Compare.Equal))

                    Try
                        ada今回.DeleteByCommon(qry今回)
                    Catch ex As Exception
                        Me.LastError = ex.Message
                        Return False
                    End Try

                    scope.Complete()
                End Using
            Catch ex As Exception
                MyBase.LastError = "以下のエラーのため、削除できませんでした。" & vbCrLf & ex.Message
                Return False
            End Try

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function

#Region "Valid_Delete"
        '''========================================================================================
        ''' <SUMMARY>検査(共通)</SUMMARY>
        ''' <RETURNS>True:正常, False:不正</RETURNS>
        '''========================================================================================
        Protected Overrides Function Valid_Delete_Header_Common() As NodeContents.ActionButton.ErrorLevel
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim strMsg As String = ""
            Dim Validator As New BaseCore.Common.Validator

            '------------------------------------------------------------------
            ' ｴﾗｰｸﾘｱ
            '------------------------------------------------------------------
            Me.Fields.Header.レイアウトCD削除.IsError = False

            '------------------------------------------------------------------
            ' 共通検査
            '------------------------------------------------------------------
            If Not Validator.BaseChecker(Me.Fields.Header.レイアウトCD削除) Then : Me.Fields.Header.レイアウトCD削除.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If

            '------------------------------------------------------------------
            ' 返却
            '------------------------------------------------------------------
            MyBase.LastError = strMsg
            Select Case True
                Case strMsg = ""
                    Return NodeContents.ActionButton.ErrorLevel.None
                Case Else
                    Return NodeContents.ActionButton.ErrorLevel.Fatal
            End Select
        End Function

        '''========================================================================================
        ''' <SUMMARY>検査(個別)</SUMMARY>
        ''' <RETURNS>True:正常, False:不正</RETURNS>
        '''========================================================================================
        Protected Overrides Function Valid_Delete_Header_Another() As NodeContents.ActionButton.ErrorLevel
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim strMsg As String = ""
            Dim Validator As New BaseCore.Common.Validator

            '------------------------------------------------------------------
            ' 個別検査
            '------------------------------------------------------------------

            '------------------------------------------------------------------
            ' 返却
            '------------------------------------------------------------------
            MyBase.LastError = strMsg
            Select Case True
                Case strMsg = ""
                    Return NodeContents.ActionButton.ErrorLevel.None
                Case Else
                    Return NodeContents.ActionButton.ErrorLevel.Fatal
            End Select
        End Function
#End Region
#End Region

#Region "ﾜｰｸﾃーﾌﾞﾙ削除"
        '''========================================================================================
        ''' <SUMMARY>ﾜｰｸﾃーﾌﾞﾙ削除</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Public Function WorkDelete() As Boolean
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim ada先頭 As New NodeDatabase.DataSetWorkTableAdapters.W先頭行TableAdapter
            Dim tbl先頭 As New NodeDatabase.DataSetWork.W先頭行DataTable
            Dim qry先頭 As New Collection

            '----------------------------------------------------------------------
            ' 事前削除
            '----------------------------------------------------------------------
            qry先頭.Clear()
            qry先頭.Add(New BaseDatabase.Condition(tbl先頭.セッションIDColumn.ColumnName, Me.Security.セッションID, BaseDatabase.Contents.Compare.Equal))

            Try
                ada先頭.DeleteByCommon(qry先頭)
            Catch ex As Exception
                Me.LastError = ex.Message
                Return False
            End Try

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region
#End Region

#Region "明細1"
#Region "Clear"
        '''========================================================================================
        ''' <SUMMARY>ｸﾘｱ</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Clear_Detail1() As Boolean
            '----------------------------------------------------------------------
            ' ｸﾘｱ
            '----------------------------------------------------------------------
            For i As Integer = 0 To Me.Fields.Detail1.Count - 1
                Me.Clear_Detail1(i)
            Next

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function

        Public Overrides Function Clear_Detail1(ByVal Index As Integer) As Boolean
            Me.Fields.Detail1(Index).行数.Value = Index + 1
            Me.Fields.Detail1(Index).伝票項目CD.Value = Nothing
            Me.Fields.Detail1(Index).伝票項目名.Value = Nothing
            Me.Fields.Detail1(Index).伝票項目名表示.Value = Nothing
            Me.Fields.Detail1(Index).カラム位置.Value = Nothing
            Me.Fields.Detail1(Index).キーフラグ.Value = Nothing
            Me.Fields.Detail1(Index).取込優先順.Value = Nothing
            Me.Fields.Detail1(Index).タイトル.Value = Nothing
            Me.Fields.Detail1(Index).項目値.Value = Nothing
        End Function
#End Region

#Region "Read"
        '''========================================================================================
        ''' <SUMMARY>読込</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Read_Detail1() As Boolean
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim ada新規 As New NodeDatabase.DataSetWorkTableAdapters.W先頭行TableAdapter
            Dim tbl新規 As New NodeDatabase.DataSetWork.W先頭行DataTable
            Dim qry新規 As New Collection

            Dim ada既存 As New NodeDatabase.DataSetViewTableAdapters.V0レイアウト今回TableAdapter
            Dim tbl既存 As New NodeDatabase.DataSetView.V0レイアウト今回DataTable
            Dim qry既存 As New Collection

            Dim strSort As String = ""
            Dim strDelim As String = ""

            '----------------------------------------------------------------------
            ' 読み込み(新規ｱｯﾌﾟﾛｰﾄﾞ時)
            '----------------------------------------------------------------------
            qry新規.Clear()
            qry新規.Add(New BaseDatabase.Condition(tbl新規.セッションIDColumn.ColumnName, MyBase.Security.セッションID, BaseDatabase.Contents.Compare.Equal))

            strSort = ""
            strDelim = ""

            strSort &= strDelim & tbl新規.カラム位置Column.ColumnName & " ASC" : strDelim = ","

            tbl新規 = ada新規.SelectByCommon(qry新規, strSort)

            '----------------------------------------------------------------------
            ' 読み込み(既存読込時)
            '----------------------------------------------------------------------
            qry既存.Clear()
            qry既存.Add(New BaseDatabase.Condition(tbl既存.レイアウトCDColumn.ColumnName, Me.Fields.Header.レイアウトCD.Value, BaseDatabase.Contents.Compare.Equal))
            qry既存.Add(New BaseDatabase.Condition(tbl既存.荷主CD内部Column.ColumnName, Me.Fields.Header.荷主CD内部.Value, BaseDatabase.Contents.Compare.Equal))

            strSort = ""
            strDelim = ""

            strSort &= strDelim & tbl既存.カラム位置Column.ColumnName & " ASC" : strDelim = ","

            tbl既存 = ada既存.SelectByCommon(qry既存, strSort)

            '----------------------------------------------------------------------
            ' ﾌﾚｰﾑ作成
            '----------------------------------------------------------------------
            Me.FrameMaker_Detail1(tbl新規, tbl既存)

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "ﾌﾚｰﾑ関連"
#Region "ﾌﾚｰﾑ作成"
        '''========================================================================================
        ''' <SUMMARY>ﾌﾚｰﾑ作成</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Function FrameMaker_Detail1(ByVal tbl新規 As NodeDatabase.DataSetWork.W先頭行DataTable, ByVal tbl既存 As NodeDatabase.DataSetView.V0レイアウト今回DataTable) As Boolean
            '----------------------------------------------------------------------
            ' ﾌﾚｰﾑ生成 & 値設定
            '----------------------------------------------------------------------
            If tbl新規.Count = 0 And tbl既存.Count > 0 Then
                '--------------------------------------------------------------
                '既存読込時
                '--------------------------------------------------------------
                '---------------------------------------
                ' ﾌﾚｰﾑ生成
                '---------------------------------------
                Me.Add_Detail1(tbl既存.Count)

                '---------------------------------------
                ' 値設定
                '---------------------------------------
                For i As Integer = 0 To tbl既存.Rows.Count - 1
                    Me.Fields.Detail1(i).行数.Value = i + 1
                    Me.Fields.Detail1(i).伝票項目CD.Value = ""
                    Me.Fields.Detail1(i).伝票項目名.Value = ""
                    Me.Fields.Detail1(i).伝票項目名表示.Value = ""
                    Me.Fields.Detail1(i).カラム位置.Value = tbl既存(i).Item(tbl既存.カラム位置Column.ColumnName)

                    If tbl既存(i).Item(tbl既存.タイトルColumn.ColumnName).ToString.Length > 20 Then
                        Me.Fields.Detail1(i).タイトル.Value = tbl既存(i).Item(tbl既存.タイトルColumn.ColumnName).ToString.Substring(0, 20)
                    Else
                        Me.Fields.Detail1(i).タイトル.Value = tbl既存(i).Item(tbl既存.タイトルColumn.ColumnName)
                    End If

                    If tbl既存(i).Item(tbl既存.項目値Column.ColumnName).ToString.Length > 20 Then
                        Me.Fields.Detail1(i).項目値.Value = tbl既存(i).Item(tbl既存.項目値Column.ColumnName).ToString.Substring(0, 20)
                    Else
                        Me.Fields.Detail1(i).項目値.Value = tbl既存(i).Item(tbl既存.項目値Column.ColumnName)
                    End If

                    Me.Fields.Detail1(i).キーフラグ.Value = ""
                    Me.Fields.Detail1(i).取込優先順.Value = 0
                Next
            Else
                '--------------------------------------------------------------
                '新規ｱｯﾌﾟﾛｰﾄﾞ時
                '--------------------------------------------------------------
                '---------------------------------------
                ' ﾌﾚｰﾑ生成
                '---------------------------------------
                Me.Add_Detail1(tbl新規.Count)

                '---------------------------------------
                ' 値設定
                '---------------------------------------
                For i As Integer = 0 To tbl新規.Rows.Count - 1
                    Me.Fields.Detail1(i).行数.Value = i + 1
                    Me.Fields.Detail1(i).伝票項目CD.Value = ""
                    Me.Fields.Detail1(i).伝票項目名.Value = ""
                    Me.Fields.Detail1(i).伝票項目名表示.Value = ""
                    Me.Fields.Detail1(i).カラム位置.Value = tbl新規(i).Item(tbl新規.カラム位置Column.ColumnName)

                    If tbl新規(i).Item(tbl新規.タイトルColumn.ColumnName).ToString.Length > 20 Then
                        Me.Fields.Detail1(i).タイトル.Value = tbl新規(i).Item(tbl新規.タイトルColumn.ColumnName).ToString.Substring(0, 20)
                    Else
                        Me.Fields.Detail1(i).タイトル.Value = tbl新規(i).Item(tbl新規.タイトルColumn.ColumnName)
                    End If

                    If tbl新規(i).Item(tbl新規.項目値Column.ColumnName).ToString.Length > 20 Then
                        Me.Fields.Detail1(i).項目値.Value = tbl新規(i).Item(tbl新規.項目値Column.ColumnName).ToString.Substring(0, 20)
                    Else
                        Me.Fields.Detail1(i).項目値.Value = tbl新規(i).Item(tbl新規.項目値Column.ColumnName)
                    End If

                    Me.Fields.Detail1(i).キーフラグ.Value = ""
                    Me.Fields.Detail1(i).取込優先順.Value = 0
                Next
            End If

            '既存ﾚｲｱｳﾄ呼出時 or 新規ｱｯﾌﾟﾛｰﾄﾞで既存ﾚｲｱｳﾄ上書時(上書前のﾚｲｱｳﾄを保持)
            If (tbl新規.Count = 0 And tbl既存.Count > 0) Or (tbl新規.Count > 0 And tbl既存.Count > 0) Then
                '----------------------------------------------------------------------
                ' 既存ﾚｲｱｳﾄの設定
                '----------------------------------------------------------------------
                Dim adaレイ As New NodeDatabase.DataSetViewTableAdapters.V0レイアウトTableAdapter
                Dim tblレイ As New NodeDatabase.DataSetView.V0レイアウトDataTable
                Dim qryレイ As New Collection

                qryレイ.Clear()
                qryレイ.Add(New BaseDatabase.Condition(tblレイ.レイアウトCDColumn.ColumnName, Me.Fields.Header.レイアウトCD.Value, BaseDatabase.Contents.Compare.Equal))
                qryレイ.Add(New BaseDatabase.Condition(tblレイ.荷主CD内部Column.ColumnName, Me.Fields.Header.荷主CD内部.Value, BaseDatabase.Contents.Compare.Equal))

                Dim strSort As String = ""
                Dim strDelim As String = ""

                strSort &= strDelim & tblレイ.カラム位置Column.ColumnName & " ASC" : strDelim = ","

                tblレイ = adaレイ.SelectByCommon(qryレイ, strSort)

                For Each rowレイ As NodeDatabase.DataSetView.V0レイアウトRow In tblレイ.Rows
                    For i As Integer = 0 To Me.Fields.Detail1.Count - 1

                        Dim strDelimin As String = ""
                        If rowレイ.カラム位置 = Me.Fields.Detail1(i).カラム位置.Value Then

                            If Me.Fields.Detail1(i).伝票項目CD.Value.ToString <> "" Then : strDelimin = "," : End If
                            Me.Fields.Detail1(i).伝票項目CD.Value &= strDelimin & rowレイ.伝票項目CD
                            Me.Fields.Detail1(i).キーフラグ.Value &= strDelimin & rowレイ.キーフラグ
                            Me.Fields.Detail1(i).伝票項目名.Value &= strDelimin & rowレイ.伝票項目名
                            Me.Fields.Detail1(i).伝票項目名表示.Value &= strDelimin & rowレイ.伝票項目名表示
                            Me.Fields.Detail1(i).取込優先順.Value = rowレイ.取込優先順
                        End If
                    Next
                Next
            End If

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "行挿入"
        '''========================================================================================
        ''' <summary>単一行挿入</summary>
        '''========================================================================================
        Public Overrides Sub Insert_Detail1(ByVal Position As Integer)
            '----------------------------------------------------------------------
            ' 行挿入
            '----------------------------------------------------------------------
            Me.Fields.Detail1.Insert(Position, New EBindLayout.Detail1(Me.Config))

            '----------------------------------------------------------------------
            ' 行ｸﾘｱ
            '----------------------------------------------------------------------
            Me.Clear_Detail1(Position)

            '----------------------------------------------------------------------
            ' 行番号の再付番
            '----------------------------------------------------------------------
            Me.ReNum_Detail1()
        End Sub

        '''========================================================================================
        ''' <summary>複数行挿入</summary>
        '''========================================================================================
        Public Overrides Sub Insert_Detail1(ByVal Position As Integer, ByVal Count As Integer)
            '----------------------------------------------------------------------
            ' 行挿入 & 行ｸﾘｱ
            '----------------------------------------------------------------------
            For i As Integer = Position To Position + Count - 1
                Me.Fields.Detail1.Insert(Position, New EBindLayout.Detail1(Me.Config))
                Me.Clear_Detail1(Position)
            Next

            '----------------------------------------------------------------------
            ' 行番号の再付番
            '----------------------------------------------------------------------
            Me.ReNum_Detail1()
        End Sub
#End Region

#Region "行複写"
        '''========================================================================================
        ''' <summary>単一行複写</summary>
        '''========================================================================================
        Public Overrides Sub Copy_Detail1(ByVal Position As Integer)
            '----------------------------------------------------------------------
            ' 行挿入
            '----------------------------------------------------------------------
            Me.Fields.Detail1.Insert(Position, New EBindLayout.Detail1(Me.Config))

            '----------------------------------------------------------------------
            ' 行ｺﾋﾟｰ
            '----------------------------------------------------------------------
            Me.CopyValue_Detail1(Position + 1, Position)

            '----------------------------------------------------------------------
            ' 行番号の再付番
            '----------------------------------------------------------------------
            Me.ReNum_Detail1()
        End Sub

        '''========================================================================================
        ''' <summary>値ｺﾋﾟｰ</summary>
        '''========================================================================================
        Protected Function CopyValue_Detail1(ByVal IndexF As Integer, ByVal IndexT As Integer) As Boolean
        End Function
#End Region

#Region "行追加"
        '''========================================================================================
        ''' <summary>単一行追加</summary>
        '''========================================================================================
        Public Overrides Sub Add_Detail1()
            Me.Fields.Detail1.Add(New EBindLayout.Detail1(Me.Config))
            Me.Clear_Detail1(Me.Fields.Detail1.Count - 1)
        End Sub

        '''========================================================================================
        ''' <summary>一括行追加</summary>
        ''' <param name="AddCount">追加行数</param>
        '''========================================================================================
        Public Overrides Sub Add_Detail1(ByVal AddCount As Integer)
            Me.Fields.Detail1.Clear()

            For i As Integer = 0 To AddCount - 1
                Me.Add_Detail1()
            Next
        End Sub
#End Region

#Region "行削除"
        '''========================================================================================
        ''' <summary>一括行削除</summary>
        '''========================================================================================
        Public Overrides Sub Del_Detail1()
            Me.Fields.Detail1.Clear()
        End Sub

        '''========================================================================================
        ''' <summary>単一行削除</summary>
        '''========================================================================================
        Public Overrides Sub Del_Detail1(ByVal Position As Integer)
            '----------------------------------------------------------------------
            ' 行削除
            '----------------------------------------------------------------------
            Me.Fields.Detail1.Remove(Me.Fields.Detail1(Position))

            '----------------------------------------------------------------------
            ' 行番号の再付番
            '----------------------------------------------------------------------
            Me.ReNum_Detail1()
        End Sub
#End Region

#Region "行選択"
        '''========================================================================================
        ''' <summary>行選択</summary>
        '''========================================================================================
        Public Sub SelectedLine_Detail1(ByVal Position As Integer)
        End Sub
#End Region

#Region "行番号の再付番"
        '''========================================================================================
        ''' <summary>行番号の再付番</summary>
        '''========================================================================================
        Public Overrides Sub ReNum_Detail1()
            For i As Integer = 0 To Me.Fields.Detail1.Count - 1
                Me.Fields.Detail1(i).行数.Value = i + 1
            Next
        End Sub
#End Region
#End Region

#Region "無効行判定"
        '''========================================================================================
        ''' <summary>無効行の判定</summary>
        ''' <param name="Detail">明細行</param>
        ''' <returns>True:無効行, False:有効行</returns>
        '''========================================================================================
        Protected Overrides Function Empty_Detail1(ByVal Detail As Object) As Boolean
            '------------------------------------------------------------------
            ' 変数定義
            '------------------------------------------------------------------
            Dim row As EBindLayout.Detail1 = CType(Detail, EBindLayout.Detail1)

            '------------------------------------------------------------------
            ' 入力有は有効行
            '------------------------------------------------------------------
            Select Case True
                Case Else
            End Select

            '------------------------------------------------------------------
            ' 無効行
            '------------------------------------------------------------------
            Return True
        End Function
#End Region
#End Region

#Region "明細2"
#Region "Clear"
        '''========================================================================================
        ''' <SUMMARY>ｸﾘｱ</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Clear_Detail2() As Boolean
            '----------------------------------------------------------------------
            ' ｸﾘｱ
            '----------------------------------------------------------------------
            For i As Integer = 0 To Me.Fields.Detail2.Count - 1
                Me.Clear_Detail2(i)
            Next

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function

        Public Overrides Function Clear_Detail2(ByVal Index As Integer) As Boolean
            Me.Fields.Detail2(Index).行数.Value = Index + 1
            Me.Fields.Detail2(Index).伝票項目CD.Value = Nothing
            Me.Fields.Detail2(Index).伝票項目名.Value = Nothing
            Me.Fields.Detail2(Index).伝票項目名表示.Value = Nothing
            Me.Fields.Detail2(Index).表示位置.Value = Nothing
            Me.Fields.Detail2(Index).キーフラグ.Value = Nothing
            Me.Fields.Detail2(Index).必須フラグ.Value = Nothing
            Me.Fields.Detail2(Index).既定値.Value = Nothing
            Me.Fields.Detail2(Index).紐付済みフラグ.Value = Nothing
        End Function
#End Region

#Region "Read"
        '''========================================================================================
        ''' <SUMMARY>読込</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Read_Detail2() As Boolean
            '----------------------------------------------------------------------
            ' 読み込み
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetMasterTableAdapters.M伝票項目TableAdapter
            Dim tbl As New NodeDatabase.DataSetMaster.M伝票項目DataTable
            Dim qry As New Collection

            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl.使用フラグ倉庫Column.ColumnName, NodeContents.Constant.CodeValue.フラグID.オン, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.伝票タイプColumn.ColumnName, Me.Fields.Header.伝票タイプ.Value, BaseDatabase.Contents.Compare.Equal))

            Dim strSort As String = ""
            Dim strDelim As String = ""

            strSort &= strDelim & tbl.表示位置Column.ColumnName & " ASC" : strDelim = ","

            tbl = ada.SelectByCommon(qry, strSort)

            '----------------------------------------------------------------------
            ' ﾌﾚｰﾑ作成
            '----------------------------------------------------------------------
            Me.FrameMaker_Detail2(tbl)

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "ﾌﾚｰﾑ関連"
#Region "ﾌﾚｰﾑ作成"
        '''========================================================================================
        ''' <SUMMARY>ﾌﾚｰﾑ作成</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Function FrameMaker_Detail2(ByVal tbl As NodeDatabase.DataSetMaster.M伝票項目DataTable) As Boolean
            '----------------------------------------------------------------------
            ' ﾌﾚｰﾑｸﾘｱ
            '----------------------------------------------------------------------
            Me.Fields.Detail2.Clear()

            '----------------------------------------------------------------------
            ' 使用済みは除外
            '----------------------------------------------------------------------
            For i As Integer = 0 To tbl.Rows.Count - 1
                '----------------------------------------------------------------------
                ' ﾌﾚｰﾑ生成
                '----------------------------------------------------------------------
                Me.Add_Detail2()

                '----------------------------------------------------------------------
                ' 値設定
                '----------------------------------------------------------------------
                Me.Fields.Detail2(i).行数.Value = i + 1
                Me.Fields.Detail2(i).伝票項目CD.Value = tbl(i).Item(tbl.伝票項目CDColumn.ColumnName)
                Me.Fields.Detail2(i).伝票項目名.Value = tbl(i).Item(tbl.伝票項目名Column.ColumnName)
                Me.Fields.Detail2(i).伝票項目名表示.Value = tbl(i).Item(tbl.伝票項目名表示Column.ColumnName)
                Me.Fields.Detail2(i).表示位置.Value = tbl(i).Item(tbl.表示位置Column.ColumnName)
                Me.Fields.Detail2(i).キーフラグ.Value = tbl(i).Item(tbl.キーフラグColumn.ColumnName)
                Me.Fields.Detail2(i).必須フラグ.Value = tbl(i).Item(tbl.必須フラグColumn.ColumnName)
                Me.Fields.Detail2(i).既定値.Value = ""
                Me.Fields.Detail2(i).紐付済みフラグ.Value = NodeContents.Constant.CodeValue.フラグID.オフ
            Next

            '----------------------------------------------------------------------
            ' 既存ﾚｲｱｳﾄの設定(既定値)
            '----------------------------------------------------------------------
            Dim adaレイ As New NodeDatabase.DataSetViewTableAdapters.V0レイアウトTableAdapter
            Dim tblレイ As New NodeDatabase.DataSetView.V0レイアウトDataTable
            Dim qryレイ As New Collection

            qryレイ.Clear()
            qryレイ.Add(New BaseDatabase.Condition(tblレイ.レイアウトCDColumn.ColumnName, Me.Fields.Header.レイアウトCD.Value, BaseDatabase.Contents.Compare.Equal))
            qryレイ.Add(New BaseDatabase.Condition(tblレイ.荷主CD内部Column.ColumnName, Me.Fields.Header.荷主CD内部.Value, BaseDatabase.Contents.Compare.Equal))
            tblレイ = adaレイ.SelectByCommon(qryレイ)

            For i As Integer = 0 To Me.Fields.Detail2.Count - 1

                For Each row As NodeDatabase.DataSetView.V0レイアウトRow In tblレイ.Rows

                    If Me.Fields.Detail2(i).伝票項目CD.Value = row.伝票項目CD Then
                        Me.Fields.Detail2(i).既定値.Value = row.既定値
                    End If
                Next
            Next

            '----------------------------------------------------------------------
            ' 紐付済みﾌﾗｸﾞ 設定
            '----------------------------------------------------------------------
            Call ClearLine_Detail2()

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "行挿入"
        '''========================================================================================
        ''' <summary>単一行挿入</summary>
        '''========================================================================================
        Public Overrides Sub Insert_Detail2(ByVal Position As Integer)
            '----------------------------------------------------------------------
            ' 行挿入
            '----------------------------------------------------------------------
            Me.Fields.Detail2.Insert(Position, New EBindLayout.Detail2(Me.Config))

            '----------------------------------------------------------------------
            ' 行ｸﾘｱ
            '----------------------------------------------------------------------
            Me.Clear_Detail2(Position)

            '----------------------------------------------------------------------
            ' 行番号の再付番
            '----------------------------------------------------------------------
            Me.ReNum_Detail2()
        End Sub

        '''========================================================================================
        ''' <summary>複数行挿入</summary>
        '''========================================================================================
        Public Overrides Sub Insert_Detail2(ByVal Position As Integer, ByVal Count As Integer)
            '----------------------------------------------------------------------
            ' 行挿入 & 行ｸﾘｱ
            '----------------------------------------------------------------------
            For i As Integer = Position To Position + Count - 1
                Me.Fields.Detail2.Insert(Position, New EBindLayout.Detail2(Me.Config))
                Me.Clear_Detail2(Position)
            Next

            '----------------------------------------------------------------------
            ' 行番号の再付番
            '----------------------------------------------------------------------
            Me.ReNum_Detail2()
        End Sub
#End Region

#Region "行複写"
        '''========================================================================================
        ''' <summary>単一行複写</summary>
        '''========================================================================================
        Public Overrides Sub Copy_Detail2(ByVal Position As Integer)
            '----------------------------------------------------------------------
            ' 行挿入
            '----------------------------------------------------------------------
            Me.Fields.Detail2.Insert(Position, New EBindLayout.Detail2(Me.Config))

            '----------------------------------------------------------------------
            ' 行ｺﾋﾟｰ
            '----------------------------------------------------------------------
            Me.CopyValue_Detail2(Position + 1, Position)

            '----------------------------------------------------------------------
            ' 行番号の再付番
            '----------------------------------------------------------------------
            Me.ReNum_Detail2()
        End Sub

        '''========================================================================================
        ''' <summary>値ｺﾋﾟｰ</summary>
        '''========================================================================================
        Protected Function CopyValue_Detail2(ByVal IndexF As Integer, ByVal IndexT As Integer) As Boolean
            Me.Fields.Detail2(IndexT).行数.Value = Me.Fields.Detail2(IndexF).行数.Value
            Me.Fields.Detail2(IndexT).伝票項目CD.Value = Me.Fields.Detail2(IndexF).伝票項目CD.Value
            Me.Fields.Detail2(IndexT).伝票項目名.Value = Me.Fields.Detail2(IndexF).伝票項目名.Value
            Me.Fields.Detail2(IndexT).伝票項目名表示.Value = Me.Fields.Detail2(IndexF).伝票項目名表示.Value
            Me.Fields.Detail2(IndexT).表示位置.Value = Me.Fields.Detail2(IndexF).表示位置.Value
            Me.Fields.Detail2(IndexT).キーフラグ.Value = Me.Fields.Detail2(IndexF).キーフラグ.Value
            Me.Fields.Detail2(IndexT).必須フラグ.Value = Me.Fields.Detail2(IndexF).必須フラグ.Value
            Me.Fields.Detail2(IndexT).既定値.Value = Me.Fields.Detail2(IndexF).既定値.Value
            Me.Fields.Detail2(IndexT).紐付済みフラグ.Value = Me.Fields.Detail2(IndexF).紐付済みフラグ.Value
        End Function
#End Region

#Region "行追加"
        '''========================================================================================
        ''' <summary>単一行追加</summary>
        '''========================================================================================
        Public Overrides Sub Add_Detail2()
            Me.Fields.Detail2.Add(New EBindLayout.Detail2(Me.Config))
            Me.Clear_Detail2(Me.Fields.Detail2.Count - 1)
        End Sub

        '''========================================================================================
        ''' <summary>一括行追加</summary>
        ''' <param name="AddCount">追加行数</param>
        '''========================================================================================
        Public Overrides Sub Add_Detail2(ByVal AddCount As Integer)
            Me.Fields.Detail2.Clear()

            For i As Integer = 0 To AddCount - 1
                Me.Add_Detail2()
            Next
        End Sub
#End Region

#Region "行削除"
        '''========================================================================================
        ''' <summary>一括行削除</summary>
        '''========================================================================================
        Public Overrides Sub Del_Detail2()
            Me.Fields.Detail2.Clear()
        End Sub

        '''========================================================================================
        ''' <summary>単一行削除</summary>
        '''========================================================================================
        Public Overrides Sub Del_Detail2(ByVal Position As Integer)
            '----------------------------------------------------------------------
            ' 行削除
            '----------------------------------------------------------------------
            Me.Fields.Detail2.Remove(Me.Fields.Detail2(Position))

            '----------------------------------------------------------------------
            ' 行番号の再付番
            '----------------------------------------------------------------------
            Me.ReNum_Detail2()
        End Sub
#End Region

#Region "行選択"
        '''========================================================================================
        ''' <summary>行選択</summary>
        '''========================================================================================
        Public Sub SelectedLine_Detail2(ByVal Position As Integer)
        End Sub
#End Region

#Region "行番号の再付番"
        '''========================================================================================
        ''' <summary>行番号の再付番</summary>
        '''========================================================================================
        Public Overrides Sub ReNum_Detail2()
            For i As Integer = 0 To Me.Fields.Detail2.Count - 1
                Me.Fields.Detail2(i).行数.Value = i + 1
            Next
        End Sub
#End Region

#Region "行確認"
        '''========================================================================================
        ''' <summary>行確認</summary>
        '''========================================================================================
        Public Sub ClearLine_Detail2()
            For i As Integer = 0 To Me.Fields.Detail2.Count - 1

                Me.Fields.Detail2(i).紐付済みフラグ.Value = NodeContents.Constant.CodeValue.フラグID.オフ
                For j As Integer = 0 To Me.Fields.Detail1.Count - 1

                    Dim str伝票項目CD() As String = Me.Fields.Detail1(j).伝票項目CD.Value.ToString.Split(",")
                    For c As Integer = 0 To str伝票項目CD.Length - 1
                        If str伝票項目CD(c) = Me.Fields.Detail2(i).伝票項目CD.Value Then
                            Me.Fields.Detail2(i).紐付済みフラグ.Value = NodeContents.Constant.CodeValue.フラグID.オン
                        End If
                    Next
                Next
            Next
        End Sub
#End Region
#End Region

#Region "無効行判定"
        '''========================================================================================
        ''' <summary>無効行の判定</summary>
        ''' <param name="Detail">明細行</param>
        ''' <returns>True:無効行, False:有効行</returns>
        '''========================================================================================
        Protected Overrides Function Empty_Detail2(ByVal Detail As Object) As Boolean
            '------------------------------------------------------------------
            ' 変数定義
            '------------------------------------------------------------------
            Dim row As EBindLayout.Detail2 = CType(Detail, EBindLayout.Detail2)

            '------------------------------------------------------------------
            ' 入力有は有効行
            '------------------------------------------------------------------
            Select Case True
                Case Else
            End Select

            '------------------------------------------------------------------
            ' 無効行
            '------------------------------------------------------------------
            Return True
        End Function
#End Region
#End Region

#Region "明細3"
#Region "Clear"
        '''========================================================================================
        ''' <SUMMARY>ｸﾘｱ</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Clear_Detail3() As Boolean
            '----------------------------------------------------------------------
            ' ｸﾘｱ
            '----------------------------------------------------------------------
            For i As Integer = 0 To Me.Fields.Detail3.Count - 1
                Me.Clear_Detail3(i)
            Next

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function

        Public Overrides Function Clear_Detail3(ByVal Index As Integer) As Boolean
            Me.Fields.Detail3(Index).行数.Value = Index + 1
            Me.Fields.Detail3(Index).削除.Value = Nothing
            Me.Fields.Detail3(Index).レイアウトCD.Value = Nothing
            Me.Fields.Detail3(Index).条件カラム位置.Value = Nothing
            Me.Fields.Detail3(Index).条件項目値.Value = Nothing
            Me.Fields.Detail3(Index).符号区分.Value = Nothing
            Me.Fields.Detail3(Index).結果伝票項目CD.Value = Nothing
            Me.Fields.Detail3(Index).結果項目値.Value = Nothing
            Me.Fields.Detail3(Index).符号区分名.Value = Nothing
        End Function
#End Region

#Region "Read"
        '''========================================================================================
        ''' <SUMMARY>読込</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Read_Detail3() As Boolean
            '----------------------------------------------------------------------
            ' 読み込み
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetViewTableAdapters.V0レイアウト条件TableAdapter
            Dim tbl As New NodeDatabase.DataSetView.V0レイアウト条件DataTable
            Dim qry As New Collection

            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl.レイアウトCDColumn.ColumnName, Me.Fields.Header.レイアウトCD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.荷主CD内部Column.ColumnName, Me.Fields.Header.荷主CD内部.Value, BaseDatabase.Contents.Compare.Equal))

            Dim strSort As String = ""
            Dim strDelim As String = ""

            strSort &= strDelim & tbl.条件カラム位置Column.ColumnName & " ASC" : strDelim = ","

            tbl = ada.SelectByCommon(qry, strSort)

            '----------------------------------------------------------------------
            ' ﾌﾚｰﾑ作成
            '----------------------------------------------------------------------
            Me.FrameMaker_Detail3(tbl)

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "Valid_Write"
        '''========================================================================================
        ''' <summary>検査</summary>
        ''' <RETURNS>True:正常, False:不正</RETURNS>
        '''========================================================================================
        Protected Overrides Function Valid_Write_Detail3_Common() As NodeContents.ActionButton.ErrorLevel
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim strMsg As String = ""
            Dim Validator As New BaseCore.Common.Validator

            '------------------------------------------------------------------
            ' ｴﾗｰｸﾘｱ
            '------------------------------------------------------------------
            For i As Integer = 0 To Me.Fields.Detail3.Count - 1
                Me.Fields.Detail3(i).削除.IsError = False
                Me.Fields.Detail3(i).行数.IsError = False
                Me.Fields.Detail3(i).レイアウトCD.IsError = False
                Me.Fields.Detail3(i).条件カラム位置.IsError = False
                Me.Fields.Detail3(i).条件項目値.IsError = False
                Me.Fields.Detail3(i).符号区分.IsError = False
                Me.Fields.Detail3(i).結果伝票項目CD.IsError = False
                Me.Fields.Detail3(i).結果項目値.IsError = False
                Me.Fields.Detail3(i).符号区分名.IsError = False
            Next

            '------------------------------------------------------------------
            ' 共通検査
            '------------------------------------------------------------------
            For i As Integer = 0 To Me.Fields.Detail3.Count - 1
                If Not Me.Empty_Detail3(Me.Fields.Detail3(i)) Then
                    If Not Validator.BaseChecker(Me.Fields.Detail3(i).削除) Then : Me.Fields.Detail3(i).削除.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                    If Not Validator.BaseChecker(Me.Fields.Detail3(i).行数) Then : Me.Fields.Detail3(i).行数.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                    If Not Validator.BaseChecker(Me.Fields.Detail3(i).レイアウトCD) Then : Me.Fields.Detail3(i).レイアウトCD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                    If Not Validator.BaseChecker(Me.Fields.Detail3(i).条件カラム位置) Then : Me.Fields.Detail3(i).条件カラム位置.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                    If Not Validator.BaseChecker(Me.Fields.Detail3(i).条件項目値) Then : Me.Fields.Detail3(i).条件項目値.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                    If Not Validator.BaseChecker(Me.Fields.Detail3(i).符号区分) Then : Me.Fields.Detail3(i).符号区分.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                    If Not Validator.BaseChecker(Me.Fields.Detail3(i).結果伝票項目CD) Then : Me.Fields.Detail3(i).結果伝票項目CD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                    If Not Validator.BaseChecker(Me.Fields.Detail3(i).結果項目値) Then : Me.Fields.Detail3(i).結果項目値.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                    If Not Validator.BaseChecker(Me.Fields.Detail3(i).符号区分名) Then : Me.Fields.Detail3(i).符号区分名.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                End If
            Next

            '------------------------------------------------------------------
            ' 返却
            '------------------------------------------------------------------
            MyBase.LastError = strMsg
            Select Case True
                Case strMsg = ""
                    Return NodeContents.ActionButton.ErrorLevel.None
                Case Else
                    Return NodeContents.ActionButton.ErrorLevel.Fatal
            End Select
        End Function

        '''========================================================================================
        ''' <SUMMARY>検査(個別)</SUMMARY>
        ''' <RETURNS>True:正常, False:不正</RETURNS>
        '''========================================================================================
        Protected Overrides Function Valid_Write_Detail3_Another() As NodeContents.ActionButton.ErrorLevel
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim strMsg As String = ""
            Dim Validator As New BaseCore.Common.Validator

            '------------------------------------------------------------------
            ' 返却
            '------------------------------------------------------------------
            MyBase.LastError = strMsg
            Select Case True
                Case strMsg = ""
                    Return NodeContents.ActionButton.ErrorLevel.None
                Case Else
                    Return NodeContents.ActionButton.ErrorLevel.Fatal
            End Select
        End Function
#End Region

#Region "ﾌﾚｰﾑ関連"
#Region "ﾌﾚｰﾑ作成"
        '''========================================================================================
        ''' <SUMMARY>ﾌﾚｰﾑ作成</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Function FrameMaker_Detail3(ByVal tbl As NodeDatabase.DataSetView.V0レイアウト条件DataTable) As Boolean
            '----------------------------------------------------------------------
            ' ﾌﾚｰﾑｸﾘｱ
            '----------------------------------------------------------------------
            Me.Fields.Detail3.Clear()

            '----------------------------------------------------------------------
            ' ﾌﾚｰﾑ生成
            '----------------------------------------------------------------------
            For i As Integer = 0 To tbl.Rows.Count - 1
                Me.Add_Detail3()

                '----------------------------------------------------------------------
                ' 値設定
                '----------------------------------------------------------------------
                Me.Fields.Detail3(i).行数.Value = i + 1
                Me.Fields.Detail3(i).削除.Value = 0
                Me.Fields.Detail3(i).レイアウトCD.Value = tbl(i).Item(tbl.レイアウトCDColumn.ColumnName)
                Me.Fields.Detail3(i).条件カラム位置.Value = tbl(i).Item(tbl.条件カラム位置Column.ColumnName)
                Me.Fields.Detail3(i).条件項目値.Value = tbl(i).Item(tbl.条件項目値Column.ColumnName)
                Me.Fields.Detail3(i).符号区分.Value = tbl(i).Item(tbl.符号区分Column.ColumnName)
                Me.Fields.Detail3(i).結果伝票項目CD.Value = tbl(i).Item(tbl.結果伝票項目CDColumn.ColumnName)
                Me.Fields.Detail3(i).結果項目値.Value = tbl(i).Item(tbl.結果項目値Column.ColumnName)
                Me.Fields.Detail3(i).符号区分名.Value = tbl(i).Item(tbl.符号区分名Column.ColumnName)
            Next

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "行挿入"
        '''========================================================================================
        ''' <summary>単一行挿入</summary>
        '''========================================================================================
        Public Overrides Sub Insert_Detail3(ByVal Position As Integer)
            '----------------------------------------------------------------------
            ' 行挿入
            '----------------------------------------------------------------------
            Me.Fields.Detail3.Insert(Position, New EBindLayout.Detail3(Me.Config))

            '----------------------------------------------------------------------
            ' 行ｸﾘｱ
            '----------------------------------------------------------------------
            Me.Clear_Detail3(Position)

            '----------------------------------------------------------------------
            ' 行番号の再付番
            '----------------------------------------------------------------------
            Me.ReNum_Detail3()
        End Sub

        '''========================================================================================
        ''' <summary>複数行挿入</summary>
        '''========================================================================================
        Public Overrides Sub Insert_Detail3(ByVal Position As Integer, ByVal Count As Integer)
            '----------------------------------------------------------------------
            ' 行挿入 & 行ｸﾘｱ
            '----------------------------------------------------------------------
            For i As Integer = Position To Position + Count - 1
                Me.Fields.Detail3.Insert(Position, New EBindLayout.Detail3(Me.Config))
                Me.Clear_Detail3(Position)
            Next

            '----------------------------------------------------------------------
            ' 行番号の再付番
            '----------------------------------------------------------------------
            Me.ReNum_Detail3()
        End Sub
#End Region

#Region "行複写"
        '''========================================================================================
        ''' <summary>単一行複写</summary>
        '''========================================================================================
        Public Overrides Sub Copy_Detail3(ByVal Position As Integer)
            '----------------------------------------------------------------------
            ' 行挿入
            '----------------------------------------------------------------------
            Me.Fields.Detail3.Insert(Position, New EBindLayout.Detail3(Me.Config))

            '----------------------------------------------------------------------
            ' 行ｺﾋﾟｰ
            '----------------------------------------------------------------------
            Me.CopyValue_Detail3(Position + 1, Position)

            '----------------------------------------------------------------------
            ' 行番号の再付番
            '----------------------------------------------------------------------
            Me.ReNum_Detail3()
        End Sub

        '''========================================================================================
        ''' <summary>値ｺﾋﾟｰ</summary>
        '''========================================================================================
        Protected Function CopyValue_Detail3(ByVal IndexF As Integer, ByVal IndexT As Integer) As Boolean
            Me.Fields.Detail3(IndexT).行数.Value = Me.Fields.Detail3(IndexF).行数.Value
            Me.Fields.Detail3(IndexT).削除.Value = Me.Fields.Detail3(IndexF).削除.Value
            Me.Fields.Detail3(IndexT).レイアウトCD.Value = Me.Fields.Detail3(IndexF).レイアウトCD.Value
            Me.Fields.Detail3(IndexT).条件カラム位置.Value = Me.Fields.Detail3(IndexF).条件カラム位置.Value
            Me.Fields.Detail3(IndexT).条件項目値.Value = Me.Fields.Detail3(IndexF).条件項目値.Value
            Me.Fields.Detail3(IndexT).符号区分.Value = Me.Fields.Detail3(IndexF).符号区分.Value
            Me.Fields.Detail3(IndexT).結果伝票項目CD.Value = Me.Fields.Detail3(IndexF).結果伝票項目CD.Value
            Me.Fields.Detail3(IndexT).結果項目値.Value = Me.Fields.Detail3(IndexF).結果項目値.Value
            Me.Fields.Detail3(IndexT).符号区分名.Value = Me.Fields.Detail3(IndexF).符号区分名.Value
        End Function
#End Region

#Region "行追加"
        '''========================================================================================
        ''' <summary>単一行追加</summary>
        '''========================================================================================
        Public Overrides Sub Add_Detail3()
            Me.Fields.Detail3.Add(New EBindLayout.Detail3(Me.Config))
            Me.Clear_Detail3(Me.Fields.Detail3.Count - 1)
        End Sub

        '''========================================================================================
        ''' <summary>一括行追加</summary>
        ''' <param name="AddCount">追加行数</param>
        '''========================================================================================
        Public Overrides Sub Add_Detail3(ByVal AddCount As Integer)
            Me.Fields.Detail3.Clear()

            For i As Integer = 0 To AddCount - 1
                Me.Add_Detail3()
            Next
        End Sub
#End Region

#Region "行削除"
        '''========================================================================================
        ''' <summary>一括行削除</summary>
        '''========================================================================================
        Public Overrides Sub Del_Detail3()
            Me.Fields.Detail3.Clear()
        End Sub

        '''========================================================================================
        ''' <summary>単一行削除</summary>
        '''========================================================================================
        Public Overrides Sub Del_Detail3(ByVal Position As Integer)
            '----------------------------------------------------------------------
            ' 行削除
            '----------------------------------------------------------------------
            Me.Fields.Detail3.Remove(Me.Fields.Detail3(Position))

            '----------------------------------------------------------------------
            ' 行番号の再付番
            '----------------------------------------------------------------------
            Me.ReNum_Detail3()
        End Sub
#End Region

#Region "行選択"
        '''========================================================================================
        ''' <summary>行選択</summary>
        '''========================================================================================
        Public Sub SelectedLine_Detail3(ByVal Position As Integer)
        End Sub
#End Region

#Region "行番号の再付番"
        '''========================================================================================
        ''' <summary>行番号の再付番</summary>
        '''========================================================================================
        Public Overrides Sub ReNum_Detail3()
            For i As Integer = 0 To Me.Fields.Detail3.Count - 1
                Me.Fields.Detail3(i).行数.Value = i + 1
            Next
        End Sub
#End Region
#End Region

#Region "無効行判定"
        '''========================================================================================
        ''' <summary>無効行の判定</summary>
        ''' <param name="Detail">明細行</param>
        ''' <returns>True:無効行, False:有効行</returns>
        '''========================================================================================
        Protected Overrides Function Empty_Detail3(ByVal Detail As Object) As Boolean
            '------------------------------------------------------------------
            ' 変数定義
            '------------------------------------------------------------------
            Dim row As EBindLayout.Detail3 = CType(Detail, EBindLayout.Detail3)

            '------------------------------------------------------------------
            ' 入力有は有効行
            '------------------------------------------------------------------
            Select Case True
                Case row.削除.Value = 0 : Return False
            End Select

            '------------------------------------------------------------------
            ' 無効行
            '------------------------------------------------------------------
            Return True
        End Function
#End Region
#End Region
    End Class
End Namespace
