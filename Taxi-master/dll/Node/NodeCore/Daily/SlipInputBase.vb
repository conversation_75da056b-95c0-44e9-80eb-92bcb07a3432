﻿Namespace Frame.Daily
    Partial Public Class SlipInputBase
        Inherits NodeCore.Common.Frame

#Region "ﾌｨｰﾙﾄﾞ定義"
#Region "ﾍｯﾀﾞ"
        Public Class Header
            Public 伝票タイプ As BaseCore.Common.Field.ItemData
            Public 伝票番号 As BaseCore.Common.Field.ItemData
            Public 入力日付 As BaseCore.Common.Field.ItemData
            Public 伝票日付 As BaseCore.Common.Field.ItemData
            Public 伝票区分 As BaseCore.Common.Field.ItemData
            Public 緊急区分 As BaseCore.Common.Field.ItemData
            Public 指示番号 As BaseCore.Common.Field.ItemData
            Public 指示区分 As BaseCore.Common.Field.ItemData
            Public 指示状況 As BaseCore.Common.Field.ItemData
            Public 指示者名 As BaseCore.Common.Field.ItemData
            Public 荷主CD内部 As BaseCore.Common.Field.ItemData
            Public 拠点CD As BaseCore.Common.Field.ItemData
            Public 企業CD As BaseCore.Common.Field.ItemData
            Public 届先CD As BaseCore.Common.Field.ItemData
            Public 届先名1 As BaseCore.Common.Field.ItemData
            Public 届先名2 As BaseCore.Common.Field.ItemData
            Public 届先郵便番号 As BaseCore.Common.Field.ItemData
            Public 届先住所1 As BaseCore.Common.Field.ItemData
            Public 届先住所2 As BaseCore.Common.Field.ItemData
            Public 届先TEL As BaseCore.Common.Field.ItemData
            Public 摘要CD1 As BaseCore.Common.Field.ItemData
            Public 摘要名1 As BaseCore.Common.Field.ItemData
            Public 摘要CD2 As BaseCore.Common.Field.ItemData
            Public 摘要名2 As BaseCore.Common.Field.ItemData
            Public 運送会社CD As BaseCore.Common.Field.ItemData
            Public 配達方法 As BaseCore.Common.Field.ItemData
            Public 配達日付 As BaseCore.Common.Field.ItemData
            Public 配達時刻区分 As BaseCore.Common.Field.ItemData
            Public 代引金額 As BaseCore.Common.Field.ItemData
            Public 送り状区分 As BaseCore.Common.Field.ItemData
            Public 送り状NO As BaseCore.Common.Field.ItemData
            Public 車番 As BaseCore.Common.Field.ItemData
            Public 便 As BaseCore.Common.Field.ItemData
            Public 運送扱数 As BaseCore.Common.Field.ItemData
            Public 運送単価 As BaseCore.Common.Field.ItemData
            Public 運送金額 As BaseCore.Common.Field.ItemData
            Public 注文ショップCD As BaseCore.Common.Field.ItemData
            Public 注文NO As BaseCore.Common.Field.ItemData
            Public 注文日付 As BaseCore.Common.Field.ItemData
            Public 注文名1 As BaseCore.Common.Field.ItemData
            Public 注文名2 As BaseCore.Common.Field.ItemData
            Public 注文会社名 As BaseCore.Common.Field.ItemData
            Public 注文部署名 As BaseCore.Common.Field.ItemData
            Public 注文郵便番号 As BaseCore.Common.Field.ItemData
            Public 注文住所1 As BaseCore.Common.Field.ItemData
            Public 注文住所2 As BaseCore.Common.Field.ItemData
            Public 注文TEL As BaseCore.Common.Field.ItemData
            Public 注文メールアドレス As BaseCore.Common.Field.ItemData
            Public 注文支払区分 As BaseCore.Common.Field.ItemData
            Public 注文入金状況 As BaseCore.Common.Field.ItemData
            Public 注文ギフト包装区分 As BaseCore.Common.Field.ItemData
            Public 注文ギフトメッセージ As BaseCore.Common.Field.ItemData
            Public 注文のし表書き区分 As BaseCore.Common.Field.ItemData
            Public 注文のし名入れ As BaseCore.Common.Field.ItemData
            Public 注文品代 As BaseCore.Common.Field.ItemData
            Public 注文軽減税 As BaseCore.Common.Field.ItemData
            Public 注文消費税 As BaseCore.Common.Field.ItemData
            Public 注文送料 As BaseCore.Common.Field.ItemData
            Public 注文手数料 As BaseCore.Common.Field.ItemData
            Public 注文ギフト包装代 As BaseCore.Common.Field.ItemData
            Public 注文値引額 As BaseCore.Common.Field.ItemData
            Public 注文利用ポイント As BaseCore.Common.Field.ItemData
            Public 注文請求金額 As BaseCore.Common.Field.ItemData
            Public 取込日時 As BaseCore.Common.Field.ItemData
            Public 登録日時 As BaseCore.Common.Field.ItemData
            Public 更新日時 As BaseCore.Common.Field.ItemData
            Public 登録者ID As BaseCore.Common.Field.ItemData
            Public 登録者名 As BaseCore.Common.Field.ItemData
            Public 更新者ID As BaseCore.Common.Field.ItemData
            Public 更新者名 As BaseCore.Common.Field.ItemData
            Public 承認者ID As BaseCore.Common.Field.ItemData
            Public 承認者名 As BaseCore.Common.Field.ItemData
            Public 公開日付 As BaseCore.Common.Field.ItemData
            Public 所属CD As BaseCore.Common.Field.ItemData
            Public 検品番号 As BaseCore.Common.Field.ItemData
            Public 営業店止め区分 As BaseCore.Common.Field.ItemData
            Public 営業店名 As BaseCore.Common.Field.ItemData

            Public 荷主CD As BaseCore.Common.Field.ItemData
            Public 仮番 As BaseCore.Common.Field.ItemData
            Public 位置 As BaseCore.Common.Field.ItemData

            '''========================================================================================
            ''' <SUMMARY>ｺﾝｽﾄﾗｸﾀ</SUMMARY>
            '''========================================================================================
            Public Sub New(ByVal Config As NodeCore.Common.Config)
                Dim tbl As New NodeDatabase.DataSetView.V0伝票HDDataTable
                'Dim tbl商品 As New NodeDatabase.DataSetMaster.M荷主商品DataTable

                Me.伝票タイプ = New BaseCore.Common.Field.ItemData(tbl.伝票タイプColumn)
                Me.伝票番号 = New BaseCore.Common.Field.ItemData(tbl.伝票番号Column)
                Me.入力日付 = New BaseCore.Common.Field.ItemData(tbl.入力日付Column)
                Me.伝票日付 = New BaseCore.Common.Field.ItemData(tbl.伝票日付Column)
                Me.伝票区分 = New BaseCore.Common.Field.ItemData(tbl.伝票区分Column)
                Me.緊急区分 = New BaseCore.Common.Field.ItemData(tbl.緊急区分Column)
                Me.指示番号 = New BaseCore.Common.Field.ItemData(tbl.指示番号Column)
                Me.指示区分 = New BaseCore.Common.Field.ItemData(tbl.指示区分Column)
                Me.指示状況 = New BaseCore.Common.Field.ItemData(tbl.指示状況Column)
                Me.指示者名 = New BaseCore.Common.Field.ItemData(tbl.指示者名Column)
                Me.荷主CD内部 = New BaseCore.Common.Field.ItemData(tbl.荷主CD内部Column)
                Me.拠点CD = New BaseCore.Common.Field.ItemData(tbl.拠点CDColumn)
                Me.企業CD = New BaseCore.Common.Field.ItemData(tbl.企業CDColumn)
                Me.届先CD = New BaseCore.Common.Field.ItemData(tbl.届先CDColumn)
                Me.届先名1 = New BaseCore.Common.Field.ItemData(tbl.届先名1Column)
                Me.届先名2 = New BaseCore.Common.Field.ItemData(tbl.届先名2Column)
                Me.届先郵便番号 = New BaseCore.Common.Field.ItemData(tbl.届先郵便番号Column)
                Me.届先住所1 = New BaseCore.Common.Field.ItemData(tbl.届先住所1Column)
                Me.届先住所2 = New BaseCore.Common.Field.ItemData(tbl.届先住所2Column)
                Me.届先TEL = New BaseCore.Common.Field.ItemData(tbl.届先TELColumn)
                Me.摘要CD1 = New BaseCore.Common.Field.ItemData(tbl.摘要CD1Column)
                Me.摘要名1 = New BaseCore.Common.Field.ItemData(tbl.摘要名1Column)
                Me.摘要CD2 = New BaseCore.Common.Field.ItemData(tbl.摘要CD2Column)
                Me.摘要名2 = New BaseCore.Common.Field.ItemData(tbl.摘要名2Column)
                Me.運送会社CD = New BaseCore.Common.Field.ItemData(tbl.運送会社CDColumn)
                Me.配達方法 = New BaseCore.Common.Field.ItemData(tbl.配達方法Column)
                Me.配達日付 = New BaseCore.Common.Field.ItemData(tbl.配達日付Column)
                Me.配達時刻区分 = New BaseCore.Common.Field.ItemData(tbl.配達時刻区分Column)
                'Me.代引金額 = New BaseCore.Common.Field.ItemData(tbl.代引金額Column, Config.桁数金額整数, Config.桁数金額小数)
                Me.送り状区分 = New BaseCore.Common.Field.ItemData(tbl.送り状区分Column)
                Me.送り状NO = New BaseCore.Common.Field.ItemData(tbl.送り状NOColumn)
                Me.車番 = New BaseCore.Common.Field.ItemData(tbl.車番Column)
                Me.便 = New BaseCore.Common.Field.ItemData(tbl.便Column, 3, 0)
                'Me.運送扱数 = New BaseCore.Common.Field.ItemData(tbl.運送扱数Column, Config.桁数数量整数, Config.桁数数量小数)
                'Me.運送単価 = New BaseCore.Common.Field.ItemData(tbl.運送単価Column, Config.桁数単価整数, Config.桁数単価小数)
                'Me.運送金額 = New BaseCore.Common.Field.ItemData(tbl.運送金額Column, Config.桁数金額整数, Config.桁数金額小数)
                Me.注文ショップCD = New BaseCore.Common.Field.ItemData(tbl.注文ショップCDColumn)
                Me.注文NO = New BaseCore.Common.Field.ItemData(tbl.注文NOColumn)
                Me.注文日付 = New BaseCore.Common.Field.ItemData(tbl.注文日付Column)
                Me.注文名1 = New BaseCore.Common.Field.ItemData(tbl.注文名1Column)
                Me.注文名2 = New BaseCore.Common.Field.ItemData(tbl.注文名2Column)
                Me.注文会社名 = New BaseCore.Common.Field.ItemData(tbl.注文会社名Column)
                Me.注文部署名 = New BaseCore.Common.Field.ItemData(tbl.注文部署名Column)
                Me.注文郵便番号 = New BaseCore.Common.Field.ItemData(tbl.注文郵便番号Column)
                Me.注文住所1 = New BaseCore.Common.Field.ItemData(tbl.注文住所1Column)
                Me.注文住所2 = New BaseCore.Common.Field.ItemData(tbl.注文住所2Column)
                Me.注文TEL = New BaseCore.Common.Field.ItemData(tbl.注文TELColumn)
                Me.注文メールアドレス = New BaseCore.Common.Field.ItemData(tbl.注文メールアドレスColumn, 50)
                Me.注文支払区分 = New BaseCore.Common.Field.ItemData(tbl.注文支払区分Column)
                Me.注文入金状況 = New BaseCore.Common.Field.ItemData(tbl.注文入金状況Column)
                Me.注文ギフト包装区分 = New BaseCore.Common.Field.ItemData(tbl.注文ギフト包装区分Column)
                Me.注文ギフトメッセージ = New BaseCore.Common.Field.ItemData(tbl.注文ギフトメッセージColumn)
                Me.注文のし表書き区分 = New BaseCore.Common.Field.ItemData(tbl.注文のし表書き区分Column)
                Me.注文のし名入れ = New BaseCore.Common.Field.ItemData(tbl.注文のし名入れColumn)
                'Me.注文品代 = New BaseCore.Common.Field.ItemData(tbl.注文品代Column, Config.桁数数量整数, Config.桁数数量小数)
                'Me.注文軽減税 = New BaseCore.Common.Field.ItemData(tbl.注文軽減税Column, Config.桁数数量整数, Config.桁数数量小数)
                'Me.注文消費税 = New BaseCore.Common.Field.ItemData(tbl.注文消費税Column, Config.桁数数量整数, Config.桁数数量小数)
                'Me.注文送料 = New BaseCore.Common.Field.ItemData(tbl.注文送料Column, Config.桁数数量整数, Config.桁数数量小数)
                'Me.注文手数料 = New BaseCore.Common.Field.ItemData(tbl.注文手数料Column, Config.桁数数量整数, Config.桁数数量小数)
                'Me.注文ギフト包装代 = New BaseCore.Common.Field.ItemData(tbl.注文ギフト包装代Column, Config.桁数数量整数, Config.桁数数量小数)
                'Me.注文値引額 = New BaseCore.Common.Field.ItemData(tbl.注文値引額Column, Config.桁数数量整数, Config.桁数数量小数)
                'Me.注文利用ポイント = New BaseCore.Common.Field.ItemData(tbl.注文利用ポイントColumn, Config.桁数数量整数, Config.桁数数量小数)
                'Me.注文請求金額 = New BaseCore.Common.Field.ItemData(tbl.注文請求金額Column, Config.桁数数量整数, Config.桁数数量小数)
                Me.取込日時 = New BaseCore.Common.Field.ItemData(tbl.取込日時Column)
                Me.登録日時 = New BaseCore.Common.Field.ItemData(tbl.登録日時Column)
                Me.更新日時 = New BaseCore.Common.Field.ItemData(tbl.更新日時Column)
                Me.登録者ID = New BaseCore.Common.Field.ItemData(tbl.登録者IDColumn)
                Me.登録者名 = New BaseCore.Common.Field.ItemData(tbl.登録者名Column)
                Me.更新者ID = New BaseCore.Common.Field.ItemData(tbl.更新者IDColumn)
                Me.更新者名 = New BaseCore.Common.Field.ItemData(tbl.更新者名Column)
                Me.承認者ID = New BaseCore.Common.Field.ItemData(tbl.承認者IDColumn)
                Me.承認者名 = New BaseCore.Common.Field.ItemData(tbl.承認者名Column)
                Me.公開日付 = New BaseCore.Common.Field.ItemData(tbl.公開日付Column)
                Me.所属CD = New BaseCore.Common.Field.ItemData(tbl.所属CDColumn)
                Me.検品番号 = New BaseCore.Common.Field.ItemData(tbl.検品番号Column)
                Me.営業店止め区分 = New BaseCore.Common.Field.ItemData(tbl.営業店止め区分Column)
                Me.営業店名 = New BaseCore.Common.Field.ItemData(tbl.営業店名Column)

                'Me.荷主CD = New BaseCore.Common.Field.ItemData(tbl.荷主CDColumn)
                Me.仮番 = New BaseCore.Common.Field.ItemData("仮番", TypeCode.Int32)
                Me.位置 = New BaseCore.Common.Field.ItemData("位置", TypeCode.Int32)
            End Sub
        End Class
#End Region

#Region "明細"
        Public Class Detail1
            Public 伝票タイプ As BaseCore.Common.Field.ItemData
            Public 伝票番号 As BaseCore.Common.Field.ItemData
            Public 行番号 As BaseCore.Common.Field.ItemData
            Public 伝票区分 As BaseCore.Common.Field.ItemData
            Public 荷主CD内部 As BaseCore.Common.Field.ItemData
            Public 部門CD As BaseCore.Common.Field.ItemData
            Public 商品CD内部 As BaseCore.Common.Field.ItemData
            Public 商品CD As BaseCore.Common.Field.ItemData
            Public 規格CD1 As BaseCore.Common.Field.ItemData
            Public 規格CD2 As BaseCore.Common.Field.ItemData
            Public 規格CD3 As BaseCore.Common.Field.ItemData
            Public 規格CD4 As BaseCore.Common.Field.ItemData
            Public 規格CD5 As BaseCore.Common.Field.ItemData
            Public ロットNO As BaseCore.Common.Field.ItemData
            Public ロット日付 As BaseCore.Common.Field.ItemData
            Public 先方商品NO As BaseCore.Common.Field.ItemData
            Public JANCD As BaseCore.Common.Field.ItemData
            Public 商品名 As BaseCore.Common.Field.ItemData
            Public 先方商品名 As BaseCore.Common.Field.ItemData
            Public 棚番CD As BaseCore.Common.Field.ItemData
            Public 荷姿区分 As BaseCore.Common.Field.ItemData
            Public 数量 As BaseCore.Common.Field.ItemData
            Public 入数 As BaseCore.Common.Field.ItemData
            Public 総数 As BaseCore.Common.Field.ItemData
            Public 単価 As BaseCore.Common.Field.ItemData
            Public 金額 As BaseCore.Common.Field.ItemData
            Public メモ As BaseCore.Common.Field.ItemData
            Public 依頼取込フラグ As BaseCore.Common.Field.ItemData
            Public 依頼削除フラグ As BaseCore.Common.Field.ItemData

            Public 削除 As BaseCore.Common.Field.ItemData
            Public 括りCD As BaseCore.Common.Field.ItemData
            Public 総入数 As BaseCore.Common.Field.ItemData
            Public 仮番 As BaseCore.Common.Field.ItemData
            Public 位置 As BaseCore.Common.Field.ItemData

            '''========================================================================================
            ''' <SUMMARY>ｺﾝｽﾄﾗｸﾀ</SUMMARY>
            '''========================================================================================
            Public Sub New(ByVal Config As NodeCore.Common.Config)
                Dim tbl As New NodeDatabase.DataSetView.V0伝票DTDataTable

                Me.伝票タイプ = New BaseCore.Common.Field.ItemData(tbl.伝票タイプColumn)
                Me.伝票番号 = New BaseCore.Common.Field.ItemData(tbl.伝票番号Column)
                Me.行番号 = New BaseCore.Common.Field.ItemData(tbl.行番号Column)
                Me.伝票区分 = New BaseCore.Common.Field.ItemData(tbl.伝票区分Column)
                Me.荷主CD内部 = New BaseCore.Common.Field.ItemData(tbl.荷主CD内部Column)
                Me.部門CD = New BaseCore.Common.Field.ItemData(tbl.部門CDColumn)
                Me.商品CD内部 = New BaseCore.Common.Field.ItemData(tbl.商品CD内部Column)
                Me.商品CD = New BaseCore.Common.Field.ItemData(tbl.商品CDColumn)
                Me.規格CD1 = New BaseCore.Common.Field.ItemData(tbl.規格CD1Column)
                Me.規格CD2 = New BaseCore.Common.Field.ItemData(tbl.規格CD2Column)
                Me.規格CD3 = New BaseCore.Common.Field.ItemData(tbl.規格CD3Column)
                Me.規格CD4 = New BaseCore.Common.Field.ItemData(tbl.規格CD4Column)
                Me.規格CD5 = New BaseCore.Common.Field.ItemData(tbl.規格CD5Column)
                Me.ロットNO = New BaseCore.Common.Field.ItemData(tbl.ロットNOColumn)
                Me.ロット日付 = New BaseCore.Common.Field.ItemData(tbl.ロット日付Column)
                Me.先方商品NO = New BaseCore.Common.Field.ItemData(tbl.先方商品NOColumn)
                Me.JANCD = New BaseCore.Common.Field.ItemData(tbl.JANCDColumn)
                Me.商品名 = New BaseCore.Common.Field.ItemData(tbl.商品名Column)
                Me.先方商品名 = New BaseCore.Common.Field.ItemData(tbl.先方商品名Column)
                'Me.棚番CD = New BaseCore.Common.Field.ItemData(tbl.棚番CDColumn, Config.桁数棚番CD)
                Me.荷姿区分 = New BaseCore.Common.Field.ItemData(tbl.荷姿区分Column)
                'Me.数量 = New BaseCore.Common.Field.ItemData(tbl.数量Column, Config.桁数数量整数, Config.桁数数量小数)
                'Me.入数 = New BaseCore.Common.Field.ItemData(tbl.入数Column, Config.桁数数量整数, Config.桁数数量小数)
                'Me.総数 = New BaseCore.Common.Field.ItemData(tbl.総数Column, Config.桁数数量整数, Config.桁数数量小数)
                'Me.単価 = New BaseCore.Common.Field.ItemData(tbl.単価Column, Config.桁数単価整数, Config.桁数単価小数)
                'Me.金額 = New BaseCore.Common.Field.ItemData(tbl.金額Column, Config.桁数金額整数, Config.桁数金額小数)
                Me.メモ = New BaseCore.Common.Field.ItemData(tbl.メモColumn)
                Me.依頼取込フラグ = New BaseCore.Common.Field.ItemData(tbl.依頼取込フラグColumn)
                Me.依頼削除フラグ = New BaseCore.Common.Field.ItemData(tbl.依頼削除フラグColumn)

                Me.削除 = New BaseCore.Common.Field.ItemData("削除", TypeCode.Int16)
                'Me.括りCD = New BaseCore.Common.Field.ItemData(tbl.商品_括りCDColumn)
                'Me.総入数 = New BaseCore.Common.Field.ItemData(tbl.商品_総入数Column)
                Me.仮番 = New BaseCore.Common.Field.ItemData("仮番", TypeCode.Int32)
                Me.位置 = New BaseCore.Common.Field.ItemData("位置", TypeCode.Int32)
            End Sub
        End Class
#End Region
#End Region

#Region "変数定義"
        Public Class Paramater
            Public Header As Header
            Public Detail1 As New List(Of Detail1)
        End Class

        '''========================================================================================
        ''' <summary>ﾍｯﾀﾞ</summary>
        '''========================================================================================
        Private _Fields As New Paramater
        Public Property Fields() As Paramater
            Get
                Return _Fields
            End Get

            Set(ByVal value As Paramater)
                _Fields = value
            End Set
        End Property
#End Region

#Region "ｺﾝｽﾄﾗｸﾀ"
        '''========================================================================================
        ''' <SUMMARY>ｺﾝｽﾄﾗｸﾀ</SUMMARY>
        ''' <PARAM name="Security">ｾｷｭﾘﾃｨ ｸﾗｽ</PARAM>
        ''' <PARAM name="Config">ｺﾝﾌｨｸﾞ ｸﾗｽ</PARAM>
        ''' <PARAM name="PagePath">ﾍﾟｰｼﾞﾊﾟｽ</PARAM>
        '''========================================================================================
        Public Sub New(ByVal Security As NodeCore.Common.Security, ByVal Config As NodeCore.Common.Config, ByVal PagePath As String)
            '----------------------------------------------------------------------
            ' ｾｷｭﾘﾃｨ 設定
            '----------------------------------------------------------------------
            MyBase.New(Security, Config, PagePath)

            Me.Fields.Header = New Header(Config)
        End Sub
#End Region

#Region "ﾍｯﾀﾞ"
#Region "Clear"
        '''========================================================================================
        ''' <SUMMARY>ｸﾘｱ</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Clear_Header(Optional ByVal KeyClear As Boolean = False) As Boolean
            '----------------------------------------------------------------------
            ' ｷｰ部ｸﾘｱ
            '----------------------------------------------------------------------
            If KeyClear Then
                Me.Fields.Header.伝票タイプ.Value = Nothing
                Me.Fields.Header.伝票番号.Value = Nothing
                Me.Fields.Header.荷主CD内部.Value = Nothing
                Me.Fields.Header.荷主CD.Value = Nothing
                Me.Fields.Header.拠点CD.Value = Nothing
            End If

            '----------------------------------------------------------------------
            ' ﾃﾞｰﾀ部ｸﾘｱ
            '----------------------------------------------------------------------
            Me.Fields.Header.入力日付.Value = Format(Now, "yyyy/MM/dd")
            Me.Fields.Header.伝票日付.Value = Format(Now, "yyyy/MM/dd")
            Me.Fields.Header.伝票区分.Value = Nothing
            Me.Fields.Header.緊急区分.Value = '1'
            Me.Fields.Header.指示番号.Value = Nothing
            Me.Fields.Header.指示区分.Value = Nothing
            Me.Fields.Header.指示状況.Value = Nothing
            Me.Fields.Header.指示者名.Value = Nothing
            Me.Fields.Header.企業CD.Value = Nothing
            Me.Fields.Header.届先CD.Value = Nothing
            Me.Fields.Header.届先名1.Value = Nothing
            Me.Fields.Header.届先名2.Value = Nothing
            Me.Fields.Header.届先郵便番号.Value = Nothing
            Me.Fields.Header.届先住所1.Value = Nothing
            Me.Fields.Header.届先住所2.Value = Nothing
            Me.Fields.Header.届先TEL.Value = Nothing
            Me.Fields.Header.摘要CD1.Value = Nothing
            Me.Fields.Header.摘要名1.Value = Nothing
            Me.Fields.Header.摘要CD2.Value = Nothing
            Me.Fields.Header.摘要名2.Value = Nothing
            Me.Fields.Header.運送会社CD.Value = Nothing
            Me.Fields.Header.配達方法.Value = Nothing
            Me.Fields.Header.配達日付.Value = Nothing
            Me.Fields.Header.配達時刻区分.Value = Nothing
            Me.Fields.Header.代引金額.Value = Nothing
            Me.Fields.Header.送り状区分.Value = Nothing
            Me.Fields.Header.送り状NO.Value = Nothing
            Me.Fields.Header.車番.Value = Nothing
            Me.Fields.Header.便.Value = Nothing
            Me.Fields.Header.運送扱数.Value = Nothing
            Me.Fields.Header.運送単価.Value = Nothing
            Me.Fields.Header.運送金額.Value = Nothing
            Me.Fields.Header.注文日付.Value = Nothing
            Me.Fields.Header.注文名1.Value = Nothing
            Me.Fields.Header.注文名2.Value = Nothing
            Me.Fields.Header.注文会社名.Value = Nothing
            Me.Fields.Header.注文部署名.Value = Nothing
            Me.Fields.Header.注文郵便番号.Value = Nothing
            Me.Fields.Header.注文住所1.Value = Nothing
            Me.Fields.Header.注文住所2.Value = Nothing
            Me.Fields.Header.注文TEL.Value = Nothing
            Me.Fields.Header.注文メールアドレス.Value = Nothing
            Me.Fields.Header.注文支払区分.Value = Nothing
            Me.Fields.Header.注文入金状況.Value = Nothing
            Me.Fields.Header.注文ギフト包装区分.Value = Nothing
            Me.Fields.Header.注文ギフトメッセージ.Value = Nothing
            Me.Fields.Header.注文のし表書き区分.Value = Nothing
            Me.Fields.Header.注文のし名入れ.Value = Nothing
            Me.Fields.Header.注文品代.Value = Nothing
            Me.Fields.Header.注文消費税.Value = Nothing
            Me.Fields.Header.注文送料.Value = Nothing
            Me.Fields.Header.注文手数料.Value = Nothing
            Me.Fields.Header.注文ギフト包装代.Value = Nothing
            Me.Fields.Header.注文値引額.Value = Nothing
            Me.Fields.Header.注文利用ポイント.Value = Nothing
            Me.Fields.Header.注文請求金額.Value = Nothing
            Me.Fields.Header.取込日時.Value = Nothing
            Me.Fields.Header.登録日時.Value = Nothing
            Me.Fields.Header.更新日時.Value = Nothing
            Me.Fields.Header.登録者ID.Value = Nothing
            Me.Fields.Header.登録者名.Value = Nothing
            Me.Fields.Header.更新者ID.Value = Nothing
            Me.Fields.Header.更新者名.Value = Nothing
            Me.Fields.Header.承認者ID.Value = Nothing
            Me.Fields.Header.承認者名.Value = Nothing
            Me.Fields.Header.公開日付.Value = Nothing
            Me.Fields.Header.所属CD.Value = Nothing
            Me.Fields.Header.検品番号.Value = Nothing
            Me.Fields.Header.営業店止め区分.Value = '1'
            Me.Fields.Header.営業店名.Value = Nothing
            Me.Fields.Header.仮番.Value = Nothing
            Me.Fields.Header.位置.Value = Nothing

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "Exist"
        '''========================================================================================
        ''' <SUMMARY>存在確認</SUMMARY>
        ''' <RETURNS>True:あり, False:なし</RETURNS>
        '''========================================================================================
        Protected Overrides Function Exist_Header() As Boolean
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetViewTableAdapters.V0伝票HDTableAdapter
            Dim tbl As New NodeDatabase.DataSetView.V0伝票HDDataTable
            Dim qry As New Collection

            '----------------------------------------------------------------------
            ' 条件設定
            '----------------------------------------------------------------------
            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl.伝票タイプColumn.ColumnName, Me.Fields.Header.伝票タイプ.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.伝票番号Column.ColumnName, Me.Fields.Header.伝票番号.Value, BaseDatabase.Contents.Compare.Equal))

            '----------------------------------------------------------------------
            ' 読込
            '----------------------------------------------------------------------
            tbl = ada.SelectByCommon(qry)
            If tbl.Count = 0 Then
                Return False
            Else
                Return True
            End If
        End Function
#End Region

#Region "Read"
        '''========================================================================================
        ''' <SUMMARY>読込</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Read_Header() As Boolean
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetViewTableAdapters.V0伝票HDTableAdapter
            Dim tbl As New NodeDatabase.DataSetView.V0伝票HDDataTable
            Dim qry As New Collection

            '----------------------------------------------------------------------
            ' 条件設定
            '----------------------------------------------------------------------
            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl.伝票タイプColumn.ColumnName, Me.Fields.Header.伝票タイプ.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.伝票番号Column.ColumnName, Me.Fields.Header.伝票番号.Value, BaseDatabase.Contents.Compare.Equal))
            '自拠点に限定、自拠点が空白の場合はすべての拠点を読み込む
            'qry.Add(New BaseDatabase.Condition(tbl.拠点CDColumn.ColumnName, Me.Security.PlaceCode, BaseDatabase.Contents.Compare.Equal, BaseDatabase.Contents.EmptyHandle.Skip))

            '----------------------------------------------------------------------
            ' 読込
            '----------------------------------------------------------------------
            tbl = ada.SelectByCommon(qry)
            Me.Fields.Header.伝票タイプ.Value = tbl(0).Item(tbl.伝票タイプColumn.ColumnName)
                Me.Fields.Header.伝票番号.Value = tbl(0).Item(tbl.伝票番号Column.ColumnName)
                Me.Fields.Header.入力日付.Value = tbl(0).Item(tbl.入力日付Column.ColumnName)
                Me.Fields.Header.伝票日付.Value = tbl(0).Item(tbl.伝票日付Column.ColumnName)
                Me.Fields.Header.伝票区分.Value = tbl(0).Item(tbl.伝票区分Column.ColumnName)
                Me.Fields.Header.緊急区分.Value = tbl(0).Item(tbl.緊急区分Column.ColumnName)
                Me.Fields.Header.指示番号.Value = tbl(0).Item(tbl.指示番号Column.ColumnName)
                Me.Fields.Header.指示区分.Value = tbl(0).Item(tbl.指示区分Column.ColumnName)
                Me.Fields.Header.指示状況.Value = tbl(0).Item(tbl.指示状況Column.ColumnName)
                Me.Fields.Header.指示者名.Value = tbl(0).Item(tbl.指示者名Column.ColumnName)
            Me.Fields.Header.荷主CD内部.Value = tbl(0).Item(tbl.荷主CD内部Column.ColumnName)
            Me.Fields.Header.拠点CD.Value = tbl(0).Item(tbl.拠点CDColumn.ColumnName)
            Me.Fields.Header.企業CD.Value = tbl(0).Item(tbl.企業CDColumn.ColumnName)
            Me.Fields.Header.届先CD.Value = tbl(0).Item(tbl.届先CDColumn.ColumnName)
            Me.Fields.Header.届先名1.Value = tbl(0).Item(tbl.届先名1Column.ColumnName)
                Me.Fields.Header.届先名2.Value = tbl(0).Item(tbl.届先名2Column.ColumnName)
                Me.Fields.Header.届先郵便番号.Value = tbl(0).Item(tbl.届先郵便番号Column.ColumnName)
                Me.Fields.Header.届先住所1.Value = tbl(0).Item(tbl.届先住所1Column.ColumnName)
                Me.Fields.Header.届先住所2.Value = tbl(0).Item(tbl.届先住所2Column.ColumnName)
                Me.Fields.Header.届先TEL.Value = tbl(0).Item(tbl.届先TELColumn.ColumnName)
            Me.Fields.Header.摘要CD1.Value = tbl(0).Item(tbl.摘要CD1Column.ColumnName)
            Me.Fields.Header.摘要名1.Value = tbl(0).Item(tbl.摘要名1Column.ColumnName)
            Me.Fields.Header.摘要CD2.Value = tbl(0).Item(tbl.摘要CD2Column.ColumnName)
            Me.Fields.Header.摘要名2.Value = tbl(0).Item(tbl.摘要名2Column.ColumnName)
            Me.Fields.Header.運送会社CD.Value = tbl(0).Item(tbl.運送会社CDColumn.ColumnName)
            Me.Fields.Header.配達方法.Value = tbl(0).Item(tbl.配達方法Column.ColumnName)
                Me.Fields.Header.配達日付.Value = tbl(0).Item(tbl.配達日付Column.ColumnName)
                Me.Fields.Header.配達時刻区分.Value = tbl(0).Item(tbl.配達時刻区分Column.ColumnName)
                Me.Fields.Header.代引金額.Value = tbl(0).Item(tbl.代引金額Column.ColumnName)
                Me.Fields.Header.送り状区分.Value = tbl(0).Item(tbl.送り状区分Column.ColumnName)
                Me.Fields.Header.送り状NO.Value = tbl(0).Item(tbl.送り状NOColumn.ColumnName)
                Me.Fields.Header.車番.Value = tbl(0).Item(tbl.車番Column.ColumnName)
                Me.Fields.Header.便.Value = tbl(0).Item(tbl.便Column.ColumnName)
                Me.Fields.Header.運送扱数.Value = tbl(0).Item(tbl.運送扱数Column.ColumnName)
                Me.Fields.Header.運送単価.Value = tbl(0).Item(tbl.運送単価Column.ColumnName)
                Me.Fields.Header.運送金額.Value = tbl(0).Item(tbl.運送金額Column.ColumnName)
                Me.Fields.Header.注文ショップCD.Value = tbl(0).Item(tbl.注文ショップCDColumn.ColumnName)
                Me.Fields.Header.注文NO.Value = tbl(0).Item(tbl.注文NOColumn.ColumnName)
                Me.Fields.Header.注文日付.Value = tbl(0).Item(tbl.注文日付Column.ColumnName)
                Me.Fields.Header.注文名1.Value = tbl(0).Item(tbl.注文名1Column.ColumnName)
                Me.Fields.Header.注文名2.Value = tbl(0).Item(tbl.注文名2Column.ColumnName)
            Me.Fields.Header.注文会社名.Value = tbl(0).Item(tbl.注文会社名Column.ColumnName)
            Me.Fields.Header.注文部署名.Value = tbl(0).Item(tbl.注文部署名Column.ColumnName)
                Me.Fields.Header.注文郵便番号.Value = tbl(0).Item(tbl.注文郵便番号Column.ColumnName)
                Me.Fields.Header.注文住所1.Value = tbl(0).Item(tbl.注文住所1Column.ColumnName)
                Me.Fields.Header.注文住所2.Value = tbl(0).Item(tbl.注文住所2Column.ColumnName)
                Me.Fields.Header.注文TEL.Value = tbl(0).Item(tbl.注文TELColumn.ColumnName)
                Me.Fields.Header.注文メールアドレス.Value = tbl(0).Item(tbl.注文メールアドレスColumn.ColumnName)
                Me.Fields.Header.注文支払区分.Value = tbl(0).Item(tbl.注文支払区分Column.ColumnName)
                Me.Fields.Header.注文入金状況.Value = tbl(0).Item(tbl.注文入金状況Column.ColumnName)
                Me.Fields.Header.注文ギフト包装区分.Value = tbl(0).Item(tbl.注文ギフト包装区分Column.ColumnName)
                Me.Fields.Header.注文ギフトメッセージ.Value = tbl(0).Item(tbl.注文ギフトメッセージColumn.ColumnName)
                Me.Fields.Header.注文のし表書き区分.Value = tbl(0).Item(tbl.注文のし表書き区分Column.ColumnName)
                Me.Fields.Header.注文のし名入れ.Value = tbl(0).Item(tbl.注文のし名入れColumn.ColumnName)
                Me.Fields.Header.注文品代.Value = tbl(0).Item(tbl.注文品代Column.ColumnName)
                Me.Fields.Header.注文軽減税.Value = tbl(0).Item(tbl.注文軽減税Column.ColumnName)
                Me.Fields.Header.注文消費税.Value = tbl(0).Item(tbl.注文消費税Column.ColumnName)
                Me.Fields.Header.注文送料.Value = tbl(0).Item(tbl.注文送料Column.ColumnName)
                Me.Fields.Header.注文手数料.Value = tbl(0).Item(tbl.注文手数料Column.ColumnName)
                Me.Fields.Header.注文ギフト包装代.Value = tbl(0).Item(tbl.注文ギフト包装代Column.ColumnName)
                Me.Fields.Header.注文値引額.Value = tbl(0).Item(tbl.注文値引額Column.ColumnName)
                Me.Fields.Header.注文利用ポイント.Value = tbl(0).Item(tbl.注文利用ポイントColumn.ColumnName)
                Me.Fields.Header.注文請求金額.Value = tbl(0).Item(tbl.注文請求金額Column.ColumnName)
                Me.Fields.Header.取込日時.Value = tbl(0).Item(tbl.取込日時Column.ColumnName)
                Me.Fields.Header.登録日時.Value = tbl(0).Item(tbl.登録日時Column.ColumnName)
                Me.Fields.Header.更新日時.Value = tbl(0).Item(tbl.更新日時Column.ColumnName)
                Me.Fields.Header.登録者ID.Value = tbl(0).Item(tbl.登録者IDColumn.ColumnName)
                Me.Fields.Header.登録者名.Value = tbl(0).Item(tbl.登録者名Column.ColumnName)
                Me.Fields.Header.更新者ID.Value = tbl(0).Item(tbl.更新者IDColumn.ColumnName)
                Me.Fields.Header.更新者名.Value = tbl(0).Item(tbl.更新者名Column.ColumnName)
                Me.Fields.Header.承認者ID.Value = tbl(0).Item(tbl.承認者IDColumn.ColumnName)
                Me.Fields.Header.承認者名.Value = tbl(0).Item(tbl.承認者名Column.ColumnName)
                Me.Fields.Header.公開日付.Value = tbl(0).Item(tbl.公開日付Column.ColumnName)
            Me.Fields.Header.所属CD.Value = tbl(0).Item(tbl.所属CDColumn.ColumnName)
            Me.Fields.Header.検品番号.Value = tbl(0).Item(tbl.検品番号Column.ColumnName)
                Me.Fields.Header.営業店止め区分.Value = tbl(0).Item(tbl.営業店止め区分Column.ColumnName)
                Me.Fields.Header.営業店名.Value = tbl(0).Item(tbl.営業店名Column.ColumnName)

            'Me.Fields.Header.荷主CD.Value = tbl(0).Item(tbl.荷主CDColumn.ColumnName)

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "Write"
        '''========================================================================================
        ''' <SUMMARY>書込</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Write_Header() As Boolean
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim strNow As String = Format(Now, "yyyy/MM/dd HH:mm:ss")

            Dim ada As New NodeDatabase.DataSetWorkTableAdapters.W伝票HDTableAdapter
            Dim tbl As New NodeDatabase.DataSetWork.W伝票HDDataTable
            Dim qry As New Collection

            '----------------------------------------------------------------------
            ' 事前削除
            '----------------------------------------------------------------------
            Try
                qry.Clear()
                qry.Add(New BaseDatabase.Condition(tbl.セッションIDColumn.ColumnName, Me.Security.セッションID, BaseDatabase.Contents.Compare.Equal))

                ada.DeleteByCommon(qry)
            Catch ex As Exception
                Me.LastError = "以下のエラーのため、処理を継続できません。" & vbCrLf & ex.Message
                Return False
            End Try

            '----------------------------------------------------------------------
            ' 事前準備
            '----------------------------------------------------------------------
            Me.Fields.Header.更新日時.Value = strNow
            Me.Fields.Header.更新者ID.Value = Me.Security.ユーザーCD
            Me.Fields.Header.更新者名.Value = Me.Security.ユーザー名

            If Me.Fields.Header.登録者ID.Value = "" Then
                Me.Fields.Header.登録者ID.Value = Me.Fields.Header.更新者ID.Value
                Me.Fields.Header.登録者名.Value = Me.Fields.Header.更新者名.Value
            End If

            '----------------------------------------------------------------------
            ' 保存
            '----------------------------------------------------------------------
            Try
                ada.Insert(Me.Security.セッションID _
                         , Me.Fields.Header.伝票タイプ.Value _
                         , Me.Fields.Header.伝票番号.Value _
                         , Me.Fields.Header.入力日付.Value _
                         , Me.Fields.Header.伝票日付.Value _
                         , Me.Fields.Header.伝票区分.Value _
                         , Me.Fields.Header.緊急区分.Value _
                         , Me.Fields.Header.指示番号.Value _
                         , Me.Fields.Header.指示区分.Value _
                         , Me.Fields.Header.指示状況.Value _
                         , Me.Fields.Header.指示者名.Value _
                         , Me.Fields.Header.荷主CD内部.Value _
                         , Me.Fields.Header.拠点CD.Value _
                         , Me.Fields.Header.企業CD.Value _
                         , Me.Fields.Header.届先CD.Value _
                         , Me.Fields.Header.届先名1.Value _
                         , Me.Fields.Header.届先名2.Value _
                         , Me.Fields.Header.届先郵便番号.Value _
                         , Me.Fields.Header.届先住所1.Value _
                         , Me.Fields.Header.届先住所2.Value _
                         , Me.Fields.Header.届先TEL.Value _
                         , Me.Fields.Header.摘要CD1.Value _
                         , Me.Fields.Header.摘要名1.Value _
                         , Me.Fields.Header.摘要CD2.Value _
                         , Me.Fields.Header.摘要名2.Value _
                         , Me.Fields.Header.運送会社CD.Value _
                         , Me.Fields.Header.配達方法.Value _
                         , Me.Fields.Header.配達日付.Value _
                         , Me.Fields.Header.配達時刻区分.Value _
                         , BaseCore.Common.Text.CVal(Me.Fields.Header.代引金額.Value) _
                         , Me.Fields.Header.送り状区分.Value _
                         , Me.Fields.Header.送り状NO.Value _
                         , Me.Fields.Header.車番.Value _
                         , BaseCore.Common.Text.CVal(Me.Fields.Header.便.Value) _
                         , BaseCore.Common.Text.CVal(Me.Fields.Header.運送扱数.Value) _
                         , BaseCore.Common.Text.CVal(Me.Fields.Header.運送単価.Value) _
                         , BaseCore.Common.Text.CVal(Me.Fields.Header.運送金額.Value) _
                         , Me.Fields.Header.注文ショップCD.Value _
                         , Me.Fields.Header.注文NO.Value _
                         , Me.Fields.Header.注文日付.Value _
                         , Me.Fields.Header.注文名1.Value _
                         , Me.Fields.Header.注文名2.Value _
                         , Me.Fields.Header.注文会社名.Value _
                         , Me.Fields.Header.注文部署名.Value _
                         , Me.Fields.Header.注文郵便番号.Value _
                         , Me.Fields.Header.注文住所1.Value _
                         , Me.Fields.Header.注文住所2.Value _
                         , Me.Fields.Header.注文TEL.Value _
                         , Me.Fields.Header.注文メールアドレス.Value _
                         , Me.Fields.Header.注文支払区分.Value _
                         , Me.Fields.Header.注文入金状況.Value _
                         , Me.Fields.Header.注文ギフト包装区分.Value _
                         , Me.Fields.Header.注文ギフトメッセージ.Value _
                         , Me.Fields.Header.注文のし表書き区分.Value _
                         , Me.Fields.Header.注文のし名入れ.Value _
                         , BaseCore.Common.Text.CVal(Me.Fields.Header.注文品代.Value) _
                         , BaseCore.Common.Text.CVal(Me.Fields.Header.注文軽減税.Value) _
                         , BaseCore.Common.Text.CVal(Me.Fields.Header.注文消費税.Value) _
                         , BaseCore.Common.Text.CVal(Me.Fields.Header.注文送料.Value) _
                         , BaseCore.Common.Text.CVal(Me.Fields.Header.注文手数料.Value) _
                         , BaseCore.Common.Text.CVal(Me.Fields.Header.注文ギフト包装代.Value) _
                         , BaseCore.Common.Text.CVal(Me.Fields.Header.注文値引額.Value) _
                         , BaseCore.Common.Text.CVal(Me.Fields.Header.注文利用ポイント.Value) _
                         , BaseCore.Common.Text.CVal(Me.Fields.Header.注文請求金額.Value) _
                         , Me.Fields.Header.取込日時.Value _
                         , Me.Fields.Header.登録日時.Value _
                         , Me.Fields.Header.更新日時.Value _
                         , Me.Fields.Header.登録者ID.Value _
                         , Me.Fields.Header.登録者名.Value _
                         , Me.Fields.Header.更新者ID.Value _
                         , Me.Fields.Header.更新者名.Value _
                         , Me.Fields.Header.承認者ID.Value _
                         , Me.Fields.Header.承認者名.Value _
                         , Me.Fields.Header.荷主CD.Value _
                         , Me.Fields.Header.公開日付.Value _
                         , Me.Fields.Header.所属CD.Value _
                         , Me.Fields.Header.検品番号.Value _
                         , Me.Fields.Header.営業店止め区分.Value _
                         , Me.Fields.Header.営業店名.Value _
                         , "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""
                          )
            Catch ex As Exception
                Me.LastError = "以下のエラーのため、処理を継続できません。" & vbCrLf & ex.Message
                Return False
            End Try

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "Delete"
        '''========================================================================================
        ''' <SUMMARY>削除</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Delete_Header() As Boolean
            Return Me.Write_Header()
            Return Me.Write_Detail1()
        End Function
#End Region

#Region "Valid_Write"
        '''========================================================================================
        ''' <SUMMARY>検査(共通)</SUMMARY>
        ''' <RETURNS>True:正常, False:不正</RETURNS>
        '''========================================================================================
        Protected Overrides Function Valid_Write_Header_Common() As NodeContents.ActionButton.ErrorLevel
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim strMsg As String = ""
            Dim Validator As New BaseCore.Common.Validator

            '------------------------------------------------------------------
            ' ｴﾗｰｸﾘｱ
            '------------------------------------------------------------------
            Me.Fields.Header.伝票タイプ.IsError = False
            Me.Fields.Header.伝票番号.IsError = False
            Me.Fields.Header.入力日付.IsError = False
            Me.Fields.Header.伝票日付.IsError = False
            Me.Fields.Header.伝票区分.IsError = False
            Me.Fields.Header.緊急区分.IsError = False
            Me.Fields.Header.指示番号.IsError = False
            Me.Fields.Header.指示区分.IsError = False
            Me.Fields.Header.指示状況.IsError = False
            Me.Fields.Header.指示者名.IsError = False
            Me.Fields.Header.荷主CD内部.IsError = False
            Me.Fields.Header.拠点CD.IsError = False
            Me.Fields.Header.企業CD.IsError = False
            Me.Fields.Header.届先CD.IsError = False
            Me.Fields.Header.届先名1.IsError = False
            Me.Fields.Header.届先名2.IsError = False
            Me.Fields.Header.届先郵便番号.IsError = False
            Me.Fields.Header.届先住所1.IsError = False
            Me.Fields.Header.届先住所2.IsError = False
            Me.Fields.Header.届先TEL.IsError = False
            Me.Fields.Header.摘要CD1.IsError = False
            Me.Fields.Header.摘要名1.IsError = False
            Me.Fields.Header.摘要CD2.IsError = False
            Me.Fields.Header.摘要名2.IsError = False
            Me.Fields.Header.運送会社CD.IsError = False
            Me.Fields.Header.配達方法.IsError = False
            Me.Fields.Header.配達日付.IsError = False
            Me.Fields.Header.配達時刻区分.IsError = False
            Me.Fields.Header.代引金額.IsError = False
            Me.Fields.Header.送り状区分.IsError = False
            Me.Fields.Header.送り状NO.IsError = False
            Me.Fields.Header.車番.IsError = False
            Me.Fields.Header.便.IsError = False
            Me.Fields.Header.運送扱数.IsError = False
            Me.Fields.Header.運送単価.IsError = False
            Me.Fields.Header.運送金額.IsError = False
            Me.Fields.Header.注文ショップCD.IsError = False
            Me.Fields.Header.注文NO.IsError = False
            Me.Fields.Header.注文日付.IsError = False
            Me.Fields.Header.注文名1.IsError = False
            Me.Fields.Header.注文名2.IsError = False
            Me.Fields.Header.注文会社名.IsError = False
            Me.Fields.Header.注文部署名.IsError = False
            Me.Fields.Header.注文郵便番号.IsError = False
            Me.Fields.Header.注文住所1.IsError = False
            Me.Fields.Header.注文住所2.IsError = False
            Me.Fields.Header.注文TEL.IsError = False
            Me.Fields.Header.注文メールアドレス.IsError = False
            Me.Fields.Header.注文支払区分.IsError = False
            Me.Fields.Header.注文入金状況.IsError = False
            Me.Fields.Header.注文ギフト包装区分.IsError = False
            Me.Fields.Header.注文ギフトメッセージ.IsError = False
            Me.Fields.Header.注文のし表書き区分.IsError = False
            Me.Fields.Header.注文のし名入れ.IsError = False
            Me.Fields.Header.注文品代.IsError = False
            Me.Fields.Header.注文軽減税.IsError = False
            Me.Fields.Header.注文消費税.IsError = False
            Me.Fields.Header.注文送料.IsError = False
            Me.Fields.Header.注文手数料.IsError = False
            Me.Fields.Header.注文ギフト包装代.IsError = False
            Me.Fields.Header.注文値引額.IsError = False
            Me.Fields.Header.注文利用ポイント.IsError = False
            Me.Fields.Header.注文請求金額.IsError = False
            Me.Fields.Header.取込日時.IsError = False
            Me.Fields.Header.登録日時.IsError = False
            Me.Fields.Header.更新日時.IsError = False
            Me.Fields.Header.登録者ID.IsError = False
            Me.Fields.Header.登録者名.IsError = False
            Me.Fields.Header.更新者ID.IsError = False
            Me.Fields.Header.更新者名.IsError = False
            Me.Fields.Header.承認者ID.IsError = False
            Me.Fields.Header.承認者名.IsError = False
            Me.Fields.Header.公開日付.IsError = False
            Me.Fields.Header.所属CD.IsError = False
            Me.Fields.Header.検品番号.IsError = False
            Me.Fields.Header.営業店止め区分.IsError = False
            Me.Fields.Header.営業店名.IsError = False

            Me.Fields.Header.荷主CD.IsError = False

            '------------------------------------------------------------------
            ' 共通検査
            '------------------------------------------------------------------
            If Not Validator.BaseChecker(Me.Fields.Header.伝票タイプ) Then : Me.Fields.Header.伝票タイプ.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.伝票番号) Then : Me.Fields.Header.伝票番号.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.入力日付) Then : Me.Fields.Header.入力日付.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.伝票日付) Then : Me.Fields.Header.伝票日付.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.伝票区分) Then : Me.Fields.Header.伝票区分.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.緊急区分) Then : Me.Fields.Header.緊急区分.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.指示番号) Then : Me.Fields.Header.指示番号.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.指示区分) Then : Me.Fields.Header.指示区分.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.指示状況) Then : Me.Fields.Header.指示状況.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.指示者名) Then : Me.Fields.Header.指示者名.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.荷主CD内部) Then : Me.Fields.Header.荷主CD内部.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.拠点CD) Then : Me.Fields.Header.拠点CD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.企業CD) Then : Me.Fields.Header.企業CD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.届先CD) Then : Me.Fields.Header.届先CD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.届先名1) Then : Me.Fields.Header.届先名1.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.届先名2) Then : Me.Fields.Header.届先名2.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.届先郵便番号) Then : Me.Fields.Header.届先郵便番号.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.届先住所1) Then : Me.Fields.Header.届先住所1.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.届先住所2) Then : Me.Fields.Header.届先住所2.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.届先TEL) Then : Me.Fields.Header.届先TEL.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.摘要CD1) Then : Me.Fields.Header.摘要CD1.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.摘要名1) Then : Me.Fields.Header.摘要名1.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.摘要CD2) Then : Me.Fields.Header.摘要CD2.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.摘要名2) Then : Me.Fields.Header.摘要名2.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.運送会社CD) Then : Me.Fields.Header.運送会社CD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.配達方法) Then : Me.Fields.Header.配達方法.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.配達日付) Then : Me.Fields.Header.配達日付.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.配達時刻区分) Then : Me.Fields.Header.配達時刻区分.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.代引金額) Then : Me.Fields.Header.代引金額.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.送り状区分) Then : Me.Fields.Header.送り状区分.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.送り状NO) Then : Me.Fields.Header.送り状NO.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.車番) Then : Me.Fields.Header.車番.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.便) Then : Me.Fields.Header.便.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.運送扱数) Then : Me.Fields.Header.運送扱数.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.運送単価) Then : Me.Fields.Header.運送単価.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.運送金額) Then : Me.Fields.Header.運送金額.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.注文ショップCD) Then : Me.Fields.Header.注文ショップCD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.注文NO) Then : Me.Fields.Header.注文NO.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.注文日付) Then : Me.Fields.Header.注文日付.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.注文名1) Then : Me.Fields.Header.注文名1.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.注文名2) Then : Me.Fields.Header.注文名2.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.注文会社名) Then : Me.Fields.Header.注文会社名.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.注文部署名) Then : Me.Fields.Header.注文部署名.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.注文郵便番号) Then : Me.Fields.Header.注文郵便番号.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.注文住所1) Then : Me.Fields.Header.注文住所1.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.注文住所2) Then : Me.Fields.Header.注文住所2.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.注文TEL) Then : Me.Fields.Header.注文TEL.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.注文メールアドレス) Then : Me.Fields.Header.注文メールアドレス.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.注文支払区分) Then : Me.Fields.Header.注文支払区分.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.注文入金状況) Then : Me.Fields.Header.注文入金状況.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.注文ギフト包装区分) Then : Me.Fields.Header.注文ギフト包装区分.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.注文ギフトメッセージ) Then : Me.Fields.Header.注文ギフトメッセージ.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.注文のし表書き区分) Then : Me.Fields.Header.注文のし表書き区分.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.注文のし名入れ) Then : Me.Fields.Header.注文のし名入れ.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.注文品代) Then : Me.Fields.Header.注文品代.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.注文軽減税) Then : Me.Fields.Header.注文軽減税.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.注文消費税) Then : Me.Fields.Header.注文消費税.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.注文送料) Then : Me.Fields.Header.注文送料.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.注文手数料) Then : Me.Fields.Header.注文手数料.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.注文ギフト包装代) Then : Me.Fields.Header.注文ギフト包装代.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.注文値引額) Then : Me.Fields.Header.注文値引額.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.注文利用ポイント) Then : Me.Fields.Header.注文利用ポイント.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.注文請求金額) Then : Me.Fields.Header.注文請求金額.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.取込日時) Then : Me.Fields.Header.取込日時.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.登録日時) Then : Me.Fields.Header.登録日時.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.更新日時) Then : Me.Fields.Header.更新日時.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.登録者ID) Then : Me.Fields.Header.登録者ID.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.登録者名) Then : Me.Fields.Header.登録者名.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.更新者ID) Then : Me.Fields.Header.更新者ID.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.更新者名) Then : Me.Fields.Header.更新者名.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.承認者ID) Then : Me.Fields.Header.承認者ID.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.承認者名) Then : Me.Fields.Header.承認者名.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.公開日付) Then : Me.Fields.Header.公開日付.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.所属CD) Then : Me.Fields.Header.所属CD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.検品番号) Then : Me.Fields.Header.検品番号.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.営業店止め区分) Then : Me.Fields.Header.営業店止め区分.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.営業店名) Then : Me.Fields.Header.営業店名.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If

            If Not Validator.BaseChecker(Me.Fields.Header.荷主CD) Then : Me.Fields.Header.荷主CD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If

            '------------------------------------------------------------------
            ' 返却
            '------------------------------------------------------------------
            MyBase.LastError = strMsg
            Select Case True
                Case strMsg = ""
                    Return NodeContents.ActionButton.ErrorLevel.None
                Case Else
                    Return NodeContents.ActionButton.ErrorLevel.Fatal
            End Select
        End Function

        '''========================================================================================
        ''' <SUMMARY>検査(個別)</SUMMARY>
        ''' <RETURNS>True:正常, False:不正</RETURNS>
        '''========================================================================================
        Protected Overrides Function Valid_Write_Header_Another() As NodeContents.ActionButton.ErrorLevel
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim strMsg As String = ""
            Dim Validator As New BaseCore.Common.Validator

            '------------------------------------------------------------------
            ' 個別検査
            '------------------------------------------------------------------

            '------------------------------------------------------------------
            ' 返却
            '------------------------------------------------------------------
            MyBase.LastError = strMsg
            Select Case True
                Case strMsg = ""
                    Return NodeContents.ActionButton.ErrorLevel.None
                Case Else
                    Return NodeContents.ActionButton.ErrorLevel.Fatal
            End Select
        End Function
#End Region

#Region "Valid_Delete"
        '''========================================================================================
        ''' <SUMMARY>検査(共通)</SUMMARY>
        ''' <RETURNS>True:正常, False:不正</RETURNS>
        '''========================================================================================
        Protected Overrides Function Valid_Delete_Header_Common() As NodeContents.ActionButton.ErrorLevel
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim strMsg As String = ""
            Dim Validator As New BaseCore.Common.Validator

            '------------------------------------------------------------------
            ' ｴﾗｰｸﾘｱ
            '------------------------------------------------------------------

            '------------------------------------------------------------------
            ' 共通検査
            '------------------------------------------------------------------

            '------------------------------------------------------------------
            ' 返却
            '------------------------------------------------------------------
            MyBase.LastError = strMsg
            Select Case True
                Case strMsg = ""
                    Return NodeContents.ActionButton.ErrorLevel.None
                Case Else
                    Return NodeContents.ActionButton.ErrorLevel.Fatal
            End Select
        End Function

        '''========================================================================================
        ''' <SUMMARY>検査(個別)</SUMMARY>
        ''' <RETURNS>True:正常, False:不正</RETURNS>
        '''========================================================================================
        Protected Overrides Function Valid_Delete_Header_Another() As NodeContents.ActionButton.ErrorLevel
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim strMsg As String = ""
            Dim Validator As New BaseCore.Common.Validator

            '------------------------------------------------------------------
            ' 個別検査
            '------------------------------------------------------------------

            '------------------------------------------------------------------
            ' 返却
            '------------------------------------------------------------------
            MyBase.LastError = strMsg
            Select Case True
                Case strMsg = ""
                    Return NodeContents.ActionButton.ErrorLevel.None
                Case Else
                    Return NodeContents.ActionButton.ErrorLevel.Fatal
            End Select
        End Function
#End Region

#Region "Write_Terminate"
        '''========================================================================================
        ''' <SUMMARY>後始末(書込)</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Write_Terminate() As Boolean
            '----------------------------------------------------------------------
            ' ｽﾄｱﾄﾞ実行
            '----------------------------------------------------------------------
            Try
                If (New NodeDatabase.DataSetStoredTableAdapters.P1伝票作成TableAdapter).Execute(Me.Security.セッションID, "") = 0 Then
                    If Not Me.StoredResultOK() Then
                        Return False
                    End If
                Else
                    If Not Me.StoredResultNG() Then
                        Return False
                    End If

                    Return False
                End If
            Catch ex As Exception
                Me.LastError = ex.Message
                Return False
            Finally
                Me.WorkDelete()
            End Try

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "Delete_Terminate"
        '''========================================================================================
        ''' <SUMMARY>後始末(削除)</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Delete_Terminate() As Boolean
            '----------------------------------------------------------------------
            ' ｽﾄｱﾄﾞ実行
            '----------------------------------------------------------------------
            Try
                'If (New NodeDatabase.DataSetStoredTableAdapters.P0伝票削除TableAdapter).Execute(Me.Security.セッションID) = 0 Then
                '    If Not Me.StoredResultOK() Then
                '        Return False
                '    End If
                'Else
                '    If Not Me.StoredResultNG() Then
                '        Return False
                '    End If

                '    Return False
                'End If                'If (New NodeDatabase.DataSetStoredTableAdapters.P0伝票削除TableAdapter).Execute(Me.Security.セッションID) = 0 Then
                '    If Not Me.StoredResultOK() Then
                '        Return False
                '    End If
                'Else
                '    If Not Me.StoredResultNG() Then
                '        Return False
                '    End If

                '    Return False
                'End If
            Catch ex As Exception
                Me.LastError = "以下のエラーのため、削除できませんでした。" & vbCrLf & ex.Message
                Return False
            Finally
                Me.WorkDelete()
            End Try

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "その他"
#Region "荷主情報 設定"
        '''========================================================================================
        ''' <SUMMARY>荷主情報 設定</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Public Function Read_Customer() As Boolean
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            'Dim ada As New NodeDatabase.DataSetViewTableAdapters.V0荷主TableAdapter
            'Dim tbl As New NodeDatabase.DataSetView.V0荷主DataTable
            'Dim qry As New Collection

            ''----------------------------------------------------------------------
            '' 条件設定
            ''----------------------------------------------------------------------
            'qry.Clear()
            'qry.Add(New BaseDatabase.Condition(tbl.荷主CDColumn.ColumnName, Me.Fields.Header.荷主CD.Value, BaseDatabase.Contents.Compare.Equal))

            ''----------------------------------------------------------------------
            '' 読込
            ''----------------------------------------------------------------------
            'tbl = ada.SelectByCommon(qry)
            'If tbl.Count = 0 Then
            '    Me.Fields.Header.荷主CD内部.Value = ""
            '    Me.Fields.Header.拠点CD.Value = ""
            '    Me.Fields.Header.運送会社CD.Value = ""
            'Else
            '    Me.Fields.Header.荷主CD内部.Value = tbl(0).Item(tbl.荷主CD内部Column.ColumnName)
            '    Me.Fields.Header.拠点CD.Value = tbl(0).Item(tbl.拠点CDColumn.ColumnName)
            '    Me.Fields.Header.運送会社CD.Value = tbl(0).Item(tbl.運送会社CDColumn.ColumnName)
            'End If

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "ｽﾄｱﾄﾞ結果 設定"
        '''========================================================================================
        ''' <SUMMARY>ﾜｰｸﾃｰﾌﾞﾙ(ｽﾄｱﾄﾞ結果)→ﾌﾚｰﾑﾜｰｸ</SUMMARY>   
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Public Function StoredResultOK() As Boolean
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim adaHD As New NodeDatabase.DataSetWorkTableAdapters.W伝票HDTableAdapter
            Dim tblHD As New NodeDatabase.DataSetWork.W伝票HDDataTable
            Dim qryHD As New Collection

            '----------------------------------------------------------------------
            ' T伝票HD
            '----------------------------------------------------------------------
            qryHD.Clear()
            qryHD.Add(New BaseDatabase.Condition(tblHD.セッションIDColumn.ColumnName, Me.Security.セッションID, BaseDatabase.Contents.Compare.Equal))
            tblHD = adaHD.SelectByCommon(qryHD)

            If tblHD.Count = 0 Then
                Me.Fields.Header.伝票番号.Value = ""
                Me.LastError = "伝票の保存に失敗しました。"
                Return False
            Else
                Me.Fields.Header.伝票番号.Value = tblHD(0).Item(tblHD.伝票番号Column.ColumnName)
            End If

            '------------------------------------------------------------------
            ' 返却
            '------------------------------------------------------------------
            Return True
        End Function

        '''========================================================================================
        ''' <SUMMARY>ﾜｰｸﾃｰﾌﾞﾙ(ｽﾄｱﾄﾞ結果)→ﾌﾚｰﾑﾜｰｸ</SUMMARY>   
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Public Function StoredResultNG() As Boolean
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim adaHD As New NodeDatabase.DataSetWorkTableAdapters.W伝票HDTableAdapter
            Dim tblHD As New NodeDatabase.DataSetWork.W伝票HDDataTable
            Dim rowHD As NodeDatabase.DataSetWork.W伝票HDRow
            Dim qryHD As New Collection

            Dim adaDT As New NodeDatabase.DataSetWorkTableAdapters.W伝票DTTableAdapter
            Dim tblDT As New NodeDatabase.DataSetWork.W伝票DTDataTable
            Dim rowDT As NodeDatabase.DataSetWork.W伝票DTRow
            Dim qryDT As New Collection

            '----------------------------------------------------------------------
            ' T伝票HD
            '----------------------------------------------------------------------
            qryHD.Clear()
            qryHD.Add(New BaseDatabase.Condition(tblHD.セッションIDColumn.ColumnName, Me.Security.セッションID, BaseDatabase.Contents.Compare.Equal))
            tblHD = adaHD.SelectByCommon(qryHD)
            rowHD = tblHD.Rows(0)

            Me.LastError = rowHD.ErrMessage

            If rowHD.Err伝票タイプ = "1" Then Me.Fields.Header.伝票タイプ.IsError = True
            If rowHD.Err伝票番号 = "1" Then Me.Fields.Header.伝票番号.IsError = True
            If rowHD.Err入力日付 = "1" Then Me.Fields.Header.入力日付.IsError = True
            If rowHD.Err伝票日付 = "1" Then Me.Fields.Header.伝票日付.IsError = True
            If rowHD.Err伝票区分 = "1" Then Me.Fields.Header.伝票区分.IsError = True
            If rowHD.Err緊急区分 = "1" Then Me.Fields.Header.緊急区分.IsError = True
            If rowHD.Err指示番号 = "1" Then Me.Fields.Header.指示番号.IsError = True
            If rowHD.Err指示区分 = "1" Then Me.Fields.Header.指示区分.IsError = True
            If rowHD.Err指示状況 = "1" Then Me.Fields.Header.指示状況.IsError = True
            If rowHD.Err指示者名 = "1" Then Me.Fields.Header.指示者名.IsError = True
            If rowHD.Err荷主CD内部 = "1" Then Me.Fields.Header.荷主CD内部.IsError = True
            If rowHD.Err拠点CD = "1" Then Me.Fields.Header.拠点CD.IsError = True
            If rowHD.Err企業CD = "1" Then Me.Fields.Header.企業CD.IsError = True
            If rowHD.Err届先CD = "1" Then Me.Fields.Header.届先CD.IsError = True
            If rowHD.Err届先名1 = "1" Then Me.Fields.Header.届先名1.IsError = True
            If rowHD.Err届先名2 = "1" Then Me.Fields.Header.届先名2.IsError = True
            If rowHD.Err届先郵便番号 = "1" Then Me.Fields.Header.届先郵便番号.IsError = True
            If rowHD.Err届先住所1 = "1" Then Me.Fields.Header.届先住所1.IsError = True
            If rowHD.Err届先住所2 = "1" Then Me.Fields.Header.届先住所2.IsError = True
            If rowHD.Err届先TEL = "1" Then Me.Fields.Header.届先TEL.IsError = True
            If rowHD.Err摘要CD1 = "1" Then Me.Fields.Header.摘要CD1.IsError = True
            If rowHD.Err摘要名1 = "1" Then Me.Fields.Header.摘要名1.IsError = True
            If rowHD.Err摘要CD2 = "1" Then Me.Fields.Header.摘要CD2.IsError = True
            If rowHD.Err摘要名2 = "1" Then Me.Fields.Header.摘要名2.IsError = True
            If rowHD.Err運送会社CD = "1" Then Me.Fields.Header.運送会社CD.IsError = True
            If rowHD.Err配達方法 = "1" Then Me.Fields.Header.配達方法.IsError = True
            If rowHD.Err配達日付 = "1" Then Me.Fields.Header.配達日付.IsError = True
            If rowHD.Err配達時刻区分 = "1" Then Me.Fields.Header.配達時刻区分.IsError = True
            If rowHD.Err代引金額 = "1" Then Me.Fields.Header.代引金額.IsError = True
            If rowHD.Err送り状区分 = "1" Then Me.Fields.Header.送り状区分.IsError = True
            If rowHD.Err送り状NO = "1" Then Me.Fields.Header.送り状NO.IsError = True
            If rowHD.Err車番 = "1" Then Me.Fields.Header.車番.IsError = True
            If rowHD.Err便 = "1" Then Me.Fields.Header.便.IsError = True
            If rowHD.Err運送扱数 = "1" Then Me.Fields.Header.運送扱数.IsError = True
            If rowHD.Err運送単価 = "1" Then Me.Fields.Header.運送単価.IsError = True
            If rowHD.Err運送金額 = "1" Then Me.Fields.Header.運送金額.IsError = True
            If rowHD.Err注文ショップCD = "1" Then Me.Fields.Header.注文ショップCD.IsError = True
            If rowHD.Err注文NO = "1" Then Me.Fields.Header.注文NO.IsError = True
            If rowHD.Err注文日付 = "1" Then Me.Fields.Header.注文日付.IsError = True
            If rowHD.Err注文名1 = "1" Then Me.Fields.Header.注文名1.IsError = True
            If rowHD.Err注文名2 = "1" Then Me.Fields.Header.注文名2.IsError = True
            If rowHD.Err注文会社名 = "1" Then Me.Fields.Header.注文会社名.IsError = True
            If rowHD.Err注文部署名 = "1" Then Me.Fields.Header.注文部署名.IsError = True
            If rowHD.Err注文郵便番号 = "1" Then Me.Fields.Header.注文郵便番号.IsError = True
            If rowHD.Err注文住所1 = "1" Then Me.Fields.Header.注文住所1.IsError = True
            If rowHD.Err注文住所2 = "1" Then Me.Fields.Header.注文住所2.IsError = True
            If rowHD.Err注文TEL = "1" Then Me.Fields.Header.注文TEL.IsError = True
            If rowHD.Err注文メールアドレス = "1" Then Me.Fields.Header.注文メールアドレス.IsError = True
            If rowHD.Err注文支払区分 = "1" Then Me.Fields.Header.注文支払区分.IsError = True
            If rowHD.Err注文入金状況 = "1" Then Me.Fields.Header.注文入金状況.IsError = True
            If rowHD.Err注文ギフト包装区分 = "1" Then Me.Fields.Header.注文ギフト包装区分.IsError = True
            If rowHD.Err注文ギフトメッセージ = "1" Then Me.Fields.Header.注文ギフトメッセージ.IsError = True
            If rowHD.Err注文のし表書き区分 = "1" Then Me.Fields.Header.注文のし表書き区分.IsError = True
            If rowHD.Err注文のし名入れ = "1" Then Me.Fields.Header.注文のし名入れ.IsError = True
            If rowHD.Err注文品代 = "1" Then Me.Fields.Header.注文品代.IsError = True
            If rowHD.Err注文軽減税 = "1" Then Me.Fields.Header.注文軽減税.IsError = True
            If rowHD.Err注文消費税 = "1" Then Me.Fields.Header.注文消費税.IsError = True
            If rowHD.Err注文送料 = "1" Then Me.Fields.Header.注文送料.IsError = True
            If rowHD.Err注文手数料 = "1" Then Me.Fields.Header.注文手数料.IsError = True
            If rowHD.Err注文ギフト包装代 = "1" Then Me.Fields.Header.注文ギフト包装代.IsError = True
            If rowHD.Err注文値引額 = "1" Then Me.Fields.Header.注文値引額.IsError = True
            If rowHD.Err注文利用ポイント = "1" Then Me.Fields.Header.注文利用ポイント.IsError = True
            If rowHD.Err注文請求金額 = "1" Then Me.Fields.Header.注文請求金額.IsError = True
            If rowHD.Err取込日時 = "1" Then Me.Fields.Header.取込日時.IsError = True
            If rowHD.Err登録日時 = "1" Then Me.Fields.Header.登録日時.IsError = True
            If rowHD.Err更新日時 = "1" Then Me.Fields.Header.更新日時.IsError = True
            If rowHD.Err登録者ID = "1" Then Me.Fields.Header.登録者ID.IsError = True
            If rowHD.Err登録者名 = "1" Then Me.Fields.Header.登録者名.IsError = True
            If rowHD.Err更新者ID = "1" Then Me.Fields.Header.更新者ID.IsError = True
            If rowHD.Err更新者名 = "1" Then Me.Fields.Header.更新者名.IsError = True
            If rowHD.Err承認者ID = "1" Then Me.Fields.Header.承認者ID.IsError = True
            If rowHD.Err承認者名 = "1" Then Me.Fields.Header.承認者名.IsError = True

            If rowHD.Err荷主CD = "1" Then Me.Fields.Header.荷主CD.IsError = True

            '----------------------------------------------------------------------
            ' T伝票DT
            '----------------------------------------------------------------------
            qryDT.Clear()
            qryDT.Add(New BaseDatabase.Condition(tblDT.セッションIDColumn.ColumnName, Me.Security.セッションID, BaseDatabase.Contents.Compare.Equal))
            qryDT.Add(New BaseDatabase.Condition(tblDT.Err全体Column.ColumnName, "1", BaseDatabase.Contents.Compare.Equal))
            tblDT = adaDT.SelectByCommon(qryDT)

            For Each rowDT In tblDT.Rows
                If rowDT.Err伝票タイプ = "1" Then Me.Fields.Detail1(rowDT.行番号 - 1).伝票タイプ.IsError = True
                If rowDT.Err伝票番号 = "1" Then Me.Fields.Detail1(rowDT.行番号 - 1).伝票番号.IsError = True
                If rowDT.Err行番号 = "1" Then Me.Fields.Detail1(rowDT.行番号 - 1).行番号.IsError = True
                If rowDT.Err伝票区分 = "1" Then Me.Fields.Detail1(rowDT.行番号 - 1).伝票区分.IsError = True
                If rowDT.Err荷主CD内部 = "1" Then Me.Fields.Detail1(rowDT.行番号 - 1).荷主CD内部.IsError = True
                If rowDT.Err部門CD = "1" Then Me.Fields.Detail1(rowDT.行番号 - 1).部門CD.IsError = True
                If rowDT.Err商品CD内部 = "1" Then Me.Fields.Detail1(rowDT.行番号 - 1).商品CD内部.IsError = True
                If rowDT.Err商品CD = "1" Then Me.Fields.Detail1(rowDT.行番号 - 1).商品CD.IsError = True
                If rowDT.Err規格CD1 = "1" Then Me.Fields.Detail1(rowDT.行番号 - 1).規格CD1.IsError = True
                If rowDT.Err規格CD2 = "1" Then Me.Fields.Detail1(rowDT.行番号 - 1).規格CD2.IsError = True
                If rowDT.Err規格CD3 = "1" Then Me.Fields.Detail1(rowDT.行番号 - 1).規格CD3.IsError = True
                If rowDT.Err規格CD4 = "1" Then Me.Fields.Detail1(rowDT.行番号 - 1).規格CD4.IsError = True
                If rowDT.Err規格CD5 = "1" Then Me.Fields.Detail1(rowDT.行番号 - 1).規格CD5.IsError = True
                If rowDT.Err先方商品NO = "1" Then Me.Fields.Detail1(rowDT.行番号 - 1).先方商品NO.IsError = True
                If rowDT.ErrJANCD = "1" Then Me.Fields.Detail1(rowDT.行番号 - 1).JANCD.IsError = True
                If rowDT.Err商品名 = "1" Then Me.Fields.Detail1(rowDT.行番号 - 1).商品名.IsError = True
                If rowDT.Err先方商品名 = "1" Then Me.Fields.Detail1(rowDT.行番号 - 1).先方商品名.IsError = True
                If rowDT.Err棚番CD = "1" Then Me.Fields.Detail1(rowDT.行番号 - 1).棚番CD.IsError = True
                If rowDT.Err荷姿区分 = "1" Then Me.Fields.Detail1(rowDT.行番号 - 1).荷姿区分.IsError = True
                If rowDT.Err数量 = "1" Then Me.Fields.Detail1(rowDT.行番号 - 1).数量.IsError = True
                If rowDT.Err入数 = "1" Then Me.Fields.Detail1(rowDT.行番号 - 1).入数.IsError = True
                If rowDT.Err総数 = "1" Then Me.Fields.Detail1(rowDT.行番号 - 1).総数.IsError = True
                If rowDT.Err単価 = "1" Then Me.Fields.Detail1(rowDT.行番号 - 1).単価.IsError = True
                If rowDT.Err金額 = "1" Then Me.Fields.Detail1(rowDT.行番号 - 1).金額.IsError = True
                If rowDT.Errメモ = "1" Then Me.Fields.Detail1(rowDT.行番号 - 1).メモ.IsError = True
            Next

            '------------------------------------------------------------------
            ' 返却
            '------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "一括ﾜｰｸ書込"
        '''========================================================================================
        ''' <SUMMARY>一括ﾜｰｸ書込</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Public Function Bulk_Write() As Boolean
            '----------------------------------------------------------------------
            ' ﾜｰｸ書込
            '----------------------------------------------------------------------
            If Not Me.Bulk_Write_Header() Then
                Return True
            End If

            If Not Me.Bulk_Write_Detail1() Then
                Return True
            End If

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function

#Region "一括ﾜｰｸ書込HD"
        '''========================================================================================
        ''' <SUMMARY>書込</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Private Function Bulk_Write_Header() As Boolean
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim strNow As String = Format(Now, "yyyy/MM/dd HH:mm:ss")

            Dim ada As New NodeDatabase.DataSetWorkTableAdapters.W伝票HD一括TableAdapter
            Dim tbl As New NodeDatabase.DataSetWork.W伝票HD一括DataTable
            Dim qry As New Collection

            '----------------------------------------------------------------------
            ' 事前準備
            '----------------------------------------------------------------------
            Me.Fields.Header.更新日時.Value = strNow
            Me.Fields.Header.更新者ID.Value = Me.Security.ユーザーCD
            Me.Fields.Header.更新者名.Value = Me.Security.ユーザー名

            If Me.Fields.Header.登録者ID.Value = "" Then
                Me.Fields.Header.登録者ID.Value = Me.Fields.Header.更新者ID.Value
                Me.Fields.Header.登録者名.Value = Me.Fields.Header.更新者名.Value
            End If

            '----------------------------------------------------------------------
            ' 保存
            '----------------------------------------------------------------------
            Try
                ada.Insert(Me.Security.セッションID _
                         , Me.Fields.Header.伝票タイプ.Value _
                         , Me.Fields.Header.伝票番号.Value _
                         , Me.Fields.Header.入力日付.Value _
                         , Me.Fields.Header.伝票日付.Value _
                         , Me.Fields.Header.伝票区分.Value _
                         , Me.Fields.Header.緊急区分.Value _
                         , Me.Fields.Header.指示番号.Value _
                         , Me.Fields.Header.指示区分.Value _
                         , Me.Fields.Header.指示状況.Value _
                         , Me.Fields.Header.指示者名.Value _
                         , Me.Fields.Header.荷主CD内部.Value _
                         , Me.Fields.Header.拠点CD.Value _
                         , Me.Fields.Header.企業CD.Value _
                         , Me.Fields.Header.届先CD.Value _
                         , Me.Fields.Header.届先名1.Value _
                         , Me.Fields.Header.届先名2.Value _
                         , Me.Fields.Header.届先郵便番号.Value _
                         , Me.Fields.Header.届先住所1.Value _
                         , Me.Fields.Header.届先住所2.Value _
                         , Me.Fields.Header.届先TEL.Value _
                         , Me.Fields.Header.摘要CD1.Value _
                         , Me.Fields.Header.摘要名1.Value _
                         , Me.Fields.Header.摘要CD2.Value _
                         , Me.Fields.Header.摘要名2.Value _
                         , Me.Fields.Header.運送会社CD.Value _
                         , Me.Fields.Header.配達方法.Value _
                         , Me.Fields.Header.配達日付.Value _
                         , Me.Fields.Header.配達時刻区分.Value _
                         , BaseCore.Common.Text.CVal(Me.Fields.Header.代引金額.Value) _
                         , Me.Fields.Header.送り状区分.Value _
                         , Me.Fields.Header.送り状NO.Value _
                         , Me.Fields.Header.車番.Value _
                         , BaseCore.Common.Text.CVal(Me.Fields.Header.便.Value) _
                         , BaseCore.Common.Text.CVal(Me.Fields.Header.運送扱数.Value) _
                         , BaseCore.Common.Text.CVal(Me.Fields.Header.運送単価.Value) _
                         , BaseCore.Common.Text.CVal(Me.Fields.Header.運送金額.Value) _
                         , Me.Fields.Header.注文ショップCD.Value _
                         , Me.Fields.Header.注文NO.Value _
                         , Me.Fields.Header.注文日付.Value _
                         , Me.Fields.Header.注文名1.Value _
                         , Me.Fields.Header.注文名2.Value _
                         , Me.Fields.Header.注文会社名.Value _
                         , Me.Fields.Header.注文部署名.Value _
                         , Me.Fields.Header.注文郵便番号.Value _
                         , Me.Fields.Header.注文住所1.Value _
                         , Me.Fields.Header.注文住所2.Value _
                         , Me.Fields.Header.注文TEL.Value _
                         , Me.Fields.Header.注文メールアドレス.Value _
                         , Me.Fields.Header.注文支払区分.Value _
                         , Me.Fields.Header.注文入金状況.Value _
                         , Me.Fields.Header.注文ギフト包装区分.Value _
                         , Me.Fields.Header.注文ギフトメッセージ.Value _
                         , Me.Fields.Header.注文のし表書き区分.Value _
                         , Me.Fields.Header.注文のし名入れ.Value _
                         , BaseCore.Common.Text.CVal(Me.Fields.Header.注文品代.Value) _
                         , BaseCore.Common.Text.CVal(Me.Fields.Header.注文軽減税.Value) _
                         , BaseCore.Common.Text.CVal(Me.Fields.Header.注文消費税.Value) _
                         , BaseCore.Common.Text.CVal(Me.Fields.Header.注文送料.Value) _
                         , BaseCore.Common.Text.CVal(Me.Fields.Header.注文手数料.Value) _
                         , BaseCore.Common.Text.CVal(Me.Fields.Header.注文ギフト包装代.Value) _
                         , BaseCore.Common.Text.CVal(Me.Fields.Header.注文値引額.Value) _
                         , BaseCore.Common.Text.CVal(Me.Fields.Header.注文利用ポイント.Value) _
                         , BaseCore.Common.Text.CVal(Me.Fields.Header.注文請求金額.Value) _
                         , Me.Fields.Header.取込日時.Value _
                         , Me.Fields.Header.登録日時.Value _
                         , Me.Fields.Header.更新日時.Value _
                         , Me.Fields.Header.登録者ID.Value _
                         , Me.Fields.Header.登録者名.Value _
                         , Me.Fields.Header.更新者ID.Value _
                         , Me.Fields.Header.更新者名.Value _
                         , Me.Fields.Header.承認者ID.Value _
                         , Me.Fields.Header.承認者名.Value _
                         , Me.Fields.Header.公開日付.Value _
                         , Me.Fields.Header.所属CD.Value _
                         , Me.Fields.Header.検品番号.Value _
                         , Me.Fields.Header.営業店止め区分.Value _
                         , Me.Fields.Header.営業店名.Value _
                         , Me.Fields.Header.荷主CD.Value _
                         , BaseCore.Common.Text.CVal(Me.Fields.Header.仮番.Value) _
                         , BaseCore.Common.Text.CVal(Me.Fields.Header.位置.Value) _
                         , "", "", "", "", "", "", "", "", "", "" _
                         , "", "", "", "", "", "", "", "", "", "" _
                         , "", "", "", "", "", "", "", "", "", "" _
                         , "", "", "", "", "", "", "", "", "", "" _
                         , "", "", "", "", "", "", "", "", "", "" _
                         , "", "", "", "", "", "", "", "", "", "" _
                         , "", "", "", "", "", "", "", "", "", "" _
                         , "", "", "", "", "", "", "", "", ""
                          )
            Catch ex As Exception
                Me.LastError = "以下のエラーのため、処理を継続できません。" & vbCrLf & ex.Message
                Return False
            End Try

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "一括ﾜｰｸ書込DT"
        '''========================================================================================
        ''' <SUMMARY>書込</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Private Function Bulk_Write_Detail1() As Boolean
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetWorkTableAdapters.W伝票DT一括TableAdapter
            Dim qry As New Collection

            '----------------------------------------------------------------------
            ' ﾜｰｸ書込
            '----------------------------------------------------------------------
            For i As Integer = 0 To Me.Fields.Detail1.Count - 1
                If Not Me.Empty_Detail1(Me.Fields.Detail1(i)) Then
                    Me.RowCount = Me.RowCount + 1

                    Try
                        ada.Insert(Me.Security.セッションID _
                                 , Me.Fields.Header.伝票タイプ.Value _
                                 , Me.Fields.Header.伝票番号.Value _
                                 , BaseCore.Common.Text.CVal(Me.Fields.Detail1(i).行番号.Value) _
                                 , Me.Fields.Header.伝票区分.Value _
                                 , Me.Fields.Header.荷主CD内部.Value _
                                 , Me.Fields.Detail1(i).部門CD.Value _
                                 , Me.Fields.Detail1(i).商品CD内部.Value _
                                 , Me.Fields.Detail1(i).商品CD.Value _
                                 , Me.Fields.Detail1(i).規格CD1.Value _
                                 , Me.Fields.Detail1(i).規格CD2.Value _
                                 , Me.Fields.Detail1(i).規格CD3.Value _
                                 , Me.Fields.Detail1(i).規格CD4.Value _
                                 , Me.Fields.Detail1(i).規格CD5.Value _
                                 , Me.Fields.Detail1(i).ロットNO.Value _
                                 , Me.Fields.Detail1(i).ロット日付.Value _
                                 , Me.Fields.Detail1(i).先方商品NO.Value _
                                 , Me.Fields.Detail1(i).JANCD.Value _
                                 , Me.Fields.Detail1(i).商品名.Value _
                                 , Me.Fields.Detail1(i).先方商品名.Value _
                                 , Me.Fields.Detail1(i).棚番CD.Value _
                                 , Me.Fields.Detail1(i).荷姿区分.Value _
                                 , BaseCore.Common.Text.CVal(Me.Fields.Detail1(i).数量.Value) _
                                 , BaseCore.Common.Text.CVal(Me.Fields.Detail1(i).入数.Value) _
                                 , BaseCore.Common.Text.CVal(Me.Fields.Detail1(i).総数.Value) _
                                 , BaseCore.Common.Text.CVal(Me.Fields.Detail1(i).単価.Value) _
                                 , BaseCore.Common.Text.CVal(Me.Fields.Detail1(i).金額.Value) _
                                 , Me.Fields.Detail1(i).メモ.Value _
                                 , BaseCore.Common.Text.CVal(Me.Fields.Detail1(i).仮番.Value) _
                                 , BaseCore.Common.Text.CVal(Me.Fields.Detail1(i).位置.Value) _
                                 , "", "", "", "", "", "", "", "", "", "" _
                                 , "", "", "", "", "", "", "", "", "", "" _
                                 , "", "", "", "", "", "", "", "", "", "" _
                                 , ""
                                  )
                    Catch ex As Exception
                        Me.LastError = "以下のエラーのため、処理を継続できません。" & vbCrLf & ex.Message
                        Return False
                    End Try
                End If
            Next

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region
#End Region

#Region "一括ｽﾄｱﾄﾞ実行"
        '''========================================================================================
        ''' <SUMMARY>ﾜｰｸﾃｰﾌﾞﾙ(ｽﾄｱﾄﾞ結果)→ﾌﾚｰﾑﾜｰｸ</SUMMARY>   
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Public Function Bulk_Excute_Stored() As Boolean
            '----------------------------------------------------------------------
            ' ｽﾄｱﾄﾞ実行
            '----------------------------------------------------------------------
            'ﾄﾗﾝｻﾞｸｼｮﾝはｽﾄｱﾄﾞで実行
            If (New NodeDatabase.DataSetStoredTableAdapters.P1伝票作成一括TableAdapter).Execute(Me.Security.セッションID) = 0 Then
                If Not Me.Bulk_StoredResultOK() Then
                    Return False
                End If
            Else
                If Not Me.Bulk_StoredResultNG() Then
                    Return False
                End If

                Return False
            End If

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "一括ｽﾄｱﾄﾞ結果 設定"
        '''========================================================================================
        ''' <SUMMARY>ﾜｰｸﾃｰﾌﾞﾙ(ｽﾄｱﾄﾞ結果)→ﾌﾚｰﾑﾜｰｸ</SUMMARY>   
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Public Function Bulk_StoredResultOK() As Boolean
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim adaHD As New NodeDatabase.DataSetWorkTableAdapters.W伝票HD一括TableAdapter
            Dim tblHD As New NodeDatabase.DataSetWork.W伝票HD一括DataTable
            Dim qryHD As New Collection

            '----------------------------------------------------------------------
            ' T伝票HD
            '----------------------------------------------------------------------
            qryHD.Clear()
            qryHD.Add(New BaseDatabase.Condition(tblHD.セッションIDColumn.ColumnName, Me.Security.セッションID, BaseDatabase.Contents.Compare.Equal))
            tblHD = adaHD.SelectByCommon(qryHD)

            If tblHD.Count = 0 Then
                Me.LastError = "伝票の保存に失敗しました。"
                Return False
            End If

            '------------------------------------------------------------------
            ' 返却
            '------------------------------------------------------------------
            Return True
        End Function

        '''========================================================================================
        ''' <SUMMARY>ﾜｰｸﾃｰﾌﾞﾙ(ｽﾄｱﾄﾞ結果)→ﾌﾚｰﾑﾜｰｸ</SUMMARY>   
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Public Function Bulk_StoredResultNG() As Boolean
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim adaHD As New NodeDatabase.DataSetWorkTableAdapters.W伝票HD一括TableAdapter
            Dim tblHD As New NodeDatabase.DataSetWork.W伝票HD一括DataTable
            Dim qryHD As New Collection

            '----------------------------------------------------------------------
            ' T伝票HD
            '----------------------------------------------------------------------
            qryHD.Clear()
            qryHD.Add(New BaseDatabase.Condition(tblHD.セッションIDColumn.ColumnName, Me.Security.セッションID, BaseDatabase.Contents.Compare.Equal))
            qryHD.Add(New BaseDatabase.Condition(tblHD.Msg全体Column.ColumnName, "", BaseDatabase.Contents.Compare.NotEqual))
            tblHD = adaHD.SelectByCommon(qryHD)

            For Each row As NodeDatabase.DataSetWork.W伝票HD一括Row In tblHD.Rows
                Me.LastError &= row.Item(tblHD.Msg全体Column.ColumnName)
            Next

            '------------------------------------------------------------------
            ' 返却
            '------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "ﾜｰｸﾃｰﾌﾞﾙ削除"
        '''========================================================================================
        ''' <SUMMARY>ﾜｰｸﾃーﾌﾞﾙ削除</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Public Function WorkDelete() As Boolean
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim adaHD As New NodeDatabase.DataSetWorkTableAdapters.W伝票HDTableAdapter
            Dim tblHD As New NodeDatabase.DataSetWork.W伝票HDDataTable
            Dim qryHD As New Collection

            Dim adaDT As New NodeDatabase.DataSetWorkTableAdapters.W伝票DTTableAdapter
            Dim tblDT As New NodeDatabase.DataSetWork.W伝票DTDataTable
            Dim qryDT As New Collection

            '----------------------------------------------------------------------
            ' 事前削除
            '----------------------------------------------------------------------
            qryHD.Clear()
            qryHD.Add(New BaseDatabase.Condition(tblHD.セッションIDColumn.ColumnName, Me.Security.セッションID, BaseDatabase.Contents.Compare.Equal))

            Try
                adaHD.DeleteByCommon(qryHD)
            Catch ex As Exception
                Me.LastError = ex.Message
                Return False
            End Try

            qryDT.Clear()
            qryDT.Add(New BaseDatabase.Condition(tblDT.セッションIDColumn.ColumnName, Me.Security.セッションID, BaseDatabase.Contents.Compare.Equal))

            Try
                adaDT.DeleteByCommon(qryDT)
            Catch ex As Exception
                Me.LastError = ex.Message
                Return False
            End Try

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region
#End Region
#End Region

#Region "明細"
#Region "Clear"
        '''========================================================================================
        ''' <SUMMARY>ｸﾘｱ</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Clear_Detail1() As Boolean
            '----------------------------------------------------------------------
            ' ｸﾘｱ
            '----------------------------------------------------------------------
            For i As Integer = 0 To Me.Fields.Detail1.Count - 1
                Me.Clear_Detail1(i)
            Next

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function

        Public Overrides Function Clear_Detail1(ByVal Index As Integer) As Boolean
            Me.Fields.Detail1(Index).伝票タイプ.Value = Nothing
            Me.Fields.Detail1(Index).伝票番号.Value = Nothing
            Me.Fields.Detail1(Index).行番号.Value = Index + 1
            Me.Fields.Detail1(Index).伝票区分.Value = Nothing
            Me.Fields.Detail1(Index).荷主CD内部.Value = Nothing
            Me.Fields.Detail1(Index).部門CD.Value = Nothing
            Me.Fields.Detail1(Index).商品CD内部.Value = Nothing
            Me.Fields.Detail1(Index).商品CD.Value = Nothing
            Me.Fields.Detail1(Index).規格CD1.Value = Nothing
            Me.Fields.Detail1(Index).規格CD2.Value = Nothing
            Me.Fields.Detail1(Index).規格CD3.Value = Nothing
            Me.Fields.Detail1(Index).規格CD4.Value = Nothing
            Me.Fields.Detail1(Index).規格CD5.Value = Nothing
            Me.Fields.Detail1(Index).ロットNO.Value = Nothing
            Me.Fields.Detail1(Index).ロット日付.Value = Nothing
            Me.Fields.Detail1(Index).先方商品NO.Value = Nothing
            Me.Fields.Detail1(Index).JANCD.Value = Nothing
            Me.Fields.Detail1(Index).商品名.Value = Nothing
            Me.Fields.Detail1(Index).先方商品名.Value = Nothing
            Me.Fields.Detail1(Index).棚番CD.Value = Nothing
            Me.Fields.Detail1(Index).荷姿区分.Value = Nothing
            Me.Fields.Detail1(Index).数量.Value = Nothing
            Me.Fields.Detail1(Index).入数.Value = Nothing
            Me.Fields.Detail1(Index).総数.Value = Nothing
            Me.Fields.Detail1(Index).単価.Value = Nothing
            Me.Fields.Detail1(Index).金額.Value = Nothing
            Me.Fields.Detail1(Index).メモ.Value = Nothing
            Me.Fields.Detail1(Index).依頼取込フラグ.Value = Nothing
            Me.Fields.Detail1(Index).依頼削除フラグ.Value = Nothing
            Me.Fields.Detail1(Index).削除.Value = 0
            Me.Fields.Detail1(Index).括りCD.Value = Nothing
            Me.Fields.Detail1(Index).総入数.Value = Nothing
            Me.Fields.Detail1(Index).仮番.Value = Nothing
            Me.Fields.Detail1(Index).位置.Value = Nothing
        End Function
#End Region

#Region "Read"
        '''========================================================================================
        ''' <SUMMARY>読込</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Read_Detail1() As Boolean
            '----------------------------------------------------------------------
            ' 読み込み
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetViewTableAdapters.V0伝票DTTableAdapter
            Dim tbl As New NodeDatabase.DataSetView.V0伝票DTDataTable
            Dim qry As New Collection

            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl.伝票タイプColumn.ColumnName, Me.Fields.Header.伝票タイプ.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.伝票番号Column.ColumnName, Me.Fields.Header.伝票番号.Value, BaseDatabase.Contents.Compare.Equal))

            Dim strSort As String = ""
            Dim strDelim As String = ""

            strSort &= strDelim & tbl.行番号Column.ColumnName & " ASC" : strDelim = ","

            tbl = ada.SelectByCommon(qry, strSort)

            '----------------------------------------------------------------------
            ' ﾌﾚｰﾑ作成
            '----------------------------------------------------------------------
            Me.FrameMaker_Detail1(tbl)

            '----------------------------------------------------------------------
            ' 配車読込
            '----------------------------------------------------------------------

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "Write"
        '''========================================================================================
        ''' <SUMMARY>書込</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Write_Detail1() As Boolean
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetWorkTableAdapters.W伝票DTTableAdapter
            Dim qry As New Collection

            '----------------------------------------------------------------------
            ' ﾜｰｸ書込
            '----------------------------------------------------------------------
            For i As Integer = 0 To Me.Fields.Detail1.Count - 1
                If Not Me.Empty_Detail1(Me.Fields.Detail1(i)) Then
                    Me.RowCount = Me.RowCount + 1

                    Try
                        ada.Insert(Me.Security.セッションID _
                                 , Me.Fields.Header.伝票タイプ.Value _
                                 , Me.Fields.Header.伝票番号.Value _
                                 , BaseCore.Common.Text.CVal(Me.Fields.Detail1(i).行番号.Value) _
                                 , Me.Fields.Header.伝票区分.Value _
                                 , Me.Fields.Header.荷主CD内部.Value _
                                 , Me.Fields.Detail1(i).部門CD.Value _
                                 , Me.Fields.Detail1(i).商品CD内部.Value _
                                 , Me.Fields.Detail1(i).商品CD.Value _
                                 , Me.Fields.Detail1(i).規格CD1.Value _
                                 , Me.Fields.Detail1(i).規格CD2.Value _
                                 , Me.Fields.Detail1(i).規格CD3.Value _
                                 , Me.Fields.Detail1(i).規格CD4.Value _
                                 , Me.Fields.Detail1(i).規格CD5.Value _
                                 , Me.Fields.Detail1(i).ロットNO.Value _
                                 , Me.Fields.Detail1(i).ロット日付.Value _
                                 , Me.Fields.Detail1(i).先方商品NO.Value _
                                 , Me.Fields.Detail1(i).JANCD.Value _
                                 , Me.Fields.Detail1(i).商品名.Value _
                                 , Me.Fields.Detail1(i).先方商品名.Value _
                                 , Me.Fields.Detail1(i).棚番CD.Value _
                                 , Me.Fields.Detail1(i).荷姿区分.Value _
                                 , BaseCore.Common.Text.CVal(Me.Fields.Detail1(i).数量.Value) _
                                 , BaseCore.Common.Text.CVal(Me.Fields.Detail1(i).入数.Value) _
                                 , BaseCore.Common.Text.CVal(Me.Fields.Detail1(i).総数.Value) _
                                 , BaseCore.Common.Text.CVal(Me.Fields.Detail1(i).単価.Value) _
                                 , BaseCore.Common.Text.CVal(Me.Fields.Detail1(i).金額.Value) _
                                 , Me.Fields.Detail1(i).メモ.Value _
                                 , Me.Fields.Detail1(i).依頼取込フラグ.Value _
                                 , Me.Fields.Detail1(i).依頼削除フラグ.Value _
                                 , Me.Fields.Detail1(i).括りCD.Value _
                                 , "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""
                                  )

                    Catch ex As Exception
                        Me.LastError = "以下のエラーのため、処理を継続できません。" & vbCrLf & ex.Message
                        Return False
                    End Try
                End If
            Next

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "Delete"
        '''========================================================================================
        ''' <SUMMARY>削除</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Delete_Detail1() As Boolean
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetWorkTableAdapters.W伝票DTTableAdapter
            Dim tbl As New NodeDatabase.DataSetWork.W伝票DTDataTable
            Dim qry As New Collection

            '----------------------------------------------------------------------
            ' 削除条件
            '----------------------------------------------------------------------
            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl.セッションIDColumn.ColumnName, Me.Security.セッションID, BaseDatabase.Contents.Compare.Equal))

            '----------------------------------------------------------------------
            ' 削除
            '----------------------------------------------------------------------
            Try
                Call ada.DeleteByCommon(qry)

            Catch ex As Exception
                MyBase.LastError = "以下のエラーのため、削除できませんでした。" & vbCrLf & ex.Message
                Return False
            End Try

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "Valid_Write"
        '''========================================================================================
        ''' <summary>検査</summary>
        ''' <RETURNS>True:正常, False:不正</RETURNS>
        '''========================================================================================
        Protected Overrides Function Valid_Write_Detail1_Common() As NodeContents.ActionButton.ErrorLevel
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim strMsg As String = ""
            Dim Validator As New BaseCore.Common.Validator

            '------------------------------------------------------------------
            ' ｴﾗｰｸﾘｱ
            '------------------------------------------------------------------
            For i As Integer = 0 To Me.Fields.Detail1.Count - 1
                Me.Fields.Detail1(i).伝票タイプ.IsError = False
                Me.Fields.Detail1(i).伝票番号.IsError = False
                Me.Fields.Detail1(i).行番号.IsError = False
                Me.Fields.Detail1(i).伝票区分.IsError = False
                Me.Fields.Detail1(i).荷主CD内部.IsError = False
                Me.Fields.Detail1(i).部門CD.IsError = False
                Me.Fields.Detail1(i).商品CD内部.IsError = False
                Me.Fields.Detail1(i).商品CD.IsError = False
                Me.Fields.Detail1(i).規格CD1.IsError = False
                Me.Fields.Detail1(i).規格CD2.IsError = False
                Me.Fields.Detail1(i).規格CD3.IsError = False
                Me.Fields.Detail1(i).規格CD4.IsError = False
                Me.Fields.Detail1(i).規格CD5.IsError = False
                Me.Fields.Detail1(i).ロットNO.IsError = False
                Me.Fields.Detail1(i).ロット日付.IsError = False
                Me.Fields.Detail1(i).先方商品NO.IsError = False
                Me.Fields.Detail1(i).JANCD.IsError = False
                Me.Fields.Detail1(i).商品名.IsError = False
                Me.Fields.Detail1(i).先方商品名.IsError = False
                Me.Fields.Detail1(i).棚番CD.IsError = False
                Me.Fields.Detail1(i).荷姿区分.IsError = False
                Me.Fields.Detail1(i).数量.IsError = False
                Me.Fields.Detail1(i).入数.IsError = False
                Me.Fields.Detail1(i).総数.IsError = False
                Me.Fields.Detail1(i).単価.IsError = False
                Me.Fields.Detail1(i).金額.IsError = False
                Me.Fields.Detail1(i).メモ.IsError = False
                Me.Fields.Detail1(i).依頼取込フラグ.IsError = False
                Me.Fields.Detail1(i).依頼削除フラグ.IsError = False

                Me.Fields.Detail1(i).削除.IsError = False
                Me.Fields.Detail1(i).括りCD.IsError = False
                Me.Fields.Detail1(i).総入数.IsError = False
            Next

            '------------------------------------------------------------------
            ' 共通検査
            '------------------------------------------------------------------
            For i As Integer = 0 To Me.Fields.Detail1.Count - 1
                If Not Me.Empty_Detail1(Me.Fields.Detail1(i)) Then
                    If Not Validator.BaseChecker(Me.Fields.Detail1(i).伝票タイプ) Then : Me.Fields.Detail1(i).伝票タイプ.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                    If Not Validator.BaseChecker(Me.Fields.Detail1(i).伝票番号) Then : Me.Fields.Detail1(i).伝票番号.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                    If Not Validator.BaseChecker(Me.Fields.Detail1(i).行番号) Then : Me.Fields.Detail1(i).行番号.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                    If Not Validator.BaseChecker(Me.Fields.Detail1(i).伝票区分) Then : Me.Fields.Detail1(i).伝票区分.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                    If Not Validator.BaseChecker(Me.Fields.Detail1(i).荷主CD内部) Then : Me.Fields.Detail1(i).荷主CD内部.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                    If Not Validator.BaseChecker(Me.Fields.Detail1(i).部門CD) Then : Me.Fields.Detail1(i).部門CD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                    If Not Validator.BaseChecker(Me.Fields.Detail1(i).商品CD内部) Then : Me.Fields.Detail1(i).商品CD内部.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                    If Not Validator.BaseChecker(Me.Fields.Detail1(i).商品CD) Then : Me.Fields.Detail1(i).商品CD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                    If Not Validator.BaseChecker(Me.Fields.Detail1(i).規格CD1) Then : Me.Fields.Detail1(i).規格CD1.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                    If Not Validator.BaseChecker(Me.Fields.Detail1(i).規格CD2) Then : Me.Fields.Detail1(i).規格CD2.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                    If Not Validator.BaseChecker(Me.Fields.Detail1(i).規格CD3) Then : Me.Fields.Detail1(i).規格CD3.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                    If Not Validator.BaseChecker(Me.Fields.Detail1(i).規格CD4) Then : Me.Fields.Detail1(i).規格CD4.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                    If Not Validator.BaseChecker(Me.Fields.Detail1(i).規格CD5) Then : Me.Fields.Detail1(i).規格CD5.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                    If Not Validator.BaseChecker(Me.Fields.Detail1(i).ロットNO) Then : Me.Fields.Detail1(i).ロットNO.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                    If Not Validator.BaseChecker(Me.Fields.Detail1(i).ロット日付) Then : Me.Fields.Detail1(i).ロット日付.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                    If Not Validator.BaseChecker(Me.Fields.Detail1(i).先方商品NO) Then : Me.Fields.Detail1(i).先方商品NO.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                    If Not Validator.BaseChecker(Me.Fields.Detail1(i).JANCD) Then : Me.Fields.Detail1(i).JANCD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                    If Not Validator.BaseChecker(Me.Fields.Detail1(i).商品名) Then : Me.Fields.Detail1(i).商品名.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                    If Not Validator.BaseChecker(Me.Fields.Detail1(i).先方商品名) Then : Me.Fields.Detail1(i).先方商品名.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                    If Not Validator.BaseChecker(Me.Fields.Detail1(i).棚番CD) Then : Me.Fields.Detail1(i).棚番CD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                    If Not Validator.BaseChecker(Me.Fields.Detail1(i).荷姿区分) Then : Me.Fields.Detail1(i).荷姿区分.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                    If Not Validator.BaseChecker(Me.Fields.Detail1(i).数量) Then : Me.Fields.Detail1(i).数量.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                    If Not Validator.BaseChecker(Me.Fields.Detail1(i).入数) Then : Me.Fields.Detail1(i).入数.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                    If Not Validator.BaseChecker(Me.Fields.Detail1(i).総数) Then : Me.Fields.Detail1(i).総数.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                    If Not Validator.BaseChecker(Me.Fields.Detail1(i).単価) Then : Me.Fields.Detail1(i).単価.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                    If Not Validator.BaseChecker(Me.Fields.Detail1(i).金額) Then : Me.Fields.Detail1(i).金額.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                    If Not Validator.BaseChecker(Me.Fields.Detail1(i).メモ) Then : Me.Fields.Detail1(i).メモ.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                    If Not Validator.BaseChecker(Me.Fields.Detail1(i).依頼取込フラグ) Then : Me.Fields.Detail1(i).依頼取込フラグ.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                    If Not Validator.BaseChecker(Me.Fields.Detail1(i).依頼削除フラグ) Then : Me.Fields.Detail1(i).依頼削除フラグ.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If

                    If Not Validator.BaseChecker(Me.Fields.Detail1(i).削除) Then : Me.Fields.Detail1(i).削除.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                    If Not Validator.BaseChecker(Me.Fields.Detail1(i).括りCD) Then : Me.Fields.Detail1(i).括りCD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                    If Not Validator.BaseChecker(Me.Fields.Detail1(i).総入数) Then : Me.Fields.Detail1(i).総入数.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
                End If
            Next

            '------------------------------------------------------------------
            ' 返却
            '------------------------------------------------------------------
            MyBase.LastError = strMsg
            Select Case True
                Case strMsg = ""
                    Return NodeContents.ActionButton.ErrorLevel.None
                Case Else
                    Return NodeContents.ActionButton.ErrorLevel.Fatal
            End Select
        End Function

        '''========================================================================================
        ''' <SUMMARY>検査(個別)</SUMMARY>
        ''' <RETURNS>True:正常, False:不正</RETURNS>
        '''========================================================================================
        Protected Overrides Function Valid_Write_Detail1_Another() As NodeContents.ActionButton.ErrorLevel
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim strMsg As String = ""
            Dim Validator As New BaseCore.Common.Validator

            '------------------------------------------------------------------
            ' 返却
            '------------------------------------------------------------------
            MyBase.LastError = strMsg
            Select Case True
                Case strMsg = ""
                    Return NodeContents.ActionButton.ErrorLevel.None
                Case Else
                    Return NodeContents.ActionButton.ErrorLevel.Fatal
            End Select
        End Function
#End Region

#Region "Valid_Delete"
        '''========================================================================================
        ''' <SUMMARY>検査(共通)</SUMMARY>
        ''' <RETURNS>True:正常, False:不正</RETURNS>
        '''========================================================================================
        Protected Overrides Function Valid_Delete_Detail1_Common() As NodeContents.ActionButton.ErrorLevel
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim strMsg As String = ""
            Dim Validator As New BaseCore.Common.Validator

            '------------------------------------------------------------------
            ' ｴﾗｰｸﾘｱ
            '------------------------------------------------------------------

            '------------------------------------------------------------------
            ' 共通検査
            '------------------------------------------------------------------

            '------------------------------------------------------------------
            ' 返却
            '------------------------------------------------------------------
            MyBase.LastError = strMsg
            Select Case True
                Case strMsg = ""
                    Return NodeContents.ActionButton.ErrorLevel.None
                Case Else
                    Return NodeContents.ActionButton.ErrorLevel.Fatal
            End Select
        End Function

        '''========================================================================================
        ''' <SUMMARY>検査(個別)</SUMMARY>
        ''' <RETURNS>True:正常, False:不正</RETURNS>
        '''========================================================================================
        Protected Overrides Function Valid_Delete_Detail1_Another() As NodeContents.ActionButton.ErrorLevel
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim strMsg As String = ""
            Dim Validator As New BaseCore.Common.Validator

            '------------------------------------------------------------------
            ' 返却
            '------------------------------------------------------------------
            MyBase.LastError = strMsg
            Select Case True
                Case strMsg = ""
                    Return NodeContents.ActionButton.ErrorLevel.None
                Case Else
                    Return NodeContents.ActionButton.ErrorLevel.Fatal
            End Select
        End Function
#End Region

#Region "ﾌﾚｰﾑ関連"
#Region "ﾌﾚｰﾑ作成"
        '''========================================================================================
        ''' <SUMMARY>ﾌﾚｰﾑ作成</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Function FrameMaker_Detail1(ByVal tbl As NodeDatabase.DataSetView.V0伝票DTDataTable) As Boolean
            '----------------------------------------------------------------------
            ' ﾌﾚｰﾑ生成
            '----------------------------------------------------------------------
            Me.Add_Detail1(tbl.Count)

            '----------------------------------------------------------------------
            ' 値設定
            '----------------------------------------------------------------------
            For i As Integer = 0 To tbl.Rows.Count - 1
                Me.Fields.Detail1(i).伝票タイプ.Value = tbl(i).Item(tbl.伝票タイプColumn.ColumnName)
                Me.Fields.Detail1(i).伝票番号.Value = tbl(i).Item(tbl.伝票番号Column.ColumnName)
                Me.Fields.Detail1(i).行番号.Value = i + 1
                Me.Fields.Detail1(i).伝票区分.Value = tbl(i).Item(tbl.伝票区分Column.ColumnName)
                Me.Fields.Detail1(i).荷主CD内部.Value = tbl(i).Item(tbl.荷主CD内部Column.ColumnName)
                Me.Fields.Detail1(i).部門CD.Value = tbl(i).Item(tbl.部門CDColumn.ColumnName)
                Me.Fields.Detail1(i).商品CD内部.Value = tbl(i).Item(tbl.商品CD内部Column.ColumnName)
                Me.Fields.Detail1(i).商品CD.Value = tbl(i).Item(tbl.商品CDColumn.ColumnName)
                Me.Fields.Detail1(i).規格CD1.Value = tbl(i).Item(tbl.規格CD1Column.ColumnName)
                Me.Fields.Detail1(i).規格CD2.Value = tbl(i).Item(tbl.規格CD2Column.ColumnName)
                Me.Fields.Detail1(i).規格CD3.Value = tbl(i).Item(tbl.規格CD3Column.ColumnName)
                Me.Fields.Detail1(i).規格CD4.Value = tbl(i).Item(tbl.規格CD4Column.ColumnName)
                Me.Fields.Detail1(i).規格CD5.Value = tbl(i).Item(tbl.規格CD5Column.ColumnName)
                Me.Fields.Detail1(i).ロットNO.Value = tbl(i).Item(tbl.ロットNOColumn.ColumnName)
                Me.Fields.Detail1(i).ロット日付.Value = tbl(i).Item(tbl.ロット日付Column.ColumnName)
                Me.Fields.Detail1(i).先方商品NO.Value = tbl(i).Item(tbl.先方商品NOColumn.ColumnName)
                Me.Fields.Detail1(i).JANCD.Value = tbl(i).Item(tbl.JANCDColumn.ColumnName)
                Me.Fields.Detail1(i).商品名.Value = tbl(i).Item(tbl.商品名Column.ColumnName)
                Me.Fields.Detail1(i).先方商品名.Value = tbl(i).Item(tbl.先方商品名Column.ColumnName)
                Me.Fields.Detail1(i).棚番CD.Value = tbl(i).Item(tbl.棚番CDColumn.ColumnName)
                Me.Fields.Detail1(i).荷姿区分.Value = tbl(i).Item(tbl.荷姿区分Column.ColumnName)
                Me.Fields.Detail1(i).数量.Value = tbl(i).Item(tbl.数量Column.ColumnName)
                'Me.Fields.Detail1(i).入数.Value = NodeCore.Common.Logic.FormatQty(tbl(i).Item(tbl.入数Column.ColumnName))
                'Me.Fields.Detail1(i).総数.Value = NodeCore.Common.Logic.FormatQty(tbl(i).Item(tbl.総数Column.ColumnName))
                Me.Fields.Detail1(i).単価.Value = tbl(i).Item(tbl.単価Column.ColumnName)
                Me.Fields.Detail1(i).金額.Value = tbl(i).Item(tbl.金額Column.ColumnName)
                Me.Fields.Detail1(i).メモ.Value = tbl(i).Item(tbl.メモColumn.ColumnName)
                Me.Fields.Detail1(i).依頼取込フラグ.Value = tbl(i).Item(tbl.依頼取込フラグColumn.ColumnName)
                Me.Fields.Detail1(i).依頼削除フラグ.Value = tbl(i).Item(tbl.依頼削除フラグColumn.ColumnName)

                Me.Fields.Detail1(i).削除.Value = 0
                'Me.Fields.Detail1(i).括りCD.Value = tbl(i).Item(tbl.商品_括りCDColumn.ColumnName)
                'Me.Fields.Detail1(i).総入数.Value = NodeCore.Common.Logic.FormatQty(tbl(i).Item(tbl.商品_総入数Column.ColumnName))
            Next

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "行挿入"
        '''========================================================================================
        ''' <summary>単一行挿入</summary>
        '''========================================================================================
        Public Overrides Sub Insert_Detail1(ByVal Position As Integer)
            '----------------------------------------------------------------------
            ' 行挿入
            '----------------------------------------------------------------------
            Me.Fields.Detail1.Insert(Position, New SlipInputBase.Detail1(Me.Config))

            '----------------------------------------------------------------------
            ' 行ｸﾘｱ
            '----------------------------------------------------------------------
            Me.Clear_Detail1(Position)

            '----------------------------------------------------------------------
            ' 行番号の再付番
            '----------------------------------------------------------------------
            Me.ReNum_Detail1()
        End Sub

        '''========================================================================================
        ''' <summary>複数行挿入</summary>
        '''========================================================================================
        Public Overrides Sub Insert_Detail1(ByVal Position As Integer, ByVal Count As Integer)
            '----------------------------------------------------------------------
            ' 行挿入 & 行ｸﾘｱ
            '----------------------------------------------------------------------
            For i As Integer = Position To Position + Count - 1
                Me.Fields.Detail1.Insert(Position, New SlipInputBase.Detail1(Me.Config))
                Me.Clear_Detail1(Position)
            Next

            '----------------------------------------------------------------------
            ' 行番号の再付番
            '----------------------------------------------------------------------
            Me.ReNum_Detail1()
        End Sub
#End Region

#Region "行複写"
        '''========================================================================================
        ''' <summary>単一行複写</summary>
        '''========================================================================================
        Public Overrides Sub Copy_Detail1(ByVal Position As Integer)
            '----------------------------------------------------------------------
            ' 行挿入
            '----------------------------------------------------------------------
            Me.Fields.Detail1.Insert(Position, New SlipInputBase.Detail1(Me.Config))

            '----------------------------------------------------------------------
            ' 行ｺﾋﾟｰ
            '----------------------------------------------------------------------
            Me.CopyValue_Detail1(Position + 1, Position)

            '----------------------------------------------------------------------
            ' 行番号の再付番
            '----------------------------------------------------------------------
            Me.ReNum_Detail1()
        End Sub

        '''========================================================================================
        ''' <summary>値ｺﾋﾟｰ</summary>
        '''========================================================================================
        Protected Function CopyValue_Detail1(ByVal IndexF As Integer, ByVal IndexT As Integer) As Boolean
            Me.Fields.Detail1(IndexT).伝票タイプ.Value = Me.Fields.Detail1(IndexF).伝票タイプ.Value
            Me.Fields.Detail1(IndexT).伝票番号.Value = Me.Fields.Detail1(IndexF).伝票番号.Value
            Me.Fields.Detail1(IndexT).行番号.Value = Me.Fields.Detail1(IndexF).行番号.Value
            Me.Fields.Detail1(IndexT).伝票区分.Value = Me.Fields.Detail1(IndexF).伝票区分.Value
            Me.Fields.Detail1(IndexT).荷主CD内部.Value = Me.Fields.Detail1(IndexF).荷主CD内部.Value
            Me.Fields.Detail1(IndexT).部門CD.Value = Me.Fields.Detail1(IndexF).部門CD.Value
            Me.Fields.Detail1(IndexT).商品CD内部.Value = Me.Fields.Detail1(IndexF).商品CD内部.Value
            Me.Fields.Detail1(IndexT).商品CD.Value = Me.Fields.Detail1(IndexF).商品CD.Value
            Me.Fields.Detail1(IndexT).規格CD1.Value = Me.Fields.Detail1(IndexF).規格CD1.Value
            Me.Fields.Detail1(IndexT).規格CD2.Value = Me.Fields.Detail1(IndexF).規格CD2.Value
            Me.Fields.Detail1(IndexT).規格CD3.Value = Me.Fields.Detail1(IndexF).規格CD3.Value
            Me.Fields.Detail1(IndexT).規格CD4.Value = Me.Fields.Detail1(IndexF).規格CD4.Value
            Me.Fields.Detail1(IndexT).規格CD5.Value = Me.Fields.Detail1(IndexF).規格CD5.Value
            Me.Fields.Detail1(IndexT).ロットNO.Value = Me.Fields.Detail1(IndexF).ロットNO.Value
            Me.Fields.Detail1(IndexT).ロット日付.Value = Me.Fields.Detail1(IndexF).ロット日付.Value
            Me.Fields.Detail1(IndexT).先方商品NO.Value = Me.Fields.Detail1(IndexF).先方商品NO.Value
            Me.Fields.Detail1(IndexT).JANCD.Value = Me.Fields.Detail1(IndexF).JANCD.Value
            Me.Fields.Detail1(IndexT).商品名.Value = Me.Fields.Detail1(IndexF).商品名.Value
            Me.Fields.Detail1(IndexT).先方商品名.Value = Me.Fields.Detail1(IndexF).先方商品名.Value
            Me.Fields.Detail1(IndexT).棚番CD.Value = Me.Fields.Detail1(IndexF).棚番CD.Value
            Me.Fields.Detail1(IndexT).荷姿区分.Value = Me.Fields.Detail1(IndexF).荷姿区分.Value
            Me.Fields.Detail1(IndexT).数量.Value = Me.Fields.Detail1(IndexF).数量.Value
            Me.Fields.Detail1(IndexT).入数.Value = Me.Fields.Detail1(IndexF).入数.Value
            Me.Fields.Detail1(IndexT).総数.Value = Me.Fields.Detail1(IndexF).総数.Value
            Me.Fields.Detail1(IndexT).単価.Value = Me.Fields.Detail1(IndexF).単価.Value
            Me.Fields.Detail1(IndexT).金額.Value = Me.Fields.Detail1(IndexF).金額.Value
            Me.Fields.Detail1(IndexT).メモ.Value = Me.Fields.Detail1(IndexF).メモ.Value
            Me.Fields.Detail1(IndexT).依頼取込フラグ.Value = Me.Fields.Detail1(IndexF).依頼取込フラグ.Value
            Me.Fields.Detail1(IndexT).依頼削除フラグ.Value = Me.Fields.Detail1(IndexF).依頼削除フラグ.Value

            Me.Fields.Detail1(IndexT).削除.Value = Me.Fields.Detail1(IndexF).削除.Value
            Me.Fields.Detail1(IndexT).括りCD.Value = Me.Fields.Detail1(IndexF).括りCD.Value
            Me.Fields.Detail1(IndexT).総入数.Value = Me.Fields.Detail1(IndexF).総入数.Value
        End Function
#End Region

#Region "行追加"
        '''========================================================================================
        ''' <summary>単一行追加</summary>
        '''========================================================================================
        Public Overrides Sub Add_Detail1()
            Me.Fields.Detail1.Add(New SlipInputBase.Detail1(Me.Config))
            Me.Clear_Detail1(Me.Fields.Detail1.Count - 1)
        End Sub

        '''========================================================================================
        ''' <summary>一括行追加</summary>
        ''' <param name="AddCount">追加行数</param>
        '''========================================================================================
        Public Overrides Sub Add_Detail1(ByVal AddCount As Integer)
            Me.Fields.Detail1.Clear()

            For i As Integer = 0 To AddCount - 1
                Me.Add_Detail1()
            Next
        End Sub
#End Region

#Region "行削除"
        '''========================================================================================
        ''' <summary>一括行削除</summary>
        '''========================================================================================
        Public Overrides Sub Del_Detail1()
            Me.Fields.Detail1.Clear()
        End Sub
#End Region

#Region "行番号の再付番"
        '''========================================================================================
        ''' <summary>行番号の再付番</summary>
        '''========================================================================================
        Public Overrides Sub ReNum_Detail1()
            For i As Integer = 0 To Me.Fields.Detail1.Count - 1
                Me.Fields.Detail1(i).行番号.Value = i + 1
            Next
        End Sub
#End Region
#End Region

#Region "無効行判定"
        '''========================================================================================
        ''' <summary>無効行の判定</summary>
        ''' <param name="Detail">明細行</param>
        ''' <returns>True:無効行, False:有効行</returns>
        '''========================================================================================
        Protected Overrides Function Empty_Detail1(ByVal Detail As Object) As Boolean
            '------------------------------------------------------------------
            ' 変数定義
            '------------------------------------------------------------------
            Dim row As SlipInputBase.Detail1 = CType(Detail, SlipInputBase.Detail1)

            '------------------------------------------------------------------
            ' 入力有は有効行
            '------------------------------------------------------------------
            'Select Case Me.Fields.Header.伝票タイプ.Value
            '    Case NodeContents.Constant.CodeValue.伝票タイプ.諸掛
            '        Return False

            '    Case Else
            '        Select Case True
            '            Case row.削除.Value = "1" : Return True
            '            Case Not BaseCore.Common.Text.IsEmptyOrZero(row.商品CD.Value) : Return False
            '            Case Not BaseCore.Common.Text.IsEmptyOrZero(row.先方商品NO.Value) : Return False
            '        End Select
            'End Select

            '------------------------------------------------------------------
            ' 無効行
            '------------------------------------------------------------------
            Return True
        End Function
#End Region
#End Region
    End Class
End Namespace
