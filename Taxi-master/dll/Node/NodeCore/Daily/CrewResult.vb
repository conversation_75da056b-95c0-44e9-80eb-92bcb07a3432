﻿Namespace Frame.Daily
    Partial Public Class CrewResult
        Inherits NodeCore.Common.Frame

#Region "ﾌｨｰﾙﾄﾞ定義"
        Public Class Header
            ''' ----------------------------------------------------------------------------------------
            ''' <SUMMARY>列項目(基本)</SUMMARY>
            ''' ----------------------------------------------------------------------------------------
            Public グループCD As BaseCore.Common.Field.ItemData
            Public 個社CD As BaseCore.Common.Field.ItemData
            Public 子会社CD As BaseCore.Common.Field.ItemData
            Public 日報日付F As BaseCore.Common.Field.ItemData
            Public 日報日付T As BaseCore.Common.Field.ItemData
            Public データ種別 As BaseCore.Common.Field.ItemData
            Public 選択一覧 As ArrayList

            '''========================================================================================
            ''' <SUMMARY>ｺﾝｽﾄﾗｸﾀ</SUMMARY>
            '''========================================================================================
            Public Sub New(ByVal Config As NodeCore.Common.Config)
                Dim tbl As New NodeDatabase.DataSetView.V0乗務記録HDDataTable

                '----------------------------------------------------------------------
                ' ｲﾝｽﾀﾝｽ(基本) 
                '----------------------------------------------------------------------
                Me.グループCD = New BaseCore.Common.Field.ItemData(tbl.グループCDColumn)
                Me.個社CD = New BaseCore.Common.Field.ItemData(tbl.個社CDColumn)
                Me.子会社CD = New BaseCore.Common.Field.ItemData(tbl.子会社CDColumn)
                Me.日報日付F = New BaseCore.Common.Field.ItemData(tbl.日報日付Column)
                Me.日報日付T = New BaseCore.Common.Field.ItemData(tbl.日報日付Column)
                Me.データ種別 = New BaseCore.Common.Field.ItemData("optMode", 1)
                'Me.データ種別.Value = "0"
                Me.選択一覧 = New ArrayList

            End Sub
        End Class
#End Region

#Region "変数定義"
        Public Class Paramater
            Public Header As Header
        End Class

        '''========================================================================================
        ''' <summary>ﾍｯﾀﾞ</summary>
        '''========================================================================================
        Private _Fields As New Paramater
        Public Property Fields() As Paramater
            Get
                Return _Fields
            End Get

            Set(ByVal value As Paramater)
                _Fields = value
            End Set
        End Property
#End Region

#Region "ｺﾝｽﾄﾗｸﾀ"
        '''========================================================================================
        ''' <SUMMARY>ｺﾝｽﾄﾗｸﾀ</SUMMARY>
        ''' <PARAM name="Security">ｾｷｭﾘﾃｨ ｸﾗｽ</PARAM>
        ''' <PARAM name="Config">ｺﾝﾌｨｸﾞ ｸﾗｽ</PARAM>
        ''' <PARAM name="PagePath">ﾍﾟｰｼﾞﾊﾟｽ</PARAM>
        '''========================================================================================
        Public Sub New(ByVal Security As NodeCore.Common.Security, ByVal Config As NodeCore.Common.Config, ByVal PagePath As String)
            '----------------------------------------------------------------------
            ' ｾｷｭﾘﾃｨ 設定
            '----------------------------------------------------------------------
            MyBase.New(Security, Config, PagePath)

            Me.Fields.Header = New Header(Config)
        End Sub
#End Region

#Region "ﾍｯﾀﾞ"
#Region "Clear"
        '''========================================================================================
        ''' <SUMMARY>ｸﾘｱ</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Clear_Header(Optional ByVal KeyClear As Boolean = False) As Boolean
            '----------------------------------------------------------------------
            ' ｷｰ部ｸﾘｱ
            '----------------------------------------------------------------------
            If KeyClear Then
                Me.Fields.Header.グループCD.Value = Nothing
                Me.Fields.Header.個社CD.Value = Nothing
                Me.Fields.Header.子会社CD.Value = Nothing
            End If

            '----------------------------------------------------------------------
            ' ﾃﾞｰﾀ部ｸﾘｱ
            '----------------------------------------------------------------------

            Me.Fields.Header.日報日付F.Value = Nothing
            Me.Fields.Header.日報日付T.Value = Nothing
            Me.Fields.Header.データ種別.Value = 0

            '----------------------------------------------------------------------
            ' ﾃﾞｰﾀ部ｸﾘｱ
            '----------------------------------------------------------------------
            'Me.Read()

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "MakeDataTable"
        '''========================================================================================
        ''' <SUMMARY>ﾃﾞｰﾀﾃｰﾌﾞﾙ生成</SUMMARY>
        ''' <RETURNS>ﾃﾞｰﾀﾃｰﾌﾞﾙ</RETURNS>
        '''========================================================================================
        Public Function MakeDataTable() As DataTable
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetFuncTableAdapters.F0乗務記録実績TableAdapter
            Dim tbl As New NodeDatabase.DataSetFunc.F0乗務記録実績DataTable
            Dim qry As New Collection

            '----------------------------------------------------------------------
            ' 事前準備
            '----------------------------------------------------------------------

            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, Me.Security.グループCD, BaseDatabase.Contents.Compare.Parameter))
            qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, Me.Security.個社CD, BaseDatabase.Contents.Compare.Parameter))
            qry.Add(New BaseDatabase.Condition(tbl.子会社CDColumn.ColumnName, Me.Security.子会社CD, BaseDatabase.Contents.Compare.Parameter))
            qry.Add(New BaseDatabase.Condition("日付F", Me.Fields.Header.日報日付F.Value, BaseDatabase.Contents.Compare.Parameter))
            qry.Add(New BaseDatabase.Condition("日付T", Me.Fields.Header.日報日付T.Value, BaseDatabase.Contents.Compare.Parameter))
            qry.Add(New BaseDatabase.Condition("データフラグ", Me.Fields.Header.データ種別.Value, BaseDatabase.Contents.Compare.Parameter))

            tbl = ada.SelectByCommon(qry)


            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return tbl
        End Function
#End Region

#Region "Execute1:Excel"
        '''========================================================================================
        ''' <SUMMARY>実行</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Execute1_Header() As Boolean
            '----------------------------------------------------------------------
            ' ﾃﾞｰﾀ抽出
            '----------------------------------------------------------------------
            Dim tbl As NodeDatabase.DataSetFunc.F0乗務記録実績DataTable = Me.MakeDataTable()


            '----------------------------------------------------------------------
            ' 必要ｶﾗﾑ以外を削除
            '----------------------------------------------------------------------
            Dim colRefresh As New BaseCore.Common.DataTable.ColumnRefresh

            colRefresh.Add(tbl.取込日付Column.ColumnName, "取込日付")
            colRefresh.Add(tbl.乗務記録NOColumn.ColumnName, "乗務記録NO")
            colRefresh.Add(tbl.日報日付Column.ColumnName, "日報日付")
            colRefresh.Add(tbl.車両番号Column.ColumnName, "車両番号")
            colRefresh.Add(tbl.車種CDColumn.ColumnName, "車種CD")
            colRefresh.Add(tbl.乗務員CDColumn.ColumnName, "乗務員CD")
            colRefresh.Add(tbl.乗務員名Column.ColumnName, "乗務員名")
            colRefresh.Add(tbl.勤務区分Column.ColumnName, "勤務区分")
            colRefresh.Add(tbl.輸送人員Column.ColumnName, "輸送人員")
            colRefresh.Add(tbl.走行kmColumn.ColumnName, "走行km")
            colRefresh.Add(tbl.営業kmColumn.ColumnName, "営業km")
            colRefresh.Add(tbl.実車回数Column.ColumnName, "実車回数")
            colRefresh.Add(tbl.爾後回数Column.ColumnName, "爾後回数")
            colRefresh.Add(tbl.迎車回数Column.ColumnName, "迎車回数")
            colRefresh.Add(tbl.予約回数Column.ColumnName, "予約回数")
            colRefresh.Add(tbl.実車金額Column.ColumnName, "実車金額")
            colRefresh.Add(tbl.爾後金額Column.ColumnName, "爾後金額")
            colRefresh.Add(tbl.迎車金額Column.ColumnName, "迎車金額")
            colRefresh.Add(tbl.予約金額Column.ColumnName, "予約金額")
            colRefresh.Add(tbl.金額計Column.ColumnName, "金額計")
            colRefresh.Add(tbl.遠割額Column.ColumnName, "遠割額")
            colRefresh.Add(tbl.定額差額AColumn.ColumnName, "定額差額A")
            colRefresh.Add(tbl.空転AColumn.ColumnName, "空転A")
            colRefresh.Add(tbl.納金Column.ColumnName, "納金")
            colRefresh.Add(tbl.TAXColumn.ColumnName, "TAX")
            colRefresh.Add(tbl.営収Column.ColumnName, "営収")
            colRefresh.Add(tbl.チケットAColumn.ColumnName, "チケットA")
            colRefresh.Add(tbl.クーポン四社AColumn.ColumnName, "クーポン四社A")
            colRefresh.Add(tbl.クーポン東旅協AColumn.ColumnName, "クーポン東旅協A")
            colRefresh.Add(tbl.福祉券AColumn.ColumnName, "福祉券A")
            colRefresh.Add(tbl.カードAColumn.ColumnName, "カードA")
            colRefresh.Add(tbl.iDAColumn.ColumnName, "iDA")
            colRefresh.Add(tbl.交通系ICAColumn.ColumnName, "交通系ICA")
            colRefresh.Add(tbl.キャブカードAColumn.ColumnName, "キャブカードA")
            colRefresh.Add(tbl.プリペイドカードAColumn.ColumnName, "プリペイドカードA")
            colRefresh.Add(tbl.WAONAColumn.ColumnName, "WAONA")
            colRefresh.Add(tbl.その他AColumn.ColumnName, "その他A")
            colRefresh.Add(tbl.障割AColumn.ColumnName, "障割A")
            colRefresh.Add(tbl.現金AColumn.ColumnName, "現金A")
            colRefresh.Add(tbl.ETC実車AColumn.ColumnName, "ETC実車A")
            colRefresh.Add(tbl.ETC乗務員AColumn.ColumnName, "ETC乗務員A")
            colRefresh.Add(tbl.ETC会社AColumn.ColumnName, "ETC会社A")
            colRefresh.Add(tbl.出庫時刻Column.ColumnName, "出庫時刻")
            colRefresh.Add(tbl.帰庫時刻Column.ColumnName, "帰庫時刻")
            colRefresh.Add(tbl.総勤務時間Column.ColumnName, "総勤務時間")
            colRefresh.Add(tbl.総休憩時間Column.ColumnName, "総休憩時間")
            colRefresh.Add(tbl.総回送時間Column.ColumnName, "総回送時間")
            colRefresh.Add(tbl.総空車停止時間Column.ColumnName, "総空車停止時間")
            colRefresh.Add(tbl.信販控除除外Column.ColumnName, "信販控除除外")
            colRefresh.Execute(tbl)

            '----------------------------------------------------------------------
            ' ﾃﾞｰﾀ加工(前)
            '----------------------------------------------------------------------
            tbl.TableName = "乗務記録実績一覧"

            '----------------------------------------------------------------------
            ' ｴｸｽﾎﾟｰﾄ
            '----------------------------------------------------------------------
            Dim Excel As New BaseCore.Common.Excel
            If Not Excel.ExcelExport(tbl, Me.Path1) Then
                Me.LastError = Excel.LastError
                Return False
            End If

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "Execute:Delete"
        '''========================================================================================
        ''' <SUMMARY>実行</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Execute_Header() As Boolean

            Dim strNow As String = Format(Now, "yyyy/MM/dd HH:mm:ss")
            Dim ada As New NodeDatabase.DataSetTranTableAdapters.T乗務記録HDTableAdapter
            Dim tbl As New NodeDatabase.DataSetTran.T乗務記録HDDataTable
            Dim qry As New Collection
            '----------------------------------------------------------------------
            ' 削除処理
            '----------------------------------------------------------------------
            If Me.CheckMark1 Is Nothing Or Not Me.CheckMark1.Count > 0 Then
                Me.LastError = "削除するデータを選択してください。"
                Return False
            End If

            'If Not Me.CheckMark1.Count > 0 Then
            '    Me.LastError = "削除するデータを選択してください。"
            '    Return False
            'End If

            For Each Key As String In Me.CheckMark1
                '----------------------------------------------------------------------
                ' 訂正
                '----------------------------------------------------------------------
                If Key.Split(vbTab).Length > 8 Then
                    Dim strValues As String() = Key.Split(vbTab)

                    Dim strGroup As String = strValues(0)
                    Dim strKosya As String = strValues(1)
                    Dim strKoKaisya As String = strValues(2)
                    Dim strNippo As String = strValues(3)
                    Dim strStartTime As String = strValues(4)
                    Dim strEndTime As String = strValues(5)
                    Dim strJyoumuInCd As String = strValues(6)
                    Dim strSyaryouBangou As String = strValues(7)
                    Dim strKijiNo As String = strValues(8)

                    'qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, strGroup, BaseDatabase.Contents.Compare.Equal))
                    'qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, strKosya, BaseDatabase.Contents.Compare.Equal))
                    'qry.Add(New BaseDatabase.Condition(tbl.子会社CDColumn.ColumnName, strKoKaisya, BaseDatabase.Contents.Compare.Equal))
                    'qry.Add(New BaseDatabase.Condition(tbl.乗務員CDColumn.ColumnName, strJyoumuInCd, BaseDatabase.Contents.Compare.Equal))
                    'qry.Add(New BaseDatabase.Condition(tbl.車両番号Column.ColumnName, strSyaryouBangou, BaseDatabase.Contents.Compare.Equal))
                    'qry.Add(New BaseDatabase.Condition(tbl.日報日付Column.ColumnName, strNippo, BaseDatabase.Contents.Compare.Equal))
                    'qry.Add(New BaseDatabase.Condition(tbl.乗務記録NOColumn.ColumnName, strKijiNo, BaseDatabase.Contents.Compare.Equal))

                    qry.Clear()
                    qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, strGroup, BaseDatabase.Contents.Compare.Equal))
                    qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, strKosya, BaseDatabase.Contents.Compare.Equal))
                    qry.Add(New BaseDatabase.Condition(tbl.子会社CDColumn.ColumnName, strKoKaisya, BaseDatabase.Contents.Compare.Equal))
                    qry.Add(New BaseDatabase.Condition(tbl.日報日付Column.ColumnName, strNippo, BaseDatabase.Contents.Compare.Equal))
                    qry.Add(New BaseDatabase.Condition(tbl.出庫時刻Column.ColumnName, strStartTime, BaseDatabase.Contents.Compare.Equal))
                    qry.Add(New BaseDatabase.Condition(tbl.入庫時刻Column.ColumnName, strEndTime, BaseDatabase.Contents.Compare.Equal))
                    qry.Add(New BaseDatabase.Condition(tbl.乗務員CDColumn.ColumnName, strJyoumuInCd, BaseDatabase.Contents.Compare.Equal))
                    qry.Add(New BaseDatabase.Condition(tbl.車両番号Column.ColumnName, strSyaryouBangou, BaseDatabase.Contents.Compare.Equal))


                    tbl = ada.SelectByCommon(qry)
                    If tbl.Count > 0 Then
                        Try
                            tbl(0).Item(tbl.削除フラグColumn.ColumnName) = 1
                            ada.Update(tbl)

                        Catch ex As Exception
                            MyBase.LastError = "以下のエラーのため、更新できませんでした。" & vbCrLf & ex.Message
                            Return False
                        End Try
                    End If


                End If

            Next
            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#End Region


    End Class
End Namespace
