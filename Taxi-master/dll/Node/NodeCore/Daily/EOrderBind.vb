﻿Namespace Frame.Daily
    Partial Public Class EOrderBind
        Inherits NodeCore.Common.Frame

#Region "ﾌｨｰﾙﾄﾞ定義"
#Region "ﾍｯﾀﾞ"
        Public Class Header
            Public レイアウトCD As BaseCore.Common.Field.ItemData
            Public レイアウト名 As BaseCore.Common.Field.ItemData
            Public ファイル形式 As BaseCore.Common.Field.ItemData
            Public 先頭タイトル As BaseCore.Common.Field.ItemData
            Public 最終ディレクトリ As BaseCore.Common.Field.ItemData

            Public ファイル名 As BaseCore.Common.Field.ItemData

            '''========================================================================================
            ''' <SUMMARY>ｺﾝｽﾄﾗｸﾀ</SUMMARY>
            '''========================================================================================
            Public Sub New(ByVal Config As NodeCore.Common.Config)
                Dim tbl As New NodeDatabase.DataSetMaster.MレイアウトHDDataTable

                Me.レイアウトCD = New BaseCore.Common.Field.ItemData(tbl.レイアウトCDColumn)
                Me.レイアウト名 = New BaseCore.Common.Field.ItemData(tbl.レイアウト名Column)
                Me.ファイル形式 = New BaseCore.Common.Field.ItemData(tbl.ファイル形式Column)
                Me.先頭タイトル = New BaseCore.Common.Field.ItemData(tbl.先頭タイトルColumn)
                Me.最終ディレクトリ = New BaseCore.Common.Field.ItemData(tbl.最終ディレクトリColumn)

                Me.ファイル名 = New BaseCore.Common.Field.ItemData("ファイル名", TypeCode.String)

            End Sub
        End Class

        Public Class Undo
            Public 伝票タイプ As BaseCore.Common.Field.ItemData
            Public 取込日時 As BaseCore.Common.Field.ItemData

            '''========================================================================================
            ''' <SUMMARY>ｺﾝｽﾄﾗｸﾀ</SUMMARY>
            '''========================================================================================
            Public Sub New(ByVal Config As NodeCore.Common.Config)
                Dim tbl As New NodeDatabase.DataSetMaster.MレイアウトHDDataTable

                Me.伝票タイプ = New BaseCore.Common.Field.ItemData(tbl.伝票タイプColumn)
                Me.取込日時 = New BaseCore.Common.Field.ItemData(tbl.登録日時Column)
            End Sub
        End Class
#End Region

#Region "明細(ﾃﾞｰﾀ項目)"
        Public Class Detail1
            Public 行数 As BaseCore.Common.Field.ItemData
            Public 伝票項目CD As BaseCore.Common.Field.ItemData
            Public 伝票項目名 As BaseCore.Common.Field.ItemData
            Public 伝票項目名表示 As BaseCore.Common.Field.ItemData
            Public カラム位置 As BaseCore.Common.Field.ItemData
            Public キーフラグ As BaseCore.Common.Field.ItemData
            Public タイトル As BaseCore.Common.Field.ItemData
            Public 項目値 As BaseCore.Common.Field.ItemData
            Public カーソル As BaseCore.Common.Field.ItemData
            Public カーソル表示 As BaseCore.Common.Field.ItemData

            '''========================================================================================
            ''' <SUMMARY>ｺﾝｽﾄﾗｸﾀ</SUMMARY>
            '''========================================================================================
            Public Sub New(ByVal Config As NodeCore.Common.Config)
                Dim tbl As New NodeDatabase.DataSetView.V0レイアウトDataTable

                Me.行数 = New BaseCore.Common.Field.ItemData("行数", TypeCode.Int16)
                Me.伝票項目CD = New BaseCore.Common.Field.ItemData(tbl.伝票項目CDColumn.ColumnName, TypeCode.String)
                Me.伝票項目名 = New BaseCore.Common.Field.ItemData(tbl.伝票項目名Column.ColumnName, TypeCode.String)
                Me.伝票項目名表示 = New BaseCore.Common.Field.ItemData(tbl.伝票項目名表示Column.ColumnName, TypeCode.String)
                Me.カラム位置 = New BaseCore.Common.Field.ItemData(tbl.カラム位置Column.ColumnName, TypeCode.String)
                Me.キーフラグ = New BaseCore.Common.Field.ItemData(tbl.キーフラグColumn.ColumnName, TypeCode.String)
                Me.タイトル = New BaseCore.Common.Field.ItemData("タイトル", TypeCode.String)
                Me.項目値 = New BaseCore.Common.Field.ItemData("項目値", TypeCode.String)
                Me.カーソル = New BaseCore.Common.Field.ItemData("カーソル", TypeCode.String)
                Me.カーソル表示 = New BaseCore.Common.Field.ItemData("カーソル表示", TypeCode.String)
            End Sub
        End Class
#End Region

#Region "明細(伝票項目)"
        Public Class Detail2
            Public 行数 As BaseCore.Common.Field.ItemData
            Public 伝票項目CD As BaseCore.Common.Field.ItemData
            Public 伝票項目名 As BaseCore.Common.Field.ItemData
            Public 伝票項目名表示 As BaseCore.Common.Field.ItemData
            Public 表示位置 As BaseCore.Common.Field.ItemData
            Public 既定値 As BaseCore.Common.Field.ItemData
            Public キーフラグ As BaseCore.Common.Field.ItemData

            '''========================================================================================
            ''' <SUMMARY>ｺﾝｽﾄﾗｸﾀ</SUMMARY>
            '''========================================================================================
            Public Sub New(ByVal Config As NodeCore.Common.Config)
                Dim tbl As New NodeDatabase.DataSetMaster.M伝票項目DataTable
                Dim tblレイ As New NodeDatabase.DataSetMaster.MレイアウトDTDataTable

                Me.行数 = New BaseCore.Common.Field.ItemData("行数", TypeCode.Int16)
                Me.伝票項目CD = New BaseCore.Common.Field.ItemData(tbl.伝票項目CDColumn)
                Me.伝票項目名 = New BaseCore.Common.Field.ItemData(tbl.伝票項目名Column)
                Me.伝票項目名表示 = New BaseCore.Common.Field.ItemData(tbl.伝票項目名表示Column)
                Me.表示位置 = New BaseCore.Common.Field.ItemData(tbl.表示位置Column)
                Me.既定値 = New BaseCore.Common.Field.ItemData(tblレイ.既定値Column)
                Me.キーフラグ = New BaseCore.Common.Field.ItemData(tblレイ.キーフラグColumn)
            End Sub
        End Class
#End Region

#Region "明細(変換ﾏｽﾀ)"
        Public Class Detail3
            Public 行数 As BaseCore.Common.Field.ItemData
            Public 削除 As BaseCore.Common.Field.ItemData
            Public レイアウトCD As BaseCore.Common.Field.ItemData
            Public 条件カラム位置 As BaseCore.Common.Field.ItemData
            Public 条件項目値 As BaseCore.Common.Field.ItemData
            Public 符号区分 As BaseCore.Common.Field.ItemData
            Public 結果伝票項目CD As BaseCore.Common.Field.ItemData
            Public 結果項目値 As BaseCore.Common.Field.ItemData

            Public 符号区分名 As BaseCore.Common.Field.ItemData
            Public 結果伝票項目名 As BaseCore.Common.Field.ItemData

            '''========================================================================================
            ''' <SUMMARY>ｺﾝｽﾄﾗｸﾀ</SUMMARY>
            '''========================================================================================
            Public Sub New(ByVal Config As NodeCore.Common.Config)
                Dim tbl As New NodeDatabase.DataSetView.V0レイアウト条件DataTable

                Me.行数 = New BaseCore.Common.Field.ItemData("行数", TypeCode.Int16)
                Me.削除 = New BaseCore.Common.Field.ItemData("削除", TypeCode.Int16)
                Me.レイアウトCD = New BaseCore.Common.Field.ItemData(tbl.レイアウトCDColumn)
                Me.条件カラム位置 = New BaseCore.Common.Field.ItemData(tbl.条件カラム位置Column, 15, 3)
                Me.条件項目値 = New BaseCore.Common.Field.ItemData(tbl.条件項目値Column)
                Me.符号区分 = New BaseCore.Common.Field.ItemData(tbl.符号区分Column)
                Me.結果伝票項目CD = New BaseCore.Common.Field.ItemData(tbl.結果伝票項目CDColumn)
                Me.結果項目値 = New BaseCore.Common.Field.ItemData(tbl.条件項目値Column)

                Me.符号区分名 = New BaseCore.Common.Field.ItemData(tbl.符号区分名Column)
                Me.結果伝票項目名 = New BaseCore.Common.Field.ItemData(tbl.結果伝票項目名Column)
            End Sub
        End Class
#End Region
#End Region

#Region "共通"
#Region "変数定義"
        Public _SlipBase As NodeCore.Frame.Daily.SlipInputBase

        Public Class Paramater
            Public Header As Header
            Public Undo As Undo
        End Class

        '''========================================================================================
        ''' <summary>ﾍｯﾀﾞ</summary>
        '''========================================================================================
        Private _Fields As New Paramater
        Public Property Fields() As Paramater
            Get
                Return _Fields
            End Get

            Set(ByVal value As Paramater)
                _Fields = value
            End Set
        End Property
#End Region

#Region "ｺﾝｽﾄﾗｸﾀ"
        '''========================================================================================
        ''' <SUMMARY>ｺﾝｽﾄﾗｸﾀ</SUMMARY>
        ''' <PARAM name="Security">ｾｷｭﾘﾃｨ ｸﾗｽ</PARAM>
        ''' <PARAM name="Config">ｺﾝﾌｨｸﾞ ｸﾗｽ</PARAM>
        ''' <PARAM name="PagePath">ﾍﾟｰｼﾞﾊﾟｽ</PARAM>
        '''========================================================================================
        Public Sub New(ByVal Security As NodeCore.Common.Security, ByVal Config As NodeCore.Common.Config, ByVal PagePath As String)
            '----------------------------------------------------------------------
            ' ｾｷｭﾘﾃｨ 設定
            '----------------------------------------------------------------------
            MyBase.New(Security, Config, PagePath)

            Me.Fields.Header = New Header(Config)
            Me.Fields.Undo = New Undo(Config)
        End Sub
#End Region

#Region "ｺﾝｽﾀﾝﾄ"
        Public Structure ConstantExcel
            Public DUMMY As String

            Public Structure Header
                Public DUMMY As String
                Public Const SheetName = "記入表"
                Public Const RowTop = 1

                Public Structure 列数
                    Public DUMMY As String

                    Public Const 取込日付 = 1
                    Public Const 日報日付 = 2
                    Public Const 日報年月 = 3
                    Public Const 出庫時刻 = 4
                    Public Const 入庫時刻 = 5
                    Public Const 乗務員CD = 6
                    Public Const 車両番号 = 7
                    Public Const 営業所CD = 8
                    Public Const 部門CD = 9
                    Public Const 勤務CD = 10
                    Public Const 車種CD = 11
                    Public Const 天気 = 12
                    Public Const 累計全走行 = 13
                    Public Const 累計実車走行 = 14
                    Public Const 累計迎車走行 = 15
                    Public Const 累計営業回数 = 16
                    Public Const 累計以後回数 = 17
                    Public Const 累計迎車回数 = 18
                    Public Const 一日全走行 = 19
                    Public Const 一日実車走行 = 20
                    Public Const 一日迎車走行 = 21
                    Public Const 一日営業回数 = 22
                    Public Const 一日以後回数 = 23
                    Public Const 一日迎車回数 = 24
                    Public Const 累計運賃 = 25
                    Public Const 累計料金 = 26
                    Public Const 累計身割回数 = 27
                    Public Const 累計身割額 = 28
                    Public Const 累計ワゴン回数 = 29
                    Public Const 累計早朝回数 = 30
                    Public Const 累計予約回数 = 31
                    Public Const 累計遠割回数 = 32
                    Public Const 累計遠割額 = 33
                    Public Const 累計貸切指数 = 34
                    Public Const 累計貸切割引額 = 35
                    Public Const 累計貸切料金 = 36
                    Public Const 累計貸切時間 = 37
                    Public Const 累計貸切走行 = 38
                    Public Const 累計固定料金キャンセル回数 = 39
                    Public Const 累計迎車キャンセル回数 = 40
                    Public Const 累計待回数 = 41
                    Public Const 累計待加算回数 = 42
                    Public Const 累計早朝キャンセル回数 = 43
                    Public Const 累計高齢割回数 = 44
                    Public Const 累計高齢割額 = 45
                    Public Const 累計幼児割回数 = 46
                    Public Const 累計幼児割額 = 47
                    Public Const 累計リセット回数 = 48
                    Public Const 累計待回数P = 49
                    Public Const 一日運賃 = 50
                    Public Const 一日料金 = 51
                    Public Const 一日身割回数 = 52
                    Public Const 一日身割額 = 53
                    Public Const 一日身割現収 = 54
                    Public Const 一日ワゴン回数 = 55
                    Public Const 一日早朝回数 = 56
                    Public Const 一日予約回数 = 57
                    Public Const 一日遠割回数 = 58
                    Public Const 一日遠割額 = 59
                    Public Const 一日貸切指数 = 60
                    Public Const 一日貸切割引 = 61
                    Public Const 一日貸切料金 = 62
                    Public Const 一日貸切時間 = 63
                    Public Const 一日貸切走行 = 64
                    Public Const 一日固定料金キャンセル回数 = 65
                    Public Const 一日迎車キャンセル回数 = 66
                    Public Const 一日待ち回数 = 67
                    Public Const 一日待ち加算回数 = 68
                    Public Const 一日早朝キャンセル回数 = 69
                    Public Const 一日高齢割回数 = 70
                    Public Const 一日高齢割額 = 71
                    Public Const 一日幼児割回数 = 72
                    Public Const 一日幼児割額 = 73
                    Public Const 一日リセット回数 = 74
                    Public Const 一日待ち回数P = 75
                    Public Const 基本料金 = 76
                    Public Const 以後料金 = 77
                    Public Const 固定料金 = 78
                    Public Const 月間営収 = 79
                    Public Const 営収 = 80
                    Public Const 男 = 81
                    Public Const 女 = 82
                    Public Const 現金 = 83
                    Public Const 未収 = 84
                    Public Const クレジット = 85
                    Public Const カード = 86
                    Public Const ID = 87
                    Public Const 交通系IC = 88
                    Public Const キャブカード = 89
                    Public Const プリペイドカード = 90
                    Public Const WAON = 91
                    Public Const メーター外料金 = 92
                    Public Const クレジット回数 = 93
                    Public Const 貸切回数 = 94
                    Public Const 空車_ETC料金 = 95
                    Public Const 実車_ETC料金 = 96
                    Public Const 燃料合計 = 97
                    Public Const 総勤務時間 = 98
                    Public Const 総休憩時間 = 99
                    Public Const 総回送時間 = 100
                    Public Const 総空車停止時間 = 101
                    Public Const リセット待回数 = 102
                    Public Const 出庫_親メーター = 103
                    Public Const 入庫_親メーター = 104
                    Public Const 空車_最高速度 = 105
                    Public Const 実車_最高速度 = 106
                    Public Const 最高速度 = 107
                    Public Const データNO = 108
                    Public Const 乗車地 = 109
                    Public Const 乗車日 = 110
                    Public Const 乗車時刻 = 111
                    Public Const フィールド112 = 112
                    Public Const フィールド113 = 113
                    Public Const フィールド114 = 114
                    Public Const フィールド115 = 115
                    Public Const フィールド116 = 116
                    Public Const メーター外 = 117
                    Public Const 元料金 = 118
                    Public Const 元現金 = 119
                    Public Const 降車地 = 120
                    Public Const 降車日 = 121
                    Public Const 降車時刻 = 122
                    Public Const 備考 = 123
                    Public Const 空車距離 = 124
                    Public Const 空車時刻 = 125
                    Public Const 空車走行時間 = 126
                    Public Const 空車最高速度 = 127
                    Public Const 迎車距離 = 128
                    Public Const 支払区分 = 129
                    Public Const タリフ区分 = 130
                    Public Const 料金 = 131
                    Public Const 実車時刻 = 132
                    Public Const 実車距離 = 133
                    Public Const 実車最高速度 = 134
                    Public Const 空車停止時間 = 135
                End Structure
            End Structure
        End Structure
#End Region
#End Region

#Region "ﾍｯﾀﾞ"
#Region "Clear"
        '''========================================================================================
        ''' <SUMMARY>ｸﾘｱ</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Clear_Header(Optional ByVal KeyClear As Boolean = False) As Boolean
            '----------------------------------------------------------------------
            ' ｷｰ部ｸﾘｱ
            '----------------------------------------------------------------------
            If KeyClear Then
                Me.Fields.Header.レイアウトCD.Value = Nothing
            End If

            '----------------------------------------------------------------------
            ' ﾃﾞｰﾀ部ｸﾘｱ
            '----------------------------------------------------------------------
            Me.Fields.Header.ファイル形式.Value = NodeContents.Constant.CodeValue.ファイル形式.Csv
            Me.Fields.Header.先頭タイトル.Value = ""
            Me.Fields.Header.最終ディレクトリ.Value = Nothing
            Me.Fields.Header.ファイル名.Value = Nothing
            Me.Fields.Undo.取込日時.Value = Nothing

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "Read"
        '''========================================================================================
        ''' <SUMMARY>読込</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Read_Header() As Boolean
            '----------------------------------------------------------------------
            ' 読み込み
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetViewTableAdapters.V0レイアウトTableAdapter
            Dim tbl As New NodeDatabase.DataSetView.V0レイアウトDataTable
            Dim qry As New Collection

            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl.レイアウトCDColumn.ColumnName, Me.Fields.Header.レイアウトCD.Value, BaseDatabase.Contents.Compare.Equal))

            Dim strSort As String = ""
            Dim strDelim As String = ""

            strSort &= strDelim & tbl.レイアウトCDColumn.ColumnName & " ASC" : strDelim = ","

            tbl = ada.SelectByCommon(qry, strSort)
            If tbl.Count > 0 Then
                Me.Fields.Header.ファイル形式.Value = tbl(0).ファイル形式
                Me.Fields.Header.先頭タイトル.Value = tbl(0).先頭タイトル
            End If

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "Execute1[実行]"
#Region "Execute1"
#Region "制御"
        '''========================================================================================
        ''' <SUMMARY>実行</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Execute1_Header() As Boolean
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim adaレイ As New NodeDatabase.DataSetViewTableAdapters.V0レイアウトTableAdapter
            Dim tblレイ As New NodeDatabase.DataSetView.V0レイアウトDataTable
            Dim qryレイ As New Collection

            Dim ada条件 As New NodeDatabase.DataSetViewTableAdapters.V0レイアウト条件TableAdapter
            Dim tbl条件 As New NodeDatabase.DataSetView.V0レイアウト条件DataTable
            Dim qry条件 As New Collection

            Dim strSort As String = ""

            Dim strNow As String = Format(Now, "yyyy/MM/dd HH:mm:ss")

            Dim Excel As New BaseCore.Common.Excel

            Dim intVirtualNo As Integer = 0 '仮番

            '----------------------------------------------------------------------
            ' ﾚｲｱｳﾄ読み込み
            '----------------------------------------------------------------------
            strSort = tblレイ.取込優先順Column.ColumnName _
              & "," & tblレイ.カラム位置Column.ColumnName

            qryレイ.Clear()
            qryレイ.Add(New BaseDatabase.Condition(tblレイ.レイアウトCDColumn.ColumnName, Me.Fields.Header.レイアウトCD.Value, BaseDatabase.Contents.Compare.Equal))
            tblレイ = adaレイ.SelectByCommon(qryレイ, strSort)

            If tblレイ.Count = 0 Then
                MyBase.LastError = "レイアウトが存在しません。"
                Return False
            End If

            qry条件.Clear()
            qry条件.Add(New BaseDatabase.Condition(tbl条件.レイアウトCDColumn.ColumnName, Me.Fields.Header.レイアウトCD.Value, BaseDatabase.Contents.Compare.Equal))
            tbl条件 = ada条件.SelectByCommon(qry条件)

            '----------------------------------------------------------------------
            ' ﾃﾝﾌﾟﾚｰﾄをｺﾋﾟｰ
            '----------------------------------------------------------------------
            Dim strPathTemplate As String = Me.Config.Dirテンプレート & "inner\Base.xlsx"
            System.IO.File.Copy(strPathTemplate, Me.Path2)

            '----------------------------------------------------------------------
            ' ｵｰﾌﾟﾝ
            '----------------------------------------------------------------------
            If Not Excel.Open(Me.Path2) Then
                Me.LastError = "正しいファイルを選択してください。"
                Return False
            End If

            '----------------------------------------------------------------------
            ' ｱｸﾃｨﾌﾞｼｰﾄ
            '----------------------------------------------------------------------
            Excel.SheetSelect(0)

            '----------------------------------------------------------------------
            ' ﾌｧｲﾙﾀｲﾌﾟ別処理
            '----------------------------------------------------------------------
            Try
                '----------------------------------------------------------------------
                ' 
                ' ﾌｧｲﾙﾀｲﾌﾟ別処理 各媒体→基本Excelへ
                ' 
                '----------------------------------------------------------------------
                Select Case True
                    Case Me.Fields.Header.ファイル形式.Value = NodeContents.Constant.CodeValue.ファイル形式.Excel
                        If Not Excel_Excel(tblレイ, tbl条件, Excel, strNow) Then
                            MyBase.LastError = Me.LastError
                            Return False
                        End If

                    Case Me.Fields.Header.ファイル形式.Value = NodeContents.Constant.CodeValue.ファイル形式.Csv
                        If Not Csv_Excel(tblレイ, tbl条件, Excel, strNow, NodeContents.Constant.CodeValue.ファイル形式.Csv) Then
                            MyBase.LastError = Me.LastError
                            Return False
                        End If
                End Select

                '----------------------------------------------------------------------
                ' 
                ' 基本Excel→ﾜｰｸ
                ' 
                '----------------------------------------------------------------------
                '----------------------------------------------------------------------
                ' 事前削除
                '----------------------------------------------------------------------
                Me.WorkDelete()

                '----------------------------------------------------------------------
                ' 事前検査
                '----------------------------------------------------------------------
                If Not Excel_Validator(Excel) Then
                    Me.LastError = "Excelの内容に誤りがあります。"
                    Return False
                End If

                '----------------------------------------------------------------------
                ' 初期化
                '----------------------------------------------------------------------
                '_SlipBase.Clear(True)
                '_SlipBase.Fields.Header.入力日付.Value = Nothing
                '_SlipBase.Fields.Header.伝票日付.Value = Nothing

                '----------------------------------------------------------------------
                ' ｲﾝﾎﾟｰﾄ
                '----------------------------------------------------------------------
                'Dim strKeyNew As String = ""
                'Dim strKeyOld As String = "@@@@@@" 'ｷｰ無しを考慮2014/10/08

                Dim LastRow As Integer = Excel.LastRowIndex        '最大行
                Dim LastCol As Integer = Excel.LastColIndex        '最大列

                For i As Long = 1 To LastRow
                    '------------------------------------------------------------------
                    ' ﾀｲﾄﾙ行 
                    '------------------------------------------------------------------
                    If i = 1 Then
                        Continue For
                    End If

                    '------------------------------------------------------------------
                    ' 登録
                    '------------------------------------------------------------------
                    '----------------------------------------------------------------------
                    ' ﾜｰｸ書込
                    '----------------------------------------------------------------------
                    If Not Me.Write_Work(intVirtualNo, strNow) Then
                        Return False
                    End If

                    '----------------------------------------------------------------------
                    ' ﾌﾚｰﾑﾜｰｸ設定 ﾍｯﾀﾞ
                    '----------------------------------------------------------------------
                    If Not Me.FrameSet_Header(Excel, i, strNow) Then
                        Return False
                    End If

                    '----------------------------------------------------------------------
                    ' ﾌﾚｰﾑﾜｰｸ設定 明細
                    '----------------------------------------------------------------------
                    'If Not Me.FrameSet_Detail(Excel, i) Then
                    '    Return False
                    'End If

                Next

                '----------------------------------------------------------------------
                ' 最終伝票対策
                '----------------------------------------------------------------------
                If Not Me.Write_Work(intVirtualNo, strNow) Then
                    Return False
                End If

                '----------------------------------------------------------------------
                ' ｽﾄｱﾄﾞ実行
                '----------------------------------------------------------------------
                Dim adaStored As New NodeDatabase.DataSetStoredTableAdapters.P1伝票作成一括TableAdapter
                Dim intStatus As Integer

                intStatus = adaStored.Execute(Me.Security.セッションID)

                '----------------------------------------------------------------------
                ' ｴﾗｰ処理
                '----------------------------------------------------------------------
                If intStatus <> 0 Then
                    Me.SetError(Excel)

                    Me.LastError = "Excelの内容に誤りがあります。"
                    Return False
                End If
            Catch ex As Exception
                MyBase.LastError = "以下のエラーの為、処理を続行できません。" & vbCrLf & ex.Message
                Return False
            Finally
                Excel.Close(Me.Path2)
                Me.WorkDelete()
            End Try

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "ﾌｧｲﾙﾀｲﾌﾟ別処理"
#Region "Excel→Excel"
        '''========================================================================================
        ''' <SUMMARY>Excel→Excel</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Function Excel_Excel(ByVal tblレイ As NodeDatabase.DataSetView.V0レイアウトDataTable _
                                     , ByVal tbl条件 As NodeDatabase.DataSetView.V0レイアウト条件DataTable _
                                     , ByRef _xlsBase As BaseCore.Common.Excel _
                                     , ByVal strNow As String
                                      ) As Boolean
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetTranTableAdapters.T乗務記録TableAdapter
            Dim tbl As New NodeDatabase.DataSetTran.T乗務記録DataTable
            Dim qry As New Collection

            Dim tblWork As New NodeDatabase.DataSetWork.W乗務記録DataTable

            Dim _xlsSource As New BaseCore.Common.Excel

            '----------------------------------------------------------------------
            ' ｵｰﾌﾟﾝ
            '----------------------------------------------------------------------
            'ｱｯﾌﾟﾛｰﾄﾞﾌｧｲﾙ
            If Not _xlsSource.Open(Me.Path1) Then
                Me.LastError = "正しいファイルを選択してください。"
                Return False
            End If

            '----------------------------------------------------------------------
            ' ｱｸﾃｨﾌﾞｼｰﾄ
            '----------------------------------------------------------------------
            _xlsSource.SheetSelect(1)

            Dim RowMax As Long = _xlsSource.LastRowIndex             '最大行
            Dim ClmMax As Long = _xlsSource.LastColIndex             '最大列

            '----------------------------------------------------------------------
            ' ｲﾝﾎﾟｰﾄ
            '----------------------------------------------------------------------
            Dim intLine As Integer = ConstantExcel.Header.RowTop

            Try
                For i As Long = 1 To RowMax
                    '------------------------------------------------------------------
                    ' ﾀｲﾄﾙ行
                    '------------------------------------------------------------------
                    If i = 1 And Not Me.Fields.Header.先頭タイトル.Value = NodeContents.Constant.CodeValue.フラグID.オフ Then
                        Continue For
                    End If

                    '------------------------------------------------------------------
                    ' ｶｳﾝﾄｱｯﾌﾟ
                    '------------------------------------------------------------------
                    intLine += 1

                    '----------------------------------------------------------------------
                    ' 値設定(ﾍｯﾀﾞ)
                    '----------------------------------------------------------------------
                    For Each rowレイ As NodeDatabase.DataSetView.V0レイアウトRow In tblレイ.Rows

                        '既定値入力は次へ
                        If rowレイ.Item(tblレイ.既定値Column.ColumnName).ToString <> "" Then
                            Continue For
                        End If

                        Dim FieldName As String = rowレイ.Item(tblレイ.伝票項目名Column.ColumnName)
                        Dim Position As Integer = rowレイ.Item(tblレイ.カラム位置Column.ColumnName)

                        Select Case FieldName.ToString '結合無はｸﾘｱ時、ﾃﾞｨﾌﾙﾄが設定されている項目に限る
                            'Case tbl.入力日付Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.入力日付, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.入力日付).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            'Case tbl.伝票日付Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.伝票日付, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.伝票日付).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            'Case tbl.伝票区分Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.伝票区分, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.伝票区分).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            'Case tbl.指示番号Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.指示番号, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.指示番号).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            'Case tbl.企業CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.企業CD, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.企業CD).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            'Case tbl.届先CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.届先CD, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.届先CD).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            'Case tbl.届先名1Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.届先名1, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.届先名1).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            'Case tbl.届先名2Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.届先名2, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.届先名2).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            'Case tbl.届先郵便番号Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.届先郵便番号, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.届先郵便番号).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            'Case tbl.届先住所1Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.届先住所1, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.届先住所1).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            'Case tbl.届先住所2Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.届先住所2, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.届先住所2).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            'Case tbl.届先TELColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.届先TEL, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.届先TEL).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            'Case tbl.摘要CD1Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.摘要CD1, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.摘要CD1).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            'Case tbl.摘要名1Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.摘要名1, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.摘要名1).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            'Case tbl.摘要CD2Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.摘要CD2, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.摘要CD2).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            'Case tbl.摘要名2Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.摘要名2, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.摘要名2).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            'Case tbl.運送会社CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.運送会社CD, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.運送会社CD).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            'Case tbl.配達日付Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.配達日付, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.配達日付).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            'Case tbl.配達時刻区分Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.配達時刻区分, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.配達時刻区分).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            'Case tbl.代引金額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.代引金額, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.代引金額).Value) + BaseCore.Common.Text.CVal(_xlsSource.CellGet(i, Position).Value))
                            'Case tbl.送り状NOColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.送り状No, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.送り状No).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            'Case tbl.注文NOColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文NO, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.注文NO).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            'Case tbl.注文日付Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文日付, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.注文日付).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            'Case tbl.注文名1Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文名1, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.注文名1).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            'Case tbl.注文名2Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文名2, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.注文名2).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            'Case tbl.注文子会社名Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文会社名, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.注文会社名).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            'Case tbl.注文部署名Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文部署名, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.注文部署名).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            'Case tbl.注文郵便番号Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文郵便番号, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.注文郵便番号).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            'Case tbl.注文住所1Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文住所1, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.注文住所1).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            'Case tbl.注文住所2Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文住所2, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.注文住所2).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            'Case tbl.注文TELColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文TEL, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.注文TEL).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            'Case tbl.注文メールアドレスColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文メールアドレス, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.注文メールアドレス).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            'Case tbl.注文支払区分Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文支払区分, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.注文支払区分).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            'Case tbl.注文入金状況Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文入金状況, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.注文入金状況).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            'Case tbl.注文ギフト包装区分Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文ギフト包装区分, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.注文ギフト包装区分).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            'Case tbl.注文ギフトメッセージColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文ギフトメッセージ, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.注文ギフトメッセージ).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            'Case tbl.注文のし表書き区分Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文のし表書き区分, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.注文のし表書き区分).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            'Case tbl.注文のし名入れColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文のし名入れ, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.注文のし名入れ).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            'Case tbl.注文品代Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文品代, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.注文品代).Value) + BaseCore.Common.Text.CVal(_xlsSource.CellGet(i, Position).Value))
                            'Case tbl.注文消費税Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文消費税, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.注文消費税).Value) + BaseCore.Common.Text.CVal(_xlsSource.CellGet(i, Position).Value))
                            'Case tbl.注文送料Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文送料, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.注文送料).Value) + BaseCore.Common.Text.CVal(_xlsSource.CellGet(i, Position).Value))
                            'Case tbl.注文手数料Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文手数料, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.注文手数料).Value) + BaseCore.Common.Text.CVal(_xlsSource.CellGet(i, Position).Value))
                            'Case tbl.注文ギフト包装代Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文ギフト包装代, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.注文ギフト包装代).Value) + BaseCore.Common.Text.CVal(_xlsSource.CellGet(i, Position).Value))
                            'Case tbl.注文値引額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文値引額, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.注文値引額).Value) + BaseCore.Common.Text.CVal(_xlsSource.CellGet(i, Position).Value))
                            'Case tbl.注文利用ポイントColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文利用ポイント, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.注文利用ポイント).Value) + BaseCore.Common.Text.CVal(_xlsSource.CellGet(i, Position).Value))
                            'Case tbl.注文請求金額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文請求金額, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.注文請求金額).Value) + BaseCore.Common.Text.CVal(_xlsSource.CellGet(i, Position).Value))
                            'Case tbl.注文ショップCDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文ショップCD, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.注文のし名入れ).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            'Case tbl.注文軽減税Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文軽減税, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.注文請求金額).Value) + BaseCore.Common.Text.CVal(_xlsSource.CellGet(i, Position).Value))

                            'Case tblDT.商品CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.商品CD, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.商品CD).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            'Case tblDT.先方商品NOColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.先方商品NO, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.先方商品NO).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            'Case tblDT.規格CD1Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.規格CD1, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.規格CD1).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            'Case tblDT.規格CD2Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.規格CD2, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.規格CD2).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            'Case tblDT.JANCDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.JANCD, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.JANCD).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            'Case tblDT.商品名Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.商品名, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.商品名).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            'Case tblDT.先方商品名Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.先方商品名, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.先方商品名).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            'Case tblDT.ロットNOColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.ロットNO, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.ロットNO).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            'Case tblDT.ロット日付Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.ロット日付, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.ロット日付).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            'Case tblDT.棚番CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.棚番CD, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.棚番CD).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            'Case tblDT.荷姿区分Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.荷姿区分, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.荷姿区分).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            'Case tblDT.単価Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.単価, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.単価).Value) + BaseCore.Common.Text.CVal(_xlsSource.CellGet(i, Position).Value))
                            'Case tblDT.金額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.金額, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.金額).Value) + BaseCore.Common.Text.CVal(_xlsSource.CellGet(i, Position).Value))
                            'Case tblDT.数量Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.数量, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.数量).Value) + BaseCore.Common.Text.CVal(_xlsSource.CellGet(i, Position).Value))

                            Case tbl.取込日付Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.取込日付, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.取込日付).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.日報日付Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.日報日付, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.日報日付).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.日報年月Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.日報年月, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.日報年月).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.出庫時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.出庫時刻, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.出庫時刻).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.入庫時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.入庫時刻, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.入庫時刻).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.乗務員CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.乗務員CD, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.乗務員CD).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.車両番号Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.車両番号, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.車両番号).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.営業所CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.営業所CD, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.営業所CD).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.部門CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.部門CD, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.部門CD).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.勤務CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.勤務CD, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.勤務CD).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.車種CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.車種CD, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.車種CD).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.天気Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.天気, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.天気).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計全走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計全走行, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計全走行).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計実車走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計実車走行, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計実車走行).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計迎車走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計迎車走行, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計迎車走行).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計営業回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計営業回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計営業回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計以後回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計以後回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計以後回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計迎車回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計迎車回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計迎車回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日全走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日全走行, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日全走行).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日実車走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日実車走行, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日実車走行).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日迎車走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日迎車走行, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日迎車走行).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日営業回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日営業回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日営業回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日以後回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日以後回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日以後回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日迎車回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日迎車回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日迎車回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計運賃Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計運賃, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計運賃).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計料金, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計料金).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計身割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計身割回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計身割回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計身割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計身割額, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計身割額).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計ワゴン回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計ワゴン回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計ワゴン回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計早朝回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計早朝回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計早朝回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計予約回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計予約回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計予約回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計遠割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計遠割回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計遠割回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計遠割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計遠割額, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計遠割額).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計貸切指数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計貸切指数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計貸切指数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計貸切割引額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計貸切割引額, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計貸切割引額).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計貸切料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計貸切料金, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計貸切料金).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計貸切時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計貸切時間, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計貸切時間).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計貸切走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計貸切走行, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計貸切走行).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計固定料金キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計固定料金キャンセル回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計固定料金キャンセル回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計迎車キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計迎車キャンセル回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計迎車キャンセル回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計待回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計待回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計待回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計待加算回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計待加算回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計待加算回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計早朝キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計早朝キャンセル回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計早朝キャンセル回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計高齢割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計高齢割回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計高齢割回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計高齢割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計高齢割額, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計高齢割額).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計幼児割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計幼児割回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計幼児割回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計幼児割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計幼児割額, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計幼児割額).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計リセット回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計リセット回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計リセット回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計待回数PColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計待回数P, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計待回数P).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日運賃Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日運賃, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日運賃).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日料金, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日料金).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日身割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日身割回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日身割回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日身割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日身割額, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日身割額).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日身割現収Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日身割現収, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日身割現収).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日ワゴン回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日ワゴン回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日ワゴン回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日早朝回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日早朝回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日早朝回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日予約回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日予約回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日予約回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日遠割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日遠割回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日遠割回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日遠割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日遠割額, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日遠割額).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日貸切指数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日貸切指数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日貸切指数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日貸切割引Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日貸切割引, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日貸切割引).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日貸切料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日貸切料金, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日貸切料金).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日貸切時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日貸切時間, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日貸切時間).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日貸切走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日貸切走行, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日貸切走行).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日固定料金キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日固定料金キャンセル回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日固定料金キャンセル回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日迎車キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日迎車キャンセル回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日迎車キャンセル回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日待ち回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日待ち回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日待ち回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日待ち加算回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日待ち加算回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日待ち加算回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日早朝キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日早朝キャンセル回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日早朝キャンセル回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日高齢割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日高齢割回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日高齢割回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日高齢割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日高齢割額, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日高齢割額).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日幼児割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日幼児割回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日幼児割回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日幼児割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日幼児割額, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日幼児割額).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日リセット回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日リセット回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日リセット回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日待ち回数PColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日待ち回数P, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日待ち回数P).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.基本料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.基本料金, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.基本料金).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.以後料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.以後料金, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.以後料金).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.固定料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.固定料金, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.固定料金).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.月間営収Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.月間営収, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.月間営収).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.営収Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.営収, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.営収).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.男Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.男, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.男).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.女Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.女, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.女).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.現金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.現金, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.現金).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.未収Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.未収, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.未収).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.クレジットColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.クレジット, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.クレジット).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.カードColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.カード, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.カード).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.IDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.ID, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.ID).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.交通系ICColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.交通系IC, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.交通系IC).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.キャブカードColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.キャブカード, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.キャブカード).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.プリペイドカードColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.プリペイドカード, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.プリペイドカード).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.WAONColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.WAON, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.WAON).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.メーター外料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.メーター外料金, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.メーター外料金).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.クレジット回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.クレジット回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.クレジット回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.貸切回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.貸切回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.貸切回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.空車_ETC料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車_ETC料金, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.空車_ETC料金).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.実車_ETC料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.実車_ETC料金, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.実車_ETC料金).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.燃料合計Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.燃料合計, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.燃料合計).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.総勤務時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.総勤務時間, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.総勤務時間).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.総休憩時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.総休憩時間, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.総休憩時間).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.総回送時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.総回送時間, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.総回送時間).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.総空車停止時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.総空車停止時間, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.総空車停止時間).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.リセット待回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.リセット待回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.リセット待回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.出庫_親メーターColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.出庫_親メーター, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.出庫_親メーター).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.入庫_親メーターColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.入庫_親メーター, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.入庫_親メーター).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.空車_最高速度Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車_最高速度, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.空車_最高速度).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.実車_最高速度Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.実車_最高速度, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.実車_最高速度).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.最高速度Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.最高速度, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.最高速度).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.データNOColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.データNO, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.データNO).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.乗車地Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.乗車地, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.乗車地).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.乗車日付Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.乗車日, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.乗車日).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.乗車時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.乗車時刻, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.乗車時刻).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.フィールド112Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.フィールド112, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.フィールド112).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.フィールド113Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.フィールド113, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.フィールド113).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.フィールド114Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.フィールド114, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.フィールド114).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.フィールド115Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.フィールド115, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.フィールド115).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.フィールド116Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.フィールド116, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.フィールド116).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.メーター外Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.メーター外, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.メーター外).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.元料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.元料金, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.元料金).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.元現金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.元現金, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.元現金).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.降車地Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.降車地, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.降車地).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.降車日付Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.降車日, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.降車日).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.降車時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.降車時刻, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.降車時刻).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.備考Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.備考, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.備考).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.空車距離Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車距離, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.空車距離).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.空車時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車時刻, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.空車時刻).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.空車走行時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車走行時間, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.空車走行時間).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.空車最高速度Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車最高速度, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.空車最高速度).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.迎車距離Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.迎車距離, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.迎車距離).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.支払区分Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.支払区分, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.支払区分).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.タリフ区分Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.タリフ区分, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.タリフ区分).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.料金, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.料金).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.実車時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.実車時刻, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.実車時刻).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.実車距離Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.実車距離, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.実車距離).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.実車最高速度Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.実車最高速度, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.実車最高速度).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.空車停止時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車停止時間, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.空車停止時間).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))

                            Case Else
                        End Select
                    Next

                    '----------------------------------------------------------------------
                    ' 変換ﾏｽﾀ
                    '----------------------------------------------------------------------
                    For Each row条件 As NodeDatabase.DataSetView.V0レイアウト条件Row In tbl条件.Rows
                        Select Case True
                            Case Not IsNumeric(row条件.Item(tbl条件.条件カラム位置Column.ColumnName))
                                Continue For
                            Case row条件.Item(tbl条件.条件カラム位置Column.ColumnName) <= 0
                                Continue For
                            Case row条件.Item(tbl条件.条件カラム位置Column.ColumnName) > _xlsSource.LastColIndex             '最大列
                                Continue For
                        End Select

                        Dim FieldPositionIf As Integer = row条件.Item(tbl条件.条件カラム位置Column.ColumnName)          'CsvとExcelの違い
                        Dim FieldValueIf As String = row条件.Item(tbl条件.条件項目値Column.ColumnName)

                        Dim SymbolCode As String = row条件.Item(tbl条件.符号区分Column.ColumnName)

                        Dim FieldCodeOt As String = row条件.Item(tbl条件.結果伝票項目CDColumn.ColumnName)
                        Dim FieldValueOt As String = row条件.Item(tbl条件.結果項目値Column.ColumnName)
                        Dim FieldNameOt As String = row条件.Item(tbl条件.結果伝票項目名Column.ColumnName)

                        '----------------------------------------------------------------------
                        ' 変換
                        '----------------------------------------------------------------------
                        If JudgeValue(BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, FieldPositionIf).Value, ""), SymbolCode, FieldValueIf) Then
                            Select Case FieldNameOt.ToString
                                Case tbl.取込日付Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.取込日付, FieldValueOt)
                                Case tbl.日報日付Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.日報日付, FieldValueOt)
                                Case tbl.日報年月Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.日報年月, FieldValueOt)
                                Case tbl.出庫時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.出庫時刻, FieldValueOt)
                                Case tbl.入庫時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.入庫時刻, FieldValueOt)
                                Case tbl.乗務員CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.乗務員CD, FieldValueOt)
                                Case tbl.車両番号Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.車両番号, FieldValueOt)
                                Case tbl.営業所CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.営業所CD, FieldValueOt)
                                Case tbl.部門CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.部門CD, FieldValueOt)
                                Case tbl.勤務CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.勤務CD, FieldValueOt)
                                Case tbl.車種CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.車種CD, FieldValueOt)
                                Case tbl.天気Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.天気, FieldValueOt)
                                Case tbl.累計全走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計全走行, FieldValueOt)
                                Case tbl.累計実車走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計実車走行, FieldValueOt)
                                Case tbl.累計迎車走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計迎車走行, FieldValueOt)
                                Case tbl.累計営業回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計営業回数, FieldValueOt)
                                Case tbl.累計以後回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計以後回数, FieldValueOt)
                                Case tbl.累計迎車回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計迎車回数, FieldValueOt)
                                Case tbl.一日全走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日全走行, FieldValueOt)
                                Case tbl.一日実車走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日実車走行, FieldValueOt)
                                Case tbl.一日迎車走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日迎車走行, FieldValueOt)
                                Case tbl.一日営業回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日営業回数, FieldValueOt)
                                Case tbl.一日以後回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日以後回数, FieldValueOt)
                                Case tbl.一日迎車回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日迎車回数, FieldValueOt)
                                Case tbl.累計運賃Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計運賃, FieldValueOt)
                                Case tbl.累計料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計料金, FieldValueOt)
                                Case tbl.累計身割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計身割回数, FieldValueOt)
                                Case tbl.累計身割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計身割額, FieldValueOt)
                                Case tbl.累計ワゴン回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計ワゴン回数, FieldValueOt)
                                Case tbl.累計早朝回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計早朝回数, FieldValueOt)
                                Case tbl.累計予約回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計予約回数, FieldValueOt)
                                Case tbl.累計遠割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計遠割回数, FieldValueOt)
                                Case tbl.累計遠割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計遠割額, FieldValueOt)
                                Case tbl.累計貸切指数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計貸切指数, FieldValueOt)
                                Case tbl.累計貸切割引額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計貸切割引額, FieldValueOt)
                                Case tbl.累計貸切料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計貸切料金, FieldValueOt)
                                Case tbl.累計貸切時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計貸切時間, FieldValueOt)
                                Case tbl.累計貸切走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計貸切走行, FieldValueOt)
                                Case tbl.累計固定料金キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計固定料金キャンセル回数, FieldValueOt)
                                Case tbl.累計迎車キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計迎車キャンセル回数, FieldValueOt)
                                Case tbl.累計待回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計待回数, FieldValueOt)
                                Case tbl.累計待加算回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計待加算回数, FieldValueOt)
                                Case tbl.累計早朝キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計早朝キャンセル回数, FieldValueOt)
                                Case tbl.累計高齢割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計高齢割回数, FieldValueOt)
                                Case tbl.累計高齢割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計高齢割額, FieldValueOt)
                                Case tbl.累計幼児割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計幼児割回数, FieldValueOt)
                                Case tbl.累計幼児割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計幼児割額, FieldValueOt)
                                Case tbl.累計リセット回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計リセット回数, FieldValueOt)
                                Case tbl.累計待回数PColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計待回数P, FieldValueOt)
                                Case tbl.一日運賃Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日運賃, FieldValueOt)
                                Case tbl.一日料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日料金, FieldValueOt)
                                Case tbl.一日身割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日身割回数, FieldValueOt)
                                Case tbl.一日身割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日身割額, FieldValueOt)
                                Case tbl.一日身割現収Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日身割現収, FieldValueOt)
                                Case tbl.一日ワゴン回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日ワゴン回数, FieldValueOt)
                                Case tbl.一日早朝回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日早朝回数, FieldValueOt)
                                Case tbl.一日予約回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日予約回数, FieldValueOt)
                                Case tbl.一日遠割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日遠割回数, FieldValueOt)
                                Case tbl.一日遠割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日遠割額, FieldValueOt)
                                Case tbl.一日貸切指数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日貸切指数, FieldValueOt)
                                Case tbl.一日貸切割引Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日貸切割引, FieldValueOt)
                                Case tbl.一日貸切料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日貸切料金, FieldValueOt)
                                Case tbl.一日貸切時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日貸切時間, FieldValueOt)
                                Case tbl.一日貸切走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日貸切走行, FieldValueOt)
                                Case tbl.一日固定料金キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日固定料金キャンセル回数, FieldValueOt)
                                Case tbl.一日迎車キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日迎車キャンセル回数, FieldValueOt)
                                Case tbl.一日待ち回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日待ち回数, FieldValueOt)
                                Case tbl.一日待ち加算回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日待ち加算回数, FieldValueOt)
                                Case tbl.一日早朝キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日早朝キャンセル回数, FieldValueOt)
                                Case tbl.一日高齢割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日高齢割回数, FieldValueOt)
                                Case tbl.一日高齢割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日高齢割額, FieldValueOt)
                                Case tbl.一日幼児割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日幼児割回数, FieldValueOt)
                                Case tbl.一日幼児割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日幼児割額, FieldValueOt)
                                Case tbl.一日リセット回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日リセット回数, FieldValueOt)
                                Case tbl.一日待ち回数PColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日待ち回数P, FieldValueOt)
                                Case tbl.基本料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.基本料金, FieldValueOt)
                                Case tbl.以後料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.以後料金, FieldValueOt)
                                Case tbl.固定料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.固定料金, FieldValueOt)
                                Case tbl.月間営収Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.月間営収, FieldValueOt)
                                Case tbl.営収Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.営収, FieldValueOt)
                                Case tbl.男Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.男, FieldValueOt)
                                Case tbl.女Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.女, FieldValueOt)
                                Case tbl.現金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.現金, FieldValueOt)
                                Case tbl.未収Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.未収, FieldValueOt)
                                Case tbl.クレジットColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.クレジット, FieldValueOt)
                                Case tbl.カードColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.カード, FieldValueOt)
                                Case tbl.IDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.ID, FieldValueOt)
                                Case tbl.交通系ICColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.交通系IC, FieldValueOt)
                                Case tbl.キャブカードColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.キャブカード, FieldValueOt)
                                Case tbl.プリペイドカードColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.プリペイドカード, FieldValueOt)
                                Case tbl.WAONColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.WAON, FieldValueOt)
                                Case tbl.メーター外料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.メーター外料金, FieldValueOt)
                                Case tbl.クレジット回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.クレジット回数, FieldValueOt)
                                Case tbl.貸切回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.貸切回数, FieldValueOt)
                                Case tbl.空車_ETC料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車_ETC料金, FieldValueOt)
                                Case tbl.実車_ETC料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.実車_ETC料金, FieldValueOt)
                                Case tbl.燃料合計Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.燃料合計, FieldValueOt)
                                Case tbl.総勤務時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.総勤務時間, FieldValueOt)
                                Case tbl.総休憩時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.総休憩時間, FieldValueOt)
                                Case tbl.総回送時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.総回送時間, FieldValueOt)
                                Case tbl.総空車停止時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.総空車停止時間, FieldValueOt)
                                Case tbl.リセット待回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.リセット待回数, FieldValueOt)
                                Case tbl.出庫_親メーターColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.出庫_親メーター, FieldValueOt)
                                Case tbl.入庫_親メーターColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.入庫_親メーター, FieldValueOt)
                                Case tbl.空車_最高速度Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車_最高速度, FieldValueOt)
                                Case tbl.実車_最高速度Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.実車_最高速度, FieldValueOt)
                                Case tbl.最高速度Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.最高速度, FieldValueOt)
                                Case tbl.データNOColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.データNO, FieldValueOt)
                                Case tbl.乗車地Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.乗車地, FieldValueOt)
                                Case tbl.乗車日付Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.乗車日, FieldValueOt)
                                Case tbl.乗車時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.乗車時刻, FieldValueOt)
                                Case tbl.フィールド112Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.フィールド112, FieldValueOt)
                                Case tbl.フィールド113Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.フィールド113, FieldValueOt)
                                Case tbl.フィールド114Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.フィールド114, FieldValueOt)
                                Case tbl.フィールド115Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.フィールド115, FieldValueOt)
                                Case tbl.フィールド116Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.フィールド116, FieldValueOt)
                                Case tbl.メーター外Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.メーター外, FieldValueOt)
                                Case tbl.元料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.元料金, FieldValueOt)
                                Case tbl.元現金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.元現金, FieldValueOt)
                                Case tbl.降車地Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.降車地, FieldValueOt)
                                Case tbl.降車日付Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.降車日, FieldValueOt)
                                Case tbl.降車時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.降車時刻, FieldValueOt)
                                Case tbl.備考Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.備考, FieldValueOt)
                                Case tbl.空車距離Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車距離, FieldValueOt)
                                Case tbl.空車時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車時刻, FieldValueOt)
                                Case tbl.空車走行時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車走行時間, FieldValueOt)
                                Case tbl.空車最高速度Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車最高速度, FieldValueOt)
                                Case tbl.迎車距離Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.迎車距離, FieldValueOt)
                                Case tbl.支払区分Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.支払区分, FieldValueOt)
                                Case tbl.タリフ区分Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.タリフ区分, FieldValueOt)
                                Case tbl.料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.料金, FieldValueOt)
                                Case tbl.実車時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.実車時刻, FieldValueOt)
                                Case tbl.実車距離Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.実車距離, FieldValueOt)
                                Case tbl.実車最高速度Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.実車最高速度, FieldValueOt)
                                Case tbl.空車停止時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車停止時間, FieldValueOt)
                                Case Else
                            End Select
                        End If
                    Next

                    '----------------------------------------------------------------------
                    ' 既定値
                    '----------------------------------------------------------------------
                    For Each rowレイ As NodeDatabase.DataSetView.V0レイアウトRow In tblレイ.Rows
                        '未入力は次へ
                        If rowレイ.Item(tblレイ.既定値Column.ColumnName).ToString = "" Then
                            Continue For
                        End If

                        Dim FieldName As String = rowレイ.Item(tblレイ.伝票項目名Column.ColumnName)
                        Select Case FieldName.ToString
                            Case tbl.取込日付Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.取込日付, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.日報日付Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.日報日付, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.日報年月Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.日報年月, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.出庫時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.出庫時刻, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.入庫時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.入庫時刻, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.乗務員CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.乗務員CD, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.車両番号Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.車両番号, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.営業所CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.営業所CD, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.部門CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.部門CD, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.勤務CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.勤務CD, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.車種CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.車種CD, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.天気Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.天気, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計全走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計全走行, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計実車走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計実車走行, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計迎車走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計迎車走行, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計営業回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計営業回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計以後回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計以後回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計迎車回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計迎車回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日全走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日全走行, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日実車走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日実車走行, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日迎車走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日迎車走行, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日営業回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日営業回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日以後回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日以後回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日迎車回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日迎車回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計運賃Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計運賃, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計料金, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計身割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計身割回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計身割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計身割額, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計ワゴン回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計ワゴン回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計早朝回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計早朝回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計予約回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計予約回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計遠割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計遠割回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計遠割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計遠割額, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計貸切指数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計貸切指数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計貸切割引額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計貸切割引額, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計貸切料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計貸切料金, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計貸切時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計貸切時間, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計貸切走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計貸切走行, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計固定料金キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計固定料金キャンセル回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計迎車キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計迎車キャンセル回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計待回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計待回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計待加算回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計待加算回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計早朝キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計早朝キャンセル回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計高齢割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計高齢割回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計高齢割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計高齢割額, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計幼児割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計幼児割回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計幼児割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計幼児割額, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計リセット回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計リセット回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計待回数PColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計待回数P, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日運賃Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日運賃, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日料金, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日身割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日身割回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日身割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日身割額, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日身割現収Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日身割現収, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日ワゴン回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日ワゴン回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日早朝回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日早朝回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日予約回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日予約回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日遠割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日遠割回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日遠割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日遠割額, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日貸切指数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日貸切指数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日貸切割引Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日貸切割引, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日貸切料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日貸切料金, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日貸切時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日貸切時間, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日貸切走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日貸切走行, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日固定料金キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日固定料金キャンセル回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日迎車キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日迎車キャンセル回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日待ち回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日待ち回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日待ち加算回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日待ち加算回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日早朝キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日早朝キャンセル回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日高齢割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日高齢割回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日高齢割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日高齢割額, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日幼児割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日幼児割回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日幼児割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日幼児割額, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日リセット回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日リセット回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日待ち回数PColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日待ち回数P, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.基本料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.基本料金, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.以後料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.以後料金, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.固定料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.固定料金, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.月間営収Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.月間営収, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.営収Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.営収, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.男Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.男, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.女Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.女, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.現金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.現金, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.未収Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.未収, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.クレジットColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.クレジット, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.カードColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.カード, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.IDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.ID, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.交通系ICColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.交通系IC, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.キャブカードColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.キャブカード, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.プリペイドカードColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.プリペイドカード, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.WAONColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.WAON, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.メーター外料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.メーター外料金, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.クレジット回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.クレジット回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.貸切回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.貸切回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.空車_ETC料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車_ETC料金, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.実車_ETC料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.実車_ETC料金, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.燃料合計Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.燃料合計, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.総勤務時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.総勤務時間, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.総休憩時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.総休憩時間, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.総回送時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.総回送時間, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.総空車停止時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.総空車停止時間, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.リセット待回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.リセット待回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.出庫_親メーターColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.出庫_親メーター, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.入庫_親メーターColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.入庫_親メーター, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.空車_最高速度Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車_最高速度, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.実車_最高速度Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.実車_最高速度, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.最高速度Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.最高速度, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.データNOColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.データNO, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.乗車地Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.乗車地, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.乗車日付Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.乗車日, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.乗車時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.乗車時刻, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.フィールド112Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.フィールド112, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.フィールド113Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.フィールド113, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.フィールド114Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.フィールド114, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.フィールド115Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.フィールド115, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.フィールド116Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.フィールド116, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.メーター外Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.メーター外, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.元料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.元料金, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.元現金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.元現金, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.降車地Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.降車地, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.降車日付Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.降車日, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.降車時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.降車時刻, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.備考Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.備考, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.空車距離Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車距離, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.空車時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車時刻, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.空車走行時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車走行時間, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.空車最高速度Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車最高速度, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.迎車距離Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.迎車距離, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.支払区分Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.支払区分, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.タリフ区分Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.タリフ区分, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.料金, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.実車時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.実車時刻, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.実車距離Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.実車距離, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.実車最高速度Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.実車最高速度, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.空車停止時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車停止時間, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case Else
                        End Select
                    Next

                    '----------------------------------------------------------------------
                    ' ｷｰ埋め込み(ｺﾒﾝﾄ)
                    '----------------------------------------------------------------------
                    Dim strKey As String = ""
                    Dim strDelimin As String = ""
                    For Each rowレイ As NodeDatabase.DataSetView.V0レイアウトRow In tblレイ.Rows
                        Dim Position As Integer = rowレイ.Item(tblレイ.カラム位置Column.ColumnName)
                        If Not rowレイ.Item(tblレイ.キーフラグColumn.ColumnName) = NodeContents.Constant.CodeValue.フラグID.オフ Then
                            If rowレイ.Item(tblレイ.既定値Column.ColumnName).ToString <> "" Then
                                strKey &= strDelimin & rowレイ.Item(tblレイ.既定値Column.ColumnName).ToString : strDelimin = ":"
                            Else
                                strKey &= strDelimin & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, "").ToString.Replace("'", "") : strDelimin = ":"
                            End If
                        End If

                        '変換ﾏｽﾀ
                        If Position <> 9999 Then
                            Dim row条件() As NodeDatabase.DataSetView.V0レイアウト条件Row = tbl条件.Select(tbl条件.条件カラム位置Column.ColumnName & "=  " & Position _
                                                                                               & " AND " & tbl条件.条件項目値Column.ColumnName & "    = '" & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, "").ToString.Replace("'", "") & "'" _
                                                                                               & " AND " & tbl条件.結果キーフラグColumn.ColumnName & "= '" & NodeContents.Constant.CodeValue.フラグID.オン & "'")
                            If row条件.Length > 0 Then
                                strKey &= strDelimin & row条件(0).Item(tbl条件.結果項目値Column) : strDelimin = ":"
                            End If
                        End If
                    Next

                    ' TODO Base.Excel deeer CellClearComment ene bhgui bn!!!!!!
                    '_xlsBase.CellClearComment(intLine, ConstantExcel.Header.列数.改伝キー)

                    '----------------------------------------------------------------------
                    ' 個別対応
                    '----------------------------------------------------------------------
                    '荷主ｺｰﾄﾞ
                    'If BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.荷主CD).Value, "") = "" Then
                    '    _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.荷主CD, Me.Fields.Header.荷主CD.Value)
                    'End If


                    '----------------------------------------------------------------------
                    ' 日付
                    '----------------------------------------------------------------------
                    Dim strValue As String = ""

                    '取込日付
                    strValue = ""
                    strValue = BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.取込日付).Value, "")
                    Select Case True
                        Case strValue = ""
                            _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.取込日付, Format(CDate(strNow), "yyyy/MM/dd"))

                        Case IsDate(strValue)
                            _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.取込日付, Format(CDate(strValue), "yyyy/MM/dd"))

                        Case strValue.ToString.Length = 8
                            _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.取込日付, BaseCore.Common.Text.TextFormat(strValue, tbl.取込日付Column.MaxLength, BaseContents.TextBox.Format.DateLong))

                        Case IsNumeric(strValue)
                            Dim strDate As String = Format(DateTime.FromOADate(strValue), "yyyy/MM/dd")
                            If IsDate(strDate) Then
                                _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.取込日付, strDate)
                            End If
                        Case Else
                    End Select

                    strValue = ""
                    strValue = BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.日報日付).Value, "")
                    Select Case True
                        Case strValue = ""
                            _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.日報日付, Format(CDate(strNow), "yyyy/MM/dd"))

                        Case IsDate(strValue)
                            _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.日報日付, Format(CDate(strValue), "yyyy/MM/dd"))

                        Case strValue.ToString.Length = 8
                            _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.日報日付, BaseCore.Common.Text.TextFormat(strValue, tbl.日報日付Column.MaxLength, BaseContents.TextBox.Format.DateLong))

                        Case IsNumeric(strValue)
                            Dim strDate As String = Format(DateTime.FromOADate(strValue), "yyyy/MM/dd")
                            If IsDate(strDate) Then
                                _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.日報日付, strDate)
                            End If
                        Case Else
                    End Select

                    strValue = ""
                    strValue = BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.乗車日).Value, "")
                    Select Case True
                        Case strValue = ""

                        Case IsDate(strValue)
                            _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.乗車日, Format(CDate(strValue), "yyyy/MM/dd"))

                        Case strValue.ToString.Length = 8
                            _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.乗車日, BaseCore.Common.Text.TextFormat(strValue, tbl.乗車日付Column.MaxLength, BaseContents.TextBox.Format.DateLong))

                        Case IsNumeric(strValue)
                            Dim strDate As String = Format(DateTime.FromOADate(strValue), "yyyy/MM/dd")
                            If IsDate(strDate) Then
                                _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.乗車日, strDate)
                            End If
                        Case Else
                    End Select

                    strValue = ""
                    strValue = BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.降車日).Value, "")
                    Select Case True
                        Case strValue = ""

                        Case IsDate(strValue)
                            _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.降車日, Format(CDate(strValue), "yyyy/MM/dd"))

                        Case strValue.ToString.Length = 8
                            _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.降車日, BaseCore.Common.Text.TextFormat(strValue, tbl.降車日付Column.MaxLength, BaseContents.TextBox.Format.DateLong))

                        Case IsNumeric(strValue)
                            Dim strDate As String = Format(DateTime.FromOADate(strValue), "yyyy/MM/dd")
                            If IsDate(strDate) Then
                                _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.降車日, strDate)
                            End If
                        Case Else
                    End Select

                Next
            Catch ex As Exception
                Me.LastError = ex.Message
                Return False
            Finally
                _xlsSource.Close(Me.Path1)
            End Try

            '----------------------------------------------------------------------
            ' 返却
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "CSV→Excel"
        '''========================================================================================
        ''' <SUMMARY>CSV→Excel</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Function Csv_Excel(ByVal tblレイ As NodeDatabase.DataSetView.V0レイアウトDataTable _
                                   , ByVal tbl条件 As NodeDatabase.DataSetView.V0レイアウト条件DataTable _
                                   , ByRef _xlsBase As BaseCore.Common.Excel _
                                   , ByVal strNow As String _
                                   , ByVal FileType As String
                                    ) As Boolean
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------

            Dim ada As New NodeDatabase.DataSetTranTableAdapters.T乗務記録TableAdapter
            Dim tbl As New NodeDatabase.DataSetTran.T乗務記録DataTable
            Dim qry As New Collection

            Dim tblWork As New NodeDatabase.DataSetWork.W乗務記録DataTable


            Dim i As Long

            '----------------------------------------------------------------------
            ' Csvﾌｧｲﾙの読み込み
            '----------------------------------------------------------------------
            Dim psrCsv As New Microsoft.VisualBasic.FileIO.TextFieldParser(Me.Path1, System.Text.Encoding.GetEncoding("Shift_JIS"))
            psrCsv.TextFieldType = Microsoft.VisualBasic.FileIO.FieldType.Delimited

            If FileType = NodeContents.Constant.CodeValue.ファイル形式.Csv Then
                psrCsv.SetDelimiters(",")       '区切り文字はｶﾝﾏ
            Else
                psrCsv.SetDelimiters(vbTab)     '区切り文字はﾀﾌﾞ
            End If

            '----------------------------------------------------------------------
            ' ｲﾝﾎﾟｰﾄ
            '----------------------------------------------------------------------
            Dim strKeyNew As String = ""
            Dim strKeyOld As String = "@@@@@@" 'ｷｰ無しを考慮2014/10/08

            Dim intLine As Integer = ConstantExcel.Header.RowTop

            Try
                Do While Not psrCsv.EndOfData
                    '------------------------------------------------------------------
                    ' 1行読み込み
                    '------------------------------------------------------------------
                    Dim arrCSV As String() = psrCsv.ReadFields()

                    '------------------------------------------------------------------
                    ' ｶｳﾝﾄｱｯﾌﾟ
                    '------------------------------------------------------------------
                    i += 1

                    '------------------------------------------------------------------
                    ' ﾀｲﾄﾙ行
                    '------------------------------------------------------------------
                    If i = 1 And Not Me.Fields.Header.先頭タイトル.Value = NodeContents.Constant.CodeValue.フラグID.オフ Then
                        Continue Do
                    End If

                    intLine += 1

                    '----------------------------------------------------------------------
                    ' 値設定(ﾍｯﾀﾞ)
                    '----------------------------------------------------------------------
                    For Each rowレイ As NodeDatabase.DataSetView.V0レイアウトRow In tblレイ.Rows

                        '既定値入力は次へ
                        If rowレイ.Item(tblレイ.既定値Column.ColumnName).ToString <> "" Then
                            Continue For
                        End If

                        Dim FieldName As String = rowレイ.Item(tblレイ.伝票項目名Column.ColumnName)
                        Dim Position As Integer = rowレイ.Item(tblレイ.カラム位置Column.ColumnName)

                        Select Case FieldName.ToString '結合無はｸﾘｱ時、ﾃﾞｨﾌﾙﾄが設定されている項目に限る
                            Case tbl.取込日付Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.取込日付, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.取込日付).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.日報日付Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.日報日付, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.日報日付).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.日報年月Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.日報年月, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.日報年月).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.出庫時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.出庫時刻, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.出庫時刻).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.入庫時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.入庫時刻, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.入庫時刻).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.乗務員CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.乗務員CD, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.乗務員CD).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.車両番号Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.車両番号, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.車両番号).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.営業所CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.営業所CD, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.営業所CD).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.部門CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.部門CD, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.部門CD).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.勤務CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.勤務CD, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.勤務CD).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.車種CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.車種CD, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.車種CD).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.天気Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.天気, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.天気).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.累計全走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計全走行, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計全走行).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.累計実車走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計実車走行, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計実車走行).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.累計迎車走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計迎車走行, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計迎車走行).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.累計営業回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計営業回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計営業回数).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.累計以後回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計以後回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計以後回数).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.累計迎車回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計迎車回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計迎車回数).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.一日全走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日全走行, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日全走行).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.一日実車走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日実車走行, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日実車走行).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.一日迎車走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日迎車走行, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日迎車走行).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.一日営業回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日営業回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日営業回数).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.一日以後回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日以後回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日以後回数).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.一日迎車回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日迎車回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日迎車回数).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.累計運賃Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計運賃, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計運賃).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.累計料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計料金, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計料金).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.累計身割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計身割回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計身割回数).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.累計身割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計身割額, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計身割額).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.累計ワゴン回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計ワゴン回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計ワゴン回数).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.累計早朝回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計早朝回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計早朝回数).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.累計予約回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計予約回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計予約回数).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.累計遠割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計遠割回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計遠割回数).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.累計遠割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計遠割額, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計遠割額).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.累計貸切指数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計貸切指数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計貸切指数).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.累計貸切割引額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計貸切割引額, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計貸切割引額).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.累計貸切料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計貸切料金, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計貸切料金).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.累計貸切時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計貸切時間, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計貸切時間).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.累計貸切走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計貸切走行, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計貸切走行).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.累計固定料金キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計固定料金キャンセル回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計固定料金キャンセル回数).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.累計迎車キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計迎車キャンセル回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計迎車キャンセル回数).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.累計待回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計待回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計待回数).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.累計待加算回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計待加算回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計待加算回数).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.累計早朝キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計早朝キャンセル回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計早朝キャンセル回数).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.累計高齢割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計高齢割回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計高齢割回数).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.累計高齢割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計高齢割額, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計高齢割額).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.累計幼児割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計幼児割回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計幼児割回数).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.累計幼児割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計幼児割額, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計幼児割額).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.累計リセット回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計リセット回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計リセット回数).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.累計待回数PColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計待回数P, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計待回数P).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.一日運賃Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日運賃, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日運賃).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.一日料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日料金, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日料金).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.一日身割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日身割回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日身割回数).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.一日身割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日身割額, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日身割額).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.一日身割現収Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日身割現収, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日身割現収).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.一日ワゴン回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日ワゴン回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日ワゴン回数).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.一日早朝回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日早朝回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日早朝回数).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.一日予約回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日予約回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日予約回数).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.一日遠割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日遠割回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日遠割回数).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.一日遠割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日遠割額, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日遠割額).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.一日貸切指数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日貸切指数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日貸切指数).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.一日貸切割引Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日貸切割引, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日貸切割引).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.一日貸切料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日貸切料金, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日貸切料金).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.一日貸切時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日貸切時間, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日貸切時間).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.一日貸切走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日貸切走行, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日貸切走行).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.一日固定料金キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日固定料金キャンセル回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日固定料金キャンセル回数).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.一日迎車キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日迎車キャンセル回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日迎車キャンセル回数).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.一日待ち回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日待ち回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日待ち回数).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.一日待ち加算回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日待ち加算回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日待ち加算回数).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.一日早朝キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日早朝キャンセル回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日早朝キャンセル回数).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.一日高齢割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日高齢割回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日高齢割回数).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.一日高齢割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日高齢割額, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日高齢割額).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.一日幼児割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日幼児割回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日幼児割回数).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.一日幼児割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日幼児割額, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日幼児割額).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.一日リセット回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日リセット回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日リセット回数).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.一日待ち回数PColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日待ち回数P, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日待ち回数P).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.基本料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.基本料金, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.基本料金).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.以後料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.以後料金, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.以後料金).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.固定料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.固定料金, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.固定料金).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.月間営収Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.月間営収, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.月間営収).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.営収Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.営収, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.営収).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.男Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.男, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.男).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.女Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.女, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.女).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.現金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.現金, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.現金).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.未収Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.未収, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.未収).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.クレジットColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.クレジット, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.クレジット).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.カードColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.カード, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.カード).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.IDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.ID, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.ID).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.交通系ICColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.交通系IC, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.交通系IC).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.キャブカードColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.キャブカード, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.キャブカード).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.プリペイドカードColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.プリペイドカード, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.プリペイドカード).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.WAONColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.WAON, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.WAON).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.メーター外料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.メーター外料金, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.メーター外料金).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.クレジット回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.クレジット回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.クレジット回数).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.貸切回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.貸切回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.貸切回数).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.空車_ETC料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車_ETC料金, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.空車_ETC料金).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.実車_ETC料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.実車_ETC料金, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.実車_ETC料金).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.燃料合計Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.燃料合計, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.燃料合計).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.総勤務時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.総勤務時間, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.総勤務時間).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.総休憩時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.総休憩時間, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.総休憩時間).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.総回送時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.総回送時間, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.総回送時間).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.総空車停止時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.総空車停止時間, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.総空車停止時間).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.リセット待回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.リセット待回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.リセット待回数).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.出庫_親メーターColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.出庫_親メーター, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.出庫_親メーター).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.入庫_親メーターColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.入庫_親メーター, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.入庫_親メーター).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.空車_最高速度Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車_最高速度, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.空車_最高速度).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.実車_最高速度Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.実車_最高速度, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.実車_最高速度).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.最高速度Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.最高速度, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.最高速度).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.データNOColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.データNO, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.データNO).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.乗車地Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.乗車地, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.乗車地).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.乗車日付Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.乗車日, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.乗車日).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.乗車時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.乗車時刻, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.乗車時刻).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.フィールド112Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.フィールド112, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.フィールド112).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.フィールド113Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.フィールド113, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.フィールド113).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.フィールド114Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.フィールド114, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.フィールド114).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.フィールド115Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.フィールド115, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.フィールド115).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.フィールド116Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.フィールド116, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.フィールド116).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.メーター外Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.メーター外, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.メーター外).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.元料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.元料金, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.元料金).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.元現金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.元現金, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.元現金).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.降車地Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.降車地, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.降車地).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.降車日付Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.降車日, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.降車日).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.降車時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.降車時刻, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.降車時刻).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.備考Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.備考, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.備考).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.空車距離Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車距離, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.空車距離).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.空車時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車時刻, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.空車時刻).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.空車走行時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車走行時間, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.空車走行時間).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.空車最高速度Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車最高速度, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.空車最高速度).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.迎車距離Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.迎車距離, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.迎車距離).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.支払区分Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.支払区分, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.支払区分).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.タリフ区分Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.タリフ区分, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.タリフ区分).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.料金, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.料金).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.実車時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.実車時刻, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.実車時刻).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.実車距離Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.実車距離, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.実車距離).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.実車最高速度Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.実車最高速度, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.実車最高速度).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.空車停止時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車停止時間, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.空車停止時間).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))

                            Case Else
                        End Select
                    Next

                    '----------------------------------------------------------------------
                    ' 変換ﾏｽﾀ
                    '----------------------------------------------------------------------
                    For Each row条件 As NodeDatabase.DataSetView.V0レイアウト条件Row In tbl条件.Rows
                        Select Case True
                            Case Not IsNumeric(row条件.Item(tbl条件.条件カラム位置Column.ColumnName))
                                Continue For
                            Case row条件.Item(tbl条件.条件カラム位置Column.ColumnName) <= 0
                                Continue For
                            Case row条件.Item(tbl条件.条件カラム位置Column.ColumnName) > arrCSV.Length
                                Continue For
                        End Select

                        Dim FieldPositionIf As Integer = row条件.Item(tbl条件.条件カラム位置Column.ColumnName) - 1       'CsvとExcelの違い
                        Dim FieldValueIf As String = row条件.Item(tbl条件.条件項目値Column.ColumnName)

                        Dim SymbolCode As String = row条件.Item(tbl条件.符号区分Column.ColumnName)

                        Dim FieldCodeOt As String = row条件.Item(tbl条件.結果伝票項目CDColumn.ColumnName)
                        Dim FieldValueOt As String = row条件.Item(tbl条件.結果項目値Column.ColumnName)
                        Dim FieldNameOt As String = row条件.Item(tbl条件.結果伝票項目名Column.ColumnName)

                        '----------------------------------------------------------------------
                        ' 変換
                        '----------------------------------------------------------------------
                        If JudgeValue(BaseCore.Common.Text.Nz(arrCSV.GetValue(FieldPositionIf), ""), SymbolCode, FieldValueIf) Then
                            Select Case FieldNameOt.ToString
                                'Case tbl.入力日付Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.入力日付, FieldValueOt)
                                'Case tbl.伝票日付Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.伝票日付, FieldValueOt)
                                'Case tbl.伝票区分Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.伝票区分, FieldValueOt)
                                'Case tbl.指示番号Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.指示番号, FieldValueOt)
                                'Case tbl.企業CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.企業CD, FieldValueOt)
                                'Case tbl.届先CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.届先CD, FieldValueOt)
                                'Case tbl.届先名1Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.届先名1, FieldValueOt)
                                'Case tbl.届先名2Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.届先名2, FieldValueOt)
                                'Case tbl.届先郵便番号Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.届先郵便番号, FieldValueOt)
                                'Case tbl.届先住所1Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.届先住所1, FieldValueOt)
                                'Case tbl.届先住所2Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.届先住所2, FieldValueOt)
                                'Case tbl.届先TELColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.届先TEL, FieldValueOt)
                                'Case tbl.摘要CD1Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.摘要CD1, FieldValueOt)
                                'Case tbl.摘要名1Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.摘要名1, FieldValueOt)
                                'Case tbl.摘要CD2Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.摘要CD2, FieldValueOt)
                                'Case tbl.摘要名2Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.摘要名2, FieldValueOt)
                                'Case tbl.運送会社CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.運送会社CD, FieldValueOt)
                                'Case tbl.配達日付Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.配達日付, FieldValueOt)
                                'Case tbl.配達時刻区分Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.配達時刻区分, FieldValueOt)
                                'Case tbl.代引金額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.代引金額, FieldValueOt)
                                'Case tbl.送り状NOColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.送り状No, FieldValueOt)
                                'Case tbl.注文NOColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文NO, FieldValueOt)
                                'Case tbl.注文日付Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文日付, FieldValueOt)
                                'Case tbl.注文名1Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文名1, FieldValueOt)
                                'Case tbl.注文名2Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文名2, FieldValueOt)
                                'Case tbl.注文子会社名Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文会社名, FieldValueOt)
                                'Case tbl.注文部署名Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文部署名, FieldValueOt)
                                'Case tbl.注文郵便番号Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文郵便番号, FieldValueOt)
                                'Case tbl.注文住所1Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文住所1, FieldValueOt)
                                'Case tbl.注文住所2Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文住所2, FieldValueOt)
                                'Case tbl.注文TELColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文TEL, FieldValueOt)
                                'Case tbl.注文メールアドレスColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文メールアドレス, FieldValueOt)
                                'Case tbl.注文支払区分Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文支払区分, FieldValueOt)
                                'Case tbl.注文入金状況Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文入金状況, FieldValueOt)
                                'Case tbl.注文ギフト包装区分Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文ギフト包装区分, FieldValueOt)
                                'Case tbl.注文ギフトメッセージColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文ギフトメッセージ, FieldValueOt)
                                'Case tbl.注文のし表書き区分Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文のし表書き区分, FieldValueOt)
                                'Case tbl.注文のし名入れColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文のし名入れ, FieldValueOt)
                                'Case tbl.注文品代Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文品代, FieldValueOt)
                                'Case tbl.注文消費税Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文消費税, FieldValueOt)
                                'Case tbl.注文送料Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文送料, FieldValueOt)
                                'Case tbl.注文手数料Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文手数料, FieldValueOt)
                                'Case tbl.注文ギフト包装代Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文ギフト包装代, FieldValueOt)
                                'Case tbl.注文値引額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文値引額, FieldValueOt)
                                'Case tbl.注文利用ポイントColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文利用ポイント, FieldValueOt)
                                'Case tbl.注文請求金額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文請求金額, FieldValueOt)
                                'Case tbl.注文ショップCDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文ショップCD, FieldValueOt)
                                'Case tbl.注文軽減税Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文軽減税, FieldValueOt)

                                'Case tblDT.商品CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.商品CD, FieldValueOt)
                                'Case tblDT.規格CD1Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.規格CD1, FieldValueOt)
                                'Case tblDT.規格CD2Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.規格CD2, FieldValueOt)
                                'Case tblDT.先方商品NOColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.先方商品NO, FieldValueOt)
                                'Case tblDT.JANCDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.JANCD, FieldValueOt)
                                'Case tblDT.ロットNOColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.ロットNO, FieldValueOt)
                                'Case tblDT.ロット日付Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.ロット日付, FieldValueOt)
                                'Case tblDT.商品名Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.商品名, FieldValueOt)
                                'Case tblDT.先方商品名Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.先方商品名, FieldValueOt)
                                'Case tblDT.棚番CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.棚番CD, FieldValueOt)
                                'Case tblDT.荷姿区分Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.荷姿区分, FieldValueOt)
                                'Case tblDT.数量Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.数量, FieldValueOt)
                                'Case tblDT.単価Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.単価, FieldValueOt)
                                'Case tblDT.金額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.金額, FieldValueOt)

                                Case tbl.取込日付Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.取込日付, FieldValueOt)
                                Case tbl.日報日付Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.日報日付, FieldValueOt)
                                Case tbl.日報年月Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.日報年月, FieldValueOt)
                                Case tbl.出庫時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.出庫時刻, FieldValueOt)
                                Case tbl.入庫時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.入庫時刻, FieldValueOt)
                                Case tbl.乗務員CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.乗務員CD, FieldValueOt)
                                Case tbl.車両番号Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.車両番号, FieldValueOt)
                                Case tbl.営業所CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.営業所CD, FieldValueOt)
                                Case tbl.部門CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.部門CD, FieldValueOt)
                                Case tbl.勤務CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.勤務CD, FieldValueOt)
                                Case tbl.車種CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.車種CD, FieldValueOt)
                                Case tbl.天気Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.天気, FieldValueOt)
                                Case tbl.累計全走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計全走行, FieldValueOt)
                                Case tbl.累計実車走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計実車走行, FieldValueOt)
                                Case tbl.累計迎車走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計迎車走行, FieldValueOt)
                                Case tbl.累計営業回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計営業回数, FieldValueOt)
                                Case tbl.累計以後回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計以後回数, FieldValueOt)
                                Case tbl.累計迎車回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計迎車回数, FieldValueOt)
                                Case tbl.一日全走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日全走行, FieldValueOt)
                                Case tbl.一日実車走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日実車走行, FieldValueOt)
                                Case tbl.一日迎車走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日迎車走行, FieldValueOt)
                                Case tbl.一日営業回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日営業回数, FieldValueOt)
                                Case tbl.一日以後回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日以後回数, FieldValueOt)
                                Case tbl.一日迎車回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日迎車回数, FieldValueOt)
                                Case tbl.累計運賃Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計運賃, FieldValueOt)
                                Case tbl.累計料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計料金, FieldValueOt)
                                Case tbl.累計身割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計身割回数, FieldValueOt)
                                Case tbl.累計身割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計身割額, FieldValueOt)
                                Case tbl.累計ワゴン回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計ワゴン回数, FieldValueOt)
                                Case tbl.累計早朝回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計早朝回数, FieldValueOt)
                                Case tbl.累計予約回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計予約回数, FieldValueOt)
                                Case tbl.累計遠割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計遠割回数, FieldValueOt)
                                Case tbl.累計遠割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計遠割額, FieldValueOt)
                                Case tbl.累計貸切指数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計貸切指数, FieldValueOt)
                                Case tbl.累計貸切割引額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計貸切割引額, FieldValueOt)
                                Case tbl.累計貸切料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計貸切料金, FieldValueOt)
                                Case tbl.累計貸切時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計貸切時間, FieldValueOt)
                                Case tbl.累計貸切走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計貸切走行, FieldValueOt)
                                Case tbl.累計固定料金キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計固定料金キャンセル回数, FieldValueOt)
                                Case tbl.累計迎車キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計迎車キャンセル回数, FieldValueOt)
                                Case tbl.累計待回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計待回数, FieldValueOt)
                                Case tbl.累計待加算回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計待加算回数, FieldValueOt)
                                Case tbl.累計早朝キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計早朝キャンセル回数, FieldValueOt)
                                Case tbl.累計高齢割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計高齢割回数, FieldValueOt)
                                Case tbl.累計高齢割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計高齢割額, FieldValueOt)
                                Case tbl.累計幼児割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計幼児割回数, FieldValueOt)
                                Case tbl.累計幼児割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計幼児割額, FieldValueOt)
                                Case tbl.累計リセット回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計リセット回数, FieldValueOt)
                                Case tbl.累計待回数PColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計待回数P, FieldValueOt)
                                Case tbl.一日運賃Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日運賃, FieldValueOt)
                                Case tbl.一日料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日料金, FieldValueOt)
                                Case tbl.一日身割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日身割回数, FieldValueOt)
                                Case tbl.一日身割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日身割額, FieldValueOt)
                                Case tbl.一日身割現収Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日身割現収, FieldValueOt)
                                Case tbl.一日ワゴン回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日ワゴン回数, FieldValueOt)
                                Case tbl.一日早朝回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日早朝回数, FieldValueOt)
                                Case tbl.一日予約回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日予約回数, FieldValueOt)
                                Case tbl.一日遠割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日遠割回数, FieldValueOt)
                                Case tbl.一日遠割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日遠割額, FieldValueOt)
                                Case tbl.一日貸切指数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日貸切指数, FieldValueOt)
                                Case tbl.一日貸切割引Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日貸切割引, FieldValueOt)
                                Case tbl.一日貸切料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日貸切料金, FieldValueOt)
                                Case tbl.一日貸切時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日貸切時間, FieldValueOt)
                                Case tbl.一日貸切走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日貸切走行, FieldValueOt)
                                Case tbl.一日固定料金キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日固定料金キャンセル回数, FieldValueOt)
                                Case tbl.一日迎車キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日迎車キャンセル回数, FieldValueOt)
                                Case tbl.一日待ち回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日待ち回数, FieldValueOt)
                                Case tbl.一日待ち加算回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日待ち加算回数, FieldValueOt)
                                Case tbl.一日早朝キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日早朝キャンセル回数, FieldValueOt)
                                Case tbl.一日高齢割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日高齢割回数, FieldValueOt)
                                Case tbl.一日高齢割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日高齢割額, FieldValueOt)
                                Case tbl.一日幼児割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日幼児割回数, FieldValueOt)
                                Case tbl.一日幼児割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日幼児割額, FieldValueOt)
                                Case tbl.一日リセット回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日リセット回数, FieldValueOt)
                                Case tbl.一日待ち回数PColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日待ち回数P, FieldValueOt)
                                Case tbl.基本料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.基本料金, FieldValueOt)
                                Case tbl.以後料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.以後料金, FieldValueOt)
                                Case tbl.固定料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.固定料金, FieldValueOt)
                                Case tbl.月間営収Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.月間営収, FieldValueOt)
                                Case tbl.営収Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.営収, FieldValueOt)
                                Case tbl.男Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.男, FieldValueOt)
                                Case tbl.女Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.女, FieldValueOt)
                                Case tbl.現金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.現金, FieldValueOt)
                                Case tbl.未収Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.未収, FieldValueOt)
                                Case tbl.クレジットColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.クレジット, FieldValueOt)
                                Case tbl.カードColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.カード, FieldValueOt)
                                Case tbl.IDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.ID, FieldValueOt)
                                Case tbl.交通系ICColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.交通系IC, FieldValueOt)
                                Case tbl.キャブカードColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.キャブカード, FieldValueOt)
                                Case tbl.プリペイドカードColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.プリペイドカード, FieldValueOt)
                                Case tbl.WAONColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.WAON, FieldValueOt)
                                Case tbl.メーター外料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.メーター外料金, FieldValueOt)
                                Case tbl.クレジット回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.クレジット回数, FieldValueOt)
                                Case tbl.貸切回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.貸切回数, FieldValueOt)
                                Case tbl.空車_ETC料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車_ETC料金, FieldValueOt)
                                Case tbl.実車_ETC料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.実車_ETC料金, FieldValueOt)
                                Case tbl.燃料合計Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.燃料合計, FieldValueOt)
                                Case tbl.総勤務時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.総勤務時間, FieldValueOt)
                                Case tbl.総休憩時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.総休憩時間, FieldValueOt)
                                Case tbl.総回送時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.総回送時間, FieldValueOt)
                                Case tbl.総空車停止時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.総空車停止時間, FieldValueOt)
                                Case tbl.リセット待回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.リセット待回数, FieldValueOt)
                                Case tbl.出庫_親メーターColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.出庫_親メーター, FieldValueOt)
                                Case tbl.入庫_親メーターColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.入庫_親メーター, FieldValueOt)
                                Case tbl.空車_最高速度Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車_最高速度, FieldValueOt)
                                Case tbl.実車_最高速度Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.実車_最高速度, FieldValueOt)
                                Case tbl.最高速度Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.最高速度, FieldValueOt)
                                Case tbl.データNOColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.データNO, FieldValueOt)
                                Case tbl.乗車地Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.乗車地, FieldValueOt)
                                Case tbl.乗車日付Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.乗車日, FieldValueOt)
                                Case tbl.乗車時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.乗車時刻, FieldValueOt)
                                Case tbl.フィールド112Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.フィールド112, FieldValueOt)
                                Case tbl.フィールド113Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.フィールド113, FieldValueOt)
                                Case tbl.フィールド114Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.フィールド114, FieldValueOt)
                                Case tbl.フィールド115Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.フィールド115, FieldValueOt)
                                Case tbl.フィールド116Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.フィールド116, FieldValueOt)
                                Case tbl.メーター外Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.メーター外, FieldValueOt)
                                Case tbl.元料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.元料金, FieldValueOt)
                                Case tbl.元現金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.元現金, FieldValueOt)
                                Case tbl.降車地Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.降車地, FieldValueOt)
                                Case tbl.降車日付Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.降車日, FieldValueOt)
                                Case tbl.降車時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.降車時刻, FieldValueOt)
                                Case tbl.備考Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.備考, FieldValueOt)
                                Case tbl.空車距離Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車距離, FieldValueOt)
                                Case tbl.空車時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車時刻, FieldValueOt)
                                Case tbl.空車走行時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車走行時間, FieldValueOt)
                                Case tbl.空車最高速度Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車最高速度, FieldValueOt)
                                Case tbl.迎車距離Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.迎車距離, FieldValueOt)
                                Case tbl.支払区分Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.支払区分, FieldValueOt)
                                Case tbl.タリフ区分Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.タリフ区分, FieldValueOt)
                                Case tbl.料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.料金, FieldValueOt)
                                Case tbl.実車時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.実車時刻, FieldValueOt)
                                Case tbl.実車距離Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.実車距離, FieldValueOt)
                                Case tbl.実車最高速度Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.実車最高速度, FieldValueOt)
                                Case tbl.空車停止時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車停止時間, FieldValueOt)

                                Case Else
                            End Select
                        End If
                    Next

                    '----------------------------------------------------------------------
                    ' 既定値
                    '----------------------------------------------------------------------
                    For Each rowレイ As NodeDatabase.DataSetView.V0レイアウトRow In tblレイ.Rows
                        '未入力は次へ
                        If rowレイ.Item(tblレイ.既定値Column.ColumnName).ToString = "" Then
                            Continue For
                        End If

                        Dim FieldName As String = rowレイ.Item(tblレイ.伝票項目名Column.ColumnName)
                        Select Case FieldName.ToString
                            Case tbl.取込日付Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.取込日付, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.日報日付Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.日報日付, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.日報年月Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.日報年月, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.出庫時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.出庫時刻, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.入庫時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.入庫時刻, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.乗務員CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.乗務員CD, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.車両番号Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.車両番号, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.営業所CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.営業所CD, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.部門CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.部門CD, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.勤務CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.勤務CD, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.車種CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.車種CD, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.天気Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.天気, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計全走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計全走行, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計実車走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計実車走行, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計迎車走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計迎車走行, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計営業回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計営業回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計以後回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計以後回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計迎車回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計迎車回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日全走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日全走行, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日実車走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日実車走行, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日迎車走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日迎車走行, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日営業回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日営業回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日以後回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日以後回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日迎車回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日迎車回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計運賃Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計運賃, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計料金, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計身割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計身割回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計身割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計身割額, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計ワゴン回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計ワゴン回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計早朝回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計早朝回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計予約回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計予約回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計遠割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計遠割回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計遠割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計遠割額, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計貸切指数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計貸切指数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計貸切割引額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計貸切割引額, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計貸切料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計貸切料金, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計貸切時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計貸切時間, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計貸切走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計貸切走行, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計固定料金キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計固定料金キャンセル回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計迎車キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計迎車キャンセル回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計待回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計待回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計待加算回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計待加算回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計早朝キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計早朝キャンセル回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計高齢割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計高齢割回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計高齢割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計高齢割額, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計幼児割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計幼児割回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計幼児割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計幼児割額, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計リセット回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計リセット回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計待回数PColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計待回数P, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日運賃Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日運賃, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日料金, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日身割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日身割回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日身割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日身割額, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日身割現収Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日身割現収, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日ワゴン回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日ワゴン回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日早朝回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日早朝回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日予約回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日予約回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日遠割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日遠割回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日遠割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日遠割額, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日貸切指数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日貸切指数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日貸切割引Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日貸切割引, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日貸切料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日貸切料金, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日貸切時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日貸切時間, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日貸切走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日貸切走行, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日固定料金キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日固定料金キャンセル回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日迎車キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日迎車キャンセル回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日待ち回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日待ち回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日待ち加算回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日待ち加算回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日早朝キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日早朝キャンセル回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日高齢割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日高齢割回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日高齢割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日高齢割額, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日幼児割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日幼児割回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日幼児割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日幼児割額, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日リセット回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日リセット回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日待ち回数PColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日待ち回数P, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.基本料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.基本料金, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.以後料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.以後料金, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.固定料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.固定料金, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.月間営収Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.月間営収, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.営収Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.営収, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.男Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.男, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.女Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.女, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.現金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.現金, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.未収Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.未収, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.クレジットColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.クレジット, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.カードColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.カード, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.IDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.ID, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.交通系ICColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.交通系IC, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.キャブカードColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.キャブカード, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.プリペイドカードColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.プリペイドカード, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.WAONColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.WAON, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.メーター外料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.メーター外料金, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.クレジット回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.クレジット回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.貸切回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.貸切回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.空車_ETC料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車_ETC料金, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.実車_ETC料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.実車_ETC料金, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.燃料合計Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.燃料合計, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.総勤務時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.総勤務時間, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.総休憩時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.総休憩時間, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.総回送時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.総回送時間, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.総空車停止時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.総空車停止時間, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.リセット待回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.リセット待回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.出庫_親メーターColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.出庫_親メーター, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.入庫_親メーターColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.入庫_親メーター, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.空車_最高速度Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車_最高速度, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.実車_最高速度Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.実車_最高速度, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.最高速度Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.最高速度, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.データNOColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.データNO, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.乗車地Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.乗車地, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.乗車日付Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.乗車日, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.乗車時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.乗車時刻, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.フィールド112Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.フィールド112, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.フィールド113Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.フィールド113, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.フィールド114Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.フィールド114, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.フィールド115Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.フィールド115, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.フィールド116Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.フィールド116, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.メーター外Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.メーター外, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.元料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.元料金, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.元現金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.元現金, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.降車地Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.降車地, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.降車日付Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.降車日, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.降車時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.降車時刻, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.備考Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.備考, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.空車距離Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車距離, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.空車時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車時刻, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.空車走行時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車走行時間, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.空車最高速度Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車最高速度, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.迎車距離Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.迎車距離, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.支払区分Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.支払区分, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.タリフ区分Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.タリフ区分, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.料金, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.実車時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.実車時刻, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.実車距離Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.実車距離, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.実車最高速度Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.実車最高速度, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.空車停止時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車停止時間, rowレイ.Item(tblレイ.既定値Column.ColumnName))

                            Case Else
                        End Select
                    Next

                    '----------------------------------------------------------------------
                    ' ｷｰ埋め込み(ｺﾒﾝﾄ)
                    '----------------------------------------------------------------------
                    Dim strKey As String = ""
                    Dim strDelimin As String = ""
                    For Each rowレイ As NodeDatabase.DataSetView.V0レイアウトRow In tblレイ.Rows
                        Dim Position As Integer = rowレイ.Item(tblレイ.カラム位置Column.ColumnName)
                        If Not rowレイ.Item(tblレイ.キーフラグColumn.ColumnName) = NodeContents.Constant.CodeValue.フラグID.オフ Then
                            If rowレイ.Item(tblレイ.既定値Column.ColumnName).ToString <> "" Then
                                strKey &= strDelimin & rowレイ.Item(tblレイ.既定値Column.ColumnName).ToString : strDelimin = ":"
                            Else
                                strKey &= strDelimin & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), "").ToString.Replace("'", "") : strDelimin = ":"
                            End If
                        End If

                        '変換ﾏｽﾀ
                        If Position <> 9999 Then
                            Dim row条件() As NodeDatabase.DataSetView.V0レイアウト条件Row = tbl条件.Select(tbl条件.条件カラム位置Column.ColumnName & "=  " & Position + 1 _
                                                                                               & " AND " & tbl条件.条件項目値Column.ColumnName & "    = '" & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), "").ToString.Replace("'", "") & "'" _
                                                                                               & " AND " & tbl条件.結果キーフラグColumn.ColumnName & "= '" & NodeContents.Constant.CodeValue.フラグID.オン & "'")
                            If row条件.Length > 0 Then
                                strKeyNew &= strDelimin & row条件(0).Item(tbl条件.結果項目値Column) : strDelimin = ":"
                            End If
                        End If
                    Next

                    ' TODO Base.Excel deeer CellClearComment ene bhgui bn!!!!!!
                    '_xlsBase.CellClearComment(intLine, ConstantExcel.Header.列数.改伝キー)
                    '_xlsBase.CellSetComment(intLine, ConstantExcel.Header.列数.改伝キー, strKey)


                    '----------------------------------------------------------------------
                    ' 日付
                    '----------------------------------------------------------------------
                    Dim strValue As String = ""

                    '取込日付
                    strValue = ""
                    strValue = BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.取込日付).Value, "")
                    Select Case True
                        Case strValue = ""
                            _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.取込日付, Format(CDate(strNow), "yyyy/MM/dd"))

                        Case IsDate(strValue)
                            _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.取込日付, Format(CDate(strValue), "yyyy/MM/dd"))

                        Case strValue.ToString.Length = 8
                            _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.取込日付, BaseCore.Common.Text.TextFormat(strValue, tbl.取込日付Column.MaxLength, BaseContents.TextBox.Format.DateLong))

                        Case IsNumeric(strValue)
                            Dim strDate As String = Format(DateTime.FromOADate(strValue), "yyyy/MM/dd")
                            If IsDate(strDate) Then
                                _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.取込日付, strDate)
                            End If
                        Case Else
                    End Select

                    strValue = ""
                    strValue = BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.日報日付).Value, "")
                    Select Case True
                        Case strValue = ""
                            _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.日報日付, Format(CDate(strNow), "yyyy/MM/dd"))

                        Case IsDate(strValue)
                            _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.日報日付, Format(CDate(strValue), "yyyy/MM/dd"))

                        Case strValue.ToString.Length = 8
                            _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.日報日付, BaseCore.Common.Text.TextFormat(strValue, tbl.日報日付Column.MaxLength, BaseContents.TextBox.Format.DateLong))

                        Case IsNumeric(strValue)
                            Dim strDate As String = Format(DateTime.FromOADate(strValue), "yyyy/MM/dd")
                            If IsDate(strDate) Then
                                _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.日報日付, strDate)
                            End If
                        Case Else
                    End Select

                    strValue = ""
                    strValue = BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.乗車日).Value, "")
                    Select Case True
                        Case strValue = ""

                        Case IsDate(strValue)
                            _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.乗車日, Format(CDate(strValue), "yyyy/MM/dd"))

                        Case strValue.ToString.Length = 8
                            _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.乗車日, BaseCore.Common.Text.TextFormat(strValue, tbl.乗車日付Column.MaxLength, BaseContents.TextBox.Format.DateLong))

                        Case IsNumeric(strValue)
                            Dim strDate As String = Format(DateTime.FromOADate(strValue), "yyyy/MM/dd")
                            If IsDate(strDate) Then
                                _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.乗車日, strDate)
                            End If
                        Case Else
                    End Select

                    strValue = ""
                    strValue = BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.降車日).Value, "")
                    Select Case True
                        Case strValue = ""

                        Case IsDate(strValue)
                            _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.降車日, Format(CDate(strValue), "yyyy/MM/dd"))

                        Case strValue.ToString.Length = 8
                            _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.降車日, BaseCore.Common.Text.TextFormat(strValue, tbl.降車日付Column.MaxLength, BaseContents.TextBox.Format.DateLong))

                        Case IsNumeric(strValue)
                            Dim strDate As String = Format(DateTime.FromOADate(strValue), "yyyy/MM/dd")
                            If IsDate(strDate) Then
                                _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.降車日, strDate)
                            End If
                        Case Else
                    End Select

                Loop
            Catch ex As Exception
                Me.LastError = ex.Message
                Return False
            Finally
                psrCsv.Close()
            End Try

            '----------------------------------------------------------------------
            ' 返却
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region
#End Region

#Region "ﾌﾚｰﾑﾜｰｸ"
#Region "ﾍｯﾀﾞ_ﾌﾚｰﾑﾜｰｸｾｯﾄ"
        '''========================================================================================
        ''' <SUMMARY>Excelﾒｲﾝ処理</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Function FrameSet_Header(ByVal Excel As BaseCore.Common.Excel _
                                         , ByVal i As Long _
                                         , ByVal strNow As String
                                          ) As Boolean
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetTranTableAdapters.T乗務記録TableAdapter
            Dim tbl As New NodeDatabase.DataSetTran.T乗務記録DataTable
            Dim qry As New Collection

            Dim tblWork As New NodeDatabase.DataSetWork.W乗務記録DataTable

            Try
                '----------------------------------------------------------------------
                ' 値設定(ﾍｯﾀﾞ)
                '----------------------------------------------------------------------
                _SlipBase.Fields.Header.伝票タイプ.Value = ""
                _SlipBase.Fields.Header.伝票番号.Value = ""
                '_SlipBase.Fields.Header.入力日付.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.入力日付).Value, "")
                '_SlipBase.Fields.Header.伝票日付.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.伝票日付).Value, "")
                '_SlipBase.Fields.Header.伝票区分.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.伝票区分).Value, "")
                _SlipBase.Fields.Header.緊急区分.Value = ""
                '_SlipBase.Fields.Header.指示番号.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.指示番号).Value, "")
                _SlipBase.Fields.Header.指示区分.Value = ""
                _SlipBase.Fields.Header.指示状況.Value = ""
                _SlipBase.Fields.Header.指示者名.Value = ""
                _SlipBase.Fields.Header.荷主CD内部.Value = ""
                _SlipBase.Fields.Header.拠点CD.Value = ""
                '_SlipBase.Fields.Header.企業CD.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.企業CD).Value, "")
                '_SlipBase.Fields.Header.届先CD.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.届先CD).Value, "")
                '_SlipBase.Fields.Header.届先名1.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.届先名1).Value, "")
                '_SlipBase.Fields.Header.届先名2.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.届先名2).Value, "")
                '_SlipBase.Fields.Header.届先郵便番号.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.届先郵便番号).Value, "")
                '_SlipBase.Fields.Header.届先住所1.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.届先住所1).Value, "")
                '_SlipBase.Fields.Header.届先住所2.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.届先住所2).Value, "")
                '_SlipBase.Fields.Header.届先TEL.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.届先TEL).Value, "")
                '_SlipBase.Fields.Header.摘要CD1.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.摘要CD1).Value, "")
                '_SlipBase.Fields.Header.摘要名1.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.摘要名1).Value, "")
                '_SlipBase.Fields.Header.摘要CD2.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.摘要CD2).Value, "")
                '_SlipBase.Fields.Header.摘要名2.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.摘要名2).Value, "")
                '_SlipBase.Fields.Header.運送会社CD.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.運送会社CD).Value, "")
                '_SlipBase.Fields.Header.配達方法.Value = ""
                '_SlipBase.Fields.Header.配達日付.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.配達日付).Value, "")
                '_SlipBase.Fields.Header.配達時刻区分.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.配達時刻区分).Value, "")
                '_SlipBase.Fields.Header.代引金額.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.代引金額).Value)
                '_SlipBase.Fields.Header.送り状区分.Value = ""
                '_SlipBase.Fields.Header.送り状NO.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.送り状No).Value, "")
                '_SlipBase.Fields.Header.車番.Value = ""
                _SlipBase.Fields.Header.便.Value = 0
                _SlipBase.Fields.Header.運送扱数.Value = 0
                _SlipBase.Fields.Header.運送単価.Value = 0
                _SlipBase.Fields.Header.運送金額.Value = 0
                '_SlipBase.Fields.Header.注文ショップCD.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.注文ショップCD).Value, "")
                '_SlipBase.Fields.Header.注文NO.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.注文NO).Value, "")
                '_SlipBase.Fields.Header.注文日付.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.注文日付).Value, "")
                '_SlipBase.Fields.Header.注文名1.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.注文名1).Value, "")
                '_SlipBase.Fields.Header.注文名2.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.注文名2).Value, "")
                '_SlipBase.Fields.Header.注文会社名.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.注文会社名).Value, "")
                '_SlipBase.Fields.Header.注文部署名.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.注文部署名).Value, "")
                '_SlipBase.Fields.Header.注文郵便番号.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.注文郵便番号).Value, "")
                '_SlipBase.Fields.Header.注文住所1.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.注文住所1).Value, "")
                '_SlipBase.Fields.Header.注文住所2.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.注文住所2).Value, "")
                '_SlipBase.Fields.Header.注文TEL.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.注文TEL).Value, "")
                '_SlipBase.Fields.Header.注文メールアドレス.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.注文メールアドレス).Value, "")
                '_SlipBase.Fields.Header.注文支払区分.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.注文支払区分).Value, "")
                '_SlipBase.Fields.Header.注文入金状況.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.注文入金状況).Value, "")
                '_SlipBase.Fields.Header.注文ギフト包装区分.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.注文ギフト包装区分).Value, "")
                '_SlipBase.Fields.Header.注文ギフトメッセージ.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.注文ギフトメッセージ).Value, "")
                '_SlipBase.Fields.Header.注文のし表書き区分.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.注文のし表書き区分).Value, "")
                '_SlipBase.Fields.Header.注文のし名入れ.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.注文のし名入れ).Value, "")
                '_SlipBase.Fields.Header.注文品代.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.注文品代).Value)
                '_SlipBase.Fields.Header.注文軽減税.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.注文軽減税).Value)
                '_SlipBase.Fields.Header.注文消費税.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.注文消費税).Value)
                '_SlipBase.Fields.Header.注文送料.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.注文送料).Value)
                '_SlipBase.Fields.Header.注文手数料.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.注文手数料).Value)
                '_SlipBase.Fields.Header.注文ギフト包装代.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.注文ギフト包装代).Value)
                '_SlipBase.Fields.Header.注文値引額.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.注文値引額).Value)
                '_SlipBase.Fields.Header.注文利用ポイント.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.注文利用ポイント).Value)
                '_SlipBase.Fields.Header.注文請求金額.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.注文請求金額).Value)
                _SlipBase.Fields.Header.取込日時.Value = ""
                _SlipBase.Fields.Header.登録日時.Value = ""
                _SlipBase.Fields.Header.更新日時.Value = ""
                _SlipBase.Fields.Header.登録者ID.Value = ""
                _SlipBase.Fields.Header.登録者名.Value = ""
                _SlipBase.Fields.Header.更新者ID.Value = ""
                _SlipBase.Fields.Header.更新者名.Value = ""
                _SlipBase.Fields.Header.承認者ID.Value = ""
                _SlipBase.Fields.Header.承認者名.Value = ""
                _SlipBase.Fields.Header.公開日付.Value = ""
                _SlipBase.Fields.Header.所属CD.Value = ""
                _SlipBase.Fields.Header.検品番号.Value = ""
                _SlipBase.Fields.Header.営業店止め区分.Value = ""
                _SlipBase.Fields.Header.営業店名.Value = ""

                '_SlipBase.Fields.Header.荷主CD.Value = Me.Fields.Header.荷主CD.Value
                _SlipBase.Fields.Header.仮番.Value = 0
                _SlipBase.Fields.Header.位置.Value = i

                '----------------------------------------------------------------------
                ' 固定値
                '----------------------------------------------------------------------
                '_SlipBase.Fields.Header.伝票タイプ.Value = Me.Fields.Header.伝票タイプ.Value
                '_SlipBase.Fields.Header.緊急区分.Value = NodeContents.Constant.CodeValue.緊急区分.緊急
                '_SlipBase.Fields.Header.指示区分.Value = NodeContents.Constant.CodeValue.指示区分.EDI
                '_SlipBase.Fields.Header.指示状況.Value = NodeContents.Constant.CodeValue.指示状況.依頼済み
                _SlipBase.Fields.Header.指示者名.Value = MyBase.Security.ユーザー名
                _SlipBase.Fields.Header.取込日時.Value = strNow
                _SlipBase.Fields.Header.登録日時.Value = strNow
                _SlipBase.Fields.Header.更新日時.Value = strNow
                _SlipBase.Fields.Header.登録者ID.Value = MyBase.Security.ユーザーCD
                _SlipBase.Fields.Header.登録者名.Value = MyBase.Security.ユーザー名
                _SlipBase.Fields.Header.更新者ID.Value = MyBase.Security.ユーザーCD
                _SlipBase.Fields.Header.更新者名.Value = MyBase.Security.ユーザー名


            Catch ex As Exception
                Me.LastError = ex.Message
                Return False
            End Try

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "明細_ﾌﾚｰﾑﾜｰｸｾｯﾄ"
        '''========================================================================================
        ''' <SUMMARY>Excelﾒｲﾝ処理</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Function FrameSet_Detail(ByVal Excel As BaseCore.Common.Excel _
                                         , ByVal i As Long
                                           ) As Boolean
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetTranTableAdapters.T伝票DTTableAdapter
            Dim tbl As New NodeDatabase.DataSetTran.T伝票DTDataTable
            Dim qry As New Collection

            Try
                '----------------------------------------------------------------------
                ' 値設定(明細)
                '----------------------------------------------------------------------
                _SlipBase.Add_Detail1()

                Dim DtlCnt As Integer = _SlipBase.Fields.Detail1.Count - 1

                _SlipBase.Fields.Detail1(DtlCnt).伝票タイプ.Value = ""
                _SlipBase.Fields.Detail1(DtlCnt).伝票番号.Value = ""
                _SlipBase.Fields.Detail1(DtlCnt).行番号.Value = 0
                '_SlipBase.Fields.Detail1(DtlCnt).伝票区分.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.伝票区分).Value, "")
                '_SlipBase.Fields.Detail1(DtlCnt).荷主CD内部.Value = ""
                '_SlipBase.Fields.Detail1(DtlCnt).部門CD.Value = ""
                '_SlipBase.Fields.Detail1(DtlCnt).商品CD内部.Value = ""
                '_SlipBase.Fields.Detail1(DtlCnt).商品CD.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.商品CD).Value, "")
                '_SlipBase.Fields.Detail1(DtlCnt).規格CD1.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.規格CD1).Value, "")
                '_SlipBase.Fields.Detail1(DtlCnt).規格CD2.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.規格CD2).Value, "")
                '_SlipBase.Fields.Detail1(DtlCnt).規格CD3.Value = ""
                '_SlipBase.Fields.Detail1(DtlCnt).規格CD4.Value = ""
                '_SlipBase.Fields.Detail1(DtlCnt).規格CD5.Value = ""
                '_SlipBase.Fields.Detail1(DtlCnt).ロットNO.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.ロットNO).Value, "")
                '_SlipBase.Fields.Detail1(DtlCnt).ロット日付.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.ロット日付).Value, "")
                '_SlipBase.Fields.Detail1(DtlCnt).先方商品NO.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.先方商品NO).Value, "")
                '_SlipBase.Fields.Detail1(DtlCnt).JANCD.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.JANCD).Value, "")
                '_SlipBase.Fields.Detail1(DtlCnt).商品名.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.商品名).Value, "")
                '_SlipBase.Fields.Detail1(DtlCnt).先方商品名.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.先方商品名).Value, "")
                '_SlipBase.Fields.Detail1(DtlCnt).棚番CD.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.棚番CD).Value, "")
                '_SlipBase.Fields.Detail1(DtlCnt).荷姿区分.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.荷姿区分).Value, "")
                '_SlipBase.Fields.Detail1(DtlCnt).数量.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.数量).Value)
                '_SlipBase.Fields.Detail1(DtlCnt).入数.Value = 0
                '_SlipBase.Fields.Detail1(DtlCnt).総数.Value = 0
                '_SlipBase.Fields.Detail1(DtlCnt).単価.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.単価).Value)
                '_SlipBase.Fields.Detail1(DtlCnt).金額.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.金額).Value)
                _SlipBase.Fields.Detail1(DtlCnt).メモ.Value = ""

                _SlipBase.Fields.Detail1(DtlCnt).仮番.Value = 0
                _SlipBase.Fields.Detail1(DtlCnt).位置.Value = i

                '----------------------------------------------------------------------
                ' 固定値
                '----------------------------------------------------------------------
                _SlipBase.Fields.Detail1(DtlCnt).伝票タイプ.Value = _SlipBase.Fields.Header.伝票タイプ.Value
                _SlipBase.Fields.Detail1(DtlCnt).伝票番号.Value = ""
                _SlipBase.Fields.Detail1(DtlCnt).行番号.Value = _SlipBase.Fields.Detail1.Count
                _SlipBase.Fields.Detail1(DtlCnt).伝票区分.Value = _SlipBase.Fields.Header.伝票区分.Value
                _SlipBase.Fields.Detail1(DtlCnt).荷主CD内部.Value = _SlipBase.Fields.Header.荷主CD内部.Value
            Catch ex As Exception
                Me.LastError = ex.Message
                Return False
            End Try

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "ﾌﾚｰﾑﾜｰｸ→ﾜｰｸﾃｰﾌﾞﾙ"
        '''========================================================================================
        ''' <SUMMARY>ﾜｰｸ書込</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Function Write_Work(ByRef intVirtualNo As Integer, ByVal strNow As String) As Boolean
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------

            Dim ada As New NodeDatabase.DataSetWorkTableAdapters.W乗務記録TableAdapter
            'Dim adaDT As New NodeDatabase.DataSetWorkTableAdapters.W伝票DT一括TableAdapter
            'Dim adaDT As New NodeDatabase.DataSetWorkTableAdapters.W伝票DT一括TableAdapter

            '----------------------------------------------------------------------
            ' 事前準備
            '----------------------------------------------------------------------
            '_SlipBase.Fields.Header.更新日時.Value = strNow
            '_SlipBase.Fields.Header.更新者名.Value = Me.Security.ユーザー名

            '仮番ｶｳﾝﾄｱｯﾌﾟ
            intVirtualNo += 1

            '----------------------------------------------------------------------
            ' ﾜｰｸ書込(ﾍｯﾀﾞ)
            '----------------------------------------------------------------------
            Try
                'adaHD.Insert(Me.Security.グループCD _
                '           , Me.Security.個社CD _
                '           , Me.Security.子会社CD _
                '           , Me.Security.セッションID _
                '           , _SlipBase.Fields.Header.伝票タイプ.Value _
                '           , _SlipBase.Fields.Header.伝票番号.Value _
                '           , _SlipBase.Fields.Header.入力日付.Value _
                '           , _SlipBase.Fields.Header.伝票日付.Value _
                '           , _SlipBase.Fields.Header.伝票区分.Value _
                '           , _SlipBase.Fields.Header.緊急区分.Value _
                '           , _SlipBase.Fields.Header.指示番号.Value _
                '           , _SlipBase.Fields.Header.指示区分.Value _
                '           , _SlipBase.Fields.Header.指示状況.Value _
                '           , _SlipBase.Fields.Header.指示者名.Value _
                '           , _SlipBase.Fields.Header.荷主CD内部.Value _
                '           , _SlipBase.Fields.Header.拠点CD.Value _
                '           , _SlipBase.Fields.Header.企業CD.Value _
                '           , _SlipBase.Fields.Header.届先CD.Value _
                '           , _SlipBase.Fields.Header.届先名1.Value _
                '           , _SlipBase.Fields.Header.届先名2.Value _
                '           , _SlipBase.Fields.Header.届先郵便番号.Value _
                '           , _SlipBase.Fields.Header.届先住所1.Value _
                '           , _SlipBase.Fields.Header.届先住所2.Value _
                '           , _SlipBase.Fields.Header.届先TEL.Value _
                '           , _SlipBase.Fields.Header.摘要CD1.Value _
                '           , _SlipBase.Fields.Header.摘要名1.Value _
                '           , _SlipBase.Fields.Header.摘要CD2.Value _
                '           , _SlipBase.Fields.Header.摘要名2.Value _
                '           , _SlipBase.Fields.Header.運送会社CD.Value _
                '           , _SlipBase.Fields.Header.配達方法.Value _
                '           , _SlipBase.Fields.Header.配達日付.Value _
                '           , _SlipBase.Fields.Header.配達時刻区分.Value _
                '           , BaseCore.Common.Text.CVal(_SlipBase.Fields.Header.代引金額.Value) _
                '           , _SlipBase.Fields.Header.送り状区分.Value _
                '           , _SlipBase.Fields.Header.送り状NO.Value _
                '           , _SlipBase.Fields.Header.車番.Value _
                '           , BaseCore.Common.Text.CVal(_SlipBase.Fields.Header.便.Value) _
                '           , BaseCore.Common.Text.CVal(_SlipBase.Fields.Header.運送扱数.Value) _
                '           , BaseCore.Common.Text.CVal(_SlipBase.Fields.Header.運送単価.Value) _
                '           , BaseCore.Common.Text.CVal(_SlipBase.Fields.Header.運送金額.Value) _
                '           , _SlipBase.Fields.Header.注文ショップCD.Value _
                '           , _SlipBase.Fields.Header.注文NO.Value _
                '           , _SlipBase.Fields.Header.注文日付.Value _
                '           , _SlipBase.Fields.Header.注文名1.Value _
                '           , _SlipBase.Fields.Header.注文名2.Value _
                '           , _SlipBase.Fields.Header.注文会社名.Value _
                '           , _SlipBase.Fields.Header.注文部署名.Value _
                '           , _SlipBase.Fields.Header.注文郵便番号.Value _
                '           , _SlipBase.Fields.Header.注文住所1.Value _
                '           , _SlipBase.Fields.Header.注文住所2.Value _
                '           , _SlipBase.Fields.Header.注文TEL.Value _
                '           , _SlipBase.Fields.Header.注文メールアドレス.Value _
                '           , _SlipBase.Fields.Header.注文支払区分.Value _
                '           , _SlipBase.Fields.Header.注文入金状況.Value _
                '           , _SlipBase.Fields.Header.注文ギフト包装区分.Value _
                '           , _SlipBase.Fields.Header.注文ギフトメッセージ.Value _
                '           , _SlipBase.Fields.Header.注文のし表書き区分.Value _
                '           , _SlipBase.Fields.Header.注文のし名入れ.Value _
                '           , BaseCore.Common.Text.CVal(_SlipBase.Fields.Header.注文品代.Value) _
                '           , BaseCore.Common.Text.CVal(_SlipBase.Fields.Header.注文軽減税.Value) _
                '           , BaseCore.Common.Text.CVal(_SlipBase.Fields.Header.注文消費税.Value) _
                '           , BaseCore.Common.Text.CVal(_SlipBase.Fields.Header.注文送料.Value) _
                '           , BaseCore.Common.Text.CVal(_SlipBase.Fields.Header.注文手数料.Value) _
                '           , BaseCore.Common.Text.CVal(_SlipBase.Fields.Header.注文ギフト包装代.Value) _
                '           , BaseCore.Common.Text.CVal(_SlipBase.Fields.Header.注文値引額.Value) _
                '           , BaseCore.Common.Text.CVal(_SlipBase.Fields.Header.注文利用ポイント.Value) _
                '           , BaseCore.Common.Text.CVal(_SlipBase.Fields.Header.注文請求金額.Value) _
                '           , _SlipBase.Fields.Header.取込日時.Value _
                '           , _SlipBase.Fields.Header.登録日時.Value _
                '           , _SlipBase.Fields.Header.更新日時.Value _
                '           , _SlipBase.Fields.Header.登録者ID.Value _
                '           , _SlipBase.Fields.Header.登録者名.Value _
                '           , _SlipBase.Fields.Header.更新者ID.Value _
                '           , _SlipBase.Fields.Header.更新者名.Value _
                '           , _SlipBase.Fields.Header.承認者ID.Value _
                '           , _SlipBase.Fields.Header.承認者名.Value _
                '           , _SlipBase.Fields.Header.公開日付.Value _
                '           , _SlipBase.Fields.Header.所属CD.Value _
                '           , _SlipBase.Fields.Header.検品番号.Value _
                '           , _SlipBase.Fields.Header.営業店止め区分.Value _
                '           , _SlipBase.Fields.Header.営業店名.Value _
                '           , _SlipBase.Fields.Header.荷主CD.Value _
                '           , intVirtualNo _
                '           , BaseCore.Common.Text.CVal(_SlipBase.Fields.Header.位置.Value) _
                '           , "", "", "", "", "", "", "", "", "", "" _
                '           , "", "", "", "", "", "", "", "", "", "" _
                '           , "", "", "", "", "", "", "", "", "", "" _
                '           , "", "", "", "", "", "", "", "", "", "" _
                '           , "", "", "", "", "", "", "", "", "", "" _
                '           , "", "", "", "", "", "", "", "", "", "" _
                '           , "", "", "", "", "", "", "", "", "", "" _
                '           , "", "", "", "", "", "", "", "", ""
                '            )
            Catch ex As Exception
                Me.LastError = "以下のエラーのため、処理を継続できません。" & vbCrLf & ex.Message
                Return False
            End Try

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region
#End Region

#Region "其他"
#Region "事前検査"
        '''========================================================================================
        ''' <SUMMARY>Excelﾒｲﾝ処理</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Function Excel_Validator(ByVal Excel As BaseCore.Common.Excel) As Boolean
            ''----------------------------------------------------------------------
            '' 変数定義
            ''----------------------------------------------------------------------
            'Dim ada項目 As New NodeDatabase.DataSetMasterTableAdapters.M伝票項目TableAdapter
            'Dim tbl項目 As New NodeDatabase.DataSetMaster.M伝票項目DataTable
            'Dim qry項目 As New Collection

            'Dim tbl検査HD As New NodeDatabase.DataSetTran.T伝票HDDataTable
            'Dim tbl検査DT As New NodeDatabase.DataSetTran.T伝票DTDataTable

            ''----------------------------------------------------------------------
            '' 項目一覧読込
            ''----------------------------------------------------------------------
            'qry項目.Clear()
            'qry項目.Add(New BaseDatabase.Condition(tbl項目.伝票タイプColumn.ColumnName, Me.Fields.Header.伝票タイプ.Value, BaseDatabase.Contents.Compare.Equal))
            'tbl項目 = ada項目.SelectByCommon(qry項目)

            ''----------------------------------------------------------------------
            '' ﾌﾚｰﾑﾜｰｸ定義(必須 & 半角/全角)
            ''----------------------------------------------------------------------
            'Dim Validator As New BaseCore.Common.Validator
            'Dim blnErr As Boolean = False
            'Dim LastRow As Integer = Excel.LastRowIndex        '最大行
            'Dim LastCol As Integer = Excel.LastColIndex        '最大列

            'For i As Long = ConstantExcel.Header.RowTop To LastRow
            '    ' ﾀｲﾄﾙ行 
            '    If i = 1 Then
            '        Continue For
            '    End If

            '    ' ｴﾗｰｸﾘｱ
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.入力日付)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.伝票日付)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.伝票区分)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.指示番号)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.企業CD)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.届先CD)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.届先名1)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.届先名2)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.届先郵便番号)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.届先住所1)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.届先住所2)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.届先TEL)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.摘要CD1)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.摘要名1)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.摘要CD2)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.摘要名2)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.運送会社CD)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.配達日付)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.配達時刻区分)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.代引金額)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.送り状No)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.注文NO)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.注文日付)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.注文名1)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.注文名2)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.注文会社名)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.注文部署名)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.注文郵便番号)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.注文住所1)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.注文住所2)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.注文TEL)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.注文メールアドレス)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.注文日時)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.注文支払区分)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.注文入金状況)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.注文ギフト包装区分)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.注文ギフトメッセージ)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.注文のし表書き区分)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.注文のし名入れ)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.注文品代)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.注文消費税)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.注文送料)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.注文手数料)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.注文ギフト包装代)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.注文値引額)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.注文利用ポイント)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.注文請求金額)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.注文ショップCD)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.注文軽減税)

            '    Excel.CellClearError(i, ConstantExcel.Header.列数.商品CD)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.規格CD1)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.規格CD2)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.先方商品NO)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.JANCD)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.ロットNO)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.ロット日付)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.商品名)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.先方商品名)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.棚番CD)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.荷姿区分)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.数量)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.単価)
            '    Excel.CellClearError(i, ConstantExcel.Header.列数.金額)

            '    For Each row項目 As NodeDatabase.DataSetMaster.M伝票項目Row In tbl項目.Rows
            '        Dim FieldName As String = row項目.Item(tbl項目.伝票項目名Column.ColumnName)
            '        Dim Required As Boolean = (row項目.Item(tbl項目.必須フラグColumn.ColumnName) = NodeContents.Constant.CodeValue.フラグID.オン)

            '        Select Case FieldName.ToString
            '            Case tbl検査HD.入力日付Column.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.入力日付).Value, "").ToString, tbl検査HD.入力日付Column, Required) Then Excel.CellSetError(i, ConstantExcel.Header.列数.入力日付, Me.LastError) : blnErr = True
            '            Case tbl検査HD.伝票日付Column.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.伝票日付).Value, "").ToString, tbl検査HD.伝票日付Column, Required) Then Excel.CellSetError(i, ConstantExcel.Header.列数.伝票日付, Me.LastError) : blnErr = True
            '            Case tbl検査HD.伝票区分Column.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.伝票区分).Value, "").ToString, tbl検査HD.伝票区分Column, Required) Then Excel.CellSetError(i, ConstantExcel.Header.列数.伝票区分, Me.LastError) : blnErr = True
            '            Case tbl検査HD.指示番号Column.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.指示番号).Value, "").ToString, tbl検査HD.指示番号Column, Required) Then Excel.CellSetError(i, ConstantExcel.Header.列数.指示番号, Me.LastError) : blnErr = True
            '            Case tbl検査HD.企業CDColumn.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.企業CD).Value, "").ToString, tbl検査HD.企業CDColumn, Required) Then Excel.CellSetError(i, ConstantExcel.Header.列数.企業CD, Me.LastError) : blnErr = True
            '            Case tbl検査HD.届先CDColumn.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.届先CD).Value, "").ToString, tbl検査HD.届先CDColumn, Required) Then Excel.CellSetError(i, ConstantExcel.Header.列数.届先CD, Me.LastError) : blnErr = True
            '            Case tbl検査HD.届先名1Column.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.届先名1).Value, "").ToString, tbl検査HD.届先名1Column, Required) Then Excel.CellSetError(i, ConstantExcel.Header.列数.届先名1, Me.LastError) : blnErr = True
            '            Case tbl検査HD.届先名2Column.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.届先名2).Value, "").ToString, tbl検査HD.届先名2Column, Required) Then Excel.CellSetError(i, ConstantExcel.Header.列数.届先名2, Me.LastError) : blnErr = True
            '            Case tbl検査HD.届先郵便番号Column.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.届先郵便番号).Value, "").ToString, tbl検査HD.届先郵便番号Column, Required) Then Excel.CellSetError(i, ConstantExcel.Header.列数.届先郵便番号, Me.LastError) : blnErr = True
            '            Case tbl検査HD.届先住所1Column.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.届先住所1).Value, "").ToString, tbl検査HD.届先住所1Column, Required) Then Excel.CellSetError(i, ConstantExcel.Header.列数.届先住所1, Me.LastError) : blnErr = True
            '            Case tbl検査HD.届先住所2Column.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.届先住所2).Value, "").ToString, tbl検査HD.届先住所2Column, Required) Then Excel.CellSetError(i, ConstantExcel.Header.列数.届先住所2, Me.LastError) : blnErr = True
            '            Case tbl検査HD.届先TELColumn.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.届先TEL).Value, "").ToString, tbl検査HD.届先TELColumn, Required) Then Excel.CellSetError(i, ConstantExcel.Header.列数.届先TEL, Me.LastError) : blnErr = True
            '            Case tbl検査HD.摘要CD1Column.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.摘要CD1).Value, "").ToString, tbl検査HD.摘要CD1Column, Required) Then Excel.CellSetError(i, ConstantExcel.Header.列数.摘要CD1, Me.LastError) : blnErr = True
            '            Case tbl検査HD.摘要名1Column.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.摘要名1).Value, "").ToString, tbl検査HD.摘要名1Column, Required) Then Excel.CellSetError(i, ConstantExcel.Header.列数.摘要名1, Me.LastError) : blnErr = True
            '            Case tbl検査HD.摘要CD2Column.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.摘要CD2).Value, "").ToString, tbl検査HD.摘要CD2Column, Required) Then Excel.CellSetError(i, ConstantExcel.Header.列数.摘要CD2, Me.LastError) : blnErr = True
            '            Case tbl検査HD.摘要名2Column.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.摘要名2).Value, "").ToString, tbl検査HD.摘要名2Column, Required) Then Excel.CellSetError(i, ConstantExcel.Header.列数.摘要名2, Me.LastError) : blnErr = True
            '            Case tbl検査HD.運送会社CDColumn.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.運送会社CD).Value, "").ToString, tbl検査HD.運送会社CDColumn, Required) Then Excel.CellSetError(i, ConstantExcel.Header.列数.運送会社CD, Me.LastError) : blnErr = True
            '            Case tbl検査HD.配達日付Column.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.配達日付).Value, "").ToString, tbl検査HD.配達日付Column, Required) Then Excel.CellSetError(i, ConstantExcel.Header.列数.配達日付, Me.LastError) : blnErr = True
            '            Case tbl検査HD.配達時刻区分Column.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.配達時刻区分).Value, "").ToString, tbl検査HD.配達時刻区分Column, Required) Then Excel.CellSetError(i, ConstantExcel.Header.列数.配達時刻区分, Me.LastError) : blnErr = True
            '            Case tbl検査HD.代引金額Column.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.代引金額).Value), tbl検査HD.代引金額Column, Required, Me.Config.桁数金額整数, Me.Config.桁数金額小数) Then Excel.CellSetError(i, ConstantExcel.Header.列数.代引金額, Me.LastError) : blnErr = True
            '            Case tbl検査HD.送り状NOColumn.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.送り状No).Value, "").ToString, tbl検査HD.送り状NOColumn, Required) Then Excel.CellSetError(i, ConstantExcel.Header.列数.送り状No, Me.LastError) : blnErr = True
            '            Case tbl検査HD.注文NOColumn.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.注文NO).Value, "").ToString, tbl検査HD.注文NOColumn, Required) Then Excel.CellSetError(i, ConstantExcel.Header.列数.注文NO, Me.LastError) : blnErr = True
            '            Case tbl検査HD.注文日付Column.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.注文日付).Value, "").ToString, tbl検査HD.注文日付Column, Required) Then Excel.CellSetError(i, ConstantExcel.Header.列数.注文日付, Me.LastError) : blnErr = True
            '            Case tbl検査HD.注文名1Column.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.注文名1).Value, "").ToString, tbl検査HD.注文名1Column, Required) Then Excel.CellSetError(i, ConstantExcel.Header.列数.注文名1, Me.LastError) : blnErr = True
            '            Case tbl検査HD.注文名2Column.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.注文名2).Value, "").ToString, tbl検査HD.注文名2Column, Required) Then Excel.CellSetError(i, ConstantExcel.Header.列数.注文名2, Me.LastError) : blnErr = True
            '            Case tbl検査HD.注文子会社名Column.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.注文会社名).Value, "").ToString, tbl検査HD.注文子会社名Column, Required) Then Excel.CellSetError(i, ConstantExcel.Header.列数.注文会社名, Me.LastError) : blnErr = True
            '            Case tbl検査HD.注文部署名Column.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.注文部署名).Value, "").ToString, tbl検査HD.注文部署名Column, Required) Then Excel.CellSetError(i, ConstantExcel.Header.列数.注文部署名, Me.LastError) : blnErr = True
            '            Case tbl検査HD.注文郵便番号Column.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.注文郵便番号).Value, "").ToString, tbl検査HD.注文郵便番号Column, Required) Then Excel.CellSetError(i, ConstantExcel.Header.列数.注文郵便番号, Me.LastError) : blnErr = True
            '            Case tbl検査HD.注文住所1Column.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.注文住所1).Value, "").ToString, tbl検査HD.注文住所1Column, Required) Then Excel.CellSetError(i, ConstantExcel.Header.列数.注文住所1, Me.LastError) : blnErr = True
            '            Case tbl検査HD.注文住所2Column.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.注文住所2).Value, "").ToString, tbl検査HD.注文住所2Column, Required) Then Excel.CellSetError(i, ConstantExcel.Header.列数.注文住所2, Me.LastError) : blnErr = True
            '            Case tbl検査HD.注文TELColumn.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.注文TEL).Value, "").ToString, tbl検査HD.注文TELColumn, Required) Then Excel.CellSetError(i, ConstantExcel.Header.列数.注文TEL, Me.LastError) : blnErr = True
            '            Case tbl検査HD.注文メールアドレスColumn.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.注文メールアドレス).Value, "").ToString, tbl検査HD.注文メールアドレスColumn, Required) Then Excel.CellSetError(i, ConstantExcel.Header.列数.注文メールアドレス, Me.LastError) : blnErr = True
            '            Case tbl検査HD.注文支払区分Column.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.注文支払区分).Value, "").ToString, tbl検査HD.注文支払区分Column, Required) Then Excel.CellSetError(i, ConstantExcel.Header.列数.注文支払区分, Me.LastError) : blnErr = True
            '            Case tbl検査HD.注文入金状況Column.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.注文入金状況).Value, "").ToString, tbl検査HD.注文入金状況Column, Required) Then Excel.CellSetError(i, ConstantExcel.Header.列数.注文入金状況, Me.LastError) : blnErr = True
            '            Case tbl検査HD.注文ギフト包装区分Column.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.注文ギフト包装区分).Value, "").ToString, tbl検査HD.注文ギフト包装区分Column, Required) Then Excel.CellSetError(i, ConstantExcel.Header.列数.注文ギフト包装区分, Me.LastError) : blnErr = True
            '            Case tbl検査HD.注文ギフトメッセージColumn.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.注文ギフトメッセージ).Value, "").ToString, tbl検査HD.注文ギフトメッセージColumn, Required) Then Excel.CellSetError(i, ConstantExcel.Header.列数.注文ギフトメッセージ, Me.LastError) : blnErr = True
            '            Case tbl検査HD.注文のし表書き区分Column.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.注文のし表書き区分).Value, "").ToString, tbl検査HD.注文のし表書き区分Column, Required) Then Excel.CellSetError(i, ConstantExcel.Header.列数.注文のし表書き区分, Me.LastError) : blnErr = True
            '            Case tbl検査HD.注文のし名入れColumn.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.注文のし名入れ).Value, "").ToString, tbl検査HD.注文のし名入れColumn, Required) Then Excel.CellSetError(i, ConstantExcel.Header.列数.注文のし名入れ, Me.LastError) : blnErr = True
            '            Case tbl検査HD.注文品代Column.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.注文品代).Value), tbl検査HD.注文品代Column, Required, Me.Config.桁数金額整数, Me.Config.桁数金額小数) Then Excel.CellSetError(i, ConstantExcel.Header.列数.注文品代, Me.LastError) : blnErr = True
            '            Case tbl検査HD.注文消費税Column.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.注文消費税).Value), tbl検査HD.注文消費税Column, Required, Me.Config.桁数金額整数, Me.Config.桁数金額小数) Then Excel.CellSetError(i, ConstantExcel.Header.列数.注文消費税, Me.LastError) : blnErr = True
            '            Case tbl検査HD.注文送料Column.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.注文送料).Value), tbl検査HD.注文送料Column, Required, Me.Config.桁数金額整数, Me.Config.桁数金額小数) Then Excel.CellSetError(i, ConstantExcel.Header.列数.注文送料, Me.LastError) : blnErr = True
            '            Case tbl検査HD.注文手数料Column.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.注文手数料).Value), tbl検査HD.注文手数料Column, Required, Me.Config.桁数金額整数, Me.Config.桁数金額小数) Then Excel.CellSetError(i, ConstantExcel.Header.列数.注文手数料, Me.LastError) : blnErr = True
            '            Case tbl検査HD.注文ギフト包装代Column.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.注文ギフト包装代).Value), tbl検査HD.注文ギフト包装代Column, Required, Me.Config.桁数金額整数, Me.Config.桁数金額小数) Then Excel.CellSetError(i, ConstantExcel.Header.列数.注文ギフト包装代, Me.LastError) : blnErr = True
            '            Case tbl検査HD.注文値引額Column.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.注文値引額).Value), tbl検査HD.注文値引額Column, Required, Me.Config.桁数金額整数, Me.Config.桁数金額小数) Then Excel.CellSetError(i, ConstantExcel.Header.列数.注文値引額, Me.LastError) : blnErr = True
            '            Case tbl検査HD.注文利用ポイントColumn.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.注文利用ポイント).Value), tbl検査HD.注文利用ポイントColumn, Required, Me.Config.桁数金額整数, Me.Config.桁数金額小数) Then Excel.CellSetError(i, ConstantExcel.Header.列数.注文利用ポイント, Me.LastError) : blnErr = True
            '            Case tbl検査HD.注文請求金額Column.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.注文請求金額).Value), tbl検査HD.注文請求金額Column, Required, Me.Config.桁数金額整数, Me.Config.桁数金額小数) Then Excel.CellSetError(i, ConstantExcel.Header.列数.注文請求金額, Me.LastError) : blnErr = True
            '            Case tbl検査HD.注文ショップCDColumn.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.注文ショップCD).Value, "").ToString, tbl検査HD.注文ショップCDColumn, Required) Then Excel.CellSetError(i, ConstantExcel.Header.列数.注文ショップCD, Me.LastError) : blnErr = True
            '            Case tbl検査HD.注文軽減税Column.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.注文軽減税).Value), tbl検査HD.注文軽減税Column, Required, Me.Config.桁数金額整数, Me.Config.桁数金額小数) Then Excel.CellSetError(i, ConstantExcel.Header.列数.注文軽減税, Me.LastError) : blnErr = True

            '            Case tbl検査DT.商品CDColumn.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.商品CD).Value, "").ToString, tbl検査DT.商品CDColumn, Required) Then Excel.CellSetError(i, ConstantExcel.Header.列数.商品CD, Me.LastError) : blnErr = True
            '            Case tbl検査DT.規格CD1Column.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.規格CD1).Value, "").ToString, tbl検査DT.規格CD1Column, Required) Then Excel.CellSetError(i, ConstantExcel.Header.列数.規格CD1, Me.LastError) : blnErr = True
            '            Case tbl検査DT.規格CD2Column.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.規格CD2).Value, "").ToString, tbl検査DT.規格CD2Column, Required) Then Excel.CellSetError(i, ConstantExcel.Header.列数.規格CD2, Me.LastError) : blnErr = True
            '            Case tbl検査DT.先方商品NOColumn.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.先方商品NO).Value, "").ToString, tbl検査DT.先方商品NOColumn, Required) Then Excel.CellSetError(i, ConstantExcel.Header.列数.先方商品NO, Me.LastError) : blnErr = True
            '            Case tbl検査DT.JANCDColumn.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.JANCD).Value, "").ToString, tbl検査DT.JANCDColumn, Required) Then Excel.CellSetError(i, ConstantExcel.Header.列数.JANCD, Me.LastError) : blnErr = True
            '            Case tbl検査DT.ロットNOColumn.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.ロットNO).Value, "").ToString, tbl検査DT.ロットNOColumn, Required) Then Excel.CellSetError(i, ConstantExcel.Header.列数.ロットNO, Me.LastError) : blnErr = True
            '            Case tbl検査DT.ロット日付Column.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.ロット日付).Value, "").ToString, tbl検査DT.ロット日付Column, Required) Then Excel.CellSetError(i, ConstantExcel.Header.列数.ロット日付, Me.LastError) : blnErr = True
            '            Case tbl検査DT.商品名Column.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.商品名).Value, "").ToString, tbl検査DT.商品名Column, Required) Then Excel.CellSetError(i, ConstantExcel.Header.列数.商品名, Me.LastError) : blnErr = True
            '            Case tbl検査DT.先方商品名Column.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.先方商品名).Value, "").ToString, tbl検査DT.先方商品名Column, Required) Then Excel.CellSetError(i, ConstantExcel.Header.列数.先方商品名, Me.LastError) : blnErr = True
            '            Case tbl検査DT.棚番CDColumn.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.棚番CD).Value, "").ToString, tbl検査DT.棚番CDColumn, Required) Then Excel.CellSetError(i, ConstantExcel.Header.列数.棚番CD, Me.LastError) : blnErr = True
            '            Case tbl検査DT.荷姿区分Column.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.荷姿区分).Value, "").ToString, tbl検査DT.荷姿区分Column, Required) Then Excel.CellSetError(i, ConstantExcel.Header.列数.荷姿区分, Me.LastError) : blnErr = True
            '            Case tbl検査DT.数量Column.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.数量).Value), tbl検査DT.数量Column, Required, 15, 3) Then Excel.CellSetError(i, ConstantExcel.Header.列数.数量, Me.LastError) : blnErr = True
            '            Case tbl検査DT.単価Column.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.単価).Value), tbl検査DT.単価Column, Required, 15, 3) Then Excel.CellSetError(i, ConstantExcel.Header.列数.単価, Me.LastError) : blnErr = True
            '            Case tbl検査DT.金額Column.ColumnName : If Not Validator.BaseChecker(Me.LastError, BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.金額).Value), tbl検査DT.金額Column, Required, 15, 3) Then Excel.CellSetError(i, ConstantExcel.Header.列数.金額, Me.LastError) : blnErr = True
            '            Case Else
            '        End Select
            '    Next
            'Next

            'If blnErr Then
            '    Return False
            'End If

            '------------------------------------------------------------------
            ' 返却
            '------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "ｴﾗｰ設定"
        '''========================================================================================
        ''' <SUMMARY>ﾜｰｸﾃｰﾌﾞﾙ(ｽﾄｱﾄﾞ結果)→ﾌﾚｰﾑﾜｰｸ</SUMMARY>   
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Public Function SetError(ByRef Excel As BaseCore.Common.Excel) As Boolean
            ''----------------------------------------------------------------------
            '' 変数定義
            ''----------------------------------------------------------------------
            'Dim adaHD As New NodeDatabase.DataSetWorkTableAdapters.W伝票HD一括TableAdapter
            'Dim tblHD As New NodeDatabase.DataSetWork.W伝票HD一括DataTable
            'Dim qryHD As New Collection

            'Dim adaDT As New NodeDatabase.DataSetWorkTableAdapters.W伝票DT一括TableAdapter
            'Dim tblDT As New NodeDatabase.DataSetWork.W伝票DT一括DataTable
            'Dim qryDT As New Collection

            ''----------------------------------------------------------------------
            '' T伝票HD
            ''----------------------------------------------------------------------
            'qryHD.Clear()
            'qryHD.Add(New BaseDatabase.Condition(tblHD.セッションIDColumn.ColumnName, Me.Security.セッションID, BaseDatabase.Contents.Compare.Equal))
            'qryHD.Add(New BaseDatabase.Condition(tblHD.Msg全体Column.ColumnName, "", BaseDatabase.Contents.Compare.NotEqual))
            'tblHD = adaHD.SelectByCommon(qryHD)
            'For Each row As DataRow In tblHD.Rows
            '    If row.Item(tblHD.Msg入力日付Column.ColumnName) <> "" Then
            '        Excel.CellSetError(row.Item(tblHD.位置Column.ColumnName), ConstantExcel.Header.列数.入力日付, row.Item(tblHD.Msg入力日付Column.ColumnName))
            '    End If

            '    If row.Item(tblHD.Msg伝票日付Column.ColumnName) <> "" Then
            '        Excel.CellSetError(row.Item(tblHD.位置Column.ColumnName), ConstantExcel.Header.列数.伝票日付, row.Item(tblHD.Msg伝票日付Column.ColumnName))
            '    End If

            '    If row.Item(tblHD.Msg伝票区分Column.ColumnName) <> "" Then
            '        Excel.CellSetError(row.Item(tblHD.位置Column.ColumnName), ConstantExcel.Header.列数.伝票区分, row.Item(tblHD.Msg伝票区分Column.ColumnName))
            '    End If

            '    If row.Item(tblHD.Msg指示番号Column.ColumnName) <> "" Then
            '        Excel.CellSetError(row.Item(tblHD.位置Column.ColumnName), ConstantExcel.Header.列数.指示番号, row.Item(tblHD.Msg指示番号Column.ColumnName))
            '    End If

            '    If row.Item(tblHD.Msg荷主CDColumn.ColumnName) <> "" Then
            '        Excel.CellSetError(row.Item(tblHD.位置Column.ColumnName), ConstantExcel.Header.列数.荷主CD, row.Item(tblHD.Msg荷主CDColumn.ColumnName))
            '    End If

            '    If row.Item(tblHD.Msg企業CDColumn.ColumnName) <> "" Then
            '        Excel.CellSetError(row.Item(tblHD.位置Column.ColumnName), ConstantExcel.Header.列数.企業CD, row.Item(tblHD.Msg企業CDColumn.ColumnName))
            '    End If

            '    If row.Item(tblHD.Msg届先CDColumn.ColumnName) <> "" Then
            '        Excel.CellSetError(row.Item(tblHD.位置Column.ColumnName), ConstantExcel.Header.列数.届先CD, row.Item(tblHD.Msg届先CDColumn.ColumnName))
            '    End If

            '    If row.Item(tblHD.Msg届先名1Column.ColumnName) <> "" Then
            '        Excel.CellSetError(row.Item(tblHD.位置Column.ColumnName), ConstantExcel.Header.列数.届先名1, row.Item(tblHD.Msg届先名1Column.ColumnName))
            '    End If

            '    If row.Item(tblHD.Msg届先名2Column.ColumnName) <> "" Then
            '        Excel.CellSetError(row.Item(tblHD.位置Column.ColumnName), ConstantExcel.Header.列数.届先名2, row.Item(tblHD.Msg届先名2Column.ColumnName))
            '    End If

            '    If row.Item(tblHD.Msg届先郵便番号Column.ColumnName) <> "" Then
            '        Excel.CellSetError(row.Item(tblHD.位置Column.ColumnName), ConstantExcel.Header.列数.届先郵便番号, row.Item(tblHD.Msg届先郵便番号Column.ColumnName))
            '    End If

            '    If row.Item(tblHD.Msg届先住所1Column.ColumnName) <> "" Then
            '        Excel.CellSetError(row.Item(tblHD.位置Column.ColumnName), ConstantExcel.Header.列数.届先住所1, row.Item(tblHD.Msg届先住所1Column.ColumnName))
            '    End If

            '    If row.Item(tblHD.Msg届先住所2Column.ColumnName) <> "" Then
            '        Excel.CellSetError(row.Item(tblHD.位置Column.ColumnName), ConstantExcel.Header.列数.届先住所2, row.Item(tblHD.Msg届先住所2Column.ColumnName))
            '    End If

            '    If row.Item(tblHD.Msg届先TELColumn.ColumnName) <> "" Then
            '        Excel.CellSetError(row.Item(tblHD.位置Column.ColumnName), ConstantExcel.Header.列数.届先TEL, row.Item(tblHD.Msg届先TELColumn.ColumnName))
            '    End If

            '    If row.Item(tblHD.Msg摘要CD1Column.ColumnName) <> "" Then
            '        Excel.CellSetError(row.Item(tblHD.位置Column.ColumnName), ConstantExcel.Header.列数.摘要CD1, row.Item(tblHD.Msg摘要CD1Column.ColumnName))
            '    End If

            '    If row.Item(tblHD.Msg摘要名1Column.ColumnName) <> "" Then
            '        Excel.CellSetError(row.Item(tblHD.位置Column.ColumnName), ConstantExcel.Header.列数.摘要名1, row.Item(tblHD.Msg摘要名1Column.ColumnName))
            '    End If

            '    If row.Item(tblHD.Msg摘要CD2Column.ColumnName) <> "" Then
            '        Excel.CellSetError(row.Item(tblHD.位置Column.ColumnName), ConstantExcel.Header.列数.摘要CD2, row.Item(tblHD.Msg摘要CD2Column.ColumnName))
            '    End If

            '    If row.Item(tblHD.Msg摘要名2Column.ColumnName) <> "" Then
            '        Excel.CellSetError(row.Item(tblHD.位置Column.ColumnName), ConstantExcel.Header.列数.摘要名2, row.Item(tblHD.Msg摘要名2Column.ColumnName))
            '    End If

            '    If row.Item(tblHD.Msg運送会社CDColumn.ColumnName) <> "" Then
            '        Excel.CellSetError(row.Item(tblHD.位置Column.ColumnName), ConstantExcel.Header.列数.運送会社CD, row.Item(tblHD.Msg運送会社CDColumn.ColumnName))
            '    End If

            '    If row.Item(tblHD.Msg配達日付Column.ColumnName) <> "" Then
            '        Excel.CellSetError(row.Item(tblHD.位置Column.ColumnName), ConstantExcel.Header.列数.配達日付, row.Item(tblHD.Msg配達日付Column.ColumnName))
            '    End If

            '    If row.Item(tblHD.Msg配達時刻区分Column.ColumnName) <> "" Then
            '        Excel.CellSetError(row.Item(tblHD.位置Column.ColumnName), ConstantExcel.Header.列数.配達時刻区分, row.Item(tblHD.Msg配達時刻区分Column.ColumnName))
            '    End If

            '    If row.Item(tblHD.Msg代引金額Column.ColumnName) <> "" Then
            '        Excel.CellSetError(row.Item(tblHD.位置Column.ColumnName), ConstantExcel.Header.列数.代引金額, row.Item(tblHD.Msg代引金額Column.ColumnName))
            '    End If

            '    If row.Item(tblHD.Msg送り状NOColumn.ColumnName) <> "" Then
            '        Excel.CellSetError(row.Item(tblHD.位置Column.ColumnName), ConstantExcel.Header.列数.送り状No, row.Item(tblHD.Msg送り状NOColumn.ColumnName))
            '    End If

            '    If row.Item(tblHD.Msg注文NOColumn.ColumnName) <> "" Then
            '        Excel.CellSetError(row.Item(tblHD.位置Column.ColumnName), ConstantExcel.Header.列数.注文NO, row.Item(tblHD.Msg注文NOColumn.ColumnName))
            '    End If

            '    If row.Item(tblHD.Msg注文日付Column.ColumnName) <> "" Then
            '        Excel.CellSetError(row.Item(tblHD.位置Column.ColumnName), ConstantExcel.Header.列数.注文日付, row.Item(tblHD.Msg注文日付Column.ColumnName))
            '    End If

            '    If row.Item(tblHD.Msg注文名1Column.ColumnName) <> "" Then
            '        Excel.CellSetError(row.Item(tblHD.位置Column.ColumnName), ConstantExcel.Header.列数.注文名1, row.Item(tblHD.Msg注文名1Column.ColumnName))
            '    End If

            '    If row.Item(tblHD.Msg注文名2Column.ColumnName) <> "" Then
            '        Excel.CellSetError(row.Item(tblHD.位置Column.ColumnName), ConstantExcel.Header.列数.注文名2, row.Item(tblHD.Msg注文名2Column.ColumnName))
            '    End If

            '    If row.Item(tblHD.Msg注文子会社名Column.ColumnName) <> "" Then
            '        Excel.CellSetError(row.Item(tblHD.位置Column.ColumnName), ConstantExcel.Header.列数.注文会社名, row.Item(tblHD.Msg注文子会社名Column.ColumnName))
            '    End If

            '    If row.Item(tblHD.Msg注文部署名Column.ColumnName) <> "" Then
            '        Excel.CellSetError(row.Item(tblHD.位置Column.ColumnName), ConstantExcel.Header.列数.注文部署名, row.Item(tblHD.Msg注文部署名Column.ColumnName))
            '    End If

            '    If row.Item(tblHD.Msg注文郵便番号Column.ColumnName) <> "" Then
            '        Excel.CellSetError(row.Item(tblHD.位置Column.ColumnName), ConstantExcel.Header.列数.注文郵便番号, row.Item(tblHD.Msg注文郵便番号Column.ColumnName))
            '    End If

            '    If row.Item(tblHD.Msg注文住所1Column.ColumnName) <> "" Then
            '        Excel.CellSetError(row.Item(tblHD.位置Column.ColumnName), ConstantExcel.Header.列数.注文住所1, row.Item(tblHD.Msg注文住所1Column.ColumnName))
            '    End If

            '    If row.Item(tblHD.Msg注文住所2Column.ColumnName) <> "" Then
            '        Excel.CellSetError(row.Item(tblHD.位置Column.ColumnName), ConstantExcel.Header.列数.注文住所2, row.Item(tblHD.Msg注文住所2Column.ColumnName))
            '    End If

            '    If row.Item(tblHD.Msg注文TELColumn.ColumnName) <> "" Then
            '        Excel.CellSetError(row.Item(tblHD.位置Column.ColumnName), ConstantExcel.Header.列数.注文TEL, row.Item(tblHD.Msg注文TELColumn.ColumnName))
            '    End If

            '    If row.Item(tblHD.Msg注文メールアドレスColumn.ColumnName) <> "" Then
            '        Excel.CellSetError(row.Item(tblHD.位置Column.ColumnName), ConstantExcel.Header.列数.注文メールアドレス, row.Item(tblHD.Msg注文メールアドレスColumn.ColumnName))
            '    End If

            '    If row.Item(tblHD.Msg注文支払区分Column.ColumnName) <> "" Then
            '        Excel.CellSetError(row.Item(tblHD.位置Column.ColumnName), ConstantExcel.Header.列数.注文支払区分, row.Item(tblHD.Msg注文支払区分Column.ColumnName))
            '    End If

            '    If row.Item(tblHD.Msg注文入金状況Column.ColumnName) <> "" Then
            '        Excel.CellSetError(row.Item(tblHD.位置Column.ColumnName), ConstantExcel.Header.列数.注文入金状況, row.Item(tblHD.Msg注文入金状況Column.ColumnName))
            '    End If

            '    If row.Item(tblHD.Msg注文ギフト包装区分Column.ColumnName) <> "" Then
            '        Excel.CellSetError(row.Item(tblHD.位置Column.ColumnName), ConstantExcel.Header.列数.注文ギフト包装区分, row.Item(tblHD.Msg注文ギフト包装区分Column.ColumnName))
            '    End If

            '    If row.Item(tblHD.Msg注文ギフトメッセージColumn.ColumnName) <> "" Then
            '        Excel.CellSetError(row.Item(tblHD.位置Column.ColumnName), ConstantExcel.Header.列数.注文ギフトメッセージ, row.Item(tblHD.Msg注文ギフトメッセージColumn.ColumnName))
            '    End If

            '    If row.Item(tblHD.Msg注文のし表書き区分Column.ColumnName) <> "" Then
            '        Excel.CellSetError(row.Item(tblHD.位置Column.ColumnName), ConstantExcel.Header.列数.注文のし表書き区分, row.Item(tblHD.Msg注文のし表書き区分Column.ColumnName))
            '    End If

            '    If row.Item(tblHD.Msg注文のし名入れColumn.ColumnName) <> "" Then
            '        Excel.CellSetError(row.Item(tblHD.位置Column.ColumnName), ConstantExcel.Header.列数.注文のし名入れ, row.Item(tblHD.Msg注文のし名入れColumn.ColumnName))
            '    End If

            '    If row.Item(tblHD.Msg注文品代Column.ColumnName) <> "" Then
            '        Excel.CellSetError(row.Item(tblHD.位置Column.ColumnName), ConstantExcel.Header.列数.注文品代, row.Item(tblHD.Msg注文品代Column.ColumnName))
            '    End If

            '    If row.Item(tblHD.Msg注文消費税Column.ColumnName) <> "" Then
            '        Excel.CellSetError(row.Item(tblHD.位置Column.ColumnName), ConstantExcel.Header.列数.注文消費税, row.Item(tblHD.Msg注文消費税Column.ColumnName))
            '    End If

            '    If row.Item(tblHD.Msg注文送料Column.ColumnName) <> "" Then
            '        Excel.CellSetError(row.Item(tblHD.位置Column.ColumnName), ConstantExcel.Header.列数.注文送料, row.Item(tblHD.Msg注文送料Column.ColumnName))
            '    End If

            '    If row.Item(tblHD.Msg注文手数料Column.ColumnName) <> "" Then
            '        Excel.CellSetError(row.Item(tblHD.位置Column.ColumnName), ConstantExcel.Header.列数.注文手数料, row.Item(tblHD.Msg注文手数料Column.ColumnName))
            '    End If

            '    If row.Item(tblHD.Msg注文ギフト包装代Column.ColumnName) <> "" Then
            '        Excel.CellSetError(row.Item(tblHD.位置Column.ColumnName), ConstantExcel.Header.列数.注文ギフト包装代, row.Item(tblHD.Msg注文ギフト包装代Column.ColumnName))
            '    End If

            '    If row.Item(tblHD.Msg注文値引額Column.ColumnName) <> "" Then
            '        Excel.CellSetError(row.Item(tblHD.位置Column.ColumnName), ConstantExcel.Header.列数.注文値引額, row.Item(tblHD.Msg注文値引額Column.ColumnName))
            '    End If

            '    If row.Item(tblHD.Msg注文利用ポイントColumn.ColumnName) <> "" Then
            '        Excel.CellSetError(row.Item(tblHD.位置Column.ColumnName), ConstantExcel.Header.列数.注文利用ポイント, row.Item(tblHD.Msg注文利用ポイントColumn.ColumnName))
            '    End If

            '    If row.Item(tblHD.Msg注文請求金額Column.ColumnName) <> "" Then
            '        Excel.CellSetError(row.Item(tblHD.位置Column.ColumnName), ConstantExcel.Header.列数.注文請求金額, row.Item(tblHD.Msg注文請求金額Column.ColumnName))
            '    End If

            '    If row.Item(tblHD.Msg注文ショップCDColumn.ColumnName) <> "" Then
            '        Excel.CellSetError(row.Item(tblHD.位置Column.ColumnName), ConstantExcel.Header.列数.注文ショップCD, row.Item(tblHD.Msg注文ショップCDColumn.ColumnName))
            '    End If

            '    If row.Item(tblHD.Msg注文軽減税Column.ColumnName) <> "" Then
            '        Excel.CellSetError(row.Item(tblHD.位置Column.ColumnName), ConstantExcel.Header.列数.注文軽減税, row.Item(tblHD.Msg注文軽減税Column.ColumnName))
            '    End If

            '    If row.Item(tblHD.Msg全体Column.ColumnName) <> "" Then
            '        Excel.CellSetError(row.Item(tblHD.位置Column.ColumnName), ConstantExcel.Header.列数.エラー詳細, row.Item(tblHD.Msg全体Column.ColumnName))
            '    End If

            '    '----------------------------------------------------------------------
            '    ' T伝票DT
            '    '----------------------------------------------------------------------
            '    qryDT.Clear()
            '    qryDT.Add(New BaseDatabase.Condition(tblDT.セッションIDColumn.ColumnName, Me.Security.セッションID, BaseDatabase.Contents.Compare.Equal))
            '    qryDT.Add(New BaseDatabase.Condition(tblDT.仮番Column.ColumnName, row.Item(tblHD.仮番Column.ColumnName), BaseDatabase.Contents.Compare.Equal))
            '    tblDT = adaDT.SelectByCommon(qryDT)

            '    For Each rowDT As DataRow In tblDT.Rows
            '        If rowDT.Item(tblDT.Msg商品CDColumn.ColumnName) <> "" Then
            '            Excel.CellSetError(rowDT.Item(tblDT.位置Column.ColumnName), ConstantExcel.Header.列数.商品CD, rowDT.Item(tblDT.Msg商品CDColumn.ColumnName))
            '        End If

            '        If rowDT.Item(tblDT.Msg規格CD1Column.ColumnName) <> "" Then
            '            Excel.CellSetError(rowDT.Item(tblDT.位置Column.ColumnName), ConstantExcel.Header.列数.規格CD1, rowDT.Item(tblDT.Msg規格CD1Column.ColumnName))
            '        End If

            '        If rowDT.Item(tblDT.Msg規格CD2Column.ColumnName) <> "" Then
            '            Excel.CellSetError(rowDT.Item(tblDT.位置Column.ColumnName), ConstantExcel.Header.列数.規格CD2, rowDT.Item(tblDT.Msg規格CD2Column.ColumnName))
            '        End If

            '        If rowDT.Item(tblDT.Msg先方商品NOColumn.ColumnName) <> "" Then
            '            Excel.CellSetError(rowDT.Item(tblDT.位置Column.ColumnName), ConstantExcel.Header.列数.先方商品NO, rowDT.Item(tblDT.Msg先方商品NOColumn.ColumnName))
            '        End If

            '        If rowDT.Item(tblDT.MsgJANCDColumn.ColumnName) <> "" Then
            '            Excel.CellSetError(rowDT.Item(tblDT.位置Column.ColumnName), ConstantExcel.Header.列数.JANCD, rowDT.Item(tblDT.MsgJANCDColumn.ColumnName))
            '        End If

            '        If rowDT.Item(tblDT.Msg商品名Column.ColumnName) <> "" Then
            '            Excel.CellSetError(rowDT.Item(tblDT.位置Column.ColumnName), ConstantExcel.Header.列数.商品名, rowDT.Item(tblDT.Msg商品名Column.ColumnName))
            '        End If

            '        If rowDT.Item(tblDT.Msg先方商品名Column.ColumnName) <> "" Then
            '            Excel.CellSetError(rowDT.Item(tblDT.位置Column.ColumnName), ConstantExcel.Header.列数.先方商品名, rowDT.Item(tblDT.Msg先方商品名Column.ColumnName))
            '        End If

            '        If rowDT.Item(tblDT.MsgロットNOColumn.ColumnName) <> "" Then
            '            Excel.CellSetError(rowDT.Item(tblDT.位置Column.ColumnName), ConstantExcel.Header.列数.ロットNO, rowDT.Item(tblDT.MsgロットNOColumn.ColumnName))
            '        End If

            '        If rowDT.Item(tblDT.Msgロット日付Column.ColumnName) <> "" Then
            '            Excel.CellSetError(rowDT.Item(tblDT.位置Column.ColumnName), ConstantExcel.Header.列数.ロット日付, rowDT.Item(tblDT.Msgロット日付Column.ColumnName))
            '        End If

            '        If rowDT.Item(tblDT.Msg棚番CDColumn.ColumnName) <> "" Then
            '            Excel.CellSetError(rowDT.Item(tblDT.位置Column.ColumnName), ConstantExcel.Header.列数.棚番CD, rowDT.Item(tblDT.Msg棚番CDColumn.ColumnName))
            '        End If

            '        If rowDT.Item(tblDT.Msg荷姿区分Column.ColumnName) <> "" Then
            '            Excel.CellSetError(rowDT.Item(tblDT.位置Column.ColumnName), ConstantExcel.Header.列数.荷姿区分, rowDT.Item(tblDT.Msg荷姿区分Column.ColumnName))
            '        End If

            '        If rowDT.Item(tblDT.Msg数量Column.ColumnName) <> "" Then
            '            Excel.CellSetError(rowDT.Item(tblDT.位置Column.ColumnName), ConstantExcel.Header.列数.数量, rowDT.Item(tblDT.Msg数量Column.ColumnName))
            '        End If

            '        If rowDT.Item(tblDT.Msg単価Column.ColumnName) <> "" Then
            '            Excel.CellSetError(rowDT.Item(tblDT.位置Column.ColumnName), ConstantExcel.Header.列数.単価, rowDT.Item(tblDT.Msg単価Column.ColumnName))
            '        End If

            '        If rowDT.Item(tblDT.Msg金額Column.ColumnName) <> "" Then
            '            Excel.CellSetError(rowDT.Item(tblDT.位置Column.ColumnName), ConstantExcel.Header.列数.金額, rowDT.Item(tblDT.Msg金額Column.ColumnName))
            '        End If
            '    Next
            'Next

            '------------------------------------------------------------------
            ' 返却
            '------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "ﾜｰｸﾃｰﾌﾞﾙ削除"
        '''========================================================================================
        ''' <SUMMARY>ﾜｰｸﾃーﾌﾞﾙ削除</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Public Function WorkDelete() As Boolean
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetWorkTableAdapters.W乗務記録TableAdapter
            Dim tbl As New NodeDatabase.DataSetWork.W乗務記録DataTable
            Dim qry As New Collection

            '----------------------------------------------------------------------
            ' 事前削除
            '----------------------------------------------------------------------
            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl.セッションIDColumn.ColumnName, Me.Security.セッションID, BaseDatabase.Contents.Compare.Equal))

            Try
                ada.DeleteByCommon(qry)
            Catch ex As Exception
                Me.LastError = ex.Message
                Return False
            End Try

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "不等号判断"
        '''========================================================================================
        ''' <SUMMARY>不等号判断</SUMMARY>
        ''' <RETURNS>True:変換する, False:変換しない</RETURNS>
        '''========================================================================================
        Public Function JudgeValue(ByVal FieldValueBase As String, ByVal SymbolCode As String, ByVal FieldValueIf As String) As Boolean
            Select Case SymbolCode
                Case NodeContents.Constant.CodeValue.符号区分.等しい
                    If FieldValueBase = FieldValueIf Then
                        Return True
                    End If
                Case NodeContents.Constant.CodeValue.符号区分.等しくない
                    If FieldValueBase <> FieldValueIf Then
                        Return True
                    End If

                Case NodeContents.Constant.CodeValue.符号区分.大きい
                    If FieldValueBase > FieldValueIf Then
                        Return True
                    End If

                Case NodeContents.Constant.CodeValue.符号区分.小さい
                    If FieldValueBase < FieldValueIf Then
                        Return True
                    End If

                Case NodeContents.Constant.CodeValue.符号区分.以上
                    If FieldValueBase >= FieldValueIf Then
                        Return True
                    End If

                Case NodeContents.Constant.CodeValue.符号区分.以下
                    If FieldValueBase <= FieldValueIf Then
                        Return True
                    End If

                Case Else
                    Return False
            End Select

            '----------------------------------------------------------------------
            ' 変換しない
            '----------------------------------------------------------------------
            Return False
        End Function
#End Region
#End Region

#Region "無効行判定"
        '''========================================================================================
        ''' <summary>無効行の判定</summary>
        ''' <param name="Detail">明細行</param>
        ''' <returns>True:無効行, False:有効行</returns>
        '''========================================================================================
        Protected Overrides Function Empty_Detail1(ByVal Detail As Object) As Boolean
            '------------------------------------------------------------------
            ' 変数定義
            '------------------------------------------------------------------
            Dim row As SlipInputBase.Detail1 = CType(Detail, SlipInputBase.Detail1)

            '------------------------------------------------------------------
            ' 入力有は有効行
            '------------------------------------------------------------------
            Return False

            '------------------------------------------------------------------
            ' 無効行
            '------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "Valid_Execute1(実行)"
        '''========================================================================================
        ''' <SUMMARY>ﾍｯﾀﾞ(共通)</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Valid_Execute1_Header_Common() As NodeContents.ActionButton.ErrorLevel
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim strMsg As String = ""
            Dim Validator As New BaseCore.Common.Validator

            '------------------------------------------------------------------
            ' ｴﾗｰｸﾘｱ
            '------------------------------------------------------------------
            Me.Fields.Header.ファイル形式.IsError = False
            Me.Fields.Header.レイアウトCD.IsError = False

            '------------------------------------------------------------------
            ' 共通検査
            '------------------------------------------------------------------
            If Not Validator.BaseChecker(Me.Fields.Header.ファイル形式) Then : Me.Fields.Header.ファイル形式.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.レイアウトCD) Then : Me.Fields.Header.レイアウトCD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If

            '------------------------------------------------------------------
            ' 返却
            '------------------------------------------------------------------
            MyBase.LastError = strMsg
            Select Case True
                Case strMsg = ""
                    Return NodeContents.ActionButton.ErrorLevel.None
                Case Else
                    Return NodeContents.ActionButton.ErrorLevel.Fatal
            End Select
        End Function

        '''========================================================================================
        ''' <SUMMARY>検査(個別)</SUMMARY>
        ''' <RETURNS>True:正常, False:不正</RETURNS>
        '''========================================================================================
        Protected Overrides Function Valid_Execute1_Header_Another() As NodeContents.ActionButton.ErrorLevel
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim strMsg As String = ""
            Dim Validator As New BaseCore.Common.Validator

            '------------------------------------------------------------------
            ' 個別検査
            '------------------------------------------------------------------
            Dim str変換拡張子 As String = System.IO.Path.GetExtension(Me.Fields.Header.ファイル名.Value).ToLower()
            '------------------------------------
            ' ﾌｧｲﾙ形式検査
            '------------------------------------
            Select Case Me.Fields.Header.ファイル形式.Value
                Case NodeContents.Constant.CodeValue.ファイル形式.Excel
                    If Not Me.Fields.Header.ファイル名.Value Like "*.xls" _
                   And Not Me.Fields.Header.ファイル名.Value Like "*.xlsx" Then
                        strMsg &= "選択されたファイル形式と対象のファイル形式が一致しません。"
                    End If
                Case NodeContents.Constant.CodeValue.ファイル形式.Csv
                    If Not str変換拡張子 = ".csv" Then
                        strMsg &= "選択されたファイル形式と対象のファイル形式が一致しません。"
                    End If
                Case Else
            End Select

            '------------------------------------------------------------------
            ' 返却
            '------------------------------------------------------------------
            MyBase.LastError = strMsg
            Select Case True
                Case strMsg = ""
                    Return NodeContents.ActionButton.ErrorLevel.None
                Case Else
                    Return NodeContents.ActionButton.ErrorLevel.Fatal
            End Select
        End Function
#End Region
#End Region
#End Region

#Region "Execute2[戻す]"
#Region "Execute2"
        '''========================================================================================
        ''' <SUMMARY>戻す</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Execute2_Header() As Boolean
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetTranTableAdapters.T乗務記録TableAdapter
            Dim tbl As New NodeDatabase.DataSetTran.T乗務記録DataTable
            Dim qry As New Collection

            Dim adaWork As New NodeDatabase.DataSetWorkTableAdapters.W乗務記録TableAdapter
            Dim tblWork As New NodeDatabase.DataSetWork.W乗務記録DataTable
            Dim qryWork As New Collection

            '----------------------------------------------------------------------
            ' 事前削除
            '----------------------------------------------------------------------
            Me.WorkDelete()

            '----------------------------------------------------------------------
            ' 抽出
            '----------------------------------------------------------------------
            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, Me.Security.グループCD, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, Me.Security.個社CD, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.子会社CDColumn.ColumnName, Me.Security.子会社CD, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.取込日付Column.ColumnName, Me.Fields.Undo.取込日時.Value, BaseDatabase.Contents.Compare.Equal))



            tbl = ada.SelectByCommon(qry)

            '----------------------------------------------------------------------
            ' 戻す
            '----------------------------------------------------------------------
            For Each row As NodeDatabase.DataSetTran.T伝票HDRow In tbl.Rows
                Try
                    'adaWork.Insert(
                    '               Me.Security.セッションID _
                    '             , row.伝票タイプ _
                    '             , row.伝票番号 _
                    '             , "", "", "", "", "", "", "", "", "", "" _
                    '             , "", "", "", "", "", "", "", "", "", "" _
                    '             , "", "", "", "", "", "" _
                    '             , 0 _
                    '             , "", "", "" _
                    '             , 0, 0, 0, 0 _
                    '             , "", "", "", "", "", "", "", "", "", "" _
                    '             , "", "", "", "", "", "", "", "" _
                    '             , 0, 0, 0, 0, 0, 0, 0, 0, 0 _
                    '             , "", "", "", "", "", "", "", "", "", "" _
                    '             , "", "", "", "", "" _
                    '             , 0, 0 _
                    '             , "", "", "", "", "", "", "", "", "", "" _
                    '             , "", "", "", "", "", "", "", "", "", "" _
                    '             , "", "", "", "", "", "", "", "", "", "" _
                    '             , "", "", "", "", "", "", "", "", "", "" _
                    '             , "", "", "", "", "", "", "", "", "", "" _
                    '             , "", "", "", "", "", "", "", "", "", "" _
                    '             , "", "", "", "", "", "", "", "", "", "" _
                    '             , "", "", "", "", "", "", "", "", ""
                    '              )
                Catch ex As Exception
                    Me.LastError = "以下のエラーのため、処理を継続できません。" & vbCrLf & ex.Message
                    Return False
                End Try
            Next

            '----------------------------------------------------------------------
            ' ｽﾄｱﾄﾞ実行
            '----------------------------------------------------------------------
            Try
                If (New NodeDatabase.DataSetStoredTableAdapters.P0伝票削除一括TableAdapter).Execute(Me.Security.セッションID) = 0 Then
                Else
                    If Not Me.Bulk_StoredResultNG() Then
                        Return False
                    End If

                    Return False
                End If

            Catch ex As Exception
                Me.LastError = ex.Message
                Return False
            Finally
                Me.WorkDelete()
            End Try

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function

#Region "一括ｽﾄｱﾄﾞ結果 設定"
        '''========================================================================================
        ''' <SUMMARY>ﾜｰｸﾃｰﾌﾞﾙ(ｽﾄｱﾄﾞ結果)→ﾌﾚｰﾑﾜｰｸ</SUMMARY>   
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Public Function Bulk_StoredResultNG() As Boolean
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim adaHD As New NodeDatabase.DataSetWorkTableAdapters.W伝票HD一括TableAdapter
            Dim tblHD As New NodeDatabase.DataSetWork.W伝票HD一括DataTable
            Dim qryHD As New Collection

            '----------------------------------------------------------------------
            ' T伝票HD
            '----------------------------------------------------------------------
            qryHD.Clear()
            qryHD.Add(New BaseDatabase.Condition(tblHD.セッションIDColumn.ColumnName, Me.Security.セッションID, BaseDatabase.Contents.Compare.Equal))
            qryHD.Add(New BaseDatabase.Condition(tblHD.Msg全体Column.ColumnName, "", BaseDatabase.Contents.Compare.NotEqual))
            tblHD = adaHD.SelectByCommon(qryHD)

            For Each row As NodeDatabase.DataSetWork.W伝票HD一括Row In tblHD.Rows
                Me.LastError &= row.Item(tblHD.Msg全体Column.ColumnName)
            Next

            '------------------------------------------------------------------
            ' 返却
            '------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "Valid_Execute2"
        '''========================================================================================
        ''' <SUMMARY>ﾍｯﾀﾞ(共通)</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Valid_Execute2_Header_Common() As NodeContents.ActionButton.ErrorLevel
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim strMsg As String = ""
            Dim Validator As New BaseCore.Common.Validator

            '------------------------------------------------------------------
            ' ｴﾗｰｸﾘｱ
            '------------------------------------------------------------------
            'Me.Fields.Undo.拠点CD.IsError = False
            Me.Fields.Undo.取込日時.IsError = False

            '------------------------------------------------------------------
            ' 共通検査
            '------------------------------------------------------------------
            'If Not Validator.BaseChecker(Me.Fields.Undo.拠点CD) Then : Me.Fields.Undo.拠点CD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Undo.取込日時) Then : Me.Fields.Undo.取込日時.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If

            '------------------------------------------------------------------
            ' 返却
            '------------------------------------------------------------------
            MyBase.LastError = strMsg
            Select Case True
                Case strMsg = ""
                    Return NodeContents.ActionButton.ErrorLevel.None
                Case Else
                    Return NodeContents.ActionButton.ErrorLevel.Fatal
            End Select
        End Function

        '''========================================================================================
        ''' <SUMMARY>検査(個別)</SUMMARY>
        ''' <RETURNS>True:正常, False:不正</RETURNS>
        '''========================================================================================
        Protected Overrides Function Valid_Execute2_Header_Another() As NodeContents.ActionButton.ErrorLevel
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim strMsg As String = ""
            Dim Validator As New BaseCore.Common.Validator

            '------------------------------------------------------------------
            ' 個別検査
            '------------------------------------------------------------------
            '------------------------------------
            ' 取込日時 指定検査
            '------------------------------------
            If Me.Fields.Undo.取込日時.Value.ToString = "" Then
                Me.Fields.Undo.取込日時.IsError = True
                Validator.SetMessage(strMsg, Me.Fields.Undo.取込日時.Name & "：指定してください。")
            End If

            '------------------------------------------------------------------
            ' 返却
            '------------------------------------------------------------------
            MyBase.LastError = strMsg
            Select Case True
                Case strMsg = ""
                    Return NodeContents.ActionButton.ErrorLevel.None
                Case Else
                    Return NodeContents.ActionButton.ErrorLevel.Fatal
            End Select
        End Function
#End Region
#End Region
#End Region
#End Region
    End Class
End Namespace
