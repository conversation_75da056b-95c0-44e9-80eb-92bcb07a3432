﻿Namespace Frame.Daily
    Partial Public Class MedicalCheckup
        Inherits NodeCore.Common.Frame

#Region "ﾌｨｰﾙﾄﾞ定義"
        Public Class Header
            ''' ----------------------------------------------------------------------------------------
            ''' <SUMMARY>列項目(基本)</SUMMARY>
            ''' ----------------------------------------------------------------------------------------
            Public グループCD As BaseCore.Common.Field.ItemData
            Public 個社CD As BaseCore.Common.Field.ItemData
            Public 子会社CD As BaseCore.Common.Field.ItemData
            Public 健康診断NO As BaseCore.Common.Field.ItemData
            Public 乗務員CD As BaseCore.Common.Field.ItemData
            Public 健康診断日 As BaseCore.Common.Field.ItemData
            Public 健康診断メモ As BaseCore.Common.Field.ItemData
            Public 登録ユーザーCD As BaseCore.Common.Field.ItemData
            Public 登録ユーザー名 As BaseCore.Common.Field.ItemData
            Public 登録日時 As BaseCore.Common.Field.ItemData
            Public 更新ユーザーCD As BaseCore.Common.Field.ItemData
            Public 更新ユーザー名 As BaseCore.Common.Field.ItemData
            Public 更新日時 As BaseCore.Common.Field.ItemData

            '''========================================================================================
            ''' <SUMMARY>ｺﾝｽﾄﾗｸﾀ</SUMMARY>
            '''========================================================================================
            Public Sub New(ByVal Config As NodeCore.Common.Config)
                Dim tbl As New NodeDatabase.DataSetTran.T健康診断DataTable

                '----------------------------------------------------------------------
                ' ｲﾝｽﾀﾝｽ(基本) 
                '----------------------------------------------------------------------
                Me.グループCD = New BaseCore.Common.Field.ItemData(tbl.グループCDColumn)
                Me.個社CD = New BaseCore.Common.Field.ItemData(tbl.個社CDColumn)
                Me.子会社CD = New BaseCore.Common.Field.ItemData(tbl.子会社CDColumn)
                Me.健康診断NO = New BaseCore.Common.Field.ItemData(tbl.健康診断NOColumn)
                Me.乗務員CD = New BaseCore.Common.Field.ItemData(tbl.乗務員CDColumn)
                Me.健康診断日 = New BaseCore.Common.Field.ItemData(tbl.健康診断日Column)
                Me.健康診断メモ = New BaseCore.Common.Field.ItemData(tbl.健康診断メモColumn)
                Me.登録ユーザーCD = New BaseCore.Common.Field.ItemData(tbl.登録ユーザーCDColumn)
                Me.登録ユーザー名 = New BaseCore.Common.Field.ItemData(tbl.登録ユーザー名Column)
                Me.登録日時 = New BaseCore.Common.Field.ItemData(tbl.登録日時Column)
                Me.更新ユーザーCD = New BaseCore.Common.Field.ItemData(tbl.更新ユーザーCDColumn)
                Me.更新ユーザー名 = New BaseCore.Common.Field.ItemData(tbl.更新ユーザー名Column)
                Me.更新日時 = New BaseCore.Common.Field.ItemData(tbl.更新日時Column)

            End Sub
        End Class
#End Region

#Region "変数定義"
        Public Class Paramater
            Public Header As Header
        End Class

        '''========================================================================================
        ''' <summary>ﾍｯﾀﾞ</summary>
        '''========================================================================================
        Private _Fields As New Paramater
        Public Property Fields() As Paramater
            Get
                Return _Fields
            End Get

            Set(ByVal value As Paramater)
                _Fields = value
            End Set
        End Property
#End Region

#Region "ｺﾝｽﾄﾗｸﾀ"
        '''========================================================================================
        ''' <SUMMARY>ｺﾝｽﾄﾗｸﾀ</SUMMARY>
        ''' <PARAM name="Security">ｾｷｭﾘﾃｨ ｸﾗｽ</PARAM>
        ''' <PARAM name="Config">ｺﾝﾌｨｸﾞ ｸﾗｽ</PARAM>
        ''' <PARAM name="PagePath">ﾍﾟｰｼﾞﾊﾟｽ</PARAM>
        '''========================================================================================
        Public Sub New(ByVal Security As NodeCore.Common.Security, ByVal Config As NodeCore.Common.Config, ByVal PagePath As String)
            '----------------------------------------------------------------------
            ' ｾｷｭﾘﾃｨ 設定
            '----------------------------------------------------------------------
            MyBase.New(Security, Config, PagePath)

            Me.Fields.Header = New Header(Config)
        End Sub
#End Region

#Region "ﾍｯﾀﾞ"
#Region "Clear"
        '''========================================================================================
        ''' <SUMMARY>ｸﾘｱ</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Clear_Header(Optional ByVal KeyClear As Boolean = False) As Boolean
            '----------------------------------------------------------------------
            ' ｷｰ部ｸﾘｱ
            '----------------------------------------------------------------------
            If KeyClear Then
                Me.Fields.Header.グループCD.Value = Nothing
                Me.Fields.Header.個社CD.Value = Nothing
                Me.Fields.Header.子会社CD.Value = Nothing
                Me.Fields.Header.健康診断NO.Value = Nothing
            End If

            '----------------------------------------------------------------------
            ' ﾃﾞｰﾀ部ｸﾘｱ
            '----------------------------------------------------------------------
            Me.Fields.Header.乗務員CD.Value = Nothing
            Me.Fields.Header.健康診断日.Value = Nothing
            Me.Fields.Header.健康診断メモ.Value = Nothing
            Me.Fields.Header.登録ユーザーCD.Value = Nothing
            Me.Fields.Header.登録ユーザー名.Value = Nothing
            Me.Fields.Header.登録日時.Value = Nothing
            Me.Fields.Header.更新ユーザーCD.Value = Nothing
            Me.Fields.Header.更新ユーザー名.Value = Nothing
            Me.Fields.Header.更新日時.Value = Nothing

            '----------------------------------------------------------------------
            ' ﾃﾞｰﾀ部ｸﾘｱ
            '----------------------------------------------------------------------
            'Me.Read()

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "Read"
        '''========================================================================================
        ''' <SUMMARY>読込</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Read_Header() As Boolean
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetViewTableAdapters.V0健康診断TableAdapter
            Dim tbl As New NodeDatabase.DataSetView.V0健康診断DataTable
            Dim qry As New Collection

            '----------------------------------------------------------------------
            ' 条件設定
            '----------------------------------------------------------------------
            qry.Clear()

            qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, Me.Fields.Header.グループCD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, Me.Fields.Header.個社CD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.子会社CDColumn.ColumnName, Me.Fields.Header.子会社CD.Value, BaseDatabase.Contents.Compare.Equal))

            'qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, Me.Security.グループCD, BaseDatabase.Contents.Compare.Equal))
            'qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, Me.Security.個社CD, BaseDatabase.Contents.Compare.Equal))
            'qry.Add(New BaseDatabase.Condition(tbl.子会社CDColumn.ColumnName, Me.Security.子会社CD, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.健康診断NOColumn.ColumnName, Me.Fields.Header.健康診断NO.Value, BaseDatabase.Contents.Compare.Equal))

            '----------------------------------------------------------------------
            ' 読込
            '----------------------------------------------------------------------
            tbl = ada.SelectByCommon(qry)
            If tbl.Count > 0 Then
                Me.Fields.Header.乗務員CD.Value = tbl(0).Item(tbl.乗務員CDColumn.ColumnName)
                Me.Fields.Header.健康診断日.Value = tbl(0).Item(tbl.健康診断日Column.ColumnName)
                Me.Fields.Header.健康診断メモ.Value = tbl(0).Item(tbl.健康診断メモColumn.ColumnName)
                Me.Fields.Header.登録ユーザーCD.Value = tbl(0).Item(tbl.登録ユーザーCDColumn.ColumnName)
                Me.Fields.Header.登録ユーザー名.Value = tbl(0).Item(tbl.登録ユーザー名Column.ColumnName)
                Me.Fields.Header.登録日時.Value = tbl(0).Item(tbl.登録日時Column.ColumnName)
                Me.Fields.Header.更新ユーザーCD.Value = tbl(0).Item(tbl.更新ユーザーCDColumn.ColumnName)
                Me.Fields.Header.更新ユーザー名.Value = tbl(0).Item(tbl.更新ユーザー名Column.ColumnName)
                Me.Fields.Header.更新日時.Value = tbl(0).Item(tbl.更新日時Column.ColumnName)
            Else
                Me.Clear(False)
            End If

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "Write"
        '''========================================================================================
        ''' <SUMMARY>書込</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Write_Header() As Boolean
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim strNow As String = Format(Now, NodeContents.Constant.System.DATE_TIME_FORMAT)

            Dim ada As New NodeDatabase.DataSetTranTableAdapters.T健康診断TableAdapter
            Dim tbl As New NodeDatabase.DataSetTran.T健康診断DataTable
            Dim qry As New Collection

            '----------------------------------------------------------------------
            ' 条件設定
            '----------------------------------------------------------------------
            qry.Clear()

            qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, Me.Fields.Header.グループCD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, Me.Fields.Header.個社CD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.子会社CDColumn.ColumnName, Me.Fields.Header.子会社CD.Value, BaseDatabase.Contents.Compare.Equal))

            'qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, Me.Security.グループCD, BaseDatabase.Contents.Compare.Equal))
            'qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, Me.Security.個社CD, BaseDatabase.Contents.Compare.Equal))
            'qry.Add(New BaseDatabase.Condition(tbl.子会社CDColumn.ColumnName, Me.Security.子会社CD, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.健康診断NOColumn.ColumnName, Me.Fields.Header.健康診断NO.Value, BaseDatabase.Contents.Compare.Equal))

            '----------------------------------------------------------------------
            ' 読込
            '----------------------------------------------------------------------
            tbl = ada.SelectByCommon(qry)


            '----------------------------------------------------------------------
            ' 事前準備
            '----------------------------------------------------------------------
            Me.Fields.Header.更新日時.Value = strNow
            Me.Fields.Header.更新ユーザーCD.Value = Me.Security.ユーザーCD
            Me.Fields.Header.更新ユーザー名.Value = Me.Security.ユーザー名

            Using scope As New System.Transactions.TransactionScope(System.Transactions.TransactionScopeOption.Required, TimeSpan.Zero)
                '----------------------------------------------------------------------
                ' 保存
                '----------------------------------------------------------------------
                If tbl.Count = 0 Then
                    '----------------------------------------------------------------------
                    ' 新規
                    '----------------------------------------------------------------------
                    Me.Fields.Header.登録日時.Value = strNow
                    Me.Fields.Header.登録ユーザーCD.Value = Me.Security.ユーザーCD
                    Me.Fields.Header.登録ユーザー名.Value = Me.Security.ユーザー名
                    Try
                        ada.Insert(Me.Fields.Header.グループCD.Value _
                             , Me.Fields.Header.個社CD.Value _
                             , Me.Fields.Header.子会社CD.Value _
                             , Me.Fields.Header.健康診断NO.Value _
                             , Me.Fields.Header.乗務員CD.Value _
                             , Me.Fields.Header.健康診断日.Value _
                             , Me.Fields.Header.健康診断メモ.Value _
                             , Me.Fields.Header.登録ユーザーCD.Value _
                             , Me.Fields.Header.登録ユーザー名.Value _
                             , Me.Fields.Header.登録日時.Value _
                             , Me.Fields.Header.更新ユーザーCD.Value _
                             , Me.Fields.Header.更新ユーザー名.Value _
                             , Me.Fields.Header.更新日時.Value
                             )

                    Catch ex As Exception
                        MyBase.LastError = "以下のエラーのため、追加できませんでした。" & vbCrLf & ex.Message
                        Return False
                    End Try
                Else
                    '----------------------------------------------------------------------
                    ' 訂正
                    '----------------------------------------------------------------------
                    Try
                        tbl(0).Item(tbl.グループCDColumn.ColumnName) = Me.Fields.Header.グループCD.Value
                        tbl(0).Item(tbl.個社CDColumn.ColumnName) = Me.Fields.Header.個社CD.Value
                        tbl(0).Item(tbl.子会社CDColumn.ColumnName) = Me.Fields.Header.子会社CD.Value
                        tbl(0).Item(tbl.健康診断NOColumn.ColumnName) = Me.Fields.Header.健康診断NO.Value
                        tbl(0).Item(tbl.乗務員CDColumn.ColumnName) = Me.Fields.Header.乗務員CD.Value
                        tbl(0).Item(tbl.健康診断日Column.ColumnName) = Me.Fields.Header.健康診断日.Value
                        tbl(0).Item(tbl.健康診断メモColumn.ColumnName) = Me.Fields.Header.健康診断メモ.Value
                        tbl(0).Item(tbl.登録ユーザーCDColumn.ColumnName) = Me.Fields.Header.登録ユーザーCD.Value
                        tbl(0).Item(tbl.登録ユーザー名Column.ColumnName) = Me.Fields.Header.登録ユーザー名.Value
                        tbl(0).Item(tbl.登録日時Column.ColumnName) = Me.Fields.Header.登録日時.Value
                        tbl(0).Item(tbl.更新ユーザーCDColumn.ColumnName) = Me.Fields.Header.更新ユーザーCD.Value
                        tbl(0).Item(tbl.更新ユーザー名Column.ColumnName) = Me.Fields.Header.更新ユーザー名.Value
                        tbl(0).Item(tbl.更新日時Column.ColumnName) = Me.Fields.Header.更新日時.Value
                        ada.Update(tbl)

                    Catch ex As Exception
                        MyBase.LastError = "以下のエラーのため、更新できませんでした。" & vbCrLf & ex.Message
                        Return False
                    End Try
                End If
                scope.Complete()
            End Using

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "Delete"
        '''========================================================================================
        ''' <SUMMARY>削除</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Delete_Header() As Boolean
            '----------------------------------------------------------------------
            ' ﾍｯﾀﾞ
            '----------------------------------------------------------------------
            '------------------------------------
            ' 変数定義
            '------------------------------------
            Dim ada As New NodeDatabase.DataSetTranTableAdapters.T健康診断TableAdapter
            Dim tbl As New NodeDatabase.DataSetTran.T健康診断DataTable
            Dim qry As New Collection

            '----------------------------------------------------------------------
            ' 条件設定
            '----------------------------------------------------------------------
            qry.Clear()

            qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, Me.Fields.Header.グループCD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, Me.Fields.Header.個社CD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.子会社CDColumn.ColumnName, Me.Fields.Header.子会社CD.Value, BaseDatabase.Contents.Compare.Equal))

            'qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, Me.Security.グループCD, BaseDatabase.Contents.Compare.Equal))
            'qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, Me.Security.個社CD, BaseDatabase.Contents.Compare.Equal))
            'qry.Add(New BaseDatabase.Condition(tbl.子会社CDColumn.ColumnName, Me.Security.子会社CD, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.健康診断NOColumn.ColumnName, Me.Fields.Header.健康診断NO.Value, BaseDatabase.Contents.Compare.Equal))

            '------------------------------------
            ' 削除
            '------------------------------------
            Try
                ada.DeleteByCommon(qry)

            Catch ex As Exception
                MyBase.LastError = "以下のエラーのため、削除できませんでした。" & vbCrLf & ex.Message
                Return False
            End Try

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "Valid_Write"
        '''========================================================================================
        ''' <SUMMARY>検査(共通)</SUMMARY>
        ''' <RETURNS>True:正常, False:不正</RETURNS>
        '''========================================================================================
        Protected Overrides Function Valid_Write_Header_Common() As NodeContents.ActionButton.ErrorLevel
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim strMsg As String = ""
            Dim Validator As New BaseCore.Common.Validator

            '------------------------------------------------------------------
            ' ｴﾗｰｸﾘｱ
            '------------------------------------------------------------------
            Me.Fields.Header.グループCD.IsError = False
            Me.Fields.Header.個社CD.IsError = False
            Me.Fields.Header.子会社CD.IsError = False
            Me.Fields.Header.健康診断NO.IsError = False
            Me.Fields.Header.乗務員CD.IsError = False
            Me.Fields.Header.健康診断日.IsError = False
            Me.Fields.Header.健康診断メモ.IsError = False
            Me.Fields.Header.登録ユーザーCD.IsError = False
            Me.Fields.Header.登録ユーザー名.IsError = False
            Me.Fields.Header.登録日時.IsError = False
            Me.Fields.Header.更新ユーザーCD.IsError = False
            Me.Fields.Header.更新ユーザー名.IsError = False
            Me.Fields.Header.更新日時.IsError = False

            '------------------------------------------------------------------
            ' 共通検査
            '-----------------------------------------------------------------
            If Not Validator.BaseChecker(Me.Fields.Header.グループCD) Then : Me.Fields.Header.グループCD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.個社CD) Then : Me.Fields.Header.個社CD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.子会社CD) Then : Me.Fields.Header.子会社CD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.健康診断NO) Then : Me.Fields.Header.健康診断NO.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.乗務員CD) Then : Me.Fields.Header.乗務員CD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.健康診断日) Then : Me.Fields.Header.健康診断日.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.健康診断メモ) Then : Me.Fields.Header.健康診断メモ.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.登録ユーザーCD) Then : Me.Fields.Header.登録ユーザーCD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.登録ユーザー名) Then : Me.Fields.Header.登録ユーザー名.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.登録日時) Then : Me.Fields.Header.登録日時.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.更新ユーザーCD) Then : Me.Fields.Header.更新ユーザーCD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.更新ユーザー名) Then : Me.Fields.Header.更新ユーザー名.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.更新日時) Then : Me.Fields.Header.更新日時.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If

            ''Exists check
            'Dim ada As New NodeDatabase.DataSetMasterTableAdapters.MグループTableAdapter
            'Dim tbl As New NodeDatabase.DataSetMaster.MグループDataTable
            'Dim qry As New Collection
            'qry.Clear()
            'qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, Me.Security.グループCD.Value, BaseDatabase.Contents.Compare.Equal))
            'tbl = ada.SelectByCommon(qry)
            'If tbl.Count = 0 Then
            '    Me.Security.グループCD.IsError = True
            '    Validator.SetMessage(strMsg, "入力したグループが存在しません！")
            'End If

            '------------------------------------------------------------------
            ' 返却
            '------------------------------------------------------------------
            MyBase.LastError = strMsg
            Select Case True
                Case strMsg = ""
                    Return NodeContents.ActionButton.ErrorLevel.None
                Case Else
                    Return NodeContents.ActionButton.ErrorLevel.Fatal
            End Select
        End Function
#End Region

#Region "Valid_Delete"
        '''========================================================================================
        ''' <SUMMARY>検査(共通)</SUMMARY>
        ''' <RETURNS>True:正常, False:不正</RETURNS>
        '''========================================================================================
        Protected Overrides Function Valid_Delete_Header_Common() As NodeContents.ActionButton.ErrorLevel
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim strMsg As String = ""
            Dim Validator As New BaseCore.Common.Validator

            '------------------------------------------------------------------
            ' ｴﾗｰｸﾘｱ
            '------------------------------------------------------------------
            Me.Fields.Header.グループCD.IsError = False
            Me.Fields.Header.個社CD.IsError = False
            Me.Fields.Header.子会社CD.IsError = False
            Me.Fields.Header.健康診断NO.IsError = False
            Me.Fields.Header.乗務員CD.IsError = False
            Me.Fields.Header.健康診断日.IsError = False
            '------------------------------------------------------------------
            ' 共通検査
            '-----------------------------------------------------------------
            If Not Validator.BaseChecker(Me.Fields.Header.グループCD) Then : Me.Fields.Header.グループCD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.個社CD) Then : Me.Fields.Header.個社CD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.子会社CD) Then : Me.Fields.Header.子会社CD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.健康診断NO) Then : Me.Fields.Header.健康診断NO.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.乗務員CD) Then : Me.Fields.Header.乗務員CD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.健康診断日) Then : Me.Fields.Header.健康診断日.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If

            '------------------------------------------------------------------
            ' 返却
            '------------------------------------------------------------------
            MyBase.LastError = strMsg
            Select Case True
                Case strMsg = ""
                    Return NodeContents.ActionButton.ErrorLevel.None
                Case Else
                    Return NodeContents.ActionButton.ErrorLevel.Fatal
            End Select
        End Function
#End Region

#End Region
    End Class
End Namespace
