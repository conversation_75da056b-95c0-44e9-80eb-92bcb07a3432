﻿Imports System.Text
Imports System.Xml
Imports System.Xml.Serialization

Namespace Frame.Daily
    Partial Public Class DeliveryImport
        Inherits NodeCore.Common.Frame

#Region "ﾌｨｰﾙﾄﾞ定義"
        Public Class Header
            '画面項目（設定項目）
            Public レイアウトコード As BaseCore.Common.Field.ItemData
            Public ファイル形式 As BaseCore.Common.Field.ItemData
            Public 先頭タイトル As BaseCore.Common.Field.ItemData
            Public 最終ディレクトリ As BaseCore.Common.Field.ItemData
            Public 取込日時 As BaseCore.Common.Field.ItemData
            Public ファイル名 As BaseCore.Common.Field.ItemData

            ' テーブル項目
            Public セッションID As BaseCore.Common.Field.ItemData

            Public 取込日付 As BaseCore.Common.Field.ItemData
            Public 日報日付 As BaseCore.Common.Field.ItemData
            Public 日報年月 As BaseCore.Common.Field.ItemData
            Public 出庫時刻 As BaseCore.Common.Field.ItemData
            Public 入庫時刻 As BaseCore.Common.Field.ItemData
            Public 乗務員CD As BaseCore.Common.Field.ItemData
            Public 車両番号 As BaseCore.Common.Field.ItemData
            Public 営業所CD As BaseCore.Common.Field.ItemData
            Public 部門CD As BaseCore.Common.Field.ItemData
            Public 勤務CD As BaseCore.Common.Field.ItemData
            Public 車種CD As BaseCore.Common.Field.ItemData
            Public 天気 As BaseCore.Common.Field.ItemData
            Public 累計全走行 As BaseCore.Common.Field.ItemData
            Public 累計実車走行 As BaseCore.Common.Field.ItemData
            Public 累計迎車走行 As BaseCore.Common.Field.ItemData
            Public 累計営業回数 As BaseCore.Common.Field.ItemData
            Public 累計以後回数 As BaseCore.Common.Field.ItemData
            Public 累計迎車回数 As BaseCore.Common.Field.ItemData
            Public 一日全走行 As BaseCore.Common.Field.ItemData
            Public 一日実車走行 As BaseCore.Common.Field.ItemData
            Public 一日迎車走行 As BaseCore.Common.Field.ItemData
            Public 一日営業回数 As BaseCore.Common.Field.ItemData
            Public 一日以後回数 As BaseCore.Common.Field.ItemData
            Public 一日迎車回数 As BaseCore.Common.Field.ItemData
            Public 累計運賃 As BaseCore.Common.Field.ItemData
            Public 累計料金 As BaseCore.Common.Field.ItemData
            Public 累計身割回数 As BaseCore.Common.Field.ItemData
            Public 累計身割額 As BaseCore.Common.Field.ItemData
            Public 累計ワゴン回数 As BaseCore.Common.Field.ItemData
            Public 累計早朝回数 As BaseCore.Common.Field.ItemData
            Public 累計予約回数 As BaseCore.Common.Field.ItemData
            Public 累計遠割回数 As BaseCore.Common.Field.ItemData
            Public 累計遠割額 As BaseCore.Common.Field.ItemData
            Public 累計貸切指数 As BaseCore.Common.Field.ItemData
            Public 累計貸切割引額 As BaseCore.Common.Field.ItemData
            Public 累計貸切料金 As BaseCore.Common.Field.ItemData
            Public 累計貸切時間 As BaseCore.Common.Field.ItemData
            Public 累計貸切走行 As BaseCore.Common.Field.ItemData
            Public 累計固定料金キャンセル回数 As BaseCore.Common.Field.ItemData
            Public 累計迎車キャンセル回数 As BaseCore.Common.Field.ItemData
            Public 累計待回数 As BaseCore.Common.Field.ItemData
            Public 累計待加算回数 As BaseCore.Common.Field.ItemData
            Public 累計早朝キャンセル回数 As BaseCore.Common.Field.ItemData
            Public 累計高齢割回数 As BaseCore.Common.Field.ItemData
            Public 累計高齢割額 As BaseCore.Common.Field.ItemData
            Public 累計幼児割回数 As BaseCore.Common.Field.ItemData
            Public 累計幼児割額 As BaseCore.Common.Field.ItemData
            Public 累計リセット回数 As BaseCore.Common.Field.ItemData
            Public 累計待回数P As BaseCore.Common.Field.ItemData
            Public 一日運賃 As BaseCore.Common.Field.ItemData
            Public 一日料金 As BaseCore.Common.Field.ItemData
            Public 一日身割回数 As BaseCore.Common.Field.ItemData
            Public 一日身割額 As BaseCore.Common.Field.ItemData
            Public 一日身割現収 As BaseCore.Common.Field.ItemData
            Public 一日ワゴン回数 As BaseCore.Common.Field.ItemData
            Public 一日早朝回数 As BaseCore.Common.Field.ItemData
            Public 一日予約回数 As BaseCore.Common.Field.ItemData
            Public 一日遠割回数 As BaseCore.Common.Field.ItemData
            Public 一日遠割額 As BaseCore.Common.Field.ItemData
            Public 一日貸切指数 As BaseCore.Common.Field.ItemData
            Public 一日貸切割引 As BaseCore.Common.Field.ItemData
            Public 一日貸切料金 As BaseCore.Common.Field.ItemData
            Public 一日貸切時間 As BaseCore.Common.Field.ItemData
            Public 一日貸切走行 As BaseCore.Common.Field.ItemData
            Public 一日固定料金キャンセル回数 As BaseCore.Common.Field.ItemData
            Public 一日迎車キャンセル回数 As BaseCore.Common.Field.ItemData
            Public 一日待ち回数 As BaseCore.Common.Field.ItemData
            Public 一日待ち加算回数 As BaseCore.Common.Field.ItemData
            Public 一日早朝キャンセル回数 As BaseCore.Common.Field.ItemData
            Public 一日高齢割回数 As BaseCore.Common.Field.ItemData
            Public 一日高齢割額 As BaseCore.Common.Field.ItemData
            Public 一日幼児割回数 As BaseCore.Common.Field.ItemData
            Public 一日幼児割額 As BaseCore.Common.Field.ItemData
            Public 一日リセット回数 As BaseCore.Common.Field.ItemData
            Public 一日待ち回数P As BaseCore.Common.Field.ItemData
            Public 基本料金 As BaseCore.Common.Field.ItemData
            Public 以後料金 As BaseCore.Common.Field.ItemData
            Public 固定料金 As BaseCore.Common.Field.ItemData
            Public 月間営収 As BaseCore.Common.Field.ItemData
            Public 営収 As BaseCore.Common.Field.ItemData
            Public 男 As BaseCore.Common.Field.ItemData
            Public 女 As BaseCore.Common.Field.ItemData
            Public 現金 As BaseCore.Common.Field.ItemData
            Public 未収 As BaseCore.Common.Field.ItemData
            Public クレジット As BaseCore.Common.Field.ItemData
            Public カード As BaseCore.Common.Field.ItemData
            Public ID As BaseCore.Common.Field.ItemData
            Public 交通系IC As BaseCore.Common.Field.ItemData
            Public キャブカード As BaseCore.Common.Field.ItemData
            Public プリペイドカード As BaseCore.Common.Field.ItemData
            Public WAON As BaseCore.Common.Field.ItemData
            Public メーター外料金 As BaseCore.Common.Field.ItemData
            Public クレジット回数 As BaseCore.Common.Field.ItemData
            Public 貸切回数 As BaseCore.Common.Field.ItemData
            Public 空車_ETC料金 As BaseCore.Common.Field.ItemData
            Public 実車_ETC料金 As BaseCore.Common.Field.ItemData
            Public 燃料合計 As BaseCore.Common.Field.ItemData
            Public 総勤務時間 As BaseCore.Common.Field.ItemData
            Public 総休憩時間 As BaseCore.Common.Field.ItemData
            Public 総回送時間 As BaseCore.Common.Field.ItemData
            Public 総空車停止時間 As BaseCore.Common.Field.ItemData
            Public リセット待回数 As BaseCore.Common.Field.ItemData
            Public 出庫_親メーター As BaseCore.Common.Field.ItemData
            Public 入庫_親メーター As BaseCore.Common.Field.ItemData
            Public 空車_最高速度 As BaseCore.Common.Field.ItemData
            Public 実車_最高速度 As BaseCore.Common.Field.ItemData
            Public 最高速度 As BaseCore.Common.Field.ItemData
            Public データNO As BaseCore.Common.Field.ItemData
            Public 乗車地 As BaseCore.Common.Field.ItemData
            Public 乗車日付 As BaseCore.Common.Field.ItemData
            Public 乗車時刻 As BaseCore.Common.Field.ItemData
            Public フィールド112 As BaseCore.Common.Field.ItemData
            Public フィールド113 As BaseCore.Common.Field.ItemData
            Public フィールド114 As BaseCore.Common.Field.ItemData
            Public フィールド115 As BaseCore.Common.Field.ItemData
            Public フィールド116 As BaseCore.Common.Field.ItemData
            Public メーター外 As BaseCore.Common.Field.ItemData
            Public 元料金 As BaseCore.Common.Field.ItemData
            Public 元現金 As BaseCore.Common.Field.ItemData
            Public 降車地 As BaseCore.Common.Field.ItemData
            Public 降車日付 As BaseCore.Common.Field.ItemData
            Public 降車時刻 As BaseCore.Common.Field.ItemData
            Public 備考 As BaseCore.Common.Field.ItemData
            Public 空車距離 As BaseCore.Common.Field.ItemData
            Public 空車時刻 As BaseCore.Common.Field.ItemData
            Public 空車走行時間 As BaseCore.Common.Field.ItemData
            Public 空車最高速度 As BaseCore.Common.Field.ItemData
            Public 迎車距離 As BaseCore.Common.Field.ItemData
            Public 支払区分 As BaseCore.Common.Field.ItemData
            Public タリフ区分 As BaseCore.Common.Field.ItemData
            Public 料金 As BaseCore.Common.Field.ItemData
            Public 実車時刻 As BaseCore.Common.Field.ItemData
            Public 実車距離 As BaseCore.Common.Field.ItemData
            Public 実車最高速度 As BaseCore.Common.Field.ItemData
            Public 空車停止時間 As BaseCore.Common.Field.ItemData

            '''========================================================================================
            ''' <SUMMARY>ｺﾝｽﾄﾗｸﾀ</SUMMARY>
            '''========================================================================================
            Public Sub New(ByVal Config As NodeCore.Common.Config)
                Dim tbl As New NodeDatabase.DataSetMaster.MレイアウトHDDataTable
                Dim tblW As New NodeDatabase.DataSetWork.W乗務記録DataTable

                Me.レイアウトコード = New BaseCore.Common.Field.ItemData(tbl.レイアウトCDColumn)
                Me.ファイル形式 = New BaseCore.Common.Field.ItemData(tbl.ファイル形式Column)
                Me.先頭タイトル = New BaseCore.Common.Field.ItemData(tbl.先頭タイトルColumn)
                Me.最終ディレクトリ = New BaseCore.Common.Field.ItemData(tbl.最終ディレクトリColumn)
                Me.取込日時 = New BaseCore.Common.Field.ItemData(tbl.登録日時Column)
                Me.ファイル名 = New BaseCore.Common.Field.ItemData("ファイル名", TypeCode.String)

                ' テーブル項目
                Me.セッションID = New BaseCore.Common.Field.ItemData(tblW.セッションIDColumn)
                Me.取込日付 = New BaseCore.Common.Field.ItemData(tblW.取込日付Column)
                Me.日報日付 = New BaseCore.Common.Field.ItemData(tblW.日報日付Column)
                Me.日報年月 = New BaseCore.Common.Field.ItemData(tblW.日報年月Column)
                Me.出庫時刻 = New BaseCore.Common.Field.ItemData(tblW.出庫時刻Column)
                Me.入庫時刻 = New BaseCore.Common.Field.ItemData(tblW.入庫時刻Column)
                Me.乗務員CD = New BaseCore.Common.Field.ItemData(tblW.乗務員CDColumn)
                Me.車両番号 = New BaseCore.Common.Field.ItemData(tblW.車両番号Column)
                Me.営業所CD = New BaseCore.Common.Field.ItemData(tblW.営業所CDColumn)
                Me.部門CD = New BaseCore.Common.Field.ItemData(tblW.部門CDColumn)
                Me.勤務CD = New BaseCore.Common.Field.ItemData(tblW.勤務CDColumn)
                Me.車種CD = New BaseCore.Common.Field.ItemData(tblW.車種CDColumn)
                Me.天気 = New BaseCore.Common.Field.ItemData(tblW.天気Column)
                Me.累計全走行 = New BaseCore.Common.Field.ItemData(tblW.累計全走行Column)
                Me.累計実車走行 = New BaseCore.Common.Field.ItemData(tblW.累計実車走行Column)
                Me.累計迎車走行 = New BaseCore.Common.Field.ItemData(tblW.累計迎車走行Column)
                Me.累計営業回数 = New BaseCore.Common.Field.ItemData(tblW.累計営業回数Column)
                Me.累計以後回数 = New BaseCore.Common.Field.ItemData(tblW.累計以後回数Column)
                Me.累計迎車回数 = New BaseCore.Common.Field.ItemData(tblW.累計迎車回数Column)
                Me.一日全走行 = New BaseCore.Common.Field.ItemData(tblW.一日全走行Column)
                Me.一日実車走行 = New BaseCore.Common.Field.ItemData(tblW.一日実車走行Column)
                Me.一日迎車走行 = New BaseCore.Common.Field.ItemData(tblW.一日迎車走行Column)
                Me.一日営業回数 = New BaseCore.Common.Field.ItemData(tblW.一日営業回数Column)
                Me.一日以後回数 = New BaseCore.Common.Field.ItemData(tblW.一日以後回数Column)
                Me.一日迎車回数 = New BaseCore.Common.Field.ItemData(tblW.一日迎車回数Column)
                Me.累計運賃 = New BaseCore.Common.Field.ItemData(tblW.累計運賃Column)
                Me.累計料金 = New BaseCore.Common.Field.ItemData(tblW.累計料金Column)
                Me.累計身割回数 = New BaseCore.Common.Field.ItemData(tblW.累計身割回数Column)
                Me.累計身割額 = New BaseCore.Common.Field.ItemData(tblW.累計身割額Column)
                Me.累計ワゴン回数 = New BaseCore.Common.Field.ItemData(tblW.累計ワゴン回数Column)
                Me.累計早朝回数 = New BaseCore.Common.Field.ItemData(tblW.累計早朝回数Column)
                Me.累計予約回数 = New BaseCore.Common.Field.ItemData(tblW.累計予約回数Column)
                Me.累計遠割回数 = New BaseCore.Common.Field.ItemData(tblW.累計遠割回数Column)
                Me.累計遠割額 = New BaseCore.Common.Field.ItemData(tblW.累計遠割額Column)
                Me.累計貸切指数 = New BaseCore.Common.Field.ItemData(tblW.累計貸切指数Column)
                Me.累計貸切割引額 = New BaseCore.Common.Field.ItemData(tblW.累計貸切割引額Column)
                Me.累計貸切料金 = New BaseCore.Common.Field.ItemData(tblW.累計貸切料金Column)
                Me.累計貸切時間 = New BaseCore.Common.Field.ItemData(tblW.累計貸切時間Column)
                Me.累計貸切走行 = New BaseCore.Common.Field.ItemData(tblW.累計貸切走行Column)
                Me.累計固定料金キャンセル回数 = New BaseCore.Common.Field.ItemData(tblW.累計固定料金キャンセル回数Column)
                Me.累計迎車キャンセル回数 = New BaseCore.Common.Field.ItemData(tblW.累計迎車キャンセル回数Column)
                Me.累計待回数 = New BaseCore.Common.Field.ItemData(tblW.累計待回数Column)
                Me.累計待加算回数 = New BaseCore.Common.Field.ItemData(tblW.累計待加算回数Column)
                Me.累計早朝キャンセル回数 = New BaseCore.Common.Field.ItemData(tblW.累計早朝キャンセル回数Column)
                Me.累計高齢割回数 = New BaseCore.Common.Field.ItemData(tblW.累計高齢割回数Column)
                Me.累計高齢割額 = New BaseCore.Common.Field.ItemData(tblW.累計高齢割額Column)
                Me.累計幼児割回数 = New BaseCore.Common.Field.ItemData(tblW.累計幼児割回数Column)
                Me.累計幼児割額 = New BaseCore.Common.Field.ItemData(tblW.累計幼児割額Column)
                Me.累計リセット回数 = New BaseCore.Common.Field.ItemData(tblW.累計リセット回数Column)
                Me.累計待回数P = New BaseCore.Common.Field.ItemData(tblW.累計待回数PColumn)
                Me.一日運賃 = New BaseCore.Common.Field.ItemData(tblW.一日運賃Column)
                Me.一日料金 = New BaseCore.Common.Field.ItemData(tblW.一日料金Column)
                Me.一日身割回数 = New BaseCore.Common.Field.ItemData(tblW.一日身割回数Column)
                Me.一日身割額 = New BaseCore.Common.Field.ItemData(tblW.一日身割額Column)
                Me.一日身割現収 = New BaseCore.Common.Field.ItemData(tblW.一日身割現収Column)
                Me.一日ワゴン回数 = New BaseCore.Common.Field.ItemData(tblW.一日ワゴン回数Column)
                Me.一日早朝回数 = New BaseCore.Common.Field.ItemData(tblW.一日早朝回数Column)
                Me.一日予約回数 = New BaseCore.Common.Field.ItemData(tblW.一日予約回数Column)
                Me.一日遠割回数 = New BaseCore.Common.Field.ItemData(tblW.一日遠割回数Column)
                Me.一日遠割額 = New BaseCore.Common.Field.ItemData(tblW.一日遠割額Column)
                Me.一日貸切指数 = New BaseCore.Common.Field.ItemData(tblW.一日貸切指数Column)
                Me.一日貸切割引 = New BaseCore.Common.Field.ItemData(tblW.一日貸切割引Column)
                Me.一日貸切料金 = New BaseCore.Common.Field.ItemData(tblW.一日貸切料金Column)
                Me.一日貸切時間 = New BaseCore.Common.Field.ItemData(tblW.一日貸切時間Column)
                Me.一日貸切走行 = New BaseCore.Common.Field.ItemData(tblW.一日貸切走行Column)
                Me.一日固定料金キャンセル回数 = New BaseCore.Common.Field.ItemData(tblW.一日固定料金キャンセル回数Column)
                Me.一日迎車キャンセル回数 = New BaseCore.Common.Field.ItemData(tblW.一日迎車キャンセル回数Column)
                Me.一日待ち回数 = New BaseCore.Common.Field.ItemData(tblW.一日待ち回数Column)
                Me.一日待ち加算回数 = New BaseCore.Common.Field.ItemData(tblW.一日待ち加算回数Column)
                Me.一日早朝キャンセル回数 = New BaseCore.Common.Field.ItemData(tblW.一日早朝キャンセル回数Column)
                Me.一日高齢割回数 = New BaseCore.Common.Field.ItemData(tblW.一日高齢割回数Column)
                Me.一日高齢割額 = New BaseCore.Common.Field.ItemData(tblW.一日高齢割額Column)
                Me.一日幼児割回数 = New BaseCore.Common.Field.ItemData(tblW.一日幼児割回数Column)
                Me.一日幼児割額 = New BaseCore.Common.Field.ItemData(tblW.一日幼児割額Column)
                Me.一日リセット回数 = New BaseCore.Common.Field.ItemData(tblW.一日リセット回数Column)
                Me.一日待ち回数P = New BaseCore.Common.Field.ItemData(tblW.一日待ち回数PColumn)
                Me.基本料金 = New BaseCore.Common.Field.ItemData(tblW.基本料金Column)
                Me.以後料金 = New BaseCore.Common.Field.ItemData(tblW.以後料金Column)
                Me.固定料金 = New BaseCore.Common.Field.ItemData(tblW.固定料金Column)
                Me.月間営収 = New BaseCore.Common.Field.ItemData(tblW.月間営収Column)
                Me.営収 = New BaseCore.Common.Field.ItemData(tblW.営収Column)
                Me.男 = New BaseCore.Common.Field.ItemData(tblW.男Column)
                Me.女 = New BaseCore.Common.Field.ItemData(tblW.女Column)
                Me.現金 = New BaseCore.Common.Field.ItemData(tblW.現金Column)
                Me.未収 = New BaseCore.Common.Field.ItemData(tblW.未収Column)
                Me.クレジット = New BaseCore.Common.Field.ItemData(tblW.クレジットColumn)
                Me.カード = New BaseCore.Common.Field.ItemData(tblW.カードColumn)
                Me.ID = New BaseCore.Common.Field.ItemData(tblW.IDColumn)
                Me.交通系IC = New BaseCore.Common.Field.ItemData(tblW.交通系ICColumn)
                Me.キャブカード = New BaseCore.Common.Field.ItemData(tblW.キャブカードColumn)
                Me.プリペイドカード = New BaseCore.Common.Field.ItemData(tblW.プリペイドカードColumn)
                Me.WAON = New BaseCore.Common.Field.ItemData(tblW.WAONColumn)
                Me.メーター外料金 = New BaseCore.Common.Field.ItemData(tblW.メーター外料金Column)
                Me.クレジット回数 = New BaseCore.Common.Field.ItemData(tblW.クレジット回数Column)
                Me.貸切回数 = New BaseCore.Common.Field.ItemData(tblW.貸切回数Column)
                Me.空車_ETC料金 = New BaseCore.Common.Field.ItemData(tblW.空車_ETC料金Column)
                Me.実車_ETC料金 = New BaseCore.Common.Field.ItemData(tblW.実車_ETC料金Column)
                Me.燃料合計 = New BaseCore.Common.Field.ItemData(tblW.燃料合計Column)
                Me.総勤務時間 = New BaseCore.Common.Field.ItemData(tblW.総勤務時間Column)
                Me.総休憩時間 = New BaseCore.Common.Field.ItemData(tblW.総休憩時間Column)
                Me.総回送時間 = New BaseCore.Common.Field.ItemData(tblW.総回送時間Column)
                Me.総空車停止時間 = New BaseCore.Common.Field.ItemData(tblW.総空車停止時間Column)
                Me.リセット待回数 = New BaseCore.Common.Field.ItemData(tblW.リセット待回数Column)
                Me.出庫_親メーター = New BaseCore.Common.Field.ItemData(tblW.出庫_親メーターColumn)
                Me.入庫_親メーター = New BaseCore.Common.Field.ItemData(tblW.入庫_親メーターColumn)
                Me.空車_最高速度 = New BaseCore.Common.Field.ItemData(tblW.空車_最高速度Column)
                Me.実車_最高速度 = New BaseCore.Common.Field.ItemData(tblW.実車_最高速度Column)
                Me.最高速度 = New BaseCore.Common.Field.ItemData(tblW.最高速度Column)
                Me.データNO = New BaseCore.Common.Field.ItemData(tblW.データNOColumn)
                Me.乗車地 = New BaseCore.Common.Field.ItemData(tblW.乗車地Column)
                Me.乗車日付 = New BaseCore.Common.Field.ItemData(tblW.乗車日付Column)
                Me.乗車時刻 = New BaseCore.Common.Field.ItemData(tblW.乗車時刻Column)
                Me.フィールド112 = New BaseCore.Common.Field.ItemData(tblW.フィールド112Column)
                Me.フィールド113 = New BaseCore.Common.Field.ItemData(tblW.フィールド113Column)
                Me.フィールド114 = New BaseCore.Common.Field.ItemData(tblW.フィールド114Column)
                Me.フィールド115 = New BaseCore.Common.Field.ItemData(tblW.フィールド115Column)
                Me.フィールド116 = New BaseCore.Common.Field.ItemData(tblW.フィールド116Column)
                Me.メーター外 = New BaseCore.Common.Field.ItemData(tblW.メーター外Column)
                Me.元料金 = New BaseCore.Common.Field.ItemData(tblW.元料金Column)
                Me.元現金 = New BaseCore.Common.Field.ItemData(tblW.元現金Column)
                Me.降車地 = New BaseCore.Common.Field.ItemData(tblW.降車地Column)
                Me.降車日付 = New BaseCore.Common.Field.ItemData(tblW.降車日付Column)
                Me.降車時刻 = New BaseCore.Common.Field.ItemData(tblW.降車時刻Column)
                Me.備考 = New BaseCore.Common.Field.ItemData(tblW.備考Column)
                Me.空車距離 = New BaseCore.Common.Field.ItemData(tblW.空車距離Column)
                Me.空車時刻 = New BaseCore.Common.Field.ItemData(tblW.空車時刻Column)
                Me.空車走行時間 = New BaseCore.Common.Field.ItemData(tblW.空車走行時間Column)
                Me.空車最高速度 = New BaseCore.Common.Field.ItemData(tblW.空車最高速度Column)
                Me.迎車距離 = New BaseCore.Common.Field.ItemData(tblW.迎車距離Column)
                Me.支払区分 = New BaseCore.Common.Field.ItemData(tblW.支払区分Column)
                Me.タリフ区分 = New BaseCore.Common.Field.ItemData(tblW.タリフ区分Column)
                Me.料金 = New BaseCore.Common.Field.ItemData(tblW.料金Column)
                Me.実車時刻 = New BaseCore.Common.Field.ItemData(tblW.実車時刻Column)
                Me.実車距離 = New BaseCore.Common.Field.ItemData(tblW.実車距離Column)
                Me.実車最高速度 = New BaseCore.Common.Field.ItemData(tblW.実車最高速度Column)
                Me.空車停止時間 = New BaseCore.Common.Field.ItemData(tblW.空車停止時間Column)
            End Sub
        End Class
#End Region

#Region "変数定義"
        Public Class Paramater
            Public Header As Header
        End Class

        '''========================================================================================
        ''' <summary>ﾍｯﾀﾞ</summary>
        '''========================================================================================
        Private _Fields As New Paramater
        Public Property Fields() As Paramater
            Get
                Return _Fields
            End Get

            Set(ByVal value As Paramater)
                _Fields = value
            End Set
        End Property
#End Region

#Region "ｺﾝｽﾄﾗｸﾀ"
        '''========================================================================================
        ''' <SUMMARY>ｺﾝｽﾄﾗｸﾀ</SUMMARY>
        ''' <PARAM name="Security">ｾｷｭﾘﾃｨ ｸﾗｽ</PARAM>
        ''' <PARAM name="Config">ｺﾝﾌｨｸﾞ ｸﾗｽ</PARAM>
        ''' <PARAM name="PagePath">ﾍﾟｰｼﾞﾊﾟｽ</PARAM>
        '''========================================================================================
        Public Sub New(ByVal Security As NodeCore.Common.Security, ByVal Config As NodeCore.Common.Config, ByVal PagePath As String)
            '----------------------------------------------------------------------
            ' ｾｷｭﾘﾃｨ 設定
            '----------------------------------------------------------------------
            MyBase.New(Security, Config, PagePath)

            Me.Fields.Header = New Header(Config)
        End Sub
#End Region

#Region "ｺﾝｽﾀﾝﾄ"
        Public Structure ConstantExcel
            Public DUMMY As String

            Public Structure Header
                Public DUMMY As String
                Public Const SheetName = "記入表"
                Public Const RowTop = 0

                Public Structure 列数
                    Public DUMMY As String

                    Public Const 取込日付 = 0
                    Public Const 日報日付 = 1
                    Public Const 日報年月 = 2
                    Public Const 出庫時刻 = 3
                    Public Const 入庫時刻 = 4
                    Public Const 乗務員CD = 5
                    Public Const 車両番号 = 6
                    Public Const 営業所CD = 7
                    Public Const 部門CD = 8
                    Public Const 勤務CD = 9
                    Public Const 車種CD = 10
                    Public Const 天気 = 11
                    Public Const 累計全走行 = 12
                    Public Const 累計実車走行 = 13
                    Public Const 累計迎車走行 = 14
                    Public Const 累計営業回数 = 15
                    Public Const 累計以後回数 = 16
                    Public Const 累計迎車回数 = 17
                    Public Const 一日全走行 = 18
                    Public Const 一日実車走行 = 19
                    Public Const 一日迎車走行 = 20
                    Public Const 一日営業回数 = 21
                    Public Const 一日以後回数 = 22
                    Public Const 一日迎車回数 = 23
                    Public Const 累計運賃 = 24
                    Public Const 累計料金 = 25
                    Public Const 累計身割回数 = 26
                    Public Const 累計身割額 = 27
                    Public Const 累計ワゴン回数 = 28
                    Public Const 累計早朝回数 = 29
                    Public Const 累計予約回数 = 30
                    Public Const 累計遠割回数 = 31
                    Public Const 累計遠割額 = 32
                    Public Const 累計貸切指数 = 33
                    Public Const 累計貸切割引額 = 34
                    Public Const 累計貸切料金 = 35
                    Public Const 累計貸切時間 = 36
                    Public Const 累計貸切走行 = 37
                    Public Const 累計固定料金キャンセル回数 = 38
                    Public Const 累計迎車キャンセル回数 = 39
                    Public Const 累計待回数 = 40
                    Public Const 累計待加算回数 = 41
                    Public Const 累計早朝キャンセル回数 = 42
                    Public Const 累計高齢割回数 = 43
                    Public Const 累計高齢割額 = 44
                    Public Const 累計幼児割回数 = 45
                    Public Const 累計幼児割額 = 46
                    Public Const 累計リセット回数 = 47
                    Public Const 累計待回数P = 48
                    Public Const 一日運賃 = 49
                    Public Const 一日料金 = 50
                    Public Const 一日身割回数 = 51
                    Public Const 一日身割額 = 52
                    Public Const 一日身割現収 = 53
                    Public Const 一日ワゴン回数 = 54
                    Public Const 一日早朝回数 = 55
                    Public Const 一日予約回数 = 56
                    Public Const 一日遠割回数 = 57
                    Public Const 一日遠割額 = 58
                    Public Const 一日貸切指数 = 59
                    Public Const 一日貸切割引 = 60
                    Public Const 一日貸切料金 = 61
                    Public Const 一日貸切時間 = 62
                    Public Const 一日貸切走行 = 63
                    Public Const 一日固定料金キャンセル回数 = 64
                    Public Const 一日迎車キャンセル回数 = 65
                    Public Const 一日待ち回数 = 66
                    Public Const 一日待ち加算回数 = 67
                    Public Const 一日早朝キャンセル回数 = 68
                    Public Const 一日高齢割回数 = 69
                    Public Const 一日高齢割額 = 70
                    Public Const 一日幼児割回数 = 71
                    Public Const 一日幼児割額 = 72
                    Public Const 一日リセット回数 = 73
                    Public Const 一日待ち回数P = 74
                    Public Const 基本料金 = 75
                    Public Const 以後料金 = 76
                    Public Const 固定料金 = 77
                    Public Const 月間営収 = 78
                    Public Const 営収 = 79
                    Public Const 男 = 80
                    Public Const 女 = 81
                    Public Const 現金 = 82
                    Public Const 未収 = 83
                    Public Const クレジット = 84
                    Public Const カード = 85
                    Public Const ID = 86
                    Public Const 交通系IC = 87
                    Public Const キャブカード = 88
                    Public Const プリペイドカード = 89
                    Public Const WAON = 90
                    Public Const メーター外料金 = 91
                    Public Const クレジット回数 = 92
                    Public Const 貸切回数 = 93
                    Public Const 空車_ETC料金 = 94
                    Public Const 実車_ETC料金 = 95
                    Public Const 燃料合計 = 96
                    Public Const 総勤務時間 = 97
                    Public Const 総休憩時間 = 98
                    Public Const 総回送時間 = 99
                    Public Const 総空車停止時間 = 100
                    Public Const リセット待回数 = 101
                    Public Const 出庫_親メーター = 102
                    Public Const 入庫_親メーター = 103
                    Public Const 空車_最高速度 = 104
                    Public Const 実車_最高速度 = 105
                    Public Const 最高速度 = 106
                    Public Const データNO = 107
                    Public Const 乗車地 = 108
                    Public Const 乗車日付 = 109
                    Public Const 乗車時刻 = 110
                    Public Const フィールド112 = 111
                    Public Const フィールド113 = 112
                    Public Const フィールド114 = 113
                    Public Const フィールド115 = 114
                    Public Const フィールド116 = 115
                    Public Const メーター外 = 116
                    Public Const 元料金 = 117
                    Public Const 元現金 = 118
                    Public Const 降車地 = 119
                    Public Const 降車日付 = 120
                    Public Const 降車時刻 = 121
                    Public Const 備考 = 122
                    Public Const 空車距離 = 123
                    Public Const 空車時刻 = 124
                    Public Const 空車走行時間 = 125
                    Public Const 空車最高速度 = 126
                    Public Const 迎車距離 = 127
                    Public Const 支払区分 = 128
                    Public Const タリフ区分 = 129
                    Public Const 料金 = 130
                    Public Const 実車時刻 = 131
                    Public Const 実車距離 = 132
                    Public Const 実車最高速度 = 133
                    Public Const 空車停止時間 = 134

                End Structure
            End Structure
            Public Structure エラー
                Public Const SheetName = "エラー内容"
            End Structure
        End Structure
#End Region

#Region "Clear"
        '''========================================================================================
        ''' <SUMMARY>ｸﾘｱ</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Clear_Header(Optional ByVal KeyClear As Boolean = False) As Boolean
            '----------------------------------------------------------------------
            ' ｷｰ部ｸﾘｱ
            '----------------------------------------------------------------------
            If KeyClear Then
                Me.Fields.Header.レイアウトコード.Value = Nothing
            End If

            '----------------------------------------------------------------------
            ' ﾃﾞｰﾀ部ｸﾘｱ
            '----------------------------------------------------------------------
            Me.Fields.Header.ファイル形式.Value = NodeContents.Constant.CodeValue.ファイル形式.Csv
            Me.Fields.Header.先頭タイトル.Value = 1
            Me.Fields.Header.最終ディレクトリ.Value = Nothing

            Me.Fields.Header.取込日時.Value = Nothing
            Me.Fields.Header.ファイル名.Value = Nothing

            Me.Fields.Header.セッションID.Value = Nothing
            Me.Fields.Header.取込日付.Value = Nothing
            Me.Fields.Header.日報日付.Value = Nothing
            Me.Fields.Header.日報年月.Value = Nothing
            Me.Fields.Header.出庫時刻.Value = Nothing
            Me.Fields.Header.入庫時刻.Value = Nothing
            Me.Fields.Header.乗務員CD.Value = Nothing
            Me.Fields.Header.車両番号.Value = Nothing
            Me.Fields.Header.営業所CD.Value = Nothing
            Me.Fields.Header.部門CD.Value = Nothing
            Me.Fields.Header.勤務CD.Value = Nothing
            Me.Fields.Header.車種CD.Value = Nothing
            Me.Fields.Header.天気.Value = Nothing
            Me.Fields.Header.累計全走行.Value = Nothing
            Me.Fields.Header.累計実車走行.Value = Nothing
            Me.Fields.Header.累計迎車走行.Value = Nothing
            Me.Fields.Header.累計営業回数.Value = Nothing
            Me.Fields.Header.累計以後回数.Value = Nothing
            Me.Fields.Header.累計迎車回数.Value = Nothing
            Me.Fields.Header.一日全走行.Value = Nothing
            Me.Fields.Header.一日実車走行.Value = Nothing
            Me.Fields.Header.一日迎車走行.Value = Nothing
            Me.Fields.Header.一日営業回数.Value = Nothing
            Me.Fields.Header.一日以後回数.Value = Nothing
            Me.Fields.Header.一日迎車回数.Value = Nothing
            Me.Fields.Header.累計運賃.Value = Nothing
            Me.Fields.Header.累計料金.Value = Nothing
            Me.Fields.Header.累計身割回数.Value = Nothing
            Me.Fields.Header.累計身割額.Value = Nothing
            Me.Fields.Header.累計ワゴン回数.Value = Nothing
            Me.Fields.Header.累計早朝回数.Value = Nothing
            Me.Fields.Header.累計予約回数.Value = Nothing
            Me.Fields.Header.累計遠割回数.Value = Nothing
            Me.Fields.Header.累計遠割額.Value = Nothing
            Me.Fields.Header.累計貸切指数.Value = Nothing
            Me.Fields.Header.累計貸切割引額.Value = Nothing
            Me.Fields.Header.累計貸切料金.Value = Nothing
            Me.Fields.Header.累計貸切時間.Value = Nothing
            Me.Fields.Header.累計貸切走行.Value = Nothing
            Me.Fields.Header.累計固定料金キャンセル回数.Value = Nothing
            Me.Fields.Header.累計迎車キャンセル回数.Value = Nothing
            Me.Fields.Header.累計待回数.Value = Nothing
            Me.Fields.Header.累計待加算回数.Value = Nothing
            Me.Fields.Header.累計早朝キャンセル回数.Value = Nothing
            Me.Fields.Header.累計高齢割回数.Value = Nothing
            Me.Fields.Header.累計高齢割額.Value = Nothing
            Me.Fields.Header.累計幼児割回数.Value = Nothing
            Me.Fields.Header.累計幼児割額.Value = Nothing
            Me.Fields.Header.累計リセット回数.Value = Nothing
            Me.Fields.Header.累計待回数P.Value = Nothing
            Me.Fields.Header.一日運賃.Value = Nothing
            Me.Fields.Header.一日料金.Value = Nothing
            Me.Fields.Header.一日身割回数.Value = Nothing
            Me.Fields.Header.一日身割額.Value = Nothing
            Me.Fields.Header.一日身割現収.Value = Nothing
            Me.Fields.Header.一日ワゴン回数.Value = Nothing
            Me.Fields.Header.一日早朝回数.Value = Nothing
            Me.Fields.Header.一日予約回数.Value = Nothing
            Me.Fields.Header.一日遠割回数.Value = Nothing
            Me.Fields.Header.一日遠割額.Value = Nothing
            Me.Fields.Header.一日貸切指数.Value = Nothing
            Me.Fields.Header.一日貸切割引.Value = Nothing
            Me.Fields.Header.一日貸切料金.Value = Nothing
            Me.Fields.Header.一日貸切時間.Value = Nothing
            Me.Fields.Header.一日貸切走行.Value = Nothing
            Me.Fields.Header.一日固定料金キャンセル回数.Value = Nothing
            Me.Fields.Header.一日迎車キャンセル回数.Value = Nothing
            Me.Fields.Header.一日待ち回数.Value = Nothing
            Me.Fields.Header.一日待ち加算回数.Value = Nothing
            Me.Fields.Header.一日早朝キャンセル回数.Value = Nothing
            Me.Fields.Header.一日高齢割回数.Value = Nothing
            Me.Fields.Header.一日高齢割額.Value = Nothing
            Me.Fields.Header.一日幼児割回数.Value = Nothing
            Me.Fields.Header.一日幼児割額.Value = Nothing
            Me.Fields.Header.一日リセット回数.Value = Nothing
            Me.Fields.Header.一日待ち回数P.Value = Nothing
            Me.Fields.Header.基本料金.Value = Nothing
            Me.Fields.Header.以後料金.Value = Nothing
            Me.Fields.Header.固定料金.Value = Nothing
            Me.Fields.Header.月間営収.Value = Nothing
            Me.Fields.Header.営収.Value = Nothing
            Me.Fields.Header.男.Value = Nothing
            Me.Fields.Header.女.Value = Nothing
            Me.Fields.Header.現金.Value = Nothing
            Me.Fields.Header.未収.Value = Nothing
            Me.Fields.Header.クレジット.Value = Nothing
            Me.Fields.Header.カード.Value = Nothing
            Me.Fields.Header.ID.Value = Nothing
            Me.Fields.Header.交通系IC.Value = Nothing
            Me.Fields.Header.キャブカード.Value = Nothing
            Me.Fields.Header.プリペイドカード.Value = Nothing
            Me.Fields.Header.WAON.Value = Nothing
            Me.Fields.Header.メーター外料金.Value = Nothing
            Me.Fields.Header.クレジット回数.Value = Nothing
            Me.Fields.Header.貸切回数.Value = Nothing
            Me.Fields.Header.空車_ETC料金.Value = Nothing
            Me.Fields.Header.実車_ETC料金.Value = Nothing
            Me.Fields.Header.燃料合計.Value = Nothing
            Me.Fields.Header.総勤務時間.Value = Nothing
            Me.Fields.Header.総休憩時間.Value = Nothing
            Me.Fields.Header.総回送時間.Value = Nothing
            Me.Fields.Header.総空車停止時間.Value = Nothing
            Me.Fields.Header.リセット待回数.Value = Nothing
            Me.Fields.Header.出庫_親メーター.Value = Nothing
            Me.Fields.Header.入庫_親メーター.Value = Nothing
            Me.Fields.Header.空車_最高速度.Value = Nothing
            Me.Fields.Header.実車_最高速度.Value = Nothing
            Me.Fields.Header.最高速度.Value = Nothing
            Me.Fields.Header.データNO.Value = Nothing
            Me.Fields.Header.乗車地.Value = Nothing
            Me.Fields.Header.乗車日付.Value = Nothing
            Me.Fields.Header.乗車時刻.Value = Nothing
            Me.Fields.Header.フィールド112.Value = Nothing
            Me.Fields.Header.フィールド113.Value = Nothing
            Me.Fields.Header.フィールド114.Value = Nothing
            Me.Fields.Header.フィールド115.Value = Nothing
            Me.Fields.Header.フィールド116.Value = Nothing
            Me.Fields.Header.メーター外.Value = Nothing
            Me.Fields.Header.元料金.Value = Nothing
            Me.Fields.Header.元現金.Value = Nothing
            Me.Fields.Header.降車地.Value = Nothing
            Me.Fields.Header.降車日付.Value = Nothing
            Me.Fields.Header.降車時刻.Value = Nothing
            Me.Fields.Header.備考.Value = Nothing
            Me.Fields.Header.空車距離.Value = Nothing
            Me.Fields.Header.空車時刻.Value = Nothing
            Me.Fields.Header.空車走行時間.Value = Nothing
            Me.Fields.Header.空車最高速度.Value = Nothing
            Me.Fields.Header.迎車距離.Value = Nothing
            Me.Fields.Header.支払区分.Value = Nothing
            Me.Fields.Header.タリフ区分.Value = Nothing
            Me.Fields.Header.料金.Value = Nothing
            Me.Fields.Header.実車時刻.Value = Nothing
            Me.Fields.Header.実車距離.Value = Nothing
            Me.Fields.Header.実車最高速度.Value = Nothing
            Me.Fields.Header.空車停止時間.Value = Nothing
            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "Read"
        '''========================================================================================
        ''' <SUMMARY>読込</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Read_Header() As Boolean
            '----------------------------------------------------------------------
            ' 読み込み
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetFuncTableAdapters.F0レイアウトTableAdapter
            Dim tbl As New NodeDatabase.DataSetFunc.F0レイアウトDataTable
            Dim qry As New Collection

            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl.レイアウトCDColumn.ColumnName, Me.Fields.Header.レイアウトコード.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition("グループCD", Me.Security.グループCD, BaseDatabase.Contents.Compare.Parameter))
            qry.Add(New BaseDatabase.Condition("個社CD", Me.Security.個社CD, BaseDatabase.Contents.Compare.Parameter))
            qry.Add(New BaseDatabase.Condition("子会社CD", Me.Security.子会社CD, BaseDatabase.Contents.Compare.Parameter))

            Dim strSort As String = ""
            Dim strDelim As String = ""

            strSort &= strDelim & tbl.レイアウトCDColumn.ColumnName & " ASC" : strDelim = ","

            tbl = ada.SelectByCommon(qry, strSort)
            If tbl.Count > 0 Then
                Me.Fields.Header.ファイル形式.Value = tbl(0).ファイル形式
                Me.Fields.Header.先頭タイトル.Value = tbl(0).先頭タイトル
            End If

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "Execute1（制御）"
        '''========================================================================================
        ''' <SUMMARY>実行</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Execute1_Header() As Boolean
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim adaレイ As New NodeDatabase.DataSetFuncTableAdapters.F0レイアウトTableAdapter
            Dim tblレイ As New NodeDatabase.DataSetFunc.F0レイアウトDataTable
            Dim qryレイ As New Collection

            Dim ada条件 As New NodeDatabase.DataSetFuncTableAdapters.F0レイアウト条件TableAdapter
            Dim tbl条件 As New NodeDatabase.DataSetFunc.F0レイアウト条件DataTable
            Dim qry条件 As New Collection

            Dim strSort As String = ""
            Dim strNow As String = Format(Now, "yyyy/MM/dd HH:mm:ss")
            Dim Excel As New BaseCore.Common.Excel
            Dim intVirtualNo As Integer = 0 '仮番

            Dim uploadFilename As String = System.IO.Path.GetFileName(Me.Path3)

            '----------------------------------------------------------------------
            ' ﾚｲｱｳﾄ読み込み
            '----------------------------------------------------------------------
            strSort = tblレイ.取込優先順Column.ColumnName _
              & "," & tblレイ.カラム位置Column.ColumnName

            qryレイ.Clear()
            qryレイ.Add(New BaseDatabase.Condition(tblレイ.レイアウトCDColumn.ColumnName, Me.Fields.Header.レイアウトコード.Value, BaseDatabase.Contents.Compare.Equal))
            qryレイ.Add(New BaseDatabase.Condition("グループCD", Me.Security.グループCD, BaseDatabase.Contents.Compare.Parameter))
            qryレイ.Add(New BaseDatabase.Condition("個社CD", Me.Security.個社CD, BaseDatabase.Contents.Compare.Parameter))
            qryレイ.Add(New BaseDatabase.Condition("子会社CD", Me.Security.子会社CD, BaseDatabase.Contents.Compare.Parameter))
            tblレイ = adaレイ.SelectByCommon(qryレイ, strSort)

            If tblレイ.Count = 0 Then
                MyBase.LastError = "レイアウトが存在しません。"
                Return False
            End If

            qry条件.Clear()
            qry条件.Add(New BaseDatabase.Condition(tbl条件.レイアウトCDColumn.ColumnName, Me.Fields.Header.レイアウトコード.Value, BaseDatabase.Contents.Compare.Equal))
            qry条件.Add(New BaseDatabase.Condition("グループCD", Me.Security.グループCD, BaseDatabase.Contents.Compare.Parameter))
            qry条件.Add(New BaseDatabase.Condition("個社CD", Me.Security.個社CD, BaseDatabase.Contents.Compare.Parameter))
            qry条件.Add(New BaseDatabase.Condition("子会社CD", Me.Security.子会社CD, BaseDatabase.Contents.Compare.Parameter))
            tbl条件 = ada条件.SelectByCommon(qry条件)

            '----------------------------------------------------------------------
            ' ﾃﾝﾌﾟﾚｰﾄをｺﾋﾟｰ
            '----------------------------------------------------------------------
            Dim strPathTemplate As String = Me.Config.Dirテンプレート & "Base.xlsx"
            System.IO.File.Copy(strPathTemplate, Me.Path2)

            '----------------------------------------------------------------------
            ' ｵｰﾌﾟﾝ
            '----------------------------------------------------------------------
            If Not Excel.Open(Me.Path2) Then
                Me.LastError = "正しいファイルを選択してください。"
                Return False
            End If

            '----------------------------------------------------------------------
            ' ｱｸﾃｨﾌﾞｼｰﾄ
            '----------------------------------------------------------------------
            Excel.SheetSelect(0)

            '----------------------------------------------------------------------
            ' ﾌｧｲﾙﾀｲﾌﾟ別処理
            '----------------------------------------------------------------------
            Try
                '----------------------------------------------------------------------
                ' 
                ' ﾌｧｲﾙﾀｲﾌﾟ別処理 各媒体→基本Excelへ
                ' 
                '----------------------------------------------------------------------
                Select Case True
                    Case Me.Fields.Header.ファイル形式.Value = NodeContents.Constant.CodeValue.ファイル形式.Excel
                        If Not Excel_Excel(tblレイ, tbl条件, Excel, strNow) Then
                            MyBase.LastError = Me.LastError
                            Return False
                        End If

                    Case Me.Fields.Header.ファイル形式.Value = NodeContents.Constant.CodeValue.ファイル形式.Csv
                        If Not Csv_Excel(tblレイ, tbl条件, Excel, strNow, NodeContents.Constant.CodeValue.ファイル形式.Csv) Then
                            MyBase.LastError = Me.LastError
                            Return False
                        End If
                End Select

                '----------------------------------------------------------------------
                ' 
                ' 基本Excel→ﾜｰｸ
                ' 
                '----------------------------------------------------------------------

                '----------------------------------------------------------------------
                ' 事前削除
                '----------------------------------------------------------------------
                Me.WorkDelete()

                '----------------------------------------------------------------------
                ' 事前検査
                '----------------------------------------------------------------------
                If Not Excel_Validator(Excel) Then
                    Me.LastError = "Excelの内容に誤りがあります。"
                    Return False
                End If

                '----------------------------------------------------------------------
                ' 初期化
                '----------------------------------------------------------------------
                Me.Clear(True)

                '----------------------------------------------------------------------
                ' ｲﾝﾎﾟｰﾄ
                '----------------------------------------------------------------------
                Dim LastRow As Integer = Excel.LastRowIndex        '最大行
                Dim LastCol As Integer = Excel.LastColIndex        '最大列


                Using scope As New System.Transactions.TransactionScope(System.Transactions.TransactionScopeOption.Required, TimeSpan.Zero)

                    For i As Long = 0 To LastRow - 1
                        '------------------------------------------------------------------
                        ' ﾀｲﾄﾙ行 
                        '------------------------------------------------------------------
                        If i = 0 Then
                            Continue For
                        End If

                        '------------------------------------------------------------------
                        ' 登録
                        '------------------------------------------------------------------

                        '----------------------------------------------------------------------
                        ' ﾌﾚｰﾑﾜｰｸ設定 ﾍｯﾀﾞ
                        '----------------------------------------------------------------------
                        If Not Me.FrameSet_Header(Excel, i, strNow) Then
                            Me.LastError = "ヘッダー情報書き込みできませんでした。"
                            Return False
                        End If

                        '----------------------------------------------------------------------
                        ' ﾜｰｸ書込
                        '----------------------------------------------------------------------
                        If Not Me.Write_Work(intVirtualNo, strNow) Then
                            Me.LastError = "ワーク情報書き込みできませんでした。"
                            Return False
                        End If

                        '----------------------------------------------------------------------
                        ' 生データ書込
                        '----------------------------------------------------------------------
                        If Not Me.Write_RawData(intVirtualNo, strNow) Then
                            Me.LastError = "CSV情報書き込みできませんでした。"
                            Return False
                        End If
                    Next


                    '----------------------------------------------------------------------
                    ' ｽﾄｱﾄﾞ実行
                    '----------------------------------------------------------------------
                    Dim adaStored As New NodeDatabase.DataSetStoredTableAdapters.P0乗務記録作成一括TableAdapter
                    Dim intStatus As Integer

                    intStatus = adaStored.Execute(Me.Security.セッションID)

                    '----------------------------------------------------------------------
                    ' ｴﾗｰ処理
                    '----------------------------------------------------------------------
                    If intStatus <> 0 Then
                        Me.SetError(Excel)

                        Me.LastError = "Excelの内容に誤りがあります。"
                        Return False
                    End If

                    scope.Complete()
                End Using


            Catch ex As Exception
                MyBase.LastError = "以下のエラーの為、処理を続行できません。" & vbCrLf & ex.Message
                Return False
            Finally
                Excel.Close(Me.Path2)
                Me.WorkDelete()
            End Try

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "ﾌｧｲﾙﾀｲﾌﾟ別処理"
#Region "Excel→Excel"
        '''========================================================================================
        ''' <SUMMARY>Excel→Excel</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Function Excel_Excel(ByVal tblレイ As NodeDatabase.DataSetFunc.F0レイアウトDataTable _
                                     , ByVal tbl条件 As NodeDatabase.DataSetFunc.F0レイアウト条件DataTable _
                                     , ByRef _xlsBase As BaseCore.Common.Excel _
                                     , ByVal strNow As String
                                      ) As Boolean
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetTranTableAdapters.T乗務記録TableAdapter
            Dim tbl As New NodeDatabase.DataSetTran.T乗務記録DataTable
            Dim qry As New Collection

            Dim tblWork As New NodeDatabase.DataSetWork.W乗務記録DataTable

            Dim _xlsSource As New BaseCore.Common.Excel

            '----------------------------------------------------------------------
            ' ｵｰﾌﾟﾝ
            '----------------------------------------------------------------------
            'ｱｯﾌﾟﾛｰﾄﾞﾌｧｲﾙ
            If Not _xlsSource.Open(Me.Path1) Then
                Me.LastError = "正しいファイルを選択してください。"
                Return False
            End If

            '----------------------------------------------------------------------
            ' ｱｸﾃｨﾌﾞｼｰﾄ
            '----------------------------------------------------------------------
            _xlsSource.SheetSelect(0)

            Dim RowMax As Long = _xlsSource.LastRowIndex             '最大行
            Dim ClmMax As Long = _xlsSource.LastColIndex             '最大列

            '----------------------------------------------------------------------
            ' ｲﾝﾎﾟｰﾄ
            '----------------------------------------------------------------------
            Dim intLine As Integer = ConstantExcel.Header.RowTop

            Try
                For i As Long = ConstantExcel.Header.RowTop To RowMax - 1
                    '------------------------------------------------------------------
                    ' ﾀｲﾄﾙ行
                    '------------------------------------------------------------------
                    If i = ConstantExcel.Header.RowTop And Not Me.Fields.Header.先頭タイトル.Value = NodeContents.Constant.CodeValue.フラグID.オフ Then
                        Continue For
                    End If

                    '------------------------------------------------------------------
                    ' ｶｳﾝﾄｱｯﾌﾟ
                    '------------------------------------------------------------------
                    intLine += 1

                    '----------------------------------------------------------------------
                    ' 値設定(ﾍｯﾀﾞ)
                    '----------------------------------------------------------------------
                    For Each rowレイ As NodeDatabase.DataSetFunc.F0レイアウトRow In tblレイ.Rows

                        '既定値入力は次へ
                        If rowレイ.Item(tblレイ.既定値Column.ColumnName).ToString <> "" Then
                            Continue For
                        End If

                        Dim FieldName As String = rowレイ.Item(tblレイ.伝票項目名Column.ColumnName)
                        Dim Position As Integer = rowレイ.Item(tblレイ.カラム位置Column.ColumnName)

                        Select Case FieldName.ToString '結合無はｸﾘｱ時、ﾃﾞｨﾌﾙﾄが設定されている項目に限る
                            Case tbl.取込日付Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.取込日付, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.取込日付).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.日報日付Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.日報日付, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.日報日付).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.日報年月Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.日報年月, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.日報年月).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.出庫時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.出庫時刻, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.出庫時刻).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.入庫時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.入庫時刻, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.入庫時刻).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.乗務員CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.乗務員CD, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.乗務員CD).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.車両番号Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.車両番号, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.車両番号).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.営業所CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.営業所CD, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.営業所CD).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.部門CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.部門CD, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.部門CD).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.勤務CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.勤務CD, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.勤務CD).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.車種CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.車種CD, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.車種CD).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.天気Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.天気, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.天気).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計全走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計全走行, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計全走行).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計実車走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計実車走行, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計実車走行).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計迎車走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計迎車走行, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計迎車走行).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計営業回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計営業回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計営業回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計以後回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計以後回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計以後回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計迎車回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計迎車回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計迎車回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日全走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日全走行, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日全走行).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日実車走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日実車走行, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日実車走行).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日迎車走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日迎車走行, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日迎車走行).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日営業回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日営業回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日営業回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日以後回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日以後回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日以後回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日迎車回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日迎車回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日迎車回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計運賃Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計運賃, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計運賃).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計料金, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計料金).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計身割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計身割回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計身割回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計身割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計身割額, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計身割額).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計ワゴン回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計ワゴン回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計ワゴン回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計早朝回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計早朝回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計早朝回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計予約回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計予約回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計予約回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計遠割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計遠割回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計遠割回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計遠割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計遠割額, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計遠割額).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計貸切指数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計貸切指数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計貸切指数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計貸切割引額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計貸切割引額, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計貸切割引額).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計貸切料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計貸切料金, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計貸切料金).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計貸切時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計貸切時間, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計貸切時間).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計貸切走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計貸切走行, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計貸切走行).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計固定料金キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計固定料金キャンセル回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計固定料金キャンセル回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計迎車キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計迎車キャンセル回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計迎車キャンセル回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計待回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計待回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計待回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計待加算回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計待加算回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計待加算回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計早朝キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計早朝キャンセル回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計早朝キャンセル回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計高齢割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計高齢割回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計高齢割回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計高齢割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計高齢割額, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計高齢割額).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計幼児割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計幼児割回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計幼児割回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計幼児割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計幼児割額, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計幼児割額).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計リセット回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計リセット回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計リセット回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.累計待回数PColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計待回数P, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計待回数P).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日運賃Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日運賃, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日運賃).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日料金, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日料金).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日身割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日身割回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日身割回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日身割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日身割額, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日身割額).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日身割現収Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日身割現収, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日身割現収).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日ワゴン回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日ワゴン回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日ワゴン回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日早朝回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日早朝回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日早朝回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日予約回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日予約回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日予約回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日遠割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日遠割回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日遠割回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日遠割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日遠割額, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日遠割額).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日貸切指数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日貸切指数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日貸切指数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日貸切割引Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日貸切割引, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日貸切割引).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日貸切料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日貸切料金, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日貸切料金).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日貸切時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日貸切時間, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日貸切時間).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日貸切走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日貸切走行, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日貸切走行).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日固定料金キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日固定料金キャンセル回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日固定料金キャンセル回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日迎車キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日迎車キャンセル回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日迎車キャンセル回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日待ち回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日待ち回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日待ち回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日待ち加算回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日待ち加算回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日待ち加算回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日早朝キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日早朝キャンセル回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日早朝キャンセル回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日高齢割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日高齢割回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日高齢割回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日高齢割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日高齢割額, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日高齢割額).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日幼児割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日幼児割回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日幼児割回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日幼児割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日幼児割額, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日幼児割額).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日リセット回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日リセット回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日リセット回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.一日待ち回数PColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日待ち回数P, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日待ち回数P).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.基本料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.基本料金, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.基本料金).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.以後料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.以後料金, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.以後料金).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.固定料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.固定料金, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.固定料金).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.月間営収Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.月間営収, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.月間営収).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.営収Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.営収, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.営収).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.男Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.男, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.男).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.女Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.女, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.女).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.現金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.現金, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.現金).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.未収Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.未収, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.未収).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.クレジットColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.クレジット, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.クレジット).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.カードColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.カード, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.カード).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.IDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.ID, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.ID).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.交通系ICColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.交通系IC, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.交通系IC).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.キャブカードColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.キャブカード, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.キャブカード).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.プリペイドカードColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.プリペイドカード, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.プリペイドカード).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.WAONColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.WAON, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.WAON).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.メーター外料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.メーター外料金, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.メーター外料金).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.クレジット回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.クレジット回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.クレジット回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.貸切回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.貸切回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.貸切回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.空車_ETC料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車_ETC料金, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.空車_ETC料金).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.実車_ETC料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.実車_ETC料金, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.実車_ETC料金).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.燃料合計Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.燃料合計, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.燃料合計).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.総勤務時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.総勤務時間, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.総勤務時間).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.総休憩時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.総休憩時間, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.総休憩時間).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.総回送時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.総回送時間, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.総回送時間).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.総空車停止時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.総空車停止時間, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.総空車停止時間).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.リセット待回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.リセット待回数, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.リセット待回数).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.出庫_親メーターColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.出庫_親メーター, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.出庫_親メーター).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.入庫_親メーターColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.入庫_親メーター, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.入庫_親メーター).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.空車_最高速度Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車_最高速度, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.空車_最高速度).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.実車_最高速度Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.実車_最高速度, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.実車_最高速度).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.最高速度Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.最高速度, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.最高速度).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.データNOColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.データNO, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.データNO).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.乗車地Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.乗車地, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.乗車地).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.乗車日付Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.乗車日付, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.乗車日付).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.乗車時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.乗車時刻, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.乗車時刻).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.フィールド112Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.フィールド112, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.フィールド112).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.フィールド113Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.フィールド113, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.フィールド113).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.フィールド114Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.フィールド114, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.フィールド114).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.フィールド115Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.フィールド115, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.フィールド115).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.フィールド116Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.フィールド116, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.フィールド116).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.メーター外Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.メーター外, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.メーター外).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.元料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.元料金, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.元料金).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.元現金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.元現金, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.元現金).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.降車地Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.降車地, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.降車地).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.降車日付Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.降車日付, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.降車日付).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.降車時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.降車時刻, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.降車時刻).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.備考Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.備考, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.備考).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.空車距離Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車距離, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.空車距離).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.空車時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車時刻, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.空車時刻).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.空車走行時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車走行時間, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.空車走行時間).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.空車最高速度Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車最高速度, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.空車最高速度).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.迎車距離Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.迎車距離, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.迎車距離).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.支払区分Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.支払区分, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.支払区分).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.タリフ区分Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.タリフ区分, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.タリフ区分).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.料金, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.料金).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.実車時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.実車時刻, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.実車時刻).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.実車距離Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.実車距離, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.実車距離).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.実車最高速度Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.実車最高速度, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.実車最高速度).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))
                            Case tbl.空車停止時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車停止時間, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.空車停止時間).Value, "") & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, ""))

                            Case Else
                        End Select
                    Next

                    '----------------------------------------------------------------------
                    ' 変換ﾏｽﾀ
                    '----------------------------------------------------------------------
                    For Each row条件 As NodeDatabase.DataSetFunc.F0レイアウト条件Row In tbl条件.Rows
                        Select Case True
                            Case Not IsNumeric(row条件.Item(tbl条件.条件カラム位置Column.ColumnName))
                                Continue For
                            Case row条件.Item(tbl条件.条件カラム位置Column.ColumnName) < 0
                                Continue For
                            Case row条件.Item(tbl条件.条件カラム位置Column.ColumnName) > _xlsSource.LastColIndex
                                Continue For
                        End Select

                        Dim FieldPositionIf As Integer = row条件.Item(tbl条件.条件カラム位置Column.ColumnName)          'CsvとExcelの違い
                        Dim FieldValueIf As String = row条件.Item(tbl条件.条件項目値Column.ColumnName)

                        Dim SymbolCode As String = row条件.Item(tbl条件.符号区分Column.ColumnName)

                        Dim FieldCodeOt As String = row条件.Item(tbl条件.結果伝票項目CDColumn.ColumnName)
                        Dim FieldValueOt As String = row条件.Item(tbl条件.結果項目値Column.ColumnName)
                        Dim FieldNameOt As String = row条件.Item(tbl条件.結果伝票項目名Column.ColumnName)

                        '----------------------------------------------------------------------
                        ' 変換
                        '----------------------------------------------------------------------
                        'If JudgeValue(BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, FieldPositionIf).Value, ""), SymbolCode, FieldValueIf) Then
                        '    Select Case FieldNameOt.ToString
                        '        Case tbl.店舗CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.店舗CD, FieldValueOt)
                        '        Case tbl.納品日付Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.納品日付, FieldValueOt)
                        '        Case tbl.伝票番号Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.伝票番号, FieldValueOt)
                        '        Case tbl.行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.行, FieldValueOt)
                        '        Case tbl.JANCDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.JANCD, FieldValueOt)
                        '        Case tbl.商品名Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.商品名, FieldValueOt)
                        '        Case tbl.定価Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.定価, FieldValueOt)
                        '        Case tbl.数量Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.数量, FieldValueOt)
                        '        Case tbl.仕入単価Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.仕入単価, FieldValueOt)
                        '        Case tbl.仕入金額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.仕入金額, FieldValueOt)
                        '        Case tbl.消費税額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.消費税額, FieldValueOt)
                        '        Case tbl.消費税率Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.消費税率, FieldValueOt)
                        '        Case tbl.入数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.入数, FieldValueOt)
                        '        Case Else
                        '    End Select
                        'End If
                    Next

                    '----------------------------------------------------------------------
                    ' 既定値
                    '----------------------------------------------------------------------
                    For Each rowレイ As NodeDatabase.DataSetFunc.F0レイアウトRow In tblレイ.Rows
                        '未入力は次へ
                        If rowレイ.Item(tblレイ.既定値Column.ColumnName).ToString = "" Then
                            Continue For
                        End If

                        Dim FieldName As String = rowレイ.Item(tblレイ.伝票項目名Column.ColumnName)
                        Select Case FieldName.ToString
                            Case tbl.取込日付Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.取込日付, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.日報日付Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.日報日付, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.日報年月Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.日報年月, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.出庫時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.出庫時刻, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.入庫時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.入庫時刻, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.乗務員CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.乗務員CD, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.車両番号Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.車両番号, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.営業所CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.営業所CD, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.部門CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.部門CD, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.勤務CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.勤務CD, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.車種CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.車種CD, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.天気Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.天気, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計全走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計全走行, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計実車走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計実車走行, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計迎車走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計迎車走行, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計営業回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計営業回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計以後回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計以後回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計迎車回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計迎車回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日全走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日全走行, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日実車走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日実車走行, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日迎車走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日迎車走行, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日営業回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日営業回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日以後回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日以後回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日迎車回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日迎車回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計運賃Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計運賃, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計料金, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計身割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計身割回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計身割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計身割額, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計ワゴン回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計ワゴン回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計早朝回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計早朝回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計予約回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計予約回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計遠割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計遠割回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計遠割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計遠割額, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計貸切指数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計貸切指数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計貸切割引額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計貸切割引額, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計貸切料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計貸切料金, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計貸切時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計貸切時間, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計貸切走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計貸切走行, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計固定料金キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計固定料金キャンセル回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計迎車キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計迎車キャンセル回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計待回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計待回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計待加算回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計待加算回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計早朝キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計早朝キャンセル回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計高齢割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計高齢割回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計高齢割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計高齢割額, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計幼児割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計幼児割回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計幼児割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計幼児割額, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計リセット回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計リセット回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計待回数PColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計待回数P, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日運賃Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日運賃, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日料金, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日身割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日身割回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日身割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日身割額, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日身割現収Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日身割現収, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日ワゴン回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日ワゴン回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日早朝回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日早朝回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日予約回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日予約回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日遠割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日遠割回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日遠割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日遠割額, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日貸切指数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日貸切指数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日貸切割引Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日貸切割引, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日貸切料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日貸切料金, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日貸切時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日貸切時間, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日貸切走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日貸切走行, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日固定料金キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日固定料金キャンセル回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日迎車キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日迎車キャンセル回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日待ち回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日待ち回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日待ち加算回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日待ち加算回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日早朝キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日早朝キャンセル回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日高齢割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日高齢割回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日高齢割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日高齢割額, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日幼児割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日幼児割回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日幼児割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日幼児割額, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日リセット回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日リセット回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日待ち回数PColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日待ち回数P, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.基本料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.基本料金, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.以後料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.以後料金, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.固定料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.固定料金, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.月間営収Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.月間営収, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.営収Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.営収, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.男Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.男, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.女Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.女, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.現金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.現金, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.未収Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.未収, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.クレジットColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.クレジット, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.カードColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.カード, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.IDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.ID, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.交通系ICColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.交通系IC, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.キャブカードColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.キャブカード, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.プリペイドカードColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.プリペイドカード, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.WAONColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.WAON, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.メーター外料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.メーター外料金, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.クレジット回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.クレジット回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.貸切回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.貸切回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.空車_ETC料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車_ETC料金, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.実車_ETC料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.実車_ETC料金, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.燃料合計Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.燃料合計, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.総勤務時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.総勤務時間, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.総休憩時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.総休憩時間, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.総回送時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.総回送時間, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.総空車停止時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.総空車停止時間, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.リセット待回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.リセット待回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.出庫_親メーターColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.出庫_親メーター, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.入庫_親メーターColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.入庫_親メーター, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.空車_最高速度Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車_最高速度, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.実車_最高速度Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.実車_最高速度, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.最高速度Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.最高速度, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.データNOColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.データNO, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.乗車地Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.乗車地, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.乗車日付Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.乗車日付, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.乗車時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.乗車時刻, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.フィールド112Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.フィールド112, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.フィールド113Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.フィールド113, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.フィールド114Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.フィールド114, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.フィールド115Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.フィールド115, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.フィールド116Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.フィールド116, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.メーター外Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.メーター外, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.元料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.元料金, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.元現金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.元現金, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.降車地Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.降車地, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.降車日付Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.降車日付, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.降車時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.降車時刻, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.備考Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.備考, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.空車距離Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車距離, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.空車時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車時刻, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.空車走行時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車走行時間, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.空車最高速度Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車最高速度, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.迎車距離Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.迎車距離, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.支払区分Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.支払区分, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.タリフ区分Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.タリフ区分, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.料金, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.実車時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.実車時刻, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.実車距離Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.実車距離, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.実車最高速度Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.実車最高速度, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.空車停止時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車停止時間, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case Else
                        End Select
                    Next

                    '----------------------------------------------------------------------
                    ' ｷｰ埋め込み(ｺﾒﾝﾄ)
                    '----------------------------------------------------------------------
                    Dim strKey As String = ""
                    Dim strDelimin As String = ""
                    For Each rowレイ As NodeDatabase.DataSetFunc.F0レイアウトRow In tblレイ.Rows
                        Dim Position As Integer = rowレイ.Item(tblレイ.カラム位置Column.ColumnName)
                        If Not rowレイ.Item(tblレイ.キーフラグColumn.ColumnName) = NodeContents.Constant.CodeValue.フラグID.オフ Then
                            If rowレイ.Item(tblレイ.既定値Column.ColumnName).ToString <> "" Then
                                strKey &= strDelimin & rowレイ.Item(tblレイ.既定値Column.ColumnName).ToString : strDelimin = ":"
                            Else
                                strKey &= strDelimin & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, "").ToString.Replace("'", "") : strDelimin = ":"
                            End If
                        End If

                        '変換ﾏｽﾀ
                        If Position <> 9999 Then
                            Dim row条件() As NodeDatabase.DataSetFunc.F0レイアウト条件Row = tbl条件.Select(tbl条件.条件カラム位置Column.ColumnName & "=  " & Position _
                                                                                               & " AND " & tbl条件.条件項目値Column.ColumnName & "    = '" & BaseCore.Common.Text.Nz(_xlsSource.CellGet(i, Position).Value, "").ToString.Replace("'", "") & "'" _
                                                                                               & " AND " & tbl条件.結果キーフラグColumn.ColumnName & "= '" & NodeContents.Constant.CodeValue.フラグID.オン & "'")
                            If row条件.Length > 0 Then
                                strKey &= strDelimin & row条件(0).Item(tbl条件.結果項目値Column) : strDelimin = ":"
                            End If
                        End If
                    Next

                    '----------------------------------------------------------------------
                    ' 日付
                    '----------------------------------------------------------------------
                    Dim strValue As String = ""

                    '日付
                    strValue = ""
                    strValue = BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.取込日付).Value, "")
                    Select Case True
                        Case strValue = ""
                            _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.取込日付, Format(CDate(strNow), "yyyy/MM/dd"))

                        Case IsDate(strValue)
                            _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.取込日付, Format(CDate(strValue), "yyyy/MM/dd"))

                        Case strValue.ToString.Length = 8
                            _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.取込日付, BaseCore.Common.Text.TextFormat(strValue, tbl.取込日付Column.MaxLength, BaseContents.TextBox.Format.DateLong))

                        Case IsNumeric(strValue)
                            Dim strDate As String = Format(DateTime.FromOADate(strValue), "yyyy/MM/dd")
                            If IsDate(strDate) Then
                                _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.取込日付, strDate)
                            End If
                        Case Else
                    End Select


                    strValue = ""
                    strValue = BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.日報日付).Value, "")
                    Select Case True
                        Case strValue = ""
                            _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.日報日付, Format(CDate(strNow), "yyyy/MM/dd"))

                        Case IsDate(strValue)
                            _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.日報日付, Format(CDate(strValue), "yyyy/MM/dd"))

                        Case strValue.ToString.Length = 8
                            _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.日報日付, BaseCore.Common.Text.TextFormat(strValue, tbl.日報日付Column.MaxLength, BaseContents.TextBox.Format.DateLong))

                        Case IsNumeric(strValue)
                            Dim strDate As String = Format(DateTime.FromOADate(strValue), "yyyy/MM/dd")
                            If IsDate(strDate) Then
                                _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.日報日付, strDate)
                            End If
                        Case Else
                    End Select

                    strValue = ""
                    strValue = BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.乗車日付).Value, "")
                    Select Case True
                        Case strValue = ""
                            _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.乗車日付, Format(CDate(strNow), "yyyy/MM/dd"))

                        Case IsDate(strValue)
                            _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.乗車日付, Format(CDate(strValue), "yyyy/MM/dd"))

                        Case strValue.ToString.Length = 8
                            _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.乗車日付, BaseCore.Common.Text.TextFormat(strValue, tbl.乗車日付Column.MaxLength, BaseContents.TextBox.Format.DateLong))

                        Case IsNumeric(strValue)
                            Dim strDate As String = Format(DateTime.FromOADate(strValue), "yyyy/MM/dd")
                            If IsDate(strDate) Then
                                _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.乗車日付, strDate)
                            End If
                        Case Else
                    End Select

                    strValue = ""
                    strValue = BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.降車日付).Value, "")
                    Select Case True
                        Case strValue = ""
                            _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.降車日付, Format(CDate(strNow), "yyyy/MM/dd"))

                        Case IsDate(strValue)
                            _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.降車日付, Format(CDate(strValue), "yyyy/MM/dd"))

                        Case strValue.ToString.Length = 8
                            _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.降車日付, BaseCore.Common.Text.TextFormat(strValue, tbl.降車日付Column.MaxLength, BaseContents.TextBox.Format.DateLong))

                        Case IsNumeric(strValue)
                            Dim strDate As String = Format(DateTime.FromOADate(strValue), "yyyy/MM/dd")
                            If IsDate(strDate) Then
                                _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.降車日付, strDate)
                            End If
                        Case Else
                    End Select

                Next
            Catch ex As Exception
                Me.LastError = ex.Message
                Return False
            Finally
                _xlsSource.Close(Me.Path1)
            End Try

            '----------------------------------------------------------------------
            ' 返却
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region


#Region "CSV→Excel"
        '''========================================================================================
        ''' <SUMMARY>CSV→Excel</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Function Csv_Excel(ByVal tblレイ As NodeDatabase.DataSetFunc.F0レイアウトDataTable _
                                   , ByVal tbl条件 As NodeDatabase.DataSetFunc.F0レイアウト条件DataTable _
                                   , ByRef _xlsBase As BaseCore.Common.Excel _
                                   , ByVal strNow As String _
                                   , ByVal FileType As String
                                    ) As Boolean
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetTranTableAdapters.T乗務記録HDTableAdapter
            Dim tbl As New NodeDatabase.DataSetTran.T乗務記録HDDataTable
            Dim qry As New Collection

            Dim adaDT As New NodeDatabase.DataSetTranTableAdapters.T乗務記録DTTableAdapter
            Dim tblDT As New NodeDatabase.DataSetTran.T乗務記録DTDataTable
            Dim qryDT As New Collection

            Dim tblWork As New NodeDatabase.DataSetWork.W乗務記録DataTable

            Dim i As Long

            '----------------------------------------------------------------------
            ' Csvﾌｧｲﾙの読み込み
            '----------------------------------------------------------------------
            Dim psrCsv As New Microsoft.VisualBasic.FileIO.TextFieldParser(Me.Path1, System.Text.Encoding.GetEncoding("Shift_JIS"))
            psrCsv.TextFieldType = Microsoft.VisualBasic.FileIO.FieldType.Delimited

            If FileType = NodeContents.Constant.CodeValue.ファイル形式.Csv Then
                psrCsv.SetDelimiters(",")       '区切り文字はｶﾝﾏ
            Else
                psrCsv.SetDelimiters(vbTab)     '区切り文字はﾀﾌﾞ
            End If

            '----------------------------------------------------------------------
            ' ｲﾝﾎﾟｰﾄ
            '----------------------------------------------------------------------
            Dim strKeyNew As String = ""
            Dim strKeyOld As String = "@@@@@@" 'ｷｰ無しを考慮2014/10/08

            Dim intLine As Integer = ConstantExcel.Header.RowTop

            Try
                Do While Not psrCsv.EndOfData
                    '------------------------------------------------------------------
                    ' 1行読み込み
                    '------------------------------------------------------------------
                    Dim arrCSV As String() = psrCsv.ReadFields()

                    '------------------------------------------------------------------
                    ' ｶｳﾝﾄｱｯﾌﾟ
                    '------------------------------------------------------------------
                    i += 1

                    '------------------------------------------------------------------
                    ' ﾀｲﾄﾙ行
                    '------------------------------------------------------------------
                    If i = 1 And Not Me.Fields.Header.先頭タイトル.Value = NodeContents.Constant.CodeValue.フラグID.オフ Then
                        Continue Do
                    End If

                    intLine += 1

                    '----------------------------------------------------------------------
                    ' 値設定(ﾍｯﾀﾞ)
                    '----------------------------------------------------------------------
                    For Each rowレイ As NodeDatabase.DataSetFunc.F0レイアウトRow In tblレイ.Rows

                        '既定値入力は次へ
                        If rowレイ.Item(tblレイ.既定値Column.ColumnName).ToString <> "" Then
                            Continue For
                        End If

                        Dim FieldName As String = rowレイ.Item(tblレイ.伝票項目名Column.ColumnName)
                        Dim Position As Integer = rowレイ.Item(tblレイ.カラム位置Column.ColumnName)

                        Select Case FieldName.ToString '結合無はｸﾘｱ時、ﾃﾞｨﾌﾙﾄが設定されている項目に限る
                            Case tbl.取込日付Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.取込日付, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.取込日付).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.日報日付Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.日報日付, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.日報日付).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.日報年月Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.日報年月, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.日報年月).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.出庫時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.出庫時刻, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.出庫時刻).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.入庫時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.入庫時刻, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.入庫時刻).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.乗務員CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.乗務員CD, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.乗務員CD).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.車両番号Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.車両番号, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.車両番号).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.営業所CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.営業所CD, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.営業所CD).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.部門CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.部門CD, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.部門CD).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.勤務CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.勤務CD, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.勤務CD).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.車種CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.車種CD, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.車種CD).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.天気Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.天気, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.天気).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.累計全走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計全走行, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計全走行).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.累計実車走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計実車走行, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計実車走行).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.累計迎車走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計迎車走行, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計迎車走行).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.累計営業回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計営業回数, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計営業回数).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.累計以後回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計以後回数, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計以後回数).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.累計迎車回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計迎車回数, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計迎車回数).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.一日全走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日全走行, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日全走行).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.一日実車走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日実車走行, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日実車走行).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.一日迎車走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日迎車走行, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日迎車走行).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.一日営業回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日営業回数, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日営業回数).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.一日以後回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日以後回数, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日以後回数).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.一日迎車回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日迎車回数, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日迎車回数).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.累計運賃Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計運賃, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計運賃).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.累計料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計料金, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計料金).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.累計身割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計身割回数, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計身割回数).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.累計身割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計身割額, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計身割額).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.累計ワゴン回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計ワゴン回数, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計ワゴン回数).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.累計早朝回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計早朝回数, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計早朝回数).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.累計予約回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計予約回数, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計予約回数).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.累計遠割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計遠割回数, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計遠割回数).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.累計遠割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計遠割額, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計遠割額).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.累計貸切指数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計貸切指数, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計貸切指数).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.累計貸切割引額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計貸切割引額, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計貸切割引額).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.累計貸切料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計貸切料金, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計貸切料金).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.累計貸切時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計貸切時間, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計貸切時間).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.累計貸切走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計貸切走行, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計貸切走行).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.累計固定料金キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計固定料金キャンセル回数, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計固定料金キャンセル回数).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.累計迎車キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計迎車キャンセル回数, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計迎車キャンセル回数).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.累計待回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計待回数, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計待回数).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.累計待加算回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計待加算回数, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計待加算回数).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.累計早朝キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計早朝キャンセル回数, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計早朝キャンセル回数).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.累計高齢割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計高齢割回数, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計高齢割回数).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.累計高齢割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計高齢割額, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計高齢割額).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.累計幼児割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計幼児割回数, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計幼児割回数).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.累計幼児割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計幼児割額, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計幼児割額).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.累計リセット回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計リセット回数, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計リセット回数).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.累計待回数PColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計待回数P, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.累計待回数P).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.一日運賃Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日運賃, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日運賃).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.一日料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日料金, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日料金).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.一日身割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日身割回数, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日身割回数).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.一日身割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日身割額, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日身割額).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.一日身割現収Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日身割現収, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日身割現収).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.一日ワゴン回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日ワゴン回数, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日ワゴン回数).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.一日早朝回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日早朝回数, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日早朝回数).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.一日予約回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日予約回数, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日予約回数).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.一日遠割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日遠割回数, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日遠割回数).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.一日遠割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日遠割額, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日遠割額).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.一日貸切指数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日貸切指数, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日貸切指数).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.一日貸切割引Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日貸切割引, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日貸切割引).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.一日貸切料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日貸切料金, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日貸切料金).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.一日貸切時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日貸切時間, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日貸切時間).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.一日貸切走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日貸切走行, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日貸切走行).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.一日固定料金キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日固定料金キャンセル回数, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日固定料金キャンセル回数).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.一日迎車キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日迎車キャンセル回数, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日迎車キャンセル回数).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.一日待ち回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日待ち回数, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日待ち回数).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.一日待ち加算回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日待ち加算回数, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日待ち加算回数).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.一日早朝キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日早朝キャンセル回数, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日早朝キャンセル回数).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.一日高齢割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日高齢割回数, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日高齢割回数).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.一日高齢割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日高齢割額, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日高齢割額).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.一日幼児割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日幼児割回数, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日幼児割回数).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.一日幼児割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日幼児割額, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日幼児割額).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.一日リセット回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日リセット回数, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日リセット回数).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.一日待ち回数PColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日待ち回数P, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.一日待ち回数P).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.基本料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.基本料金, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.基本料金).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.以後料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.以後料金, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.以後料金).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.固定料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.固定料金, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.固定料金).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.月間営収Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.月間営収, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.月間営収).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.営収Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.営収, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.営収).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.男Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.男, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.男).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.女Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.女, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.女).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.現金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.現金, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.現金).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.未収Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.未収, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.未収).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.クレジットColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.クレジット, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.クレジット).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.カードColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.カード, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.カード).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.IDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.ID, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.ID).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.交通系ICColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.交通系IC, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.交通系IC).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.キャブカードColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.キャブカード, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.キャブカード).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.プリペイドカードColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.プリペイドカード, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.プリペイドカード).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.WAONColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.WAON, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.WAON).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.メーター外料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.メーター外料金, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.メーター外料金).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.クレジット回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.クレジット回数, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.クレジット回数).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.貸切回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.貸切回数, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.貸切回数).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.空車_ETC料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車_ETC料金, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.空車_ETC料金).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.実車_ETC料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.実車_ETC料金, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.実車_ETC料金).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.燃料合計Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.燃料合計, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.燃料合計).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.総勤務時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.総勤務時間, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.総勤務時間).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.総休憩時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.総休憩時間, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.総休憩時間).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.総回送時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.総回送時間, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.総回送時間).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.総空車停止時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.総空車停止時間, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.総空車停止時間).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tbl.リセット待回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.リセット待回数, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.リセット待回数).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.出庫_親メーターColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.出庫_親メーター, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.出庫_親メーター).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.入庫_親メーターColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.入庫_親メーター, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.入庫_親メーター).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.空車_最高速度Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車_最高速度, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.空車_最高速度).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.実車_最高速度Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.実車_最高速度, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.実車_最高速度).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tbl.最高速度Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.最高速度, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.最高速度).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))

                            Case tblDT.データNOColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.データNO, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.データNO).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tblDT.乗車地Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.乗車地, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.乗車地).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tblDT.乗車日付Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.乗車日付, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.乗車日付).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tblDT.乗車時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.乗車時刻, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.乗車時刻).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tblDT.フィールド112Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.フィールド112, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.フィールド112).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tblDT.フィールド113Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.フィールド113, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.フィールド113).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tblDT.フィールド114Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.フィールド114, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.フィールド114).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tblDT.フィールド115Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.フィールド115, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.フィールド115).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tblDT.フィールド116Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.フィールド116, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.フィールド116).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tblDT.メーター外Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.メーター外, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.メーター外).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tblDT.元料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.元料金, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.元料金).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tblDT.元現金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.元現金, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.元現金).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tblDT.降車地Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.降車地, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.降車地).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tblDT.降車日付Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.降車日付, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.降車日付).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tblDT.降車時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.降車時刻, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.降車時刻).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tblDT.備考Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.備考, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.備考).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tblDT.空車距離Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車距離, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.空車距離).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tblDT.空車時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車時刻, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.空車時刻).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tblDT.空車走行時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車走行時間, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.空車走行時間).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tblDT.空車最高速度Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車最高速度, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.空車最高速度).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tblDT.迎車距離Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.迎車距離, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.迎車距離).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tblDT.支払区分Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.支払区分, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.支払区分).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tblDT.タリフ区分Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.タリフ区分, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.タリフ区分).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tblDT.料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.料金, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.料金).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tblDT.実車時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.実車時刻, BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.実車時刻).Value, "") & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), ""))
                            Case tblDT.実車距離Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.実車距離, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.実車距離).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tblDT.実車最高速度Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.実車最高速度, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.実車最高速度).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))
                            Case tblDT.空車停止時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車停止時間, BaseCore.Common.Text.CVal(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.空車停止時間).Value) + BaseCore.Common.Text.CVal(arrCSV.GetValue(Position)))

                            Case Else
                        End Select
                    Next

                    '----------------------------------------------------------------------
                    ' 変換ﾏｽﾀ
                    '----------------------------------------------------------------------
                    For Each row条件 As NodeDatabase.DataSetFunc.F0レイアウト条件Row In tbl条件.Rows
                        Select Case True
                            Case Not IsNumeric(row条件.Item(tbl条件.条件カラム位置Column.ColumnName))
                                Continue For
                            Case row条件.Item(tbl条件.条件カラム位置Column.ColumnName) <= 0
                                Continue For
                            Case row条件.Item(tbl条件.条件カラム位置Column.ColumnName) > arrCSV.Length
                                Continue For
                        End Select

                        Dim FieldPositionIf As Integer = row条件.Item(tbl条件.条件カラム位置Column.ColumnName) - 1       'CsvとExcelの違い
                        Dim FieldValueIf As String = row条件.Item(tbl条件.条件項目値Column.ColumnName)

                        Dim SymbolCode As String = row条件.Item(tbl条件.符号区分Column.ColumnName)

                        Dim FieldCodeOt As String = row条件.Item(tbl条件.結果伝票項目CDColumn.ColumnName)
                        Dim FieldValueOt As String = row条件.Item(tbl条件.結果項目値Column.ColumnName)
                        Dim FieldNameOt As String = row条件.Item(tbl条件.結果伝票項目名Column.ColumnName)

                        '----------------------------------------------------------------------
                        ' 変換
                        '----------------------------------------------------------------------
                        'If JudgeValue(BaseCore.Common.Text.Nz(arrCSV.GetValue(FieldPositionIf), ""), SymbolCode, FieldValueIf) Then
                        '    Select Case FieldNameOt.ToString
                        '        Case tbl.入力日付Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.入力日付, FieldValueOt)
                        '        Case tbl.伝票日付Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.伝票日付, FieldValueOt)
                        '        Case tbl.伝票区分Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.伝票区分, FieldValueOt)
                        '        Case tbl.指示番号Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.指示番号, FieldValueOt)
                        '        Case tbl.企業コードColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.企業コード, FieldValueOt)
                        '        Case tbl.届先コードColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.届先コード, FieldValueOt)
                        '        Case tbl.届先名1Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.届先名1, FieldValueOt)
                        '        Case tbl.届先名2Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.届先名2, FieldValueOt)
                        '        Case tbl.届先郵便番号Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.届先郵便番号, FieldValueOt)
                        '        Case tbl.届先住所1Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.届先住所1, FieldValueOt)
                        '        Case tbl.届先住所2Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.届先住所2, FieldValueOt)
                        '        Case tbl.届先TELColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.届先TEL, FieldValueOt)
                        '        Case tbl.摘要コード1Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.摘要コード1, FieldValueOt)
                        '        Case tbl.摘要名1Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.摘要名1, FieldValueOt)
                        '        Case tbl.摘要コード2Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.摘要コード2, FieldValueOt)
                        '        Case tbl.摘要名2Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.摘要名2, FieldValueOt)
                        '        Case tbl.運送会社コードColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.運送会社コード, FieldValueOt)
                        '        Case tbl.配達日付Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.配達日付, FieldValueOt)
                        '        Case tbl.配達時刻区分Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.配達時刻区分, FieldValueOt)
                        '        Case tbl.代引金額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.代引金額, FieldValueOt)
                        '        Case tbl.送り状NOColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.送り状No, FieldValueOt)
                        '        Case tbl.注文NOColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文NO, FieldValueOt)
                        '        Case tbl.注文日付Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文日付, FieldValueOt)
                        '        Case tbl.注文名1Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文名1, FieldValueOt)
                        '        Case tbl.注文名2Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文名2, FieldValueOt)
                        '        Case tbl.注文会社名Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文会社名, FieldValueOt)
                        '        Case tbl.注文部署名Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文部署名, FieldValueOt)
                        '        Case tbl.注文郵便番号Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文郵便番号, FieldValueOt)
                        '        Case tbl.注文住所1Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文住所1, FieldValueOt)
                        '        Case tbl.注文住所2Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文住所2, FieldValueOt)
                        '        Case tbl.注文TELColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文TEL, FieldValueOt)
                        '        Case tbl.注文メールアドレスColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文メールアドレス, FieldValueOt)
                        '        Case tbl.注文支払区分Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文支払区分, FieldValueOt)
                        '        Case tbl.注文入金状況Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文入金状況, FieldValueOt)
                        '        Case tbl.注文ギフト包装区分Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文ギフト包装区分, FieldValueOt)
                        '        Case tbl.注文ギフトメッセージColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文ギフトメッセージ, FieldValueOt)
                        '        Case tbl.注文のし表書き区分Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文のし表書き区分, FieldValueOt)
                        '        Case tbl.注文のし名入れColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文のし名入れ, FieldValueOt)
                        '        Case tbl.注文品代Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文品代, FieldValueOt)
                        '        Case tbl.注文消費税Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文消費税, FieldValueOt)
                        '        Case tbl.注文送料Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文送料, FieldValueOt)
                        '        Case tbl.注文手数料Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文手数料, FieldValueOt)
                        '        Case tbl.注文ギフト包装代Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文ギフト包装代, FieldValueOt)
                        '        Case tbl.注文値引額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文値引額, FieldValueOt)
                        '        Case tbl.注文利用ポイントColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文利用ポイント, FieldValueOt)
                        '        Case tbl.注文請求金額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文請求金額, FieldValueOt)
                        '        Case tbl.注文ショップCDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文ショップCD, FieldValueOt)
                        '        Case tbl.注文軽減税Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.注文軽減税, FieldValueOt)

                        '        Case tblDT.商品コードColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.商品コード, FieldValueOt)
                        '        Case tblDT.規格コード1Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.規格コード1, FieldValueOt)
                        '        Case tblDT.規格コード2Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.規格コード2, FieldValueOt)
                        '        Case tblDT.先方商品NOColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.先方商品NO, FieldValueOt)
                        '        Case tblDT.JANコードColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.JANコード, FieldValueOt)
                        '        Case tblDT.ロットNOColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.ロットNO, FieldValueOt)
                        '        Case tblDT.ロット日付Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.ロット日付, FieldValueOt)
                        '        Case tblDT.商品名Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.商品名, FieldValueOt)
                        '        Case tblDT.先方商品名Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.先方商品名, FieldValueOt)
                        '        Case tblDT.棚番コードColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.棚番コード, FieldValueOt)
                        '        Case tblDT.荷姿区分Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.荷姿区分, FieldValueOt)
                        '        Case tblDT.数量Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.数量, FieldValueOt)
                        '        Case tblDT.単価Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.単価, FieldValueOt)
                        '        Case tblDT.金額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.金額, FieldValueOt)
                        '        Case Else
                        '    End Select
                        'End If
                    Next

                    '----------------------------------------------------------------------
                    ' 既定値
                    '----------------------------------------------------------------------
                    For Each rowレイ As NodeDatabase.DataSetFunc.F0レイアウトRow In tblレイ.Rows
                        '未入力は次へ
                        If rowレイ.Item(tblレイ.既定値Column.ColumnName).ToString = "" Then
                            Continue For
                        End If

                        Dim FieldName As String = rowレイ.Item(tblレイ.伝票項目名Column.ColumnName)
                        Select Case FieldName.ToString
                            Case tbl.取込日付Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.取込日付, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.日報日付Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.日報日付, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.日報年月Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.日報年月, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.出庫時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.出庫時刻, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.入庫時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.入庫時刻, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.乗務員CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.乗務員CD, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.車両番号Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.車両番号, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.営業所CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.営業所CD, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.部門CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.部門CD, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.勤務CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.勤務CD, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.車種CDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.車種CD, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.天気Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.天気, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計全走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計全走行, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計実車走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計実車走行, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計迎車走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計迎車走行, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計営業回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計営業回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計以後回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計以後回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計迎車回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計迎車回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日全走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日全走行, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日実車走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日実車走行, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日迎車走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日迎車走行, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日営業回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日営業回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日以後回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日以後回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日迎車回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日迎車回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計運賃Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計運賃, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計料金, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計身割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計身割回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計身割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計身割額, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計ワゴン回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計ワゴン回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計早朝回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計早朝回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計予約回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計予約回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計遠割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計遠割回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計遠割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計遠割額, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計貸切指数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計貸切指数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計貸切割引額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計貸切割引額, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計貸切料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計貸切料金, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計貸切時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計貸切時間, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計貸切走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計貸切走行, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計固定料金キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計固定料金キャンセル回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計迎車キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計迎車キャンセル回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計待回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計待回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計待加算回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計待加算回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計早朝キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計早朝キャンセル回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計高齢割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計高齢割回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計高齢割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計高齢割額, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計幼児割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計幼児割回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計幼児割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計幼児割額, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計リセット回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計リセット回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.累計待回数PColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.累計待回数P, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日運賃Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日運賃, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日料金, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日身割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日身割回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日身割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日身割額, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日身割現収Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日身割現収, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日ワゴン回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日ワゴン回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日早朝回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日早朝回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日予約回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日予約回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日遠割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日遠割回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日遠割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日遠割額, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日貸切指数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日貸切指数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日貸切割引Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日貸切割引, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日貸切料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日貸切料金, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日貸切時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日貸切時間, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日貸切走行Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日貸切走行, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日固定料金キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日固定料金キャンセル回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日迎車キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日迎車キャンセル回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日待ち回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日待ち回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日待ち加算回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日待ち加算回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日早朝キャンセル回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日早朝キャンセル回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日高齢割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日高齢割回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日高齢割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日高齢割額, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日幼児割回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日幼児割回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日幼児割額Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日幼児割額, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日リセット回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日リセット回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.一日待ち回数PColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.一日待ち回数P, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.基本料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.基本料金, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.以後料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.以後料金, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.固定料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.固定料金, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.月間営収Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.月間営収, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.営収Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.営収, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.男Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.男, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.女Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.女, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.現金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.現金, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.未収Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.未収, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.クレジットColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.クレジット, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.カードColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.カード, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.IDColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.ID, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.交通系ICColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.交通系IC, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.キャブカードColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.キャブカード, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.プリペイドカードColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.プリペイドカード, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.WAONColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.WAON, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.メーター外料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.メーター外料金, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.クレジット回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.クレジット回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.貸切回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.貸切回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.空車_ETC料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車_ETC料金, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.実車_ETC料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.実車_ETC料金, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.燃料合計Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.燃料合計, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.総勤務時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.総勤務時間, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.総休憩時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.総休憩時間, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.総回送時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.総回送時間, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.総空車停止時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.総空車停止時間, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.リセット待回数Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.リセット待回数, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.出庫_親メーターColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.出庫_親メーター, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.入庫_親メーターColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.入庫_親メーター, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.空車_最高速度Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車_最高速度, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.実車_最高速度Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.実車_最高速度, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tbl.最高速度Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.最高速度, rowレイ.Item(tblレイ.既定値Column.ColumnName))

                            Case tblDT.データNOColumn.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.データNO, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tblDT.乗車地Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.乗車地, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tblDT.乗車日付Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.乗車日付, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tblDT.乗車時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.乗車時刻, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tblDT.フィールド112Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.フィールド112, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tblDT.フィールド113Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.フィールド113, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tblDT.フィールド114Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.フィールド114, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tblDT.フィールド115Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.フィールド115, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tblDT.フィールド116Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.フィールド116, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tblDT.メーター外Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.メーター外, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tblDT.元料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.元料金, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tblDT.元現金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.元現金, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tblDT.降車地Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.降車地, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tblDT.降車日付Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.降車日付, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tblDT.降車時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.降車時刻, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tblDT.備考Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.備考, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tblDT.空車距離Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車距離, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tblDT.空車時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車時刻, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tblDT.空車走行時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車走行時間, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tblDT.空車最高速度Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車最高速度, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tblDT.迎車距離Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.迎車距離, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tblDT.支払区分Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.支払区分, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tblDT.タリフ区分Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.タリフ区分, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tblDT.料金Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.料金, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tblDT.実車時刻Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.実車時刻, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tblDT.実車距離Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.実車距離, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tblDT.実車最高速度Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.実車最高速度, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case tblDT.空車停止時間Column.ColumnName : _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.空車停止時間, rowレイ.Item(tblレイ.既定値Column.ColumnName))
                            Case Else

                        End Select
                    Next

                    '----------------------------------------------------------------------
                    ' ｷｰ埋め込み(ｺﾒﾝﾄ)
                    '----------------------------------------------------------------------
                    Dim strKey As String = ""
                    Dim strDelimin As String = ""
                    For Each rowレイ As NodeDatabase.DataSetFunc.F0レイアウトRow In tblレイ.Rows
                        Dim Position As Integer = rowレイ.Item(tblレイ.カラム位置Column.ColumnName)
                        If Not rowレイ.Item(tblレイ.キーフラグColumn.ColumnName) = NodeContents.Constant.CodeValue.フラグID.オフ Then
                            If rowレイ.Item(tblレイ.既定値Column.ColumnName).ToString <> "" Then
                                strKey &= strDelimin & rowレイ.Item(tblレイ.既定値Column.ColumnName).ToString : strDelimin = ":"
                            Else
                                strKey &= strDelimin & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), "").ToString.Replace("'", "") : strDelimin = ":"
                            End If
                        End If

                        '変換ﾏｽﾀ
                        If Position <> 9999 Then
                            Dim row条件() As NodeDatabase.DataSetFunc.F0レイアウト条件Row = tbl条件.Select(tbl条件.条件カラム位置Column.ColumnName & "=  " & Position + 1 _
                                                                                               & " AND " & tbl条件.条件項目値Column.ColumnName & "    = '" & BaseCore.Common.Text.Nz(arrCSV.GetValue(Position), "").ToString.Replace("'", "") & "'" _
                                                                                               & " AND " & tbl条件.結果キーフラグColumn.ColumnName & "= '" & NodeContents.Constant.CodeValue.フラグID.オン & "'")
                            If row条件.Length > 0 Then
                                strKeyNew &= strDelimin & row条件(0).Item(tbl条件.結果項目値Column) : strDelimin = ":"
                            End If
                        End If
                    Next

                    '_xlsBase.CellClearComment(intLine, ConstantExcel.Header.列数.改伝キー)
                    '_xlsBase.CellSetComment(intLine, ConstantExcel.Header.列数.改伝キー, strKey)

                    '----------------------------------------------------------------------
                    ' 個別対応
                    '----------------------------------------------------------------------


                    '----------------------------------------------------------------------
                    ' 日付
                    '----------------------------------------------------------------------
                    Dim strValue As String = ""

                    '日付
                    strValue = ""
                    strValue = BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.取込日付).Value, "")
                    Select Case True
                        Case strValue = ""
                            _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.取込日付, Format(CDate(strNow), "yyyy/MM/dd"))

                        Case IsDate(strValue)
                            _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.取込日付, Format(CDate(strValue), "yyyy/MM/dd"))

                        Case strValue.ToString.Length = 8
                            _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.取込日付, BaseCore.Common.Text.TextFormat(strValue, tbl.取込日付Column.MaxLength, BaseContents.TextBox.Format.DateLong))

                        Case IsNumeric(strValue)
                            Dim strDate As String = Format(DateTime.FromOADate(strValue), "yyyy/MM/dd")
                            If IsDate(strDate) Then
                                _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.取込日付, strDate)
                            End If
                        Case Else
                    End Select


                    strValue = ""
                    strValue = BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.日報日付).Value, "")
                    Select Case True
                        Case strValue = ""
                            _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.日報日付, Format(CDate(strNow), "yyyy/MM/dd"))

                        Case IsDate(strValue)
                            _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.日報日付, Format(CDate(strValue), "yyyy/MM/dd"))

                        Case strValue.ToString.Length = 8
                            _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.日報日付, BaseCore.Common.Text.TextFormat(strValue, tbl.日報日付Column.MaxLength, BaseContents.TextBox.Format.DateLong))

                        Case IsNumeric(strValue)
                            Dim strDate As String = Format(DateTime.FromOADate(strValue), "yyyy/MM/dd")
                            If IsDate(strDate) Then
                                _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.日報日付, strDate)
                            End If
                        Case Else
                    End Select

                    strValue = ""
                    strValue = BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.乗車日付).Value, "")
                    Select Case True
                        Case strValue = ""
                            _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.乗車日付, Format(CDate(strNow), "yyyy/MM/dd"))

                        Case IsDate(strValue)
                            _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.乗車日付, Format(CDate(strValue), "yyyy/MM/dd"))

                        Case strValue.ToString.Length = 8
                            _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.乗車日付, BaseCore.Common.Text.TextFormat(strValue, tblDT.乗車日付Column.MaxLength, BaseContents.TextBox.Format.DateLong))

                        Case IsNumeric(strValue)
                            Dim strDate As String = Format(DateTime.FromOADate(strValue), "yyyy/MM/dd")
                            If IsDate(strDate) Then
                                _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.乗車日付, strDate)
                            End If
                        Case Else
                    End Select

                    strValue = ""
                    strValue = BaseCore.Common.Text.Nz(_xlsBase.CellGet(intLine, ConstantExcel.Header.列数.降車日付).Value, "")
                    Select Case True
                        Case strValue = ""
                            _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.降車日付, Format(CDate(strNow), "yyyy/MM/dd"))

                        Case IsDate(strValue)
                            _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.降車日付, Format(CDate(strValue), "yyyy/MM/dd"))

                        Case strValue.ToString.Length = 8
                            _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.降車日付, BaseCore.Common.Text.TextFormat(strValue, tblDT.降車日付Column.MaxLength, BaseContents.TextBox.Format.DateLong))

                        Case IsNumeric(strValue)
                            Dim strDate As String = Format(DateTime.FromOADate(strValue), "yyyy/MM/dd")
                            If IsDate(strDate) Then
                                _xlsBase.CellSetValue(intLine, ConstantExcel.Header.列数.降車日付, strDate)
                            End If
                        Case Else
                    End Select

                Loop
            Catch ex As Exception
                Me.LastError = ex.Message
                Return False
            Finally
                psrCsv.Close()
            End Try

            '----------------------------------------------------------------------
            ' 返却
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#End Region

#Region "ﾌﾚｰﾑﾜｰｸ"
#Region "ﾍｯﾀﾞ_ﾌﾚｰﾑﾜｰｸｾｯﾄ"
        '''========================================================================================
        ''' <SUMMARY>Excelﾒｲﾝ処理</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Function FrameSet_Header(ByVal Excel As BaseCore.Common.Excel _
                                         , ByVal i As Long _
                                         , ByVal strNow As String
                                          ) As Boolean
            Try
                '----------------------------------------------------------------------
                ' 値設定(ﾍｯﾀﾞ)
                '----------------------------------------------------------------------
                Me.Fields.Header.取込日付.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.取込日付).Value, "")
                Me.Fields.Header.日報日付.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.日報日付).Value, "")
                Me.Fields.Header.日報年月.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.日報年月).Value, "")
                Me.Fields.Header.出庫時刻.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.出庫時刻).Value, "")
                Me.Fields.Header.入庫時刻.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.入庫時刻).Value, "")
                Me.Fields.Header.乗務員CD.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.乗務員CD).Value, "")
                Me.Fields.Header.車両番号.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.車両番号).Value, "")
                Me.Fields.Header.営業所CD.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.営業所CD).Value)
                Me.Fields.Header.部門CD.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.部門CD).Value)
                Me.Fields.Header.勤務CD.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.勤務CD).Value)
                Me.Fields.Header.車種CD.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.車種CD).Value)
                Me.Fields.Header.天気.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.天気).Value, "")
                Me.Fields.Header.累計全走行.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.累計全走行).Value)
                Me.Fields.Header.累計実車走行.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.累計実車走行).Value)
                Me.Fields.Header.累計迎車走行.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.累計迎車走行).Value)
                Me.Fields.Header.累計営業回数.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.累計営業回数).Value)
                Me.Fields.Header.累計以後回数.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.累計以後回数).Value)
                Me.Fields.Header.累計迎車回数.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.累計迎車回数).Value)
                Me.Fields.Header.一日全走行.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.一日全走行).Value)
                Me.Fields.Header.一日実車走行.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.一日実車走行).Value)
                Me.Fields.Header.一日迎車走行.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.一日迎車走行).Value)
                Me.Fields.Header.一日営業回数.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.一日営業回数).Value)
                Me.Fields.Header.一日以後回数.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.一日以後回数).Value)
                Me.Fields.Header.一日迎車回数.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.一日迎車回数).Value)
                Me.Fields.Header.累計運賃.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.累計運賃).Value)
                Me.Fields.Header.累計料金.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.累計料金).Value)
                Me.Fields.Header.累計身割回数.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.累計身割回数).Value)
                Me.Fields.Header.累計身割額.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.累計身割額).Value)
                Me.Fields.Header.累計ワゴン回数.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.累計ワゴン回数).Value)
                Me.Fields.Header.累計早朝回数.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.累計早朝回数).Value)
                Me.Fields.Header.累計予約回数.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.累計予約回数).Value)
                Me.Fields.Header.累計遠割回数.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.累計遠割回数).Value)
                Me.Fields.Header.累計遠割額.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.累計遠割額).Value)
                Me.Fields.Header.累計貸切指数.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.累計貸切指数).Value)
                Me.Fields.Header.累計貸切割引額.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.累計貸切割引額).Value)
                Me.Fields.Header.累計貸切料金.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.累計貸切料金).Value)
                Me.Fields.Header.累計貸切時間.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.累計貸切時間).Value)
                Me.Fields.Header.累計貸切走行.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.累計貸切走行).Value)
                Me.Fields.Header.累計固定料金キャンセル回数.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.累計固定料金キャンセル回数).Value)
                Me.Fields.Header.累計迎車キャンセル回数.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.累計迎車キャンセル回数).Value)
                Me.Fields.Header.累計待回数.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.累計待回数).Value)
                Me.Fields.Header.累計待加算回数.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.累計待加算回数).Value)
                Me.Fields.Header.累計早朝キャンセル回数.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.累計早朝キャンセル回数).Value)
                Me.Fields.Header.累計高齢割回数.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.累計高齢割回数).Value)
                Me.Fields.Header.累計高齢割額.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.累計高齢割額).Value)
                Me.Fields.Header.累計幼児割回数.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.累計幼児割回数).Value)
                Me.Fields.Header.累計幼児割額.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.累計幼児割額).Value)
                Me.Fields.Header.累計リセット回数.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.累計リセット回数).Value)
                Me.Fields.Header.累計待回数P.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.累計待回数P).Value)
                Me.Fields.Header.一日運賃.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.一日運賃).Value)
                Me.Fields.Header.一日料金.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.一日料金).Value)
                Me.Fields.Header.一日身割回数.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.一日身割回数).Value)
                Me.Fields.Header.一日身割額.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.一日身割額).Value)
                Me.Fields.Header.一日身割現収.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.一日身割現収).Value)
                Me.Fields.Header.一日ワゴン回数.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.一日ワゴン回数).Value)
                Me.Fields.Header.一日早朝回数.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.一日早朝回数).Value)
                Me.Fields.Header.一日予約回数.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.一日予約回数).Value)
                Me.Fields.Header.一日遠割回数.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.一日遠割回数).Value)
                Me.Fields.Header.一日遠割額.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.一日遠割額).Value)
                Me.Fields.Header.一日貸切指数.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.一日貸切指数).Value)
                Me.Fields.Header.一日貸切割引.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.一日貸切割引).Value)
                Me.Fields.Header.一日貸切料金.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.一日貸切料金).Value)
                Me.Fields.Header.一日貸切時間.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.一日貸切時間).Value)
                Me.Fields.Header.一日貸切走行.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.一日貸切走行).Value)
                Me.Fields.Header.一日固定料金キャンセル回数.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.一日固定料金キャンセル回数).Value)
                Me.Fields.Header.一日迎車キャンセル回数.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.一日迎車キャンセル回数).Value)
                Me.Fields.Header.一日待ち回数.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.一日待ち回数).Value)
                Me.Fields.Header.一日待ち加算回数.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.一日待ち加算回数).Value)
                Me.Fields.Header.一日早朝キャンセル回数.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.一日早朝キャンセル回数).Value)
                Me.Fields.Header.一日高齢割回数.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.一日高齢割回数).Value)
                Me.Fields.Header.一日高齢割額.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.一日高齢割額).Value)
                Me.Fields.Header.一日幼児割回数.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.一日幼児割回数).Value)
                Me.Fields.Header.一日幼児割額.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.一日幼児割額).Value)
                Me.Fields.Header.一日リセット回数.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.一日リセット回数).Value)
                Me.Fields.Header.一日待ち回数P.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.一日待ち回数P).Value)
                Me.Fields.Header.基本料金.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.基本料金).Value)
                Me.Fields.Header.以後料金.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.以後料金).Value)
                Me.Fields.Header.固定料金.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.固定料金).Value)
                Me.Fields.Header.月間営収.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.月間営収).Value)
                Me.Fields.Header.営収.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.営収).Value)
                Me.Fields.Header.男.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.男).Value)
                Me.Fields.Header.女.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.女).Value)
                Me.Fields.Header.現金.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.現金).Value)
                Me.Fields.Header.未収.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.未収).Value)
                Me.Fields.Header.クレジット.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.クレジット).Value)
                Me.Fields.Header.カード.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.カード).Value)
                Me.Fields.Header.ID.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.ID).Value)
                Me.Fields.Header.交通系IC.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.交通系IC).Value)
                Me.Fields.Header.キャブカード.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.キャブカード).Value)
                Me.Fields.Header.プリペイドカード.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.プリペイドカード).Value)
                Me.Fields.Header.WAON.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.WAON).Value)
                Me.Fields.Header.メーター外料金.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.メーター外料金).Value)
                Me.Fields.Header.クレジット回数.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.クレジット回数).Value)
                Me.Fields.Header.貸切回数.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.貸切回数).Value)
                Me.Fields.Header.空車_ETC料金.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.空車_ETC料金).Value)
                Me.Fields.Header.実車_ETC料金.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.実車_ETC料金).Value)
                Me.Fields.Header.燃料合計.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.燃料合計).Value)
                Me.Fields.Header.総勤務時間.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.総勤務時間).Text, "")
                Me.Fields.Header.総休憩時間.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.総休憩時間).Text, "")
                Me.Fields.Header.総回送時間.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.総回送時間).Text, "")
                Me.Fields.Header.総空車停止時間.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.総空車停止時間).Text, "")
                Me.Fields.Header.リセット待回数.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.リセット待回数).Value)
                Me.Fields.Header.出庫_親メーター.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.出庫_親メーター).Value)
                Me.Fields.Header.入庫_親メーター.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.入庫_親メーター).Value)
                Me.Fields.Header.空車_最高速度.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.空車_最高速度).Value)
                Me.Fields.Header.実車_最高速度.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.実車_最高速度).Value)
                Me.Fields.Header.最高速度.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.最高速度).Value)
                Me.Fields.Header.データNO.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.データNO).Value)
                Me.Fields.Header.乗車地.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.乗車地).Value, "")
                Me.Fields.Header.乗車日付.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.乗車日付).Text, "")
                Me.Fields.Header.乗車時刻.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.乗車時刻).Text, "")
                Me.Fields.Header.フィールド112.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.フィールド112).Value)
                Me.Fields.Header.フィールド113.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.フィールド113).Value)
                Me.Fields.Header.フィールド114.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.フィールド114).Value)
                Me.Fields.Header.フィールド115.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.フィールド115).Value)
                Me.Fields.Header.フィールド116.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.フィールド116).Value)
                Me.Fields.Header.メーター外.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.メーター外).Value)
                Me.Fields.Header.元料金.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.元料金).Value)
                Me.Fields.Header.元現金.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.元現金).Value)
                Me.Fields.Header.降車地.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.降車地).Text, "")
                Me.Fields.Header.降車日付.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.降車日付).Text, "")
                Me.Fields.Header.降車時刻.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.降車時刻).Text, "")
                Me.Fields.Header.備考.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.備考).Text, "")
                Me.Fields.Header.空車距離.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.空車距離).Value)
                Me.Fields.Header.空車時刻.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.空車時刻).Text, "")
                Me.Fields.Header.空車走行時間.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.空車走行時間).Value)
                Me.Fields.Header.空車最高速度.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.空車最高速度).Value)
                Me.Fields.Header.迎車距離.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.迎車距離).Value)
                Me.Fields.Header.支払区分.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.支払区分).Value)
                Me.Fields.Header.タリフ区分.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.タリフ区分).Value)
                Me.Fields.Header.料金.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.料金).Value)
                Me.Fields.Header.実車時刻.Value = BaseCore.Common.Text.Nz(Excel.CellGet(i, ConstantExcel.Header.列数.実車時刻).Text, "")
                Me.Fields.Header.実車距離.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.実車距離).Value)
                Me.Fields.Header.実車最高速度.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.実車最高速度).Value)
                Me.Fields.Header.空車停止時間.Value = BaseCore.Common.Text.CVal(Excel.CellGet(i, ConstantExcel.Header.列数.空車停止時間).Value)

            Catch ex As Exception
                Me.LastError = ex.Message
                Return False
            End Try

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "ﾌﾚｰﾑﾜｰｸ→ﾜｰｸﾃｰﾌﾞﾙ"
        '''========================================================================================
        ''' <SUMMARY>ﾜｰｸ書込</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Function Write_Work(ByRef intVirtualNo As Integer, ByVal strNow As String) As Boolean
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim adaHD As New NodeDatabase.DataSetWorkTableAdapters.W乗務記録TableAdapter

            '仮番ｶｳﾝﾄｱｯﾌﾟ
            intVirtualNo += 1

            '----------------------------------------------------------------------
            ' ﾜｰｸ書込(ﾍｯﾀﾞ)
            '----------------------------------------------------------------------
            Dim t乗務記録NO = Format(CDate(Me.Fields.Header.日報日付.Value), "yyyyMMdd") & Replace(Me.Fields.Header.出庫時刻.Value, ":", "") & Me.Fields.Header.車両番号.Value
            Try
                adaHD.Insert(Me.Security.セッションID _
                            , Me.Security.グループCD _
                            , Me.Security.個社CD _
                            , Me.Security.子会社CD _
                            , t乗務記録NO _
                            , strNow _ ', Me.Fields.Header.取込日付.Value _
                            , Me.Fields.Header.日報日付.Value _
                            , Me.Fields.Header.日報年月.Value _
                            , Me.Fields.Header.出庫時刻.Value _
                            , Me.Fields.Header.入庫時刻.Value _
                            , Me.Fields.Header.乗務員CD.Value _
                            , Me.Fields.Header.車両番号.Value _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.営業所CD.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.部門CD.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.勤務CD.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.車種CD.Value) _
                            , Me.Fields.Header.天気.Value _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計全走行.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計実車走行.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計迎車走行.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計営業回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計以後回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計迎車回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日全走行.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日実車走行.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日迎車走行.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日営業回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日以後回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日迎車回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計運賃.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計料金.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計身割回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計身割額.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計ワゴン回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計早朝回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計予約回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計遠割回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計遠割額.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計貸切指数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計貸切割引額.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計貸切料金.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計貸切時間.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計貸切走行.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計固定料金キャンセル回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計迎車キャンセル回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計待回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計待加算回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計早朝キャンセル回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計高齢割回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計高齢割額.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計幼児割回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計幼児割額.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計リセット回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計待回数P.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日運賃.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日料金.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日身割回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日身割額.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日身割現収.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日ワゴン回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日早朝回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日予約回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日遠割回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日遠割額.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日貸切指数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日貸切割引.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日貸切料金.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日貸切時間.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日貸切走行.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日固定料金キャンセル回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日迎車キャンセル回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日待ち回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日待ち加算回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日早朝キャンセル回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日高齢割回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日高齢割額.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日幼児割回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日幼児割額.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日リセット回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日待ち回数P.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.基本料金.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.以後料金.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.固定料金.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.月間営収.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.営収.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.男.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.女.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.現金.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.未収.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.クレジット.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.カード.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.ID.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.交通系IC.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.キャブカード.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.プリペイドカード.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.WAON.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.メーター外料金.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.クレジット回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.貸切回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.空車_ETC料金.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.実車_ETC料金.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.燃料合計.Value) _
                            , Me.Fields.Header.総勤務時間.Value _
                            , Me.Fields.Header.総休憩時間.Value _
                            , Me.Fields.Header.総回送時間.Value _
                            , Me.Fields.Header.総空車停止時間.Value _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.リセット待回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.出庫_親メーター.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.入庫_親メーター.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.空車_最高速度.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.実車_最高速度.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.最高速度.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.データNO.Value) _
                            , Me.Fields.Header.乗車地.Value _
                            , Me.Fields.Header.乗車日付.Value _
                            , Me.Fields.Header.乗車時刻.Value _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.フィールド112.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.フィールド113.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.フィールド114.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.フィールド115.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.フィールド116.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.メーター外.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.元料金.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.元現金.Value) _
                            , Me.Fields.Header.降車地.Value _
                            , Me.Fields.Header.降車日付.Value _
                            , Me.Fields.Header.降車時刻.Value _
                            , Me.Fields.Header.備考.Value _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.空車距離.Value) _
                            , Me.Fields.Header.空車時刻.Value _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.空車走行時間.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.空車最高速度.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.迎車距離.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.支払区分.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.タリフ区分.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.料金.Value) _
                            , Me.Fields.Header.実車時刻.Value _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.実車距離.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.実車最高速度.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.空車停止時間.Value) _
                             , "", "", "", "", "", "", "", "", "", "" _
                             , "", "", "", "", "", "", "", "", "", "" _
                             , "", "", "", "", "", "", "", "", "", "" _
                             , "", "", "", "", "", "", "", "", "", "" _
                             , "", "", "", "", "", "", "", "", "", "" _
                             , "", "", "", "", "", "", "", "", "", "" _
                             , "", "", "", "", "", "", "", "", "", "" _
                             , "", "", "", "", "", "", "", "", "", "" _
                             , "", "", "", "", "", "", "", "", "", "" _
                             , "", "", "", "", "", "", "", "", "", "" _
                             , "", "", "", "", "", "", "", "", "", "" _
                             , "", "", "", "", "", "", "", "", "", "" _
                             , "", "", "", "", "", "", "", "", "", "" _
                             , "", "", "", "", "", ""
                          )
            Catch ex As Exception
                Me.LastError = "以下のエラーのため、処理を継続できません。" & vbCrLf & ex.Message
                Return False
            End Try

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "ﾌﾚｰﾑﾜｰｸ→RAWデータ"
        '''========================================================================================
        ''' <SUMMARY>ﾜｰｸ書込</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Function Write_RawData(ByRef intVirtualNo As Integer, ByVal strNow As String) As Boolean
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim adaHD As New NodeDatabase.DataSetTranTableAdapters.T乗務記録TableAdapter
            Dim strNowTime As String = Format(Now, "yyyy/MM/dd HH:mm:ss")

            '仮番ｶｳﾝﾄｱｯﾌﾟ
            intVirtualNo += 1

            '----------------------------------------------------------------------
            ' ﾜｰｸ書込(ﾍｯﾀﾞ)
            '----------------------------------------------------------------------
            Dim t乗務記録NO = Format(CDate(Me.Fields.Header.日報日付.Value), "yyyyMMdd") & Replace(Me.Fields.Header.出庫時刻.Value, ":", "") & Me.Fields.Header.車両番号.Value
            Try
                adaHD.Insert(Me.Security.グループCD _
                            , Me.Security.個社CD _
                            , Me.Security.子会社CD _
                            , Me.Fields.Header.日報日付.Value _
                            , Me.Fields.Header.乗務員CD.Value _
                            , Me.Fields.Header.車両番号.Value _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.データNO.Value) _
                            , strNow _
                            , Me.Fields.Header.日報年月.Value _
                            , Me.Fields.Header.出庫時刻.Value _
                            , Me.Fields.Header.入庫時刻.Value _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.営業所CD.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.部門CD.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.勤務CD.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.車種CD.Value) _
                            , Me.Fields.Header.天気.Value _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計全走行.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計実車走行.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計迎車走行.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計営業回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計以後回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計迎車回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日全走行.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日実車走行.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日迎車走行.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日営業回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日以後回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日迎車回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計運賃.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計料金.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計身割回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計身割額.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計ワゴン回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計早朝回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計予約回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計遠割回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計遠割額.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計貸切指数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計貸切割引額.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計貸切料金.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計貸切時間.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計貸切走行.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計固定料金キャンセル回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計迎車キャンセル回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計待回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計待加算回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計早朝キャンセル回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計高齢割回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計高齢割額.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計幼児割回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計幼児割額.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計リセット回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計待回数P.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日運賃.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日料金.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日身割回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日身割額.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日身割現収.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日ワゴン回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日早朝回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日予約回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日遠割回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日遠割額.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日貸切指数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日貸切割引.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日貸切料金.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日貸切時間.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日貸切走行.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日固定料金キャンセル回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日迎車キャンセル回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日待ち回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日待ち加算回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日早朝キャンセル回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日高齢割回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日高齢割額.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日幼児割回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日幼児割額.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日リセット回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日待ち回数P.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.基本料金.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.以後料金.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.固定料金.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.月間営収.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.営収.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.男.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.女.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.現金.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.未収.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.クレジット.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.カード.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.ID.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.交通系IC.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.キャブカード.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.プリペイドカード.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.WAON.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.メーター外料金.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.クレジット回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.貸切回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.空車_ETC料金.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.実車_ETC料金.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.燃料合計.Value) _
                            , Me.Fields.Header.総勤務時間.Value _
                            , Me.Fields.Header.総休憩時間.Value _
                            , Me.Fields.Header.総回送時間.Value _
                            , Me.Fields.Header.総空車停止時間.Value _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.リセット待回数.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.出庫_親メーター.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.入庫_親メーター.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.空車_最高速度.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.実車_最高速度.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.最高速度.Value) _
                            , Me.Fields.Header.乗車地.Value _
                            , Me.Fields.Header.乗車日付.Value _
                            , Me.Fields.Header.乗車時刻.Value _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.フィールド112.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.フィールド113.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.フィールド114.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.フィールド115.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.フィールド116.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.メーター外.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.元料金.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.元現金.Value) _
                            , Me.Fields.Header.降車地.Value _
                            , Me.Fields.Header.降車日付.Value _
                            , Me.Fields.Header.降車時刻.Value _
                            , Me.Fields.Header.備考.Value _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.空車距離.Value) _
                            , Me.Fields.Header.空車時刻.Value _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.空車走行時間.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.空車最高速度.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.迎車距離.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.支払区分.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.タリフ区分.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.料金.Value) _
                            , Me.Fields.Header.実車時刻.Value _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.実車距離.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.実車最高速度.Value) _
                            , BaseCore.Common.Text.CVal(Me.Fields.Header.空車停止時間.Value) _
                            , Me.Security.ユーザーCD _
                            , Me.Security.ユーザー名 _
                            , strNowTime _
                            , Me.Security.ユーザーCD _
                            , Me.Security.ユーザー名 _
                            , strNowTime
                            )

                'adaHD.Insert(Me.Security.セッションID _
                '            , Me.Security.グループCD _
                '            , Me.Security.個社CD _
                '            , Me.Security.子会社CD _
                '            , t乗務記録NO _
                '            , strNow _ ', Me.Fields.Header.取込日付.Value _
                '            , Me.Fields.Header.日報日付.Value _
                '            , Me.Fields.Header.日報年月.Value _
                '            , Me.Fields.Header.出庫時刻.Value _
                '            , Me.Fields.Header.入庫時刻.Value _
                '            , Me.Fields.Header.乗務員CD.Value _
                '            , Me.Fields.Header.車両番号.Value _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.営業所CD.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.部門CD.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.勤務CD.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.車種CD.Value) _
                '            , Me.Fields.Header.天気.Value _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計全走行.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計実車走行.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計迎車走行.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計営業回数.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計以後回数.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計迎車回数.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日全走行.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日実車走行.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日迎車走行.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日営業回数.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日以後回数.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日迎車回数.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計運賃.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計料金.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計身割回数.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計身割額.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計ワゴン回数.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計早朝回数.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計予約回数.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計遠割回数.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計遠割額.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計貸切指数.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計貸切割引額.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計貸切料金.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計貸切時間.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計貸切走行.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計固定料金キャンセル回数.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計迎車キャンセル回数.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計待回数.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計待加算回数.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計早朝キャンセル回数.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計高齢割回数.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計高齢割額.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計幼児割回数.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計幼児割額.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計リセット回数.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.累計待回数P.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日運賃.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日料金.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日身割回数.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日身割額.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日身割現収.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日ワゴン回数.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日早朝回数.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日予約回数.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日遠割回数.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日遠割額.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日貸切指数.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日貸切割引.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日貸切料金.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日貸切時間.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日貸切走行.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日固定料金キャンセル回数.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日迎車キャンセル回数.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日待ち回数.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日待ち加算回数.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日早朝キャンセル回数.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日高齢割回数.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日高齢割額.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日幼児割回数.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日幼児割額.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日リセット回数.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.一日待ち回数P.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.基本料金.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.以後料金.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.固定料金.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.月間営収.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.営収.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.男.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.女.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.現金.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.未収.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.クレジット.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.カード.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.ID.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.交通系IC.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.キャブカード.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.プリペイドカード.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.WAON.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.メーター外料金.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.クレジット回数.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.貸切回数.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.空車_ETC料金.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.実車_ETC料金.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.燃料合計.Value) _
                '            , Me.Fields.Header.総勤務時間.Value _
                '            , Me.Fields.Header.総休憩時間.Value _
                '            , Me.Fields.Header.総回送時間.Value _
                '            , Me.Fields.Header.総空車停止時間.Value _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.リセット待回数.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.出庫_親メーター.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.入庫_親メーター.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.空車_最高速度.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.実車_最高速度.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.最高速度.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.データNO.Value) _
                '            , Me.Fields.Header.乗車地.Value _
                '            , Me.Fields.Header.乗車日付.Value _
                '            , Me.Fields.Header.乗車時刻.Value _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.フィールド112.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.フィールド113.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.フィールド114.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.フィールド115.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.フィールド116.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.メーター外.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.元料金.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.元現金.Value) _
                '            , Me.Fields.Header.降車地.Value _
                '            , Me.Fields.Header.降車日付.Value _
                '            , Me.Fields.Header.降車時刻.Value _
                '            , Me.Fields.Header.備考.Value _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.空車距離.Value) _
                '            , Me.Fields.Header.空車時刻.Value _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.空車走行時間.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.空車最高速度.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.迎車距離.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.支払区分.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.タリフ区分.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.料金.Value) _
                '            , Me.Fields.Header.実車時刻.Value _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.実車距離.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.実車最高速度.Value) _
                '            , BaseCore.Common.Text.CVal(Me.Fields.Header.空車停止時間.Value _
                '            , Me.Security.ユーザーCD _
                '            , Me.Security.ユーザー名 _
                '            , strNowTime _
                '            , Me.Security.ユーザーCD _
                '            , Me.Security.ユーザー名 _
                '            , strNowTime
                '            )
            Catch ex As Exception
                Me.LastError = "以下のエラーのため、処理を継続できません。" & vbCrLf & ex.Message
                Return False
            End Try

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region
#End Region

#Region "其他"
#Region "事前検査"
        '''========================================================================================
        ''' <SUMMARY>Excelﾒｲﾝ処理</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Function Excel_Validator(ByVal Excel As BaseCore.Common.Excel) As Boolean
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim ada項目 As New NodeDatabase.DataSetMasterTableAdapters.M伝票項目TableAdapter
            Dim tbl項目 As New NodeDatabase.DataSetMaster.M伝票項目DataTable
            Dim qry項目 As New Collection

            Dim tbl検査HD As New NodeDatabase.DataSetTran.T乗務記録DataTable

            '----------------------------------------------------------------------
            ' 項目一覧読込
            '----------------------------------------------------------------------
            qry項目.Clear()
            tbl項目 = ada項目.SelectByCommon(qry項目)

            '----------------------------------------------------------------------
            ' ﾌﾚｰﾑﾜｰｸ定義(必須 & 半角/全角)
            '----------------------------------------------------------------------
            Dim Validator As New BaseCore.Common.Validator
            Dim blnErr As Boolean = False
            Dim LastRow As Integer = Excel.LastRowIndex        '最大行
            Dim LastCol As Integer = Excel.LastColIndex        '最大列

            For i As Long = ConstantExcel.Header.RowTop To LastRow - 1
                ' ﾀｲﾄﾙ行 
                If i = ConstantExcel.Header.RowTop Then
                    Continue For
                End If

                ' ｴﾗｰｸﾘｱ
                Excel.CellClearError(i, ConstantExcel.Header.列数.取込日付)
                Excel.CellClearError(i, ConstantExcel.Header.列数.日報日付)
                Excel.CellClearError(i, ConstantExcel.Header.列数.日報年月)
                Excel.CellClearError(i, ConstantExcel.Header.列数.出庫時刻)
                Excel.CellClearError(i, ConstantExcel.Header.列数.入庫時刻)
                Excel.CellClearError(i, ConstantExcel.Header.列数.乗務員CD)
                Excel.CellClearError(i, ConstantExcel.Header.列数.車両番号)
                Excel.CellClearError(i, ConstantExcel.Header.列数.営業所CD)
                Excel.CellClearError(i, ConstantExcel.Header.列数.部門CD)
                Excel.CellClearError(i, ConstantExcel.Header.列数.勤務CD)
                Excel.CellClearError(i, ConstantExcel.Header.列数.車種CD)
                Excel.CellClearError(i, ConstantExcel.Header.列数.天気)
                Excel.CellClearError(i, ConstantExcel.Header.列数.累計全走行)
                Excel.CellClearError(i, ConstantExcel.Header.列数.累計実車走行)
                Excel.CellClearError(i, ConstantExcel.Header.列数.累計迎車走行)
                Excel.CellClearError(i, ConstantExcel.Header.列数.累計営業回数)
                Excel.CellClearError(i, ConstantExcel.Header.列数.累計以後回数)
                Excel.CellClearError(i, ConstantExcel.Header.列数.累計迎車回数)
                Excel.CellClearError(i, ConstantExcel.Header.列数.一日全走行)
                Excel.CellClearError(i, ConstantExcel.Header.列数.一日実車走行)
                Excel.CellClearError(i, ConstantExcel.Header.列数.一日迎車走行)
                Excel.CellClearError(i, ConstantExcel.Header.列数.一日営業回数)
                Excel.CellClearError(i, ConstantExcel.Header.列数.一日以後回数)
                Excel.CellClearError(i, ConstantExcel.Header.列数.一日迎車回数)
                Excel.CellClearError(i, ConstantExcel.Header.列数.累計運賃)
                Excel.CellClearError(i, ConstantExcel.Header.列数.累計料金)
                Excel.CellClearError(i, ConstantExcel.Header.列数.累計身割回数)
                Excel.CellClearError(i, ConstantExcel.Header.列数.累計身割額)
                Excel.CellClearError(i, ConstantExcel.Header.列数.累計ワゴン回数)
                Excel.CellClearError(i, ConstantExcel.Header.列数.累計早朝回数)
                Excel.CellClearError(i, ConstantExcel.Header.列数.累計予約回数)
                Excel.CellClearError(i, ConstantExcel.Header.列数.累計遠割回数)
                Excel.CellClearError(i, ConstantExcel.Header.列数.累計遠割額)
                Excel.CellClearError(i, ConstantExcel.Header.列数.累計貸切指数)
                Excel.CellClearError(i, ConstantExcel.Header.列数.累計貸切割引額)
                Excel.CellClearError(i, ConstantExcel.Header.列数.累計貸切料金)
                Excel.CellClearError(i, ConstantExcel.Header.列数.累計貸切時間)
                Excel.CellClearError(i, ConstantExcel.Header.列数.累計貸切走行)
                Excel.CellClearError(i, ConstantExcel.Header.列数.累計固定料金キャンセル回数)
                Excel.CellClearError(i, ConstantExcel.Header.列数.累計迎車キャンセル回数)
                Excel.CellClearError(i, ConstantExcel.Header.列数.累計待回数)
                Excel.CellClearError(i, ConstantExcel.Header.列数.累計待加算回数)
                Excel.CellClearError(i, ConstantExcel.Header.列数.累計早朝キャンセル回数)
                Excel.CellClearError(i, ConstantExcel.Header.列数.累計高齢割回数)
                Excel.CellClearError(i, ConstantExcel.Header.列数.累計高齢割額)
                Excel.CellClearError(i, ConstantExcel.Header.列数.累計幼児割回数)
                Excel.CellClearError(i, ConstantExcel.Header.列数.累計幼児割額)
                Excel.CellClearError(i, ConstantExcel.Header.列数.累計リセット回数)
                Excel.CellClearError(i, ConstantExcel.Header.列数.累計待回数P)
                Excel.CellClearError(i, ConstantExcel.Header.列数.一日運賃)
                Excel.CellClearError(i, ConstantExcel.Header.列数.一日料金)
                Excel.CellClearError(i, ConstantExcel.Header.列数.一日身割回数)
                Excel.CellClearError(i, ConstantExcel.Header.列数.一日身割額)
                Excel.CellClearError(i, ConstantExcel.Header.列数.一日身割現収)
                Excel.CellClearError(i, ConstantExcel.Header.列数.一日ワゴン回数)
                Excel.CellClearError(i, ConstantExcel.Header.列数.一日早朝回数)
                Excel.CellClearError(i, ConstantExcel.Header.列数.一日予約回数)
                Excel.CellClearError(i, ConstantExcel.Header.列数.一日遠割回数)
                Excel.CellClearError(i, ConstantExcel.Header.列数.一日遠割額)
                Excel.CellClearError(i, ConstantExcel.Header.列数.一日貸切指数)
                Excel.CellClearError(i, ConstantExcel.Header.列数.一日貸切割引)
                Excel.CellClearError(i, ConstantExcel.Header.列数.一日貸切料金)
                Excel.CellClearError(i, ConstantExcel.Header.列数.一日貸切時間)
                Excel.CellClearError(i, ConstantExcel.Header.列数.一日貸切走行)
                Excel.CellClearError(i, ConstantExcel.Header.列数.一日固定料金キャンセル回数)
                Excel.CellClearError(i, ConstantExcel.Header.列数.一日迎車キャンセル回数)
                Excel.CellClearError(i, ConstantExcel.Header.列数.一日待ち回数)
                Excel.CellClearError(i, ConstantExcel.Header.列数.一日待ち加算回数)
                Excel.CellClearError(i, ConstantExcel.Header.列数.一日早朝キャンセル回数)
                Excel.CellClearError(i, ConstantExcel.Header.列数.一日高齢割回数)
                Excel.CellClearError(i, ConstantExcel.Header.列数.一日高齢割額)
                Excel.CellClearError(i, ConstantExcel.Header.列数.一日幼児割回数)
                Excel.CellClearError(i, ConstantExcel.Header.列数.一日幼児割額)
                Excel.CellClearError(i, ConstantExcel.Header.列数.一日リセット回数)
                Excel.CellClearError(i, ConstantExcel.Header.列数.一日待ち回数P)
                Excel.CellClearError(i, ConstantExcel.Header.列数.基本料金)
                Excel.CellClearError(i, ConstantExcel.Header.列数.以後料金)
                Excel.CellClearError(i, ConstantExcel.Header.列数.固定料金)
                Excel.CellClearError(i, ConstantExcel.Header.列数.月間営収)
                Excel.CellClearError(i, ConstantExcel.Header.列数.営収)
                Excel.CellClearError(i, ConstantExcel.Header.列数.男)
                Excel.CellClearError(i, ConstantExcel.Header.列数.女)
                Excel.CellClearError(i, ConstantExcel.Header.列数.現金)
                Excel.CellClearError(i, ConstantExcel.Header.列数.未収)
                Excel.CellClearError(i, ConstantExcel.Header.列数.クレジット)
                Excel.CellClearError(i, ConstantExcel.Header.列数.カード)
                Excel.CellClearError(i, ConstantExcel.Header.列数.ID)
                Excel.CellClearError(i, ConstantExcel.Header.列数.交通系IC)
                Excel.CellClearError(i, ConstantExcel.Header.列数.キャブカード)
                Excel.CellClearError(i, ConstantExcel.Header.列数.プリペイドカード)
                Excel.CellClearError(i, ConstantExcel.Header.列数.WAON)
                Excel.CellClearError(i, ConstantExcel.Header.列数.メーター外料金)
                Excel.CellClearError(i, ConstantExcel.Header.列数.クレジット回数)
                Excel.CellClearError(i, ConstantExcel.Header.列数.貸切回数)
                Excel.CellClearError(i, ConstantExcel.Header.列数.空車_ETC料金)
                Excel.CellClearError(i, ConstantExcel.Header.列数.実車_ETC料金)
                Excel.CellClearError(i, ConstantExcel.Header.列数.燃料合計)
                Excel.CellClearError(i, ConstantExcel.Header.列数.総勤務時間)
                Excel.CellClearError(i, ConstantExcel.Header.列数.総休憩時間)
                Excel.CellClearError(i, ConstantExcel.Header.列数.総回送時間)
                Excel.CellClearError(i, ConstantExcel.Header.列数.総空車停止時間)
                Excel.CellClearError(i, ConstantExcel.Header.列数.リセット待回数)
                Excel.CellClearError(i, ConstantExcel.Header.列数.出庫_親メーター)
                Excel.CellClearError(i, ConstantExcel.Header.列数.入庫_親メーター)
                Excel.CellClearError(i, ConstantExcel.Header.列数.空車_最高速度)
                Excel.CellClearError(i, ConstantExcel.Header.列数.実車_最高速度)
                Excel.CellClearError(i, ConstantExcel.Header.列数.最高速度)
                Excel.CellClearError(i, ConstantExcel.Header.列数.データNO)
                Excel.CellClearError(i, ConstantExcel.Header.列数.乗車地)
                Excel.CellClearError(i, ConstantExcel.Header.列数.乗車日付)
                Excel.CellClearError(i, ConstantExcel.Header.列数.乗車時刻)
                Excel.CellClearError(i, ConstantExcel.Header.列数.フィールド112)
                Excel.CellClearError(i, ConstantExcel.Header.列数.フィールド113)
                Excel.CellClearError(i, ConstantExcel.Header.列数.フィールド114)
                Excel.CellClearError(i, ConstantExcel.Header.列数.フィールド115)
                Excel.CellClearError(i, ConstantExcel.Header.列数.フィールド116)
                Excel.CellClearError(i, ConstantExcel.Header.列数.メーター外)
                Excel.CellClearError(i, ConstantExcel.Header.列数.元料金)
                Excel.CellClearError(i, ConstantExcel.Header.列数.元現金)
                Excel.CellClearError(i, ConstantExcel.Header.列数.降車地)
                Excel.CellClearError(i, ConstantExcel.Header.列数.降車日付)
                Excel.CellClearError(i, ConstantExcel.Header.列数.降車時刻)
                Excel.CellClearError(i, ConstantExcel.Header.列数.備考)
                Excel.CellClearError(i, ConstantExcel.Header.列数.空車距離)
                Excel.CellClearError(i, ConstantExcel.Header.列数.空車時刻)
                Excel.CellClearError(i, ConstantExcel.Header.列数.空車走行時間)
                Excel.CellClearError(i, ConstantExcel.Header.列数.空車最高速度)
                Excel.CellClearError(i, ConstantExcel.Header.列数.迎車距離)
                Excel.CellClearError(i, ConstantExcel.Header.列数.支払区分)
                Excel.CellClearError(i, ConstantExcel.Header.列数.タリフ区分)
                Excel.CellClearError(i, ConstantExcel.Header.列数.料金)
                Excel.CellClearError(i, ConstantExcel.Header.列数.実車時刻)
                Excel.CellClearError(i, ConstantExcel.Header.列数.実車距離)
                Excel.CellClearError(i, ConstantExcel.Header.列数.実車最高速度)
                Excel.CellClearError(i, ConstantExcel.Header.列数.空車停止時間)

                'Me.Fields.Header.店舗CD.Value = Excel.CellGet(i, ConstantExcel.Header.列数.店舗CD).Value
                'Me.Fields.Header.納品日付.Value = Excel.CellGet(i, ConstantExcel.Header.列数.納品日付).Value
                'Me.Fields.Header.伝票番号.Value = Excel.CellGet(i, ConstantExcel.Header.列数.伝票番号).Value
                'Me.Fields.Header.行.Value = Excel.CellGet(i, ConstantExcel.Header.列数.行).Value
                'Me.Fields.Header.JANCD.Value = Excel.CellGet(i, ConstantExcel.Header.列数.JANCD).Value
                'Me.Fields.Header.商品名.Value = Excel.CellGet(i, ConstantExcel.Header.列数.商品名).Value
                'Me.Fields.Header.定価.Value = Excel.CellGet(i, ConstantExcel.Header.列数.定価).Value
                'Me.Fields.Header.数量.Value = Excel.CellGet(i, ConstantExcel.Header.列数.数量).Value
                'Me.Fields.Header.仕入単価.Value = Excel.CellGet(i, ConstantExcel.Header.列数.仕入単価).Value
                'Me.Fields.Header.仕入金額.Value = Excel.CellGet(i, ConstantExcel.Header.列数.仕入金額).Value
                'Me.Fields.Header.消費税額.Value = Excel.CellGet(i, ConstantExcel.Header.列数.消費税額).Value
                'Me.Fields.Header.消費税率.Value = Excel.CellGet(i, ConstantExcel.Header.列数.消費税率).Value
                'Me.Fields.Header.入数.Value = Excel.CellGet(i, ConstantExcel.Header.列数.入数).Value

                For Each row項目 As NodeDatabase.DataSetMaster.M伝票項目Row In tbl項目.Rows
                    Dim FieldName As String = row項目.Item(tbl項目.伝票項目名Column.ColumnName)
                    Dim Required As Boolean = (row項目.Item(tbl項目.必須フラグColumn.ColumnName) = NodeContents.Constant.CodeValue.フラグID.オン)

                    Select Case FieldName.ToString
                        Case tbl検査HD.取込日付Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.取込日付, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.取込日付) : blnErr = True
                        Case tbl検査HD.日報日付Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.日報日付, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.日報日付) : blnErr = True
                        Case tbl検査HD.日報年月Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.日報年月, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.日報年月) : blnErr = True
                        Case tbl検査HD.出庫時刻Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.出庫時刻, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.出庫時刻) : blnErr = True
                        Case tbl検査HD.入庫時刻Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.入庫時刻, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.入庫時刻) : blnErr = True
                        Case tbl検査HD.乗務員CDColumn.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.乗務員CD, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.乗務員CD) : blnErr = True
                        Case tbl検査HD.車両番号Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.車両番号, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.車両番号) : blnErr = True
                        Case tbl検査HD.営業所CDColumn.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.営業所CD, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.営業所CD) : blnErr = True
                        Case tbl検査HD.部門CDColumn.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.部門CD, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.部門CD) : blnErr = True
                        Case tbl検査HD.勤務CDColumn.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.勤務CD, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.勤務CD) : blnErr = True
                        Case tbl検査HD.車種CDColumn.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.車種CD, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.車種CD) : blnErr = True
                        Case tbl検査HD.天気Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.天気, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.天気) : blnErr = True
                        Case tbl検査HD.累計全走行Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.累計全走行, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.累計全走行) : blnErr = True
                        Case tbl検査HD.累計実車走行Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.累計実車走行, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.累計実車走行) : blnErr = True
                        Case tbl検査HD.累計迎車走行Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.累計迎車走行, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.累計迎車走行) : blnErr = True
                        Case tbl検査HD.累計営業回数Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.累計営業回数, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.累計営業回数) : blnErr = True
                        Case tbl検査HD.累計以後回数Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.累計以後回数, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.累計以後回数) : blnErr = True
                        Case tbl検査HD.累計迎車回数Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.累計迎車回数, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.累計迎車回数) : blnErr = True
                        Case tbl検査HD.一日全走行Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.一日全走行, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.一日全走行) : blnErr = True
                        Case tbl検査HD.一日実車走行Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.一日実車走行, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.一日実車走行) : blnErr = True
                        Case tbl検査HD.一日迎車走行Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.一日迎車走行, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.一日迎車走行) : blnErr = True
                        Case tbl検査HD.一日営業回数Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.一日営業回数, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.一日営業回数) : blnErr = True
                        Case tbl検査HD.一日以後回数Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.一日以後回数, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.一日以後回数) : blnErr = True
                        Case tbl検査HD.一日迎車回数Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.一日迎車回数, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.一日迎車回数) : blnErr = True
                        Case tbl検査HD.累計運賃Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.累計運賃, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.累計運賃) : blnErr = True
                        Case tbl検査HD.累計料金Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.累計料金, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.累計料金) : blnErr = True
                        Case tbl検査HD.累計身割回数Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.累計身割回数, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.累計身割回数) : blnErr = True
                        Case tbl検査HD.累計身割額Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.累計身割額, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.累計身割額) : blnErr = True
                        Case tbl検査HD.累計ワゴン回数Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.累計ワゴン回数, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.累計ワゴン回数) : blnErr = True
                        Case tbl検査HD.累計早朝回数Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.累計早朝回数, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.累計早朝回数) : blnErr = True
                        Case tbl検査HD.累計予約回数Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.累計予約回数, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.累計予約回数) : blnErr = True
                        Case tbl検査HD.累計遠割回数Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.累計遠割回数, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.累計遠割回数) : blnErr = True
                        Case tbl検査HD.累計遠割額Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.累計遠割額, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.累計遠割額) : blnErr = True
                        Case tbl検査HD.累計貸切指数Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.累計貸切指数, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.累計貸切指数) : blnErr = True
                        Case tbl検査HD.累計貸切割引額Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.累計貸切割引額, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.累計貸切割引額) : blnErr = True
                        Case tbl検査HD.累計貸切料金Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.累計貸切料金, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.累計貸切料金) : blnErr = True
                        Case tbl検査HD.累計貸切時間Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.累計貸切時間, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.累計貸切時間) : blnErr = True
                        Case tbl検査HD.累計貸切走行Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.累計貸切走行, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.累計貸切走行) : blnErr = True
                        Case tbl検査HD.累計固定料金キャンセル回数Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.累計固定料金キャンセル回数, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.累計固定料金キャンセル回数) : blnErr = True
                        Case tbl検査HD.累計迎車キャンセル回数Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.累計迎車キャンセル回数, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.累計迎車キャンセル回数) : blnErr = True
                        Case tbl検査HD.累計待回数Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.累計待回数, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.累計待回数) : blnErr = True
                        Case tbl検査HD.累計待加算回数Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.累計待加算回数, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.累計待加算回数) : blnErr = True
                        Case tbl検査HD.累計早朝キャンセル回数Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.累計早朝キャンセル回数, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.累計早朝キャンセル回数) : blnErr = True
                        Case tbl検査HD.累計高齢割回数Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.累計高齢割回数, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.累計高齢割回数) : blnErr = True
                        Case tbl検査HD.累計高齢割額Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.累計高齢割額, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.累計高齢割額) : blnErr = True
                        Case tbl検査HD.累計幼児割回数Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.累計幼児割回数, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.累計幼児割回数) : blnErr = True
                        Case tbl検査HD.累計幼児割額Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.累計幼児割額, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.累計幼児割額) : blnErr = True
                        Case tbl検査HD.累計リセット回数Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.累計リセット回数, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.累計リセット回数) : blnErr = True
                        Case tbl検査HD.累計待回数PColumn.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.累計待回数P, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.累計待回数P) : blnErr = True
                        Case tbl検査HD.一日運賃Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.一日運賃, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.一日運賃) : blnErr = True
                        Case tbl検査HD.一日料金Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.一日料金, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.一日料金) : blnErr = True
                        Case tbl検査HD.一日身割回数Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.一日身割回数, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.一日身割回数) : blnErr = True
                        Case tbl検査HD.一日身割額Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.一日身割額, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.一日身割額) : blnErr = True
                        Case tbl検査HD.一日身割現収Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.一日身割現収, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.一日身割現収) : blnErr = True
                        Case tbl検査HD.一日ワゴン回数Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.一日ワゴン回数, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.一日ワゴン回数) : blnErr = True
                        Case tbl検査HD.一日早朝回数Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.一日早朝回数, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.一日早朝回数) : blnErr = True
                        Case tbl検査HD.一日予約回数Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.一日予約回数, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.一日予約回数) : blnErr = True
                        Case tbl検査HD.一日遠割回数Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.一日遠割回数, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.一日遠割回数) : blnErr = True
                        Case tbl検査HD.一日遠割額Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.一日遠割額, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.一日遠割額) : blnErr = True
                        Case tbl検査HD.一日貸切指数Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.一日貸切指数, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.一日貸切指数) : blnErr = True
                        Case tbl検査HD.一日貸切割引Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.一日貸切割引, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.一日貸切割引) : blnErr = True
                        Case tbl検査HD.一日貸切料金Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.一日貸切料金, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.一日貸切料金) : blnErr = True
                        Case tbl検査HD.一日貸切時間Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.一日貸切時間, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.一日貸切時間) : blnErr = True
                        Case tbl検査HD.一日貸切走行Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.一日貸切走行, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.一日貸切走行) : blnErr = True
                        Case tbl検査HD.一日固定料金キャンセル回数Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.一日固定料金キャンセル回数, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.一日固定料金キャンセル回数) : blnErr = True
                        Case tbl検査HD.一日迎車キャンセル回数Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.一日迎車キャンセル回数, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.一日迎車キャンセル回数) : blnErr = True
                        Case tbl検査HD.一日待ち回数Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.一日待ち回数, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.一日待ち回数) : blnErr = True
                        Case tbl検査HD.一日待ち加算回数Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.一日待ち加算回数, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.一日待ち加算回数) : blnErr = True
                        Case tbl検査HD.一日早朝キャンセル回数Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.一日早朝キャンセル回数, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.一日早朝キャンセル回数) : blnErr = True
                        Case tbl検査HD.一日高齢割回数Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.一日高齢割回数, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.一日高齢割回数) : blnErr = True
                        Case tbl検査HD.一日高齢割額Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.一日高齢割額, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.一日高齢割額) : blnErr = True
                        Case tbl検査HD.一日幼児割回数Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.一日幼児割回数, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.一日幼児割回数) : blnErr = True
                        Case tbl検査HD.一日幼児割額Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.一日幼児割額, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.一日幼児割額) : blnErr = True
                        Case tbl検査HD.一日リセット回数Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.一日リセット回数, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.一日リセット回数) : blnErr = True
                        Case tbl検査HD.一日待ち回数PColumn.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.一日待ち回数P, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.一日待ち回数P) : blnErr = True
                        Case tbl検査HD.基本料金Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.基本料金, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.基本料金) : blnErr = True
                        Case tbl検査HD.以後料金Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.以後料金, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.以後料金) : blnErr = True
                        Case tbl検査HD.固定料金Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.固定料金, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.固定料金) : blnErr = True
                        Case tbl検査HD.月間営収Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.月間営収, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.月間営収) : blnErr = True
                        Case tbl検査HD.営収Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.営収, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.営収) : blnErr = True
                        Case tbl検査HD.男Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.男, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.男) : blnErr = True
                        Case tbl検査HD.女Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.女, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.女) : blnErr = True
                        Case tbl検査HD.現金Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.現金, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.現金) : blnErr = True
                        Case tbl検査HD.未収Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.未収, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.未収) : blnErr = True
                        Case tbl検査HD.クレジットColumn.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.クレジット, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.クレジット) : blnErr = True
                        Case tbl検査HD.カードColumn.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.カード, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.カード) : blnErr = True
                        Case tbl検査HD.IDColumn.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.ID, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.ID) : blnErr = True
                        Case tbl検査HD.交通系ICColumn.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.交通系IC, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.交通系IC) : blnErr = True
                        Case tbl検査HD.キャブカードColumn.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.キャブカード, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.キャブカード) : blnErr = True
                        Case tbl検査HD.プリペイドカードColumn.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.プリペイドカード, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.プリペイドカード) : blnErr = True
                        Case tbl検査HD.WAONColumn.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.WAON, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.WAON) : blnErr = True
                        Case tbl検査HD.メーター外料金Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.メーター外料金, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.メーター外料金) : blnErr = True
                        Case tbl検査HD.クレジット回数Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.クレジット回数, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.クレジット回数) : blnErr = True
                        Case tbl検査HD.貸切回数Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.貸切回数, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.貸切回数) : blnErr = True
                        Case tbl検査HD.空車_ETC料金Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.空車_ETC料金, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.空車_ETC料金) : blnErr = True
                        Case tbl検査HD.実車_ETC料金Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.実車_ETC料金, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.実車_ETC料金) : blnErr = True
                        Case tbl検査HD.燃料合計Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.燃料合計, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.燃料合計) : blnErr = True
                        Case tbl検査HD.総勤務時間Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.総勤務時間, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.総勤務時間) : blnErr = True
                        Case tbl検査HD.総休憩時間Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.総休憩時間, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.総休憩時間) : blnErr = True
                        Case tbl検査HD.総回送時間Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.総回送時間, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.総回送時間) : blnErr = True
                        Case tbl検査HD.総空車停止時間Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.総空車停止時間, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.総空車停止時間) : blnErr = True
                        Case tbl検査HD.リセット待回数Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.リセット待回数, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.リセット待回数) : blnErr = True
                        Case tbl検査HD.出庫_親メーターColumn.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.出庫_親メーター, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.出庫_親メーター) : blnErr = True
                        Case tbl検査HD.入庫_親メーターColumn.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.入庫_親メーター, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.入庫_親メーター) : blnErr = True
                        Case tbl検査HD.空車_最高速度Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.空車_最高速度, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.空車_最高速度) : blnErr = True
                        Case tbl検査HD.実車_最高速度Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.実車_最高速度, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.実車_最高速度) : blnErr = True
                        Case tbl検査HD.最高速度Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.最高速度, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.最高速度) : blnErr = True
                        Case tbl検査HD.データNOColumn.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.データNO, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.データNO) : blnErr = True
                        Case tbl検査HD.乗車地Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.乗車地, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.乗車地) : blnErr = True
                        Case tbl検査HD.乗車日付Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.乗車日付, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.乗車日付) : blnErr = True
                        Case tbl検査HD.乗車時刻Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.乗車時刻, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.乗車時刻) : blnErr = True
                        Case tbl検査HD.フィールド112Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.フィールド112, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.フィールド112) : blnErr = True
                        Case tbl検査HD.フィールド113Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.フィールド113, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.フィールド113) : blnErr = True
                        Case tbl検査HD.フィールド114Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.フィールド114, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.フィールド114) : blnErr = True
                        Case tbl検査HD.フィールド115Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.フィールド115, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.フィールド115) : blnErr = True
                        Case tbl検査HD.フィールド116Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.フィールド116, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.フィールド116) : blnErr = True
                        Case tbl検査HD.メーター外Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.メーター外, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.メーター外) : blnErr = True
                        Case tbl検査HD.元料金Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.元料金, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.元料金) : blnErr = True
                        Case tbl検査HD.元現金Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.元現金, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.元現金) : blnErr = True
                        Case tbl検査HD.降車地Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.降車地, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.降車地) : blnErr = True
                        Case tbl検査HD.降車日付Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.降車日付, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.降車日付) : blnErr = True
                        Case tbl検査HD.降車時刻Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.降車時刻, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.降車時刻) : blnErr = True
                        Case tbl検査HD.備考Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.備考, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.備考) : blnErr = True
                        Case tbl検査HD.空車距離Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.空車距離, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.空車距離) : blnErr = True
                        Case tbl検査HD.空車時刻Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.空車時刻, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.空車時刻) : blnErr = True
                        Case tbl検査HD.空車走行時間Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.空車走行時間, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.空車走行時間) : blnErr = True
                        Case tbl検査HD.空車最高速度Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.空車最高速度, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.空車最高速度) : blnErr = True
                        Case tbl検査HD.迎車距離Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.迎車距離, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.迎車距離) : blnErr = True
                        Case tbl検査HD.支払区分Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.支払区分, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.支払区分) : blnErr = True
                        Case tbl検査HD.タリフ区分Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.タリフ区分, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.タリフ区分) : blnErr = True
                        Case tbl検査HD.料金Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.料金, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.料金) : blnErr = True
                        Case tbl検査HD.実車時刻Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.実車時刻, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.実車時刻) : blnErr = True
                        Case tbl検査HD.実車距離Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.実車距離, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.実車距離) : blnErr = True
                        Case tbl検査HD.実車最高速度Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.実車最高速度, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.実車最高速度) : blnErr = True
                        Case tbl検査HD.空車停止時間Column.ColumnName : If Not Validator.BaseChecker(Me.Fields.Header.空車停止時間, i) Then Validator.SetMessage(Me.LastError, Validator.LastError) : Excel.CellSetError(i, ConstantExcel.Header.列数.空車停止時間) : blnErr = True
                        Case Else
                    End Select
                Next
            Next

            ' 個別検査
            Me.Excel_Validator_Another(Excel)

            If Me.LastError <> "" Then
                Excel.SheetInsert(ConstantExcel.エラー.SheetName)

                Dim arrError() As String = Me.LastError.Replace(vbCrLf, vbTab).Split(vbTab)
                For i As Integer = 0 To arrError.Length - 1
                    Excel.CellSetValue(i, 0, arrError(i))
                Next

                Return False
            End If

            '------------------------------------------------------------------
            ' 返却
            '------------------------------------------------------------------
            Return True
        End Function

        '''========================================================================================
        ''' <SUMMARY>Excelﾒｲﾝ処理</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Function Excel_Validator_Another(ByVal Excel As BaseCore.Common.Excel) As Boolean
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------

            '----------------------------------------------------------------------
            ' 読込
            '----------------------------------------------------------------------

            '----------------------------------------------------------------------
            ' 個別検査
            '----------------------------------------------------------------------
            Dim Validator As New BaseCore.Common.Validator
            Dim LastRow As Integer = Excel.LastRowIndex        '最大行

            For i As Long = ConstantExcel.Header.RowTop To LastRow - 1
                ' ﾀｲﾄﾙ行 
                If i = ConstantExcel.Header.RowTop Then
                    Continue For
                End If

                Me.Fields.Header.取込日付.Value = Excel.CellGet(i, ConstantExcel.Header.列数.取込日付).Value
                Me.Fields.Header.日報日付.Value = Excel.CellGet(i, ConstantExcel.Header.列数.日報日付).Value
                Me.Fields.Header.日報年月.Value = Excel.CellGet(i, ConstantExcel.Header.列数.日報年月).Value
                Me.Fields.Header.出庫時刻.Value = Excel.CellGet(i, ConstantExcel.Header.列数.出庫時刻).Value
                Me.Fields.Header.入庫時刻.Value = Excel.CellGet(i, ConstantExcel.Header.列数.入庫時刻).Value
                Me.Fields.Header.乗務員CD.Value = Excel.CellGet(i, ConstantExcel.Header.列数.乗務員CD).Value
                Me.Fields.Header.車両番号.Value = Excel.CellGet(i, ConstantExcel.Header.列数.車両番号).Value
                Me.Fields.Header.営業所CD.Value = Excel.CellGet(i, ConstantExcel.Header.列数.営業所CD).Value
                Me.Fields.Header.部門CD.Value = Excel.CellGet(i, ConstantExcel.Header.列数.部門CD).Value
                Me.Fields.Header.勤務CD.Value = Excel.CellGet(i, ConstantExcel.Header.列数.勤務CD).Value
                Me.Fields.Header.車種CD.Value = Excel.CellGet(i, ConstantExcel.Header.列数.車種CD).Value
                Me.Fields.Header.天気.Value = Excel.CellGet(i, ConstantExcel.Header.列数.天気).Value
                Me.Fields.Header.累計全走行.Value = Excel.CellGet(i, ConstantExcel.Header.列数.累計全走行).Value
                Me.Fields.Header.累計実車走行.Value = Excel.CellGet(i, ConstantExcel.Header.列数.累計実車走行).Value
                Me.Fields.Header.累計迎車走行.Value = Excel.CellGet(i, ConstantExcel.Header.列数.累計迎車走行).Value
                Me.Fields.Header.累計営業回数.Value = Excel.CellGet(i, ConstantExcel.Header.列数.累計営業回数).Value
                Me.Fields.Header.累計以後回数.Value = Excel.CellGet(i, ConstantExcel.Header.列数.累計以後回数).Value
                Me.Fields.Header.累計迎車回数.Value = Excel.CellGet(i, ConstantExcel.Header.列数.累計迎車回数).Value
                Me.Fields.Header.一日全走行.Value = Excel.CellGet(i, ConstantExcel.Header.列数.一日全走行).Value
                Me.Fields.Header.一日実車走行.Value = Excel.CellGet(i, ConstantExcel.Header.列数.一日実車走行).Value
                Me.Fields.Header.一日迎車走行.Value = Excel.CellGet(i, ConstantExcel.Header.列数.一日迎車走行).Value
                Me.Fields.Header.一日営業回数.Value = Excel.CellGet(i, ConstantExcel.Header.列数.一日営業回数).Value
                Me.Fields.Header.一日以後回数.Value = Excel.CellGet(i, ConstantExcel.Header.列数.一日以後回数).Value
                Me.Fields.Header.一日迎車回数.Value = Excel.CellGet(i, ConstantExcel.Header.列数.一日迎車回数).Value
                Me.Fields.Header.累計運賃.Value = Excel.CellGet(i, ConstantExcel.Header.列数.累計運賃).Value
                Me.Fields.Header.累計料金.Value = Excel.CellGet(i, ConstantExcel.Header.列数.累計料金).Value
                Me.Fields.Header.累計身割回数.Value = Excel.CellGet(i, ConstantExcel.Header.列数.累計身割回数).Value
                Me.Fields.Header.累計身割額.Value = Excel.CellGet(i, ConstantExcel.Header.列数.累計身割額).Value
                Me.Fields.Header.累計ワゴン回数.Value = Excel.CellGet(i, ConstantExcel.Header.列数.累計ワゴン回数).Value
                Me.Fields.Header.累計早朝回数.Value = Excel.CellGet(i, ConstantExcel.Header.列数.累計早朝回数).Value
                Me.Fields.Header.累計予約回数.Value = Excel.CellGet(i, ConstantExcel.Header.列数.累計予約回数).Value
                Me.Fields.Header.累計遠割回数.Value = Excel.CellGet(i, ConstantExcel.Header.列数.累計遠割回数).Value
                Me.Fields.Header.累計遠割額.Value = Excel.CellGet(i, ConstantExcel.Header.列数.累計遠割額).Value
                Me.Fields.Header.累計貸切指数.Value = Excel.CellGet(i, ConstantExcel.Header.列数.累計貸切指数).Value
                Me.Fields.Header.累計貸切割引額.Value = Excel.CellGet(i, ConstantExcel.Header.列数.累計貸切割引額).Value
                Me.Fields.Header.累計貸切料金.Value = Excel.CellGet(i, ConstantExcel.Header.列数.累計貸切料金).Value
                Me.Fields.Header.累計貸切時間.Value = Excel.CellGet(i, ConstantExcel.Header.列数.累計貸切時間).Value
                Me.Fields.Header.累計貸切走行.Value = Excel.CellGet(i, ConstantExcel.Header.列数.累計貸切走行).Value
                Me.Fields.Header.累計固定料金キャンセル回数.Value = Excel.CellGet(i, ConstantExcel.Header.列数.累計固定料金キャンセル回数).Value
                Me.Fields.Header.累計迎車キャンセル回数.Value = Excel.CellGet(i, ConstantExcel.Header.列数.累計迎車キャンセル回数).Value
                Me.Fields.Header.累計待回数.Value = Excel.CellGet(i, ConstantExcel.Header.列数.累計待回数).Value
                Me.Fields.Header.累計待加算回数.Value = Excel.CellGet(i, ConstantExcel.Header.列数.累計待加算回数).Value
                Me.Fields.Header.累計早朝キャンセル回数.Value = Excel.CellGet(i, ConstantExcel.Header.列数.累計早朝キャンセル回数).Value
                Me.Fields.Header.累計高齢割回数.Value = Excel.CellGet(i, ConstantExcel.Header.列数.累計高齢割回数).Value
                Me.Fields.Header.累計高齢割額.Value = Excel.CellGet(i, ConstantExcel.Header.列数.累計高齢割額).Value
                Me.Fields.Header.累計幼児割回数.Value = Excel.CellGet(i, ConstantExcel.Header.列数.累計幼児割回数).Value
                Me.Fields.Header.累計幼児割額.Value = Excel.CellGet(i, ConstantExcel.Header.列数.累計幼児割額).Value
                Me.Fields.Header.累計リセット回数.Value = Excel.CellGet(i, ConstantExcel.Header.列数.累計リセット回数).Value
                Me.Fields.Header.累計待回数P.Value = Excel.CellGet(i, ConstantExcel.Header.列数.累計待回数P).Value
                Me.Fields.Header.一日運賃.Value = Excel.CellGet(i, ConstantExcel.Header.列数.一日運賃).Value
                Me.Fields.Header.一日料金.Value = Excel.CellGet(i, ConstantExcel.Header.列数.一日料金).Value
                Me.Fields.Header.一日身割回数.Value = Excel.CellGet(i, ConstantExcel.Header.列数.一日身割回数).Value
                Me.Fields.Header.一日身割額.Value = Excel.CellGet(i, ConstantExcel.Header.列数.一日身割額).Value
                Me.Fields.Header.一日身割現収.Value = Excel.CellGet(i, ConstantExcel.Header.列数.一日身割現収).Value
                Me.Fields.Header.一日ワゴン回数.Value = Excel.CellGet(i, ConstantExcel.Header.列数.一日ワゴン回数).Value
                Me.Fields.Header.一日早朝回数.Value = Excel.CellGet(i, ConstantExcel.Header.列数.一日早朝回数).Value
                Me.Fields.Header.一日予約回数.Value = Excel.CellGet(i, ConstantExcel.Header.列数.一日予約回数).Value
                Me.Fields.Header.一日遠割回数.Value = Excel.CellGet(i, ConstantExcel.Header.列数.一日遠割回数).Value
                Me.Fields.Header.一日遠割額.Value = Excel.CellGet(i, ConstantExcel.Header.列数.一日遠割額).Value
                Me.Fields.Header.一日貸切指数.Value = Excel.CellGet(i, ConstantExcel.Header.列数.一日貸切指数).Value
                Me.Fields.Header.一日貸切割引.Value = Excel.CellGet(i, ConstantExcel.Header.列数.一日貸切割引).Value
                Me.Fields.Header.一日貸切料金.Value = Excel.CellGet(i, ConstantExcel.Header.列数.一日貸切料金).Value
                Me.Fields.Header.一日貸切時間.Value = Excel.CellGet(i, ConstantExcel.Header.列数.一日貸切時間).Value
                Me.Fields.Header.一日貸切走行.Value = Excel.CellGet(i, ConstantExcel.Header.列数.一日貸切走行).Value
                Me.Fields.Header.一日固定料金キャンセル回数.Value = Excel.CellGet(i, ConstantExcel.Header.列数.一日固定料金キャンセル回数).Value
                Me.Fields.Header.一日迎車キャンセル回数.Value = Excel.CellGet(i, ConstantExcel.Header.列数.一日迎車キャンセル回数).Value
                Me.Fields.Header.一日待ち回数.Value = Excel.CellGet(i, ConstantExcel.Header.列数.一日待ち回数).Value
                Me.Fields.Header.一日待ち加算回数.Value = Excel.CellGet(i, ConstantExcel.Header.列数.一日待ち加算回数).Value
                Me.Fields.Header.一日早朝キャンセル回数.Value = Excel.CellGet(i, ConstantExcel.Header.列数.一日早朝キャンセル回数).Value
                Me.Fields.Header.一日高齢割回数.Value = Excel.CellGet(i, ConstantExcel.Header.列数.一日高齢割回数).Value
                Me.Fields.Header.一日高齢割額.Value = Excel.CellGet(i, ConstantExcel.Header.列数.一日高齢割額).Value
                Me.Fields.Header.一日幼児割回数.Value = Excel.CellGet(i, ConstantExcel.Header.列数.一日幼児割回数).Value
                Me.Fields.Header.一日幼児割額.Value = Excel.CellGet(i, ConstantExcel.Header.列数.一日幼児割額).Value
                Me.Fields.Header.一日リセット回数.Value = Excel.CellGet(i, ConstantExcel.Header.列数.一日リセット回数).Value
                Me.Fields.Header.一日待ち回数P.Value = Excel.CellGet(i, ConstantExcel.Header.列数.一日待ち回数P).Value
                Me.Fields.Header.基本料金.Value = Excel.CellGet(i, ConstantExcel.Header.列数.基本料金).Value
                Me.Fields.Header.以後料金.Value = Excel.CellGet(i, ConstantExcel.Header.列数.以後料金).Value
                Me.Fields.Header.固定料金.Value = Excel.CellGet(i, ConstantExcel.Header.列数.固定料金).Value
                Me.Fields.Header.月間営収.Value = Excel.CellGet(i, ConstantExcel.Header.列数.月間営収).Value
                Me.Fields.Header.営収.Value = Excel.CellGet(i, ConstantExcel.Header.列数.営収).Value
                Me.Fields.Header.男.Value = Excel.CellGet(i, ConstantExcel.Header.列数.男).Value
                Me.Fields.Header.女.Value = Excel.CellGet(i, ConstantExcel.Header.列数.女).Value
                Me.Fields.Header.現金.Value = Excel.CellGet(i, ConstantExcel.Header.列数.現金).Value
                Me.Fields.Header.未収.Value = Excel.CellGet(i, ConstantExcel.Header.列数.未収).Value
                Me.Fields.Header.クレジット.Value = Excel.CellGet(i, ConstantExcel.Header.列数.クレジット).Value
                Me.Fields.Header.カード.Value = Excel.CellGet(i, ConstantExcel.Header.列数.カード).Value
                Me.Fields.Header.ID.Value = Excel.CellGet(i, ConstantExcel.Header.列数.ID).Value
                Me.Fields.Header.交通系IC.Value = Excel.CellGet(i, ConstantExcel.Header.列数.交通系IC).Value
                Me.Fields.Header.キャブカード.Value = Excel.CellGet(i, ConstantExcel.Header.列数.キャブカード).Value
                Me.Fields.Header.プリペイドカード.Value = Excel.CellGet(i, ConstantExcel.Header.列数.プリペイドカード).Value
                Me.Fields.Header.WAON.Value = Excel.CellGet(i, ConstantExcel.Header.列数.WAON).Value
                Me.Fields.Header.メーター外料金.Value = Excel.CellGet(i, ConstantExcel.Header.列数.メーター外料金).Value
                Me.Fields.Header.クレジット回数.Value = Excel.CellGet(i, ConstantExcel.Header.列数.クレジット回数).Value
                Me.Fields.Header.貸切回数.Value = Excel.CellGet(i, ConstantExcel.Header.列数.貸切回数).Value
                Me.Fields.Header.空車_ETC料金.Value = Excel.CellGet(i, ConstantExcel.Header.列数.空車_ETC料金).Value
                Me.Fields.Header.実車_ETC料金.Value = Excel.CellGet(i, ConstantExcel.Header.列数.実車_ETC料金).Value
                Me.Fields.Header.燃料合計.Value = Excel.CellGet(i, ConstantExcel.Header.列数.燃料合計).Value
                Me.Fields.Header.総勤務時間.Value = Excel.CellGet(i, ConstantExcel.Header.列数.総勤務時間).Text
                Me.Fields.Header.総休憩時間.Value = Excel.CellGet(i, ConstantExcel.Header.列数.総休憩時間).Text
                Me.Fields.Header.総回送時間.Value = Excel.CellGet(i, ConstantExcel.Header.列数.総回送時間).Text
                Me.Fields.Header.総空車停止時間.Value = Excel.CellGet(i, ConstantExcel.Header.列数.総空車停止時間).Text
                Me.Fields.Header.リセット待回数.Value = Excel.CellGet(i, ConstantExcel.Header.列数.リセット待回数).Value
                Me.Fields.Header.出庫_親メーター.Value = Excel.CellGet(i, ConstantExcel.Header.列数.出庫_親メーター).Value
                Me.Fields.Header.入庫_親メーター.Value = Excel.CellGet(i, ConstantExcel.Header.列数.入庫_親メーター).Value
                Me.Fields.Header.空車_最高速度.Value = Excel.CellGet(i, ConstantExcel.Header.列数.空車_最高速度).Value
                Me.Fields.Header.実車_最高速度.Value = Excel.CellGet(i, ConstantExcel.Header.列数.実車_最高速度).Value
                Me.Fields.Header.最高速度.Value = Excel.CellGet(i, ConstantExcel.Header.列数.最高速度).Value
                Me.Fields.Header.データNO.Value = Excel.CellGet(i, ConstantExcel.Header.列数.データNO).Value
                Me.Fields.Header.乗車地.Value = Excel.CellGet(i, ConstantExcel.Header.列数.乗車地).Value
                Me.Fields.Header.乗車日付.Value = Excel.CellGet(i, ConstantExcel.Header.列数.乗車日付).Text
                Me.Fields.Header.乗車時刻.Value = Excel.CellGet(i, ConstantExcel.Header.列数.乗車時刻).Value
                Me.Fields.Header.フィールド112.Value = Excel.CellGet(i, ConstantExcel.Header.列数.フィールド112).Value
                Me.Fields.Header.フィールド113.Value = Excel.CellGet(i, ConstantExcel.Header.列数.フィールド113).Value
                Me.Fields.Header.フィールド114.Value = Excel.CellGet(i, ConstantExcel.Header.列数.フィールド114).Value
                Me.Fields.Header.フィールド115.Value = Excel.CellGet(i, ConstantExcel.Header.列数.フィールド115).Value
                Me.Fields.Header.フィールド116.Value = Excel.CellGet(i, ConstantExcel.Header.列数.フィールド116).Value
                Me.Fields.Header.メーター外.Value = Excel.CellGet(i, ConstantExcel.Header.列数.メーター外).Value
                Me.Fields.Header.元料金.Value = Excel.CellGet(i, ConstantExcel.Header.列数.元料金).Value
                Me.Fields.Header.元現金.Value = Excel.CellGet(i, ConstantExcel.Header.列数.元現金).Value
                Me.Fields.Header.降車地.Value = Excel.CellGet(i, ConstantExcel.Header.列数.降車地).Value
                Me.Fields.Header.降車日付.Value = Excel.CellGet(i, ConstantExcel.Header.列数.降車日付).Text
                Me.Fields.Header.降車時刻.Value = Excel.CellGet(i, ConstantExcel.Header.列数.降車時刻).Value
                Me.Fields.Header.備考.Value = Excel.CellGet(i, ConstantExcel.Header.列数.備考).Value
                Me.Fields.Header.空車距離.Value = Excel.CellGet(i, ConstantExcel.Header.列数.空車距離).Value
                Me.Fields.Header.空車時刻.Value = Excel.CellGet(i, ConstantExcel.Header.列数.空車時刻).Value
                Me.Fields.Header.空車走行時間.Value = Excel.CellGet(i, ConstantExcel.Header.列数.空車走行時間).Value
                Me.Fields.Header.空車最高速度.Value = Excel.CellGet(i, ConstantExcel.Header.列数.空車最高速度).Value
                Me.Fields.Header.迎車距離.Value = Excel.CellGet(i, ConstantExcel.Header.列数.迎車距離).Value
                Me.Fields.Header.支払区分.Value = Excel.CellGet(i, ConstantExcel.Header.列数.支払区分).Value
                Me.Fields.Header.タリフ区分.Value = Excel.CellGet(i, ConstantExcel.Header.列数.タリフ区分).Value
                Me.Fields.Header.料金.Value = Excel.CellGet(i, ConstantExcel.Header.列数.料金).Value
                Me.Fields.Header.実車時刻.Value = Excel.CellGet(i, ConstantExcel.Header.列数.実車時刻).Value
                Me.Fields.Header.実車距離.Value = Excel.CellGet(i, ConstantExcel.Header.列数.実車距離).Value
                Me.Fields.Header.実車最高速度.Value = Excel.CellGet(i, ConstantExcel.Header.列数.実車最高速度).Value
                Me.Fields.Header.空車停止時間.Value = Excel.CellGet(i, ConstantExcel.Header.列数.空車停止時間).Value

                '------------------------------------------------------------------
                ' 桁数チェック
                '------------------------------------------------------------------
                If Len(Me.Fields.Header.取込日付.Value) <> 10 Then
                    Validator.SetMessage(Me.LastError, "取込日付：桁数が正しくありません。10桁で入力してください。(" & i.ToString & "行目)")
                    Excel.CellSetError(i, ConstantExcel.Header.列数.取込日付)
                End If

                'If Len(Me.Fields.Header.乗車日付.Value) <> 10 Then
                '    Validator.SetMessage(Me.LastError, "乗車日付：桁数が正しくありません。10桁で入力してください。(" & i.ToString & "行目)")
                '    Excel.CellSetError(i, ConstantExcel.Header.列数.乗車日付)
                'End If

                'If Len(Me.Fields.Header.降車日付.Value) <> 10 Then
                '    Validator.SetMessage(Me.LastError, "降車日付：桁数が正しくありません。10桁で入力してください。(" & i.ToString & "行目)")
                '    Excel.CellSetError(i, ConstantExcel.Header.列数.降車日付)
                'End If

                If Len(Me.Fields.Header.日報日付.Value) <> 10 Then
                    Validator.SetMessage(Me.LastError, "日報日付：桁数が正しくありません。10桁で入力してください。(" & i.ToString & "行目)")
                    Excel.CellSetError(i, ConstantExcel.Header.列数.日報日付)
                End If

                '------------------------------------------------------------------
                ' 日付
                '------------------------------------------------------------------
                If BaseCore.Common.Text.IsDateNull(Me.Fields.Header.取込日付.Value) Is DBNull.Value Then
                    Validator.SetMessage(Me.LastError, "取込日付：正しくありません。(" & i.ToString & "行目)")
                    Excel.CellSetError(i, ConstantExcel.Header.列数.取込日付)
                End If

                'If BaseCore.Common.Text.IsDateNull(Me.Fields.Header.乗車日付.Value) Is DBNull.Value Then
                '    Validator.SetMessage(Me.LastError, "乗車日付：正しくありません。(" & i.ToString & "行目)")
                '    Excel.CellSetError(i, ConstantExcel.Header.列数.乗車日付)
                'End If

                'If BaseCore.Common.Text.IsDateNull(Me.Fields.Header.降車日付.Value) Is DBNull.Value Then
                '    Validator.SetMessage(Me.LastError, "降車日付：正しくありません。(" & i.ToString & "行目)")
                '    Excel.CellSetError(i, ConstantExcel.Header.列数.降車日付)
                'End If

                If BaseCore.Common.Text.IsDateNull(Me.Fields.Header.日報日付.Value) Is DBNull.Value Then
                    Validator.SetMessage(Me.LastError, "日報日付：正しくありません。(" & i.ToString & "行目)")
                    Excel.CellSetError(i, ConstantExcel.Header.列数.日報日付)
                End If
            Next

            If Me.LastError <> "" Then
                Return False
            End If

            '------------------------------------------------------------------
            ' 返却
            '------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "ｴﾗｰ設定"
        '''========================================================================================
        ''' <SUMMARY>ﾜｰｸﾃｰﾌﾞﾙ(ｽﾄｱﾄﾞ結果)→ﾌﾚｰﾑﾜｰｸ</SUMMARY>   
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Public Function SetError(ByRef Excel As BaseCore.Common.Excel) As Boolean
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetWorkTableAdapters.W乗務記録TableAdapter
            Dim tbl As New NodeDatabase.DataSetWork.W乗務記録DataTable
            Dim qry As New Collection

            '----------------------------------------------------------------------
            ' T納品
            '----------------------------------------------------------------------
            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl.セッションIDColumn.ColumnName, Me.Security.セッションID, BaseDatabase.Contents.Compare.Equal))
            tbl = ada.SelectByCommon(qry)
            For Each row As DataRow In tbl.Rows

                'If row.Item(tbl.Msg取込日付Column.ColumnName) <> "" Then
                '    ' Msg取込日付の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg取込日付Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.取込日付)
                'End If

                'If row.Item(tbl.Msg日報日付Column.ColumnName) <> "" Then
                '    ' Msg日報日付の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg日報日付Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.日報日付)
                'End If

                'If row.Item(tbl.Msg日報年月Column.ColumnName) <> "" Then
                '    ' Msg日報年月の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg日報年月Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.日報年月)
                'End If

                'If row.Item(tbl.Msg出庫時刻Column.ColumnName) <> "" Then
                '    ' Msg出庫時刻の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg出庫時刻Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.出庫時刻)
                'End If

                'If row.Item(tbl.Msg入庫時刻Column.ColumnName) <> "" Then
                '    ' Msg入庫時刻の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg入庫時刻Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.入庫時刻)
                'End If

                'If row.Item(tbl.Msg乗務員CDColumn.ColumnName) <> "" Then
                '    ' Msg乗務員CDの処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg乗務員CDColumn.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.乗務員CD)
                'End If

                'If row.Item(tbl.Msg車両番号Column.ColumnName) <> "" Then
                '    ' Msg車両番号の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg車両番号Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.車両番号)
                'End If

                'If row.Item(tbl.Msg営業所CDColumn.ColumnName) <> "" Then
                '    ' Msg営業所CDの処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg営業所CDColumn.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.営業所CD)
                'End If

                'If row.Item(tbl.Msg部門CDColumn.ColumnName) <> "" Then
                '    ' Msg部門CDの処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg部門CDColumn.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.部門CD)
                'End If

                'If row.Item(tbl.Msg勤務CDColumn.ColumnName) <> "" Then
                '    ' Msg勤務CDの処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg勤務CDColumn.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.勤務CD)
                'End If

                'If row.Item(tbl.Msg車種CDColumn.ColumnName) <> "" Then
                '    ' Msg車種CDの処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg車種CDColumn.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.車種CD)
                'End If

                'If row.Item(tbl.Msg天気Column.ColumnName) <> "" Then
                '    ' Msg天気の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg天気Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.天気)
                'End If

                'If row.Item(tbl.Msg累計全走行Column.ColumnName) <> "" Then
                '    ' Msg累計全走行の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg累計全走行Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.累計全走行)
                'End If

                'If row.Item(tbl.Msg累計実車走行Column.ColumnName) <> "" Then
                '    ' Msg累計実車走行の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg累計実車走行Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.累計実車走行)
                'End If

                'If row.Item(tbl.Msg累計迎車走行Column.ColumnName) <> "" Then
                '    ' Msg累計迎車走行の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg累計迎車走行Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.累計迎車走行)
                'End If

                'If row.Item(tbl.Msg累計営業回数Column.ColumnName) <> "" Then
                '    ' Msg累計営業回数の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg累計営業回数Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.累計営業回数)
                'End If

                'If row.Item(tbl.Msg累計以後回数Column.ColumnName) <> "" Then
                '    ' Msg累計以後回数の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg累計以後回数Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.累計以後回数)
                'End If

                'If row.Item(tbl.Msg累計迎車回数Column.ColumnName) <> "" Then
                '    ' Msg累計迎車回数の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg累計迎車回数Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.累計迎車回数)
                'End If

                'If row.Item(tbl.Msg一日全走行Column.ColumnName) <> "" Then
                '    ' Msg一日全走行の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg一日全走行Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.一日全走行)
                'End If

                'If row.Item(tbl.Msg一日実車走行Column.ColumnName) <> "" Then
                '    ' Msg一日実車走行の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg一日実車走行Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.一日実車走行)
                'End If

                'If row.Item(tbl.Msg一日迎車走行Column.ColumnName) <> "" Then
                '    ' Msg一日迎車走行の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg一日迎車走行Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.一日迎車走行)
                'End If

                'If row.Item(tbl.Msg一日営業回数Column.ColumnName) <> "" Then
                '    ' Msg一日営業回数の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg一日営業回数Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.一日営業回数)
                'End If

                'If row.Item(tbl.Msg一日以後回数Column.ColumnName) <> "" Then
                '    ' Msg一日以後回数の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg一日以後回数Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.一日以後回数)
                'End If

                'If row.Item(tbl.Msg一日迎車回数Column.ColumnName) <> "" Then
                '    ' Msg一日迎車回数の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg一日迎車回数Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.一日迎車回数)
                'End If

                'If row.Item(tbl.Msg累計運賃Column.ColumnName) <> "" Then
                '    ' Msg累計運賃の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg累計運賃Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.累計運賃)
                'End If

                'If row.Item(tbl.Msg累計料金Column.ColumnName) <> "" Then
                '    ' Msg累計料金の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg累計料金Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.累計料金)
                'End If

                'If row.Item(tbl.Msg累計身割回数Column.ColumnName) <> "" Then
                '    ' Msg累計身割回数の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg累計身割回数Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.累計身割回数)
                'End If

                'If row.Item(tbl.Msg累計身割額Column.ColumnName) <> "" Then
                '    ' Msg累計身割額の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg累計身割額Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.累計身割額)
                'End If

                'If row.Item(tbl.Msg累計ワゴン回数Column.ColumnName) <> "" Then
                '    ' Msg累計ワゴン回数の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg累計ワゴン回数Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.累計ワゴン回数)
                'End If

                'If row.Item(tbl.Msg累計早朝回数Column.ColumnName) <> "" Then
                '    ' Msg累計早朝回数の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg累計早朝回数Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.累計早朝回数)
                'End If

                'If row.Item(tbl.Msg累計予約回数Column.ColumnName) <> "" Then
                '    ' Msg累計予約回数の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg累計予約回数Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.累計予約回数)
                'End If

                'If row.Item(tbl.Msg累計遠割回数Column.ColumnName) <> "" Then
                '    ' Msg累計遠割回数の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg累計遠割回数Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.累計遠割回数)
                'End If

                'If row.Item(tbl.Msg累計遠割額Column.ColumnName) <> "" Then
                '    ' Msg累計遠割額の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg累計遠割額Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.累計遠割額)
                'End If

                'If row.Item(tbl.Msg累計貸切指数Column.ColumnName) <> "" Then
                '    ' Msg累計貸切指数の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg累計貸切指数Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.累計貸切指数)
                'End If

                'If row.Item(tbl.Msg累計貸切割引額Column.ColumnName) <> "" Then
                '    ' Msg累計貸切割引額の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg累計貸切割引額Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.累計貸切割引額)
                'End If

                'If row.Item(tbl.Msg累計貸切料金Column.ColumnName) <> "" Then
                '    ' Msg累計貸切料金の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg累計貸切料金Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.累計貸切料金)
                'End If

                'If row.Item(tbl.Msg累計貸切時間Column.ColumnName) <> "" Then
                '    ' Msg累計貸切時間の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg累計貸切時間Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.累計貸切時間)
                'End If

                'If row.Item(tbl.Msg累計貸切走行Column.ColumnName) <> "" Then
                '    ' Msg累計貸切走行の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg累計貸切走行Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.累計貸切走行)
                'End If

                'If row.Item(tbl.Msg累計固定料金キャンセル回数Column.ColumnName) <> "" Then
                '    ' Msg累計固定料金キャンセル回数の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg累計固定料金キャンセル回数Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.累計固定料金キャンセル回数)
                'End If

                'If row.Item(tbl.Msg累計迎車キャンセル回数Column.ColumnName) <> "" Then
                '    ' Msg累計迎車キャンセル回数の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg累計迎車キャンセル回数Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.累計迎車キャンセル回数)
                'End If

                'If row.Item(tbl.Msg累計待回数Column.ColumnName) <> "" Then
                '    ' Msg累計待回数の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg累計待回数Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.累計待回数)
                'End If

                'If row.Item(tbl.Msg累計待加算回数Column.ColumnName) <> "" Then
                '    ' Msg累計待加算回数の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg累計待加算回数Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.累計待加算回数)
                'End If

                'If row.Item(tbl.Msg累計早朝キャンセル回数Column.ColumnName) <> "" Then
                '    ' Msg累計早朝キャンセル回数の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg累計早朝キャンセル回数Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.累計早朝キャンセル回数)
                'End If

                'If row.Item(tbl.Msg累計高齢割回数Column.ColumnName) <> "" Then
                '    ' Msg累計高齢割回数の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg累計高齢割回数Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.累計高齢割回数)
                'End If

                'If row.Item(tbl.Msg累計高齢割額Column.ColumnName) <> "" Then
                '    ' Msg累計高齢割額の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg累計高齢割額Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.累計高齢割額)
                'End If

                'If row.Item(tbl.Msg累計幼児割回数Column.ColumnName) <> "" Then
                '    ' Msg累計幼児割回数の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg累計幼児割回数Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.累計幼児割回数)
                'End If

                'If row.Item(tbl.Msg累計幼児割額Column.ColumnName) <> "" Then
                '    ' Msg累計幼児割額の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg累計幼児割額Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.累計幼児割額)
                'End If

                'If row.Item(tbl.Msg累計リセット回数Column.ColumnName) <> "" Then
                '    ' Msg累計リセット回数の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg累計リセット回数Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.累計リセット回数)
                'End If

                'If row.Item(tbl.Msg累計待回数PColumn.ColumnName) <> "" Then
                '    ' Msg累計待回数Pの処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg累計待回数PColumn.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.累計待回数P)
                'End If

                'If row.Item(tbl.Msg一日運賃Column.ColumnName) <> "" Then
                '    ' Msg一日運賃の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg一日運賃Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.一日運賃)
                'End If

                'If row.Item(tbl.Msg一日料金Column.ColumnName) <> "" Then
                '    ' Msg一日料金の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg一日料金Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.一日料金)
                'End If

                'If row.Item(tbl.Msg一日身割回数Column.ColumnName) <> "" Then
                '    ' Msg一日身割回数の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg一日身割回数Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.一日身割回数)
                'End If

                'If row.Item(tbl.Msg一日身割額Column.ColumnName) <> "" Then
                '    ' Msg一日身割額の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg一日身割額Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.一日身割額)
                'End If

                'If row.Item(tbl.Msg一日身割現収Column.ColumnName) <> "" Then
                '    ' Msg一日身割現収の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg一日身割現収Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.一日身割現収)
                'End If

                'If row.Item(tbl.Msg一日ワゴン回数Column.ColumnName) <> "" Then
                '    ' Msg一日ワゴン回数の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg一日ワゴン回数Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.一日ワゴン回数)
                'End If

                'If row.Item(tbl.Msg一日早朝回数Column.ColumnName) <> "" Then
                '    ' Msg一日早朝回数の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg一日早朝回数Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.一日早朝回数)
                'End If

                'If row.Item(tbl.Msg一日予約回数Column.ColumnName) <> "" Then
                '    ' Msg一日予約回数の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg一日予約回数Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.一日予約回数)
                'End If

                'If row.Item(tbl.Msg一日遠割回数Column.ColumnName) <> "" Then
                '    ' Msg一日遠割回数の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg一日遠割回数Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.一日遠割回数)
                'End If

                'If row.Item(tbl.Msg一日遠割額Column.ColumnName) <> "" Then
                '    ' Msg一日遠割額の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg一日遠割額Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.一日遠割額)
                'End If

                'If row.Item(tbl.Msg一日貸切指数Column.ColumnName) <> "" Then
                '    ' Msg一日貸切指数の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg一日貸切指数Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.一日貸切指数)
                'End If

                'If row.Item(tbl.Msg一日貸切割引Column.ColumnName) <> "" Then
                '    ' Msg一日貸切割引の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg一日貸切割引Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.一日貸切割引)
                'End If

                'If row.Item(tbl.Msg一日貸切料金Column.ColumnName) <> "" Then
                '    ' Msg一日貸切料金の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg一日貸切料金Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.一日貸切料金)
                'End If

                'If row.Item(tbl.Msg一日貸切時間Column.ColumnName) <> "" Then
                '    ' Msg一日貸切時間の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg一日貸切時間Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.一日貸切時間)
                'End If

                'If row.Item(tbl.Msg一日貸切走行Column.ColumnName) <> "" Then
                '    ' Msg一日貸切走行の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg一日貸切走行Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.一日貸切走行)
                'End If

                'If row.Item(tbl.Msg一日固定料金キャンセル回数Column.ColumnName) <> "" Then
                '    ' Msg一日固定料金キャンセル回数の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg一日固定料金キャンセル回数Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.一日固定料金キャンセル回数)
                'End If

                'If row.Item(tbl.Msg一日迎車キャンセル回数Column.ColumnName) <> "" Then
                '    ' Msg一日迎車キャンセル回数の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg一日迎車キャンセル回数Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.一日迎車キャンセル回数)
                'End If

                'If row.Item(tbl.Msg一日待ち回数Column.ColumnName) <> "" Then
                '    ' Msg一日待ち回数の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg一日待ち回数Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.一日待ち回数)
                'End If

                'If row.Item(tbl.Msg一日待ち加算回数Column.ColumnName) <> "" Then
                '    ' Msg一日待ち加算回数の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg一日待ち加算回数Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.一日待ち加算回数)
                'End If

                'If row.Item(tbl.Msg一日早朝キャンセル回数Column.ColumnName) <> "" Then
                '    ' Msg一日早朝キャンセル回数の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg一日早朝キャンセル回数Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.一日早朝キャンセル回数)
                'End If

                'If row.Item(tbl.Msg一日高齢割回数Column.ColumnName) <> "" Then
                '    ' Msg一日高齢割回数の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg一日高齢割回数Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.一日高齢割回数)
                'End If

                'If row.Item(tbl.Msg一日高齢割額Column.ColumnName) <> "" Then
                '    ' Msg一日高齢割額の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg一日高齢割額Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.一日高齢割額)
                'End If

                'If row.Item(tbl.Msg一日幼児割回数Column.ColumnName) <> "" Then
                '    ' Msg一日幼児割回数の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg一日幼児割回数Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.一日幼児割回数)
                'End If

                'If row.Item(tbl.Msg一日幼児割額Column.ColumnName) <> "" Then
                '    ' Msg一日幼児割額の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg一日幼児割額Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.一日幼児割額)
                'End If

                'If row.Item(tbl.Msg一日リセット回数Column.ColumnName) <> "" Then
                '    ' Msg一日リセット回数の処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg一日リセット回数Column.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.一日リセット回数)
                'End If

                'If row.Item(tbl.Msg一日待回数PColumn.ColumnName) <> "" Then
                '    ' Msg一日待回数Pの処理
                '    Me.LastError &= vbCrLf & row.Item(tbl.Msg一日待回数PColumn.ColumnName)
                '    Excel.CellSetError(row.Item(tbl.位置Column.ColumnName), ConstantExcel.Header.列数.一日待回数P)
                'End If


            Next

            If Me.LastError <> "" Then
                Excel.SheetInsert(ConstantExcel.エラー.SheetName)

                Dim arrError() As String = Me.LastError.Replace(vbCrLf, vbTab).Split(vbTab)
                For i As Integer = 0 To arrError.Length - 1
                    Excel.CellSetValue(i, 0, arrError(i))
                Next

                Return False
            End If

            '------------------------------------------------------------------
            ' 返却
            '------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "ﾜｰｸﾃｰﾌﾞﾙ削除"
        '''========================================================================================
        ''' <SUMMARY>ﾜｰｸﾃーﾌﾞﾙ削除</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Public Function WorkDelete() As Boolean
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim adaHD As New NodeDatabase.DataSetWorkTableAdapters.W乗務記録TableAdapter
            Dim tblHD As New NodeDatabase.DataSetWork.W乗務記録DataTable
            Dim qryHD As New Collection

            '----------------------------------------------------------------------
            ' 事前削除
            '----------------------------------------------------------------------
            qryHD.Clear()
            qryHD.Add(New BaseDatabase.Condition(tblHD.セッションIDColumn.ColumnName, Me.Security.セッションID, BaseDatabase.Contents.Compare.Equal))

            Try
                adaHD.DeleteByCommon(qryHD)
            Catch ex As Exception
                Me.LastError = ex.Message
                Return False
            End Try

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "不等号判断"
        '''========================================================================================
        ''' <SUMMARY>不等号判断</SUMMARY>
        ''' <RETURNS>True:変換する, False:変換しない</RETURNS>
        '''========================================================================================
        Public Function JudgeValue(ByVal FieldValueBase As String, ByVal SymbolCode As String, ByVal FieldValueIf As String) As Boolean
            Select Case SymbolCode
                Case NodeContents.Constant.CodeValue.符号区分.等しい
                    If FieldValueBase = FieldValueIf Then
                        Return True
                    End If
                Case NodeContents.Constant.CodeValue.符号区分.等しくない
                    If FieldValueBase <> FieldValueIf Then
                        Return True
                    End If

                Case NodeContents.Constant.CodeValue.符号区分.大きい
                    If FieldValueBase > FieldValueIf Then
                        Return True
                    End If

                Case NodeContents.Constant.CodeValue.符号区分.小さい
                    If FieldValueBase < FieldValueIf Then
                        Return True
                    End If

                Case NodeContents.Constant.CodeValue.符号区分.以上
                    If FieldValueBase >= FieldValueIf Then
                        Return True
                    End If

                Case NodeContents.Constant.CodeValue.符号区分.以下
                    If FieldValueBase <= FieldValueIf Then
                        Return True
                    End If

                Case Else
                    Return False
            End Select

            '----------------------------------------------------------------------
            ' 変換しない
            '----------------------------------------------------------------------
            Return False
        End Function
#End Region
#End Region

#Region "Valid_Execute1(実行)"
        '''========================================================================================
        ''' <SUMMARY>ﾍｯﾀﾞ(共通)</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Valid_Execute1_Header_Common() As NodeContents.ActionButton.ErrorLevel
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim strMsg As String = ""
            Dim Validator As New BaseCore.Common.Validator

            '------------------------------------------------------------------
            ' ｴﾗｰｸﾘｱ
            '------------------------------------------------------------------
            Me.Fields.Header.ファイル形式.IsError = False
            Me.Fields.Header.レイアウトコード.IsError = False

            '------------------------------------------------------------------
            ' 共通検査
            '------------------------------------------------------------------
            If Not Validator.BaseChecker(Me.Fields.Header.ファイル形式) Then : Me.Fields.Header.ファイル形式.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.レイアウトコード) Then : Me.Fields.Header.レイアウトコード.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If

            '------------------------------------------------------------------
            ' 返却
            '------------------------------------------------------------------
            MyBase.LastError = strMsg
            Select Case True
                Case strMsg = ""
                    Return NodeContents.ActionButton.ErrorLevel.None
                Case Else
                    Return NodeContents.ActionButton.ErrorLevel.Fatal
            End Select
        End Function

        '''========================================================================================
        ''' <SUMMARY>検査(個別)</SUMMARY>
        ''' <RETURNS>True:正常, False:不正</RETURNS>
        '''========================================================================================
        Protected Overrides Function Valid_Execute1_Header_Another() As NodeContents.ActionButton.ErrorLevel
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim strMsg As String = ""
            Dim Validator As New BaseCore.Common.Validator

            '------------------------------------------------------------------
            ' 個別検査
            '------------------------------------------------------------------
            '------------------------------------
            ' ﾌｧｲﾙ形式検査
            '------------------------------------
            Select Case Me.Fields.Header.ファイル形式.Value
                Case NodeContents.Constant.CodeValue.ファイル形式.Excel
                    If Not Me.Fields.Header.ファイル名.Value Like "*.xls" _
                   And Not Me.Fields.Header.ファイル名.Value Like "*.xlsx" Then
                        strMsg &= "選択されたファイル形式と対象のファイル形式が一致しません。"
                    End If
                Case NodeContents.Constant.CodeValue.ファイル形式.Csv
                    If Not Me.Fields.Header.ファイル名.Value Like "*.csv" Then
                        strMsg &= "選択されたファイル形式と対象のファイル形式が一致しません。"
                    End If
                Case Else
            End Select

            '------------------------------------------------------------------
            ' 返却
            '------------------------------------------------------------------
            MyBase.LastError = strMsg
            Select Case True
                Case strMsg = ""
                    Return NodeContents.ActionButton.ErrorLevel.None
                Case Else
                    Return NodeContents.ActionButton.ErrorLevel.Fatal
            End Select
        End Function
#End Region
    End Class
End Namespace
