﻿Namespace Frame.Master
    Partial Public Class CrewRecord
        Inherits NodeCore.Common.Frame

#Region "ﾌｨｰﾙﾄﾞ定義"
        Public Class Header
            ''' ----------------------------------------------------------------------------------------
            ''' <SUMMARY>列項目(基本)</SUMMARY>
            ''' ----------------------------------------------------------------------------------------
            Public グループCD As BaseCore.Common.Field.ItemData
            Public 個社CD As BaseCore.Common.Field.ItemData
            Public 子会社CD As BaseCore.Common.Field.ItemData
            Public 日報日付 As BaseCore.Common.Field.ItemData
            Public 乗務員CD As BaseCore.Common.Field.ItemData
            Public 車両番号 As BaseCore.Common.Field.ItemData
            Public 一日全走行 As BaseCore.Common.Field.ItemData
            Public 一日実車走行 As BaseCore.Common.Field.ItemData
            Public 一日営業回数 As BaseCore.Common.Field.ItemData
            Public 一日以後回数 As BaseCore.Common.Field.ItemData
            Public 一日迎車回数 As BaseCore.Common.Field.ItemData
            Public 一日予約回数 As BaseCore.Common.Field.ItemData
            Public 男 As BaseCore.Common.Field.ItemData
            Public 女 As BaseCore.Common.Field.ItemData
            Public 回数料金 As BaseCore.Common.Field.ItemData
            Public 爾後料金 As BaseCore.Common.Field.ItemData
            Public 迎車料金 As BaseCore.Common.Field.ItemData
            Public 予約料金 As BaseCore.Common.Field.ItemData
            Public 定額差額A As BaseCore.Common.Field.ItemData
            Public 一日遠割額 As BaseCore.Common.Field.ItemData
            Public 料金合計 As BaseCore.Common.Field.ItemData
            Public 出庫時刻 As BaseCore.Common.Field.ItemData
            Public 入庫時刻 As BaseCore.Common.Field.ItemData
            Public 現金 As BaseCore.Common.Field.ItemData
            Public 未収 As BaseCore.Common.Field.ItemData
            Public クーポン四社A As BaseCore.Common.Field.ItemData
            Public クーポン東旅協A As BaseCore.Common.Field.ItemData
            Public 福祉券A As BaseCore.Common.Field.ItemData
            Public カード As BaseCore.Common.Field.ItemData
            Public ID As BaseCore.Common.Field.ItemData
            Public 交通系IC As BaseCore.Common.Field.ItemData
            Public キャブカード As BaseCore.Common.Field.ItemData
            Public プリペイドカード As BaseCore.Common.Field.ItemData
            Public WAON As BaseCore.Common.Field.ItemData
            Public その他 As BaseCore.Common.Field.ItemData
            Public 一日身割額 As BaseCore.Common.Field.ItemData
            Public 空転A As BaseCore.Common.Field.ItemData
            Public 料金内訳合計 As BaseCore.Common.Field.ItemData
            Public 実車_ETC料金 As BaseCore.Common.Field.ItemData
            Public ETC乗務員A As BaseCore.Common.Field.ItemData
            Public ETC会社A As BaseCore.Common.Field.ItemData
            Public ETC合計 As BaseCore.Common.Field.ItemData
            Public 基本料金 As BaseCore.Common.Field.ItemData
            Public 以後料金 As BaseCore.Common.Field.ItemData
            Public 固定料金 As BaseCore.Common.Field.ItemData
            Public 予約単価 As BaseCore.Common.Field.ItemData
            Public 営収 As BaseCore.Common.Field.ItemData
            Public 営収額 As BaseCore.Common.Field.ItemData
            Public TAX As BaseCore.Common.Field.ItemData
            Public TAXHid As BaseCore.Common.Field.ItemData
            Public 乗務記録NO As BaseCore.Common.Field.ItemData
            Public 信販控除除外 As BaseCore.Common.Field.ItemData
            Public 登録ユーザーCD As BaseCore.Common.Field.ItemData
            Public 登録ユーザー名 As BaseCore.Common.Field.ItemData
            Public 登録日時 As BaseCore.Common.Field.ItemData
            Public 更新ユーザーCD As BaseCore.Common.Field.ItemData
            Public 更新ユーザー名 As BaseCore.Common.Field.ItemData
            Public 更新日時 As BaseCore.Common.Field.ItemData
            Public 障割 As BaseCore.Common.Field.ItemData

            Public 現金A As BaseCore.Common.Field.ItemData
            Public チケットA As BaseCore.Common.Field.ItemData
            Public カードA As BaseCore.Common.Field.ItemData
            Public IDA As BaseCore.Common.Field.ItemData
            Public 交通系ICA As BaseCore.Common.Field.ItemData
            Public キャブカードA As BaseCore.Common.Field.ItemData
            Public プリペイドカードA As BaseCore.Common.Field.ItemData
            Public WAONA As BaseCore.Common.Field.ItemData
            Public その他A As BaseCore.Common.Field.ItemData
            Public 障割A As BaseCore.Common.Field.ItemData
            Public 料金内訳合計A As BaseCore.Common.Field.ItemData
            Public 料金内訳合計AHid As BaseCore.Common.Field.ItemData
            Public 料金合計Hid As BaseCore.Common.Field.ItemData

            '''========================================================================================
            ''' <SUMMARY>ｺﾝｽﾄﾗｸﾀ</SUMMARY>
            '''========================================================================================
            Public Sub New(ByVal Config As NodeCore.Common.Config)
                Dim tbl As New NodeDatabase.DataSetView.V0乗務記録HDDataTable

                '----------------------------------------------------------------------
                ' ｲﾝｽﾀﾝｽ(基本) 
                '----------------------------------------------------------------------
                Me.グループCD = New BaseCore.Common.Field.ItemData(tbl.グループCDColumn)
                Me.個社CD = New BaseCore.Common.Field.ItemData(tbl.個社CDColumn)
                Me.子会社CD = New BaseCore.Common.Field.ItemData(tbl.子会社CDColumn, 1)
                Me.日報日付 = New BaseCore.Common.Field.ItemData(tbl.日報日付Column)
                Me.乗務員CD = New BaseCore.Common.Field.ItemData(tbl.乗務員CDColumn)
                Me.車両番号 = New BaseCore.Common.Field.ItemData(tbl.車両番号Column)
                Me.一日全走行 = New BaseCore.Common.Field.ItemData(tbl.一日全走行Column, 6, 0)
                Me.一日実車走行 = New BaseCore.Common.Field.ItemData(tbl.一日実車走行Column, 6, 0)
                Me.一日営業回数 = New BaseCore.Common.Field.ItemData(tbl.一日営業回数Column, 3, 0)
                Me.一日以後回数 = New BaseCore.Common.Field.ItemData(tbl.一日以後回数Column, 4, 0)
                Me.一日迎車回数 = New BaseCore.Common.Field.ItemData(tbl.一日迎車回数Column, 3, 0)
                Me.一日予約回数 = New BaseCore.Common.Field.ItemData(tbl.一日予約回数Column, 3, 0)
                Me.男 = New BaseCore.Common.Field.ItemData(tbl.男Column, 3, 0)
                Me.女 = New BaseCore.Common.Field.ItemData(tbl.女Column, 3, 0)
                Me.基本料金 = New BaseCore.Common.Field.ItemData(tbl.基本料金Column, Config.桁数単価整数, Config.桁数単価小数)
                Me.以後料金 = New BaseCore.Common.Field.ItemData(tbl.以後料金Column, Config.桁数単価整数, Config.桁数単価小数)
                Me.固定料金 = New BaseCore.Common.Field.ItemData(tbl.固定料金Column, Config.桁数単価整数, Config.桁数単価小数)
                Me.予約単価 = New BaseCore.Common.Field.ItemData(tbl.予約料金Column, Config.桁数単価整数, Config.桁数単価小数)
                Me.回数料金 = New BaseCore.Common.Field.ItemData(tbl.営収Column, Config.桁数金額整数, Config.桁数金額小数)
                Me.爾後料金 = New BaseCore.Common.Field.ItemData(tbl.営収Column, Config.桁数金額整数, Config.桁数金額小数)
                Me.迎車料金 = New BaseCore.Common.Field.ItemData(tbl.営収Column, Config.桁数金額整数, Config.桁数金額小数)
                Me.予約料金 = New BaseCore.Common.Field.ItemData(tbl.営収Column, Config.桁数金額整数, Config.桁数金額小数)
                Me.定額差額A = New BaseCore.Common.Field.ItemData(tbl.定額差額AColumn, Config.桁数金額整数, Config.桁数金額小数)
                Me.一日遠割額 = New BaseCore.Common.Field.ItemData(tbl.一日遠割額Column, Config.桁数金額整数, Config.桁数金額小数)
                Me.料金合計 = New BaseCore.Common.Field.ItemData(tbl.営収Column, Config.桁数金額整数, Config.桁数金額小数)
                Me.出庫時刻 = New BaseCore.Common.Field.ItemData(tbl.出庫時刻Column)
                Me.入庫時刻 = New BaseCore.Common.Field.ItemData(tbl.入庫時刻Column)
                Me.現金 = New BaseCore.Common.Field.ItemData(tbl.現金Column, Config.桁数金額整数, Config.桁数金額小数)
                Me.未収 = New BaseCore.Common.Field.ItemData(tbl.未収Column, Config.桁数金額整数, Config.桁数金額小数)
                Me.クーポン四社A = New BaseCore.Common.Field.ItemData(tbl.クーポン四社AColumn, Config.桁数金額整数, Config.桁数金額小数)
                Me.クーポン東旅協A = New BaseCore.Common.Field.ItemData(tbl.クーポン東旅協AColumn, Config.桁数金額整数, Config.桁数金額小数)
                Me.福祉券A = New BaseCore.Common.Field.ItemData(tbl.福祉券AColumn, Config.桁数金額整数, Config.桁数金額小数)
                Me.カード = New BaseCore.Common.Field.ItemData(tbl.カードColumn, Config.桁数金額整数, Config.桁数金額小数)
                Me.ID = New BaseCore.Common.Field.ItemData(tbl.IDColumn, Config.桁数金額整数, Config.桁数金額小数)
                Me.交通系IC = New BaseCore.Common.Field.ItemData(tbl.交通系ICColumn, Config.桁数金額整数, Config.桁数金額小数)
                Me.キャブカード = New BaseCore.Common.Field.ItemData(tbl.キャブカードColumn, Config.桁数金額整数, Config.桁数金額小数)
                Me.プリペイドカード = New BaseCore.Common.Field.ItemData(tbl.プリペイドカードColumn, Config.桁数金額整数, Config.桁数金額小数)
                Me.WAON = New BaseCore.Common.Field.ItemData(tbl.WAONColumn, Config.桁数金額整数, Config.桁数金額小数)
                Me.その他 = New BaseCore.Common.Field.ItemData(tbl.その他Column, Config.桁数金額整数, Config.桁数金額小数)
                Me.障割 = New BaseCore.Common.Field.ItemData(tbl.障割Column, Config.桁数金額整数, Config.桁数金額小数)
                Me.一日身割額 = New BaseCore.Common.Field.ItemData(tbl.一日身割額Column, Config.桁数金額整数, Config.桁数金額小数)
                Me.空転A = New BaseCore.Common.Field.ItemData(tbl.空転AColumn, Config.桁数金額整数, Config.桁数金額小数)
                Me.料金内訳合計 = New BaseCore.Common.Field.ItemData(tbl.営収Column, Config.桁数金額整数, Config.桁数金額小数)
                Me.実車_ETC料金 = New BaseCore.Common.Field.ItemData(tbl.実車_ETC料金Column, Config.桁数金額整数, Config.桁数金額小数)
                Me.ETC乗務員A = New BaseCore.Common.Field.ItemData(tbl.ETC乗務員AColumn, Config.桁数金額整数, Config.桁数金額小数)
                Me.ETC会社A = New BaseCore.Common.Field.ItemData(tbl.ETC会社AColumn, Config.桁数金額整数, Config.桁数金額小数)
                Me.ETC合計 = New BaseCore.Common.Field.ItemData(tbl.営収Column, Config.桁数金額整数, Config.桁数金額小数)
                Me.営収 = New BaseCore.Common.Field.ItemData(tbl.営収Column, Config.桁数金額整数, Config.桁数金額小数)
                Me.営収額 = New BaseCore.Common.Field.ItemData(tbl.営収額Column)
                Me.乗務記録NO = New BaseCore.Common.Field.ItemData(tbl.乗務記録NOColumn, 20)
                Me.信販控除除外 = New BaseCore.Common.Field.ItemData(tbl.信販控除除外Column, Config.桁数金額整数, Config.桁数金額小数)
                Me.登録ユーザーCD = New BaseCore.Common.Field.ItemData(tbl.登録ユーザーCDColumn)
                Me.登録ユーザー名 = New BaseCore.Common.Field.ItemData(tbl.登録ユーザー名Column)
                Me.登録日時 = New BaseCore.Common.Field.ItemData(tbl.登録日時Column)
                Me.更新ユーザーCD = New BaseCore.Common.Field.ItemData(tbl.更新ユーザーCDColumn)
                Me.更新ユーザー名 = New BaseCore.Common.Field.ItemData(tbl.更新ユーザー名Column)
                Me.更新日時 = New BaseCore.Common.Field.ItemData(tbl.更新日時Column)

                Me.現金A = New BaseCore.Common.Field.ItemData(tbl.現金AColumn, Config.桁数金額整数, Config.桁数金額小数)
                Me.チケットA = New BaseCore.Common.Field.ItemData(tbl.チケットAColumn, Config.桁数金額整数, Config.桁数金額小数)
                Me.カードA = New BaseCore.Common.Field.ItemData(tbl.カードAColumn, Config.桁数金額整数, Config.桁数金額小数)
                Me.IDA = New BaseCore.Common.Field.ItemData(tbl.iDAColumn, Config.桁数金額整数, Config.桁数金額小数)
                Me.交通系ICA = New BaseCore.Common.Field.ItemData(tbl.交通系ICAColumn, Config.桁数金額整数, Config.桁数金額小数)
                Me.キャブカードA = New BaseCore.Common.Field.ItemData(tbl.キャブカードAColumn, Config.桁数金額整数, Config.桁数金額小数)
                Me.プリペイドカードA = New BaseCore.Common.Field.ItemData(tbl.プリペイドカードAColumn, Config.桁数金額整数, Config.桁数金額小数)
                Me.WAONA = New BaseCore.Common.Field.ItemData(tbl.WAONAColumn, Config.桁数金額整数, Config.桁数金額小数)
                Me.その他A = New BaseCore.Common.Field.ItemData(tbl.その他AColumn, Config.桁数金額整数, Config.桁数金額小数)
                Me.障割A = New BaseCore.Common.Field.ItemData(tbl.障割AColumn, Config.桁数金額整数, Config.桁数金額小数)
                Me.TAX = New BaseCore.Common.Field.ItemData("TAX", TypeCode.Decimal, 15, Config.桁数金額整数, Config.桁数金額小数)
                Me.TAXHid = New BaseCore.Common.Field.ItemData(tbl.税額Column)
                Me.料金内訳合計A = New BaseCore.Common.Field.ItemData("料金内訳合計A", TypeCode.Decimal, 15, Config.桁数金額整数, Config.桁数金額小数)
                Me.料金内訳合計AHid = New BaseCore.Common.Field.ItemData("料金内訳合計AHid", TypeCode.Decimal, 15, Config.桁数金額整数, Config.桁数金額小数)
                Me.料金合計Hid = New BaseCore.Common.Field.ItemData("料金合計Hid", TypeCode.Decimal, 15, Config.桁数金額整数, Config.桁数金額小数)


            End Sub
        End Class
#End Region

#Region "変数定義"
        Public Class Paramater
            Public Header As Header
        End Class

        '''========================================================================================
        ''' <summary>ﾍｯﾀﾞ</summary>
        '''========================================================================================
        Private _Fields As New Paramater
        Public Property Fields() As Paramater
            Get
                Return _Fields
            End Get

            Set(ByVal value As Paramater)
                _Fields = value
            End Set
        End Property
#End Region

#Region "ｺﾝｽﾄﾗｸﾀ"
        '''========================================================================================
        ''' <SUMMARY>ｺﾝｽﾄﾗｸﾀ</SUMMARY>
        ''' <PARAM name="Security">ｾｷｭﾘﾃｨ ｸﾗｽ</PARAM>
        ''' <PARAM name="Config">ｺﾝﾌｨｸﾞ ｸﾗｽ</PARAM>
        ''' <PARAM name="PagePath">ﾍﾟｰｼﾞﾊﾟｽ</PARAM>
        '''========================================================================================
        Public Sub New(ByVal Security As NodeCore.Common.Security, ByVal Config As NodeCore.Common.Config, ByVal PagePath As String)
            '----------------------------------------------------------------------
            ' ｾｷｭﾘﾃｨ 設定
            '----------------------------------------------------------------------
            MyBase.New(Security, Config, PagePath)

            Me.Fields.Header = New Header(Config)
        End Sub
#End Region

#Region "ﾍｯﾀﾞ"
#Region "Clear"
        '''========================================================================================
        ''' <SUMMARY>ｸﾘｱ</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Clear_Header(Optional ByVal KeyClear As Boolean = False) As Boolean
            '----------------------------------------------------------------------
            ' ｷｰ部ｸﾘｱ
            '----------------------------------------------------------------------
            If KeyClear Then
                Me.Fields.Header.グループCD.Value = Nothing
                Me.Fields.Header.子会社CD.Value = Nothing
                Me.Fields.Header.個社CD.Value = Nothing
                Me.Fields.Header.出庫時刻.Value = Nothing
                Me.Fields.Header.入庫時刻.Value = Nothing
                Me.Fields.Header.日報日付.Value = Nothing
                Me.Fields.Header.乗務員CD.Value = Nothing
                Me.Fields.Header.車両番号.Value = Nothing
            End If

            '----------------------------------------------------------------------
            ' ﾃﾞｰﾀ部ｸﾘｱ
            '----------------------------------------------------------------------
            Me.Fields.Header.一日全走行.Value = Nothing
            Me.Fields.Header.一日実車走行.Value = Nothing
            Me.Fields.Header.一日営業回数.Value = Nothing
            Me.Fields.Header.一日以後回数.Value = Nothing
            Me.Fields.Header.一日迎車回数.Value = Nothing
            Me.Fields.Header.一日予約回数.Value = Nothing
            Me.Fields.Header.男.Value = Nothing
            Me.Fields.Header.女.Value = Nothing
            Me.Fields.Header.回数料金.Value = Nothing
            Me.Fields.Header.爾後料金.Value = Nothing
            Me.Fields.Header.迎車料金.Value = Nothing
            Me.Fields.Header.予約料金.Value = Nothing
            Me.Fields.Header.定額差額A.Value = Nothing
            Me.Fields.Header.一日遠割額.Value = Nothing
            Me.Fields.Header.料金合計.Value = Nothing
            Me.Fields.Header.現金.Value = Nothing
            Me.Fields.Header.未収.Value = Nothing
            Me.Fields.Header.クーポン四社A.Value = Nothing
            Me.Fields.Header.クーポン東旅協A.Value = Nothing
            Me.Fields.Header.福祉券A.Value = Nothing
            Me.Fields.Header.カード.Value = Nothing
            Me.Fields.Header.ID.Value = Nothing
            Me.Fields.Header.交通系IC.Value = Nothing
            Me.Fields.Header.キャブカード.Value = Nothing
            Me.Fields.Header.プリペイドカード.Value = Nothing
            Me.Fields.Header.WAON.Value = Nothing
            Me.Fields.Header.その他.Value = Nothing
            Me.Fields.Header.障割.Value = Nothing
            Me.Fields.Header.一日身割額.Value = Nothing
            Me.Fields.Header.空転A.Value = Nothing
            Me.Fields.Header.料金内訳合計.Value = Nothing
            Me.Fields.Header.実車_ETC料金.Value = Nothing
            Me.Fields.Header.ETC乗務員A.Value = Nothing
            Me.Fields.Header.ETC会社A.Value = Nothing
            Me.Fields.Header.ETC合計.Value = Nothing
            Me.Fields.Header.基本料金.Value = Nothing
            Me.Fields.Header.以後料金.Value = Nothing
            Me.Fields.Header.固定料金.Value = Nothing
            Me.Fields.Header.予約単価.Value = Nothing
            Me.Fields.Header.営収.Value = Nothing
            Me.Fields.Header.TAX.Value = Nothing
            Me.Fields.Header.乗務記録NO.Value = Nothing
            Me.Fields.Header.信販控除除外.Value = Nothing
            Me.Fields.Header.登録ユーザーCD.Value = Nothing
            Me.Fields.Header.登録ユーザー名.Value = Nothing
            Me.Fields.Header.登録日時.Value = Nothing
            Me.Fields.Header.更新ユーザーCD.Value = Nothing
            Me.Fields.Header.更新ユーザー名.Value = Nothing
            Me.Fields.Header.更新日時.Value = Nothing

            Me.Fields.Header.現金A.Value = Nothing
            Me.Fields.Header.カードA.Value = Nothing
            Me.Fields.Header.チケットA.Value = Nothing
            Me.Fields.Header.IDA.Value = Nothing
            Me.Fields.Header.交通系ICA.Value = Nothing
            Me.Fields.Header.キャブカードA.Value = Nothing
            Me.Fields.Header.プリペイドカードA.Value = Nothing
            Me.Fields.Header.WAONA.Value = Nothing
            Me.Fields.Header.その他A.Value = Nothing
            Me.Fields.Header.障割A.Value = Nothing
            Me.Fields.Header.料金内訳合計A.Value = Nothing
            Me.Fields.Header.料金内訳合計AHid.Value = Nothing
            Me.Fields.Header.料金合計Hid.Value = Nothing

            '----------------------------------------------------------------------
            ' ﾃﾞｰﾀ部ｸﾘｱ
            '----------------------------------------------------------------------
            'Me.Read()

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "Read"
        '''========================================================================================
        ''' <SUMMARY>読込</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Read_Header() As Boolean
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetFuncTableAdapters.F0乗務記録HDTableAdapter
            Dim tbl As New NodeDatabase.DataSetFunc.F0乗務記録HDDataTable

            Dim qry As New Collection

            '----------------------------------------------------------------------
            ' 条件設定
            '----------------------------------------------------------------------
            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, Me.Fields.Header.グループCD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, Me.Fields.Header.個社CD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.子会社CDColumn.ColumnName, Me.Fields.Header.子会社CD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.日報日付Column.ColumnName, Me.Fields.Header.日報日付.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.出庫時刻Column.ColumnName, Me.Fields.Header.出庫時刻.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.入庫時刻Column.ColumnName, Me.Fields.Header.入庫時刻.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.乗務員CDColumn.ColumnName, Me.Fields.Header.乗務員CD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.車両番号Column.ColumnName, Me.Fields.Header.車両番号.Value, BaseDatabase.Contents.Compare.Equal))

            '----------------------------------------------------------------------
            ' 読込
            '----------------------------------------------------------------------
            tbl = ada.SelectByCommon(qry)
            If tbl.Count > 0 Then
                Me.Fields.Header.乗務記録NO.Value = tbl(0).Item(tbl.乗務記録NOColumn.ColumnName)
                Me.Fields.Header.一日全走行.Value = Math.Floor(tbl(0).Item(tbl.一日全走行Column.ColumnName) / 1000)
                Me.Fields.Header.一日実車走行.Value = Math.Floor(tbl(0).Item(tbl.一日実車走行Column.ColumnName) / 1000)
                Me.Fields.Header.一日営業回数.Value = tbl(0).Item(tbl.一日営業回数Column.ColumnName)
                Me.Fields.Header.一日以後回数.Value = tbl(0).Item(tbl.一日以後回数Column.ColumnName)
                Me.Fields.Header.一日迎車回数.Value = tbl(0).Item(tbl.一日迎車回数Column.ColumnName)
                Me.Fields.Header.一日予約回数.Value = tbl(0).Item(tbl.一日予約回数Column.ColumnName)
                Me.Fields.Header.男.Value = tbl(0).Item(tbl.男Column.ColumnName)
                Me.Fields.Header.女.Value = tbl(0).Item(tbl.女Column.ColumnName)

                Me.Fields.Header.基本料金.Value = tbl(0).Item(tbl.基本料金Column.ColumnName)    'TODO: 回数単価 OR M_料金.基本料金 ??
                Me.Fields.Header.以後料金.Value = tbl(0).Item(tbl.以後料金Column.ColumnName)    'TODO: 爾後単価 OR M_料金.以後料金 ??
                Me.Fields.Header.固定料金.Value = tbl(0).Item(tbl.固定料金Column.ColumnName)    'TODO: 迎車単価 OR M_料金.固定料金 ??
                Me.Fields.Header.予約単価.Value = tbl(0).Item(tbl.予約単価Column.ColumnName)    'TODO: M_料金.予約料金 ??

                '料金
                Me.Fields.Header.回数料金.Value = BaseCore.Common.Text.CVal(Me.Fields.Header.一日営業回数.Value) * BaseCore.Common.Text.CVal(Me.Fields.Header.基本料金.Value)
                Me.Fields.Header.爾後料金.Value = BaseCore.Common.Text.CVal(Me.Fields.Header.一日以後回数.Value) * BaseCore.Common.Text.CVal(Me.Fields.Header.以後料金.Value)
                Me.Fields.Header.迎車料金.Value = BaseCore.Common.Text.CVal(Me.Fields.Header.一日迎車回数.Value) * BaseCore.Common.Text.CVal(Me.Fields.Header.固定料金.Value)
                Me.Fields.Header.予約料金.Value = BaseCore.Common.Text.CVal(Me.Fields.Header.一日予約回数.Value) * BaseCore.Common.Text.CVal(Me.Fields.Header.予約単価.Value)

                Me.Fields.Header.定額差額A.Value = tbl(0).Item(tbl.定額差額AColumn.ColumnName)
                Me.Fields.Header.一日遠割額.Value = tbl(0).Item(tbl.一日遠割額Column.ColumnName)
                Me.Fields.Header.料金合計.Value = BaseCore.Common.Text.CVal(Me.Fields.Header.回数料金.Value) +
                                                  BaseCore.Common.Text.CVal(Me.Fields.Header.爾後料金.Value) +
                                                  BaseCore.Common.Text.CVal(Me.Fields.Header.迎車料金.Value) +
                                                  BaseCore.Common.Text.CVal(Me.Fields.Header.予約料金.Value) +
                                                  BaseCore.Common.Text.CVal(Me.Fields.Header.定額差額A.Value) +
                                                  BaseCore.Common.Text.CVal(Me.Fields.Header.一日遠割額.Value)
                '売上
                Me.Fields.Header.実車_ETC料金.Value = tbl(0).Item(tbl.実車_ETC料金Column.ColumnName)
                Me.Fields.Header.信販控除除外.Value = tbl(0).Item(tbl.信販控除除外Column.ColumnName)
                Me.Fields.Header.ETC乗務員A.Value = tbl(0).Item(tbl.ETC乗務員AColumn.ColumnName)
                Me.Fields.Header.ETC会社A.Value = tbl(0).Item(tbl.ETC会社AColumn.ColumnName)
                Me.Fields.Header.ETC合計.Value = tbl(0).Item(tbl.ETC合計Column.ColumnName)

                '販売区分
                Me.Fields.Header.現金.Value = NodeCore.Common.Logic.FormatMoney(tbl(0).Item(tbl.現金Column.ColumnName).ToString())
                Me.Fields.Header.未収.Value = NodeCore.Common.Logic.FormatMoney(tbl(0).Item(tbl.未収Column.ColumnName).ToString())
                Me.Fields.Header.クーポン四社A.Value = tbl(0).Item(tbl.クーポン四社AColumn.ColumnName)
                Me.Fields.Header.クーポン東旅協A.Value = tbl(0).Item(tbl.クーポン東旅協AColumn.ColumnName)
                Me.Fields.Header.福祉券A.Value = tbl(0).Item(tbl.福祉券AColumn.ColumnName)
                Me.Fields.Header.カード.Value = NodeCore.Common.Logic.FormatMoney(tbl(0).Item(tbl.カードColumn.ColumnName).ToString())
                Me.Fields.Header.ID.Value = NodeCore.Common.Logic.FormatMoney(tbl(0).Item(tbl.IDColumn.ColumnName).ToString())
                Me.Fields.Header.交通系IC.Value = NodeCore.Common.Logic.FormatMoney(tbl(0).Item(tbl.交通系ICColumn.ColumnName).ToString())
                Me.Fields.Header.キャブカード.Value = NodeCore.Common.Logic.FormatMoney(tbl(0).Item(tbl.キャブカードColumn.ColumnName).ToString())
                Me.Fields.Header.プリペイドカード.Value = NodeCore.Common.Logic.FormatMoney(tbl(0).Item(tbl.プリペイドカードColumn.ColumnName).ToString())
                Me.Fields.Header.WAON.Value = NodeCore.Common.Logic.FormatMoney(tbl(0).Item(tbl.WAONColumn.ColumnName).ToString())
                Me.Fields.Header.その他.Value = NodeCore.Common.Logic.FormatMoney(tbl(0).Item(tbl.その他Column.ColumnName).ToString())
                Me.Fields.Header.障割.Value = NodeCore.Common.Logic.FormatMoney(tbl(0).Item(tbl.障割Column.ColumnName).ToString())
                Me.Fields.Header.一日身割額.Value = NodeCore.Common.Logic.FormatMoney(tbl(0).Item(tbl.一日身割額Column.ColumnName).ToString())
                Me.Fields.Header.空転A.Value = tbl(0).Item(tbl.空転AColumn.ColumnName)
                Me.Fields.Header.営収.Value = Me.Fields.Header.営収額.Value
                'Me.Fields.Header.営収.Value = BaseCore.Common.Text.CVal(Me.Fields.Header.現金.Value) +
                '                                BaseCore.Common.Text.CVal(Me.Fields.Header.未収.Value) +
                '                                BaseCore.Common.Text.CVal(Me.Fields.Header.カード.Value) +
                '                                BaseCore.Common.Text.CVal(Me.Fields.Header.ID.Value) +
                '                                BaseCore.Common.Text.CVal(Me.Fields.Header.交通系IC.Value) +
                '                                BaseCore.Common.Text.CVal(Me.Fields.Header.キャブカード.Value) +
                '                                BaseCore.Common.Text.CVal(Me.Fields.Header.プリペイドカード.Value) +
                '                                BaseCore.Common.Text.CVal(Me.Fields.Header.WAON.Value) +
                '                                BaseCore.Common.Text.CVal(Me.Fields.Header.その他.Value) +
                '                                BaseCore.Common.Text.CVal(Me.Fields.Header.障割.Value)

                Me.Fields.Header.料金内訳合計.Value = NodeCore.Common.Logic.FormatMoney((BaseCore.Common.Text.CVal(Me.Fields.Header.現金.Value) +
                                                      BaseCore.Common.Text.CVal(Me.Fields.Header.未収.Value) +
                                                      BaseCore.Common.Text.CVal(Me.Fields.Header.カード.Value) +
                                                      BaseCore.Common.Text.CVal(Me.Fields.Header.ID.Value) +
                                                      BaseCore.Common.Text.CVal(Me.Fields.Header.交通系IC.Value) +
                                                      BaseCore.Common.Text.CVal(Me.Fields.Header.キャブカード.Value) +
                                                      BaseCore.Common.Text.CVal(Me.Fields.Header.プリペイドカード.Value) +
                                                      BaseCore.Common.Text.CVal(Me.Fields.Header.WAON.Value) +
                                                      BaseCore.Common.Text.CVal(Me.Fields.Header.その他.Value) +
                                                      BaseCore.Common.Text.CVal(Me.Fields.Header.障割.Value)).ToString())


                'Me.Fields.Header.料金内訳合計.Value = NodeCore.Common.Logic.FormatMoney((BaseCore.Common.Text.CVal(Me.Fields.Header.回数料金.Value) +
                '                                  BaseCore.Common.Text.CVal(Me.Fields.Header.爾後料金.Value) +
                '                                  BaseCore.Common.Text.CVal(Me.Fields.Header.迎車料金.Value) +
                '                                  BaseCore.Common.Text.CVal(Me.Fields.Header.予約料金.Value) +
                '                                  BaseCore.Common.Text.CVal(Me.Fields.Header.定額差額A.Value) +
                '                                  BaseCore.Common.Text.CVal(Me.Fields.Header.一日遠割額.Value)).ToString())

                'BaseCore.Common.Text.CVal(Me.Fields.Header.一日身割額A.Value)

                '更新履歴
                Me.Fields.Header.登録ユーザーCD.Value = tbl(0).Item(tbl.登録ユーザーCDColumn.ColumnName)
                Me.Fields.Header.登録ユーザー名.Value = tbl(0).Item(tbl.登録ユーザー名Column.ColumnName)
                Me.Fields.Header.登録日時.Value = tbl(0).Item(tbl.登録日時Column.ColumnName)
                Me.Fields.Header.更新ユーザーCD.Value = tbl(0).Item(tbl.更新ユーザーCDColumn.ColumnName)
                Me.Fields.Header.更新ユーザー名.Value = tbl(0).Item(tbl.更新ユーザー名Column.ColumnName)
                Me.Fields.Header.更新日時.Value = tbl(0).Item(tbl.更新日時Column.ColumnName)
                '取込データ（参照のみ）
                Me.Fields.Header.現金A.Value = NodeCore.Common.Logic.NumberFormatCutFloat(tbl(0).Item(tbl.現金AColumn.ColumnName).ToString())
                Me.Fields.Header.チケットA.Value = tbl(0).Item(tbl.チケットAColumn.ColumnName)
                Me.Fields.Header.カードA.Value = tbl(0).Item(tbl.カードAColumn.ColumnName)
                Me.Fields.Header.IDA.Value = tbl(0).Item(tbl.iDAColumn.ColumnName)
                Me.Fields.Header.交通系ICA.Value = tbl(0).Item(tbl.交通系ICAColumn.ColumnName)
                Me.Fields.Header.キャブカードA.Value = tbl(0).Item(tbl.キャブカードAColumn.ColumnName)
                Me.Fields.Header.プリペイドカードA.Value = tbl(0).Item(tbl.プリペイドカードAColumn.ColumnName)
                Me.Fields.Header.WAONA.Value = tbl(0).Item(tbl.WAONAColumn.ColumnName)
                Me.Fields.Header.その他A.Value = tbl(0).Item(tbl.その他AColumn.ColumnName)
                Me.Fields.Header.障割A.Value = tbl(0).Item(tbl.障割AColumn.ColumnName)
                Me.Fields.Header.TAX.Value = tbl(0).Item(tbl.営収Column.ColumnName)

                Me.Fields.Header.料金内訳合計A.Value = BaseCore.Common.Text.CVal(Me.Fields.Header.現金A.Value) +
                                                      BaseCore.Common.Text.CVal(Me.Fields.Header.チケットA.Value) +
                                                      BaseCore.Common.Text.CVal(Me.Fields.Header.カードA.Value) +
                                                      BaseCore.Common.Text.CVal(Me.Fields.Header.IDA.Value) +
                                                      BaseCore.Common.Text.CVal(Me.Fields.Header.交通系ICA.Value) +
                                                      BaseCore.Common.Text.CVal(Me.Fields.Header.キャブカードA.Value) +
                                                      BaseCore.Common.Text.CVal(Me.Fields.Header.プリペイドカードA.Value) +
                                                      BaseCore.Common.Text.CVal(Me.Fields.Header.WAONA.Value) +
                                                      BaseCore.Common.Text.CVal(Me.Fields.Header.その他A.Value) +
                                                      BaseCore.Common.Text.CVal(Me.Fields.Header.クーポン四社A.Value) +
                                                      BaseCore.Common.Text.CVal(Me.Fields.Header.クーポン東旅協A.Value) +
                                                      BaseCore.Common.Text.CVal(Me.Fields.Header.福祉券A.Value) +
                                                      BaseCore.Common.Text.CVal(Me.Fields.Header.空転A.Value)
            Else
                Clear(False)
            End If

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "Write"
        '''========================================================================================
        ''' <SUMMARY>書込</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Write_Header() As Boolean
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim strNow As String = Format(Now, NodeContents.Constant.System.DATE_TIME_FORMAT)

            Dim ada As New NodeDatabase.DataSetTranTableAdapters.T乗務記録HDTableAdapter
            Dim tbl As New NodeDatabase.DataSetTran.T乗務記録HDDataTable
            Dim qry As New Collection

            Dim ada乗務員 As New NodeDatabase.DataSetViewTableAdapters.V0乗務員TableAdapter
            Dim tbl乗務員 As New NodeDatabase.DataSetView.V0乗務員DataTable
            Dim qry乗務員 As New Collection

            '----------------------------------------------------------------------
            ' 条件設定
            '----------------------------------------------------------------------
            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, Me.Fields.Header.グループCD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, Me.Fields.Header.個社CD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.子会社CDColumn.ColumnName, Me.Fields.Header.子会社CD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.日報日付Column.ColumnName, Me.Fields.Header.日報日付.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.出庫時刻Column.ColumnName, Me.Fields.Header.出庫時刻.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.入庫時刻Column.ColumnName, Me.Fields.Header.入庫時刻.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.乗務員CDColumn.ColumnName, Me.Fields.Header.乗務員CD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.車両番号Column.ColumnName, Me.Fields.Header.車両番号.Value, BaseDatabase.Contents.Compare.Equal))

            qry乗務員.Clear()
            qry乗務員.Add(New BaseDatabase.Condition(tbl乗務員.グループCDColumn.ColumnName, Me.Fields.Header.グループCD.Value, BaseDatabase.Contents.Compare.Equal))
            qry乗務員.Add(New BaseDatabase.Condition(tbl乗務員.個社CDColumn.ColumnName, Me.Fields.Header.個社CD.Value, BaseDatabase.Contents.Compare.Equal))
            qry乗務員.Add(New BaseDatabase.Condition(tbl乗務員.子会社CDColumn.ColumnName, Me.Fields.Header.子会社CD.Value, BaseDatabase.Contents.Compare.Equal))
            qry乗務員.Add(New BaseDatabase.Condition(tbl乗務員.乗務員CDColumn.ColumnName, Me.Fields.Header.乗務員CD.Value, BaseDatabase.Contents.Compare.Equal))

            '----------------------------------------------------------------------
            ' 読込
            '----------------------------------------------------------------------
            tbl = ada.SelectByCommon(qry)

            tbl乗務員 = ada乗務員.SelectByCommon(qry乗務員)

            '----------------------------------------------------------------------
            ' 事前準備
            '----------------------------------------------------------------------
            Me.Fields.Header.更新日時.Value = strNow
            Me.Fields.Header.更新ユーザーCD.Value = Me.Security.ユーザーCD
            Me.Fields.Header.更新ユーザー名.Value = Me.Security.ユーザー名


            '----------------------------------------------------------------------
            ' 保存
            '----------------------------------------------------------------------
            If tbl.Count = 0 Then
                '----------------------------------------------------------------------
                ' 新規
                '----------------------------------------------------------------------
                Dim 総勤務時間 As String = Get_KadouJikan(Me.Fields.Header.出庫時刻.Value.ToString, Me.Fields.Header.入庫時刻.Value.ToString)
                Dim 出庫日時 As DateTime = DateTime.Parse(Me.Fields.Header.日報日付.Value.ToString & " " & Me.Fields.Header.出庫時刻.Value.ToString)
                Dim 日報年月 As String = 出庫日時.Year.ToString & 出庫日時.Month.ToString.PadLeft(2, "0")
                Dim 乗務記録NO As String = "C" & 出庫日時.Year.ToString & 出庫日時.Month.ToString.PadLeft(2, "0") & 出庫日時.Day.ToString.PadLeft(2, "0") _
                                         & 出庫日時.Hour.ToString.PadLeft(2, "0") & 出庫日時.Minute.ToString.PadLeft(2, "0") _
                                         & Me.Fields.Header.車両番号.Value.ToString

                If (Me.Fields.Header.乗務記録NO.Value <> "") Then
                    乗務記録NO = "C" & Me.Fields.Header.乗務記録NO.Value
                End If

                Dim 乗務員名 As String = tbl乗務員(0).Item(tbl乗務員.乗務員名Column.ColumnName)
                Dim 勤務区分 As String = tbl乗務員(0).Item(tbl乗務員.勤務区分Column.ColumnName)
                Dim 総休憩時間 As String = ""

                Me.Fields.Header.登録日時.Value = strNow
                Me.Fields.Header.登録ユーザーCD.Value = Me.Security.ユーザーCD
                Me.Fields.Header.登録ユーザー名.Value = Me.Security.ユーザー名

                Try
                    ada.Insert(Me.Security.グループCD _
                             , Me.Security.個社CD _
                             , Me.Security.子会社CD _
                             , Me.Fields.Header.日報日付.Value _
                             , 日報年月 _
                             , Me.Fields.Header.出庫時刻.Value _
                             , Me.Fields.Header.入庫時刻.Value _
                             , Me.Fields.Header.乗務員CD.Value _
                             , Me.Fields.Header.車両番号.Value _
                             , 乗務記録NO _
                             , strNow _ '取込日付
                             , "1" _   '営業所CD
                             , "0" _   '部門CD
                             , "1" _   '勤務CD
                             , "1" _
                             , "晴" _ '天気
                             , 0 _ ' 累計全走行.Value _
                             , 0 _ '累計実車走行
                             , 0 _ '累計迎車走行
                             , 0 _ '累計営業回数
                             , 0 _ '累計以後回数
                             , 0 _ '累計迎車回数
                             , Val(Me.Fields.Header.一日全走行.Value * 1000) _
                             , Val(Me.Fields.Header.一日実車走行.Value * 1000) _
                             , 0 _ '一日迎車走行
                             , Me.Fields.Header.一日営業回数.Value _
                             , Me.Fields.Header.一日以後回数.Value _
                             , Me.Fields.Header.一日迎車回数.Value _
                             , 0 _ '累計運賃
                             , 0 _ '累計料金
                             , 0 _ '累計身割回数
                             , 0 _ '累計身割額
                             , 0 _ '累計ワゴン回数
                             , 0 _ '累計早朝回数
                             , 0 _ '累計予約回数
                             , 0 _ '累計遠割回数
                             , 0 _ '累計遠割額
                             , 0 _ '累計貸切指数
                             , 0 _ '累計貸切割引額
                             , 0 _ '累計貸切料金
                             , 0 _ '累計貸切時間
                             , 0 _ '累計貸切走行
                             , 0 _ '累計固定料金キャンセル回数
                             , 0 _ '累計迎車キャンセル回数
                             , 0 _ '累計待回数
                             , 0 _ '累計待加算回数
                             , 0 _ '累計早朝キャンセル回数
                             , 0 _ '累計高齢割回数
                             , 0 _ '累計高齢割額
                             , 0 _ '累計幼児割回数
                             , 0 _ '累計幼児割額
                             , 0 _ '累計リセット回数
                             , 0 _ '累計待回数P
                             , 0 _ '一日運賃
                             , 0 _ '一日料金
                             , 0 _ '一日身割回数
                             , 0 _ 'Me.Fields.Header.一日身割額.Value _
                             , 0 _ '一日身割現収
                             , 0 _ '一日ワゴン回数
                             , 0 _ '一日早朝回数
                             , Me.Fields.Header.一日予約回数.Value _
                             , 0 _ '一日遠割回数
                             , Me.Fields.Header.一日遠割額.Value _
                             , 0 _ '一日貸切指数
                             , 0 _ '一日貸切割引
                             , 0 _ '一日貸切料金
                             , 0 _ '一日貸切時間
                             , 0 _ '一日貸切走行
                             , 0 _ '一日固定料金キャンセル回数
                             , 0 _ '一日迎車キャンセル回数
                             , 0 _ '一日待ち回数
                             , 0 _ '一日待ち加算回数
                             , 0 _ '一日早朝キャンセル回数
                             , 0 _ '一日高齢割回数
                             , 0 _ '一日高齢割額
                             , 0 _ '一日幼児割回数
                             , 0 _ '一日幼児割額
                             , 0 _ '一日リセット回数
                             , 0 _ '一日待ち回数P
                             , Me.Fields.Header.基本料金.Value _
                             , Me.Fields.Header.以後料金.Value _
                             , Me.Fields.Header.固定料金.Value _
                             , 0 _ '月間営収
                             , BaseCore.Common.Text.CVal(Me.Fields.Header.料金合計Hid.Value) _ '営収
                             , Me.Fields.Header.男.Value _
                             , Me.Fields.Header.女.Value _
                             , 0 _ '現金
                             , 0 _ '未収
                             , 0 _ 'クレジット
                             , 0 _ 'Me.Fields.Header.カード.Value _
                             , 0 _ 'Me.Fields.Header.ID.Value _
                             , 0 _ 'Me.Fields.Header.交通系IC.Value _
                             , 0 _ 'Me.Fields.Header.キャブカード.Value _
                             , 0 _ 'Me.Fields.Header.プリペイドカード.Value _
                             , 0 _ 'Me.Fields.Header.WAON.Value _
                             , 0 _ 'メーター外料金
                             , 0 _ 'クレジット回数
                             , 0 _ '貸切回数
                             , Me.Fields.Header.ETC会社A.Value + Me.Fields.Header.ETC乗務員A.Value _
                             , Me.Fields.Header.実車_ETC料金.Value _
                             , 0 _ '燃料合計
                             , 総勤務時間 _
                             , 総休憩時間 _
                             , 0 _ '総回送時間
                             , 0 _ '総空車停止時間
                             , 0 _ 'リセット待回数
                             , 0 _ '出庫_親メーター
                             , 0 _ '入庫_親メーター
                             , 0 _ '空車_最高速度
                             , 0 _ '実車_最高速度
                             , 0 _ '最高速度
                             , 0 _ '削除
                             , Me.Fields.Header.定額差額A.Value _
                             , Me.Fields.Header.チケットA.Value _
                             , Me.Fields.Header.クーポン四社A.Value _
                             , Me.Fields.Header.クーポン東旅協A.Value _
                             , Me.Fields.Header.福祉券A.Value _
                             , Me.Fields.Header.カードA.Value _ 'カードA
                             , Me.Fields.Header.IDA.Value _ 'IDA
                             , Me.Fields.Header.交通系ICA.Value _ '交通系ICA
                             , Me.Fields.Header.キャブカードA.Value _ 'キャブカードA
                             , Me.Fields.Header.プリペイドカードA.Value _ 'プリペイドカードA
                             , Me.Fields.Header.WAONA.Value _ 'WAONA
                             , Me.Fields.Header.その他A.Value _
                             , Me.Fields.Header.障割A.Value _ '障割A
                             , Me.Fields.Header.現金A.Value _ '現金A
                             , Me.Fields.Header.実車_ETC料金.Value _ 'ETC実車A
                             , Me.Fields.Header.ETC乗務員A.Value _
                             , Me.Fields.Header.ETC会社A.Value _
                             , Me.Fields.Header.登録ユーザーCD.Value _
                             , Me.Fields.Header.登録ユーザー名.Value _
                             , Me.Fields.Header.登録日時.Value _
                             , Me.Fields.Header.更新ユーザーCD.Value _
                             , Me.Fields.Header.更新ユーザー名.Value _
                             , Me.Fields.Header.更新日時.Value _
                             , Me.Fields.Header.空転A.Value _
                             , Me.Fields.Header.信販控除除外.Value _
                             , BaseCore.Common.Text.CVal(Me.Fields.Header.その他.Value) _
                             , BaseCore.Common.Text.CVal(Me.Fields.Header.障割.Value) _
                             , BaseCore.Common.Text.CVal(Me.Fields.Header.TAXHid.Value) _
                             , BaseCore.Common.Text.CVal(Me.Fields.Header.料金合計Hid.Value) - BaseCore.Common.Text.CVal(Me.Fields.Header.TAXHid.Value) _
                             , BaseCore.Common.Text.CVal(Me.Fields.Header.予約単価.Value)
                            )


                Catch ex As Exception
                    MyBase.LastError = "以下のエラーのため、追加できませんでした。" & vbCrLf & ex.Message
                    Return False
                End Try
            Else
                '----------------------------------------------------------------------
                ' 訂正
                '----------------------------------------------------------------------
                Try
                    tbl(0).Item(tbl.乗務記録NOColumn.ColumnName) = Me.Fields.Header.乗務記録NO.Value
                    tbl(0).Item(tbl.一日全走行Column.ColumnName) = Val(Me.Fields.Header.一日全走行.Value) * 1000
                    tbl(0).Item(tbl.一日実車走行Column.ColumnName) = Val(Me.Fields.Header.一日実車走行.Value) * 1000
                    tbl(0).Item(tbl.一日営業回数Column.ColumnName) = Me.Fields.Header.一日営業回数.Value
                    tbl(0).Item(tbl.一日以後回数Column.ColumnName) = Me.Fields.Header.一日以後回数.Value
                    tbl(0).Item(tbl.一日迎車回数Column.ColumnName) = Me.Fields.Header.一日迎車回数.Value
                    tbl(0).Item(tbl.一日予約回数Column.ColumnName) = Me.Fields.Header.一日予約回数.Value

                    tbl(0).Item(tbl.基本料金Column.ColumnName) = Me.Fields.Header.基本料金.Value
                    tbl(0).Item(tbl.以後料金Column.ColumnName) = Me.Fields.Header.以後料金.Value
                    tbl(0).Item(tbl.固定料金Column.ColumnName) = Me.Fields.Header.固定料金.Value
                    tbl(0).Item(tbl.予約料金Column.ColumnName) = Me.Fields.Header.予約単価.Value

                    tbl(0).Item(tbl.男Column.ColumnName) = Me.Fields.Header.男.Value
                    tbl(0).Item(tbl.女Column.ColumnName) = Me.Fields.Header.女.Value

                    tbl(0).Item(tbl.定額差額AColumn.ColumnName) = Me.Fields.Header.定額差額A.Value
                    tbl(0).Item(tbl.一日遠割額Column.ColumnName) = Me.Fields.Header.一日遠割額.Value
                    tbl(0).Item(tbl.実車_ETC料金Column.ColumnName) = Me.Fields.Header.実車_ETC料金.Value 'ETC実車A
                    tbl(0).Item(tbl.ETC乗務員AColumn.ColumnName) = Me.Fields.Header.ETC乗務員A.Value
                    tbl(0).Item(tbl.ETC会社AColumn.ColumnName) = Me.Fields.Header.ETC会社A.Value
                    tbl(0).Item(tbl.信販控除除外Column.ColumnName) = Me.Fields.Header.信販控除除外.Value
                    ' 売上区分
                    tbl(0).Item(tbl.現金AColumn.ColumnName) = Me.Fields.Header.現金A.Value
                    tbl(0).Item(tbl.チケットAColumn.ColumnName) = Me.Fields.Header.チケットA.Value
                    tbl(0).Item(tbl.クーポン四社AColumn.ColumnName) = Me.Fields.Header.クーポン四社A.Value
                    tbl(0).Item(tbl.クーポン東旅協AColumn.ColumnName) = Me.Fields.Header.クーポン東旅協A.Value
                    tbl(0).Item(tbl.福祉券AColumn.ColumnName) = Me.Fields.Header.福祉券A.Value
                    tbl(0).Item(tbl.カードAColumn.ColumnName) = Me.Fields.Header.カードA.Value
                    tbl(0).Item(tbl.iDAColumn.ColumnName) = Me.Fields.Header.IDA.Value
                    tbl(0).Item(tbl.交通系ICAColumn.ColumnName) = Me.Fields.Header.交通系ICA.Value
                    tbl(0).Item(tbl.キャブカードAColumn.ColumnName) = Me.Fields.Header.キャブカードA.Value
                    tbl(0).Item(tbl.プリペイドカードAColumn.ColumnName) = Me.Fields.Header.プリペイドカードA.Value
                    tbl(0).Item(tbl.WAONAColumn.ColumnName) = Me.Fields.Header.WAONA.Value
                    tbl(0).Item(tbl.その他AColumn.ColumnName) = Me.Fields.Header.その他A.Value
                    tbl(0).Item(tbl.障割AColumn.ColumnName) = Me.Fields.Header.障割A.Value '障割
                    tbl(0).Item(tbl.空転AColumn.ColumnName) = Me.Fields.Header.空転A.Value
                    tbl(0).Item(tbl.その他Column.ColumnName) = Me.Fields.Header.その他.Value
                    tbl(0).Item(tbl.障割Column.ColumnName) = Me.Fields.Header.障割.Value '障割
                    tbl(0).Item(tbl.営収Column.ColumnName) = Me.Fields.Header.料金合計Hid.Value
                    tbl(0).Item(tbl.税額Column.ColumnName) = Me.Fields.Header.TAXHid.Value
                    tbl(0).Item(tbl.営収額Column.ColumnName) = Me.Fields.Header.営収額.Value

                    tbl(0).Item(tbl.更新ユーザーCDColumn.ColumnName) = Me.Fields.Header.更新ユーザーCD.Value
                    tbl(0).Item(tbl.更新ユーザー名Column.ColumnName) = Me.Fields.Header.更新ユーザー名.Value
                    tbl(0).Item(tbl.更新日時Column.ColumnName) = Me.Fields.Header.更新日時.Value
                    ada.Update(tbl)

                Catch ex As Exception
                    MyBase.LastError = "以下のエラーのため、更新できませんでした。" & vbCrLf & ex.Message
                    Return False
                End Try
            End If

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function

        '''========================================================================================
        ''' <SUMMARY>稼働時間計算</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Private Function Get_KadouJikan(出庫時刻 As String, 帰庫時刻 As String) As String

            '日時を文字列でセット
            Dim tmp出庫時刻 As New DateTime
            tmp出庫時刻 = Convert.ToDateTime(出庫時刻)

            Dim tmp帰庫時刻 As New DateTime
            tmp帰庫時刻 = Convert.ToDateTime(帰庫時刻)

            '日を跨いだ場合
            If (tmp出庫時刻 > tmp帰庫時刻) Then
                tmp帰庫時刻.AddDays(1)
            End If

            ' 引き算
            Dim tmp稼働時間 As New TimeSpan
            tmp稼働時間 = tmp帰庫時刻 - tmp出庫時刻

            Console.WriteLine(tmp稼働時間.TotalMinutes)

            Return tmp稼働時間.Hours.ToString & ":" & tmp稼働時間.Minutes.ToString.PadLeft(2, "0") & ":" & tmp稼働時間.Seconds.ToString.PadLeft(2, "0")
        End Function
#End Region

#Region "Delete"
        '''========================================================================================
        ''' <SUMMARY>削除</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Delete_Header() As Boolean
            '----------------------------------------------------------------------
            ' ﾍｯﾀﾞ
            '----------------------------------------------------------------------
            '------------------------------------
            ' 変数定義
            '------------------------------------
            Dim ada As New NodeDatabase.DataSetTranTableAdapters.T乗務記録HDTableAdapter
            Dim tbl As New NodeDatabase.DataSetTran.T乗務記録HDDataTable
            Dim qry As New Collection

            '----------------------------------------------------------------------
            ' 条件設定
            '----------------------------------------------------------------------
            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, Me.Fields.Header.グループCD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, Me.Fields.Header.個社CD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.子会社CDColumn.ColumnName, Me.Fields.Header.子会社CD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.日報日付Column.ColumnName, Me.Fields.Header.日報日付.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.出庫時刻Column.ColumnName, Me.Fields.Header.出庫時刻.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.入庫時刻Column.ColumnName, Me.Fields.Header.入庫時刻.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.乗務員CDColumn.ColumnName, Me.Fields.Header.乗務員CD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.車両番号Column.ColumnName, Me.Fields.Header.車両番号.Value, BaseDatabase.Contents.Compare.Equal))

            '------------------------------------
            ' 削除
            '------------------------------------
            'Try
            '    tbl(0).Item(tbl.削除フラグColumn.ColumnName) = 1
            '    ada.Update(tbl)

            'Catch ex As Exception
            '    MyBase.LastError = "以下のエラーのため、削除できませんでした。" & vbCrLf & ex.Message
            '    Return False
            'End Try

            tbl = ada.SelectByCommon(qry)
            If tbl.Count > 0 Then
                Try
                    tbl(0).Item(tbl.削除フラグColumn.ColumnName) = 1
                    ada.Update(tbl)

                Catch ex As Exception
                    MyBase.LastError = "以下のエラーのため、削除できませんでした。" & vbCrLf & ex.Message
                    Return False
                End Try
            End If
            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "Valid_Write"
        '''========================================================================================
        ''' <SUMMARY>検査(共通)</SUMMARY>
        ''' <RETURNS>True:正常, False:不正</RETURNS>
        '''========================================================================================
        Protected Overrides Function Valid_Write_Header_Common() As NodeContents.ActionButton.ErrorLevel
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim strMsg As String = ""
            Dim Validator As New BaseCore.Common.Validator

            '------------------------------------------------------------------
            ' ｴﾗｰｸﾘｱ
            '------------------------------------------------------------------
            Me.Fields.Header.個社CD.IsError = False
            Me.Fields.Header.子会社CD.IsError = False
            Me.Fields.Header.日報日付.IsError = False
            Me.Fields.Header.乗務員CD.IsError = False
            Me.Fields.Header.車両番号.IsError = False
            Me.Fields.Header.一日全走行.IsError = False
            Me.Fields.Header.一日実車走行.IsError = False
            Me.Fields.Header.一日営業回数.IsError = False
            Me.Fields.Header.一日以後回数.IsError = False
            Me.Fields.Header.一日迎車回数.IsError = False
            Me.Fields.Header.一日予約回数.IsError = False
            Me.Fields.Header.男.IsError = False
            Me.Fields.Header.女.IsError = False
            Me.Fields.Header.回数料金.IsError = False
            Me.Fields.Header.爾後料金.IsError = False
            Me.Fields.Header.迎車料金.IsError = False
            Me.Fields.Header.予約料金.IsError = False
            Me.Fields.Header.定額差額A.IsError = False
            Me.Fields.Header.一日遠割額.IsError = False
            Me.Fields.Header.料金合計.IsError = False
            Me.Fields.Header.出庫時刻.IsError = False
            Me.Fields.Header.入庫時刻.IsError = False
            Me.Fields.Header.現金A.IsError = False
            Me.Fields.Header.チケットA.IsError = False
            Me.Fields.Header.クーポン四社A.IsError = False
            Me.Fields.Header.クーポン東旅協A.IsError = False
            Me.Fields.Header.福祉券A.IsError = False
            Me.Fields.Header.カードA.IsError = False
            Me.Fields.Header.IDA.IsError = False
            Me.Fields.Header.交通系ICA.IsError = False
            Me.Fields.Header.キャブカードA.IsError = False
            Me.Fields.Header.プリペイドカードA.IsError = False
            Me.Fields.Header.WAONA.IsError = False
            Me.Fields.Header.その他A.IsError = False
            Me.Fields.Header.障割A.IsError = False
            Me.Fields.Header.空転A.IsError = False
            Me.Fields.Header.料金内訳合計A.IsError = False
            Me.Fields.Header.実車_ETC料金.IsError = False
            Me.Fields.Header.ETC乗務員A.IsError = False
            Me.Fields.Header.ETC会社A.IsError = False
            Me.Fields.Header.ETC合計.IsError = False
            Me.Fields.Header.基本料金.IsError = False
            Me.Fields.Header.以後料金.IsError = False
            Me.Fields.Header.固定料金.IsError = False
            Me.Fields.Header.予約単価.IsError = False
            Me.Fields.Header.営収.IsError = False
            Me.Fields.Header.TAX.IsError = False
            Me.Fields.Header.乗務記録NO.IsError = False
            Me.Fields.Header.信販控除除外.IsError = False
            Me.Fields.Header.登録ユーザーCD.IsError = False
            Me.Fields.Header.登録ユーザー名.IsError = False
            Me.Fields.Header.登録日時.IsError = False
            Me.Fields.Header.更新ユーザーCD.IsError = False
            Me.Fields.Header.更新ユーザー名.IsError = False
            Me.Fields.Header.更新日時.IsError = False
            Me.Fields.Header.その他.IsError = False
            Me.Fields.Header.障割.IsError = False

            '------------------------------------------------------------------
            ' 共通検査
            '------------------------------------------------------------------
            If Not Validator.BaseChecker(Me.Fields.Header.個社CD) Then : Me.Fields.Header.個社CD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.子会社CD) Then : Me.Fields.Header.子会社CD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.日報日付) Then : Me.Fields.Header.日報日付.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.乗務員CD) Then : Me.Fields.Header.乗務員CD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.車両番号) Then : Me.Fields.Header.車両番号.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.一日全走行) Then : Me.Fields.Header.一日全走行.IsError = True : strMsg &= vbCrLf & "走行Km：未入力です" : End If
            If Not Validator.BaseChecker(Me.Fields.Header.一日実車走行) Then : Me.Fields.Header.一日実車走行.IsError = True : strMsg &= vbCrLf & "営業Km：未入力です" : End If
            If Not Validator.BaseChecker(Me.Fields.Header.一日営業回数) Then : Me.Fields.Header.一日営業回数.IsError = True : strMsg &= vbCrLf & "営業回数：未入力です" : End If
            If Not Validator.BaseChecker(Me.Fields.Header.一日以後回数) Then : Me.Fields.Header.一日以後回数.IsError = True : strMsg &= vbCrLf & "爾後回数：未入力です" : End If
            If Not Validator.BaseChecker(Me.Fields.Header.一日迎車回数) Then : Me.Fields.Header.一日迎車回数.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.一日予約回数) Then : Me.Fields.Header.一日予約回数.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.男) Then : Me.Fields.Header.男.IsError = True : strMsg &= vbCrLf & "輸送人員(男)：未入力です" : End If
            If Not Validator.BaseChecker(Me.Fields.Header.女) Then : Me.Fields.Header.女.IsError = True : strMsg &= vbCrLf & "輸送人員(女)：未入力です" : End If
            If Not Validator.BaseChecker(Me.Fields.Header.回数料金) Then : Me.Fields.Header.回数料金.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.爾後料金) Then : Me.Fields.Header.爾後料金.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.迎車料金) Then : Me.Fields.Header.迎車料金.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.予約料金) Then : Me.Fields.Header.予約料金.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.定額差額A) Then : Me.Fields.Header.定額差額A.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.一日遠割額) Then : Me.Fields.Header.一日遠割額.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.料金合計) Then : Me.Fields.Header.料金合計.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.出庫時刻) Then : Me.Fields.Header.出庫時刻.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.入庫時刻) Then : Me.Fields.Header.入庫時刻.IsError = True : strMsg &= vbCrLf & "帰庫時刻：未入力です" : End If
            If Not Validator.BaseChecker(Me.Fields.Header.現金A) Then : Me.Fields.Header.現金A.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.チケットA) Then : Me.Fields.Header.チケットA.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.クーポン四社A) Then : Me.Fields.Header.クーポン四社A.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.クーポン東旅協A) Then : Me.Fields.Header.クーポン東旅協A.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.福祉券A) Then : Me.Fields.Header.福祉券A.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.カードA) Then : Me.Fields.Header.カードA.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.IDA) Then : Me.Fields.Header.IDA.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.交通系ICA) Then : Me.Fields.Header.交通系ICA.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.キャブカードA) Then : Me.Fields.Header.キャブカードA.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.プリペイドカードA) Then : Me.Fields.Header.プリペイドカードA.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.WAONA) Then : Me.Fields.Header.WAONA.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.その他A) Then : Me.Fields.Header.その他A.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.障割A) Then : Me.Fields.Header.障割A.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.空転A) Then : Me.Fields.Header.空転A.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.料金内訳合計A) Then : Me.Fields.Header.料金内訳合計A.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.実車_ETC料金) Then : Me.Fields.Header.実車_ETC料金.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.ETC乗務員A) Then : Me.Fields.Header.ETC乗務員A.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.ETC会社A) Then : Me.Fields.Header.ETC会社A.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.ETC合計) Then : Me.Fields.Header.ETC合計.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.基本料金) Then : Me.Fields.Header.基本料金.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.以後料金) Then : Me.Fields.Header.以後料金.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.固定料金) Then : Me.Fields.Header.固定料金.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.予約単価) Then : Me.Fields.Header.予約単価.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.営収) Then : Me.Fields.Header.営収.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.TAX) Then : Me.Fields.Header.TAX.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.乗務記録NO) Then : Me.Fields.Header.乗務記録NO.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.信販控除除外) Then : Me.Fields.Header.信販控除除外.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.登録ユーザーCD) Then : Me.Fields.Header.登録ユーザーCD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.登録ユーザー名) Then : Me.Fields.Header.登録ユーザー名.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.登録日時) Then : Me.Fields.Header.登録日時.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.更新ユーザーCD) Then : Me.Fields.Header.更新ユーザーCD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.更新ユーザー名) Then : Me.Fields.Header.更新ユーザー名.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.更新日時) Then : Me.Fields.Header.更新日時.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.その他) Then : Me.Fields.Header.その他.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.障割) Then : Me.Fields.Header.障割.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If

            '------------------------------------------------------------------

            ' 返却
            '------------------------------------------------------------------
            MyBase.LastError = strMsg
            Select Case True
                Case strMsg = ""
                    Return NodeContents.ActionButton.ErrorLevel.None
                Case Else
                    Return NodeContents.ActionButton.ErrorLevel.Fatal
            End Select
        End Function

        '''========================================================================================
        ''' <SUMMARY>検査(個別)</SUMMARY>
        ''' <RETURNS>True:正常, False:不正</RETURNS>
        '''========================================================================================
        Protected Overrides Function Valid_Write_Header_Another() As NodeContents.ActionButton.ErrorLevel
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim strMsg As String = ""

            '------------------------------------------------------------------
            ' 個別検査
            '------------------------------------------------------------------

            If (BaseCore.Common.Text.CVal(Me.Fields.Header.料金合計Hid.Value) <> BaseCore.Common.Text.CVal(Me.Fields.Header.料金内訳合計AHid.Value)) Then
                'If (BaseCore.Common.Text.CVal(Me.Fields.Header.料金合計Hid.Value) <> BaseCore.Common.Text.CVal(Me.Fields.Header.料金内訳合計AHid.Value)) Then
                strMsg = "料金内訳合計が正しくありません。"
            End If



            '------------------------------------------------------------------
            ' 返却
            '------------------------------------------------------------------
            MyBase.LastError = strMsg
            Select Case True
                Case strMsg = ""
                    Return NodeContents.ActionButton.ErrorLevel.None
                Case Else
                    Return NodeContents.ActionButton.ErrorLevel.Fatal
            End Select
        End Function
#End Region

#Region "Valid_Delete"
        '''========================================================================================
        ''' <SUMMARY>検査(共通)</SUMMARY>
        ''' <RETURNS>True:正常, False:不正</RETURNS>
        '''========================================================================================
        Protected Overrides Function Valid_Delete_Header_Common() As NodeContents.ActionButton.ErrorLevel
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim strMsg As String = ""
            Dim Validator As New BaseCore.Common.Validator

            '------------------------------------------------------------------
            ' ｴﾗｰｸﾘｱ
            '------------------------------------------------------------------
            Me.Fields.Header.個社CD.IsError = False
            Me.Fields.Header.子会社CD.IsError = False
            Me.Fields.Header.日報日付.IsError = False
            Me.Fields.Header.乗務員CD.IsError = False
            Me.Fields.Header.車両番号.IsError = False
            Me.Fields.Header.一日全走行.IsError = False
            Me.Fields.Header.一日実車走行.IsError = False
            Me.Fields.Header.一日営業回数.IsError = False
            Me.Fields.Header.一日以後回数.IsError = False
            Me.Fields.Header.男.IsError = False
            Me.Fields.Header.出庫時刻.IsError = False
            Me.Fields.Header.入庫時刻.IsError = False
            '------------------------------------------------------------------
            ' 共通検査
            '------------------------------------------------------------------
            If Not Validator.BaseChecker(Me.Fields.Header.個社CD) Then : Me.Fields.Header.個社CD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.子会社CD) Then : Me.Fields.Header.子会社CD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.日報日付) Then : Me.Fields.Header.日報日付.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.乗務員CD) Then : Me.Fields.Header.乗務員CD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.車両番号) Then : Me.Fields.Header.車両番号.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.一日全走行) Then : Me.Fields.Header.一日全走行.IsError = True : strMsg &= vbCrLf & "走行Km：未入力です" : End If
            If Not Validator.BaseChecker(Me.Fields.Header.一日実車走行) Then : Me.Fields.Header.一日実車走行.IsError = True : strMsg &= vbCrLf & "営業Km：未入力です" : End If
            If Not Validator.BaseChecker(Me.Fields.Header.一日営業回数) Then : Me.Fields.Header.一日営業回数.IsError = True : strMsg &= vbCrLf & "営業回数：未入力です" : End If
            If Not Validator.BaseChecker(Me.Fields.Header.一日以後回数) Then : Me.Fields.Header.一日以後回数.IsError = True : strMsg &= vbCrLf & "爾後回数：未入力です" : End If
            If Not Validator.BaseChecker(Me.Fields.Header.男) Then : Me.Fields.Header.男.IsError = True : strMsg &= vbCrLf & "輸送人員(男)：未入力です" : End If
            If Not Validator.BaseChecker(Me.Fields.Header.女) Then : Me.Fields.Header.女.IsError = True : strMsg &= vbCrLf & "輸送人員(女)：未入力です" : End If
            If Not Validator.BaseChecker(Me.Fields.Header.出庫時刻) Then : Me.Fields.Header.出庫時刻.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.入庫時刻) Then : Me.Fields.Header.入庫時刻.IsError = True : strMsg &= vbCrLf & "帰庫時刻：未入力です" : End If
            '------------------------------------------------------------------
            ' 返却
            '------------------------------------------------------------------
            MyBase.LastError = strMsg
            Select Case True
                Case strMsg = ""
                    Return NodeContents.ActionButton.ErrorLevel.None
                Case Else
                    Return NodeContents.ActionButton.ErrorLevel.Fatal
            End Select
        End Function
#End Region

#End Region
    End Class
End Namespace
