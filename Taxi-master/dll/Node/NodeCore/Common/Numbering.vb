﻿Imports Microsoft.VisualBasic

Namespace Common
    Public Class Numbering
#Region "列挙"
        '''========================================================================================
        ''' <summary>採番ｺｰﾄﾞ</summary>
        '''========================================================================================
        Public Enum ModeContents
            None = 0
            荷主内部 = 1
            商品内部 = 2
            荷主CD = 3

            伝票共通 = 10
            伝票出予 = 11
            伝票入予 = 12
            伝票諸掛 = 13
            伝票運賃 = 14
            伝票入金 = 15

            請求番号 = 20

            レイアウト = 25
            レイアウト送り状 = 26
            レイアウトマスタ = 27
            レイアウト出力 = 28

            JAN = 30
        End Enum
#End Region

#Region "変数"
        Private _LastError As String = ""
        Private _Value As String = ""
#End Region

#Region "ﾌﾟﾛﾊﾟﾃｨ"
        '''========================================================================================
        ''' <SUMMARY>ﾃｰﾌﾞﾙ名</SUMMARY>   
        '''========================================================================================
        Private ReadOnly Property TableName() As String
            Get
                Return "採番情報"
            End Get
        End Property

        '''========================================================================================
        '''  <summary>採番した番号</summary>
        '''========================================================================================
        Public ReadOnly Property Value() As String
            Get
                Return Me._Value
            End Get
        End Property

        '''========================================================================================
        '''  <summary>ｴﾗｰﾒｯｾｰｼﾞ</summary>
        '''========================================================================================
        Public ReadOnly Property LastError() As String
            Get
                Return Me._LastError
            End Get
        End Property
#End Region

#Region "ﾒｿｯﾄﾞ"
#Region "DataExecute  [実行]"
        '''========================================================================================
        ''' <SUMMARY>読込</SUMMARY>   
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Public Function Execute(ByVal mode As ModeContents, Optional ByVal NumberKey As String = "") As Boolean
            '----------------------------------------------------------------------
            ' ｴﾗｰｸﾘｱ
            '----------------------------------------------------------------------
            Me._LastError = ""

            '----------------------------------------------------------------------
            ' 採番ｺｰﾄﾞ 算出
            '----------------------------------------------------------------------
            Dim strCode As String = ""
            Select Case mode
                Case ModeContents.荷主内部 : strCode = "内部荷主"
                Case ModeContents.商品内部 : strCode = "内部商品"
                Case ModeContents.荷主CD : strCode = "申請荷主"

                Case ModeContents.伝票共通 : strCode = "伝票共通"
                Case ModeContents.伝票出予 : strCode = "伝票出予"
                Case ModeContents.伝票入予 : strCode = "伝票入予"
                Case ModeContents.伝票諸掛 : strCode = "伝票諸掛"
                Case ModeContents.伝票運賃 : strCode = "伝票運賃"
                Case ModeContents.伝票入金 : strCode = "伝票入金"

                Case ModeContents.請求番号 : strCode = "請求番号"

                Case ModeContents.レイアウト : strCode = "レイアウト"
                Case ModeContents.レイアウト送り状 : strCode = "レイアウト送り状"
                Case ModeContents.レイアウトマスタ : strCode = "レイアウトマスタ"
                Case ModeContents.レイアウト出力 : strCode = "レイアウト出力"
                Case ModeContents.JAN : strCode = "JAN" & NumberKey
            End Select

            '----------------------------------------------------------------------
            ' M採番 更新
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetMasterTableAdapters.M採番TableAdapter
            Dim tbl As New NodeDatabase.DataSetMaster.M採番DataTable
            Dim qry As New Collection

            qry.Add(New BaseDatabase.Condition(tbl.採番CDColumn.ColumnName, strCode, BaseDatabase.Contents.Compare.Equal))
            tbl = ada.SelectByCommon(qry)

            If tbl.Count > 0 Then
                '--------------------------------
                ' 訂正
                '--------------------------------
                Try
                    Me._Value = Me.NumberFormat(tbl(0).最終NO, tbl(0).桁数, tbl(0).パディング)
                    tbl(0).最終NO = Me._Value
                    ada.Update(tbl)
                Catch ex As Exception
                    Me._LastError = "以下のエラーのため、" & TableName & "が更新できませんでした。" & vbCrLf & ex.Message
                    Return False
                End Try
            Else
                '--------------------------------
                ' 新規
                '--------------------------------
                Try
                    Me._Value = Me.NumberFormat("0", 10, "1")

                    ada.Insert(strCode _
                             , 10 _
                             , "1" _
                             , Me._Value _
                              )
                Catch ex As Exception
                    Me._LastError = "以下のエラーのため、" & TableName & "が追加できませんでした。" & vbCrLf & ex.Message
                    Return False
                End Try
            End If

            Return True
        End Function
#End Region
#End Region

#Region "ﾌﾟﾛｼｰｼﾞｬ"
        '''========================================================================================
        ''' <SUMMARY>最終NO 取得</SUMMARY>   
        '''========================================================================================
        Private Function NumberFormat(ByVal Num As String, ByVal Length As Integer, ByVal Padding As String) As String
            Dim strReturn As String = ""

            '----------------------------------------------------------------------
            ' 桁数, ｾﾞﾛｻﾌﾟﾚｽ ﾃﾞｨﾌｫﾙﾄ処理
            '----------------------------------------------------------------------
            If Length = 0 Then
                Length = 10
            End If

            If Padding = "" Then
                Padding = "1"
            End If

            '----------------------------------------------------------------------
            ' 最終NO 採番
            '----------------------------------------------------------------------
            strReturn = CDec(BaseCore.Common.Text.TextFormat(Num, Length, BaseContents.TextBox.Format.Code, , True)) + 1

            '----------------------------------------------------------------------
            ' 最終NO 整形
            '----------------------------------------------------------------------
            If Padding = "1" Then
                '頭にｾﾞﾛを付与する
                strReturn = BaseCore.Common.Text.TextFormat(strReturn, Length, BaseContents.TextBox.Format.Code, , True)
            Else
                '頭にｾﾞﾛを付与しない
                strReturn = CStr(strReturn)
            End If

            '----------------------------------------------------------------------
            ' 返却
            '----------------------------------------------------------------------
            Return strReturn
        End Function


        Private Function NumberFormatCutFloat(ByVal Num As String) As String
            Dim delimiter As Char = "."c
            Dim strReturn As String = Num.Split(delimiter).ElementAt(0)

            '----------------------------------------------------------------------
            ' 返却
            '----------------------------------------------------------------------
            Return strReturn
        End Function
#End Region
    End Class
End Namespace
