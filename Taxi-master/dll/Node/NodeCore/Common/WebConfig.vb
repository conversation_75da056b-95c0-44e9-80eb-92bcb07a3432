﻿Imports System
Imports System.Configuration
Imports System.Collections.Specialized

Namespace Common
    Public Class WebConfig

#Region "ﾌﾟﾛﾊﾟﾃｨ"
        Private _Provider As NodeDatabase.Provider

        '''========================================================================================
        ''' <SUMMARY>ｺﾈｸｼｮﾝｽﾄﾘﾝｸﾞ</SUMMARY>
        '''========================================================================================
        Public ReadOnly Property ConnectionStringBase() As String
            Get
                Return _Provider.ConnectionStringBase
            End Get
        End Property

        'Public ReadOnly Property ConnectionStringMail() As String
        '    Get
        '        Return _Provider.ConnectionStringMail
        '    End Get
        'End Property

        'Public ReadOnly Property ConnectionStringPick() As String
        '    Get
        '        Return _Provider.ConnectionStringPick
        '    End Get
        'End Property
#End Region

#Region "ｺﾝｽﾄﾗｸﾀ"
        '''========================================================================================
        ''' <SUMMARY>ﾃﾞｰﾀﾍﾞｰｽｸﾗｽのｺﾈｸｼｮﾝｽﾄﾘﾝｸﾞを書き換え</SUMMARY>
        '''========================================================================================
        Public Sub New()
            '------------------------------------
            ' 本体
            '------------------------------------
            _Provider = New NodeDatabase.Provider

            _Provider.ConnectionStringBase = CType(ConfigurationManager.GetSection("DBSettings"), NameValueCollection).Item("ConnectionStringBase")
            '_Provider.ConnectionStringMail = CType(ConfigurationManager.GetSection("DBSettings"), NameValueCollection).Item("ConnectionStringMail")
            '_Provider.ConnectionStringPick = CType(ConfigurationManager.GetSection("DBSettings"), NameValueCollection).Item("ConnectionStringPick")
        End Sub
#End Region
    End Class
End Namespace
