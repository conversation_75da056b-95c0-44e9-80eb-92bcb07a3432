﻿Imports Microsoft.VisualBasic

Namespace Common
    Public Class Exist
#Region "M区分"
        '''========================================================================================
        ''' <summary>M区分</summary>
        '''========================================================================================
        Public Shared Function M区分(ByVal 区分種別 As String, ByVal 区分ID As String) As Boolean
            '----------------------------------------------------------------------
            ' ｷｰが空白なら即終了
            '----------------------------------------------------------------------
            If 区分ID = "" Then
                Return True
            End If

            '----------------------------------------------------------------------
            ' 名称類 取得
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetMasterTableAdapters.M区分TableAdapter
            Dim tbl As New NodeDatabase.DataSetMaster.M区分DataTable
            Dim qry As New Collection

            qry.Add(New BaseDatabase.Condition(tbl.区分種別Column.ColumnName, 区分種別, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.区分IDColumn.ColumnName, 区分ID, BaseDatabase.Contents.Compare.Equal))
            tbl = ada.SelectByCommon(qry)

            '----------------------------------------------------------------------
            ' 返却
            '----------------------------------------------------------------------
            Return tbl.Count > 0
        End Function
#End Region

#Region "M区分"
        '''========================================================================================
        ''' <summary>Mシフト区分</summary>
        '''========================================================================================
        Public Shared Function Mシフト区分(ByVal シフト区分 As String) As Boolean
            '----------------------------------------------------------------------
            ' ｷｰが空白なら即終了
            '----------------------------------------------------------------------
            If シフト区分 = "" Then
                Return True
            End If

            '----------------------------------------------------------------------
            ' 名称類 取得
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetMasterTableAdapters.Mシフト区分TableAdapter
            Dim tbl As New NodeDatabase.DataSetMaster.Mシフト区分DataTable
            Dim qry As New Collection

            qry.Add(New BaseDatabase.Condition(tbl.シフト区分Column.ColumnName, シフト区分, BaseDatabase.Contents.Compare.Equal))
            tbl = ada.SelectByCommon(qry)

            '----------------------------------------------------------------------
            ' 返却
            '----------------------------------------------------------------------
            Return tbl.Count > 0
        End Function
#End Region

#Region "Mグループ"
        '''========================================================================================
        ''' <summary>Mグループ</summary>
        '''========================================================================================
        Public Shared Function Mグループ(ByVal コード As String) As Boolean
            '----------------------------------------------------------------------
            ' ｷｰが空白なら即終了
            '----------------------------------------------------------------------
            If コード = "" Then
                Return True
            End If

            '----------------------------------------------------------------------
            ' 名称類 取得
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetMasterTableAdapters.MグループTableAdapter
            Dim tbl As New NodeDatabase.DataSetMaster.MグループDataTable
            Dim qry As New Collection

            qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, コード, BaseDatabase.Contents.Compare.Equal))
            tbl = ada.SelectByCommon(qry)

            '----------------------------------------------------------------------
            ' 返却
            '----------------------------------------------------------------------
            Return tbl.Count > 0
        End Function
#End Region

#Region "M個社"
        '''========================================================================================
        ''' <summary>M個社</summary>
        '''========================================================================================
        Public Shared Function M個社(ByVal グループCD As String, ByVal 個社CD As String) As Boolean
            '----------------------------------------------------------------------
            ' ｷｰが空白なら即終了
            '----------------------------------------------------------------------
            If グループCD = "" Then
                Return False
            End If

            If 個社CD = "" Then
                Return True
            End If

            '----------------------------------------------------------------------
            ' 名称類 取得
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetMasterTableAdapters.M個社TableAdapter
            Dim tbl As New NodeDatabase.DataSetMaster.M個社DataTable
            Dim qry As New Collection

            qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, グループCD, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, 個社CD, BaseDatabase.Contents.Compare.Equal))
            tbl = ada.SelectByCommon(qry)

            '----------------------------------------------------------------------
            ' 返却
            '----------------------------------------------------------------------
            Return tbl.Count > 0
        End Function
#End Region

#Region "M子会社"
        '''========================================================================================
        ''' <summary>M子会社</summary>
        '''========================================================================================
        Public Shared Function M子会社(ByVal グループCD As String, ByVal 個社CD As String, ByVal 子会社CD As String) As Boolean
            '----------------------------------------------------------------------
            ' ｷｰが空白なら即終了
            '----------------------------------------------------------------------
            If グループCD = "" Then
                Return False
            End If

            If 個社CD = "" Then
                Return False
            End If

            If 子会社CD = "" Then
                Return True
            End If


            '----------------------------------------------------------------------
            ' 名称類 取得
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetMasterTableAdapters.M子会社TableAdapter
            Dim tbl As New NodeDatabase.DataSetMaster.M子会社DataTable
            Dim qry As New Collection

            qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, グループCD, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, 個社CD, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.子会社CDColumn.ColumnName, 子会社CD, BaseDatabase.Contents.Compare.Equal))
            tbl = ada.SelectByCommon(qry)

            '----------------------------------------------------------------------
            ' 返却
            '----------------------------------------------------------------------
            Return tbl.Count > 0
        End Function
#End Region


    End Class
End Namespace
