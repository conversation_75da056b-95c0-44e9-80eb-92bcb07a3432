﻿Namespace Common
    Public Class FileIO
#Region "同一ﾌｧｲﾙ名(拡張子違い)の中から先頭ﾌｧｲﾙ名を返す(該当ﾌｧｲﾙ名が見つからない時は、｢NoImage.jpg｣を返す)"
        '''========================================================================================
        ''' <SUMMARY>同一ﾌｧｲﾙ名(拡張子違い)の中から先頭ﾌｧｲﾙ名を返す(該当ﾌｧｲﾙ名が見つからない時は、｢NoImage.jpg｣を返す)</SUMMARY>
        ''' <PARAM NAME="ImageDir">ﾃﾞｨﾚｸﾄﾘ名</PARAM>
        ''' <PARAM NAME="ImageFileName">探し出すﾌｧｲﾙ名</PARAM>
        ''' <RETURNS>ﾊﾞｲﾄ配列</RETURNS>
        '''========================================================================================
        Public Shared Function ImageFilePath(ByVal Config As NodeCore.Common.Config, ByVal ImageDir As String, ByVal ImageFileName As String) As String
            '----------------------------------------------------------
            ' ｢商品ｺｰﾄﾞ + 拡張子｣のﾌｧｲﾙ一覧を取得して先頭のﾌｧｲﾙを画像ﾌｧｲﾙとする
            '----------------------------------------------------------
            Dim strPath As String = Config.Dirテンプレート & "__no_image.jpg"
            Dim arrPath As String()
            If System.IO.Directory.Exists(ImageDir) Then
                arrPath = System.IO.Directory.GetFiles(ImageDir, ImageFileName & ".*")
                If arrPath.Length > 0 Then strPath = arrPath(0)
            End If

            '----------------------------------------------------------
            ' ﾊﾟｽ返却
            '----------------------------------------------------------
            Return strPath
        End Function
#End Region

#Region "同一ﾌｧｲﾙ名(拡張子違い)の中から先頭ﾌｧｲﾙのﾊﾞｲﾄ配列を返す(該当ﾌｧｲﾙ名が見つからない時は、｢NoImage.jpg｣のﾊﾞｲﾄ配列を返す)"
        '''========================================================================================
        ''' <SUMMARY>同一ﾌｧｲﾙ名(拡張子違い)の中から先頭ﾌｧｲﾙのﾊﾞｲﾄ配列を返す(該当ﾌｧｲﾙ名が見つからない時は、｢NoImage.jpg｣のﾊﾞｲﾄ配列を返す)</SUMMARY>
        ''' <PARAM NAME="ImageDir">ﾃﾞｨﾚｸﾄﾘ名</PARAM>
        ''' <PARAM NAME="ImageFileName">探し出すﾌｧｲﾙ名</PARAM>
        ''' <RETURNS>ﾊﾞｲﾄ配列</RETURNS>
        '''========================================================================================
        Public Shared Function ImageFileByte(ByVal Config As NodeCore.Common.Config, ByVal ImageDir As String, ByVal ImageFileName As String) As Byte()
            Return Microsoft.VisualBasic.FileIO.FileSystem.ReadAllBytes(ImageFilePath(Config, ImageDir, ImageFileName))
        End Function
#End Region
    End Class
End Namespace
