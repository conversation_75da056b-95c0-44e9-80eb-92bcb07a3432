﻿Namespace Common
    Public Class Logic
#Region "ﾜｰｸの削除日付 取得"
        '''========================================================================================
        ''' <summary>ﾜｰｸの削除日時を返す</summary>
        ''' <returns>削除日時</returns>
        '''========================================================================================
        Public Shared Function ワーク削除日時() As String
            Return Format(Now.AddDays(-7), "yyyy/MM/dd HH:mm:ss")
        End Function
#End Region


#Region "ﾃﾞｰﾀﾍﾞｰｽ"
        Public Class Database
#Region "Sメニュー"
            '''========================================================================================
            ''' <summary>Sメニューから有効なページを検索して返す</summary>
            ''' <returns>Sメニューのﾃｰﾌﾞﾙ</returns>
            '''========================================================================================
            Public Shared Function Sメニュー(Optional ページID As String = "") As NodeDatabase.DataSetSystem.SメニューDataTable
                '----------------------------------------------------------------------
                ' 変数定義
                '----------------------------------------------------------------------
                Dim ada As New NodeDatabase.DataSetSystemTableAdapters.SメニューTableAdapter
                Dim tbl As New NodeDatabase.DataSetSystem.SメニューDataTable
                Dim qry As New Collection

                '----------------------------------------------------------------------
                ' 並べ替え
                '----------------------------------------------------------------------
                Dim strSort As String = ""
                Dim strDelim As String = ""

                strSort &= strDelim & tbl.表示順Column.ColumnName & " ASC" : strDelim = ","

                '----------------------------------------------------------------------
                ' 条件設定
                '----------------------------------------------------------------------
                qry.Clear()
                If ページID = "" Then
                    qry.Add(New BaseDatabase.Condition(tbl.ページIDColumn.ColumnName, "", BaseDatabase.Contents.Compare.NotEqual))
                Else
                    qry.Add(New BaseDatabase.Condition(tbl.ページIDColumn.ColumnName, ページID, BaseDatabase.Contents.Compare.Equal))
                End If

                '----------------------------------------------------------------------
                ' 読込
                '----------------------------------------------------------------------
                tbl = ada.SelectByCommon(qry, strSort)

                '----------------------------------------------------------------------
                ' 正常終了
                '----------------------------------------------------------------------
                Return tbl
            End Function
#End Region

#Region "M区分"
            '''========================================================================================
            ''' <summary>Sメニューから有効なページを検索して返す</summary>
            ''' <returns>Sメニューのﾃｰﾌﾞﾙ</returns>
            '''========================================================================================
            Public Shared Function M区分(区分種別 As String) As NodeDatabase.DataSetMaster.M区分DataTable
                '----------------------------------------------------------------------
                ' 変数定義
                '----------------------------------------------------------------------
                Dim ada As New NodeDatabase.DataSetMasterTableAdapters.M区分TableAdapter
                Dim tbl As New NodeDatabase.DataSetMaster.M区分DataTable
                Dim qry As New Collection

                '----------------------------------------------------------------------
                ' 並べ替え
                '----------------------------------------------------------------------
                Dim strSort As String = ""
                Dim strDelim As String = ""

                strSort &= strDelim & tbl.区分IDColumn.ColumnName & " ASC" : strDelim = ","

                '----------------------------------------------------------------------
                ' 条件設定
                '----------------------------------------------------------------------
                qry.Clear()
                qry.Add(New BaseDatabase.Condition(tbl.区分種別Column.ColumnName, 区分種別, BaseDatabase.Contents.Compare.Equal))

                '----------------------------------------------------------------------
                ' 読込
                '----------------------------------------------------------------------
                tbl = ada.SelectByCommon(qry, strSort)

                '----------------------------------------------------------------------
                ' 正常終了
                '----------------------------------------------------------------------
                Return tbl
            End Function
#End Region

#Region "Mシフト区分"
            '''========================================================================================
            ''' <summary>Sメニューから有効なページを検索して返す</summary>
            ''' <returns>Sメニューのﾃｰﾌﾞﾙ</returns>
            '''========================================================================================
            Public Shared Function Mシフト区分() As NodeDatabase.DataSetMaster.Mシフト区分DataTable
                '----------------------------------------------------------------------
                ' 変数定義
                '----------------------------------------------------------------------
                Dim ada As New NodeDatabase.DataSetMasterTableAdapters.Mシフト区分TableAdapter
                Dim tbl As New NodeDatabase.DataSetMaster.Mシフト区分DataTable
                Dim qry As New Collection

                '----------------------------------------------------------------------
                ' 並べ替え
                '----------------------------------------------------------------------
                Dim strSort As String = ""
                Dim strDelim As String = ""

                strSort &= strDelim & tbl.シフト区分Column.ColumnName & " ASC" : strDelim = ","

                '----------------------------------------------------------------------
                ' 条件設定
                '----------------------------------------------------------------------
                qry.Clear()

                '----------------------------------------------------------------------
                ' 読込
                '----------------------------------------------------------------------
                tbl = ada.SelectByCommon(qry, strSort)

                '----------------------------------------------------------------------
                ' 正常終了
                '----------------------------------------------------------------------
                Return tbl
            End Function
#End Region

#Region "M子会社"
            '''========================================================================================
            ''' <summary>Sメニューから有効なページを検索して返す</summary>
            ''' <returns>Sメニューのﾃｰﾌﾞﾙ</returns>
            '''========================================================================================
            Public Shared Function M子会社() As NodeDatabase.DataSetMaster.M子会社DataTable
                '----------------------------------------------------------------------
                ' 変数定義
                '----------------------------------------------------------------------
                Dim ada As New NodeDatabase.DataSetMasterTableAdapters.M子会社TableAdapter
                Dim tbl As New NodeDatabase.DataSetMaster.M子会社DataTable
                Dim qry As New Collection

                '----------------------------------------------------------------------
                ' 並べ替え
                '----------------------------------------------------------------------
                Dim strSort As String = ""
                Dim strDelim As String = ""

                strSort &= strDelim & tbl.子会社CDColumn.ColumnName & " ASC" : strDelim = ","

                '----------------------------------------------------------------------
                ' 条件設定
                '----------------------------------------------------------------------
                qry.Clear()

                '----------------------------------------------------------------------
                ' 読込
                '----------------------------------------------------------------------
                tbl = ada.SelectByCommon(qry, strSort)

                '----------------------------------------------------------------------
                ' 正常終了
                '----------------------------------------------------------------------
                Return tbl
            End Function
#End Region

        End Class
#End Region


#Region "金額項目のｶﾝﾏ編集"
        '''========================================================================================
        ''' <SUMMARY>金額項目のｶﾝﾏ編集</SUMMARY>
        ''' <PARAM NAME="Money">金額</PARAM>
        ''' <RETURNS>ｶﾝﾏ編集</RETURNS>
        '''========================================================================================
        Public Shared Function FormatMoney(ByVal Money As String) As String
            Return BaseCore.Common.Text.TextFormat(BaseCore.Common.Text.CVal(Money), 10, BaseContents.TextBox.Format.Currency, 0, True)
        End Function
#End Region

#Region "数値項目のｶﾝﾏ書式"
        '''========================================================================================
        ''' <SUMMARY>数値項目のｶﾝﾏ書式</SUMMARY>
        ''' <PARAM NAME="MinorLength">小数点以下の桁数</PARAM>
        ''' <RETURNS>ｶﾝﾏ書式</RETURNS>
        '''========================================================================================
        Public Shared Function FormatStyle(ByVal MinorLength As Integer) As String
            Dim strStyle As String = ""
            Dim strMinor As String = ""

            ' 小数点以下の書式
            If MinorLength > 0 Then
                strMinor = "." & StrDup(MinorLength, "0")
            End If

            ' 書式整形
            strStyle = "{0:#,##0@}".Replace("@", strMinor)

            ' 返却
            Return strStyle
        End Function
#End Region

#Region "数量項目のｶﾝﾏ編集"
        '''========================================================================================
        ''' <SUMMARY>数量項目のｶﾝﾏ編集</SUMMARY>
        ''' <PARAM NAME="Qty">数量</PARAM>
        ''' <RETURNS>ｶﾝﾏ編集</RETURNS>
        '''========================================================================================
        Public Shared Function FormatQty(ByVal Qty As String) As String
            Return BaseCore.Common.Text.TextFormat(BaseCore.Common.Text.CVal(Qty), NodeContents.Constant.System.QTY_LENGTH, BaseContents.TextBox.Format.Currency, , True)
        End Function
#End Region

#Region "単価項目のｶﾝﾏ編集"
        '''========================================================================================
        ''' <SUMMARY>単価項目のｶﾝﾏ編集</SUMMARY>
        ''' <PARAM NAME="Price">単価</PARAM>
        ''' <RETURNS>ｶﾝﾏ編集</RETURNS>
        '''========================================================================================
        Public Shared Function FormatPrice(ByVal Price As String) As String
            Return BaseCore.Common.Text.TextFormat(BaseCore.Common.Text.CVal(Price), 10, BaseContents.TextBox.Format.Currency, 3, True)
        End Function
#End Region

#Region "決算の算出"
        '''========================================================================================
        ''' <summary>年度の算出</summary>
        ''' <param name="日付">日付</param>
        ''' <param name="決算月">決算月</param>
        ''' <returns>決算年度</returns>
        '''========================================================================================
        Public Shared Function 決算年度(ByVal 日付 As Date, ByVal 決算月 As String) As String
            If Format(日付, "MM") <= 決算月 Then
                Return Format(日付.AddYears(-1), "yyyy")
            Else
                Return Format(日付.AddYears(0), "yyyy")
            End If
        End Function
#End Region

#Region "勤怠年月"
        '''========================================================================================
        ''' <summary>勤怠年月</summary>
        ''' <param name="日付">日付</param>
        ''' <returns>勤怠の年月</returns>
        '''========================================================================================
        Public Shared Function 勤怠年月(ByVal 日付 As Date) As String
            If Format(日付, "dd") <= "20" Then
                Return Format(日付.AddMonths(0), "yyyy/MM")
            Else
                Return Format(日付.AddMonths(+1), "yyyy/MM")
            End If
        End Function
#End Region

#Region "勤怠日付F"
        '''========================================================================================
        ''' <summary>勤怠日付F</summary>
        ''' <param name="日付">日付</param>
        ''' <returns>勤怠の開始日付</returns>
        '''========================================================================================
        Public Shared Function 勤怠日付F(ByVal 日付 As Date) As String
            If Format(日付, "dd") <= "20" Then
                Return Format(日付.AddMonths(-1), "yyyy/MM") & "/21"
            Else
                Return Format(日付.AddMonths(0), "yyyy/MM") & "/21"
            End If
        End Function
#End Region

#Region "勤怠日付T"
        '''========================================================================================
        ''' <summary>勤怠日付T</summary>
        ''' <param name="日付">日付</param>
        ''' <returns>勤怠の終了日付</returns>
        '''========================================================================================
        Public Shared Function 勤怠日付T(ByVal 日付 As Date) As String
            If Format(日付, "dd") <= "20" Then
                Return Format(日付.AddMonths(0), "yyyy/MM") & "/20"
            Else
                Return Format(日付.AddMonths(1), "yyyy/MM") & "/20"
            End If
        End Function
#End Region

#Region "CodeToName"
        Public Class CodeToName
#Region "区分名"
            Public Shared Function 区分ID(ByVal str区分ID As String, ByVal str区分種別 As String) As String
                '----------------------------------------------------------------------
                ' 返却 配列
                '----------------------------------------------------------------------
                Dim strReturn As String = ""

                If str区分ID = "" Then
                    Return strReturn
                End If

                If str区分種別 = "" Then
                    Return strReturn
                End If

                '----------------------------------------------------------------------
                ' コード 取得
                '----------------------------------------------------------------------
                Dim ada As New NodeDatabase.DataSetMasterTableAdapters.M区分TableAdapter
                Dim tbl As New NodeDatabase.DataSetMaster.M区分DataTable
                Dim qry As New Collection

                qry.Clear()
                qry.Add(New BaseDatabase.Condition(tbl.区分IDColumn.ColumnName, str区分ID, BaseDatabase.Contents.Compare.Equal))
                qry.Add(New BaseDatabase.Condition(tbl.区分種別Column.ColumnName, str区分種別, BaseDatabase.Contents.Compare.Equal))
                tbl = ada.SelectByCommon(qry)

                If tbl.Count > 0 Then
                    strReturn = tbl(0).Item(tbl.区分名Column.ColumnName)
                End If

                Return strReturn
            End Function
#End Region

#Region "ユーザーCD"
            Public Shared Function ユーザーCD(ByVal strユーザーCD As String) As String
                '----------------------------------------------------------------------
                ' 返却 配列
                '----------------------------------------------------------------------
                Dim strReturn As String = ""

                If strユーザーCD = "" Then
                    Return strReturn
                End If

                '----------------------------------------------------------------------
                ' CD 取得
                '----------------------------------------------------------------------
                Dim ada As New NodeDatabase.DataSetMasterTableAdapters.MユーザーTableAdapter
                Dim tbl As New NodeDatabase.DataSetMaster.MユーザーDataTable
                Dim qry As New Collection

                qry.Clear()
                qry.Add(New BaseDatabase.Condition(tbl.ユーザーCDColumn.ColumnName, strユーザーCD, BaseDatabase.Contents.Compare.Equal))
                tbl = ada.SelectByCommon(qry)

                If tbl.Count > 0 Then
                    strReturn = tbl(0).Item(tbl.ユーザー名Column.ColumnName)
                End If

                Return strReturn
            End Function
#End Region

        End Class
#End Region

#Region "NameToCode"
        Public Class NameToCode
#Region "区分名"
            Public Shared Function 区分名(ByVal str区分名 As String, ByVal str区分種別 As String) As String
                '----------------------------------------------------------------------
                ' 返却 配列
                '----------------------------------------------------------------------
                Dim strReturn As String = ""

                If str区分名 = "" Then
                    Return strReturn
                End If

                If str区分種別 = "" Then
                    Return strReturn
                End If

                '----------------------------------------------------------------------
                ' CD 取得
                '----------------------------------------------------------------------
                Dim ada As New NodeDatabase.DataSetMasterTableAdapters.M区分TableAdapter
                Dim tbl As New NodeDatabase.DataSetMaster.M区分DataTable
                Dim qry As New Collection

                qry.Clear()
                qry.Add(New BaseDatabase.Condition(tbl.区分名Column.ColumnName, str区分名, BaseDatabase.Contents.Compare.Equal))
                qry.Add(New BaseDatabase.Condition(tbl.区分種別Column.ColumnName, str区分種別, BaseDatabase.Contents.Compare.Equal))
                tbl = ada.SelectByCommon(qry)

                If tbl.Count > 0 Then
                    strReturn = tbl(0).Item(tbl.区分IDColumn.ColumnName)
                End If

                Return strReturn
            End Function
#End Region

#Region "ユーザー名"
            Public Shared Function ユーザー名(ByVal strユーザー名 As String) As String
                '----------------------------------------------------------------------
                ' 返却 配列
                '----------------------------------------------------------------------
                Dim strReturn As String = ""

                If strユーザー名 = "" Then
                    Return strReturn
                End If

                '----------------------------------------------------------------------
                ' CD 取得
                '----------------------------------------------------------------------
                Dim ada As New NodeDatabase.DataSetMasterTableAdapters.MユーザーTableAdapter
                Dim tbl As New NodeDatabase.DataSetMaster.MユーザーDataTable
                Dim qry As New Collection

                qry.Clear()
                qry.Add(New BaseDatabase.Condition(tbl.ユーザー名Column.ColumnName, strユーザー名, BaseDatabase.Contents.Compare.Equal))
                tbl = ada.SelectByCommon(qry)

                If tbl.Count > 0 Then
                    strReturn = tbl(0).Item(tbl.ユーザーCDColumn.ColumnName)
                End If

                Return strReturn
            End Function
#End Region

        End Class
#End Region

#Region "NumberFormatCutFloat"

        Public Shared Function NumberFormatCutFloat(ByVal Num As String) As String
            Dim delimiter As Char = "."c
            Dim strReturn As String = Num.Split(delimiter).ElementAt(0)

            '----------------------------------------------------------------------
            ' 返却
            '----------------------------------------------------------------------
            Return strReturn
        End Function
#End Region

    End Class
End Namespace
