﻿Namespace Common
    Public Class Config
#Region "ﾌﾟﾛﾊﾟﾃｨ"
        '''========================================================================================
        ''' <summary>Dirテンポラリ</summary>
        '''========================================================================================
        Private _Dirテンポラリ As String
        Public ReadOnly Property Dirテンポラリ() As String
            Get
                Return _Dirテンポラリ
            End Get
        End Property

        '''========================================================================================
        ''' <summary>Dirテンプレート</summary>
        '''========================================================================================
        Private _Dirテンプレート As String
        Public ReadOnly Property Dirテンプレート() As String
            Get
                Return _Dirテンプレート
            End Get
        End Property

        '''========================================================================================
        ''' <summary>Dirハンコ</summary>
        '''========================================================================================
        Private _Dirハンコ As String
        Public ReadOnly Property Dirハンコ() As String
            Get
                Return _Dirハンコ
            End Get
        End Property

        '''========================================================================================
        ''' <summary>決算月</summary>
        '''========================================================================================
        Private _決算月 As String
        Public ReadOnly Property 決算月() As String
            Get
                Return _決算月
            End Get
        End Property

        '''========================================================================================
        ''' <summary>実働時間</summary>
        '''========================================================================================
        Private _実働時間 As Decimal
        Public ReadOnly Property 実働時間() As Decimal
            Get
                Return _実働時間
            End Get
        End Property

        '''========================================================================================
        ''' <summary>日当</summary>
        '''========================================================================================
        Private _日当 As Decimal
        Public ReadOnly Property 日当() As Decimal
            Get
                Return _日当
            End Get
        End Property

        '''========================================================================================
        ''' <summary>トップ達成率</summary>
        '''========================================================================================
        Private _トップ達成率 As Decimal
        Public ReadOnly Property トップ達成率() As Decimal
            Get
                Return _トップ達成率
            End Get
        End Property

        '''========================================================================================
        ''' <summary>トップ見積期限日数</summary>
        '''========================================================================================
        Private _トップ見積期限日数 As Decimal
        Public ReadOnly Property トップ見積期限日数() As Decimal
            Get
                Return _トップ見積期限日数
            End Get
        End Property

        '''========================================================================================
        ''' <summary>トップ不一致月数</summary>
        '''========================================================================================
        Private _トップ不一致月数 As Decimal
        Public ReadOnly Property トップ不一致月数() As Decimal
            Get
                Return _トップ不一致月数
            End Get
        End Property

        '''========================================================================================
        ''' <summary>有給付与開始月数</summary>
        '''========================================================================================
        Private _有給付与開始月数 As Decimal
        Public ReadOnly Property 有給付与開始月数() As Decimal
            Get
                Return _有給付与開始月数
            End Get
        End Property

        '''========================================================================================
        ''' <summary>桁数単価整数</summary>
        '''========================================================================================
        Private _税率 As Decimal
        Public ReadOnly Property 税率() As Decimal
            Get
                Return _税率
            End Get
        End Property

        '''========================================================================================
        ''' <summary>桁数数量整数</summary>
        '''========================================================================================
        Private _桁数数量整数 As Decimal
        Public ReadOnly Property 桁数数量整数() As Decimal
            Get
                Return _桁数数量整数
            End Get
        End Property

        '''========================================================================================
        ''' <summary>桁数数量小数</summary>
        '''========================================================================================
        Private _桁数数量小数 As Decimal
        Public ReadOnly Property 桁数数量小数() As Decimal
            Get
                Return _桁数数量小数
            End Get
        End Property

        '''========================================================================================
        ''' <summary>桁数単価整数</summary>
        '''========================================================================================
        Private _桁数単価整数 As Decimal
        Public ReadOnly Property 桁数単価整数() As Decimal
            Get
                Return _桁数単価整数
            End Get
        End Property

        '''========================================================================================
        ''' <summary>桁数単価小数</summary>
        '''========================================================================================
        Private _桁数単価小数 As Decimal
        Public ReadOnly Property 桁数単価小数() As Decimal
            Get
                Return _桁数単価小数
            End Get
        End Property

        '''========================================================================================
        ''' <summary>金額整数桁</summary>
        '''========================================================================================
        Private _桁数金額整数 As Decimal
        Public ReadOnly Property 桁数金額整数() As Decimal
            Get
                Return _桁数金額整数
            End Get
        End Property

        '''========================================================================================
        ''' <summary>桁数金額小数</summary>
        '''========================================================================================
        Private _桁数金額小数 As Decimal
        Public ReadOnly Property 桁数金額小数() As Decimal
            Get
                Return _桁数金額小数
            End Get
        End Property
#End Region

#Region "ｺﾝｽﾄﾗｸﾀ"
        '''========================================================================================
        ''' <SUMMARY>ｺﾝｽﾄﾗｸﾀ</SUMMARY>
        '''========================================================================================
        Public Sub New()
            '----------------------------------------------------------------------
            ' Sシステム
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetSystemTableAdapters.SシステムTableAdapter
            Dim tbl As NodeDatabase.DataSetSystem.SシステムDataTable
            Dim qry As New Collection

            ' 読込
            tbl = ada.SelectByCommon(qry)
            If tbl.Count > 0 Then
                Me._Dirテンポラリ = BaseCore.Common.Text.Nz(tbl(0).Item(tbl.DirテンポラリColumn.ColumnName), "")
                Me._Dirテンプレート = BaseCore.Common.Text.Nz(tbl(0).Item(tbl.DirテンプレートColumn.ColumnName), "")
                Me._Dirハンコ = BaseCore.Common.Text.Nz(tbl(0).Item(tbl.DirハンコColumn.ColumnName), "")
                Me._決算月 = BaseCore.Common.Text.Nz(tbl(0).Item(tbl.決算月Column.ColumnName), "")
                Me._実働時間 = BaseCore.Common.Text.Nz(tbl(0).Item(tbl.実働時間Column.ColumnName), 0)
                Me._日当 = BaseCore.Common.Text.Nz(tbl(0).Item(tbl.日当Column.ColumnName), 0)
                Me._トップ達成率 = BaseCore.Common.Text.Nz(tbl(0).Item(tbl.トップ達成率Column.ColumnName), 0)
                Me._トップ見積期限日数 = BaseCore.Common.Text.Nz(tbl(0).Item(tbl.トップ見積期限日数Column.ColumnName), 0)
                Me._トップ不一致月数 = BaseCore.Common.Text.Nz(tbl(0).Item(tbl.トップ不一致月数Column.ColumnName), 0)
                Me._有給付与開始月数 = BaseCore.Common.Text.Nz(tbl(0).Item(tbl.有給付与開始月数Column.ColumnName), 0)
                Me._税率 = tbl(0).Item(tbl.税率Column.ColumnName)
                'Me._桁数数量整数 = BaseCore.Common.Text.Nz(tbl(0).Item(tbl.桁数数量整数Column.ColumnName), 0)
                'Me._桁数数量小数 = BaseCore.Common.Text.Nz(tbl(0).Item(tbl.桁数数量小数Column.ColumnName), 0)
                'Me._桁数単価整数 = BaseCore.Common.Text.Nz(tbl(0).Item(tbl.桁数単価整数Column.ColumnName), 0)
                'Me._桁数単価小数 = BaseCore.Common.Text.Nz(tbl(0).Item(tbl.桁数単価小数Column.ColumnName), 0)
                'Me._桁数金額整数 = BaseCore.Common.Text.Nz(tbl(0).Item(tbl.桁数金額整数Column.ColumnName), 0)
                'Me._桁数金額小数 = BaseCore.Common.Text.Nz(tbl(0).Item(tbl.桁数金額小数Column.ColumnName), 0)
                Me._桁数数量整数 = 12
                Me._桁数数量小数 = 0
                Me._桁数単価整数 = 12
                Me._桁数単価小数 = 0
                Me._桁数金額整数 = 12
                Me._桁数金額小数 = 0

            End If
        End Sub

#Region "ﾃﾞｨﾚｸﾄﾘ変換"
        Public Function Hierarchy(ByVal ディレクトリ As String) As String
            If 0 < ディレクトリ.Length Then
                If ディレクトリ.Substring(ディレクトリ.Length - 1) <> "\" Then
                    Return ディレクトリ + "\"
                End If
            End If
            Return ディレクトリ
        End Function
#End Region

#End Region
    End Class
End Namespace
