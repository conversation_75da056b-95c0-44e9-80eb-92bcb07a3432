﻿Namespace Common
    Public Class JanCode
        '''========================================================================================
        ''' <SUMMARY>Janｺｰﾄﾞ(12桁)からﾁｪｯｸﾃﾞｨｼﾞｯﾄを計算して、13桁のJanｺｰﾄﾞを返す</SUMMARY>
        ''' <PARAM NAME="Code">Janｺｰﾄﾞ(12桁)</PARAM>
        ''' <RETURNS>Janｺｰﾄﾞ(13桁)</RETURNS>
        '''========================================================================================
        Public Shared Function AttachDigit(ByVal Code As String) As String
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim intOdd As Integer = 0       '奇数列
            Dim intEven As Integer = 0      '偶数列
            Dim intTotal As Integer = 0     '合計
            Dim intRem As Integer = 0       '余り
            Dim strDigit As String = ""     'ﾁｪｯｸﾃﾞｨｼﾞｯﾄ

            '----------------------------------------------------------------------
            ' 12桁未満は空白を返す
            '----------------------------------------------------------------------
            If Len(Code) < 12 Then
                Return ""
            End If

            '----------------------------------------------------------------------
            ' 奇数桁と偶数桁をそれぞれ加算
            '----------------------------------------------------------------------
            intOdd = CInt(Code.Substring(0, 1)) + CInt(Code.Substring(2, 1)) + CInt(Code.Substring(4, 1)) + CInt(Code.Substring(6, 1)) + CInt(Code.Substring(8, 1)) + CInt(Code.Substring(10, 1))
            intEven = CInt(Code.Substring(1, 1)) + CInt(Code.Substring(3, 1)) + CInt(Code.Substring(5, 1)) + CInt(Code.Substring(7, 1)) + CInt(Code.Substring(9, 1)) + CInt(Code.Substring(11, 1))

            '----------------------------------------------------------------------
            ' ((奇数桁 × 3) ＋ (偶数桁 × 1))の結果を10で割った余りを求める(intRem)
            '----------------------------------------------------------------------
            intTotal = (intOdd * 1) + (intEven * 3)
            Math.DivRem(intTotal, 10, intRem)

            If intRem = 0 Then
                strDigit = "0"
            Else
                strDigit = CStr(10 - intRem)
            End If

            '----------------------------------------------------------------------
            ' 入力のJanｺｰﾄﾞ(12桁) ＋ ﾁｪｯｸﾃﾞｨｼﾞｯﾄを返す
            '----------------------------------------------------------------------
            Return Code.Substring(0, 12) & strDigit
        End Function
    End Class
End Namespace
