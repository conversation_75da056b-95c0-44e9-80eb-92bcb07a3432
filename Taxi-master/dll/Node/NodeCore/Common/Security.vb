﻿Namespace Common
    Public Class Security
        '''========================================================================================
        ''' <summary>子会社CD</summary>
        '''========================================================================================
        Protected _子会社CD As String = ""

        Public Property 子会社CD() As String
            Get
                Return Me._子会社CD
            End Get
            Set(ByVal value As String)
                Me._子会社CD = value
            End Set
        End Property

        '''========================================================================================
        ''' <summary>グループCD</summary>
        '''========================================================================================
        Protected _グループCD As String = ""

        Public Property グループCD() As String
            Get
                Return Me._グループCD
            End Get
            Set(ByVal value As String)
                Me._グループCD = value
            End Set
        End Property


        '''========================================================================================
        ''' <summary>個社CD</summary>
        '''========================================================================================
        Protected _個社CD As String = ""

        Public Property 個社CD() As String
            Get
                Return Me._個社CD
            End Get
            Set(ByVal value As String)
                Me._個社CD = value
            End Set
        End Property
        '''========================================================================================
        ''' <summary>ユーザーCD</summary>
        '''========================================================================================
        Protected _ユーザーCD As String = ""
        Public ReadOnly Property ユーザーCD() As String
            Get
                Return _ユーザーCD
            End Get
        End Property

        '''========================================================================================
        ''' <summary>ユーザー名</summary>
        '''========================================================================================
        Protected _ユーザー名 As String = ""
        Public ReadOnly Property ユーザー名() As String
            Get
                Return _ユーザー名
            End Get
        End Property

        '''========================================================================================
        ''' <summary>権限区分ID</summary>
        '''========================================================================================
        Protected _権限区分ID As String = ""
        Public ReadOnly Property 権限区分ID() As String
            Get
                Return _権限区分ID
            End Get
        End Property

        '''========================================================================================
        ''' <summary>ｴﾗｰﾒｯｾｰｼﾞ</summary>
        '''========================================================================================
        Protected _LastError As String = ""
        Public Property LastError() As String
            Get
                Return _LastError
            End Get
            Set(ByVal value As String)
                _LastError = value
            End Set
        End Property

        '''========================================================================================
        ''' <summary>ｾｯｼｮﾝID</summary>
        '''========================================================================================
        Private _セッションID As String = ""
        Public Property セッションID() As String
            Get
                Return Me._セッションID
            End Get
            Set(ByVal value As String)
                Me._セッションID = value
            End Set
        End Property

        '''========================================================================================
        ''' <summary>IPアドレス</summary>
        '''========================================================================================
        Private _IPアドレス As String = ""
        Public Property IPアドレス() As String
            Get
                Return Me._IPアドレス
            End Get
            Set(ByVal value As String)
                Me._IPアドレス = value
            End Set
        End Property

        '''========================================================================================
        ''' <summary>モジュールID</summary>
        '''========================================================================================
        Private _モジュールID As String = ""
        Public Property モジュールID() As String
            Get
                Return Me._モジュールID
            End Get
            Set(ByVal value As String)
                Me._モジュールID = value
            End Set
        End Property

        '''========================================================================================
        ''' <summary>モジュール名</summary>
        '''========================================================================================
        Private _モジュール名 As String = ""
        Public Property モジュール名() As String
            Get
                Return Me._モジュール名
            End Get
            Set(ByVal value As String)
                Me._モジュール名 = value
            End Set
        End Property

        '''========================================================================================
        ''' <summary>モジュール名</summary>
        '''========================================================================================
        Private _権限区分 As String = ""
        Public Property 権限区分() As String
            Get
                Return Me._権限区分
            End Get
            Set(ByVal value As String)
                Me._権限区分 = value
            End Set
        End Property

        '''========================================================================================
        ''' <summary>事業開始日</summary>
        '''========================================================================================
        Protected _事業開始日 As String = ""

        Public Property 事業開始日() As String
            Get
                Return Me._事業開始日
            End Get
            Set(ByVal value As String)
                Me._事業開始日 = value
            End Set
        End Property

#Region "ﾒｿｯﾄﾞ"
#Region "Mﾛｸﾞｲﾝ 認証"
        '''========================================================================================
        ''' <summary>ﾕｰｻﾞ認証(True:認証OK、False:認証NG)(※)</summary>
        ''' <param name="AgreeCD">契約CD</param>
        ''' <param name="UserID">ﾕｰｻﾞID</param>
        ''' <param name="Password">ﾊﾟｽﾜｰﾄﾞ</param>
        ''' <returns>True:認証OK、False:認証NG</returns>
        '''========================================================================================
        Public Function AuthUserID(ByVal AgreeCD As String, ByVal UserID As String, ByVal Password As String) As Boolean
            '------------------------------------------------------------------
            ' ﾕｰｻﾞID、ﾊﾟｽﾜｰﾄﾞ 検査
            '------------------------------------------------------------------
            LastError = "正しいユーザIDとパスワードを入力してください。"

            Dim blnReturn As Boolean = False

            Dim ada As New NodeDatabase.DataSetViewTableAdapters.V0ユーザーTableAdapter
            Dim tbl As New NodeDatabase.DataSetView.V0ユーザーDataTable
            Dim qry As New Collection

            Dim ada子会社 As New NodeDatabase.DataSetViewTableAdapters.V0子会社TableAdapter
            Dim tbl子会社 As New NodeDatabase.DataSetView.V0子会社DataTable
            Dim qry子会社 As New Collection

            If AgreeCD = "" Then
                Return blnReturn
            End If

            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl.契約CDColumn.ColumnName, AgreeCD, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.ユーザーCDColumn.ColumnName, UserID, BaseDatabase.Contents.Compare.Equal))
            tbl = ada.SelectByCommon(qry)


            If tbl.Count > 0 Then
                blnReturn = True

                If tbl(0).パスワード <> Password Then
                    blnReturn = False
                End If

                Me._グループCD = tbl(0).グループCD
                Me._個社CD = tbl(0).個社CD
                Me._子会社CD = tbl(0).子会社CD
                Me._ユーザーCD = tbl(0).ユーザーCD.ToLower
                Me._ユーザー名 = tbl(0).ユーザー名
                Me._権限区分 = tbl(0).権限区分

                qry子会社.Clear()
                qry子会社.Add(New BaseDatabase.Condition(tbl子会社.グループCDColumn.ColumnName, tbl(0).グループCD, BaseDatabase.Contents.Compare.Equal))
                qry子会社.Add(New BaseDatabase.Condition(tbl子会社.個社CDColumn.ColumnName, tbl(0).個社CD, BaseDatabase.Contents.Compare.Equal))
                qry子会社.Add(New BaseDatabase.Condition(tbl子会社.子会社CDColumn.ColumnName, tbl(0).子会社CD, BaseDatabase.Contents.Compare.Equal))
                tbl子会社 = ada子会社.SelectByCommon(qry子会社)


                If tbl子会社.Count > 0 Then
                    Me._事業開始日 = If(tbl子会社(0).業務開始日 = "", "16", tbl子会社(0).業務開始日)
                Else
                    Me._事業開始日 = "16"
                End If


            End If

            If blnReturn Then
                Me.LastError = ""
            End If

            '------------------------------------------------------------------
            ' 戻る
            '------------------------------------------------------------------
            Return blnReturn
        End Function
#End Region

#Region "Mﾒﾆｭｰ 認証"

        '''========================================================================================
        ''' <summary>ﾍﾟｰｼﾞ認証(True:認証OK、False:認証NG)(※)</summary>
        ''' <param name="PageID">ﾍﾟｰｼﾞID</param>
        ''' <param name="Role">ﾛｰﾙ</param>
        ''' <returns>True:認証OK、False:認証NG</returns>
        '''========================================================================================
        Public Function AuthPageID(ByVal PageID As String, ByVal Role As String) As Boolean
            '------------------------------------------------------------------
            ' 初期化
            '------------------------------------------------------------------
            Me.LastError = ""
            Return True

            '------------------------------------------------------------------
            ' ﾄｯﾌﾟﾍﾟｰｼﾞ または ﾌｧｲﾝﾀﾞｰ は認証対象外
            '------------------------------------------------------------------
            If (PageID.ToUpper.Contains(NodeContents.Constant.System.MainDir.ToUpper)) And (PageID <> NodeContents.Constant.System.HomeURL) Then
                Dim ada As New NodeDatabase.DataSetViewTableAdapters.V0メニューTableAdapter
                Dim tbl As New NodeDatabase.DataSetView.V0メニューDataTable
                Dim qry As New Collection

                qry.Clear()
                qry.Add(New BaseDatabase.Condition(tbl.URLColumn.ColumnName, PageID, BaseDatabase.Contents.Compare.Equal))
                Select Case Me.権限区分
                    Case "1" : qry.Add(New BaseDatabase.Condition(tbl.ロール1Column.ColumnName, "×", BaseDatabase.Contents.Compare.NotEqual))
                    Case "2" : qry.Add(New BaseDatabase.Condition(tbl.ロール2Column.ColumnName, "×", BaseDatabase.Contents.Compare.NotEqual))
                    Case "3" : qry.Add(New BaseDatabase.Condition(tbl.ロール3Column.ColumnName, "×", BaseDatabase.Contents.Compare.NotEqual))
                    Case "4" : qry.Add(New BaseDatabase.Condition(tbl.ロール4Column.ColumnName, "×", BaseDatabase.Contents.Compare.NotEqual))
                    Case "5" : qry.Add(New BaseDatabase.Condition(tbl.ロール5Column.ColumnName, "×", BaseDatabase.Contents.Compare.NotEqual))
                    Case "6" : qry.Add(New BaseDatabase.Condition(tbl.ロール6Column.ColumnName, "×", BaseDatabase.Contents.Compare.NotEqual))
                    Case "7" : qry.Add(New BaseDatabase.Condition(tbl.ロール7Column.ColumnName, "×", BaseDatabase.Contents.Compare.NotEqual))
                    Case "8" : qry.Add(New BaseDatabase.Condition(tbl.ロール8Column.ColumnName, "×", BaseDatabase.Contents.Compare.NotEqual))
                    Case "9" : qry.Add(New BaseDatabase.Condition(tbl.ロール9Column.ColumnName, "×", BaseDatabase.Contents.Compare.NotEqual))
                End Select
                tbl = ada.SelectByCommon(qry)

                If tbl.Rows.Count = 0 Then
                    Me.LastError = "不正アクセスです。(Page=" & PageID & ", User=" & Me.ユーザーCD & ")"
                End If
            End If

            '------------------------------------------------------------------
            ' 戻る
            '------------------------------------------------------------------
            Return (Me.LastError = "")
        End Function
#End Region
#End Region

#Region "ﾛｸﾞ出力"
        '''========================================================================================
        ''' <summary>ﾛｸﾞを出力する(※)</summary>
        ''' <param name="表題">ﾀｲﾄﾙ</param>
        ''' <param name="本文">本文</param>
        '''========================================================================================
        Public Sub WriteLog(ByVal 表題 As String,
                            ByVal 本文 As String)
            Dim ada As New NodeDatabase.DataSetTranTableAdapters.TログTableAdapter
            Dim tbl As New NodeDatabase.DataSetTran.TログDataTable
            Dim qry As New Collection

            '------------------------------------------------------------------
            ' ﾛｸﾞを記録(Definesで「ﾛｸﾞ出力する」の指定の時のみ)
            '------------------------------------------------------------------
            Try
                ada.Insert(Format(Now, "yyyy/MM/dd"),
                           Format(Now, "HH:mm:ss"),
                           Me.ユーザーCD,
                           Me.ユーザー名,
                           Me.IPアドレス,
                           Me.モジュールID,
                           Me.モジュール名,
                           表題,
                           本文)
            Catch ex As Exception
            End Try
        End Sub
#End Region
    End Class
End Namespace
