﻿Imports System.Globalization

Namespace Frame.Master
    Partial Public Class Crew
        Inherits NodeCore.Common.Frame

#Region "ｺﾝｽﾀﾝﾄ"
        Public Class ConstantExcel
            Enum DAYS
                月
                火
                水
                木
                金
                土
                日
            End Enum 'DAYS

            Public Class Header
                Public Const SheetName = "乗務員データ出力"
                Public Const RowTop = 1  '一行目スタート
                Public Const 年セル = "$B$2"
                Public Const 月セル = "$D$2"
                Public Const 日セル = "$F$2"

                Public Class 列数
                    Public Const 乗務員名 = 0
                    Public Const 社員NO = 1
                    Public Const 乗務員CD = 2
                    Public Const 勤務体系 = 3
                    Public Const 住所_都道府県 = 4
                    Public Const 住所_市町村 = 5
                    Public Const 住所_地区 = 6
                    Public Const 電話番号 = 7
                    Public Const 電話番号2 = 8
                    Public Const 緊急連絡先1 = 9
                    Public Const 続柄1 = 10
                    Public Const 緊急連絡先2 = 11
                    Public Const 続柄2 = 12
                    Public Const 生年月日 = 13
                    Public Const 入社年月日 = 14
                    Public Const 基準日 = 15
                    Public Const 作業領域1 = 16
                    Public Const 作業領域2 = 17
                    Public Const 年齢 = 18
                    Public Const 勤続 = 19
                    Public Const 現状通勤 = 20
                    Public Const 通勤時間 = 21
                    Public Const 家族構成 = 22
                    Public Const 扶養者数 = 23
                    Public Const 備考 = 24
                    Public Const 健康診断受診日 = 25
                    Public Const 汎用項目1 = 26
                    Public Const 汎用項目2 = 27
                    Public Const 汎用項目3 = 28
                    Public Const 汎用項目4 = 29
                    Public Const 汎用項目5 = 30
                    Public Const 汎用項目6 = 31
                    Public Const 汎用項目7 = 32
                    Public Const 汎用項目8 = 33
                    Public Const 汎用項目9 = 34
                    Public Const 汎用項目10 = 35
                    Public Const 汎用項目11 = 36
                    Public Const 汎用項目12 = 37
                    Public Const 汎用項目13 = 38
                    Public Const 汎用項目14 = 39
                    Public Const 汎用項目15 = 40
                    Public Const 汎用項目16 = 41
                    Public Const 汎用項目17 = 42
                    Public Const 汎用項目18 = 43
                    Public Const 汎用項目19 = 44
                    Public Const 汎用項目20 = 45
                    Public Const 登録ユーザーCD = 46
                    Public Const 登録ユーザー名 = 47
                    Public Const 登録日時 = 48
                    Public Const 更新ユーザーCD = 49
                    Public Const 更新ユーザー名 = 50
                    Public Const 更新日時 = 51

                End Class

            End Class

        End Class


#End Region

#Region "ﾌｨｰﾙﾄﾞ定義"
        Public Class Header
            ''' ----------------------------------------------------------------------------------------
            ''' <SUMMARY>列項目(基本)</SUMMARY>
            ''' ----------------------------------------------------------------------------------------
            Public グループCD As BaseCore.Common.Field.ItemData
            Public 個社CD As BaseCore.Common.Field.ItemData
            Public 子会社CD As BaseCore.Common.Field.ItemData
            Public 子会社名_旧 As BaseCore.Common.Field.ItemData
            Public 子会社住所_旧 As BaseCore.Common.Field.ItemData
            Public 乗務員CD As BaseCore.Common.Field.ItemData
            Public 乗務員氏 As BaseCore.Common.Field.ItemData
            Public 乗務員氏_新 As BaseCore.Common.Field.ItemData
            Public 乗務員氏フリガナ As BaseCore.Common.Field.ItemData
            Public 乗務員氏フリガナ_新 As BaseCore.Common.Field.ItemData
            Public 乗務員名 As BaseCore.Common.Field.ItemData
            Public 乗務員名_新 As BaseCore.Common.Field.ItemData
            Public 乗務員名フリガナ As BaseCore.Common.Field.ItemData
            Public 乗務員名フリガナ_新 As BaseCore.Common.Field.ItemData
            Public 所属 As BaseCore.Common.Field.ItemData
            Public 職種 As BaseCore.Common.Field.ItemData
            Public ID As BaseCore.Common.Field.ItemData
            Public 車両番号 As BaseCore.Common.Field.ItemData
            Public 運転免許証の番号 As BaseCore.Common.Field.ItemData
            Public 運転免許証の番号_新 As BaseCore.Common.Field.ItemData
            Public 運転免許証の有効期限 As BaseCore.Common.Field.ItemData
            Public 運転免許証の有効期限_新 As BaseCore.Common.Field.ItemData
            Public 運転免許の種類 As BaseCore.Common.Field.ItemData
            Public 運転免許の種類_新 As BaseCore.Common.Field.ItemData
            Public 勤務区分 As BaseCore.Common.Field.ItemData
            Public シフト区分 As BaseCore.Common.Field.ItemData
            Public 入社年月日 As BaseCore.Common.Field.ItemData
            Public 退社年月日 As BaseCore.Common.Field.ItemData
            Public 社員NO As BaseCore.Common.Field.ItemData
            Public 勤務体系 As BaseCore.Common.Field.ItemData
            Public 住所_都道府県 As BaseCore.Common.Field.ItemData
            Public 住所_都道府県_新 As BaseCore.Common.Field.ItemData
            Public 住所_都道府県フリガナ As BaseCore.Common.Field.ItemData
            Public 住所_都道府県フリガナ_新 As BaseCore.Common.Field.ItemData
            Public 住所_市町村 As BaseCore.Common.Field.ItemData
            Public 住所_市町村_新 As BaseCore.Common.Field.ItemData
            Public 住所_市町村フリガナ As BaseCore.Common.Field.ItemData
            Public 住所_市町村フリガナ_新 As BaseCore.Common.Field.ItemData
            Public 住所_地区 As BaseCore.Common.Field.ItemData
            Public 住所_地区_新 As BaseCore.Common.Field.ItemData
            Public 住所_地区フリガナ As BaseCore.Common.Field.ItemData
            Public 住所_地区フリガナ_新 As BaseCore.Common.Field.ItemData
            Public 電話番号 As BaseCore.Common.Field.ItemData
            Public 電話番号2 As BaseCore.Common.Field.ItemData
            Public 緊急連絡先1 As BaseCore.Common.Field.ItemData
            Public 続柄1 As BaseCore.Common.Field.ItemData
            Public 緊急連絡先2 As BaseCore.Common.Field.ItemData
            Public 続柄2 As BaseCore.Common.Field.ItemData
            Public 生年月日 As BaseCore.Common.Field.ItemData
            Public 現状通勤 As BaseCore.Common.Field.ItemData
            Public 通勤時間 As BaseCore.Common.Field.ItemData
            Public 家族構成 As BaseCore.Common.Field.ItemData
            Public 扶養者数 As BaseCore.Common.Field.ItemData
            Public 備考 As BaseCore.Common.Field.ItemData
            Public 健康診断受診日 As BaseCore.Common.Field.ItemData
            Public 汎用項目1 As BaseCore.Common.Field.ItemData
            Public 汎用項目2 As BaseCore.Common.Field.ItemData
            Public 汎用項目3 As BaseCore.Common.Field.ItemData
            Public 汎用項目4 As BaseCore.Common.Field.ItemData
            Public 汎用項目5 As BaseCore.Common.Field.ItemData
            Public 汎用項目6 As BaseCore.Common.Field.ItemData
            Public 汎用項目7 As BaseCore.Common.Field.ItemData
            Public 汎用項目8 As BaseCore.Common.Field.ItemData
            Public 汎用項目9 As BaseCore.Common.Field.ItemData
            Public 汎用項目10 As BaseCore.Common.Field.ItemData
            Public 汎用項目11 As BaseCore.Common.Field.ItemData
            Public 汎用項目12 As BaseCore.Common.Field.ItemData
            Public 汎用項目13 As BaseCore.Common.Field.ItemData
            Public 汎用項目14 As BaseCore.Common.Field.ItemData
            Public 汎用項目15 As BaseCore.Common.Field.ItemData
            Public 汎用項目16 As BaseCore.Common.Field.ItemData
            Public 汎用項目17 As BaseCore.Common.Field.ItemData
            Public 汎用項目18 As BaseCore.Common.Field.ItemData
            Public 汎用項目19 As BaseCore.Common.Field.ItemData
            Public 汎用項目20 As BaseCore.Common.Field.ItemData
            Public 登録ユーザーCD As BaseCore.Common.Field.ItemData
            Public 登録ユーザー名 As BaseCore.Common.Field.ItemData
            Public 登録日時 As BaseCore.Common.Field.ItemData
            Public 更新ユーザーCD As BaseCore.Common.Field.ItemData
            Public 更新ユーザー名 As BaseCore.Common.Field.ItemData
            Public 更新日時 As BaseCore.Common.Field.ItemData
            Public 登録番号 As BaseCore.Common.Field.ItemData
            Public 様式選択 As BaseCore.Common.Field.ItemData
            Public ChkMenkyoNo As BaseCore.Common.Field.ItemData
            Public ChkMenkyoExpiryDate As BaseCore.Common.Field.ItemData
            Public ChkMenkyoType As BaseCore.Common.Field.ItemData
            Public ChkName As BaseCore.Common.Field.ItemData
            Public ChkAddress As BaseCore.Common.Field.ItemData
            Public ChkJIgyousyo As BaseCore.Common.Field.ItemData



            '''========================================================================================
            ''' <SUMMARY>ｺﾝｽﾄﾗｸﾀ</SUMMARY>
            '''========================================================================================
            Public Sub New(ByVal Config As NodeCore.Common.Config)
                Dim tbl As New NodeDatabase.DataSetView.V0乗務員DataTable

                '----------------------------------------------------------------------
                ' ｲﾝｽﾀﾝｽ(基本) 
                '----------------------------------------------------------------------
                Me.グループCD = New BaseCore.Common.Field.ItemData(tbl.グループCDColumn)
                Me.個社CD = New BaseCore.Common.Field.ItemData(tbl.個社CDColumn)
                Me.子会社CD = New BaseCore.Common.Field.ItemData(tbl.子会社CDColumn)
                Me.子会社名_旧 = New BaseCore.Common.Field.ItemData(tbl.子会社名_旧Column)
                Me.子会社住所_旧 = New BaseCore.Common.Field.ItemData(tbl.子会社住所_旧Column)
                Me.乗務員CD = New BaseCore.Common.Field.ItemData(tbl.乗務員CDColumn)
                Me.乗務員氏 = New BaseCore.Common.Field.ItemData(tbl.乗務員氏Column)
                Me.乗務員氏_新 = New BaseCore.Common.Field.ItemData(tbl.乗務員氏_新Column)
                Me.乗務員氏フリガナ = New BaseCore.Common.Field.ItemData(tbl.乗務員氏フリガナColumn)
                Me.乗務員氏フリガナ_新 = New BaseCore.Common.Field.ItemData(tbl.乗務員氏フリガナ_新Column)
                Me.乗務員名 = New BaseCore.Common.Field.ItemData(tbl.乗務員名Column)
                Me.乗務員名_新 = New BaseCore.Common.Field.ItemData(tbl.乗務員名_新Column)
                Me.乗務員名フリガナ = New BaseCore.Common.Field.ItemData(tbl.乗務員名フリガナColumn)
                Me.乗務員名フリガナ_新 = New BaseCore.Common.Field.ItemData(tbl.乗務員名フリガナ_新Column)
                Me.所属 = New BaseCore.Common.Field.ItemData(tbl.所属Column)
                Me.職種 = New BaseCore.Common.Field.ItemData(tbl.職種Column)
                Me.ID = New BaseCore.Common.Field.ItemData(tbl.IDColumn)
                Me.車両番号 = New BaseCore.Common.Field.ItemData(tbl.車両番号Column)
                Me.運転免許証の番号 = New BaseCore.Common.Field.ItemData(tbl.運転免許証の番号Column)
                Me.運転免許証の番号_新 = New BaseCore.Common.Field.ItemData(tbl.運転免許証の番号_新Column)
                Me.運転免許証の有効期限 = New BaseCore.Common.Field.ItemData(tbl.運転免許証の有効期限Column)
                Me.運転免許証の有効期限_新 = New BaseCore.Common.Field.ItemData(tbl.運転免許証の有効期限_新Column)
                Me.運転免許の種類 = New BaseCore.Common.Field.ItemData(tbl.運転免許の種類Column)
                Me.運転免許の種類_新 = New BaseCore.Common.Field.ItemData(tbl.運転免許の種類_新Column)
                Me.勤務区分 = New BaseCore.Common.Field.ItemData(tbl.勤務区分Column)
                Me.シフト区分 = New BaseCore.Common.Field.ItemData(tbl.シフト区分Column)
                Me.入社年月日 = New BaseCore.Common.Field.ItemData(tbl.入社年月日Column)
                Me.退社年月日 = New BaseCore.Common.Field.ItemData(tbl.退社年月日Column)
                Me.社員NO = New BaseCore.Common.Field.ItemData(tbl.社員NOColumn)
                Me.勤務体系 = New BaseCore.Common.Field.ItemData(tbl.勤務体系Column)
                Me.住所_都道府県 = New BaseCore.Common.Field.ItemData(tbl.住所_都道府県Column)
                Me.住所_都道府県_新 = New BaseCore.Common.Field.ItemData(tbl.住所_都道府県_新Column)
                Me.住所_都道府県フリガナ = New BaseCore.Common.Field.ItemData(tbl.住所_都道府県フリガナColumn)
                Me.住所_都道府県フリガナ_新 = New BaseCore.Common.Field.ItemData(tbl.住所_都道府県フリガナ_新Column)
                Me.住所_市町村 = New BaseCore.Common.Field.ItemData(tbl.住所_市町村Column)
                Me.住所_市町村_新 = New BaseCore.Common.Field.ItemData(tbl.住所_市町村_新Column)
                Me.住所_市町村フリガナ = New BaseCore.Common.Field.ItemData(tbl.住所_市町村フリガナColumn)
                Me.住所_市町村フリガナ_新 = New BaseCore.Common.Field.ItemData(tbl.住所_市町村フリガナ_新Column)
                Me.住所_地区 = New BaseCore.Common.Field.ItemData(tbl.住所_地区Column)
                Me.住所_地区_新 = New BaseCore.Common.Field.ItemData(tbl.住所_地区_新Column)
                Me.住所_地区フリガナ = New BaseCore.Common.Field.ItemData(tbl.住所_地区フリガナColumn)
                Me.住所_地区フリガナ_新 = New BaseCore.Common.Field.ItemData(tbl.住所_地区フリガナ_新Column)
                Me.電話番号 = New BaseCore.Common.Field.ItemData(tbl.電話番号Column)
                Me.電話番号2 = New BaseCore.Common.Field.ItemData(tbl.電話番号2Column)
                Me.緊急連絡先1 = New BaseCore.Common.Field.ItemData(tbl.緊急連絡先1Column)
                Me.続柄1 = New BaseCore.Common.Field.ItemData(tbl.続柄1Column)
                Me.緊急連絡先2 = New BaseCore.Common.Field.ItemData(tbl.緊急連絡先2Column)
                Me.続柄2 = New BaseCore.Common.Field.ItemData(tbl.続柄2Column)
                Me.生年月日 = New BaseCore.Common.Field.ItemData(tbl.生年月日Column)
                Me.現状通勤 = New BaseCore.Common.Field.ItemData(tbl.現状通勤Column)
                Me.通勤時間 = New BaseCore.Common.Field.ItemData(tbl.通勤時間Column)
                Me.家族構成 = New BaseCore.Common.Field.ItemData(tbl.家族構成Column)
                Me.扶養者数 = New BaseCore.Common.Field.ItemData(tbl.扶養者数Column)
                Me.備考 = New BaseCore.Common.Field.ItemData(tbl.備考Column)
                Me.健康診断受診日 = New BaseCore.Common.Field.ItemData(tbl.健康診断受診日Column)
                Me.汎用項目1 = New BaseCore.Common.Field.ItemData(tbl.汎用項目1Column)
                Me.汎用項目2 = New BaseCore.Common.Field.ItemData(tbl.汎用項目2Column)
                Me.汎用項目3 = New BaseCore.Common.Field.ItemData(tbl.汎用項目3Column)
                Me.汎用項目4 = New BaseCore.Common.Field.ItemData(tbl.汎用項目4Column)
                Me.汎用項目5 = New BaseCore.Common.Field.ItemData(tbl.汎用項目5Column)
                Me.汎用項目6 = New BaseCore.Common.Field.ItemData(tbl.汎用項目6Column)
                Me.汎用項目7 = New BaseCore.Common.Field.ItemData(tbl.汎用項目7Column)
                Me.汎用項目8 = New BaseCore.Common.Field.ItemData(tbl.汎用項目8Column)
                Me.汎用項目9 = New BaseCore.Common.Field.ItemData(tbl.汎用項目9Column)
                Me.汎用項目10 = New BaseCore.Common.Field.ItemData(tbl.汎用項目10Column)
                Me.汎用項目11 = New BaseCore.Common.Field.ItemData(tbl.汎用項目11Column)
                Me.汎用項目12 = New BaseCore.Common.Field.ItemData(tbl.汎用項目12Column)
                Me.汎用項目13 = New BaseCore.Common.Field.ItemData(tbl.汎用項目13Column)
                Me.汎用項目14 = New BaseCore.Common.Field.ItemData(tbl.汎用項目14Column)
                Me.汎用項目15 = New BaseCore.Common.Field.ItemData(tbl.汎用項目15Column)
                Me.汎用項目16 = New BaseCore.Common.Field.ItemData(tbl.汎用項目16Column)
                Me.汎用項目17 = New BaseCore.Common.Field.ItemData(tbl.汎用項目17Column)
                Me.汎用項目18 = New BaseCore.Common.Field.ItemData(tbl.汎用項目18Column)
                Me.汎用項目19 = New BaseCore.Common.Field.ItemData(tbl.汎用項目19Column)
                Me.汎用項目20 = New BaseCore.Common.Field.ItemData(tbl.汎用項目20Column)
                Me.登録ユーザーCD = New BaseCore.Common.Field.ItemData(tbl.登録ユーザーCDColumn)
                Me.登録ユーザー名 = New BaseCore.Common.Field.ItemData(tbl.登録ユーザー名Column)
                Me.登録日時 = New BaseCore.Common.Field.ItemData(tbl.登録日時Column)
                Me.更新ユーザーCD = New BaseCore.Common.Field.ItemData(tbl.更新ユーザーCDColumn)
                Me.更新ユーザー名 = New BaseCore.Common.Field.ItemData(tbl.更新ユーザー名Column)
                Me.更新日時 = New BaseCore.Common.Field.ItemData(tbl.更新日時Column)
                Me.登録番号 = New BaseCore.Common.Field.ItemData(tbl.更新日時Column)
                Me.様式選択 = New BaseCore.Common.Field.ItemData("様式選択", TypeCode.String, 1)
                Me.ChkMenkyoNo = New BaseCore.Common.Field.ItemData("ChkMenkyoNo", TypeCode.Boolean)
                Me.ChkMenkyoExpiryDate = New BaseCore.Common.Field.ItemData("ChkMenkyoExpiryDate", TypeCode.Boolean)
                Me.ChkMenkyoType = New BaseCore.Common.Field.ItemData("ChkMenkyoType", TypeCode.Boolean)
                Me.ChkName = New BaseCore.Common.Field.ItemData("ChkName", TypeCode.Boolean)
                Me.ChkAddress = New BaseCore.Common.Field.ItemData("ChkAddress", TypeCode.Boolean)
                Me.ChkJIgyousyo = New BaseCore.Common.Field.ItemData("ChkJIgyousyo", TypeCode.Boolean)

            End Sub
        End Class
#End Region

#Region "変数定義"
        Public Class Paramater
            Public Header As Header
        End Class

        '''========================================================================================
        ''' <summary>ﾍｯﾀﾞ</summary>
        '''========================================================================================
        Private _Fields As New Paramater
        Public Property Fields() As Paramater
            Get
                Return _Fields
            End Get

            Set(ByVal value As Paramater)
                _Fields = value
            End Set
        End Property
#End Region

#Region "ｺﾝｽﾄﾗｸﾀ"
        '''========================================================================================
        ''' <SUMMARY>ｺﾝｽﾄﾗｸﾀ</SUMMARY>
        ''' <PARAM name="Security">ｾｷｭﾘﾃｨ ｸﾗｽ</PARAM>
        ''' <PARAM name="Config">ｺﾝﾌｨｸﾞ ｸﾗｽ</PARAM>
        ''' <PARAM name="PagePath">ﾍﾟｰｼﾞﾊﾟｽ</PARAM>
        '''========================================================================================
        Public Sub New(ByVal Security As NodeCore.Common.Security, ByVal Config As NodeCore.Common.Config, ByVal PagePath As String)
            '----------------------------------------------------------------------
            ' ｾｷｭﾘﾃｨ 設定
            '----------------------------------------------------------------------
            MyBase.New(Security, Config, PagePath)

            Me.Fields.Header = New Header(Config)
        End Sub
#End Region

#Region "ﾍｯﾀﾞ"
#Region "Clear"
        '''========================================================================================
        ''' <SUMMARY>ｸﾘｱ</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Clear_Header(Optional ByVal KeyClear As Boolean = False) As Boolean
            '----------------------------------------------------------------------
            ' ｷｰ部ｸﾘｱ
            '----------------------------------------------------------------------
            If KeyClear Then
                Me.Fields.Header.グループCD.Value = Nothing
                Me.Fields.Header.個社CD.Value = Nothing
                Me.Fields.Header.子会社CD.Value = Nothing
                Me.Fields.Header.乗務員CD.Value = Nothing
                Me.Fields.Header.様式選択.Value = NodeContents.Constant.Logic.Crew.様式選択.雇用条件及び雇用契約証明書
            End If

            '----------------------------------------------------------------------
            ' ﾃﾞｰﾀ部ｸﾘｱ
            '----------------------------------------------------------------------
            Me.Fields.Header.子会社名_旧.Value = Nothing
            Me.Fields.Header.子会社住所_旧.Value = Nothing
            Me.Fields.Header.乗務員氏.Value = Nothing
            Me.Fields.Header.乗務員氏_新.Value = Nothing
            Me.Fields.Header.乗務員氏フリガナ.Value = Nothing
            Me.Fields.Header.乗務員氏フリガナ_新.Value = Nothing
            Me.Fields.Header.乗務員名.Value = Nothing
            Me.Fields.Header.乗務員名_新.Value = Nothing
            Me.Fields.Header.乗務員名フリガナ.Value = Nothing
            Me.Fields.Header.乗務員名フリガナ_新.Value = Nothing
            Me.Fields.Header.所属.Value = Nothing
            Me.Fields.Header.職種.Value = Nothing
            Me.Fields.Header.ID.Value = Nothing
            Me.Fields.Header.車両番号.Value = Nothing
            Me.Fields.Header.運転免許証の番号.Value = Nothing
            Me.Fields.Header.運転免許証の番号_新.Value = Nothing
            Me.Fields.Header.運転免許証の有効期限.Value = Nothing
            Me.Fields.Header.運転免許証の有効期限_新.Value = Nothing
            Me.Fields.Header.運転免許の種類.Value = Nothing
            Me.Fields.Header.運転免許の種類_新.Value = Nothing
            Me.Fields.Header.勤務区分.Value = Nothing
            Me.Fields.Header.シフト区分.Value = Nothing
            Me.Fields.Header.入社年月日.Value = Nothing
            Me.Fields.Header.退社年月日.Value = Nothing
            Me.Fields.Header.社員NO.Value = Nothing
            Me.Fields.Header.勤務体系.Value = Nothing
            Me.Fields.Header.住所_都道府県.Value = Nothing
            Me.Fields.Header.住所_都道府県_新.Value = Nothing
            Me.Fields.Header.住所_都道府県フリガナ.Value = Nothing
            Me.Fields.Header.住所_都道府県フリガナ_新.Value = Nothing
            Me.Fields.Header.住所_市町村.Value = Nothing
            Me.Fields.Header.住所_市町村_新.Value = Nothing
            Me.Fields.Header.住所_市町村フリガナ.Value = Nothing
            Me.Fields.Header.住所_市町村フリガナ_新.Value = Nothing
            Me.Fields.Header.住所_地区.Value = Nothing
            Me.Fields.Header.住所_地区_新.Value = Nothing
            Me.Fields.Header.住所_地区フリガナ.Value = Nothing
            Me.Fields.Header.住所_地区フリガナ_新.Value = Nothing
            Me.Fields.Header.電話番号.Value = Nothing
            Me.Fields.Header.電話番号2.Value = Nothing
            Me.Fields.Header.緊急連絡先1.Value = Nothing
            Me.Fields.Header.続柄1.Value = Nothing
            Me.Fields.Header.緊急連絡先2.Value = Nothing
            Me.Fields.Header.続柄2.Value = Nothing
            Me.Fields.Header.生年月日.Value = Nothing
            Me.Fields.Header.現状通勤.Value = Nothing
            Me.Fields.Header.通勤時間.Value = Nothing
            Me.Fields.Header.家族構成.Value = Nothing
            Me.Fields.Header.扶養者数.Value = Nothing
            Me.Fields.Header.備考.Value = Nothing
            Me.Fields.Header.健康診断受診日.Value = Nothing
            Me.Fields.Header.汎用項目1.Value = Nothing
            Me.Fields.Header.汎用項目2.Value = Nothing
            Me.Fields.Header.汎用項目3.Value = Nothing
            Me.Fields.Header.汎用項目4.Value = Nothing
            Me.Fields.Header.汎用項目5.Value = Nothing
            Me.Fields.Header.汎用項目6.Value = Nothing
            Me.Fields.Header.汎用項目7.Value = Nothing
            Me.Fields.Header.汎用項目8.Value = Nothing
            Me.Fields.Header.汎用項目9.Value = Nothing
            Me.Fields.Header.汎用項目10.Value = Nothing
            Me.Fields.Header.汎用項目11.Value = Nothing
            Me.Fields.Header.汎用項目12.Value = Nothing
            Me.Fields.Header.汎用項目13.Value = Nothing
            Me.Fields.Header.汎用項目14.Value = Nothing
            Me.Fields.Header.汎用項目15.Value = Nothing
            Me.Fields.Header.汎用項目16.Value = Nothing
            Me.Fields.Header.汎用項目17.Value = Nothing
            Me.Fields.Header.汎用項目18.Value = Nothing
            Me.Fields.Header.汎用項目19.Value = Nothing
            Me.Fields.Header.汎用項目20.Value = Nothing
            Me.Fields.Header.登録ユーザーCD.Value = Nothing
            Me.Fields.Header.登録ユーザー名.Value = Nothing
            Me.Fields.Header.登録日時.Value = Nothing
            Me.Fields.Header.更新ユーザーCD.Value = Nothing
            Me.Fields.Header.更新ユーザー名.Value = Nothing
            Me.Fields.Header.更新日時.Value = Nothing
            Me.Fields.Header.登録番号.Value = Nothing

            '----------------------------------------------------------------------
            ' ﾃﾞｰﾀ部ｸﾘｱ
            '----------------------------------------------------------------------
            'Me.Read()

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "Read"
        '''========================================================================================
        ''' <SUMMARY>読込</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Read_Header() As Boolean
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetViewTableAdapters.V0乗務員TableAdapter
            Dim tbl As New NodeDatabase.DataSetView.V0乗務員DataTable

            Dim qry As New Collection

            '----------------------------------------------------------------------
            ' 条件設定
            '----------------------------------------------------------------------
            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, Me.Fields.Header.グループCD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, Me.Fields.Header.個社CD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.子会社CDColumn.ColumnName, Me.Fields.Header.子会社CD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.乗務員CDColumn.ColumnName, Me.Fields.Header.乗務員CD.Value, BaseDatabase.Contents.Compare.Equal))

            '----------------------------------------------------------------------
            ' 読込
            '----------------------------------------------------------------------
            tbl = ada.SelectByCommon(qry)
            If tbl.Count > 0 Then
                'Me.Fields.Header.乗務員CD.Value = tbl(0).Item(tbl.乗務員CDColumn.ColumnName)
                Me.Fields.Header.子会社名_旧.Value = tbl(0).Item(tbl.子会社名_旧Column.ColumnName)
                Me.Fields.Header.子会社住所_旧.Value = tbl(0).Item(tbl.子会社住所_旧Column.ColumnName)
                Me.Fields.Header.乗務員氏.Value = tbl(0).Item(tbl.乗務員氏Column.ColumnName)
                Me.Fields.Header.乗務員氏_新.Value = tbl(0).Item(tbl.乗務員氏_新Column.ColumnName)
                Me.Fields.Header.乗務員氏フリガナ.Value = tbl(0).Item(tbl.乗務員氏フリガナColumn.ColumnName)
                Me.Fields.Header.乗務員氏フリガナ_新.Value = tbl(0).Item(tbl.乗務員氏フリガナ_新Column.ColumnName)
                Me.Fields.Header.乗務員名.Value = tbl(0).Item(tbl.乗務員名Column.ColumnName)
                Me.Fields.Header.乗務員名_新.Value = tbl(0).Item(tbl.乗務員名_新Column.ColumnName)
                Me.Fields.Header.乗務員名フリガナ.Value = tbl(0).Item(tbl.乗務員名フリガナColumn.ColumnName)
                Me.Fields.Header.乗務員名フリガナ_新.Value = tbl(0).Item(tbl.乗務員名フリガナ_新Column.ColumnName)
                Me.Fields.Header.所属.Value = tbl(0).Item(tbl.所属Column.ColumnName)
                Me.Fields.Header.職種.Value = tbl(0).Item(tbl.職種Column.ColumnName)
                Me.Fields.Header.ID.Value = tbl(0).Item(tbl.IDColumn.ColumnName)
                Me.Fields.Header.車両番号.Value = tbl(0).Item(tbl.車両番号Column.ColumnName)
                Me.Fields.Header.運転免許証の番号.Value = tbl(0).Item(tbl.運転免許証の番号Column.ColumnName)
                Me.Fields.Header.運転免許証の番号_新.Value = tbl(0).Item(tbl.運転免許証の番号_新Column.ColumnName)
                Me.Fields.Header.運転免許証の有効期限.Value = tbl(0).Item(tbl.運転免許証の有効期限Column.ColumnName)
                Me.Fields.Header.運転免許証の有効期限_新.Value = tbl(0).Item(tbl.運転免許証の有効期限_新Column.ColumnName)
                Me.Fields.Header.運転免許の種類.Value = tbl(0).Item(tbl.運転免許の種類Column.ColumnName)
                Me.Fields.Header.運転免許の種類_新.Value = tbl(0).Item(tbl.運転免許の種類_新Column.ColumnName)
                Me.Fields.Header.勤務区分.Value = tbl(0).Item(tbl.勤務区分Column.ColumnName)
                Me.Fields.Header.シフト区分.Value = tbl(0).Item(tbl.シフト区分Column.ColumnName)
                Me.Fields.Header.入社年月日.Value = tbl(0).Item(tbl.入社年月日Column.ColumnName)
                Me.Fields.Header.退社年月日.Value = tbl(0).Item(tbl.退社年月日Column.ColumnName)
                Me.Fields.Header.社員NO.Value = tbl(0).Item(tbl.社員NOColumn.ColumnName)
                Me.Fields.Header.勤務体系.Value = tbl(0).Item(tbl.勤務体系Column.ColumnName)
                Me.Fields.Header.住所_都道府県.Value = tbl(0).Item(tbl.住所_都道府県Column.ColumnName)
                Me.Fields.Header.住所_都道府県_新.Value = tbl(0).Item(tbl.住所_都道府県_新Column.ColumnName)
                Me.Fields.Header.住所_都道府県フリガナ.Value = tbl(0).Item(tbl.住所_都道府県フリガナColumn.ColumnName)
                Me.Fields.Header.住所_都道府県フリガナ_新.Value = tbl(0).Item(tbl.住所_都道府県フリガナ_新Column.ColumnName)
                Me.Fields.Header.住所_市町村.Value = tbl(0).Item(tbl.住所_市町村Column.ColumnName)
                Me.Fields.Header.住所_市町村_新.Value = tbl(0).Item(tbl.住所_市町村_新Column.ColumnName)
                Me.Fields.Header.住所_市町村フリガナ.Value = tbl(0).Item(tbl.住所_市町村フリガナColumn.ColumnName)
                Me.Fields.Header.住所_市町村フリガナ_新.Value = tbl(0).Item(tbl.住所_市町村フリガナ_新Column.ColumnName)
                Me.Fields.Header.住所_地区.Value = tbl(0).Item(tbl.住所_地区Column.ColumnName)
                Me.Fields.Header.住所_地区_新.Value = tbl(0).Item(tbl.住所_地区_新Column.ColumnName)
                Me.Fields.Header.住所_地区フリガナ.Value = tbl(0).Item(tbl.住所_地区フリガナColumn.ColumnName)
                Me.Fields.Header.住所_地区フリガナ_新.Value = tbl(0).Item(tbl.住所_地区フリガナ_新Column.ColumnName)
                Me.Fields.Header.電話番号.Value = tbl(0).Item(tbl.電話番号Column.ColumnName)
                Me.Fields.Header.電話番号2.Value = tbl(0).Item(tbl.電話番号2Column.ColumnName)
                Me.Fields.Header.緊急連絡先1.Value = tbl(0).Item(tbl.緊急連絡先1Column.ColumnName)
                Me.Fields.Header.続柄1.Value = tbl(0).Item(tbl.続柄1Column.ColumnName)
                Me.Fields.Header.緊急連絡先2.Value = tbl(0).Item(tbl.緊急連絡先2Column.ColumnName)
                Me.Fields.Header.続柄2.Value = tbl(0).Item(tbl.続柄2Column.ColumnName)
                Me.Fields.Header.生年月日.Value = tbl(0).Item(tbl.生年月日Column.ColumnName)
                Me.Fields.Header.現状通勤.Value = tbl(0).Item(tbl.現状通勤Column.ColumnName)
                Me.Fields.Header.通勤時間.Value = tbl(0).Item(tbl.通勤時間Column.ColumnName)
                Me.Fields.Header.家族構成.Value = tbl(0).Item(tbl.家族構成Column.ColumnName)
                Me.Fields.Header.扶養者数.Value = tbl(0).Item(tbl.扶養者数Column.ColumnName)
                Me.Fields.Header.備考.Value = tbl(0).Item(tbl.備考Column.ColumnName)
                Me.Fields.Header.健康診断受診日.Value = tbl(0).Item(tbl.健康診断受診日Column.ColumnName)
                Me.Fields.Header.汎用項目1.Value = tbl(0).Item(tbl.汎用項目1Column.ColumnName)
                Me.Fields.Header.汎用項目2.Value = tbl(0).Item(tbl.汎用項目2Column.ColumnName)
                Me.Fields.Header.汎用項目3.Value = tbl(0).Item(tbl.汎用項目3Column.ColumnName)
                Me.Fields.Header.汎用項目4.Value = tbl(0).Item(tbl.汎用項目4Column.ColumnName)
                Me.Fields.Header.汎用項目5.Value = tbl(0).Item(tbl.汎用項目5Column.ColumnName)
                Me.Fields.Header.汎用項目6.Value = tbl(0).Item(tbl.汎用項目6Column.ColumnName)
                Me.Fields.Header.汎用項目7.Value = tbl(0).Item(tbl.汎用項目7Column.ColumnName)
                Me.Fields.Header.汎用項目8.Value = tbl(0).Item(tbl.汎用項目8Column.ColumnName)
                Me.Fields.Header.汎用項目9.Value = tbl(0).Item(tbl.汎用項目9Column.ColumnName)
                Me.Fields.Header.汎用項目10.Value = tbl(0).Item(tbl.汎用項目10Column.ColumnName)
                Me.Fields.Header.汎用項目11.Value = tbl(0).Item(tbl.汎用項目11Column.ColumnName)
                Me.Fields.Header.汎用項目12.Value = tbl(0).Item(tbl.汎用項目12Column.ColumnName)
                Me.Fields.Header.汎用項目13.Value = tbl(0).Item(tbl.汎用項目13Column.ColumnName)
                Me.Fields.Header.汎用項目14.Value = tbl(0).Item(tbl.汎用項目14Column.ColumnName)
                Me.Fields.Header.汎用項目15.Value = tbl(0).Item(tbl.汎用項目15Column.ColumnName)
                Me.Fields.Header.汎用項目16.Value = tbl(0).Item(tbl.汎用項目16Column.ColumnName)
                Me.Fields.Header.汎用項目17.Value = tbl(0).Item(tbl.汎用項目17Column.ColumnName)
                Me.Fields.Header.汎用項目18.Value = tbl(0).Item(tbl.汎用項目18Column.ColumnName)
                Me.Fields.Header.汎用項目19.Value = tbl(0).Item(tbl.汎用項目19Column.ColumnName)
                Me.Fields.Header.汎用項目20.Value = tbl(0).Item(tbl.汎用項目20Column.ColumnName)
                Me.Fields.Header.登録ユーザーCD.Value = tbl(0).Item(tbl.登録ユーザーCDColumn.ColumnName)
                Me.Fields.Header.登録ユーザー名.Value = tbl(0).Item(tbl.登録ユーザー名Column.ColumnName)
                Me.Fields.Header.登録日時.Value = tbl(0).Item(tbl.登録日時Column.ColumnName)
                Me.Fields.Header.更新ユーザーCD.Value = tbl(0).Item(tbl.更新ユーザーCDColumn.ColumnName)
                Me.Fields.Header.更新ユーザー名.Value = tbl(0).Item(tbl.更新ユーザー名Column.ColumnName)
                Me.Fields.Header.更新日時.Value = tbl(0).Item(tbl.更新日時Column.ColumnName)
                Me.Fields.Header.登録番号.Value = tbl(0).Item(tbl.登録番号Column.ColumnName)

            Else
                Clear(False)
            End If

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "Write"
        '''========================================================================================
        ''' <SUMMARY>書込</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Write_Header() As Boolean
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim strNow As String = Format(Now, NodeContents.Constant.System.DATE_TIME_FORMAT)

            Dim ada As New NodeDatabase.DataSetMasterTableAdapters.M乗務員TableAdapter
            Dim tbl As New NodeDatabase.DataSetMaster.M乗務員DataTable
            Dim qry As New Collection

            '----------------------------------------------------------------------
            ' 条件設定
            '----------------------------------------------------------------------
            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, Me.Fields.Header.グループCD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, Me.Fields.Header.個社CD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.子会社CDColumn.ColumnName, Me.Fields.Header.子会社CD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.乗務員CDColumn.ColumnName, Me.Fields.Header.乗務員CD.Value, BaseDatabase.Contents.Compare.Equal))


            '----------------------------------------------------------------------
            ' 読込
            '----------------------------------------------------------------------
            tbl = ada.SelectByCommon(qry)


            '----------------------------------------------------------------------
            ' 事前準備
            '----------------------------------------------------------------------
            Me.Fields.Header.更新日時.Value = strNow
            Me.Fields.Header.更新ユーザーCD.Value = Me.Security.ユーザーCD
            Me.Fields.Header.更新ユーザー名.Value = Me.Security.ユーザー名


            '----------------------------------------------------------------------
            ' 保存
            '----------------------------------------------------------------------
            If tbl.Count = 0 Then
                '----------------------------------------------------------------------
                ' 新規
                '----------------------------------------------------------------------
                Me.Fields.Header.登録日時.Value = strNow
                Me.Fields.Header.登録ユーザーCD.Value = Me.Security.ユーザーCD
                Me.Fields.Header.登録ユーザー名.Value = Me.Security.ユーザー名
                Try
                    ada.Insert(Me.Fields.Header.グループCD.Value _
                            , Me.Fields.Header.個社CD.Value _
                            , Me.Fields.Header.子会社CD.Value _
                            , Me.Fields.Header.子会社名_旧.Value _
                            , Me.Fields.Header.子会社住所_旧.Value _
                            , Me.Fields.Header.乗務員CD.Value _
                            , Me.Fields.Header.乗務員氏.Value _
                            , Me.Fields.Header.乗務員氏_新.Value _
                            , Me.Fields.Header.乗務員氏フリガナ.Value _
                            , Me.Fields.Header.乗務員氏フリガナ_新.Value _
                            , Me.Fields.Header.乗務員名.Value _
                            , Me.Fields.Header.乗務員名_新.Value _
                            , Me.Fields.Header.乗務員名フリガナ.Value _
                            , Me.Fields.Header.乗務員名フリガナ_新.Value _
                            , Me.Fields.Header.所属.Value _
                            , Me.Fields.Header.職種.Value _
                            , Me.Fields.Header.ID.Value _
                            , Me.Fields.Header.車両番号.Value _
                            , Me.Fields.Header.運転免許証の番号.Value _
                            , Me.Fields.Header.運転免許証の番号_新.Value _
                            , Me.Fields.Header.運転免許証の有効期限.Value _
                            , Me.Fields.Header.運転免許証の有効期限_新.Value _
                            , Me.Fields.Header.運転免許の種類.Value _
                            , Me.Fields.Header.運転免許の種類_新.Value _
                            , Me.Fields.Header.勤務区分.Value _
                            , Me.Fields.Header.シフト区分.Value _
                            , Me.Fields.Header.入社年月日.Value _
                            , Me.Fields.Header.退社年月日.Value _
                            , Me.Fields.Header.社員NO.Value _
                            , Me.Fields.Header.勤務体系.Value _
                            , Me.Fields.Header.住所_都道府県.Value _
                            , Me.Fields.Header.住所_都道府県_新.Value _
                            , Me.Fields.Header.住所_都道府県フリガナ.Value _
                            , Me.Fields.Header.住所_都道府県フリガナ_新.Value _
                            , Me.Fields.Header.住所_市町村.Value _
                            , Me.Fields.Header.住所_市町村_新.Value _
                            , Me.Fields.Header.住所_市町村フリガナ.Value _
                            , Me.Fields.Header.住所_市町村フリガナ_新.Value _
                            , Me.Fields.Header.住所_地区.Value _
                            , Me.Fields.Header.住所_地区_新.Value _
                            , Me.Fields.Header.住所_地区フリガナ.Value _
                            , Me.Fields.Header.住所_地区フリガナ_新.Value _
                            , Me.Fields.Header.電話番号.Value _
                            , Me.Fields.Header.電話番号2.Value _
                            , Me.Fields.Header.緊急連絡先1.Value _
                            , Me.Fields.Header.続柄1.Value _
                            , Me.Fields.Header.緊急連絡先2.Value _
                            , Me.Fields.Header.続柄2.Value _
                            , Me.Fields.Header.生年月日.Value _
                            , Me.Fields.Header.現状通勤.Value _
                            , Me.Fields.Header.通勤時間.Value _
                            , Me.Fields.Header.家族構成.Value _
                            , Me.Fields.Header.扶養者数.Value _
                            , Me.Fields.Header.備考.Value _
                            , Me.Fields.Header.健康診断受診日.Value _
                            , Me.Fields.Header.汎用項目1.Value _
                            , Me.Fields.Header.汎用項目2.Value _
                            , Me.Fields.Header.汎用項目3.Value _
                            , Me.Fields.Header.汎用項目4.Value _
                            , Me.Fields.Header.汎用項目5.Value _
                            , Me.Fields.Header.汎用項目6.Value _
                            , Me.Fields.Header.汎用項目7.Value _
                            , Me.Fields.Header.汎用項目8.Value _
                            , Me.Fields.Header.汎用項目9.Value _
                            , Me.Fields.Header.汎用項目10.Value _
                            , Me.Fields.Header.汎用項目11.Value _
                            , Me.Fields.Header.汎用項目12.Value _
                            , Me.Fields.Header.汎用項目13.Value _
                            , Me.Fields.Header.汎用項目14.Value _
                            , Me.Fields.Header.汎用項目15.Value _
                            , Me.Fields.Header.汎用項目16.Value _
                            , Me.Fields.Header.汎用項目17.Value _
                            , Me.Fields.Header.汎用項目18.Value _
                            , Me.Fields.Header.汎用項目19.Value _
                            , Me.Fields.Header.汎用項目20.Value _
                            , Me.Fields.Header.登録ユーザーCD.Value _
                            , Me.Fields.Header.登録ユーザー名.Value _
                            , Me.Fields.Header.登録日時.Value _
                            , Me.Fields.Header.更新ユーザーCD.Value _
                            , Me.Fields.Header.更新ユーザー名.Value _
                            , Me.Fields.Header.更新日時.Value _
                            , Me.Fields.Header.登録番号.Value
                            )

                Catch ex As Exception
                    MyBase.LastError = "以下のエラーのため、追加できませんでした。" & vbCrLf & ex.Message
                    Return False
                End Try
            Else
                '----------------------------------------------------------------------
                ' 訂正
                '----------------------------------------------------------------------
                Try
                    tbl(0).Item(tbl.グループCDColumn.ColumnName) = Me.Fields.Header.グループCD.Value
                    tbl(0).Item(tbl.個社CDColumn.ColumnName) = Me.Fields.Header.個社CD.Value
                    tbl(0).Item(tbl.子会社CDColumn.ColumnName) = Me.Fields.Header.子会社CD.Value
                    tbl(0).Item(tbl.子会社名_旧Column.ColumnName) = Me.Fields.Header.子会社名_旧.Value
                    tbl(0).Item(tbl.子会社住所_旧Column.ColumnName) = Me.Fields.Header.子会社住所_旧.Value
                    tbl(0).Item(tbl.乗務員CDColumn.ColumnName) = Me.Fields.Header.乗務員CD.Value
                    tbl(0).Item(tbl.乗務員氏Column.ColumnName) = Me.Fields.Header.乗務員氏.Value
                    tbl(0).Item(tbl.乗務員氏_新Column.ColumnName) = Me.Fields.Header.乗務員氏_新.Value
                    tbl(0).Item(tbl.乗務員氏フリガナColumn.ColumnName) = Me.Fields.Header.乗務員氏フリガナ.Value
                    tbl(0).Item(tbl.乗務員氏フリガナ_新Column.ColumnName) = Me.Fields.Header.乗務員氏フリガナ_新.Value
                    tbl(0).Item(tbl.乗務員名Column.ColumnName) = Me.Fields.Header.乗務員名.Value
                    tbl(0).Item(tbl.乗務員名_新Column.ColumnName) = Me.Fields.Header.乗務員名_新.Value
                    tbl(0).Item(tbl.乗務員名フリガナColumn.ColumnName) = Me.Fields.Header.乗務員名フリガナ.Value
                    tbl(0).Item(tbl.乗務員名フリガナ_新Column.ColumnName) = Me.Fields.Header.乗務員名フリガナ_新.Value
                    tbl(0).Item(tbl.所属Column.ColumnName) = Me.Fields.Header.所属.Value
                    tbl(0).Item(tbl.職種Column.ColumnName) = Me.Fields.Header.職種.Value
                    tbl(0).Item(tbl.IDColumn.ColumnName) = Me.Fields.Header.ID.Value
                    tbl(0).Item(tbl.車両番号Column.ColumnName) = Me.Fields.Header.車両番号.Value
                    tbl(0).Item(tbl.運転免許証の番号Column.ColumnName) = Me.Fields.Header.運転免許証の番号.Value
                    tbl(0).Item(tbl.運転免許証の番号_新Column.ColumnName) = Me.Fields.Header.運転免許証の番号_新.Value
                    tbl(0).Item(tbl.運転免許証の有効期限Column.ColumnName) = Me.Fields.Header.運転免許証の有効期限.Value
                    tbl(0).Item(tbl.運転免許証の有効期限_新Column.ColumnName) = Me.Fields.Header.運転免許証の有効期限_新.Value
                    tbl(0).Item(tbl.運転免許の種類Column.ColumnName) = Me.Fields.Header.運転免許の種類.Value
                    tbl(0).Item(tbl.運転免許の種類_新Column.ColumnName) = Me.Fields.Header.運転免許の種類_新.Value
                    tbl(0).Item(tbl.勤務区分Column.ColumnName) = Me.Fields.Header.勤務区分.Value
                    tbl(0).Item(tbl.シフト区分Column.ColumnName) = Me.Fields.Header.シフト区分.Value
                    tbl(0).Item(tbl.入社年月日Column.ColumnName) = Me.Fields.Header.入社年月日.Value
                    tbl(0).Item(tbl.退社年月日Column.ColumnName) = Me.Fields.Header.退社年月日.Value
                    tbl(0).Item(tbl.社員NOColumn.ColumnName) = Me.Fields.Header.社員NO.Value
                    tbl(0).Item(tbl.勤務体系Column.ColumnName) = Me.Fields.Header.勤務体系.Value
                    tbl(0).Item(tbl.住所_都道府県Column.ColumnName) = Me.Fields.Header.住所_都道府県.Value
                    tbl(0).Item(tbl.住所_都道府県_新Column.ColumnName) = Me.Fields.Header.住所_都道府県_新.Value
                    tbl(0).Item(tbl.住所_都道府県フリガナColumn.ColumnName) = Me.Fields.Header.住所_都道府県フリガナ.Value
                    tbl(0).Item(tbl.住所_都道府県フリガナ_新Column.ColumnName) = Me.Fields.Header.住所_都道府県フリガナ_新.Value
                    tbl(0).Item(tbl.住所_市町村Column.ColumnName) = Me.Fields.Header.住所_市町村.Value
                    tbl(0).Item(tbl.住所_市町村_新Column.ColumnName) = Me.Fields.Header.住所_市町村_新.Value
                    tbl(0).Item(tbl.住所_市町村フリガナColumn.ColumnName) = Me.Fields.Header.住所_市町村フリガナ.Value
                    tbl(0).Item(tbl.住所_市町村フリガナ_新Column.ColumnName) = Me.Fields.Header.住所_市町村フリガナ_新.Value
                    tbl(0).Item(tbl.住所_地区Column.ColumnName) = Me.Fields.Header.住所_地区.Value
                    tbl(0).Item(tbl.住所_地区_新Column.ColumnName) = Me.Fields.Header.住所_地区_新.Value
                    tbl(0).Item(tbl.住所_地区フリガナColumn.ColumnName) = Me.Fields.Header.住所_地区フリガナ.Value
                    tbl(0).Item(tbl.住所_地区フリガナ_新Column.ColumnName) = Me.Fields.Header.住所_地区フリガナ_新.Value
                    tbl(0).Item(tbl.電話番号Column.ColumnName) = Me.Fields.Header.電話番号.Value
                    tbl(0).Item(tbl.電話番号2Column.ColumnName) = Me.Fields.Header.電話番号2.Value
                    tbl(0).Item(tbl.緊急連絡先1Column.ColumnName) = Me.Fields.Header.緊急連絡先1.Value
                    tbl(0).Item(tbl.続柄1Column.ColumnName) = Me.Fields.Header.続柄1.Value
                    tbl(0).Item(tbl.緊急連絡先2Column.ColumnName) = Me.Fields.Header.緊急連絡先2.Value
                    tbl(0).Item(tbl.続柄2Column.ColumnName) = Me.Fields.Header.続柄2.Value
                    tbl(0).Item(tbl.生年月日Column.ColumnName) = Me.Fields.Header.生年月日.Value
                    tbl(0).Item(tbl.現状通勤Column.ColumnName) = Me.Fields.Header.現状通勤.Value
                    tbl(0).Item(tbl.通勤時間Column.ColumnName) = Me.Fields.Header.通勤時間.Value
                    tbl(0).Item(tbl.家族構成Column.ColumnName) = Me.Fields.Header.家族構成.Value
                    tbl(0).Item(tbl.扶養者数Column.ColumnName) = Me.Fields.Header.扶養者数.Value
                    tbl(0).Item(tbl.備考Column.ColumnName) = Me.Fields.Header.備考.Value
                    tbl(0).Item(tbl.健康診断受診日Column.ColumnName) = Me.Fields.Header.健康診断受診日.Value
                    tbl(0).Item(tbl.汎用項目1Column.ColumnName) = Me.Fields.Header.汎用項目1.Value
                    tbl(0).Item(tbl.汎用項目2Column.ColumnName) = Me.Fields.Header.汎用項目2.Value
                    tbl(0).Item(tbl.汎用項目3Column.ColumnName) = Me.Fields.Header.汎用項目3.Value
                    tbl(0).Item(tbl.汎用項目4Column.ColumnName) = Me.Fields.Header.汎用項目4.Value
                    tbl(0).Item(tbl.汎用項目5Column.ColumnName) = Me.Fields.Header.汎用項目5.Value
                    tbl(0).Item(tbl.汎用項目6Column.ColumnName) = Me.Fields.Header.汎用項目6.Value
                    tbl(0).Item(tbl.汎用項目7Column.ColumnName) = Me.Fields.Header.汎用項目7.Value
                    tbl(0).Item(tbl.汎用項目8Column.ColumnName) = Me.Fields.Header.汎用項目8.Value
                    tbl(0).Item(tbl.汎用項目9Column.ColumnName) = Me.Fields.Header.汎用項目9.Value
                    tbl(0).Item(tbl.汎用項目10Column.ColumnName) = Me.Fields.Header.汎用項目10.Value
                    tbl(0).Item(tbl.汎用項目11Column.ColumnName) = Me.Fields.Header.汎用項目11.Value
                    tbl(0).Item(tbl.汎用項目12Column.ColumnName) = Me.Fields.Header.汎用項目12.Value
                    tbl(0).Item(tbl.汎用項目13Column.ColumnName) = Me.Fields.Header.汎用項目13.Value
                    tbl(0).Item(tbl.汎用項目14Column.ColumnName) = Me.Fields.Header.汎用項目14.Value
                    tbl(0).Item(tbl.汎用項目15Column.ColumnName) = Me.Fields.Header.汎用項目15.Value
                    tbl(0).Item(tbl.汎用項目16Column.ColumnName) = Me.Fields.Header.汎用項目16.Value
                    tbl(0).Item(tbl.汎用項目17Column.ColumnName) = Me.Fields.Header.汎用項目17.Value
                    tbl(0).Item(tbl.汎用項目18Column.ColumnName) = Me.Fields.Header.汎用項目18.Value
                    tbl(0).Item(tbl.汎用項目19Column.ColumnName) = Me.Fields.Header.汎用項目19.Value
                    tbl(0).Item(tbl.汎用項目20Column.ColumnName) = Me.Fields.Header.汎用項目20.Value
                    tbl(0).Item(tbl.登録ユーザーCDColumn.ColumnName) = Me.Fields.Header.登録ユーザーCD.Value
                    tbl(0).Item(tbl.登録ユーザー名Column.ColumnName) = Me.Fields.Header.登録ユーザー名.Value
                    tbl(0).Item(tbl.登録日時Column.ColumnName) = Me.Fields.Header.登録日時.Value
                    tbl(0).Item(tbl.更新ユーザーCDColumn.ColumnName) = Me.Fields.Header.更新ユーザーCD.Value
                    tbl(0).Item(tbl.更新ユーザー名Column.ColumnName) = Me.Fields.Header.更新ユーザー名.Value
                    tbl(0).Item(tbl.更新日時Column.ColumnName) = Me.Fields.Header.更新日時.Value
                    tbl(0).Item(tbl.登録番号Column.ColumnName) = Me.Fields.Header.登録番号.Value
                    ada.Update(tbl)

                Catch ex As Exception
                    MyBase.LastError = "以下のエラーのため、更新できませんでした。" & vbCrLf & ex.Message
                    Return False
                End Try
            End If

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "Delete"
        '''========================================================================================
        ''' <SUMMARY>削除</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Delete_Header() As Boolean
            '----------------------------------------------------------------------
            ' ﾍｯﾀﾞ
            '----------------------------------------------------------------------
            '------------------------------------
            ' 変数定義
            '------------------------------------
            Dim ada As New NodeDatabase.DataSetMasterTableAdapters.M乗務員TableAdapter
            Dim tbl As New NodeDatabase.DataSetMaster.M乗務員DataTable
            Dim qry As New Collection

            '----------------------------------------------------------------------
            ' 条件設定
            '----------------------------------------------------------------------
            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, Me.Fields.Header.グループCD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, Me.Fields.Header.個社CD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.子会社CDColumn.ColumnName, Me.Fields.Header.子会社CD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.乗務員CDColumn.ColumnName, Me.Fields.Header.乗務員CD.Value, BaseDatabase.Contents.Compare.Equal))

            '------------------------------------
            ' 削除
            '------------------------------------
            Try
                ada.DeleteByCommon(qry)

            Catch ex As Exception
                MyBase.LastError = "以下のエラーのため、削除できませんでした。" & vbCrLf & ex.Message
                Return False
            End Try

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "Valid_Write"
        '''========================================================================================
        ''' <SUMMARY>検査(共通)</SUMMARY>
        ''' <RETURNS>True:正常, False:不正</RETURNS>
        '''========================================================================================
        Protected Overrides Function Valid_Write_Header_Common() As NodeContents.ActionButton.ErrorLevel
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim strMsg As String = ""
            Dim Validator As New BaseCore.Common.Validator

            '------------------------------------------------------------------
            ' ｴﾗｰｸﾘｱ
            '------------------------------------------------------------------
            Me.Fields.Header.グループCD.IsError = False
            Me.Fields.Header.個社CD.IsError = False
            Me.Fields.Header.子会社CD.IsError = False
            Me.Fields.Header.子会社名_旧.IsError = False
            Me.Fields.Header.子会社住所_旧.IsError = False
            Me.Fields.Header.乗務員CD.IsError = False
            Me.Fields.Header.乗務員氏.IsError = False
            Me.Fields.Header.乗務員氏_新.IsError = False
            Me.Fields.Header.乗務員氏フリガナ.IsError = False
            Me.Fields.Header.乗務員氏フリガナ_新.IsError = False
            Me.Fields.Header.乗務員名.IsError = False
            Me.Fields.Header.乗務員名_新.IsError = False
            Me.Fields.Header.乗務員名フリガナ.IsError = False
            Me.Fields.Header.乗務員名フリガナ_新.IsError = False
            Me.Fields.Header.所属.IsError = False
            Me.Fields.Header.職種.IsError = False
            Me.Fields.Header.ID.IsError = False
            Me.Fields.Header.車両番号.IsError = False
            Me.Fields.Header.運転免許証の番号.IsError = False
            Me.Fields.Header.運転免許証の番号_新.IsError = False
            Me.Fields.Header.運転免許証の有効期限.IsError = False
            Me.Fields.Header.運転免許証の有効期限_新.IsError = False
            Me.Fields.Header.運転免許の種類.IsError = False
            Me.Fields.Header.運転免許の種類_新.IsError = False
            Me.Fields.Header.勤務区分.IsError = False
            Me.Fields.Header.シフト区分.IsError = False
            Me.Fields.Header.入社年月日.IsError = False
            Me.Fields.Header.退社年月日.IsError = False
            Me.Fields.Header.社員NO.IsError = False
            Me.Fields.Header.勤務体系.IsError = False
            Me.Fields.Header.住所_都道府県.IsError = False
            Me.Fields.Header.住所_都道府県_新.IsError = False
            Me.Fields.Header.住所_都道府県フリガナ.IsError = False
            Me.Fields.Header.住所_都道府県フリガナ_新.IsError = False
            Me.Fields.Header.住所_市町村.IsError = False
            Me.Fields.Header.住所_市町村_新.IsError = False
            Me.Fields.Header.住所_市町村フリガナ.IsError = False
            Me.Fields.Header.住所_市町村フリガナ_新.IsError = False
            Me.Fields.Header.住所_地区.IsError = False
            Me.Fields.Header.住所_地区_新.IsError = False
            Me.Fields.Header.住所_地区フリガナ.IsError = False
            Me.Fields.Header.住所_地区フリガナ_新.IsError = False
            Me.Fields.Header.電話番号.IsError = False
            Me.Fields.Header.電話番号2.IsError = False
            Me.Fields.Header.緊急連絡先1.IsError = False
            Me.Fields.Header.続柄1.IsError = False
            Me.Fields.Header.緊急連絡先2.IsError = False
            Me.Fields.Header.続柄2.IsError = False
            Me.Fields.Header.生年月日.IsError = False
            Me.Fields.Header.現状通勤.IsError = False
            Me.Fields.Header.通勤時間.IsError = False
            Me.Fields.Header.家族構成.IsError = False
            Me.Fields.Header.扶養者数.IsError = False
            Me.Fields.Header.備考.IsError = False
            Me.Fields.Header.健康診断受診日.IsError = False
            Me.Fields.Header.汎用項目1.IsError = False
            Me.Fields.Header.汎用項目2.IsError = False
            Me.Fields.Header.汎用項目3.IsError = False
            Me.Fields.Header.汎用項目4.IsError = False
            Me.Fields.Header.汎用項目5.IsError = False
            Me.Fields.Header.汎用項目6.IsError = False
            Me.Fields.Header.汎用項目7.IsError = False
            Me.Fields.Header.汎用項目8.IsError = False
            Me.Fields.Header.汎用項目9.IsError = False
            Me.Fields.Header.汎用項目10.IsError = False
            Me.Fields.Header.汎用項目11.IsError = False
            Me.Fields.Header.汎用項目12.IsError = False
            Me.Fields.Header.汎用項目13.IsError = False
            Me.Fields.Header.汎用項目14.IsError = False
            Me.Fields.Header.汎用項目15.IsError = False
            Me.Fields.Header.汎用項目16.IsError = False
            Me.Fields.Header.汎用項目17.IsError = False
            Me.Fields.Header.汎用項目18.IsError = False
            Me.Fields.Header.汎用項目19.IsError = False
            Me.Fields.Header.汎用項目20.IsError = False
            Me.Fields.Header.登録ユーザーCD.IsError = False
            Me.Fields.Header.登録ユーザー名.IsError = False
            Me.Fields.Header.登録日時.IsError = False
            Me.Fields.Header.更新ユーザーCD.IsError = False
            Me.Fields.Header.更新ユーザー名.IsError = False
            Me.Fields.Header.更新日時.IsError = False
            Me.Fields.Header.登録番号.IsError = False

            '------------------------------------------------------------------
            ' 共通検査
            '------------------------------------------------------------------
            If Not Validator.BaseChecker(Me.Fields.Header.グループCD) Then : Me.Fields.Header.グループCD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.個社CD) Then : Me.Fields.Header.個社CD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.子会社CD) Then : Me.Fields.Header.子会社CD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.子会社名_旧) Then : Me.Fields.Header.子会社名_旧.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.子会社住所_旧) Then : Me.Fields.Header.子会社住所_旧.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.乗務員CD) Then : Me.Fields.Header.乗務員CD.IsError = True : strMsg &= vbCrLf & "社員NO：未入力です" : End If
            If Not Validator.BaseChecker(Me.Fields.Header.乗務員氏) Then : Me.Fields.Header.乗務員氏.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.乗務員氏_新) Then : Me.Fields.Header.乗務員氏_新.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.乗務員氏フリガナ) Then : Me.Fields.Header.乗務員氏フリガナ.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.乗務員氏フリガナ_新) Then : Me.Fields.Header.乗務員氏フリガナ_新.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.乗務員名) Then : Me.Fields.Header.乗務員名.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.乗務員名_新) Then : Me.Fields.Header.乗務員名_新.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.乗務員名フリガナ) Then : Me.Fields.Header.乗務員名フリガナ.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.乗務員名フリガナ_新) Then : Me.Fields.Header.乗務員名フリガナ_新.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.所属) Then : Me.Fields.Header.所属.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.職種) Then : Me.Fields.Header.職種.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.ID) Then : Me.Fields.Header.ID.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.車両番号) Then : Me.Fields.Header.車両番号.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.運転免許証の番号) Then : Me.Fields.Header.運転免許証の番号.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.運転免許証の番号_新) Then : Me.Fields.Header.運転免許証の番号_新.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.運転免許証の有効期限) Then : Me.Fields.Header.運転免許証の有効期限.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.運転免許証の有効期限_新) Then : Me.Fields.Header.運転免許証の有効期限_新.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.運転免許の種類) Then : Me.Fields.Header.運転免許の種類.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.運転免許の種類_新) Then : Me.Fields.Header.運転免許の種類_新.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.勤務区分) Then : Me.Fields.Header.勤務区分.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.シフト区分) Then : Me.Fields.Header.シフト区分.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.入社年月日) Then : Me.Fields.Header.入社年月日.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.退社年月日) Then : Me.Fields.Header.退社年月日.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.社員NO) Then : Me.Fields.Header.社員NO.IsError = True : strMsg &= vbCrLf & "乗務員CD：未入力です" : End If
            If Not Validator.BaseChecker(Me.Fields.Header.勤務体系) Then : Me.Fields.Header.勤務体系.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.住所_都道府県) Then : Me.Fields.Header.住所_都道府県.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.住所_都道府県_新) Then : Me.Fields.Header.住所_都道府県_新.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.住所_都道府県フリガナ) Then : Me.Fields.Header.住所_都道府県フリガナ.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.住所_都道府県フリガナ_新) Then : Me.Fields.Header.住所_都道府県フリガナ_新.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.住所_市町村) Then : Me.Fields.Header.住所_市町村.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.住所_市町村_新) Then : Me.Fields.Header.住所_市町村_新.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.住所_市町村フリガナ) Then : Me.Fields.Header.住所_市町村フリガナ.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.住所_市町村フリガナ_新) Then : Me.Fields.Header.住所_市町村フリガナ_新.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.住所_地区) Then : Me.Fields.Header.住所_地区.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.住所_地区_新) Then : Me.Fields.Header.住所_地区_新.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.住所_地区フリガナ) Then : Me.Fields.Header.住所_地区フリガナ.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.住所_地区フリガナ_新) Then : Me.Fields.Header.住所_地区フリガナ_新.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.電話番号) Then : Me.Fields.Header.電話番号.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.電話番号2) Then : Me.Fields.Header.電話番号2.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.緊急連絡先1) Then : Me.Fields.Header.緊急連絡先1.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.続柄1) Then : Me.Fields.Header.続柄1.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.緊急連絡先2) Then : Me.Fields.Header.緊急連絡先2.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.続柄2) Then : Me.Fields.Header.続柄2.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.生年月日) Then : Me.Fields.Header.生年月日.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.現状通勤) Then : Me.Fields.Header.現状通勤.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.通勤時間) Then : Me.Fields.Header.通勤時間.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.家族構成) Then : Me.Fields.Header.家族構成.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.扶養者数) Then : Me.Fields.Header.扶養者数.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.備考) Then : Me.Fields.Header.備考.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.健康診断受診日) Then : Me.Fields.Header.健康診断受診日.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.汎用項目1) Then : Me.Fields.Header.汎用項目1.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.汎用項目2) Then : Me.Fields.Header.汎用項目2.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.汎用項目3) Then : Me.Fields.Header.汎用項目3.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.汎用項目4) Then : Me.Fields.Header.汎用項目4.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.汎用項目5) Then : Me.Fields.Header.汎用項目5.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.汎用項目6) Then : Me.Fields.Header.汎用項目6.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.汎用項目7) Then : Me.Fields.Header.汎用項目7.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.汎用項目8) Then : Me.Fields.Header.汎用項目8.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.汎用項目9) Then : Me.Fields.Header.汎用項目9.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.汎用項目10) Then : Me.Fields.Header.汎用項目10.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.汎用項目11) Then : Me.Fields.Header.汎用項目11.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.汎用項目12) Then : Me.Fields.Header.汎用項目12.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.汎用項目13) Then : Me.Fields.Header.汎用項目13.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.汎用項目14) Then : Me.Fields.Header.汎用項目14.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.汎用項目15) Then : Me.Fields.Header.汎用項目15.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.汎用項目16) Then : Me.Fields.Header.汎用項目16.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.汎用項目17) Then : Me.Fields.Header.汎用項目17.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.汎用項目18) Then : Me.Fields.Header.汎用項目18.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.汎用項目19) Then : Me.Fields.Header.汎用項目19.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.汎用項目20) Then : Me.Fields.Header.汎用項目20.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.登録ユーザーCD) Then : Me.Fields.Header.登録ユーザーCD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.登録ユーザー名) Then : Me.Fields.Header.登録ユーザー名.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.登録日時) Then : Me.Fields.Header.登録日時.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.更新ユーザーCD) Then : Me.Fields.Header.更新ユーザーCD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.更新ユーザー名) Then : Me.Fields.Header.更新ユーザー名.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.更新日時) Then : Me.Fields.Header.更新日時.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.登録番号) Then : Me.Fields.Header.登録番号.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            '------------------------------------------------------------------
            ' 返却
            '------------------------------------------------------------------
            MyBase.LastError = strMsg
            Select Case True
                Case strMsg = ""
                    Return NodeContents.ActionButton.ErrorLevel.None
                Case Else
                    Return NodeContents.ActionButton.ErrorLevel.Fatal
            End Select
        End Function
#End Region

#Region "Valid_Delete"
        '''========================================================================================
        ''' <SUMMARY>検査(共通)</SUMMARY>
        ''' <RETURNS>True:正常, False:不正</RETURNS>
        '''========================================================================================
        Protected Overrides Function Valid_Delete_Header_Common() As NodeContents.ActionButton.ErrorLevel
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim strMsg As String = ""
            Dim Validator As New BaseCore.Common.Validator

            '------------------------------------------------------------------
            ' ｴﾗｰｸﾘｱ
            '------------------------------------------------------------------
            Me.Fields.Header.グループCD.IsError = False
            Me.Fields.Header.個社CD.IsError = False
            Me.Fields.Header.子会社CD.IsError = False
            Me.Fields.Header.乗務員CD.IsError = False
            Me.Fields.Header.勤務区分.IsError = False
            Me.Fields.Header.シフト区分.IsError = False
            Me.Fields.Header.入社年月日.IsError = False
            Me.Fields.Header.社員NO.IsError = False

            '------------------------------------------------------------------
            ' 共通検査
            '------------------------------------------------------------------
            If Not Validator.BaseChecker(Me.Fields.Header.グループCD) Then : Me.Fields.Header.グループCD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.個社CD) Then : Me.Fields.Header.個社CD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.子会社CD) Then : Me.Fields.Header.子会社CD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.乗務員CD) Then : Me.Fields.Header.乗務員CD.IsError = True : strMsg &= vbCrLf & "社員NO：未入力です" : End If
            If Not Validator.BaseChecker(Me.Fields.Header.勤務区分) Then : Me.Fields.Header.勤務区分.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.シフト区分) Then : Me.Fields.Header.シフト区分.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.入社年月日) Then : Me.Fields.Header.入社年月日.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.社員NO) Then : Me.Fields.Header.社員NO.IsError = True : strMsg &= vbCrLf & "乗務員CD：未入力です" : End If
            '------------------------------------------------------------------
            ' 返却
            '------------------------------------------------------------------
            MyBase.LastError = strMsg
            Select Case True
                Case strMsg = ""
                    Return NodeContents.ActionButton.ErrorLevel.None
                Case Else
                    Return NodeContents.ActionButton.ErrorLevel.Fatal
            End Select
        End Function
#End Region

        '#Region "Valid_Excel"
        '        '''========================================================================================
        '        ''' <SUMMARY>検査(共通)</SUMMARY>
        '        ''' <RETURNS>True:正常, False:不正</RETURNS>
        '        '''========================================================================================
        '        Protected Overrides Function Valid_Excel_Header_Common() As NodeContents.ActionButton.ErrorLevel
        '            '----------------------------------------------------------------------
        '            ' 変数定義
        '            '----------------------------------------------------------------------
        '            Dim strMsg As String = ""
        '            Dim Validator As New BaseCore.Common.Validator

        '            '------------------------------------------------------------------
        '            ' ｴﾗｰｸﾘｱ
        '            '------------------------------------------------------------------
        '            Me.Fields.Header.グループCD.IsError = False
        '            Me.Fields.Header.個社CD.IsError = False
        '            Me.Fields.Header.子会社CD.IsError = False
        '            Me.Fields.Header.乗務員CD.IsError = False
        '            Me.Fields.Header.勤務区分.IsError = False
        '            Me.Fields.Header.シフト区分.IsError = False
        '            Me.Fields.Header.入社年月日.IsError = False
        '            Me.Fields.Header.社員NO.IsError = False

        '            '------------------------------------------------------------------
        '            ' 共通検査
        '            '------------------------------------------------------------------
        '            If Not Validator.BaseChecker(Me.Fields.Header.グループCD) Then : Me.Fields.Header.グループCD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
        '            If Not Validator.BaseChecker(Me.Fields.Header.個社CD) Then : Me.Fields.Header.個社CD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
        '            If Not Validator.BaseChecker(Me.Fields.Header.子会社CD) Then : Me.Fields.Header.子会社CD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
        '            If Not Validator.BaseChecker(Me.Fields.Header.乗務員CD) Then : Me.Fields.Header.乗務員CD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
        '            If Not Validator.BaseChecker(Me.Fields.Header.勤務区分) Then : Me.Fields.Header.勤務区分.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
        '            If Not Validator.BaseChecker(Me.Fields.Header.シフト区分) Then : Me.Fields.Header.シフト区分.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
        '            If Not Validator.BaseChecker(Me.Fields.Header.入社年月日) Then : Me.Fields.Header.入社年月日.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
        '            If Not Validator.BaseChecker(Me.Fields.Header.社員NO) Then : Me.Fields.Header.社員NO.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
        '            '------------------------------------------------------------------
        '            ' 返却
        '            '------------------------------------------------------------------
        '            MyBase.LastError = strMsg
        '            Select Case True
        '                Case strMsg = ""
        '                    Return NodeContents.ActionButton.ErrorLevel.None
        '                Case Else
        '                    Return NodeContents.ActionButton.ErrorLevel.Fatal
        '            End Select
        '        End Function
        '#End Region

#Region "MakeDataTable"
        '''========================================================================================
        ''' <SUMMARY>ﾃﾞｰﾀﾃｰﾌﾞﾙ生成</SUMMARY>
        ''' <RETURNS>ﾃﾞｰﾀﾃｰﾌﾞﾙ</RETURNS>
        '''========================================================================================
        Public Function MakeDataTable() As DataTable
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetViewTableAdapters.V0乗務員データ出力TableAdapter
            Dim tbl As New NodeDatabase.DataSetView.V0乗務員データ出力DataTable
            Dim qry As New Collection

            '----------------------------------------------------------------------
            ' 事前準備
            '----------------------------------------------------------------------

            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, Me.Security.グループCD, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, Me.Security.個社CD, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.子会社CDColumn.ColumnName, Me.Security.子会社CD, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.乗務員CDColumn.ColumnName, Me.Fields.Header.乗務員CD.Value, BaseDatabase.Contents.Compare.GreaterEqual))


            tbl = ada.SelectByCommon(qry)


            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return tbl
        End Function
#End Region

#Region "Execute1:Excel"
        '''========================================================================================
        ''' <SUMMARY>実行</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Execute1_Header() As Boolean

            Me.LastError = Nothing
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim xls As New BaseCore.Common.Excel

            Try
                '----------------------------------------------------------------------
                ' ﾃﾞｰﾀ抽出
                '----------------------------------------------------------------------
                Dim tbl As DataTable = Me.MakeDataTable()

                ''----------------------------------------------------------------------
                '' ｾﾞﾛ件
                ''----------------------------------------------------------------------
                If tbl.Rows.Count = 0 Then
                    Me.LastError = "指定された条件に合致するデータはありません。"
                    Return False
                End If

                '----------------------------------------------------------------------
                ' ｵｰﾌﾟﾝ
                '----------------------------------------------------------------------
                If Not xls.Open(Me.Path1) Then
                    Me.LastError = "テンプレートが見つかりません。(" & Me.Path1 & ")"
                    Return False
                End If

                '------------------------------------------------------------------------
                ' 作成
                '------------------------------------------------------------------------
                xls.SheetSelect(ConstantExcel.Header.SheetName)       'ｱｸﾃｨﾌﾞｼｰﾄ

                If Not MakeExcelData(tbl, xls) Then
                    MyBase.LastError = Me.LastError
                    Return False
                End If

                '----------------------------------------------------------------------
                ' 先頭へｶｰｿﾙ移動
                '----------------------------------------------------------------------
                xls.CellGet(0, 0)

                xls.Close(Me.Path1)
            Catch ex As Exception
                Me.LastError = ex.Message
                Return False
            Finally
            End Try


            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------

            ''----------------------------------------------------------------------
            '' ﾃﾞｰﾀ加工(前)
            ''----------------------------------------------------------------------
            'tbl.TableName = "乗務員"

            ''----------------------------------------------------------------------
            '' ｴｸｽﾎﾟｰﾄ
            ''----------------------------------------------------------------------
            'Dim Excel As New BaseCore.Common.Excel
            'If Not Excel.ExcelExport(tbl, Me.Path1) Then
            '    Me.LastError = Excel.LastError
            '    Return False
            'End If

            ''----------------------------------------------------------------------
            '' 正常終了
            ''----------------------------------------------------------------------

            Return True
        End Function
#End Region

#Region "Execute2:Excel"
        '''========================================================================================
        ''' <SUMMARY>実行</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Execute2_Header() As Boolean

            Me.LastError = Nothing
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim xls As New BaseCore.Common.Excel

            Try

                '----------------------------------------------------------------------
                ' ｵｰﾌﾟﾝ
                '----------------------------------------------------------------------
                If Not xls.Open(Me.Path1) Then
                    Me.LastError = "テンプレートが見つかりません。(" & Me.Path1 & ")"
                    Return False
                End If

                '------------------------------------------------------------------------
                ' 作成
                '------------------------------------------------------------------------
                xls.SheetSelect("第二号様式")       'ｱｸﾃｨﾌﾞｼｰﾄ

                If Not MakeExcelDataDai2gou(xls) Then
                    MyBase.LastError = Me.LastError
                    Return False
                End If

                ''----------------------------------------------------------------------
                ' 先頭へｶｰｿﾙ移動
                '----------------------------------------------------------------------
                'xls.CellGet(1, 1)

                xls.Close(Me.Path1)
            Catch ex As Exception
                Me.LastError = ex.Message
                Return False
            Finally
            End Try


            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------

            ''----------------------------------------------------------------------
            '' ﾃﾞｰﾀ加工(前)
            ''----------------------------------------------------------------------
            'tbl.TableName = "乗務員"

            ''----------------------------------------------------------------------
            '' ｴｸｽﾎﾟｰﾄ
            ''----------------------------------------------------------------------
            'Dim Excel As New BaseCore.Common.Excel
            'If Not Excel.ExcelExport(tbl, Me.Path1) Then
            '    Me.LastError = Excel.LastError
            '    Return False
            'End If

            ''----------------------------------------------------------------------
            '' 正常終了
            ''----------------------------------------------------------------------

            Return True
        End Function
#End Region

#Region "Execute3:Excel"
        '''========================================================================================
        ''' <SUMMARY>実行</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Execute3_Header() As Boolean

            Me.LastError = Nothing
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim xls As New BaseCore.Common.Excel

            Try

                '----------------------------------------------------------------------
                ' ｵｰﾌﾟﾝ
                '----------------------------------------------------------------------
                If Not xls.Open(Me.Path1) Then
                    Me.LastError = "テンプレートが見つかりません。(" & Me.Path1 & ")"
                    Return False
                End If

                '------------------------------------------------------------------------
                ' 作成
                '------------------------------------------------------------------------
                xls.SheetSelect("第四号様式")       'ｱｸﾃｨﾌﾞｼｰﾄ

                If Not MakeExcelDataDai4gou(xls) Then
                    MyBase.LastError = Me.LastError
                    Return False
                End If

                ''----------------------------------------------------------------------
                ' 先頭へｶｰｿﾙ移動
                '----------------------------------------------------------------------
                'xls.CellGet(1, 1)

                xls.Close(Me.Path1)
            Catch ex As Exception
                Me.LastError = ex.Message
                Return False
            Finally
            End Try


            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------

            ''----------------------------------------------------------------------
            '' ﾃﾞｰﾀ加工(前)
            ''----------------------------------------------------------------------
            'tbl.TableName = "乗務員"

            ''----------------------------------------------------------------------
            '' ｴｸｽﾎﾟｰﾄ
            ''----------------------------------------------------------------------
            'Dim Excel As New BaseCore.Common.Excel
            'If Not Excel.ExcelExport(tbl, Me.Path1) Then
            '    Me.LastError = Excel.LastError
            '    Return False
            'End If

            ''----------------------------------------------------------------------
            '' 正常終了
            ''----------------------------------------------------------------------

            Return True
        End Function
#End Region

#Region "Execute4:Excel"
        '''========================================================================================
        ''' <SUMMARY>実行</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Execute4_Header() As Boolean

            Me.LastError = Nothing
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim xls As New BaseCore.Common.Excel

            Try

                '----------------------------------------------------------------------
                ' ｵｰﾌﾟﾝ
                '----------------------------------------------------------------------
                If Not xls.Open(Me.Path1) Then
                    Me.LastError = "テンプレートが見つかりません。(" & Me.Path1 & ")"
                    Return False
                End If

                '------------------------------------------------------------------------
                ' 作成
                '------------------------------------------------------------------------
                xls.SheetSelect("第九号様式")       'ｱｸﾃｨﾌﾞｼｰﾄ

                If Not MakeExcelDataDai9gou(xls) Then
                    MyBase.LastError = Me.LastError
                    Return False
                End If

                ''----------------------------------------------------------------------
                ' 先頭へｶｰｿﾙ移動
                '----------------------------------------------------------------------
                'xls.CellGet(1, 1)

                xls.Close(Me.Path1)
            Catch ex As Exception
                Me.LastError = ex.Message
                Return False
            Finally
            End Try


            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------

            ''----------------------------------------------------------------------
            '' ﾃﾞｰﾀ加工(前)
            ''----------------------------------------------------------------------
            'tbl.TableName = "乗務員"

            ''----------------------------------------------------------------------
            '' ｴｸｽﾎﾟｰﾄ
            ''----------------------------------------------------------------------
            'Dim Excel As New BaseCore.Common.Excel
            'If Not Excel.ExcelExport(tbl, Me.Path1) Then
            '    Me.LastError = Excel.LastError
            '    Return False
            'End If

            ''----------------------------------------------------------------------
            '' 正常終了
            ''----------------------------------------------------------------------

            Return True
        End Function
#End Region

#Region "Execute5:Excel"
        '''========================================================================================
        ''' <SUMMARY>実行</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Execute5_Header() As Boolean

            Me.LastError = Nothing
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim xls As New BaseCore.Common.Excel

            Try

                '----------------------------------------------------------------------
                ' ｵｰﾌﾟﾝ
                '----------------------------------------------------------------------
                If Not xls.Open(Me.Path1) Then
                    Me.LastError = "テンプレートが見つかりません。(" & Me.Path1 & ")"
                    Return False
                End If

                '------------------------------------------------------------------------
                ' 作成
                '------------------------------------------------------------------------
                xls.SheetSelect("第十号様式")       'ｱｸﾃｨﾌﾞｼｰﾄ

                If Not MakeExcelDataDai10gou(xls) Then
                    MyBase.LastError = Me.LastError
                    Return False
                End If

                ''----------------------------------------------------------------------
                ' 先頭へｶｰｿﾙ移動
                '----------------------------------------------------------------------
                'xls.CellGet(1, 1)

                xls.Close(Me.Path1)
            Catch ex As Exception
                Me.LastError = ex.Message
                Return False
            Finally
            End Try


            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------

            ''----------------------------------------------------------------------
            '' ﾃﾞｰﾀ加工(前)
            ''----------------------------------------------------------------------
            'tbl.TableName = "乗務員"

            ''----------------------------------------------------------------------
            '' ｴｸｽﾎﾟｰﾄ
            ''----------------------------------------------------------------------
            'Dim Excel As New BaseCore.Common.Excel
            'If Not Excel.ExcelExport(tbl, Me.Path1) Then
            '    Me.LastError = Excel.LastError
            '    Return False
            'End If

            ''----------------------------------------------------------------------
            '' 正常終了
            ''----------------------------------------------------------------------

            Return True
        End Function
#End Region

#Region "Execute6:Excel"
        '''========================================================================================
        ''' <SUMMARY>実行</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Execute6_Header() As Boolean

            Me.LastError = Nothing
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim xls As New BaseCore.Common.Excel

            Try

                '----------------------------------------------------------------------
                ' ｵｰﾌﾟﾝ
                '----------------------------------------------------------------------
                If Not xls.Open(Me.Path1) Then
                    Me.LastError = "テンプレートが見つかりません。(" & Me.Path1 & ")"
                    Return False
                End If

                '------------------------------------------------------------------------
                ' 作成
                '------------------------------------------------------------------------
                xls.SheetSelect("雇用条件及び雇用契約証明")       'ｱｸﾃｨﾌﾞｼｰﾄ

                If Not MakeExcelDataConditionNContract(xls) Then
                    MyBase.LastError = Me.LastError
                    Return False
                End If

                ''----------------------------------------------------------------------
                ' 先頭へｶｰｿﾙ移動
                '----------------------------------------------------------------------
                'xls.CellGet(1, 1)

                xls.Close(Me.Path1)
            Catch ex As Exception
                Me.LastError = ex.Message
                Return False
            Finally
            End Try


            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------

            ''----------------------------------------------------------------------
            '' ﾃﾞｰﾀ加工(前)
            ''----------------------------------------------------------------------
            'tbl.TableName = "乗務員"

            ''----------------------------------------------------------------------
            '' ｴｸｽﾎﾟｰﾄ
            ''----------------------------------------------------------------------
            'Dim Excel As New BaseCore.Common.Excel
            'If Not Excel.ExcelExport(tbl, Me.Path1) Then
            '    Me.LastError = Excel.LastError
            '    Return False
            'End If

            ''----------------------------------------------------------------------
            '' 正常終了
            ''----------------------------------------------------------------------

            Return True
        End Function
#End Region

#Region "エクセルデータ作成"
        '''========================================================================================
        ''' <SUMMARY>抽出したﾃｰﾌﾞﾙに編集を加える</SUMMARY>   
        '''========================================================================================
        Public Function MakeExcelData(ByRef tbl As NodeDatabase.DataSetView.V0乗務員データ出力DataTable, ByRef xls As BaseCore.Common.Excel) As Boolean
            '------------------------------------------------------------------------
            ' 変数定義
            '------------------------------------------------------------------------
            Dim intDtlCnt As Integer = ConstantExcel.Header.RowTop

            Try

                '    Dim myCal As JapaneseCalendar = New JapaneseCalendar()

                '    Dim tDate As Date = Date.Parse(Fields.Header.日報日付.Value)

                '    xls.CellSetValue(1, 1, (tDate.Year - 2017).ToString())
                '    xls.CellSetValue(1, 3, tDate.Month.ToString())
                '    xls.CellSetValue(1, 5, tDate.Day.ToString())
                '    xls.CellSetValue(1, 7, [Enum].GetName(GetType(ConstantExcel.DAYS), myCal.GetDayOfWeek(tDate) + 1))

                xls.InsertRow(ConstantExcel.Header.SheetName, intDtlCnt + 2, tbl.Rows.Count - 1)

                For Each row As NodeDatabase.DataSetView.V0乗務員データ出力Row In tbl.Rows


                    '------------------------------------------------------------------------
                    ' 値設定 明細
                    '-----------------------------------------------------------------------
                    'xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.グループCD, BaseCore.Common.Text.Nz(row.Item(tbl.グループCDColumn.ColumnName), ""))
                    'xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.個社CD, BaseCore.Common.Text.Nz(row.Item(tbl.個社CDColumn.ColumnName), ""))
                    'xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.子会社CD, BaseCore.Common.Text.Nz(row.Item(tbl.子会社CDColumn.ColumnName), ""))
                    'xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.乗務員CD, BaseCore.Common.Text.Nz(row.Item(tbl.乗務員CDColumn.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.乗務員名, BaseCore.Common.Text.Nz(row.Item(tbl.乗務員名Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.社員NO, BaseCore.Common.Text.Nz(row.Item(tbl.乗務員CDColumn.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.乗務員CD, BaseCore.Common.Text.Nz(row.Item(tbl.社員NOColumn.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.勤務体系, BaseCore.Common.Text.Nz(row.Item(tbl.勤務体系Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.住所_都道府県, BaseCore.Common.Text.Nz(row.Item(tbl.住所_都道府県Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.住所_市町村, BaseCore.Common.Text.Nz(row.Item(tbl.住所_市町村Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.住所_地区, BaseCore.Common.Text.Nz(row.Item(tbl.住所_地区Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.電話番号, BaseCore.Common.Text.Nz(row.Item(tbl.電話番号Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.電話番号2, BaseCore.Common.Text.Nz(row.Item(tbl.電話番号2Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.緊急連絡先1, BaseCore.Common.Text.Nz(row.Item(tbl.緊急連絡先1Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.続柄1, BaseCore.Common.Text.Nz(row.Item(tbl.続柄1Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.緊急連絡先2, BaseCore.Common.Text.Nz(row.Item(tbl.緊急連絡先2Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.続柄2, BaseCore.Common.Text.Nz(row.Item(tbl.続柄2Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.生年月日, BaseCore.Common.Text.Nz(row.Item(tbl.生年月日Column.ColumnName), ""))
                    If BaseCore.Common.Text.Nz(row.Item(tbl.入社年月日Column.ColumnName), "") <> "" Then
                        xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.入社年月日, Format(CDate(BaseCore.Common.Text.Nz(row.Item(tbl.入社年月日Column.ColumnName), "")), "yyyy/MM/dd"))
                    End If
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.基準日, "=NOW()")
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.作業領域1, "=DATEDIF(N" & (intDtlCnt + 1).ToString() & ",P" & (intDtlCnt + 1).ToString() & ",""m"")")
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.作業領域2, "=DATEDIF(O" & (intDtlCnt + 1).ToString() & ",P" & (intDtlCnt + 1).ToString() & ",""m"")")
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.年齢, "=INT(Q" & (intDtlCnt + 1).ToString() & "/12)+MOD(Q" & (intDtlCnt + 1).ToString() & ",12)/100")
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.勤続, "=INT(DATEDIF(O" & (intDtlCnt + 1).ToString() & ",NOW(),""d"")/365)")
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.現状通勤, BaseCore.Common.Text.Nz(row.Item(tbl.現状通勤Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.通勤時間, BaseCore.Common.Text.Nz(row.Item(tbl.通勤時間Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.家族構成, BaseCore.Common.Text.Nz(row.Item(tbl.家族構成Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.扶養者数, BaseCore.Common.Text.Nz(row.Item(tbl.扶養者数Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.備考, BaseCore.Common.Text.Nz(row.Item(tbl.備考Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.健康診断受診日, BaseCore.Common.Text.Nz(row.Item(tbl.健康診断受診日Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.汎用項目1, BaseCore.Common.Text.Nz(row.Item(tbl.汎用項目1Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.汎用項目2, BaseCore.Common.Text.Nz(row.Item(tbl.汎用項目2Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.汎用項目3, BaseCore.Common.Text.Nz(row.Item(tbl.汎用項目3Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.汎用項目4, BaseCore.Common.Text.Nz(row.Item(tbl.汎用項目4Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.汎用項目5, BaseCore.Common.Text.Nz(row.Item(tbl.汎用項目5Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.汎用項目6, BaseCore.Common.Text.Nz(row.Item(tbl.汎用項目6Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.汎用項目7, BaseCore.Common.Text.Nz(row.Item(tbl.汎用項目7Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.汎用項目8, BaseCore.Common.Text.Nz(row.Item(tbl.汎用項目8Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.汎用項目9, BaseCore.Common.Text.Nz(row.Item(tbl.汎用項目9Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.汎用項目10, BaseCore.Common.Text.Nz(row.Item(tbl.汎用項目10Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.汎用項目11, BaseCore.Common.Text.Nz(row.Item(tbl.汎用項目11Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.汎用項目12, BaseCore.Common.Text.Nz(row.Item(tbl.汎用項目12Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.汎用項目13, BaseCore.Common.Text.Nz(row.Item(tbl.汎用項目13Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.汎用項目14, BaseCore.Common.Text.Nz(row.Item(tbl.汎用項目14Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.汎用項目15, BaseCore.Common.Text.Nz(row.Item(tbl.汎用項目15Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.汎用項目16, BaseCore.Common.Text.Nz(row.Item(tbl.汎用項目16Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.汎用項目17, BaseCore.Common.Text.Nz(row.Item(tbl.汎用項目17Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.汎用項目18, BaseCore.Common.Text.Nz(row.Item(tbl.汎用項目18Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.汎用項目19, BaseCore.Common.Text.Nz(row.Item(tbl.汎用項目19Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.汎用項目20, BaseCore.Common.Text.Nz(row.Item(tbl.汎用項目20Column.ColumnName), ""))
                    'xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.登録ユーザーCD, BaseCore.Common.Text.Nz(row.Item(tbl.登録ユーザーCDColumn.ColumnName), ""))
                    'xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.登録ユーザー名, BaseCore.Common.Text.Nz(row.Item(tbl.登録ユーザー名Column.ColumnName), ""))
                    'xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.登録日時, BaseCore.Common.Text.Nz(row.Item(tbl.登録日時Column.ColumnName), ""))
                    'xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.更新ユーザーCD, BaseCore.Common.Text.Nz(row.Item(tbl.更新ユーザーCDColumn.ColumnName), ""))
                    'xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.更新ユーザー名, BaseCore.Common.Text.Nz(row.Item(tbl.更新ユーザー名Column.ColumnName), ""))
                    'xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.更新日時, BaseCore.Common.Text.Nz(row.Item(tbl.更新日時Column.ColumnName), ""))
                    intDtlCnt += 1
                Next

            Catch ex As Exception
                Me.LastError = ex.Message
                Return False
            Finally
            End Try
            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "第二号データ作成"
        '''========================================================================================
        ''' <SUMMARY>抽出したﾃｰﾌﾞﾙに編集を加える</SUMMARY>   
        '''========================================================================================
        Public Function MakeExcelDataDai2gou(ByRef xls As BaseCore.Common.Excel) As Boolean


            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetViewTableAdapters.V0子会社TableAdapter
            Dim tbl As New NodeDatabase.DataSetView.V0子会社DataTable
            Dim qry As New Collection
            Dim strTaxiCenter = ""
            Dim strJigyouCD = ""
            Dim strCompanyName = ""
            Dim strCompanyNameFurigana = ""
            Dim strCompanyAddress = ""
            Dim strCompanyAddressFurigana = ""
            '----------------------------------------------------------------------
            ' 条件設定
            '----------------------------------------------------------------------
            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, Me.Fields.Header.グループCD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, Me.Fields.Header.個社CD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.子会社CDColumn.ColumnName, Me.Fields.Header.子会社CD.Value, BaseDatabase.Contents.Compare.Equal))

            '----------------------------------------------------------------------
            ' 読込
            '----------------------------------------------------------------------
            tbl = ada.SelectByCommon(qry)
            If tbl.Count > 0 Then
                strCompanyName = tbl(0).Item(tbl.子会社名Column.ColumnName)
                strCompanyNameFurigana = tbl(0).Item(tbl.子会社名フリガナColumn.ColumnName)
                strCompanyAddress = tbl(0).Item(tbl.子会社住所Column.ColumnName)
                strCompanyAddressFurigana = tbl(0).Item(tbl.子会社住所フリガナColumn.ColumnName)
                strJigyouCD = tbl(0).Item(tbl.事業者CDColumn.ColumnName)
                strTaxiCenter = tbl(0).Item(tbl.タクシーセンターColumn.ColumnName)
            End If


            '------------------------------------------------------------------------
            ' 変数定義
            '------------------------------------------------------------------------

            Dim intDtlCnt As Integer = ConstantExcel.Header.RowTop
            Dim stp As Integer = 0
            Try
                stp = 1
                For Each ch As Char In Me.Fields.Header.運転免許証の番号.Value
                    xls.CellSetValue(7, stp, ch)
                    stp += 1
                Next

                'Dim strReiwaYear = (Now.Year - 2018).ToString.PadLeft(2, "0")
                Dim strReiwaYear = Now.Year.ToString
                Dim strMnth = Now.Month.ToString.PadLeft(2, "0")
                Dim strDay = Now.Day.ToString.PadLeft(2, "0")

                xls.CellSetValue(7, 42, strReiwaYear(0))
                xls.CellSetValue(7, 43, strReiwaYear(1))
                xls.CellSetValue(7, 44, strReiwaYear(2))
                xls.CellSetValue(7, 45, strReiwaYear(3))
                xls.CellSetValue(7, 47, strMnth(0))
                xls.CellSetValue(7, 48, strMnth(1))
                xls.CellSetValue(7, 50, strDay(0))
                xls.CellSetValue(7, 51, strDay(1))

                xls.CellSetValue(12, 16, Me.Fields.Header.乗務員氏フリガナ.Value)
                xls.CellSetValue(13, 16, Me.Fields.Header.乗務員氏.Value)
                xls.CellSetValue(12, 27, Me.Fields.Header.乗務員名フリガナ.Value)
                xls.CellSetValue(13, 27, Me.Fields.Header.乗務員名.Value)

                xls.CellSetValue(17, 16, Me.Fields.Header.住所_都道府県.Value)
                xls.CellSetValue(16, 25, Me.Fields.Header.住所_都道府県フリガナ.Value & Me.Fields.Header.住所_地区フリガナ.Value & Me.Fields.Header.住所_市町村フリガナ.Value)
                xls.CellSetValue(17, 24, Me.Fields.Header.住所_地区.Value & Me.Fields.Header.住所_市町村.Value)

                Dim strBirthDay = Me.Fields.Header.生年月日.Value
                Dim format As String = "yyyy/MM/dd"
                Dim isValidDate As Boolean = DateTime.TryParseExact(strBirthDay, format, CultureInfo.InvariantCulture, DateTimeStyles.None, Nothing)

                If isValidDate Then
                    Dim parsedDate As DateTime = DateTime.ParseExact(strBirthDay, format, CultureInfo.InvariantCulture)

                    strReiwaYear = parsedDate.Year.ToString
                    strMnth = parsedDate.Month.ToString.PadLeft(2, "0")
                    strDay = parsedDate.Day.ToString.PadLeft(2, "0")

                    xls.CellSetValue(14, 40, strReiwaYear(0))
                    xls.CellSetValue(14, 41, strReiwaYear(1))
                    xls.CellSetValue(14, 42, strReiwaYear(2))
                    xls.CellSetValue(14, 43, strReiwaYear(3))
                    xls.CellSetValue(14, 45, strMnth(0))
                    xls.CellSetValue(14, 46, strMnth(1))
                    xls.CellSetValue(14, 48, strDay(0))
                    xls.CellSetValue(14, 49, strDay(1))
                End If


                Dim strDate = Me.Fields.Header.運転免許証の有効期限.Value
                isValidDate = DateTime.TryParseExact(strDate, format, CultureInfo.InvariantCulture, DateTimeStyles.None, Nothing)

                If isValidDate Then
                    Dim parsedDate As DateTime = DateTime.ParseExact(strDate, format, CultureInfo.InvariantCulture)

                    strReiwaYear = parsedDate.Year.ToString
                    strMnth = parsedDate.Month.ToString.PadLeft(2, "0")
                    strDay = parsedDate.Day.ToString.PadLeft(2, "0")

                    xls.CellSetValue(10, 1, strReiwaYear(0))
                    xls.CellSetValue(10, 2, strReiwaYear(1))
                    xls.CellSetValue(10, 3, strReiwaYear(2))
                    xls.CellSetValue(10, 4, strReiwaYear(3))
                    xls.CellSetValue(10, 6, strMnth(0))
                    xls.CellSetValue(10, 7, strMnth(1))
                    xls.CellSetValue(10, 9, strDay(0))
                    xls.CellSetValue(10, 10, strDay(1))
                End If

                If Me.Fields.Header.運転免許の種類.Value = "" Then
                    xls.Sheet.Shapes("Circle1").Delete()
                    xls.Sheet.Shapes("Circle2").Delete()
                    xls.Sheet.Shapes("Circle3").Delete()
                End If

                If Me.Fields.Header.運転免許の種類.Value = "1" Then
                        xls.Sheet.Shapes("Circle2").Delete()
                        xls.Sheet.Shapes("Circle3").Delete()
                    End If

                    If Me.Fields.Header.運転免許の種類.Value = "2" Then
                        xls.Sheet.Shapes("Circle1").Delete()
                        xls.Sheet.Shapes("Circle3").Delete()
                    End If

                If Me.Fields.Header.運転免許の種類.Value = "3" Then
                    xls.Sheet.Shapes("Circle1").Delete()
                    xls.Sheet.Shapes("Circle2").Delete()
                End If

                xls.CellSetValue(10, 31, strTaxiCenter)
                xls.CellSetValue(19, 16, strJigyouCD)
                xls.CellSetValue(19, 21, strCompanyName)
                xls.CellSetValue(21, 21, strCompanyAddress)
                xls.CellSetValue(23, 18, strCompanyName)
                xls.CellSetValue(25, 18, strCompanyAddress)
            Catch ex As Exception
                Me.LastError = ex.Message
                Return False
            Finally
            End Try
            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "第四号データ作成"
        '''========================================================================================
        ''' <SUMMARY>抽出したﾃｰﾌﾞﾙに編集を加える</SUMMARY>   
        '''========================================================================================
        Public Function MakeExcelDataDai4gou(ByRef xls As BaseCore.Common.Excel) As Boolean


            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetViewTableAdapters.V0子会社TableAdapter
            Dim tbl As New NodeDatabase.DataSetView.V0子会社DataTable
            Dim qry As New Collection
            Dim strTaxiCenter = ""
            Dim strJigyouCD = ""
            Dim strCompanyName = ""
            Dim strCompanyNameNew = ""
            Dim strCompanyNameFurigana = ""
            Dim strCompanyAddress = ""
            Dim strCompanyAddressNew = ""
            Dim strCompanyAddressFurigana = ""
            '----------------------------------------------------------------------
            ' 条件設定
            '----------------------------------------------------------------------
            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, Me.Fields.Header.グループCD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, Me.Fields.Header.個社CD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.子会社CDColumn.ColumnName, Me.Fields.Header.子会社CD.Value, BaseDatabase.Contents.Compare.Equal))

            '----------------------------------------------------------------------
            ' 読込
            '----------------------------------------------------------------------
            tbl = ada.SelectByCommon(qry)
            If tbl.Count > 0 Then
                strCompanyName = tbl(0).Item(tbl.子会社名Column.ColumnName)
                strCompanyNameFurigana = tbl(0).Item(tbl.子会社名フリガナColumn.ColumnName)
                strCompanyAddress = tbl(0).Item(tbl.子会社住所Column.ColumnName)
                strCompanyAddressFurigana = tbl(0).Item(tbl.子会社住所フリガナColumn.ColumnName)
                strJigyouCD = tbl(0).Item(tbl.事業者CDColumn.ColumnName)
                strTaxiCenter = tbl(0).Item(tbl.タクシーセンターColumn.ColumnName)
                strCompanyNameNew = tbl(0).Item(tbl.子会社名Column.ColumnName)
                strCompanyAddressNew = tbl(0).Item(tbl.子会社住所Column.ColumnName)

            End If

            '------------------------------------------------------------------------
            ' 変数定義
            '------------------------------------------------------------------------
            Dim intDtlCnt As Integer = ConstantExcel.Header.RowTop
            Dim stp As Integer = 0
            Try

                '------------------------------------------------------------------------
                ' 登録番号
                '------------------------------------------------------------------------
                stp = 8
                For Each ch As Char In Me.Fields.Header.登録番号.Value
                    xls.CellSetValue(5, stp, ch)
                    stp += 1
                Next


                Dim strReiwaYear = (Now.Year - 2018).ToString.PadLeft(2, "0")
                'Dim strReiwaYear = Now.Year.ToString
                Dim strMnth = Now.Month.ToString.PadLeft(2, "0")
                Dim strDay = Now.Day.ToString.PadLeft(2, "0")

                xls.CellSetValue(5, 44, strReiwaYear(0))
                xls.CellSetValue(5, 45, strReiwaYear(1))
                xls.CellSetValue(5, 47, strMnth(0))
                xls.CellSetValue(5, 48, strMnth(1))
                xls.CellSetValue(5, 50, strDay(0))
                xls.CellSetValue(5, 51, strDay(1))

                '------------------------------------------------------------------------
                ' 運転免許証の番号
                '------------------------------------------------------------------------
                If Me.Fields.Header.ChkMenkyoNo.Value = 1 Then
                    stp = 11
                    For Each ch As Char In Me.Fields.Header.運転免許証の番号_新.Value
                        xls.CellSetValue(7, stp, ch)
                        stp += 1
                    Next
                End If


                stp = 11
                For Each ch As Char In Me.Fields.Header.運転免許証の番号.Value
                    xls.CellSetValue(8, stp, ch)
                    stp += 1
                Next

                If Me.Fields.Header.ChkMenkyoNo.Value = 1 Then

                End If

                '------------------------------------------------------------------------
                ' 運転免許証の有効期限
                '------------------------------------------------------------------------
                Dim strDate = Me.Fields.Header.運転免許証の有効期限.Value
                Dim format As String = "yyyy/MM/dd"
                Dim isValidDate = DateTime.TryParseExact(strDate, format, CultureInfo.InvariantCulture, DateTimeStyles.None, Nothing)

                If isValidDate Then
                    Dim parsedDate As DateTime = DateTime.ParseExact(strDate, format, CultureInfo.InvariantCulture)

                    strReiwaYear = (parsedDate.Year - 2018).ToString.PadLeft(2, "0")
                    strMnth = parsedDate.Month.ToString.PadLeft(2, "0")
                    strDay = parsedDate.Day.ToString.PadLeft(2, "0")

                    xls.CellSetValue(10, 13, strReiwaYear(0))
                    xls.CellSetValue(10, 14, strReiwaYear(1))
                    xls.CellSetValue(10, 16, strMnth(0))
                    xls.CellSetValue(10, 17, strMnth(1))
                    xls.CellSetValue(10, 19, strDay(0))
                    xls.CellSetValue(10, 20, strDay(1))
                End If

                If Me.Fields.Header.ChkMenkyoExpiryDate.Value = 1 Then
                    strDate = Me.Fields.Header.運転免許証の有効期限_新.Value
                    isValidDate = DateTime.TryParseExact(strDate, format, CultureInfo.InvariantCulture, DateTimeStyles.None, Nothing)

                    If isValidDate Then
                        Dim parsedDate As DateTime = DateTime.ParseExact(strDate, format, CultureInfo.InvariantCulture)

                        strReiwaYear = (parsedDate.Year - 2018).ToString.PadLeft(2, "0")
                        strMnth = parsedDate.Month.ToString.PadLeft(2, "0")
                        strDay = parsedDate.Day.ToString.PadLeft(2, "0")

                        xls.CellSetValue(9, 13, strReiwaYear(0))
                        xls.CellSetValue(9, 14, strReiwaYear(1))
                        xls.CellSetValue(9, 16, strMnth(0))
                        xls.CellSetValue(9, 17, strMnth(1))
                        xls.CellSetValue(9, 19, strDay(0))
                        xls.CellSetValue(9, 20, strDay(1))
                    End If
                End If

                '------------------------------------------------------------------------
                ' 運転免許の種類
                '------------------------------------------------------------------------
                If Me.Fields.Header.ChkMenkyoType.Value = 0 Then
                    If Me.Fields.Header.運転免許の種類_新.Value = "" Then
                        xls.Sheet.Shapes("Circle1").Delete()
                        xls.Sheet.Shapes("Circle2").Delete()
                        xls.Sheet.Shapes("Circle3").Delete()
                    End If

                    If Me.Fields.Header.運転免許の種類_新.Value = "1" Then
                        xls.Sheet.Shapes("Circle1").Delete()
                        xls.Sheet.Shapes("Circle2").Delete()
                        xls.Sheet.Shapes("Circle3").Delete()
                    End If

                    If Me.Fields.Header.運転免許の種類_新.Value = "2" Then
                        xls.Sheet.Shapes("Circle1").Delete()
                        xls.Sheet.Shapes("Circle2").Delete()
                        xls.Sheet.Shapes("Circle3").Delete()
                    End If

                    If Me.Fields.Header.運転免許の種類_新.Value = "3" Then
                        xls.Sheet.Shapes("Circle1").Delete()
                        xls.Sheet.Shapes("Circle2").Delete()
                        xls.Sheet.Shapes("Circle3").Delete()
                    End If
                End If

                If Me.Fields.Header.ChkMenkyoType.Value = 1 Then
                    If Me.Fields.Header.運転免許の種類_新.Value = "" Then
                        xls.Sheet.Shapes("Circle1").Delete()
                        xls.Sheet.Shapes("Circle2").Delete()
                        xls.Sheet.Shapes("Circle3").Delete()
                    End If

                    If Me.Fields.Header.運転免許の種類_新.Value = "1" Then
                        xls.Sheet.Shapes("Circle2").Delete()
                        xls.Sheet.Shapes("Circle3").Delete()
                    End If

                    If Me.Fields.Header.運転免許の種類_新.Value = "2" Then
                        xls.Sheet.Shapes("Circle1").Delete()
                        xls.Sheet.Shapes("Circle3").Delete()
                    End If

                    If Me.Fields.Header.運転免許の種類_新.Value = "3" Then
                        xls.Sheet.Shapes("Circle1").Delete()
                        xls.Sheet.Shapes("Circle2").Delete()
                    End If
                End If

                If Me.Fields.Header.運転免許の種類.Value = "" Then
                    xls.Sheet.Shapes("Circle4").Delete()
                    xls.Sheet.Shapes("Circle5").Delete()
                    xls.Sheet.Shapes("Circle6").Delete()
                End If

                If Me.Fields.Header.運転免許の種類.Value = "1" Then
                    xls.Sheet.Shapes("Circle5").Delete()
                    xls.Sheet.Shapes("Circle6").Delete()
                End If

                If Me.Fields.Header.運転免許の種類.Value = "2" Then
                    xls.Sheet.Shapes("Circle4").Delete()
                    xls.Sheet.Shapes("Circle6").Delete()
                End If

                If Me.Fields.Header.運転免許の種類.Value = "3" Then
                    xls.Sheet.Shapes("Circle4").Delete()
                    xls.Sheet.Shapes("Circle5").Delete()
                End If


                '------------------------------------------------------------------------
                ' 氏名
                '------------------------------------------------------------------------

                xls.CellSetValue(14, 13, Me.Fields.Header.乗務員氏フリガナ.Value)
                xls.CellSetValue(16, 12, Me.Fields.Header.乗務員氏.Value)
                xls.CellSetValue(14, 20, Me.Fields.Header.乗務員名フリガナ.Value)
                xls.CellSetValue(16, 21, Me.Fields.Header.乗務員名.Value)

                If Me.Fields.Header.ChkName.Value = 1 Then
                    xls.CellSetValue(15, 12, Me.Fields.Header.乗務員氏_新.Value)
                    xls.CellSetValue(15, 21, Me.Fields.Header.乗務員名_新.Value)

                    If Me.Fields.Header.乗務員氏フリガナ_新.Value <> "" Or Me.Fields.Header.乗務員名フリガナ_新.Value <> "" Then
                        xls.CellSetValue(14, 13, Me.Fields.Header.乗務員氏フリガナ_新.Value)
                        xls.CellSetValue(14, 20, Me.Fields.Header.乗務員名フリガナ_新.Value)
                    End If
                End If

                '------------------------------------------------------------------------
                ' 住所
                '------------------------------------------------------------------------
                xls.CellSetValue(17, 13, Me.Fields.Header.住所_都道府県フリガナ.Value & Me.Fields.Header.住所_地区フリガナ.Value & Me.Fields.Header.住所_市町村フリガナ.Value)
                xls.CellSetValue(19, 11, Me.Fields.Header.住所_都道府県.Value)
                xls.CellSetValue(19, 16, Me.Fields.Header.住所_地区.Value & Me.Fields.Header.住所_市町村.Value)

                If Me.Fields.Header.ChkAddress.Value = 1 Then
                    xls.CellSetValue(18, 11, Me.Fields.Header.住所_都道府県_新.Value)
                    xls.CellSetValue(18, 16, Me.Fields.Header.住所_地区_新.Value & Me.Fields.Header.住所_市町村_新.Value)

                    If Me.Fields.Header.住所_都道府県_新.Value <> "" Or Me.Fields.Header.住所_地区_新.Value & Me.Fields.Header.住所_市町村_新.Value <> "" Then
                        xls.CellSetValue(17, 13, Me.Fields.Header.住所_都道府県フリガナ_新.Value & Me.Fields.Header.住所_地区フリガナ_新.Value & Me.Fields.Header.住所_市町村フリガナ_新.Value)
                    End If
                End If

                '------------------------------------------------------------------------
                ' 事業所
                '------------------------------------------------------------------------
                xls.CellSetValue(5, 22, strTaxiCenter)
                xls.CellSetValue(15, 44, strJigyouCD)
                xls.CellSetValue(17, 37, strCompanyName)
                xls.CellSetValue(19, 37, strCompanyAddress)
                xls.CellSetValue(21, 18, strCompanyName)
                xls.CellSetValue(23, 18, strCompanyAddress)

                If Me.Fields.Header.ChkJIgyousyo.Value = 1 Then
                    xls.CellSetValue(17, 37, Me.Fields.Header.子会社名_旧.Value)
                    xls.CellSetValue(19, 37, Me.Fields.Header.子会社住所_旧.Value)
                    xls.CellSetValue(16, 37, strCompanyNameNew)
                    xls.CellSetValue(18, 37, strCompanyAddressNew)
                End If




            Catch ex As Exception
                Me.LastError = ex.Message
                Return False
            Finally
            End Try
            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "第九号データ作成"
        '''========================================================================================
        ''' <SUMMARY>抽出したﾃｰﾌﾞﾙに編集を加える</SUMMARY>   
        '''========================================================================================
        Public Function MakeExcelDataDai9gou(ByRef xls As BaseCore.Common.Excel) As Boolean


            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetViewTableAdapters.V0子会社TableAdapter
            Dim tbl As New NodeDatabase.DataSetView.V0子会社DataTable
            Dim qry As New Collection
            Dim strTaxiCenter = ""
            Dim strJigyouCD = ""
            Dim strCompanyName = ""
            Dim strCompanyNameFurigana = ""
            Dim strCompanyAddress = ""
            Dim strCompanyAddressFurigana = ""
            '----------------------------------------------------------------------
            ' 条件設定
            '----------------------------------------------------------------------
            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, Me.Fields.Header.グループCD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, Me.Fields.Header.個社CD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.子会社CDColumn.ColumnName, Me.Fields.Header.子会社CD.Value, BaseDatabase.Contents.Compare.Equal))

            '----------------------------------------------------------------------
            ' 読込
            '----------------------------------------------------------------------
            tbl = ada.SelectByCommon(qry)
            If tbl.Count > 0 Then
                strCompanyName = tbl(0).Item(tbl.子会社名Column.ColumnName)
                strCompanyNameFurigana = tbl(0).Item(tbl.子会社名フリガナColumn.ColumnName)
                strCompanyAddress = tbl(0).Item(tbl.子会社住所Column.ColumnName)
                strCompanyAddressFurigana = tbl(0).Item(tbl.子会社住所フリガナColumn.ColumnName)
                strJigyouCD = tbl(0).Item(tbl.事業者CDColumn.ColumnName)
                strTaxiCenter = tbl(0).Item(tbl.タクシーセンターColumn.ColumnName)
            End If



            '------------------------------------------------------------------------
            ' 変数定義
            '------------------------------------------------------------------------

            Try
                Dim stp As Integer = 0

                stp = 7
                For Each ch As Char In Me.Fields.Header.登録番号.Value
                    xls.CellSetValue(6, stp, ch)
                    stp += 1
                Next


                stp = 1
                For Each ch As Char In Me.Fields.Header.運転免許証の番号.Value
                    xls.CellSetValue(9, stp, ch)
                    stp += 1
                Next

                Dim strReiwaYear = (Now.Year - 2018).ToString.PadLeft(2, "0")
                'Dim strReiwaYear = Now.Year.ToString
                Dim strMnth = Now.Month.ToString.PadLeft(2, "0")
                Dim strDay = Now.Day.ToString.PadLeft(2, "0")

                xls.CellSetValue(7, 42, strReiwaYear(0))
                xls.CellSetValue(7, 43, strReiwaYear(1))
                xls.CellSetValue(7, 45, strMnth(0))
                xls.CellSetValue(7, 46, strMnth(1))
                xls.CellSetValue(7, 48, strDay(0))
                xls.CellSetValue(7, 49, strDay(1))

                xls.CellSetValue(11, 5, Me.Fields.Header.乗務員氏フリガナ.Value)
                xls.CellSetValue(12, 6, Me.Fields.Header.乗務員氏.Value)
                xls.CellSetValue(11, 12, Me.Fields.Header.乗務員名フリガナ.Value)
                xls.CellSetValue(12, 13, Me.Fields.Header.乗務員名.Value)

                xls.CellSetValue(4, 28, strTaxiCenter)
                xls.CellSetValue(10, 46, strJigyouCD)
                xls.CellSetValue(13, 28, strCompanyName)
                xls.CellSetValue(16, 28, strCompanyAddress)


            Catch ex As Exception
                Me.LastError = ex.Message
                Return False
            Finally
            End Try
            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "第十号データ作成"
        '''========================================================================================
        ''' <SUMMARY>抽出したﾃｰﾌﾞﾙに編集を加える</SUMMARY>   
        '''========================================================================================
        Public Function MakeExcelDataDai10gou(ByRef xls As BaseCore.Common.Excel) As Boolean


            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetViewTableAdapters.V0子会社TableAdapter
            Dim tbl As New NodeDatabase.DataSetView.V0子会社DataTable
            Dim qry As New Collection
            Dim strTaxiCenter = ""
            Dim strJigyouCD = ""
            Dim strCompanyName = ""
            Dim strCompanyNameFurigana = ""
            Dim strCompanyAddress = ""
            Dim strCompanyAddressFurigana = ""
            '----------------------------------------------------------------------
            ' 条件設定
            '----------------------------------------------------------------------
            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, Me.Fields.Header.グループCD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, Me.Fields.Header.個社CD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.子会社CDColumn.ColumnName, Me.Fields.Header.子会社CD.Value, BaseDatabase.Contents.Compare.Equal))

            '----------------------------------------------------------------------
            ' 読込
            '----------------------------------------------------------------------
            tbl = ada.SelectByCommon(qry)
            If tbl.Count > 0 Then
                strCompanyName = tbl(0).Item(tbl.子会社名Column.ColumnName)
                strCompanyNameFurigana = tbl(0).Item(tbl.子会社名フリガナColumn.ColumnName)
                strCompanyAddress = tbl(0).Item(tbl.子会社住所Column.ColumnName)
                strCompanyAddressFurigana = tbl(0).Item(tbl.子会社住所フリガナColumn.ColumnName)
                strJigyouCD = tbl(0).Item(tbl.事業者CDColumn.ColumnName)
                strTaxiCenter = tbl(0).Item(tbl.タクシーセンターColumn.ColumnName)
            End If



            '------------------------------------------------------------------------
            ' 変数定義
            '------------------------------------------------------------------------

            Try
                Dim stp As Integer = 0

                stp = 8
                For Each ch As Char In Me.Fields.Header.登録番号.Value
                    xls.CellSetValue(9, stp, ch)
                    stp += 1
                Next


                stp = 2
                For Each ch As Char In Me.Fields.Header.運転免許証の番号.Value
                    xls.CellSetValue(12, stp, ch)
                    stp += 1
                Next

                Dim strReiwaYear = (Now.Year - 2018).ToString.PadLeft(2, "0")
                'Dim strReiwaYear = Now.Year.ToString
                Dim strMnth = Now.Month.ToString.PadLeft(2, "0")
                Dim strDay = Now.Day.ToString.PadLeft(2, "0")

                xls.CellSetValue(10, 42, strReiwaYear(0))
                xls.CellSetValue(10, 43, strReiwaYear(1))
                xls.CellSetValue(10, 45, strMnth(0))
                xls.CellSetValue(10, 46, strMnth(1))
                xls.CellSetValue(10, 48, strDay(0))
                xls.CellSetValue(10, 49, strDay(1))

                xls.CellSetValue(14, 6, Me.Fields.Header.乗務員氏フリガナ.Value)
                xls.CellSetValue(15, 7, Me.Fields.Header.乗務員氏.Value)
                xls.CellSetValue(14, 13, Me.Fields.Header.乗務員名フリガナ.Value)
                xls.CellSetValue(15, 14, Me.Fields.Header.乗務員名.Value)

                xls.CellSetValue(7, 28, strTaxiCenter)
                xls.CellSetValue(12, 46, strJigyouCD)
                xls.CellSetValue(14, 27, strCompanyName)
                xls.CellSetValue(17, 27, strCompanyAddress)


            Catch ex As Exception
                Me.LastError = ex.Message
                Return False
            Finally
            End Try
            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "雇用条件及び雇用契約証明データ作成"
        '''========================================================================================
        ''' <SUMMARY>抽出したﾃｰﾌﾞﾙに編集を加える</SUMMARY>   
        '''========================================================================================
        Public Function MakeExcelDataConditionNContract(ByRef xls As BaseCore.Common.Excel) As Boolean


            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetViewTableAdapters.V0子会社TableAdapter
            Dim tbl As New NodeDatabase.DataSetView.V0子会社DataTable
            Dim qry As New Collection
            Dim strTaxiCenter = ""
            Dim strJigyouCD = ""
            Dim strCompanyName = ""
            Dim strCompanyNameFurigana = ""
            Dim strCompanyAddress = ""
            Dim strCompanyAddressFurigana = ""
            '----------------------------------------------------------------------
            ' 条件設定
            '----------------------------------------------------------------------
            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, Me.Fields.Header.グループCD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, Me.Fields.Header.個社CD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.子会社CDColumn.ColumnName, Me.Fields.Header.子会社CD.Value, BaseDatabase.Contents.Compare.Equal))

            '----------------------------------------------------------------------
            ' 読込
            '----------------------------------------------------------------------
            tbl = ada.SelectByCommon(qry)
            If tbl.Count > 0 Then
                strCompanyName = tbl(0).Item(tbl.子会社名Column.ColumnName)
                strCompanyNameFurigana = tbl(0).Item(tbl.子会社名フリガナColumn.ColumnName)
                strCompanyAddress = tbl(0).Item(tbl.子会社住所Column.ColumnName)
                strCompanyAddressFurigana = tbl(0).Item(tbl.子会社住所フリガナColumn.ColumnName)
                strJigyouCD = tbl(0).Item(tbl.事業者CDColumn.ColumnName)
                strTaxiCenter = tbl(0).Item(tbl.タクシーセンターColumn.ColumnName)
            End If



            '------------------------------------------------------------------------
            ' 変数定義
            '------------------------------------------------------------------------
            Try
                xls.CellSetValue(3, 22, Me.Fields.Header.乗務員氏.Value)
                xls.CellSetValue(3, 27, Me.Fields.Header.乗務員名.Value)

                xls.CellSetValue(5, 22, Me.Fields.Header.住所_都道府県.Value & Me.Fields.Header.住所_地区.Value & Me.Fields.Header.住所_市町村.Value)


                Dim strEntryDay = Me.Fields.Header.入社年月日.Value
                Dim format As String = "yyyy/MM/dd"
                Dim isEntryDate As Boolean = DateTime.TryParseExact(strEntryDay, format, CultureInfo.InvariantCulture, DateTimeStyles.None, Nothing)

                Dim strReiwaYear = (Now.Year - 2018).ToString.PadLeft(2, "0")
                Dim strMnth = Now.Month.ToString.PadLeft(2, "0")
                Dim strDay = Now.Day.ToString.PadLeft(2, "0")

                xls.CellSetValue(21, 4, strReiwaYear(0))
                xls.CellSetValue(21, 5, strReiwaYear(1))
                xls.CellSetValue(21, 8, strMnth(0))
                xls.CellSetValue(21, 9, strMnth(1))
                xls.CellSetValue(21, 12, strDay(0))
                xls.CellSetValue(21, 13, strDay(1))

                If isEntryDate Then

                    Dim parsedDate As DateTime = DateTime.Parse(strEntryDay)
                    strReiwaYear = parsedDate.Year.ToString
                    strMnth = parsedDate.Month.ToString.PadLeft(2, "0")
                    strDay = parsedDate.Day.ToString.PadLeft(2, "0")

                    xls.CellSetValue(11, 10, strReiwaYear(0))
                    xls.CellSetValue(11, 11, strReiwaYear(1))
                    xls.CellSetValue(11, 12, strReiwaYear(2))
                    xls.CellSetValue(11, 13, strReiwaYear(3))
                    xls.CellSetValue(11, 15, strMnth(0))
                    xls.CellSetValue(11, 16, strMnth(1))
                    xls.CellSetValue(11, 18, strDay(0))
                    xls.CellSetValue(11, 19, strDay(1))
                End If



                xls.CellSetValue(23, 20, strCompanyName)
                xls.CellSetValue(24, 21, strCompanyAddress)


            Catch ex As Exception
                Me.LastError = ex.Message
                Return False
            Finally
            End Try
            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region


        Public Function ConvertToReiwa(year As Integer, month As Integer, day As Integer) As String
            Dim reiwaYear As Integer = year - 2018 ' 令和元年は2019年なので、差分を求める
            Return "令和" & reiwaYear.ToString() & "年" & month.ToString("D2") & "月" & day.ToString("D2") & "日"
        End Function

#End Region
    End Class
End Namespace
