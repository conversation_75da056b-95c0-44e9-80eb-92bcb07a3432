﻿Namespace Frame.Master
    Partial Public Class Company
        Inherits NodeCore.Common.Frame

#Region "ﾌｨｰﾙﾄﾞ定義"
        Public Class Header
            ''' ----------------------------------------------------------------------------------------
            ''' <SUMMARY>列項目(基本)</SUMMARY>
            ''' ----------------------------------------------------------------------------------------
            Public グループCD As BaseCore.Common.Field.ItemData
            Public 個社CD As BaseCore.Common.Field.ItemData
            Public 子会社CD As BaseCore.Common.Field.ItemData
            Public 子会社名 As BaseCore.Common.Field.ItemData
            Public 子会社名フリガナ As BaseCore.Common.Field.ItemData
            Public 子会社住所 As BaseCore.Common.Field.ItemData
            Public 子会社住所フリガナ As BaseCore.Common.Field.ItemData
            Public 事業者CD As BaseCore.Common.Field.ItemData
            Public タクシーセンター As BaseCore.Common.Field.ItemData
            Public 登録ユーザーCD As BaseCore.Common.Field.ItemData
            Public 登録ユーザー名 As BaseCore.Common.Field.ItemData
            Public 登録日時 As BaseCore.Common.Field.ItemData
            Public 更新ユーザーCD As BaseCore.Common.Field.ItemData
            Public 更新ユーザー名 As BaseCore.Common.Field.ItemData
            Public 更新日時 As BaseCore.Common.Field.ItemData
            Public 業務開始日 As BaseCore.Common.Field.ItemData
            Public Tax As BaseCore.Common.Field.ItemData


            '''========================================================================================
            ''' <SUMMARY>ｺﾝｽﾄﾗｸﾀ</SUMMARY>
            '''========================================================================================
            Public Sub New(ByVal Config As NodeCore.Common.Config)
                Dim tbl As New NodeDatabase.DataSetMaster.M子会社DataTable

                '----------------------------------------------------------------------
                ' ｲﾝｽﾀﾝｽ(基本) 
                '----------------------------------------------------------------------
                Me.グループCD = New BaseCore.Common.Field.ItemData(tbl.グループCDColumn)
                Me.個社CD = New BaseCore.Common.Field.ItemData(tbl.個社CDColumn)
                Me.子会社CD = New BaseCore.Common.Field.ItemData(tbl.子会社CDColumn)
                Me.子会社名 = New BaseCore.Common.Field.ItemData(tbl.子会社名Column)
                Me.子会社名フリガナ = New BaseCore.Common.Field.ItemData(tbl.子会社名フリガナColumn)
                Me.子会社住所 = New BaseCore.Common.Field.ItemData(tbl.子会社住所Column)
                Me.子会社住所フリガナ = New BaseCore.Common.Field.ItemData(tbl.子会社住所フリガナColumn)
                Me.事業者CD = New BaseCore.Common.Field.ItemData(tbl.事業者CDColumn)
                Me.タクシーセンター = New BaseCore.Common.Field.ItemData(tbl.タクシーセンターColumn)
                Me.登録ユーザーCD = New BaseCore.Common.Field.ItemData(tbl.登録ユーザーCDColumn)
                Me.登録ユーザー名 = New BaseCore.Common.Field.ItemData(tbl.登録ユーザー名Column)
                Me.登録日時 = New BaseCore.Common.Field.ItemData(tbl.登録日時Column)
                Me.更新ユーザーCD = New BaseCore.Common.Field.ItemData(tbl.更新ユーザーCDColumn)
                Me.更新ユーザー名 = New BaseCore.Common.Field.ItemData(tbl.更新ユーザー名Column)
                Me.更新日時 = New BaseCore.Common.Field.ItemData(tbl.更新日時Column)
                Me.業務開始日 = New BaseCore.Common.Field.ItemData(tbl.業務開始日Column)
                Me.Tax = New BaseCore.Common.Field.ItemData("Tax", TypeCode.Decimal, 15, 4, 2)

            End Sub
        End Class
#End Region

#Region "変数定義"
        Public Class Paramater
            Public Header As Header
        End Class

        '''========================================================================================
        ''' <summary>ﾍｯﾀﾞ</summary>
        '''========================================================================================
        Private _Fields As New Paramater
        Public Property Fields() As Paramater
            Get
                Return _Fields
            End Get

            Set(ByVal value As Paramater)
                _Fields = value
            End Set
        End Property
#End Region

#Region "ｺﾝｽﾄﾗｸﾀ"
        '''========================================================================================
        ''' <SUMMARY>ｺﾝｽﾄﾗｸﾀ</SUMMARY>
        ''' <PARAM name="Security">ｾｷｭﾘﾃｨ ｸﾗｽ</PARAM>
        ''' <PARAM name="Config">ｺﾝﾌｨｸﾞ ｸﾗｽ</PARAM>
        ''' <PARAM name="PagePath">ﾍﾟｰｼﾞﾊﾟｽ</PARAM>
        '''========================================================================================
        Public Sub New(ByVal Security As NodeCore.Common.Security, ByVal Config As NodeCore.Common.Config, ByVal PagePath As String)
            '----------------------------------------------------------------------
            ' ｾｷｭﾘﾃｨ 設定
            '----------------------------------------------------------------------
            MyBase.New(Security, Config, PagePath)

            Me.Fields.Header = New Header(Config)
        End Sub
#End Region

#Region "ﾍｯﾀﾞ"
#Region "Clear"
        '''========================================================================================
        ''' <SUMMARY>ｸﾘｱ</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Clear_Header(Optional ByVal KeyClear As Boolean = False) As Boolean
            '----------------------------------------------------------------------
            ' ｷｰ部ｸﾘｱ
            '----------------------------------------------------------------------
            If KeyClear Then
                Me.Fields.Header.グループCD.Value = Nothing
                Me.Fields.Header.個社CD.Value = Nothing
                Me.Fields.Header.子会社CD.Value = Nothing
            End If

            '----------------------------------------------------------------------
            ' ﾃﾞｰﾀ部ｸﾘｱ
            '----------------------------------------------------------------------
            Me.Fields.Header.子会社名.Value = Nothing
            Me.Fields.Header.子会社名フリガナ.Value = Nothing
            Me.Fields.Header.子会社住所.Value = Nothing
            Me.Fields.Header.子会社住所フリガナ.Value = Nothing
            Me.Fields.Header.事業者CD.Value = Nothing
            Me.Fields.Header.タクシーセンター.Value = Nothing
            Me.Fields.Header.業務開始日.Value = Nothing
            Me.Fields.Header.Tax.Value = Nothing
            Me.Fields.Header.登録ユーザーCD.Value = Nothing
            Me.Fields.Header.登録ユーザー名.Value = Nothing
            Me.Fields.Header.登録日時.Value = Nothing
            Me.Fields.Header.更新ユーザーCD.Value = Nothing
            Me.Fields.Header.更新ユーザー名.Value = Nothing
            Me.Fields.Header.更新日時.Value = Nothing
            '----------------------------------------------------------------------
            ' ﾃﾞｰﾀ部ｸﾘｱ
            '----------------------------------------------------------------------
            'Me.Read()

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "Read"
        '''========================================================================================
        ''' <SUMMARY>読込</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Read_Header() As Boolean
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetViewTableAdapters.V0子会社TableAdapter
            Dim tbl As New NodeDatabase.DataSetView.V0子会社DataTable
            Dim qry As New Collection

            '----------------------------------------------------------------------
            ' 条件設定
            '----------------------------------------------------------------------
            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, Me.Fields.Header.グループCD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, Me.Fields.Header.個社CD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.子会社CDColumn.ColumnName, Me.Fields.Header.子会社CD.Value, BaseDatabase.Contents.Compare.Equal))

            '----------------------------------------------------------------------
            ' 読込
            '----------------------------------------------------------------------
            tbl = ada.SelectByCommon(qry)
            If tbl.Count > 0 Then
                Me.Fields.Header.子会社名.Value = tbl(0).Item(tbl.子会社名Column.ColumnName)
                Me.Fields.Header.子会社名フリガナ.Value = tbl(0).Item(tbl.子会社名フリガナColumn.ColumnName)
                Me.Fields.Header.子会社住所.Value = tbl(0).Item(tbl.子会社住所Column.ColumnName)
                Me.Fields.Header.子会社住所フリガナ.Value = tbl(0).Item(tbl.子会社住所フリガナColumn.ColumnName)
                Me.Fields.Header.事業者CD.Value = tbl(0).Item(tbl.事業者CDColumn.ColumnName)
                Me.Fields.Header.タクシーセンター.Value = tbl(0).Item(tbl.タクシーセンターColumn.ColumnName)
                Me.Fields.Header.登録ユーザーCD.Value = tbl(0).Item(tbl.登録ユーザーCDColumn.ColumnName)
                Me.Fields.Header.登録ユーザー名.Value = tbl(0).Item(tbl.登録ユーザー名Column.ColumnName)
                Me.Fields.Header.登録日時.Value = tbl(0).Item(tbl.登録日時Column.ColumnName)
                Me.Fields.Header.更新ユーザーCD.Value = tbl(0).Item(tbl.更新ユーザーCDColumn.ColumnName)
                Me.Fields.Header.更新ユーザー名.Value = tbl(0).Item(tbl.更新ユーザー名Column.ColumnName)
                Me.Fields.Header.更新日時.Value = tbl(0).Item(tbl.更新日時Column.ColumnName)
                Me.Fields.Header.業務開始日.Value = tbl(0).Item(tbl.業務開始日Column.ColumnName)
                Me.Fields.Header.Tax.Value = tbl(0).Item(tbl.TaxColumn.ColumnName)
            Else
                Me.Clear(False)
            End If

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "Write"
        '''========================================================================================
        ''' <SUMMARY>書込</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Write_Header() As Boolean
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim strNow As String = Format(Now, NodeContents.Constant.System.DATE_TIME_FORMAT)

            Dim ada As New NodeDatabase.DataSetMasterTableAdapters.M子会社TableAdapter
            Dim tbl As New NodeDatabase.DataSetMaster.M子会社DataTable
            Dim qry As New Collection

            '----------------------------------------------------------------------
            ' 条件設定
            '----------------------------------------------------------------------
            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, Me.Fields.Header.グループCD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, Me.Fields.Header.個社CD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.子会社CDColumn.ColumnName, Me.Fields.Header.子会社CD.Value, BaseDatabase.Contents.Compare.Equal))

            '----------------------------------------------------------------------
            ' 読込
            '----------------------------------------------------------------------
            tbl = ada.SelectByCommon(qry)


            '----------------------------------------------------------------------
            ' 事前準備
            '----------------------------------------------------------------------
            Me.Fields.Header.更新日時.Value = strNow
            Me.Fields.Header.更新ユーザーCD.Value = Me.Security.ユーザーCD
            Me.Fields.Header.更新ユーザー名.Value = Me.Security.ユーザー名

            Using scope As New System.Transactions.TransactionScope(System.Transactions.TransactionScopeOption.Required, TimeSpan.Zero)
                '----------------------------------------------------------------------
                ' 保存
                '----------------------------------------------------------------------
                If tbl.Count = 0 Then
                    '----------------------------------------------------------------------
                    ' 新規
                    '----------------------------------------------------------------------
                    Me.Fields.Header.登録日時.Value = strNow
                    Me.Fields.Header.登録ユーザーCD.Value = Me.Security.ユーザーCD
                    Me.Fields.Header.登録ユーザー名.Value = Me.Security.ユーザー名
                    Try
                        ada.Insert(Me.Fields.Header.グループCD.Value _
                             , Me.Fields.Header.個社CD.Value _
                             , Me.Fields.Header.子会社CD.Value _
                             , Me.Fields.Header.子会社名.Value _
                             , Me.Fields.Header.子会社名フリガナ.Value _
                             , Me.Fields.Header.事業者CD.Value _
                             , Me.Fields.Header.子会社住所.Value _
                             , Me.Fields.Header.子会社住所フリガナ.Value _
                             , Me.Fields.Header.タクシーセンター.Value _
                             , Me.Fields.Header.業務開始日.Value _
                             , Me.Fields.Header.Tax.Value _
                             , Me.Fields.Header.登録ユーザーCD.Value _
                             , Me.Fields.Header.登録ユーザー名.Value _
                             , Me.Fields.Header.登録日時.Value _
                             , Me.Fields.Header.更新ユーザーCD.Value _
                             , Me.Fields.Header.更新ユーザー名.Value _
                             , Me.Fields.Header.更新日時.Value
                             )

                    Catch ex As Exception
                        MyBase.LastError = "以下のエラーのため、追加できませんでした。" & vbCrLf & ex.Message
                        Return False
                    End Try
                Else
                    '----------------------------------------------------------------------
                    ' 訂正
                    '----------------------------------------------------------------------
                    Try
                        tbl(0).Item(tbl.グループCDColumn.ColumnName) = Me.Fields.Header.グループCD.Value
                        tbl(0).Item(tbl.個社CDColumn.ColumnName) = Me.Fields.Header.個社CD.Value
                        tbl(0).Item(tbl.子会社CDColumn.ColumnName) = Me.Fields.Header.子会社CD.Value
                        tbl(0).Item(tbl.子会社名Column.ColumnName) = Me.Fields.Header.子会社名.Value
                        tbl(0).Item(tbl.子会社名フリガナColumn.ColumnName) = Me.Fields.Header.子会社名フリガナ.Value
                        tbl(0).Item(tbl.子会社住所Column.ColumnName) = Me.Fields.Header.子会社住所.Value
                        tbl(0).Item(tbl.子会社住所フリガナColumn.ColumnName) = Me.Fields.Header.子会社住所フリガナ.Value
                        tbl(0).Item(tbl.事業者CDColumn.ColumnName) = Me.Fields.Header.事業者CD.Value
                        tbl(0).Item(tbl.タクシーセンターColumn.ColumnName) = Me.Fields.Header.タクシーセンター.Value
                        tbl(0).Item(tbl.登録ユーザーCDColumn.ColumnName) = Me.Fields.Header.登録ユーザーCD.Value
                        tbl(0).Item(tbl.登録ユーザー名Column.ColumnName) = Me.Fields.Header.登録ユーザー名.Value
                        tbl(0).Item(tbl.登録日時Column.ColumnName) = Me.Fields.Header.登録日時.Value
                        tbl(0).Item(tbl.更新ユーザーCDColumn.ColumnName) = Me.Fields.Header.更新ユーザーCD.Value
                        tbl(0).Item(tbl.更新ユーザー名Column.ColumnName) = Me.Fields.Header.更新ユーザー名.Value
                        tbl(0).Item(tbl.更新日時Column.ColumnName) = Me.Fields.Header.更新日時.Value
                        tbl(0).Item(tbl.業務開始日Column.ColumnName) = Me.Fields.Header.業務開始日.Value
                        tbl(0).Item(tbl.TaxColumn.ColumnName) = Me.Fields.Header.Tax.Value
                        ada.Update(tbl)

                    Catch ex As Exception
                        MyBase.LastError = "以下のエラーのため、更新できませんでした。" & vbCrLf & ex.Message
                        Return False
                    End Try
                End If
                scope.Complete()
            End Using

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "Delete"
        '''========================================================================================
        ''' <SUMMARY>削除</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Delete_Header() As Boolean
            '----------------------------------------------------------------------
            ' ﾍｯﾀﾞ
            '----------------------------------------------------------------------
            '------------------------------------
            ' 変数定義
            '------------------------------------
            Dim ada As New NodeDatabase.DataSetMasterTableAdapters.M子会社TableAdapter
            Dim tbl As New NodeDatabase.DataSetMaster.M子会社DataTable
            Dim qry As New Collection

            Dim adaHD As New NodeDatabase.DataSetTranTableAdapters.T乗務記録HDTableAdapter
            Dim tblHD As New NodeDatabase.DataSetTran.T乗務記録HDDataTable
            Dim qryHD As New Collection

            '----------------------------------------------------------------------
            ' 条件設定
            '----------------------------------------------------------------------
            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, Me.Fields.Header.グループCD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, Me.Fields.Header.個社CD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.子会社CDColumn.ColumnName, Me.Fields.Header.子会社CD.Value, BaseDatabase.Contents.Compare.Equal))

            qryHD.Clear()
            qryHD.Add(New BaseDatabase.Condition(tblHD.グループCDColumn.ColumnName, Me.Fields.Header.グループCD.Value, BaseDatabase.Contents.Compare.Equal))
            qryHD.Add(New BaseDatabase.Condition(tblHD.個社CDColumn.ColumnName, Me.Fields.Header.個社CD.Value, BaseDatabase.Contents.Compare.Equal))
            qryHD.Add(New BaseDatabase.Condition(tblHD.子会社CDColumn.ColumnName, Me.Fields.Header.子会社CD.Value, BaseDatabase.Contents.Compare.Equal))


            '------------------------------------
            ' 削除
            '------------------------------------
            Try
                adaHD.DeleteByCommon(qryHD)

                ada.DeleteByCommon(qry)

            Catch ex As Exception
                MyBase.LastError = "以下のエラーのため、削除できませんでした。" & vbCrLf & ex.Message
                Return False
            End Try

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "Valid_Write"
        '''========================================================================================
        ''' <SUMMARY>検査(共通)</SUMMARY>
        ''' <RETURNS>True:正常, False:不正</RETURNS>
        '''========================================================================================
        Protected Overrides Function Valid_Write_Header_Common() As NodeContents.ActionButton.ErrorLevel
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim strMsg As String = ""
            Dim Validator As New BaseCore.Common.Validator

            '------------------------------------------------------------------
            ' ｴﾗｰｸﾘｱ
            '------------------------------------------------------------------
            Me.Fields.Header.グループCD.IsError = False
            Me.Fields.Header.個社CD.IsError = False
            Me.Fields.Header.子会社CD.IsError = False
            Me.Fields.Header.子会社名.IsError = False
            Me.Fields.Header.子会社名フリガナ.IsError = False
            Me.Fields.Header.子会社住所.IsError = False
            Me.Fields.Header.子会社住所フリガナ.IsError = False
            Me.Fields.Header.事業者CD.IsError = False
            Me.Fields.Header.タクシーセンター.IsError = False
            Me.Fields.Header.登録ユーザーCD.IsError = False
            Me.Fields.Header.登録ユーザー名.IsError = False
            Me.Fields.Header.登録日時.IsError = False
            Me.Fields.Header.更新ユーザーCD.IsError = False
            Me.Fields.Header.更新ユーザー名.IsError = False
            Me.Fields.Header.更新日時.IsError = False
            Me.Fields.Header.業務開始日.IsError = False
            Me.Fields.Header.Tax.IsError = False

            '------------------------------------------------------------------
            ' 共通検査
            '------------------------------------------------------------------
            If Not Validator.BaseChecker(Me.Fields.Header.グループCD) Then : Me.Fields.Header.グループCD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.個社CD) Then : Me.Fields.Header.個社CD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.子会社CD) Then : Me.Fields.Header.子会社CD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.子会社名) Then : Me.Fields.Header.子会社名.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.子会社名フリガナ) Then : Me.Fields.Header.子会社名フリガナ.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.子会社住所) Then : Me.Fields.Header.子会社住所.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.子会社住所フリガナ) Then : Me.Fields.Header.子会社住所フリガナ.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.事業者CD) Then : Me.Fields.Header.事業者CD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.タクシーセンター) Then : Me.Fields.Header.タクシーセンター.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.登録ユーザーCD) Then : Me.Fields.Header.登録ユーザーCD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.登録ユーザー名) Then : Me.Fields.Header.登録ユーザー名.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.登録日時) Then : Me.Fields.Header.登録日時.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.更新ユーザーCD) Then : Me.Fields.Header.更新ユーザーCD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.更新ユーザー名) Then : Me.Fields.Header.更新ユーザー名.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.更新日時) Then : Me.Fields.Header.更新日時.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.業務開始日) Then : Me.Fields.Header.業務開始日.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.Tax) Then : Me.Fields.Header.Tax.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If

            'Exists check
            Dim ada As New NodeDatabase.DataSetViewTableAdapters.V0グループTableAdapter
            Dim tbl As New NodeDatabase.DataSetView.V0グループDataTable
            Dim qry As New Collection
            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, Me.Fields.Header.グループCD.Value, BaseDatabase.Contents.Compare.Equal))
            tbl = ada.SelectByCommon(qry)
            If tbl.Count = 0 Then
                Me.Fields.Header.グループCD.IsError = True
                Validator.SetMessage(strMsg, "入力したグループが存在しません！")
            End If

            Dim ada1 As New NodeDatabase.DataSetViewTableAdapters.V0個社TableAdapter
            Dim tbl1 As New NodeDatabase.DataSetView.V0個社DataTable
            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl1.グループCDColumn.ColumnName, Me.Fields.Header.グループCD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl1.個社CDColumn.ColumnName, Me.Fields.Header.個社CD.Value, BaseDatabase.Contents.Compare.Equal))
            tbl1 = ada1.SelectByCommon(qry)
            If tbl1.Count = 0 Then
                Me.Fields.Header.個社CD.IsError = True
                Validator.SetMessage(strMsg, "入力した個社が存在しません！")
            End If

            '------------------------------------------------------------------
            ' 返却
            '------------------------------------------------------------------
            MyBase.LastError = strMsg
            Select Case True
                Case strMsg = ""
                    Return NodeContents.ActionButton.ErrorLevel.None
                Case Else
                    Return NodeContents.ActionButton.ErrorLevel.Fatal
            End Select
        End Function
#End Region

#Region "Valid_Delete"
        '''========================================================================================
        ''' <SUMMARY>検査(共通)</SUMMARY>
        ''' <RETURNS>True:正常, False:不正</RETURNS>
        '''========================================================================================
        Protected Overrides Function Valid_Delete_Header_Common() As NodeContents.ActionButton.ErrorLevel
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim strMsg As String = ""
            Dim Validator As New BaseCore.Common.Validator

            '------------------------------------------------------------------
            ' ｴﾗｰｸﾘｱ
            '------------------------------------------------------------------
            Me.Fields.Header.グループCD.IsError = False
            Me.Fields.Header.個社CD.IsError = False
            Me.Fields.Header.子会社CD.IsError = False
            Me.Fields.Header.子会社名.IsError = False
            Me.Fields.Header.子会社名フリガナ.IsError = False
            Me.Fields.Header.子会社住所.IsError = False
            Me.Fields.Header.子会社住所フリガナ.IsError = False
            Me.Fields.Header.事業者CD.IsError = False
            Me.Fields.Header.タクシーセンター.IsError = False
            Me.Fields.Header.業務開始日.IsError = False
            Me.Fields.Header.Tax.IsError = False
            '------------------------------------------------------------------
            ' 共通検査
            '------------------------------------------------------------------
            If Not Validator.BaseChecker(Me.Fields.Header.グループCD) Then : Me.Fields.Header.グループCD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.個社CD) Then : Me.Fields.Header.個社CD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.子会社CD) Then : Me.Fields.Header.子会社CD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.子会社名) Then : Me.Fields.Header.子会社名.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.子会社名フリガナ) Then : Me.Fields.Header.子会社名フリガナ.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.子会社住所) Then : Me.Fields.Header.子会社住所.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.子会社住所フリガナ) Then : Me.Fields.Header.子会社住所フリガナ.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.事業者CD) Then : Me.Fields.Header.事業者CD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.タクシーセンター) Then : Me.Fields.Header.タクシーセンター.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.業務開始日) Then : Me.Fields.Header.業務開始日.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.Tax) Then : Me.Fields.Header.Tax.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            '------------------------------------------------------------------
            ' 返却
            '------------------------------------------------------------------
            MyBase.LastError = strMsg
            Select Case True
                Case strMsg = ""
                    Return NodeContents.ActionButton.ErrorLevel.None
                Case Else
                    Return NodeContents.ActionButton.ErrorLevel.Fatal
            End Select
        End Function
#End Region

#End Region
    End Class
End Namespace
