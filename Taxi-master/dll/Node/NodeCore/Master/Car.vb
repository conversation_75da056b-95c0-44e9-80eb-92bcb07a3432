﻿Namespace Frame.Master
    Partial Public Class Car
        Inherits NodeCore.Common.Frame

#Region "ｺﾝｽﾀﾝﾄ"
        Public Class ConstantExcel
            Public Class Header
                Public Const SheetName = "車両一覧"
                Public Const RowTop = 1  '一行目スタート

                Public Class 列数
                    Public Const グループCD = 0
                    Public Const グループ名 = 1
                    Public Const 個社CD = 2
                    Public Const 個社名 = 3
                    Public Const 子会社CD = 4
                    Public Const 子会社名 = 5
                    Public Const 車両番号 = 6
                    Public Const ドアNO = 7
                    Public Const 車名 = 8
                    Public Const 型式 = 9
                    Public Const 車台番号 = 10
                    Public Const 車種CD = 11
                    Public Const 登録番号 = 12
                    Public Const 初年度登録年月 = 13
                    Public Const 車検満了日付 = 14
                    Public Const 明細番号 = 15
                    Public Const 免責 = 16
                    Public Const 対物 = 17
                    Public Const ドラレコ = 18
                    Public Const 日報デジタコ = 19
                    Public Const 決済端末 = 20
                    Public Const メーター機 = 21
                    Public Const 無線機 = 22
                    Public Const プリンター = 23
                    Public Const 稼働状況 = 24
                    Public Const 担当乗務員01 = 25
                    Public Const 担当乗務員02 = 26
                    Public Const 担当乗務員03 = 27
                    Public Const コメント = 28
                    Public Const 登録日時 = 29
                    Public Const 登録ユーザーCD = 30
                    Public Const 登録ユーザー名 = 31
                    Public Const 更新日時 = 32
                    Public Const 更新ユーザーCD = 33
                    Public Const 更新ユーザー名 = 34
                End Class

            End Class

        End Class


#End Region

#Region "ﾌｨｰﾙﾄﾞ定義"
        Public Class Header
            ''' ----------------------------------------------------------------------------------------
            ''' <SUMMARY>列項目(基本)</SUMMARY>
            ''' ----------------------------------------------------------------------------------------
            Public グループCD As BaseCore.Common.Field.ItemData
            Public 個社CD As BaseCore.Common.Field.ItemData
            Public 子会社CD As BaseCore.Common.Field.ItemData
            Public 車両番号 As BaseCore.Common.Field.ItemData
            Public ドアNO As BaseCore.Common.Field.ItemData
            Public 車名 As BaseCore.Common.Field.ItemData
            Public 型式 As BaseCore.Common.Field.ItemData
            Public 車台番号 As BaseCore.Common.Field.ItemData
            Public 車種CD As BaseCore.Common.Field.ItemData
            Public 登録番号 As BaseCore.Common.Field.ItemData
            Public 初年度登録年月 As BaseCore.Common.Field.ItemData
            Public 車検満了日付 As BaseCore.Common.Field.ItemData
            Public 明細番号 As BaseCore.Common.Field.ItemData
            Public 免責 As BaseCore.Common.Field.ItemData
            Public 対物 As BaseCore.Common.Field.ItemData
            Public ドラレコ As BaseCore.Common.Field.ItemData
            Public 日報デジタコ As BaseCore.Common.Field.ItemData
            Public 決済端末 As BaseCore.Common.Field.ItemData
            Public メーター機 As BaseCore.Common.Field.ItemData
            Public 無線機 As BaseCore.Common.Field.ItemData
            Public プリンター As BaseCore.Common.Field.ItemData
            Public 稼働状況 As BaseCore.Common.Field.ItemData
            Public 担当乗務員01 As BaseCore.Common.Field.ItemData
            Public 担当乗務員02 As BaseCore.Common.Field.ItemData
            Public 担当乗務員03 As BaseCore.Common.Field.ItemData
            Public コメント As BaseCore.Common.Field.ItemData
            Public 登録日時 As BaseCore.Common.Field.ItemData
            Public 登録ユーザーCD As BaseCore.Common.Field.ItemData
            Public 登録ユーザー名 As BaseCore.Common.Field.ItemData
            Public 更新日時 As BaseCore.Common.Field.ItemData
            Public 更新ユーザーCD As BaseCore.Common.Field.ItemData
            Public 更新ユーザー名 As BaseCore.Common.Field.ItemData

            '''========================================================================================
            ''' <SUMMARY>ｺﾝｽﾄﾗｸﾀ</SUMMARY>
            '''========================================================================================
            Public Sub New(ByVal Config As NodeCore.Common.Config)
                Dim tbl As New NodeDatabase.DataSetView.V0車両DataTable

                '----------------------------------------------------------------------
                ' ｲﾝｽﾀﾝｽ(基本) 
                '----------------------------------------------------------------------

                Me.グループCD = New BaseCore.Common.Field.ItemData(tbl.グループCDColumn)
                Me.個社CD = New BaseCore.Common.Field.ItemData(tbl.個社CDColumn)
                Me.子会社CD = New BaseCore.Common.Field.ItemData(tbl.子会社CDColumn)
                Me.車両番号 = New BaseCore.Common.Field.ItemData(tbl.車両番号Column)
                Me.ドアNO = New BaseCore.Common.Field.ItemData(tbl.ドアNOColumn)
                Me.車名 = New BaseCore.Common.Field.ItemData(tbl.車名Column)
                Me.型式 = New BaseCore.Common.Field.ItemData(tbl.型式Column)
                Me.車台番号 = New BaseCore.Common.Field.ItemData(tbl.車台番号Column)
                Me.車種CD = New BaseCore.Common.Field.ItemData(tbl.車種CDColumn)
                Me.登録番号 = New BaseCore.Common.Field.ItemData(tbl.登録番号Column)
                Me.初年度登録年月 = New BaseCore.Common.Field.ItemData(tbl.初年度登録年月Column)
                Me.車検満了日付 = New BaseCore.Common.Field.ItemData(tbl.車検満了日付Column)
                Me.明細番号 = New BaseCore.Common.Field.ItemData(tbl.明細番号Column)
                Me.免責 = New BaseCore.Common.Field.ItemData(tbl.免責Column)
                Me.対物 = New BaseCore.Common.Field.ItemData(tbl.対物Column)
                Me.ドラレコ = New BaseCore.Common.Field.ItemData(tbl.ドラレコColumn)
                Me.日報デジタコ = New BaseCore.Common.Field.ItemData(tbl.日報デジタコColumn)
                Me.決済端末 = New BaseCore.Common.Field.ItemData(tbl.決済端末Column)
                Me.メーター機 = New BaseCore.Common.Field.ItemData(tbl.メーター機Column)
                Me.無線機 = New BaseCore.Common.Field.ItemData(tbl.無線機Column)
                Me.プリンター = New BaseCore.Common.Field.ItemData(tbl.プリンターColumn)
                Me.稼働状況 = New BaseCore.Common.Field.ItemData(tbl.稼働状況Column)
                Me.担当乗務員01 = New BaseCore.Common.Field.ItemData(tbl.担当乗務員01Column)
                Me.担当乗務員02 = New BaseCore.Common.Field.ItemData(tbl.担当乗務員02Column)
                Me.担当乗務員03 = New BaseCore.Common.Field.ItemData(tbl.担当乗務員03Column)
                Me.コメント = New BaseCore.Common.Field.ItemData(tbl.コメントColumn)
                Me.登録日時 = New BaseCore.Common.Field.ItemData(tbl.登録日時Column)
                Me.登録ユーザーCD = New BaseCore.Common.Field.ItemData(tbl.登録ユーザーCDColumn)
                Me.登録ユーザー名 = New BaseCore.Common.Field.ItemData(tbl.登録ユーザー名Column)
                Me.更新日時 = New BaseCore.Common.Field.ItemData(tbl.更新日時Column)
                Me.更新ユーザーCD = New BaseCore.Common.Field.ItemData(tbl.更新ユーザーCDColumn)
                Me.更新ユーザー名 = New BaseCore.Common.Field.ItemData(tbl.更新ユーザー名Column)

            End Sub
        End Class
#End Region

#Region "変数定義"
        Public Class Paramater
            Public Header As Header
        End Class

        '''========================================================================================
        ''' <summary>ﾍｯﾀﾞ</summary>
        '''========================================================================================
        Private _Fields As New Paramater
        Public Property Fields() As Paramater
            Get
                Return _Fields
            End Get

            Set(ByVal value As Paramater)
                _Fields = value
            End Set
        End Property
#End Region

#Region "ｺﾝｽﾄﾗｸﾀ"
        '''========================================================================================
        ''' <SUMMARY>ｺﾝｽﾄﾗｸﾀ</SUMMARY>
        ''' <PARAM name="Security">ｾｷｭﾘﾃｨ ｸﾗｽ</PARAM>
        ''' <PARAM name="Config">ｺﾝﾌｨｸﾞ ｸﾗｽ</PARAM>
        ''' <PARAM name="PagePath">ﾍﾟｰｼﾞﾊﾟｽ</PARAM>
        '''========================================================================================
        Public Sub New(ByVal Security As NodeCore.Common.Security, ByVal Config As NodeCore.Common.Config, ByVal PagePath As String)
            '----------------------------------------------------------------------
            ' ｾｷｭﾘﾃｨ 設定
            '----------------------------------------------------------------------
            MyBase.New(Security, Config, PagePath)

            Me.Fields.Header = New Header(Config)
        End Sub
#End Region

#Region "ﾍｯﾀﾞ"
#Region "Clear"
        '''========================================================================================
        ''' <SUMMARY>ｸﾘｱ</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Clear_Header(Optional ByVal KeyClear As Boolean = False) As Boolean
            '----------------------------------------------------------------------
            ' ｷｰ部ｸﾘｱ
            '----------------------------------------------------------------------
            If KeyClear Then
                Me.Fields.Header.グループCD.Value = Nothing
                Me.Fields.Header.個社CD.Value = Nothing
                Me.Fields.Header.子会社CD.Value = Nothing
                Me.Fields.Header.車両番号.Value = Nothing
            End If

            '----------------------------------------------------------------------
            ' ﾃﾞｰﾀ部ｸﾘｱ
            '----------------------------------------------------------------------
            Me.Fields.Header.ドアNO.Value = Nothing
            Me.Fields.Header.車名.Value = Nothing
            Me.Fields.Header.型式.Value = Nothing
            Me.Fields.Header.車台番号.Value = Nothing
            Me.Fields.Header.車種CD.Value = Nothing
            Me.Fields.Header.登録番号.Value = Nothing
            Me.Fields.Header.初年度登録年月.Value = Nothing
            Me.Fields.Header.車検満了日付.Value = Nothing
            Me.Fields.Header.明細番号.Value = Nothing
            Me.Fields.Header.免責.Value = Nothing
            Me.Fields.Header.対物.Value = Nothing
            Me.Fields.Header.ドラレコ.Value = Nothing
            Me.Fields.Header.日報デジタコ.Value = Nothing
            Me.Fields.Header.決済端末.Value = Nothing
            Me.Fields.Header.メーター機.Value = Nothing
            Me.Fields.Header.無線機.Value = Nothing
            Me.Fields.Header.プリンター.Value = Nothing
            Me.Fields.Header.稼働状況.Value = Nothing
            Me.Fields.Header.担当乗務員01.Value = Nothing
            Me.Fields.Header.担当乗務員02.Value = Nothing
            Me.Fields.Header.担当乗務員03.Value = Nothing
            Me.Fields.Header.コメント.Value = Nothing
            Me.Fields.Header.登録日時.Value = Nothing
            Me.Fields.Header.更新日時.Value = Nothing

            '----------------------------------------------------------------------
            ' ﾃﾞｰﾀ部ｸﾘｱ
            '----------------------------------------------------------------------
            'Me.Read()

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "Read"
        '''========================================================================================
        ''' <SUMMARY>読込</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Read_Header() As Boolean
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetViewTableAdapters.V0車両TableAdapter
            Dim tbl As New NodeDatabase.DataSetView.V0車両DataTable

            Dim qry As New Collection

            '----------------------------------------------------------------------
            ' 条件設定
            '----------------------------------------------------------------------
            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, Me.Fields.Header.グループCD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, Me.Fields.Header.個社CD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.子会社CDColumn.ColumnName, Me.Fields.Header.子会社CD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.車両番号Column.ColumnName, Me.Fields.Header.車両番号.Value, BaseDatabase.Contents.Compare.Equal))

            '----------------------------------------------------------------------
            ' 読込
            '----------------------------------------------------------------------
            tbl = ada.SelectByCommon(qry)
            If tbl.Count > 0 Then
                'Me.Fields.Header.会社CD.Value = tbl(0).Item(tbl.子会社CDColumn.ColumnName)
                'Me.Fields.Header.車両番号.Value = tbl(0).Item(tbl.車両番号Column.ColumnName)
                Me.Fields.Header.ドアNO.Value = tbl(0).Item(tbl.ドアNOColumn.ColumnName)
                Me.Fields.Header.車名.Value = tbl(0).Item(tbl.車名Column.ColumnName)
                Me.Fields.Header.型式.Value = tbl(0).Item(tbl.型式Column.ColumnName)
                Me.Fields.Header.車台番号.Value = tbl(0).Item(tbl.車台番号Column.ColumnName)
                Me.Fields.Header.車種CD.Value = tbl(0).Item(tbl.車種CDColumn.ColumnName)
                Me.Fields.Header.登録番号.Value = tbl(0).Item(tbl.登録番号Column.ColumnName)
                Me.Fields.Header.初年度登録年月.Value = tbl(0).Item(tbl.初年度登録年月Column.ColumnName)
                Me.Fields.Header.車検満了日付.Value = tbl(0).Item(tbl.車検満了日付Column.ColumnName)
                Me.Fields.Header.明細番号.Value = tbl(0).Item(tbl.明細番号Column.ColumnName)
                Me.Fields.Header.免責.Value = tbl(0).Item(tbl.免責Column.ColumnName)
                Me.Fields.Header.対物.Value = tbl(0).Item(tbl.対物Column.ColumnName)
                Me.Fields.Header.ドラレコ.Value = tbl(0).Item(tbl.ドラレコColumn.ColumnName)
                Me.Fields.Header.日報デジタコ.Value = tbl(0).Item(tbl.日報デジタコColumn.ColumnName)
                Me.Fields.Header.決済端末.Value = tbl(0).Item(tbl.決済端末Column.ColumnName)
                Me.Fields.Header.メーター機.Value = tbl(0).Item(tbl.メーター機Column.ColumnName)
                Me.Fields.Header.無線機.Value = tbl(0).Item(tbl.無線機Column.ColumnName)
                Me.Fields.Header.プリンター.Value = tbl(0).Item(tbl.プリンターColumn.ColumnName)
                Me.Fields.Header.稼働状況.Value = tbl(0).Item(tbl.稼働状況Column.ColumnName)
                Me.Fields.Header.担当乗務員01.Value = tbl(0).Item(tbl.担当乗務員01Column.ColumnName)
                Me.Fields.Header.担当乗務員02.Value = tbl(0).Item(tbl.担当乗務員02Column.ColumnName)
                Me.Fields.Header.担当乗務員03.Value = tbl(0).Item(tbl.担当乗務員03Column.ColumnName)
                Me.Fields.Header.コメント.Value = tbl(0).Item(tbl.コメントColumn.ColumnName)
                Me.Fields.Header.登録日時.Value = tbl(0).Item(tbl.登録日時Column.ColumnName)
                Me.Fields.Header.更新日時.Value = tbl(0).Item(tbl.更新日時Column.ColumnName)
            Else
                Clear(False)
            End If

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "Write"
        '''========================================================================================
        ''' <SUMMARY>書込</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Write_Header() As Boolean
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim strNow As String = Format(Now, NodeContents.Constant.System.DATE_TIME_FORMAT)

            Dim ada As New NodeDatabase.DataSetMasterTableAdapters.M車両TableAdapter
            Dim tbl As New NodeDatabase.DataSetMaster.M車両DataTable
            Dim qry As New Collection

            '----------------------------------------------------------------------
            ' 条件設定
            '----------------------------------------------------------------------
            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, Me.Fields.Header.グループCD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, Me.Fields.Header.個社CD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.子会社CDColumn.ColumnName, Me.Fields.Header.子会社CD.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.車両番号Column.ColumnName, Me.Fields.Header.車両番号.Value, BaseDatabase.Contents.Compare.Equal))


            '----------------------------------------------------------------------
            ' 読込
            '----------------------------------------------------------------------
            tbl = ada.SelectByCommon(qry)


            '----------------------------------------------------------------------
            ' 事前準備
            '----------------------------------------------------------------------
            Me.Fields.Header.更新日時.Value = strNow
            Me.Fields.Header.更新ユーザーCD.Value = Me.Security.ユーザーCD
            Me.Fields.Header.更新ユーザー名.Value = Me.Security.ユーザー名


            '----------------------------------------------------------------------
            ' 保存
            '----------------------------------------------------------------------
            If tbl.Count = 0 Then
                '----------------------------------------------------------------------
                ' 新規
                '----------------------------------------------------------------------
                Me.Fields.Header.登録日時.Value = strNow
                Me.Fields.Header.登録ユーザーCD.Value = Me.Security.ユーザーCD
                Me.Fields.Header.登録ユーザー名.Value = Me.Security.ユーザー名
                Try
                    ada.Insert(Me.Fields.Header.グループCD.Value _
                             , Me.Fields.Header.個社CD.Value _
                             , Me.Fields.Header.子会社CD.Value _
                             , Me.Fields.Header.車両番号.Value _
                             , Me.Fields.Header.ドアNO.Value _
                             , Me.Fields.Header.車名.Value _
                             , Me.Fields.Header.型式.Value _
                             , Me.Fields.Header.車台番号.Value _
                             , Me.Fields.Header.車種CD.Value _
                             , Me.Fields.Header.登録番号.Value _
                             , Me.Fields.Header.初年度登録年月.Value _
                             , Me.Fields.Header.車検満了日付.Value _
                             , Me.Fields.Header.明細番号.Value _
                             , Me.Fields.Header.免責.Value _
                             , Me.Fields.Header.対物.Value _
                             , Me.Fields.Header.ドラレコ.Value _
                             , Me.Fields.Header.日報デジタコ.Value _
                             , Me.Fields.Header.決済端末.Value _
                             , Me.Fields.Header.メーター機.Value _
                             , Me.Fields.Header.無線機.Value _
                             , Me.Fields.Header.プリンター.Value _
                             , Me.Fields.Header.稼働状況.Value _
                             , Me.Fields.Header.担当乗務員01.Value _
                             , Me.Fields.Header.担当乗務員02.Value _
                             , Me.Fields.Header.担当乗務員03.Value _
                             , Me.Fields.Header.コメント.Value _
                             , Me.Fields.Header.登録日時.Value _
                             , Me.Fields.Header.登録ユーザーCD.Value _
                             , Me.Fields.Header.登録ユーザー名.Value _
                             , Me.Fields.Header.更新日時.Value _
                             , Me.Fields.Header.更新ユーザーCD.Value _
                             , Me.Fields.Header.更新ユーザー名.Value
                            )

                Catch ex As Exception
                    MyBase.LastError = "以下のエラーのため、追加できませんでした。" & vbCrLf & ex.Message
                    Return False
                End Try
            Else
                '----------------------------------------------------------------------
                ' 訂正
                '----------------------------------------------------------------------
                Try
                    tbl(0).Item(tbl.グループCDColumn.ColumnName) = Me.Fields.Header.グループCD.Value
                    tbl(0).Item(tbl.個社CDColumn.ColumnName) = Me.Fields.Header.個社CD.Value
                    tbl(0).Item(tbl.子会社CDColumn.ColumnName) = Me.Fields.Header.子会社CD.Value
                    tbl(0).Item(tbl.車両番号Column.ColumnName) = Me.Fields.Header.車両番号.Value
                    tbl(0).Item(tbl.ドアNOColumn.ColumnName) = Me.Fields.Header.ドアNO.Value
                    tbl(0).Item(tbl.車名Column.ColumnName) = Me.Fields.Header.車名.Value
                    tbl(0).Item(tbl.型式Column.ColumnName) = Me.Fields.Header.型式.Value
                    tbl(0).Item(tbl.車台番号Column.ColumnName) = Me.Fields.Header.車台番号.Value
                    tbl(0).Item(tbl.車種CDColumn.ColumnName) = Me.Fields.Header.車種CD.Value
                    tbl(0).Item(tbl.登録番号Column.ColumnName) = Me.Fields.Header.登録番号.Value
                    tbl(0).Item(tbl.初年度登録年月Column.ColumnName) = Me.Fields.Header.初年度登録年月.Value
                    tbl(0).Item(tbl.車検満了日付Column.ColumnName) = Me.Fields.Header.車検満了日付.Value
                    tbl(0).Item(tbl.明細番号Column.ColumnName) = Me.Fields.Header.明細番号.Value
                    tbl(0).Item(tbl.免責Column.ColumnName) = Me.Fields.Header.免責.Value
                    tbl(0).Item(tbl.対物Column.ColumnName) = Me.Fields.Header.対物.Value
                    tbl(0).Item(tbl.ドラレコColumn.ColumnName) = Me.Fields.Header.ドラレコ.Value
                    tbl(0).Item(tbl.日報デジタコColumn.ColumnName) = Me.Fields.Header.日報デジタコ.Value
                    tbl(0).Item(tbl.決済端末Column.ColumnName) = Me.Fields.Header.決済端末.Value
                    tbl(0).Item(tbl.メーター機Column.ColumnName) = Me.Fields.Header.メーター機.Value
                    tbl(0).Item(tbl.無線機Column.ColumnName) = Me.Fields.Header.無線機.Value
                    tbl(0).Item(tbl.プリンターColumn.ColumnName) = Me.Fields.Header.プリンター.Value
                    tbl(0).Item(tbl.稼働状況Column.ColumnName) = Me.Fields.Header.稼働状況.Value
                    tbl(0).Item(tbl.担当乗務員01Column.ColumnName) = Me.Fields.Header.担当乗務員01.Value
                    tbl(0).Item(tbl.担当乗務員02Column.ColumnName) = Me.Fields.Header.担当乗務員02.Value
                    tbl(0).Item(tbl.担当乗務員03Column.ColumnName) = Me.Fields.Header.担当乗務員03.Value
                    tbl(0).Item(tbl.コメントColumn.ColumnName) = Me.Fields.Header.コメント.Value
                    tbl(0).Item(tbl.登録日時Column.ColumnName) = Me.Fields.Header.登録日時.Value
                    tbl(0).Item(tbl.登録ユーザーCDColumn.ColumnName) = Me.Fields.Header.登録ユーザーCD.Value
                    tbl(0).Item(tbl.登録ユーザー名Column.ColumnName) = Me.Fields.Header.登録ユーザー名.Value
                    tbl(0).Item(tbl.更新日時Column.ColumnName) = Me.Fields.Header.更新日時.Value
                    tbl(0).Item(tbl.更新ユーザーCDColumn.ColumnName) = Me.Fields.Header.更新ユーザーCD.Value
                    tbl(0).Item(tbl.更新ユーザー名Column.ColumnName) = Me.Fields.Header.更新ユーザー名.Value
                    ada.Update(tbl)

                Catch ex As Exception
                    MyBase.LastError = "以下のエラーのため、更新できませんでした。" & vbCrLf & ex.Message
                    Return False
                End Try
            End If

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "Delete"
        '''========================================================================================
        ''' <SUMMARY>削除</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Delete_Header() As Boolean
            '----------------------------------------------------------------------
            ' ﾍｯﾀﾞ
            '----------------------------------------------------------------------
            '------------------------------------
            ' 変数定義
            '------------------------------------
            Dim ada As New NodeDatabase.DataSetMasterTableAdapters.M車両TableAdapter
            Dim tbl As New NodeDatabase.DataSetMaster.M車両DataTable
            Dim qry As New Collection

            '----------------------------------------------------------------------
            ' 条件設定
            '----------------------------------------------------------------------
            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, Me.Security.グループCD, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, Me.Security.個社CD, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.子会社CDColumn.ColumnName, Me.Security.子会社CD, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.車両番号Column.ColumnName, Me.Fields.Header.車両番号.Value, BaseDatabase.Contents.Compare.Equal))

            '------------------------------------
            ' 削除
            '------------------------------------
            Try
                ada.DeleteByCommon(qry)

            Catch ex As Exception
                MyBase.LastError = "以下のエラーのため、削除できませんでした。" & vbCrLf & ex.Message
                Return False
            End Try

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "Valid_Write"
        '''========================================================================================
        ''' <SUMMARY>検査(共通)</SUMMARY>
        ''' <RETURNS>True:正常, False:不正</RETURNS>
        '''========================================================================================
        Protected Overrides Function Valid_Write_Header_Common() As NodeContents.ActionButton.ErrorLevel
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim strMsg As String = ""
            Dim Validator As New BaseCore.Common.Validator

            '------------------------------------------------------------------
            ' ｴﾗｰｸﾘｱ
            '------------------------------------------------------------------
            Me.Fields.Header.グループCD.IsError = False
            Me.Fields.Header.個社CD.IsError = False
            Me.Fields.Header.子会社CD.IsError = False
            Me.Fields.Header.車両番号.IsError = False
            Me.Fields.Header.ドアNO.IsError = False
            Me.Fields.Header.車名.IsError = False
            Me.Fields.Header.型式.IsError = False
            Me.Fields.Header.車台番号.IsError = False
            Me.Fields.Header.車種CD.IsError = False
            Me.Fields.Header.登録番号.IsError = False
            Me.Fields.Header.初年度登録年月.IsError = False
            Me.Fields.Header.車検満了日付.IsError = False
            Me.Fields.Header.明細番号.IsError = False
            Me.Fields.Header.免責.IsError = False
            Me.Fields.Header.対物.IsError = False
            Me.Fields.Header.ドラレコ.IsError = False
            Me.Fields.Header.日報デジタコ.IsError = False
            Me.Fields.Header.決済端末.IsError = False
            Me.Fields.Header.メーター機.IsError = False
            Me.Fields.Header.無線機.IsError = False
            Me.Fields.Header.プリンター.IsError = False
            Me.Fields.Header.稼働状況.IsError = False
            Me.Fields.Header.担当乗務員01.IsError = False
            Me.Fields.Header.担当乗務員02.IsError = False
            Me.Fields.Header.担当乗務員03.IsError = False
            Me.Fields.Header.コメント.IsError = False
            Me.Fields.Header.登録日時.IsError = False
            Me.Fields.Header.登録ユーザーCD.IsError = False
            Me.Fields.Header.登録ユーザー名.IsError = False
            Me.Fields.Header.更新日時.IsError = False
            Me.Fields.Header.更新ユーザーCD.IsError = False
            Me.Fields.Header.更新ユーザー名.IsError = False

            '------------------------------------------------------------------
            ' 共通検査
            '------------------------------------------------------------------
            If Not Validator.BaseChecker(Me.Fields.Header.グループCD) Then : Me.Fields.Header.グループCD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.個社CD) Then : Me.Fields.Header.個社CD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.子会社CD) Then : Me.Fields.Header.子会社CD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.車両番号) Then : Me.Fields.Header.車両番号.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.ドアNO) Then : Me.Fields.Header.ドアNO.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.車名) Then : Me.Fields.Header.車名.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.型式) Then : Me.Fields.Header.型式.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.車台番号) Then : Me.Fields.Header.車台番号.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.車種CD) Then : Me.Fields.Header.車種CD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.登録番号) Then : Me.Fields.Header.登録番号.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.初年度登録年月) Then : Me.Fields.Header.初年度登録年月.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.車検満了日付) Then : Me.Fields.Header.車検満了日付.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.明細番号) Then : Me.Fields.Header.明細番号.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.免責) Then : Me.Fields.Header.免責.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.対物) Then : Me.Fields.Header.対物.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.ドラレコ) Then : Me.Fields.Header.ドラレコ.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.日報デジタコ) Then : Me.Fields.Header.日報デジタコ.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.決済端末) Then : Me.Fields.Header.決済端末.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.メーター機) Then : Me.Fields.Header.メーター機.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.無線機) Then : Me.Fields.Header.無線機.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.プリンター) Then : Me.Fields.Header.プリンター.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.稼働状況) Then : Me.Fields.Header.稼働状況.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.担当乗務員01) Then : Me.Fields.Header.担当乗務員01.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.担当乗務員02) Then : Me.Fields.Header.担当乗務員02.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.担当乗務員03) Then : Me.Fields.Header.担当乗務員03.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.コメント) Then : Me.Fields.Header.コメント.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.登録日時) Then : Me.Fields.Header.登録日時.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.登録ユーザーCD) Then : Me.Fields.Header.登録ユーザーCD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.登録ユーザー名) Then : Me.Fields.Header.登録ユーザー名.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.更新日時) Then : Me.Fields.Header.更新日時.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.更新ユーザーCD) Then : Me.Fields.Header.更新ユーザーCD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.更新ユーザー名) Then : Me.Fields.Header.更新ユーザー名.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If

            '------------------------------------------------------------------
            ' 返却
            '------------------------------------------------------------------
            MyBase.LastError = strMsg
            Select Case True
                Case strMsg = ""
                    Return NodeContents.ActionButton.ErrorLevel.None
                Case Else
                    Return NodeContents.ActionButton.ErrorLevel.Fatal
            End Select
        End Function
#End Region

#Region "Valid_Delete"
        '''========================================================================================
        ''' <SUMMARY>検査(共通)</SUMMARY>
        ''' <RETURNS>True:正常, False:不正</RETURNS>
        '''========================================================================================
        Protected Overrides Function Valid_Delete_Header_Common() As NodeContents.ActionButton.ErrorLevel
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim strMsg As String = ""
            Dim Validator As New BaseCore.Common.Validator

            '------------------------------------------------------------------
            ' ｴﾗｰｸﾘｱ
            '------------------------------------------------------------------
            Me.Fields.Header.グループCD.IsError = False
            Me.Fields.Header.個社CD.IsError = False
            Me.Fields.Header.子会社CD.IsError = False
            Me.Fields.Header.車両番号.IsError = False
            Me.Fields.Header.車名.IsError = False
            Me.Fields.Header.車種CD.IsError = False
            '------------------------------------------------------------------
            ' 共通検査
            '------------------------------------------------------------------
            If Not Validator.BaseChecker(Me.Fields.Header.グループCD) Then : Me.Fields.Header.グループCD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.個社CD) Then : Me.Fields.Header.個社CD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.子会社CD) Then : Me.Fields.Header.子会社CD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.車両番号) Then : Me.Fields.Header.車両番号.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.車名) Then : Me.Fields.Header.車名.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.車種CD) Then : Me.Fields.Header.車種CD.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            '------------------------------------------------------------------
            ' 返却
            '------------------------------------------------------------------
            MyBase.LastError = strMsg
            Select Case True
                Case strMsg = ""
                    Return NodeContents.ActionButton.ErrorLevel.None
                Case Else
                    Return NodeContents.ActionButton.ErrorLevel.Fatal
            End Select
        End Function
#End Region

#Region "MakeDataTable"
        '''========================================================================================
        ''' <SUMMARY>ﾃﾞｰﾀﾃｰﾌﾞﾙ生成</SUMMARY>
        ''' <RETURNS>ﾃﾞｰﾀﾃｰﾌﾞﾙ</RETURNS>
        '''========================================================================================
        Public Function MakeDataTable() As DataTable
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetViewTableAdapters.V0車両一覧TableAdapter
            Dim tbl As New NodeDatabase.DataSetView.V0車両一覧DataTable
            Dim qry As New Collection

            '----------------------------------------------------------------------
            ' 事前準備
            '----------------------------------------------------------------------

            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, Me.Security.グループCD, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, Me.Security.個社CD, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.子会社CDColumn.ColumnName, Me.Security.子会社CD, BaseDatabase.Contents.Compare.Equal))

            tbl = ada.SelectByCommon(qry)


            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return tbl
        End Function
#End Region

#Region "Execute1:Excel"
        '''========================================================================================
        ''' <SUMMARY>実行</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Execute1_Header() As Boolean

            Me.LastError = Nothing
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim xls As New BaseCore.Common.Excel

            Try
                '----------------------------------------------------------------------
                ' ﾃﾞｰﾀ抽出
                '----------------------------------------------------------------------
                Dim tbl As DataTable = Me.MakeDataTable()

                ''----------------------------------------------------------------------
                '' ｾﾞﾛ件
                ''----------------------------------------------------------------------
                If tbl.Rows.Count = 0 Then
                    Me.LastError = "指定された条件に合致するデータはありません。"
                    Return False
                End If

                '----------------------------------------------------------------------
                ' ｵｰﾌﾟﾝ
                '----------------------------------------------------------------------
                If Not xls.Open(Me.Path1) Then
                    Me.LastError = "テンプレートが見つかりません。(" & Me.Path1 & ")"
                    Return False
                End If

                '------------------------------------------------------------------------
                ' 作成
                '------------------------------------------------------------------------
                xls.SheetSelect(ConstantExcel.Header.SheetName)       'ｱｸﾃｨﾌﾞｼｰﾄ

                If Not MakeExcelData(tbl, xls) Then
                    MyBase.LastError = Me.LastError
                    Return False
                End If

                '----------------------------------------------------------------------
                ' 先頭へｶｰｿﾙ移動
                '----------------------------------------------------------------------
                xls.CellGet(0, 0)

                xls.Close(Me.Path1)
            Catch ex As Exception
                Me.LastError = ex.Message
                Return False
            Finally
            End Try

            Return True
        End Function
#End Region

#Region "エクセルデータ作成"
        '''========================================================================================
        ''' <SUMMARY>抽出したﾃｰﾌﾞﾙに編集を加える</SUMMARY>   
        '''========================================================================================
        Public Function MakeExcelData(ByRef tbl As NodeDatabase.DataSetView.V0車両一覧DataTable, ByRef xls As BaseCore.Common.Excel) As Boolean
            '------------------------------------------------------------------------
            ' 変数定義
            '------------------------------------------------------------------------
            Dim intDtlCnt As Integer = ConstantExcel.Header.RowTop

            Try
                xls.InsertRow(ConstantExcel.Header.SheetName, intDtlCnt + 2, tbl.Rows.Count - 1)

                For Each row As NodeDatabase.DataSetView.V0車両一覧Row In tbl.Rows


                    '------------------------------------------------------------------------
                    ' 値設定 明細
                    '-----------------------------------------------------------------------

                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.グループCD, BaseCore.Common.Text.Nz(row.Item(tbl.グループCDColumn.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.グループ名, BaseCore.Common.Text.Nz(row.Item(tbl.グループ名Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.個社CD, BaseCore.Common.Text.Nz(row.Item(tbl.個社CDColumn.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.個社名, BaseCore.Common.Text.Nz(row.Item(tbl.個社名Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.子会社CD, BaseCore.Common.Text.Nz(row.Item(tbl.子会社CDColumn.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.子会社名, BaseCore.Common.Text.Nz(row.Item(tbl.子会社名Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.車両番号, BaseCore.Common.Text.Nz(row.Item(tbl.車両番号Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.ドアNO, BaseCore.Common.Text.Nz(row.Item(tbl.ドアNOColumn.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.車名, BaseCore.Common.Text.Nz(row.Item(tbl.車名Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.型式, BaseCore.Common.Text.Nz(row.Item(tbl.型式Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.車台番号, BaseCore.Common.Text.Nz(row.Item(tbl.車台番号Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.車種CD, BaseCore.Common.Text.Nz(row.Item(tbl.車種CDColumn.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.登録番号, BaseCore.Common.Text.Nz(row.Item(tbl.登録番号Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.初年度登録年月, BaseCore.Common.Text.Nz(row.Item(tbl.初年度登録年月Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.車検満了日付, BaseCore.Common.Text.Nz(row.Item(tbl.車検満了日付Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.明細番号, BaseCore.Common.Text.Nz(row.Item(tbl.明細番号Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.免責, BaseCore.Common.Text.Nz(row.Item(tbl.免責Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.対物, BaseCore.Common.Text.Nz(row.Item(tbl.対物Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.ドラレコ, BaseCore.Common.Text.Nz(row.Item(tbl.ドラレコColumn.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.日報デジタコ, BaseCore.Common.Text.Nz(row.Item(tbl.日報デジタコColumn.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.決済端末, BaseCore.Common.Text.Nz(row.Item(tbl.決済端末Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.メーター機, BaseCore.Common.Text.Nz(row.Item(tbl.メーター機Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.無線機, BaseCore.Common.Text.Nz(row.Item(tbl.無線機Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.プリンター, BaseCore.Common.Text.Nz(row.Item(tbl.プリンターColumn.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.稼働状況, BaseCore.Common.Text.Nz(row.Item(tbl.稼働状況Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.担当乗務員01, BaseCore.Common.Text.Nz(row.Item(tbl.担当乗務員01Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.担当乗務員02, BaseCore.Common.Text.Nz(row.Item(tbl.担当乗務員02Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.担当乗務員03, BaseCore.Common.Text.Nz(row.Item(tbl.担当乗務員03Column.ColumnName), ""))
                    xls.CellSetValue(intDtlCnt, ConstantExcel.Header.列数.コメント, BaseCore.Common.Text.Nz(row.Item(tbl.コメントColumn.ColumnName), ""))

                    intDtlCnt += 1
                Next

            Catch ex As Exception
                Me.LastError = ex.Message
                Return False
            Finally
            End Try
            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#End Region

    End Class
End Namespace
