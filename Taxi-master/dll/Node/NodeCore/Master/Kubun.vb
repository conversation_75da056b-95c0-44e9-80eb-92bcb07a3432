﻿Namespace Frame.Master
    Partial Public Class Kubun
        Inherits NodeCore.Common.Frame

#Region "ﾌｨｰﾙﾄﾞ定義"
        Public Class Header
            ''' ----------------------------------------------------------------------------------------
            ''' <SUMMARY>列項目(基本)</SUMMARY>
            ''' ----------------------------------------------------------------------------------------
            Public 区分種別 As BaseCore.Common.Field.ItemData
            Public 区分ID As BaseCore.Common.Field.ItemData
            Public 区分名 As BaseCore.Common.Field.ItemData
            Public 汎用項目1 As BaseCore.Common.Field.ItemData
            Public 汎用項目2 As BaseCore.Common.Field.ItemData
            Public 汎用項目3 As BaseCore.Common.Field.ItemData
            Public 汎用項目4 As BaseCore.Common.Field.ItemData
            Public 汎用項目5 As BaseCore.Common.Field.ItemData
            Public 汎用項目6 As BaseCore.Common.Field.ItemData
            Public 汎用項目7 As BaseCore.Common.Field.ItemData
            Public 汎用項目8 As BaseCore.Common.Field.ItemData
            Public 汎用項目9 As BaseCore.Common.Field.ItemData
            Public 汎用項目10 As BaseCore.Common.Field.ItemData
            Public メモ As BaseCore.Common.Field.ItemData
            Public 登録ユーザーCD As BaseCore.Common.Field.ItemData
            Public 登録ユーザー名 As BaseCore.Common.Field.ItemData
            Public 登録日時 As BaseCore.Common.Field.ItemData
            Public 更新ユーザーCD As BaseCore.Common.Field.ItemData
            Public 更新ユーザー名 As BaseCore.Common.Field.ItemData
            Public 更新日時 As BaseCore.Common.Field.ItemData

            '''========================================================================================
            ''' <SUMMARY>ｺﾝｽﾄﾗｸﾀ</SUMMARY>
            '''========================================================================================
            Public Sub New(ByVal Config As NodeCore.Common.Config)
                Dim tbl As New NodeDatabase.DataSetView.V0区分DataTable

                '----------------------------------------------------------------------
                ' ｲﾝｽﾀﾝｽ(基本) 
                '----------------------------------------------------------------------

                Me.区分種別 = New BaseCore.Common.Field.ItemData(tbl.区分種別Column)
                Me.区分ID = New BaseCore.Common.Field.ItemData(tbl.区分IDColumn)
                Me.区分名 = New BaseCore.Common.Field.ItemData(tbl.区分名Column)
                Me.汎用項目1 = New BaseCore.Common.Field.ItemData(tbl.汎用項目1Column)
                Me.汎用項目2 = New BaseCore.Common.Field.ItemData(tbl.汎用項目2Column)
                Me.汎用項目3 = New BaseCore.Common.Field.ItemData(tbl.汎用項目3Column)
                Me.汎用項目4 = New BaseCore.Common.Field.ItemData(tbl.汎用項目4Column)
                Me.汎用項目5 = New BaseCore.Common.Field.ItemData(tbl.汎用項目5Column)
                Me.汎用項目6 = New BaseCore.Common.Field.ItemData(tbl.汎用項目6Column)
                Me.汎用項目7 = New BaseCore.Common.Field.ItemData(tbl.汎用項目7Column)
                Me.汎用項目8 = New BaseCore.Common.Field.ItemData(tbl.汎用項目8Column)
                Me.汎用項目9 = New BaseCore.Common.Field.ItemData(tbl.汎用項目9Column)
                Me.汎用項目10 = New BaseCore.Common.Field.ItemData(tbl.汎用項目10Column)
                Me.メモ = New BaseCore.Common.Field.ItemData(tbl.メモColumn)
                Me.登録ユーザーCD = New BaseCore.Common.Field.ItemData(tbl.登録ユーザーCDColumn)
                Me.登録ユーザー名 = New BaseCore.Common.Field.ItemData(tbl.登録ユーザー名Column)
                Me.登録日時 = New BaseCore.Common.Field.ItemData(tbl.登録日時Column)
                Me.更新ユーザーCD = New BaseCore.Common.Field.ItemData(tbl.更新ユーザーCDColumn)
                Me.更新ユーザー名 = New BaseCore.Common.Field.ItemData(tbl.更新ユーザー名Column)
                Me.更新日時 = New BaseCore.Common.Field.ItemData(tbl.更新日時Column)

            End Sub
        End Class
#End Region

#Region "変数定義"
        Public Class Paramater
            Public Header As Header
        End Class

        '''========================================================================================
        ''' <summary>ﾍｯﾀﾞ</summary>
        '''========================================================================================
        Private _Fields As New Paramater
        Public Property Fields() As Paramater
            Get
                Return _Fields
            End Get

            Set(ByVal value As Paramater)
                _Fields = value
            End Set
        End Property
#End Region

#Region "ｺﾝｽﾄﾗｸﾀ"
        '''========================================================================================
        ''' <SUMMARY>ｺﾝｽﾄﾗｸﾀ</SUMMARY>
        ''' <PARAM name="Security">ｾｷｭﾘﾃｨ ｸﾗｽ</PARAM>
        ''' <PARAM name="Config">ｺﾝﾌｨｸﾞ ｸﾗｽ</PARAM>
        ''' <PARAM name="PagePath">ﾍﾟｰｼﾞﾊﾟｽ</PARAM>
        '''========================================================================================
        Public Sub New(ByVal Security As NodeCore.Common.Security, ByVal Config As NodeCore.Common.Config, ByVal PagePath As String)
            '----------------------------------------------------------------------
            ' ｾｷｭﾘﾃｨ 設定
            '----------------------------------------------------------------------
            MyBase.New(Security, Config, PagePath)

            Me.Fields.Header = New Header(Config)
        End Sub
#End Region

#Region "ﾍｯﾀﾞ"
#Region "Clear"
        '''========================================================================================
        ''' <SUMMARY>ｸﾘｱ</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Clear_Header(Optional ByVal KeyClear As Boolean = False) As Boolean
            '----------------------------------------------------------------------
            ' ｷｰ部ｸﾘｱ
            '----------------------------------------------------------------------
            If KeyClear Then
                Me.Fields.Header.区分種別.Value = Nothing
                Me.Fields.Header.区分ID.Value = Nothing
            End If

            '----------------------------------------------------------------------
            ' ﾃﾞｰﾀ部ｸﾘｱ
            '----------------------------------------------------------------------
            Me.Fields.Header.区分名.Value = Nothing
            Me.Fields.Header.汎用項目1.Value = Nothing
            Me.Fields.Header.汎用項目2.Value = Nothing
            Me.Fields.Header.汎用項目3.Value = Nothing
            Me.Fields.Header.汎用項目4.Value = Nothing
            Me.Fields.Header.汎用項目5.Value = Nothing
            Me.Fields.Header.汎用項目6.Value = Nothing
            Me.Fields.Header.汎用項目7.Value = Nothing
            Me.Fields.Header.汎用項目8.Value = Nothing
            Me.Fields.Header.汎用項目9.Value = Nothing
            Me.Fields.Header.汎用項目10.Value = Nothing
            Me.Fields.Header.メモ.Value = Nothing
            Me.Fields.Header.登録日時.Value = Nothing
            Me.Fields.Header.更新日時.Value = Nothing

            '----------------------------------------------------------------------
            ' ﾃﾞｰﾀ部ｸﾘｱ
            '----------------------------------------------------------------------
            'Me.Read()

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "Read"
        '''========================================================================================
        ''' <SUMMARY>読込</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Read_Header() As Boolean
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetViewTableAdapters.V0区分TableAdapter
            Dim tbl As New NodeDatabase.DataSetView.V0区分DataTable

            Dim qry As New Collection

            '----------------------------------------------------------------------
            ' 条件設定
            '----------------------------------------------------------------------
            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl.区分種別Column.ColumnName, Me.Fields.Header.区分種別.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.区分IDColumn.ColumnName, Me.Fields.Header.区分ID.Value, BaseDatabase.Contents.Compare.Equal))

            '----------------------------------------------------------------------
            ' 読込
            '----------------------------------------------------------------------
            tbl = ada.SelectByCommon(qry)
            If tbl.Count > 0 Then
                'Me.Fields.Header.会社CD.Value = tbl(0).Item(tbl.子会社CDColumn.ColumnName)
                'Me.Fields.Header.区分種別.Value = tbl(0).Item(tbl.区分種別Column.ColumnName)
                'Me.Fields.Header.区分ID.Value = tbl(0).Item(tbl.区分IDColumn.ColumnName)
                Me.Fields.Header.区分名.Value = tbl(0).Item(tbl.区分名Column.ColumnName)
                Me.Fields.Header.汎用項目1.Value = tbl(0).Item(tbl.汎用項目1Column.ColumnName)
                Me.Fields.Header.汎用項目2.Value = tbl(0).Item(tbl.汎用項目2Column.ColumnName)
                Me.Fields.Header.汎用項目3.Value = tbl(0).Item(tbl.汎用項目3Column.ColumnName)
                Me.Fields.Header.汎用項目4.Value = tbl(0).Item(tbl.汎用項目4Column.ColumnName)
                Me.Fields.Header.汎用項目5.Value = tbl(0).Item(tbl.汎用項目5Column.ColumnName)
                Me.Fields.Header.汎用項目6.Value = tbl(0).Item(tbl.汎用項目6Column.ColumnName)
                Me.Fields.Header.汎用項目7.Value = tbl(0).Item(tbl.汎用項目7Column.ColumnName)
                Me.Fields.Header.汎用項目8.Value = tbl(0).Item(tbl.汎用項目8Column.ColumnName)
                Me.Fields.Header.汎用項目9.Value = tbl(0).Item(tbl.汎用項目9Column.ColumnName)
                Me.Fields.Header.汎用項目10.Value = tbl(0).Item(tbl.汎用項目10Column.ColumnName)
                Me.Fields.Header.メモ.Value = tbl(0).Item(tbl.メモColumn.ColumnName)
                Me.Fields.Header.登録日時.Value = tbl(0).Item(tbl.登録日時Column.ColumnName)
                Me.Fields.Header.更新日時.Value = tbl(0).Item(tbl.更新日時Column.ColumnName)
            Else
                Me.Clear(False)
            End If

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "Write"
        '''========================================================================================
        ''' <SUMMARY>書込</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Write_Header() As Boolean
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim strNow As String = Format(Now, NodeContents.Constant.System.DATE_TIME_FORMAT)

            Dim ada As New NodeDatabase.DataSetMasterTableAdapters.M区分TableAdapter
            Dim tbl As New NodeDatabase.DataSetMaster.M区分DataTable
            Dim qry As New Collection

            '----------------------------------------------------------------------
            ' 条件設定
            '----------------------------------------------------------------------
            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl.区分種別Column.ColumnName, Me.Fields.Header.区分種別.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.区分IDColumn.ColumnName, Me.Fields.Header.区分ID.Value, BaseDatabase.Contents.Compare.Equal))


            '----------------------------------------------------------------------
            ' 読込
            '----------------------------------------------------------------------
            tbl = ada.SelectByCommon(qry)


            '----------------------------------------------------------------------
            ' 事前準備
            '----------------------------------------------------------------------
            Me.Fields.Header.更新日時.Value = strNow
            Me.Fields.Header.更新ユーザーCD.Value = Me.Security.ユーザーCD
            Me.Fields.Header.更新ユーザー名.Value = Me.Security.ユーザー名


            '----------------------------------------------------------------------
            ' 保存
            '----------------------------------------------------------------------
            If tbl.Count = 0 Then
                '----------------------------------------------------------------------
                ' 新規
                '----------------------------------------------------------------------
                Me.Fields.Header.登録日時.Value = strNow
                Me.Fields.Header.登録ユーザーCD.Value = Me.Security.ユーザーCD
                Me.Fields.Header.登録ユーザー名.Value = Me.Security.ユーザー名
                Try
                    ada.Insert(Me.Fields.Header.区分種別.Value _
                            , Me.Fields.Header.区分ID.Value _
                            , Me.Fields.Header.区分名.Value _
                            , Me.Fields.Header.汎用項目1.Value _
                            , Me.Fields.Header.汎用項目2.Value _
                            , Me.Fields.Header.汎用項目3.Value _
                            , Me.Fields.Header.汎用項目4.Value _
                            , Me.Fields.Header.汎用項目5.Value _
                            , Me.Fields.Header.汎用項目6.Value _
                            , Me.Fields.Header.汎用項目7.Value _
                            , Me.Fields.Header.汎用項目8.Value _
                            , Me.Fields.Header.汎用項目9.Value _
                            , Me.Fields.Header.汎用項目10.Value _
                            , Me.Fields.Header.メモ.Value _
                            , Me.Fields.Header.登録ユーザーCD.Value _
                            , Me.Fields.Header.登録ユーザー名.Value _
                            , Me.Fields.Header.登録日時.Value _
                            , Me.Fields.Header.更新ユーザーCD.Value _
                            , Me.Fields.Header.更新ユーザー名.Value _
                            , Me.Fields.Header.更新日時.Value _
                            , 1
                            )

                Catch ex As Exception
                    MyBase.LastError = "以下のエラーのため、追加できませんでした。" & vbCrLf & ex.Message
                    Return False
                End Try
            Else
                '----------------------------------------------------------------------
                ' 訂正
                '----------------------------------------------------------------------
                Try
                    tbl(0).Item(tbl.区分種別Column.ColumnName) = Me.Fields.Header.区分種別.Value
                    tbl(0).Item(tbl.区分IDColumn.ColumnName) = Me.Fields.Header.区分ID.Value
                    tbl(0).Item(tbl.区分名Column.ColumnName) = Me.Fields.Header.区分名.Value
                    tbl(0).Item(tbl.汎用項目1Column.ColumnName) = Me.Fields.Header.汎用項目1.Value
                    tbl(0).Item(tbl.汎用項目2Column.ColumnName) = Me.Fields.Header.汎用項目2.Value
                    tbl(0).Item(tbl.汎用項目3Column.ColumnName) = Me.Fields.Header.汎用項目3.Value
                    tbl(0).Item(tbl.汎用項目4Column.ColumnName) = Me.Fields.Header.汎用項目4.Value
                    tbl(0).Item(tbl.汎用項目5Column.ColumnName) = Me.Fields.Header.汎用項目5.Value
                    tbl(0).Item(tbl.汎用項目6Column.ColumnName) = Me.Fields.Header.汎用項目6.Value
                    tbl(0).Item(tbl.汎用項目7Column.ColumnName) = Me.Fields.Header.汎用項目7.Value
                    tbl(0).Item(tbl.汎用項目8Column.ColumnName) = Me.Fields.Header.汎用項目8.Value
                    tbl(0).Item(tbl.汎用項目9Column.ColumnName) = Me.Fields.Header.汎用項目9.Value
                    tbl(0).Item(tbl.汎用項目10Column.ColumnName) = Me.Fields.Header.汎用項目10.Value
                    tbl(0).Item(tbl.メモColumn.ColumnName) = Me.Fields.Header.メモ.Value
                    tbl(0).Item(tbl.登録日時Column.ColumnName) = Me.Fields.Header.登録日時.Value
                    tbl(0).Item(tbl.更新日時Column.ColumnName) = Me.Fields.Header.更新日時.Value
                    ada.Update(tbl)

                Catch ex As Exception
                    MyBase.LastError = "以下のエラーのため、更新できませんでした。" & vbCrLf & ex.Message
                    Return False
                End Try
            End If

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "Delete"
        '''========================================================================================
        ''' <SUMMARY>削除</SUMMARY>
        ''' <RETURNS>True:正常終了, False:ｼｽﾃﾑｴﾗｰ</RETURNS>
        '''========================================================================================
        Protected Overrides Function Delete_Header() As Boolean
            '----------------------------------------------------------------------
            ' ﾍｯﾀﾞ
            '----------------------------------------------------------------------
            '------------------------------------
            ' 変数定義
            '------------------------------------
            Dim ada As New NodeDatabase.DataSetMasterTableAdapters.M区分TableAdapter
            Dim tbl As New NodeDatabase.DataSetMaster.M区分DataTable
            Dim qry As New Collection

            '----------------------------------------------------------------------
            ' 条件設定
            '----------------------------------------------------------------------
            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl.区分種別Column.ColumnName, Me.Fields.Header.区分種別.Value, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.区分IDColumn.ColumnName, Me.Fields.Header.区分ID.Value, BaseDatabase.Contents.Compare.Equal))

            '------------------------------------
            ' 削除
            '------------------------------------
            Try
                ada.DeleteByCommon(qry)

            Catch ex As Exception
                MyBase.LastError = "以下のエラーのため、削除できませんでした。" & vbCrLf & ex.Message
                Return False
            End Try

            '----------------------------------------------------------------------
            ' 正常終了
            '----------------------------------------------------------------------
            Return True
        End Function
#End Region

#Region "Valid_Write"
        '''========================================================================================
        ''' <SUMMARY>検査(共通)</SUMMARY>
        ''' <RETURNS>True:正常, False:不正</RETURNS>
        '''========================================================================================
        Protected Overrides Function Valid_Write_Header_Common() As NodeContents.ActionButton.ErrorLevel
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim strMsg As String = ""
            Dim Validator As New BaseCore.Common.Validator

            '------------------------------------------------------------------
            ' ｴﾗｰｸﾘｱ
            '------------------------------------------------------------------
            Me.Fields.Header.区分種別.IsError = False
            Me.Fields.Header.区分ID.IsError = False
            Me.Fields.Header.区分名.IsError = False
            Me.Fields.Header.汎用項目1.IsError = False
            Me.Fields.Header.汎用項目2.IsError = False
            Me.Fields.Header.汎用項目3.IsError = False
            Me.Fields.Header.汎用項目4.IsError = False
            Me.Fields.Header.汎用項目5.IsError = False
            Me.Fields.Header.汎用項目6.IsError = False
            Me.Fields.Header.汎用項目7.IsError = False
            Me.Fields.Header.汎用項目8.IsError = False
            Me.Fields.Header.汎用項目9.IsError = False
            Me.Fields.Header.汎用項目10.IsError = False
            Me.Fields.Header.メモ.IsError = False
            Me.Fields.Header.登録日時.IsError = False
            Me.Fields.Header.更新日時.IsError = False

            '------------------------------------------------------------------
            ' 共通検査
            '------------------------------------------------------------------
            If Not Validator.BaseChecker(Me.Fields.Header.区分種別) Then : Me.Fields.Header.区分種別.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.区分ID) Then : Me.Fields.Header.区分ID.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.区分名) Then : Me.Fields.Header.区分名.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.汎用項目1) Then : Me.Fields.Header.汎用項目1.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.汎用項目2) Then : Me.Fields.Header.汎用項目2.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.汎用項目3) Then : Me.Fields.Header.汎用項目3.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.汎用項目4) Then : Me.Fields.Header.汎用項目4.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.汎用項目5) Then : Me.Fields.Header.汎用項目5.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.汎用項目6) Then : Me.Fields.Header.汎用項目6.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.汎用項目7) Then : Me.Fields.Header.汎用項目7.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.汎用項目8) Then : Me.Fields.Header.汎用項目8.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.汎用項目9) Then : Me.Fields.Header.汎用項目9.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.汎用項目10) Then : Me.Fields.Header.汎用項目10.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.メモ) Then : Me.Fields.Header.メモ.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.登録日時) Then : Me.Fields.Header.登録日時.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.更新日時) Then : Me.Fields.Header.更新日時.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            '------------------------------------------------------------------
            ' 返却
            '------------------------------------------------------------------
            MyBase.LastError = strMsg
            Select Case True
                Case strMsg = ""
                    Return NodeContents.ActionButton.ErrorLevel.None
                Case Else
                    Return NodeContents.ActionButton.ErrorLevel.Fatal
            End Select
        End Function
#End Region

#Region "Valid_Delete"
        '''========================================================================================
        ''' <SUMMARY>検査(共通)</SUMMARY>
        ''' <RETURNS>True:正常, False:不正</RETURNS>
        '''========================================================================================
        Protected Overrides Function Valid_Delete_Header_Common() As NodeContents.ActionButton.ErrorLevel
            '----------------------------------------------------------------------
            ' 変数定義
            '----------------------------------------------------------------------
            Dim strMsg As String = ""
            Dim Validator As New BaseCore.Common.Validator

            '------------------------------------------------------------------
            ' ｴﾗｰｸﾘｱ
            '------------------------------------------------------------------
            Me.Fields.Header.区分種別.IsError = False
            Me.Fields.Header.区分ID.IsError = False
            Me.Fields.Header.区分名.IsError = False
            '------------------------------------------------------------------
            ' 共通検査
            '------------------------------------------------------------------
            If Not Validator.BaseChecker(Me.Fields.Header.区分種別) Then : Me.Fields.Header.区分種別.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.区分ID) Then : Me.Fields.Header.区分ID.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            If Not Validator.BaseChecker(Me.Fields.Header.区分名) Then : Me.Fields.Header.区分名.IsError = True : Validator.SetMessage(strMsg, Validator.LastError) : End If
            '------------------------------------------------------------------
            ' 返却
            '------------------------------------------------------------------
            MyBase.LastError = strMsg
            Select Case True
                Case strMsg = ""
                    Return NodeContents.ActionButton.ErrorLevel.None
                Case Else
                    Return NodeContents.ActionButton.ErrorLevel.Fatal
            End Select
        End Function
#End Region

#End Region
    End Class
End Namespace
