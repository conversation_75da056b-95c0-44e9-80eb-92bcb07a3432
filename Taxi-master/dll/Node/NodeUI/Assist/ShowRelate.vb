﻿Namespace Help
    Public Class ShowRelate
#Region "実行"
        Public Function Execute(ByVal HelpType As NodeUI.Help.Type, ByVal Value As String) As String
            Select Case HelpType
                'ｺｰﾄﾞﾏｽﾀ
                Case NodeUI.Help.Type.M区分_権限区分 : Return M区分_共通部品(Value, NodeContents.Constant.CodeType.権限区分)
                Case NodeUI.Help.Type.M区分_勤務区分 : Return M区分_共通部品(Value, NodeContents.Constant.CodeType.勤務区分)
                Case NodeUI.Help.Type.Mシフト区分 : Return M区分_共通部品(Value, NodeContents.Constant.CodeType.シフト区分)
                'Case NodeUI.Help.Type.Mシフト区分 : Return Mシフト区分(Value)
                Case NodeUI.Help.Type.M区分_符号区分 : Return M区分_共通部品(Value, NodeContents.Constant.CodeType.符号区分)
                Case NodeUI.Help.Type.M区分_得意先区分 : Return M区分_共通部品(Value, NodeContents.Constant.CodeType.得意先区分)
                Case NodeUI.Help.Type.M区分_事故区分 : Return M区分_共通部品(Value, NodeContents.Constant.CodeType.事故区分)
                Case NodeUI.Help.Type.M区分_車種区分 : Return M区分_共通部品(Value, NodeContents.Constant.CodeType.車種区分)
                Case NodeUI.Help.Type.M区分_運転免許種類 : Return M区分_共通部品(Value, NodeContents.Constant.CodeType.運転免許種類)


                'ｼｽﾃﾑ


                'ﾏｽﾀ
                Case NodeUI.Help.Type.Mユーザー : Return Mユーザー(Value)
                Case NodeUI.Help.Type.M乗務員 : Return M乗務員(Value)
                Case NodeUI.Help.Type.Mグループ : Return Mグループ(Value)
                Case NodeUI.Help.Type.M個社 : Return M個社(Value)
                Case NodeUI.Help.Type.M子会社 : Return M子会社(Value)
                Case NodeUI.Help.Type.M車両 : Return M車両(Value)
                Case NodeUI.Help.Type.M得意先 : Return M得意先(Value)

                    'ﾄﾗﾝｻﾞｸｼｮﾝ


                    'その他
                Case NodeUI.Help.Type.シフト_合計計算 : Return シフト_合計計算(Value)
                Case NodeUI.Help.Type.シフト_予定計算 : Return シフト_予定計算(Value)
                Case NodeUI.Help.Type.シフト_実績計算 : Return シフト_実績計算(Value)
                Case NodeUI.Help.Type.内訳計算 : Return 内訳計算(Value)
                Case NodeUI.Help.Type.料金計算 : Return 料金計算(Value)
                Case NodeUI.Help.Type.料金合計計算 : Return 料金合計計算(Value)
                Case NodeUI.Help.Type.営収額計算 : Return 営収額計算(Value)


                Case Else : Return Nothing
            End Select
        End Function
#End Region

#Region "返却"
        Private Shared Function ReturnValue(ByVal value() As String) As String
            Dim strReturn As String = ""
            Dim strDelim As String = ""

            For i As Integer = 0 To value.Length - 1
                strReturn &= strDelim & value(i)
                strDelim = vbTab
            Next

            Return strReturn
        End Function
#End Region

#Region "ｺﾝｽﾀﾝﾄ"
#Region "共通部品"
        '''========================================================================================
        ''' <summary>共通部品</summary>
        ''' <param name="Value">
        '''  0(区分種別)
        ''' </param>
        ''' <returns>
        '''  0(区分名)
        ''' </returns>
        '''========================================================================================
        Private Function M区分_共通部品(ByVal Value As String, ByVal TypeClass As String) As String
            '----------------------------------------------------------------------
            ' 返却 配列
            '----------------------------------------------------------------------
            Dim strReturn(0) As String

            '----------------------------------------------------------------------
            ' ｷｰが空白なら即終了
            '----------------------------------------------------------------------
            Select Case True
                Case Value.Split(vbTab)(0) = ""
                    Return ReturnValue(strReturn)
            End Select

            '----------------------------------------------------------------------
            ' 事前準備
            '----------------------------------------------------------------------
            Dim strCodeValue As String = BaseCore.Common.Text.Nz(Value.Split(vbTab)(0).ToString, "")

            '----------------------------------------------------------------------
            ' 名称類 取得
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetMasterTableAdapters.M区分TableAdapter
            Dim tbl As New NodeDatabase.DataSetMaster.M区分DataTable
            Dim qry As New Collection

            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl.区分種別Column.ColumnName, TypeClass, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.区分IDColumn.ColumnName, strCodeValue, BaseDatabase.Contents.Compare.Equal))
            tbl = ada.SelectByCommon(qry)

            If tbl.Count > 0 Then
                strReturn(0) = tbl(0).Item(tbl.区分名Column.ColumnName)
            End If

            '----------------------------------------------------------------------
            ' 返却
            '----------------------------------------------------------------------
            Return ReturnValue(strReturn)
        End Function
#End Region

#End Region

#Region "ｼｽﾃﾑ"

#End Region

#Region "ﾏｽﾀ"

#Region "Mユーザー"
        '''========================================================================================
        ''' <summary>Mユーザー</summary>
        '''========================================================================================
        Private Shared Function Mユーザー(ByVal Value As String) As String
            '----------------------------------------------------------------------
            ' 返却 配列
            '----------------------------------------------------------------------
            Dim strReturn(0) As String

            '----------------------------------------------------------------------
            ' ｷｰが空白なら即終了
            '----------------------------------------------------------------------
            Select Case True
                Case Value.Split(vbTab)(0) = ""
                    Return ReturnValue(strReturn)
            End Select

            '----------------------------------------------------------------------
            ' 名称類 取得
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetViewTableAdapters.V0ユーザーTableAdapter
            Dim tbl As New NodeDatabase.DataSetView.V0ユーザーDataTable
            Dim qry As New Collection

            qry.Clear()
            'qry.Add(New BaseDatabase.Condition(tbl.CDColumn.ColumnName, Value.Split(vbTab)(0), BaseDatabase.Contents.Compare.Equal))
            'qry.Add(New BaseDatabase.Condition(tbl.ユーザーCDColumn.ColumnName, Value.Split(vbTab)(0), BaseDatabase.Contents.Compare.Equal))
            'qry.Add(New BaseDatabase.Condition(tbl.ユーザーCDColumn.ColumnName, Value.Split(vbTab)(0), BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.ユーザーCDColumn.ColumnName, Value.Split(vbTab)(0), BaseDatabase.Contents.Compare.Equal))
            tbl = ada.SelectByCommon(qry)

            If tbl.Count > 0 Then
                strReturn(0) = tbl(0).Item(tbl.ユーザー名Column.ColumnName).ToString
            End If

            '----------------------------------------------------------------------
            ' 返却
            '----------------------------------------------------------------------
            Return ReturnValue(strReturn)
        End Function
#End Region

#Region "M個社"
        '''========================================================================================
        ''' <summary>M乗務員</summary>
        '''========================================================================================
        Private Shared Function M個社(ByVal Value As String) As String
            '----------------------------------------------------------------------
            ' 返却 配列
            '----------------------------------------------------------------------
            Dim strReturn(0) As String

            '----------------------------------------------------------------------
            ' ｷｰが空白なら即終了
            '----------------------------------------------------------------------
            Select Case True
                Case Value.Split(vbTab)(0) = ""
                    Return ReturnValue(strReturn)
            End Select

            '----------------------------------------------------------------------
            ' 名称類 取得
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetViewTableAdapters.V0個社TableAdapter
            Dim tbl As New NodeDatabase.DataSetView.V0個社DataTable
            Dim qry As New Collection

            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, Value.Split(vbTab)(0), BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, Value.Split(vbTab)(1), BaseDatabase.Contents.Compare.Equal))
            tbl = ada.SelectByCommon(qry)

            If tbl.Count > 0 Then
                strReturn(0) = tbl(0).Item(tbl.個社名Column.ColumnName).ToString
            End If

            '----------------------------------------------------------------------
            ' 返却
            '----------------------------------------------------------------------
            Return ReturnValue(strReturn)
        End Function
#End Region

#Region "M子会社"
        '''========================================================================================
        ''' <summary>M子会社</summary>
        '''========================================================================================
        Private Shared Function M子会社(ByVal Value As String) As String
            '----------------------------------------------------------------------
            ' 返却 配列
            '----------------------------------------------------------------------
            Dim strReturn(1) As String

            '----------------------------------------------------------------------
            ' ｷｰが空白なら即終了
            '----------------------------------------------------------------------
            Select Case True
                Case Value.Split(vbTab)(0) = ""
                    Return ReturnValue(strReturn)
            End Select

            '----------------------------------------------------------------------
            ' 名称類 取得
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetViewTableAdapters.V0子会社TableAdapter
            Dim tbl As New NodeDatabase.DataSetView.V0子会社DataTable
            Dim qry As New Collection

            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, Value.Split(vbTab)(0), BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, Value.Split(vbTab)(1), BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.子会社CDColumn.ColumnName, Value.Split(vbTab)(2), BaseDatabase.Contents.Compare.Equal))
            tbl = ada.SelectByCommon(qry)

            If tbl.Count > 0 Then
                strReturn(0) = tbl(0).Item(tbl.子会社名Column.ColumnName).ToString
            End If

            strReturn(1) = ""

            '----------------------------------------------------------------------
            ' 返却
            '----------------------------------------------------------------------
            Return ReturnValue(strReturn)
        End Function
#End Region

#Region "M車両"
        '''========================================================================================
        ''' <summary>M車両</summary>
        '''========================================================================================
        Private Shared Function M車両(ByVal Value As String) As String
            '----------------------------------------------------------------------
            ' 返却 配列
            '----------------------------------------------------------------------
            Dim strReturn(0) As String

            '----------------------------------------------------------------------
            ' ｷｰが空白なら即終了
            '----------------------------------------------------------------------
            Select Case True
                Case Value.Split(vbTab)(0) = ""
                    Return ReturnValue(strReturn)
            End Select

            '----------------------------------------------------------------------
            ' 名称類 取得
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetViewTableAdapters.V0車両TableAdapter
            Dim tbl As New NodeDatabase.DataSetView.V0車両DataTable
            Dim qry As New Collection

            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, Value.Split(vbTab)(0), BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, Value.Split(vbTab)(1), BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.子会社CDColumn.ColumnName, Value.Split(vbTab)(2), BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.車両番号Column.ColumnName, Value.Split(vbTab)(3), BaseDatabase.Contents.Compare.Equal))
            tbl = ada.SelectByCommon(qry)

            If tbl.Count > 0 Then
                strReturn(0) = tbl(0).Item(tbl.車名Column.ColumnName).ToString
            End If

            '----------------------------------------------------------------------
            ' 返却
            '----------------------------------------------------------------------
            Return ReturnValue(strReturn)
        End Function
#End Region

#Region "Mグループ"
        '''========================================================================================
        ''' <summary>M乗務員</summary>
        '''========================================================================================
        Private Shared Function Mグループ(ByVal Value As String) As String
            '----------------------------------------------------------------------
            ' 返却 配列
            '----------------------------------------------------------------------
            Dim strReturn(0) As String

            '----------------------------------------------------------------------
            ' ｷｰが空白なら即終了
            '----------------------------------------------------------------------
            Select Case True
                Case Value.Split(vbTab)(0) = ""
                    Return ReturnValue(strReturn)
            End Select

            '----------------------------------------------------------------------
            ' 名称類 取得
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetViewTableAdapters.V0グループTableAdapter
            Dim tbl As New NodeDatabase.DataSetView.V0グループDataTable
            Dim qry As New Collection

            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, Value.Split(vbTab)(0), BaseDatabase.Contents.Compare.Equal))
            tbl = ada.SelectByCommon(qry)

            If tbl.Count > 0 Then
                strReturn(0) = tbl(0).Item(tbl.グループ名Column.ColumnName).ToString
            End If

            '----------------------------------------------------------------------
            ' 返却
            '----------------------------------------------------------------------
            Return ReturnValue(strReturn)
        End Function
#End Region

#Region "M乗務員"
        '''========================================================================================
        ''' <summary>M乗務員</summary>
        '''========================================================================================
        Private Shared Function M乗務員(ByVal Value As String) As String
            '----------------------------------------------------------------------
            ' 返却 配列
            '----------------------------------------------------------------------
            Dim strReturn(0) As String

            '----------------------------------------------------------------------
            ' ｷｰが空白なら即終了
            '----------------------------------------------------------------------
            Select Case True
                Case Value.Split(vbTab)(0) = ""
                    Return ReturnValue(strReturn)
            End Select

            '----------------------------------------------------------------------
            ' 名称類 取得
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetViewTableAdapters.V0乗務員TableAdapter
            Dim tbl As New NodeDatabase.DataSetView.V0乗務員DataTable
            Dim qry As New Collection

            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, Value.Split(vbTab)(0), BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, Value.Split(vbTab)(1), BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.子会社CDColumn.ColumnName, Value.Split(vbTab)(2), BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.乗務員CDColumn.ColumnName, Value.Split(vbTab)(3), BaseDatabase.Contents.Compare.Equal))
            tbl = ada.SelectByCommon(qry)

            If tbl.Count > 0 Then
                strReturn(0) = tbl(0).Item(tbl.乗務員氏Column.ColumnName).ToString() + tbl(0).Item(tbl.乗務員名Column.ColumnName).ToString()
            End If

            '----------------------------------------------------------------------
            ' 返却
            '----------------------------------------------------------------------
            Return ReturnValue(strReturn)
        End Function
#End Region

#Region "Mシフト区分"
        '''========================================================================================
        ''' <summary>Mシフト区分</summary>
        '''========================================================================================
        Private Function Mシフト区分(ByVal Value As String) As String
            '----------------------------------------------------------------------
            ' 返却 配列
            '----------------------------------------------------------------------
            Dim strReturn(0) As String

            '----------------------------------------------------------------------
            ' ｷｰが空白なら即終了
            '----------------------------------------------------------------------
            Select Case True
                Case Value.Split(vbTab)(0) = ""
                    Return ReturnValue(strReturn)
            End Select

            '----------------------------------------------------------------------
            ' 事前準備
            '----------------------------------------------------------------------
            Dim strCodeValue As String = BaseCore.Common.Text.Nz(Value.Split(vbTab)(0).ToString, "")

            '----------------------------------------------------------------------
            ' 名称類 取得
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetMasterTableAdapters.Mシフト区分TableAdapter
            Dim tbl As New NodeDatabase.DataSetMaster.Mシフト区分DataTable
            Dim qry As New Collection

            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl.シフト区分Column.ColumnName, strCodeValue, BaseDatabase.Contents.Compare.Equal))
            tbl = ada.SelectByCommon(qry)

            If tbl.Count > 0 Then
                strReturn(0) = tbl(0).Item(tbl.シフト名Column.ColumnName)
            End If

            '----------------------------------------------------------------------
            ' 返却
            '----------------------------------------------------------------------
            Return ReturnValue(strReturn)
        End Function
#End Region

#Region "M得意先"
        '''========================================================================================
        ''' <summary>M得意先</summary>
        '''========================================================================================
        Private Shared Function M得意先(ByVal Value As String) As String
            '----------------------------------------------------------------------
            ' 返却 配列
            '----------------------------------------------------------------------
            Dim strReturn(0) As String

            '----------------------------------------------------------------------
            ' ｷｰが空白なら即終了
            '----------------------------------------------------------------------
            Select Case True
                Case Value.Split(vbTab)(0) = ""
                    Return ReturnValue(strReturn)
            End Select

            '----------------------------------------------------------------------
            ' 名称類 取得
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetViewTableAdapters.V0得意先TableAdapter
            Dim tbl As New NodeDatabase.DataSetView.V0得意先DataTable
            Dim qry As New Collection

            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, Value.Split(vbTab)(0), BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, Value.Split(vbTab)(1), BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.子会社CDColumn.ColumnName, Value.Split(vbTab)(2), BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.得意先CDColumn.ColumnName, Value.Split(vbTab)(3), BaseDatabase.Contents.Compare.Equal))
            tbl = ada.SelectByCommon(qry)

            If tbl.Count > 0 Then
                strReturn(0) = tbl(0).Item(tbl.得意先名Column.ColumnName).ToString
            End If

            '----------------------------------------------------------------------
            ' 返却
            '----------------------------------------------------------------------
            Return ReturnValue(strReturn)
        End Function
#End Region

#End Region

#Region "ﾄﾗﾝｻﾞｸｼｮﾝ"

#End Region

#Region "その他"

#Region "シフト_合計計算"
        '''========================================================================================
        ''' <summary>シフト_合計計算</summary>
        ''' <param name="Value">
        '''  0～(左辺)
        ''' </param>
        ''' <returns>
        '''  0(合計値)
        ''' </returns>
        '''========================================================================================
        Private Shared Function シフト_合計計算(ByVal Value As String) As String

            Dim strReturn(0) As String

            Dim decTotal As Decimal = 0

            '----------------------------------------------------------------------
            ' 返却 配列
            '----------------------------------------------------------------------
            For i As Integer = 0 To Value.Split(vbTab).Length - 1
                If Value.Split(vbTab)(i).Trim = "1" Then
                    decTotal += 1
                End If
            Next

            strReturn(0) = Format(decTotal, "#,##0")

            '----------------------------------------------------------------------
            ' 返却
            '----------------------------------------------------------------------
            Return ReturnValue(strReturn)
        End Function
#End Region


#Region "シフト_予定計算"
        '''========================================================================================
        ''' <summary>シフト_予定計算</summary>
        ''' <param name="Value">
        '''  0～(左辺)
        ''' </param>
        ''' <returns>
        '''  0(合計値)
        ''' </returns>
        '''========================================================================================
        Private Shared Function シフト_予定計算(ByVal Value As String) As String

            Dim strReturn(6) As String

            Dim dec昼 As Decimal = 0
            Dim dec夜 As Decimal = 0
            Dim dec隔 As Decimal = 0
            Dim dec計 As Decimal = 0

            '----------------------------------------------------------------------
            ' 返却 配列
            '----------------------------------------------------------------------

            For i As Integer = 0 To Value.Split(vbTab).Length - 1
                If Value.Split(vbTab)(i).Trim <> "" Then
                    dec計 += 1
                End If

                Select Case Value.Split(vbTab)(i).Trim
                    Case "1"
                        dec昼 += 1
                    Case "2"
                        dec夜 += 1
                    Case "3"
                        dec隔 += 1
                End Select
            Next

            strReturn(0) = Format(dec昼, "#,##0")
            strReturn(1) = Format(dec夜, "#,##0")
            strReturn(2) = Format(dec隔, "#,##0")
            strReturn(3) = Format(dec計, "#,##0")

            '----------------------------------------------------------------------
            ' 返却
            '----------------------------------------------------------------------
            Return ReturnValue(strReturn)
        End Function
#End Region


#Region "シフト_実績計算"
        '''========================================================================================
        ''' <summary>シフト_実績計算</summary>
        ''' <param name="Value">
        '''  0～(左辺)
        ''' </param>
        ''' <returns>
        '''  0(合計値)
        ''' </returns>
        '''========================================================================================
        Private Shared Function シフト_実績計算(ByVal Value As String) As String

            Dim strReturn(6) As String

            Dim dec昼 As Decimal = 0
            Dim dec夜 As Decimal = 0
            Dim dec隔 As Decimal = 0
            Dim dec計 As Decimal = 0
            Dim dec欠 As Decimal = 0
            Dim dec有 As Decimal = 0

            '----------------------------------------------------------------------
            ' 返却 配列
            '----------------------------------------------------------------------

            For i As Integer = 0 To Value.Split(vbTab).Length - 1
                If Value.Split(vbTab)(i).Trim <> "" Then
                    dec計 += 1
                End If

                Select Case Value.Split(vbTab)(i).Trim
                    Case "1"
                        dec昼 += 1
                    Case "2"
                        dec夜 += 1
                    Case "3"
                        dec隔 += 1
                    Case "4"
                        dec欠 += 1
                    Case "5"
                        dec有 += 1
                End Select
            Next

            strReturn(0) = Format(dec昼, "#,##0")
            strReturn(1) = Format(dec夜, "#,##0")
            strReturn(2) = Format(dec隔, "#,##0")
            strReturn(3) = Format(dec欠, "#,##0")
            strReturn(4) = Format(dec有, "#,##0")
            strReturn(5) = Format(dec計 - dec欠 - dec有, "#,##0")

            '----------------------------------------------------------------------
            ' 返却
            '----------------------------------------------------------------------
            Return ReturnValue(strReturn)
        End Function
#End Region


#Region "内訳計算"
        '''========================================================================================
        ''' <summary>内訳計算</summary>
        ''' <param name="Value">
        '''  0～(左辺)
        ''' </param>
        ''' <returns>
        '''  0(合計値)
        ''' </returns>
        '''========================================================================================
        Private Shared Function 内訳計算(ByVal Value As String) As String
            Dim strReturn(2) As String
            Dim retVal As Decimal = 0

            '----------------------------------------------------------------------
            ' 返却 配列
            '----------------------------------------------------------------------

            For i As Integer = 0 To Value.Split(vbTab).Length - 1
                If Value.Split(vbTab)(i).Trim <> "" Then
                    retVal += Val(Value.Split(vbTab)(i).Trim.Replace(",", ""))
                End If

            Next

            strReturn(0) = retVal.ToString()
            strReturn(0) = NodeCore.Common.Logic.FormatMoney(retVal.ToString())
            strReturn(1) = retVal.ToString()
            '----------------------------------------------------------------------
            ' 返却
            '----------------------------------------------------------------------
            Return ReturnValue(strReturn)
        End Function
#End Region


#Region "料金計算"
        '''========================================================================================
        ''' <summary>掛算</summary>
        ''' <param name="Value">
        '''  0～(左辺)
        ''' </param>
        ''' <returns>
        '''  0(合計値)
        ''' </returns>
        '''========================================================================================
        Private Shared Function 料金計算(ByVal Value As String) As String
            Dim strReturn(8) As String
            Dim 掛算1 As Decimal = 0
            Dim 掛算2 As Decimal = 0
            Dim 掛算3 As Decimal = 0
            Dim 掛算4 As Decimal = 0

            Dim 足算 As Decimal = 0
            Dim strTemp(2) As String

            '----------------------------------------------------------------------
            ' 返却 配列
            '----------------------------------------------------------------------
            '掛算
            掛算1 = BaseCore.Common.Text.CVal(Value.Split(vbTab)(0)) * BaseCore.Common.Text.CVal(Value.Split(vbTab)(1))
            掛算2 = BaseCore.Common.Text.CVal(Value.Split(vbTab)(2)) * BaseCore.Common.Text.CVal(Value.Split(vbTab)(3))
            掛算3 = BaseCore.Common.Text.CVal(Value.Split(vbTab)(4)) * BaseCore.Common.Text.CVal(Value.Split(vbTab)(5))
            掛算4 = BaseCore.Common.Text.CVal(Value.Split(vbTab)(6)) * BaseCore.Common.Text.CVal(Value.Split(vbTab)(7))

            'For i As Integer = 0 To 1
            '    If Value.Split(vbTab)(i).Trim <> "" Then
            '        掛算 *= BaseCore.Common.Text.CVal(Value.Split(vbTab)(i))
            '    End If
            'Next
            strReturn(0) = NodeCore.Common.Logic.FormatMoney(掛算1.ToString())
            strReturn(1) = NodeCore.Common.Logic.FormatMoney(掛算2.ToString())
            strReturn(2) = NodeCore.Common.Logic.FormatMoney(掛算3.ToString())
            strReturn(3) = NodeCore.Common.Logic.FormatMoney(掛算4.ToString())

            '足算
            For i As Integer = 8 To Value.Split(vbTab).Length - 7
                If Value.Split(vbTab)(i).Trim <> "" Then
                    足算 += BaseCore.Common.Text.CVal(Value.Split(vbTab)(i))
                End If
            Next

            '引算
            For i As Integer = Value.Split(vbTab).Length - 3 To Value.Split(vbTab).Length - 1
                If Value.Split(vbTab)(i).Trim <> "" Then
                    足算 -= BaseCore.Common.Text.CVal(Value.Split(vbTab)(i))
                End If
            Next
            strReturn(4) = NodeCore.Common.Logic.FormatMoney((足算 + 掛算1 + 掛算2 + 掛算3 + 掛算4).ToString())
            strReturn(5) = NodeCore.Common.Logic.FormatMoney((足算 + 掛算1 + 掛算2 + 掛算3 + 掛算4).ToString())

            '営収額計算
            strTemp = 営収額計算((足算 + 掛算1 + 掛算2 + 掛算3 + 掛算4).ToString()).Split(vbTab)
            strReturn(6) = strTemp(0)
            strReturn(7) = strTemp(1)

            '----------------------------------------------------------------------
            ' 返却
            '----------------------------------------------------------------------
            Return ReturnValue(strReturn)
        End Function
#End Region


#Region "料金合計計算"
        '''========================================================================================
        ''' <summary>掛算</summary>
        ''' <param name="Value">
        '''  0～(左辺)
        ''' </param>
        ''' <returns>
        '''  0(合計値)
        ''' </returns>
        '''========================================================================================
        Private Shared Function 料金合計計算(ByVal Value As String) As String
            Dim strReturn(4) As String
            Dim pVal As Decimal = 0
            Dim strTemp(2) As String

            '----------------------------------------------------------------------
            ' 返却 配列
            '----------------------------------------------------------------------

            For i As Integer = 0 To 3
                If Value.Split(vbTab)(i).Trim <> "" Then
                    pVal += BaseCore.Common.Text.CVal(Value.Split(vbTab)(i))
                End If
            Next

            For i As Integer = 4 To Value.Split(vbTab).Length - 1
                If Value.Split(vbTab)(i).Trim <> "" Then
                    pVal -= BaseCore.Common.Text.CVal(Value.Split(vbTab)(i))
                End If
            Next

            strReturn(0) = NodeCore.Common.Logic.FormatMoney(pVal.ToString())
            strReturn(1) = NodeCore.Common.Logic.FormatMoney(pVal.ToString())
            'strReturn(2) = NodeCore.Common.Logic.FormatMoney(pVal.ToString())


            '営収額計算
            strTemp = 営収額計算(pVal.ToString()).Split(vbTab)
            strReturn(2) = strTemp(0)
            strReturn(3) = strTemp(1)

            '----------------------------------------------------------------------
            ' 返却
            '----------------------------------------------------------------------
            Return ReturnValue(strReturn)
        End Function
#End Region


#Region "営収額計算"
        '''========================================================================================
        ''' <summary>営収額計算</summary>
        ''' <param name="Value">
        '''  0～(左辺)
        ''' </param>
        ''' <returns>
        '''  0(合計値)
        ''' </returns>
        '''========================================================================================
        Private Shared Function 営収額計算(ByVal Value As String) As String
            Dim strReturn(2) As String
            Dim pVal As Decimal = 0
            Dim p税率 As Decimal = 10


            '----------------------------------------------------------------------
            ' 税率 取得
            '----------------------------------------------------------------------
            Dim ada As New NodeDatabase.DataSetSystemTableAdapters.SシステムTableAdapter
            Dim tbl As NodeDatabase.DataSetSystem.SシステムDataTable
            Dim qry As New Collection

            ' 読込
            tbl = ada.SelectByCommon(qry)
            If tbl.Count > 0 Then
                p税率 = tbl(0).Item(tbl.税率Column.ColumnName)
            End If

            '税額計算
            pVal = BaseCore.Common.Text.CVal(Value.Split(vbTab)(0))
            Dim taxVal As Decimal = Math.Round((pVal / (p税率 + 100)) * p税率, 0)
            taxVal = Math.Round(taxVal / 10, 0) * 10
            Dim eiVal As Decimal = Math.Floor(pVal - taxVal)


            strReturn(0) = NodeCore.Common.Logic.FormatMoney(eiVal.ToString())
            strReturn(1) = NodeCore.Common.Logic.FormatMoney(taxVal.ToString())

            '----------------------------------------------------------------------
            ' 返却
            '----------------------------------------------------------------------
            Return ReturnValue(strReturn)
        End Function
#End Region

#End Region
    End Class
End Namespace
