﻿Namespace Help
    Public Class Suggest
#Region "ﾚﾝｸﾞｽ"
        '''========================================================================================
        ''' <summary>出現桁数</summary>
        ''' <param name="HelpType">ﾍﾙﾌﾟﾀｲﾌﾟ</param>
        ''' <returns>出現桁数</returns>
        '''========================================================================================
        Public Shared Function Length(ByVal HelpType As NodeUI.Help.Type) As Integer
            Select Case HelpType
                'ﾏｽﾀ
                Case NodeUI.Help.Type.Mユーザー : Return 2

                    'ﾄﾗﾝｻﾞｸｼｮﾝ

                    'その他

                Case Else : Return 0
            End Select
        End Function
#End Region

#Region "実行"
        Public Function Execute(ByVal HelpType As NodeUI.Help.Type, ByVal Context As String, ByVal count As Integer, ByVal Paramater As Array) As String()
            Select Case HelpType
                'ｺﾝｽﾀﾝﾄ
                Case NodeUI.Help.Type.M区分_権限区分 : Return M区分_共通部品(NodeContents.Constant.CodeType.権限区分, count)
                Case NodeUI.Help.Type.M区分_勤務区分 : Return M区分_共通部品(NodeContents.Constant.CodeType.勤務区分, count)
                Case NodeUI.Help.Type.M区分種別 : Return M区分_種別(NodeContents.Constant.CodeType.区分種別, count)
                Case NodeUI.Help.Type.M区分_得意先区分 : Return M区分_共通部品(NodeContents.Constant.CodeType.得意先分類, count)
                Case NodeUI.Help.Type.M区分_事故区分 : Return M区分_共通部品(NodeContents.Constant.CodeType.事故区分, count)
                Case NodeUI.Help.Type.M区分_車種区分 : Return M区分_共通部品(NodeContents.Constant.CodeType.車種区分, count)
                Case NodeUI.Help.Type.M区分_運転免許種類 : Return M区分_共通部品(NodeContents.Constant.CodeType.運転免許種類, count)
                Case NodeUI.Help.Type.Mシフト区分 : Return M区分_共通部品(NodeContents.Constant.CodeType.シフト区分, count)



                    'ｼｽﾃﾑ


                    'ﾏｽﾀ

                    'Case NodeUI.Help.Type.Mユーザー : Return Mユーザー(count, Paramater)
                    'Case NodeUI.Help.Type.Mシフト区分 : Return Mシフト区分(count)
                    'Case NodeUI.Help.Type.Mグループ : Return Mグループ(count, Paramater)
                    'Case NodeUI.Help.Type.M個社 : Return M個社(count, Paramater)

                    'ﾄﾗﾝｻﾞｸｼｮﾝ
                    'Case NodeUI.Help.Type.Tチケット : Return Tチケット(count, Paramater)


                    'その他
                Case Else : Return Nothing
            End Select
        End Function
#End Region

#Region "ｺﾝｽﾀﾝﾄ"
#Region "共通処理"
        '''========================================================================================
        ''' <summary>共通処理</summary>
        '''========================================================================================
        Private Function M区分_共通部品(ByVal TypeClass As String, ByVal count As Integer) As String()
            Dim ada As New NodeDatabase.DataSetMasterTableAdapters.M区分TableAdapter
            Dim tbl As New NodeDatabase.DataSetMaster.M区分DataTable
            Dim qry As New Collection

            Dim list As New List(Of String)

            '----------------------------------------------------------------------
            ' 即終了
            '----------------------------------------------------------------------
            If count = 0 Then
                Return list.ToArray
            End If

            '----------------------------------------------------------------------
            ' 条件設定
            '----------------------------------------------------------------------
            qry.Clear()
            'qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, , BaseDatabase.Contents.Compare.Equal))
            'qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, TypeClass, BaseDatabase.Contents.Compare.Equal))
            'qry.Add(New BaseDatabase.Condition(tbl.子会社CDColumn.ColumnName, TypeClass, BaseDatabase.Contents.Compare.Equal))
            qry.Add(New BaseDatabase.Condition(tbl.区分種別Column.ColumnName, TypeClass, BaseDatabase.Contents.Compare.Equal))

            '----------------------------------------------------------------------
            ' 読込
            '----------------------------------------------------------------------
            tbl = ada.SelectByCommon(qry, tbl.区分IDColumn.ColumnName, count)

            '----------------------------------------------------------------------
            ' 編集
            '----------------------------------------------------------------------
            For Each datRow As DataRow In tbl.Rows
                list.Add(datRow.Item(tbl.区分IDColumn.ColumnName) & vbTab & datRow.Item(tbl.区分IDColumn.ColumnName) & ":" & datRow.Item(tbl.区分名Column.ColumnName))
            Next

            '----------------------------------------------------------------------
            ' 返却
            '----------------------------------------------------------------------
            Return list.ToArray
        End Function

        '''========================================================================================
        ''' <summary>共通処理</summary>
        '''========================================================================================
        Private Function M区分_種別(ByVal TypeClass As String, ByVal count As Integer) As String()
            Dim ada As New NodeDatabase.DataSetMasterTableAdapters.M区分TableAdapter
            Dim tbl As New NodeDatabase.DataSetMaster.M区分DataTable
            Dim qry As New Collection

            Dim list As New List(Of String)

            '----------------------------------------------------------------------
            ' 即終了
            '----------------------------------------------------------------------
            If count = 0 Then
                Return list.ToArray
            End If

            '----------------------------------------------------------------------
            ' 条件設定
            '----------------------------------------------------------------------
            qry.Clear()
            'qry.Add(New BaseDatabase.Condition(tbl.区分種別Column.ColumnName, TypeClass, BaseDatabase.Contents.Compare.Equal))

            '----------------------------------------------------------------------
            ' 読込
            '----------------------------------------------------------------------
            tbl = ada.SelectByCommon(qry, tbl.区分IDColumn.ColumnName, count)

            '----------------------------------------------------------------------
            ' 編集
            '----------------------------------------------------------------------
            For Each datRow As DataRow In tbl.Rows
                If list.IndexOf(datRow.Item(tbl.区分種別Column.ColumnName)) > -1 Then
                    Continue For
                End If
                list.Add(datRow.Item(tbl.区分種別Column.ColumnName))
            Next

            '----------------------------------------------------------------------
            ' 返却
            '----------------------------------------------------------------------
            Return list.ToArray
        End Function
#End Region
#End Region

#Region "ｼｽﾃﾑ"

#End Region

#Region "ﾏｽﾀ"


#Region "Mグループ"
        '''========================================================================================
        ''' <summary>Mユーザー</summary>
        ''' <param name="Paramater">
        '''  0(ユーザーCD)
        ''' </param>
        '''========================================================================================
        Private Function Mグループ(ByVal count As Integer, ByVal Paramater As Array) As String()
            Dim ada As New NodeDatabase.DataSetMasterTableAdapters.MグループTableAdapter
            Dim tbl As New NodeDatabase.DataSetMaster.MグループDataTable
            Dim qry As New Collection

            Dim list As New List(Of String)

            '----------------------------------------------------------------------
            ' 即終了
            '----------------------------------------------------------------------
            If count = 0 Then
                Return list.ToArray
            End If

            '----------------------------------------------------------------------
            ' 事前準備
            '----------------------------------------------------------------------
            Dim strCode As String = BaseCore.Common.Text.Nz(Paramater(0), "")

            '----------------------------------------------------------------------
            ' 条件設定
            '----------------------------------------------------------------------
            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, strCode, BaseDatabase.Contents.Compare.LikeFront))

            '----------------------------------------------------------------------
            ' 読込
            '----------------------------------------------------------------------
            tbl = ada.SelectByCommon(qry, tbl.グループCDColumn.ColumnName, count)

            '----------------------------------------------------------------------
            ' 編集
            '----------------------------------------------------------------------
            Dim intCount As Integer = 0
            For Each datRow As DataRow In tbl.Rows
                intCount += 1
                If intCount > count Then
                    Exit For
                End If

                list.Add(datRow.Item(tbl.グループCDColumn.ColumnName) & vbTab & datRow.Item(tbl.グループCDColumn.ColumnName) & ":" & datRow.Item(tbl.グループ名Column.ColumnName))
            Next

            '----------------------------------------------------------------------
            ' 返却
            '----------------------------------------------------------------------
            Return list.ToArray
        End Function
#End Region

#Region "M個社"
        '''========================================================================================
        ''' <summary>Mユーザー</summary>
        ''' <param name="Paramater">
        '''  0(ユーザーCD)
        ''' </param>
        '''========================================================================================
        Private Function M個社(ByVal count As Integer, ByVal Paramater As Array) As String()
            Dim ada As New NodeDatabase.DataSetMasterTableAdapters.M個社TableAdapter
            Dim tbl As New NodeDatabase.DataSetMaster.M個社DataTable
            Dim qry As New Collection

            Dim list As New List(Of String)

            '----------------------------------------------------------------------
            ' 即終了
            '----------------------------------------------------------------------
            If count = 0 Then
                Return list.ToArray
            End If

            '----------------------------------------------------------------------
            ' 事前準備
            '----------------------------------------------------------------------
            Dim strCode As String = BaseCore.Common.Text.Nz(Paramater(0), "")
            'TODO
            'Dim strCode1 As String = BaseCore.Common.Text.Nz(Paramater(1), "")

            '----------------------------------------------------------------------
            ' 条件設定
            '----------------------------------------------------------------------
            qry.Clear()
            'qry.Add(New BaseDatabase.Condition(tbl.グループCDColumn.ColumnName, strCode, BaseDatabase.Contents.Compare.LikeFront))
            'qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, strCode1, BaseDatabase.Contents.Compare.LikeFront))
            qry.Add(New BaseDatabase.Condition(tbl.個社CDColumn.ColumnName, strCode, BaseDatabase.Contents.Compare.LikeFront))

            '----------------------------------------------------------------------
            ' 読込
            '----------------------------------------------------------------------
            tbl = ada.SelectByCommon(qry, tbl.個社CDColumn.ColumnName, count)

            '----------------------------------------------------------------------
            ' 編集
            '----------------------------------------------------------------------
            Dim intCount As Integer = 0
            For Each datRow As DataRow In tbl.Rows
                intCount += 1
                If intCount > count Then
                    Exit For
                End If

                list.Add(datRow.Item(tbl.個社CDColumn.ColumnName) & vbTab & datRow.Item(tbl.個社CDColumn.ColumnName) & ":" & datRow.Item(tbl.個社名Column.ColumnName))
            Next

            '----------------------------------------------------------------------
            ' 返却
            '----------------------------------------------------------------------
            Return list.ToArray
        End Function
#End Region

#Region "Mユーザー"
        '''========================================================================================
        ''' <summary>Mユーザー</summary>
        ''' <param name="Paramater">
        '''  0(ユーザーCD)
        ''' </param>
        '''========================================================================================
        Private Function Mユーザー(ByVal count As Integer, ByVal Paramater As Array) As String()
            Dim ada As New NodeDatabase.DataSetMasterTableAdapters.MユーザーTableAdapter
            Dim tbl As New NodeDatabase.DataSetMaster.MユーザーDataTable
            Dim qry As New Collection

            Dim list As New List(Of String)

            '----------------------------------------------------------------------
            ' 即終了
            '----------------------------------------------------------------------
            If count = 0 Then
                Return list.ToArray
            End If

            '----------------------------------------------------------------------
            ' 事前準備
            '----------------------------------------------------------------------
            Dim strCode As String = BaseCore.Common.Text.Nz(Paramater(0), "")

            '----------------------------------------------------------------------
            ' 条件設定
            '----------------------------------------------------------------------
            qry.Clear()
            'TODO me.security. companyCD
            'qry.Add(New BaseDatabase.Condition(tbl.子会社CDColumn.ColumnName, strCode, Me.Security.))
            qry.Add(New BaseDatabase.Condition(tbl.ユーザーCDColumn.ColumnName, strCode, BaseDatabase.Contents.Compare.LikeFront))

            '----------------------------------------------------------------------
            ' 読込
            '----------------------------------------------------------------------
            tbl = ada.SelectByCommon(qry, tbl.ユーザーCDColumn.ColumnName, count)

            '----------------------------------------------------------------------
            ' 編集
            '----------------------------------------------------------------------
            Dim intCount As Integer = 0
            For Each datRow As DataRow In tbl.Rows
                intCount += 1
                If intCount > count Then
                    Exit For
                End If

                list.Add(datRow.Item(tbl.ユーザーCDColumn.ColumnName) & vbTab & datRow.Item(tbl.ユーザーCDColumn.ColumnName) & ":" & datRow.Item(tbl.ユーザー名Column.ColumnName))
            Next

            '----------------------------------------------------------------------
            ' 返却
            '----------------------------------------------------------------------
            Return list.ToArray
        End Function
#End Region

#Region "M乗務員"
        '''========================================================================================
        ''' <summary>Mユーザー</summary>
        ''' <param name="Paramater">
        '''  0(ユーザーCD)
        ''' </param>
        '''========================================================================================
        Private Function M乗務員(ByVal count As Integer, ByVal Paramater As Array) As String()
            Dim ada As New NodeDatabase.DataSetMasterTableAdapters.M乗務員TableAdapter
            Dim tbl As New NodeDatabase.DataSetMaster.M乗務員DataTable
            Dim qry As New Collection

            Dim list As New List(Of String)

            '----------------------------------------------------------------------
            ' 即終了
            '----------------------------------------------------------------------
            If count = 0 Then
                Return list.ToArray
            End If

            '----------------------------------------------------------------------
            ' 事前準備
            '----------------------------------------------------------------------
            Dim strCode As String = BaseCore.Common.Text.Nz(Paramater(0), "")

            '----------------------------------------------------------------------
            ' 条件設定
            '----------------------------------------------------------------------
            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl.乗務員CDColumn.ColumnName, strCode, BaseDatabase.Contents.Compare.LikeFront))

            '----------------------------------------------------------------------
            ' 読込
            '----------------------------------------------------------------------
            tbl = ada.SelectByCommon(qry, tbl.乗務員CDColumn.ColumnName, count)

            '----------------------------------------------------------------------
            ' 編集
            '----------------------------------------------------------------------
            Dim intCount As Integer = 0
            For Each datRow As DataRow In tbl.Rows
                intCount += 1
                If intCount > count Then
                    Exit For
                End If

                list.Add(datRow.Item(tbl.乗務員CDColumn.ColumnName) & vbTab & datRow.Item(tbl.乗務員CDColumn.ColumnName) & ":" & datRow.Item(tbl.乗務員名Column.ColumnName))
            Next

            '----------------------------------------------------------------------
            ' 返却
            '----------------------------------------------------------------------
            Return list.ToArray
        End Function
#End Region

#Region "Mシフト区分"
        '''========================================================================================
        ''' <summary>Mシフト区分</summary>
        '''========================================================================================
        Private Function Mシフト区分(ByVal count As Integer) As String()
            Dim ada As New NodeDatabase.DataSetMasterTableAdapters.Mシフト区分TableAdapter
            Dim tbl As New NodeDatabase.DataSetMaster.Mシフト区分DataTable
            Dim qry As New Collection

            Dim list As New List(Of String)

            '----------------------------------------------------------------------
            ' 即終了
            '----------------------------------------------------------------------
            If count = 0 Then
                Return list.ToArray
            End If

            '----------------------------------------------------------------------
            ' 条件設定
            '----------------------------------------------------------------------
            qry.Clear()

            '----------------------------------------------------------------------
            ' 読込
            '----------------------------------------------------------------------
            tbl = ada.SelectByCommon(qry, tbl.シフト区分Column.ColumnName, count)

            '----------------------------------------------------------------------
            ' 編集
            '----------------------------------------------------------------------
            For Each datRow As DataRow In tbl.Rows
                list.Add(datRow.Item(tbl.シフト区分Column.ColumnName) & vbTab & datRow.Item(tbl.シフト区分Column.ColumnName) & ":" & datRow.Item(tbl.シフト名Column.ColumnName))
            Next

            '----------------------------------------------------------------------
            ' 返却
            '----------------------------------------------------------------------
            Return list.ToArray
        End Function
#End Region

#End Region

#Region "ﾄﾗﾝｻﾞｸｼｮﾝ"

#Region "Tチケット"
        '''========================================================================================
        ''' <summary>Tチケット</summary>
        ''' <param name="Paramater">
        '''  0(ユーザーCD)
        ''' </param>
        '''========================================================================================
        Private Function Tチケット(ByVal count As Integer, ByVal Paramater As Array) As String()
            Dim ada As New NodeDatabase.DataSetTranTableAdapters.TチケットTableAdapter
            Dim tbl As New NodeDatabase.DataSetTran.TチケットDataTable
            Dim qry As New Collection

            Dim list As New List(Of String)

            '----------------------------------------------------------------------
            ' 即終了
            '----------------------------------------------------------------------
            If count = 0 Then
                Return list.ToArray
            End If

            '----------------------------------------------------------------------
            ' 事前準備
            '----------------------------------------------------------------------
            Dim strCode As String = BaseCore.Common.Text.Nz(Paramater(0), "")

            '----------------------------------------------------------------------
            ' 条件設定
            '----------------------------------------------------------------------
            qry.Clear()
            qry.Add(New BaseDatabase.Condition(tbl.IDColumn.ColumnName, strCode, BaseDatabase.Contents.Compare.LikeFront))

            '----------------------------------------------------------------------
            ' 読込
            '----------------------------------------------------------------------
            tbl = ada.SelectByCommon(qry, tbl.IDColumn.ColumnName, count)

            '----------------------------------------------------------------------
            ' 編集
            '----------------------------------------------------------------------
            Dim intCount As Integer = 0
            For Each datRow As DataRow In tbl.Rows
                intCount += 1
                If intCount > count Then
                    Exit For
                End If

                list.Add(datRow.Item(tbl.IDColumn.ColumnName) & vbTab & datRow.Item(tbl.IDColumn.ColumnName) & ":" & datRow.Item(tbl.IDColumn.ColumnName))
            Next

            '----------------------------------------------------------------------
            ' 返却
            '----------------------------------------------------------------------
            Return list.ToArray
        End Function
#End Region

#End Region

#Region "その他"
        '#Region "X売上其他_箱"
        '        '''========================================================================================
        '        ''' <summary>売上其他_箱</summary>
        '        '''========================================================================================
        '        Private Function X売上其他_箱(ByVal count As Integer, ByVal Paramater As Array) As String()
        '            Dim ada As New NodeDatabase.DataSetFuncTableAdapters.F0売上其他表示TableAdapter
        '            Dim tbl As New NodeDatabase.DataSetFunc.F0売上其他表示DataTable
        '            Dim qry As New Collection

        '            Dim list As New List(Of String)

        '            '----------------------------------------------------------------------
        '            ' 即終了
        '            '----------------------------------------------------------------------
        '            If count = 0 Then
        '                Return list.ToArray
        '            End If

        '            '----------------------------------------------------------------------
        '            ' 事前準備
        '            '----------------------------------------------------------------------
        '            Dim strTransPortCode As String = NodeCore.Common.Logic.CodeOuterToInner.Customer(BaseCore.Common.Text.Nz(Paramater(0), ""))
        '            Dim strSlipType As String = BaseCore.Common.Text.Nz(Paramater(1), "")
        '            Dim strBoxtype As String = BaseCore.Common.Text.Nz(Paramater(2), "")

        '            '----------------------------------------------------------------------
        '            ' 条件設定
        '            '----------------------------------------------------------------------
        '            qry.Clear()
        '            qry.Add(New BaseDatabase.Condition(NodeDatabase.DataSetFuncTableAdapters.F0売上其他表示TableAdapter.Contents.荷主内部CD, strTransPortCode, BaseDatabase.Contents.Compare.Parameter))
        '            qry.Add(New BaseDatabase.Condition(NodeDatabase.DataSetFuncTableAdapters.F0売上其他表示TableAdapter.Contents.伝票区分, strSlipType, BaseDatabase.Contents.Compare.Parameter))
        '            qry.Add(New BaseDatabase.Condition(tbl.寄託箱種別Column.ColumnName, strBoxtype, BaseDatabase.Contents.Compare.LikeFront, BaseDatabase.Contents.EmptyHandle.Skip))

        '            '----------------------------------------------------------------------
        '            ' 読込
        '            '----------------------------------------------------------------------
        '            tbl = ada.SelectByCommon(qry, tbl.寄託箱種別Column.ColumnName, count)

        '            '----------------------------------------------------------------------
        '            ' 編集
        '            '----------------------------------------------------------------------
        '            Dim intCount As Integer = 0
        '            For Each datRow As DataRow In tbl.Rows
        '                intCount += 1
        '                If intCount > count Then
        '                    Exit For
        '                End If

        '                list.Add(datRow.Item(tbl.寄託箱種別Column.ColumnName) & vbTab & datRow.Item(tbl.寄託箱種別Column.ColumnName))
        '            Next

        '            '----------------------------------------------------------------------
        '            ' 返却
        '            '----------------------------------------------------------------------
        '            Return list.ToArray
        '        End Function
        '#End Region

#Region "X休日_年"
        '''========================================================================================
        ''' <summary>X休日_年</summary>
        '''========================================================================================
        Private Function X休日_年(ByVal count As Integer, ByVal Paramater As Array) As String()
            Dim list As New List(Of String)
            '----------------------------------------------------------------------
            ' 事前準備
            '----------------------------------------------------------------------
            Dim 基準年 As String = Format(Now, "yyyy")

            '----------------------------------------------------------------------
            ' 編集
            '----------------------------------------------------------------------
            For i As Integer = 基準年 - 1 To 基準年 + 10
                list.Add(i & vbTab & i)
            Next

            '----------------------------------------------------------------------
            ' 返却
            '----------------------------------------------------------------------
            Return list.ToArray
        End Function
#End Region

#Region "X休日_月"
        '''========================================================================================
        ''' <summary>X休日_月</summary>
        '''========================================================================================
        Private Function X休日_月(ByVal count As Integer, ByVal Paramater As Array) As String()
            Dim list As New List(Of String)
            '----------------------------------------------------------------------
            ' 編集
            '----------------------------------------------------------------------
            list.Add("01" & vbTab & "01")
            list.Add("02" & vbTab & "02")
            list.Add("03" & vbTab & "03")
            list.Add("04" & vbTab & "04")
            list.Add("05" & vbTab & "05")
            list.Add("06" & vbTab & "06")
            list.Add("07" & vbTab & "07")
            list.Add("08" & vbTab & "08")
            list.Add("09" & vbTab & "09")
            list.Add("10" & vbTab & "10")
            list.Add("11" & vbTab & "11")
            list.Add("12" & vbTab & "12")

            '----------------------------------------------------------------------
            ' 返却
            '----------------------------------------------------------------------
            Return list.ToArray
        End Function
#End Region

#Region "X休日_曜日"
        '''========================================================================================
        ''' <summary>X休日_曜日</summary>
        '''========================================================================================
        Private Function X休日_曜日(ByVal count As Integer, ByVal Paramater As Array) As String()
            Dim list As New List(Of String)
            '----------------------------------------------------------------------
            ' 編集
            '----------------------------------------------------------------------
            list.Add("日" & vbTab & "日")
            list.Add("月" & vbTab & "月")
            list.Add("火" & vbTab & "火")
            list.Add("水" & vbTab & "水")
            list.Add("木" & vbTab & "木")
            list.Add("金" & vbTab & "金")
            list.Add("土" & vbTab & "土")

            '----------------------------------------------------------------------
            ' 返却
            '----------------------------------------------------------------------
            Return list.ToArray
        End Function
#End Region
#End Region
    End Class
End Namespace
