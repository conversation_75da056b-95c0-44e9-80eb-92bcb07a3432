﻿Namespace Constant
    '''----------------------------------------------------------------------------------------
    ''' <summary>System</summary>
    '''----------------------------------------------------------------------------------------
    Public Class System
        Public Const APP_COPYRIGHT = "Copyright &copy;2023-@Year Advocaat Co.,Ltd. All rights reserved."

        Public Const STRONG_USERID = "admin"                      ' 管理者ID
        Public Const STRONG_PASSWD = "signadmin"                  ' 管理者パスワード

        Public Const LOG_RECORD = True                            ' システムログを記録するかどうか
        Public Const QTY_LENGTH = 6                               ' 数量項目の桁数

        Public Const HomeURL As String = "~/PageMain/other/index.aspx"
        Public Const MainDir As String = "~/PageMain/"
        Public Const ProjectID As String = "wmsinner"

        Public Const WIDTH_QTY = 100                              ' 数値の項目幅
        Public Const WIDTH_ITEMNAME = 350                         ' 商品名の項目幅
        Public Const EXPRESS_CNT = 700                            ' 送り状CSVの行数

        Public Const DATE_TIME_FORMAT = "yyyy/MM/dd HH:mm:ss"     ' 登録日持、更新日時をDBへ書き込み書式
    End Class

    '''----------------------------------------------------------------------------------------
    ''' <summary>ページID</summary>
    '''----------------------------------------------------------------------------------------
    Public Class ページID
        Public Const 障害報告 As String = "Trouble.aspx"
        Public Const 担当割入力 As String = "AssignInput.aspx"
    End Class

    '''----------------------------------------------------------------------------------------
    ''' <summary>検索条件</summary>
    '''----------------------------------------------------------------------------------------
    Public Class SessionKey
        Public Const 契約CD As String = "AGREE_CD"
        Public Const ログインID As String = "LOGIN_ID"
        Public Const ログインPW As String = "LOGIN_PASSWD"
        Public Const ロック日付 As String = "DateLock"

        '「Parts/GridView.ascx」で使用
        Public Const GridView0 As String = "GridView0"
        Public Const GridView1 As String = "GridView1"
        Public Const GridView2 As String = "GridView2"
        Public Const GridView3 As String = "GridView3"
        Public Const GridView4 As String = "GridView4"
        Public Const GridView5 As String = "GridView5"
        Public Const GridView6 As String = "GridView6"
        Public Const GridView7 As String = "GridView7"
        Public Const GridView8 As String = "GridView8"
        Public Const GridView9 As String = "GridView9"

        '「Parts/GridInput.ascx」で使用
        Public Const GridInput1 As String = "GridInput1"
        Public Const GridInput2 As String = "GridInput2"
        Public Const GridInput3 As String = "GridInput3"
        Public Const GridInput4 As String = "GridInput4"
        Public Const GridInput5 As String = "GridInput5"
        Public Const GridInput6 As String = "GridInput6"
    End Class


    '''----------------------------------------------------------------------------------------
    ''' <summary>区分種別</summary>
    '''----------------------------------------------------------------------------------------
    Public Class CodeType
        Public Const 権限区分 As String = "権限区分"
        Public Const 勤務区分 As String = "勤務区分"
        Public Const 区分種別 As String = "区分種別"
        Public Const 得意先分類 As String = "得意先分類"
        Public Const 符号区分 As String = "符号区分"
        Public Const 得意先区分 As String = "得意先分類"
        Public Const 事故区分 As String = "事故区分"
        Public Const 車種区分 As String = "車種区分"
        Public Const 運転免許種類 As String = "運転免許種類"
        Public Const シフト区分 As String = "シフト区分"



    End Class

    '''----------------------------------------------------------------------------------------
    ''' <summary>区分ｺｰﾄﾞ</summary>
    '''----------------------------------------------------------------------------------------
    Public Class CodeValue
        '''----------------------------------------------------------------------------------------
        ''' <summary>フラグ</summary>
        '''----------------------------------------------------------------------------------------
        Public Class フラグID
            Public Const オフ As String = "0"
            Public Const オン As String = "1"
        End Class

        '''----------------------------------------------------------------------------------------
        ''' <summary>休日区分</summary>
        '''----------------------------------------------------------------------------------------
        Public Class 休日区分ID
            Public Const 公休日 As String = "1"
            Public Const 有給充当日 As String = "2"
        End Class

        '''----------------------------------------------------------------------------------------
        ''' <summary>権限区分</summary>
        '''----------------------------------------------------------------------------------------
        Public Class 権限区分ID
            Public Const 役員 As String = "1"
            Public Const システム As String = "2"
            Public Const 営業 As String = "3"
            Public Const 事務 As String = "4"
            Public Const 管理職 As String = "8"
            Public Const その他 As String = "9"
        End Class

        '''----------------------------------------------------------------------------------------
        ''' <summary>見積計算区分</summary>
        '''----------------------------------------------------------------------------------------
        Public Class 見積計算区分ID
            Public Const 比率計算 As String = "1"
            Public Const 工数積算 As String = "2"
        End Class

        '''----------------------------------------------------------------------------------------
        ''' <summary>見積工程区分</summary>
        '''----------------------------------------------------------------------------------------
        Public Class 見積工程区分ID
            Public Const 基本設計 As String = "1"
            Public Const 詳細設計 As String = "2"
            Public Const 総合テスト As String = "4"
            Public Const マニュアル As String = "5"
        End Class


        '''----------------------------------------------------------------------------------------
        ''' <summary>符号区分</summary>
        '''----------------------------------------------------------------------------------------
        Public Class 符号区分
            Public Const 等しい As String = "1"
            Public Const 等しくない As String = "2"
            Public Const 大きい As String = "3"
            Public Const 小さい As String = "4"
            Public Const 以上 As String = "5"
            Public Const 以下 As String = "6"
        End Class

        '''----------------------------------------------------------------------------------------
        ''' <summary>見積状況区分</summary>
        '''----------------------------------------------------------------------------------------
        Public Class 見積状況区分ID
            Public Const 見積提出 As String = "0"
            Public Const 失注 As String = "1"
            Public Const 受注 As String = "2"
            Public Const 検収中 As String = "3"
            Public Const 納品完了 As String = "4"
        End Class

        '''----------------------------------------------------------------------------------------
        ''' <summary>見積分類区分</summary>
        '''----------------------------------------------------------------------------------------
        Public Class 見積分類区分ID
            Public Const 通常見積 As String = "1"
            Public Const 保守見積 As String = "2"
            Public Const その他見積 As String = "3"
        End Class

        ''''----------------------------------------------------------------------------------------
        '''' <summary>作業工程区分</summary>
        ''''----------------------------------------------------------------------------------------
        'Public Class 作業工程区分ID
        '    Public Const 営業 As String = "1"
        '    Public Const 開発 As String = "2"
        '    Public Const 保守内 As String = "3"
        '    Public Const 保守外 As String = "4"
        '    Public Const 会社 As String = "5"
        'End Class

        '''----------------------------------------------------------------------------------------
        ''' <summary>職種区分</summary>
        '''----------------------------------------------------------------------------------------
        Public Class 職種区分ID
            Public Const 営業 As String = "1"
            Public Const 開発 As String = "2"
            Public Const 保守内 As String = "3"
            Public Const 保守外 As String = "4"
            Public Const 会社 As String = "5"
        End Class
        '''----------------------------------------------------------------------------------------
        ''' <summary>調達区分</summary>
        '''----------------------------------------------------------------------------------------
        Public Class 調達区分ID
            Public Const 内製 As String = "1"
            Public Const 外製 As String = "2"
        End Class


        '''----------------------------------------------------------------------------------------
        ''' <summary>売上経路区分</summary>
        '''----------------------------------------------------------------------------------------
        Public Class 売上経路区分ID
            Public Const 一括 As String = "1"
            Public Const 個別 As String = "2"
        End Class

        '''----------------------------------------------------------------------------------------
        ''' <summary>単位区分</summary>
        '''----------------------------------------------------------------------------------------
        Public Class 単位区分ID
            Public Const 式 As String = "1"
            Public Const 人日 As String = "2"
            Public Const 個 As String = "3"
        End Class

        '''----------------------------------------------------------------------------------------
        ''' <summary>端数区分</summary>
        '''----------------------------------------------------------------------------------------
        Public Class 端数区分ID
            Public Const 四捨五入 As String = "1"
            Public Const 切り上げ As String = "2"
            Public Const 切り捨て As String = "3"
        End Class

        '''----------------------------------------------------------------------------------------
        ''' <summary>障害原因区分</summary>
        '''----------------------------------------------------------------------------------------
        Public Class 障害原因区分ID
            Public Const None As String = "0"
            Public Const バグ As String = "1"
            Public Const 仕様漏れ As String = "2"
            Public Const 投稿勘違い As String = "3"
        End Class

        '''----------------------------------------------------------------------------------------
        ''' <summary>障害状況区分</summary>
        '''----------------------------------------------------------------------------------------
        Public Class 障害状況区分ID
            Public Const 投稿 As String = "1"
            Public Const 確認中 As String = "2"
            Public Const 対応完了 As String = "3"
        End Class

        '''----------------------------------------------------------------------------------------
        ''' <summary>サポート区分</summary>
        '''----------------------------------------------------------------------------------------
        Public Class サポート区分ID
            Public Const None As String = "0"
            Public Const バグ As String = "1"
            Public Const 保守_問い合わせ As String = "2"
            Public Const 保守_作業 As String = "3"
            Public Const その他 As String = "9"
        End Class
        '''----------------------------------------------------------------------------------------
        ''' <summary>対応状況区分</summary>
        '''----------------------------------------------------------------------------------------
        Public Class 対応状況区分ID
            Public Const 入力待ち As String = "0"
            Public Const 対応中 As String = "1"
            Public Const 完了 As String = "2"
            Public Const 返答待ち As String = "3"
        End Class

        '''----------------------------------------------------------------------------------------
        ''' <summary>取引状況区分</summary>
        '''----------------------------------------------------------------------------------------
        Public Class 取引状況区分ID
            Public Const 正規顧客 As String = "1"
            Public Const 取引終了 As String = "2"
            Public Const 見積提出 As String = "3"
        End Class

        '''----------------------------------------------------------------------------------------
        ''' <summary>移動分類区分</summary>
        '''----------------------------------------------------------------------------------------
        Public Class 移動分類区分
            Public Const 交通費 As String = "1"
            Public Const 通勤費 As String = "2"
        End Class

        '''----------------------------------------------------------------------------------------
        ''' <summary>検索表示分類区分</summary>
        '''----------------------------------------------------------------------------------------
        Public Class 検索表示分類区分
            Public Const テキストボックス単体 As String = "1"
            Public Const テキストボックス範囲 As String = "2"
            Public Const オプションボタン As String = "3"
            Public Const ドロップダウンリスト As String = "4"
        End Class
        '''----------------------------------------------------------------------------------------
        ''' <summary>サポート用件区分</summary>
        '''----------------------------------------------------------------------------------------
        Public Class サポート用件区分ID
            Public Const None As String = "0"
            Public Const 問い合わせ As String = "1"
            Public Const 障害報告 As String = "2"
            Public Const バグ As String = "3"
            Public Const 新規案件 As String = "4"

        End Class
        '''----------------------------------------------------------------------------------------
        ''' <summary>サポート対応状況区分</summary>
        '''----------------------------------------------------------------------------------------
        Public Class サポート対応状況区分ID
            Public Const None As String = "0"
            Public Const 記入待ち As String = "1"
            Public Const 対応中 As String = "2"
            Public Const 完了 As String = "3"
        End Class

        '''----------------------------------------------------------------------------------------
        ''' <summary>ファイル形式</summary>
        '''----------------------------------------------------------------------------------------
        Public Class ファイル形式
            Public Const Excel As String = "1"
            Public Const Csv As String = "2"
            Public Const Text As String = "3"
        End Class
    End Class

    '''----------------------------------------------------------------------------------------
    ''' <summary>ﾛｼﾞｯｸ</summary>
    '''----------------------------------------------------------------------------------------
    Public Class Logic
        ' '''----------------------------------------------------------------------------------------
        ' ''' <summary>共通</summary>
        ' '''----------------------------------------------------------------------------------------
        'Public Class Common

        'End Class

        '''----------------------------------------------------------------------------------------
        ''' <summary>見積入力</summary>
        '''----------------------------------------------------------------------------------------
        Public Class EstimateInput
            '''----------------------------------------------------------------------------------------
            ''' <summary>処理区分</summary>
            '''----------------------------------------------------------------------------------------
            Public Class テキスト
                Public Const 小計 As String = "小　　計"
                Public Const 余白 As String = "－ 以下余白 －"
            End Class

            Public Class 単位区分名
                Public Const 式 As String = "式"
            End Class
        End Class

        '''----------------------------------------------------------------------------------------
        ''' <summary>乗務員</summary>
        '''----------------------------------------------------------------------------------------
        Public Class Crew
            '''----------------------------------------------------------------------------------------
            ''' <summary>様式選択</summary>
            '''----------------------------------------------------------------------------------------
            Public Class 様式選択
                Public Const 雇用条件及び雇用契約証明書 As String = "1"
                Public Const 第2号様式 As String = "2"
                Public Const 第4号様式 As String = "3"
                Public Const 第9号様式 As String = "4"
                Public Const 第10号様式 As String = "5"
            End Class
        End Class

        '''----------------------------------------------------------------------------------------
        ''' <summary>納品完了入力</summary>
        '''----------------------------------------------------------------------------------------
        Public Class ProjectComplete
            '''----------------------------------------------------------------------------------------
            ''' <summary>処理区分</summary>
            '''----------------------------------------------------------------------------------------
            Public Class 処理区分
                Public Const 完了 As String = "1"
                Public Const 戻し As String = "2"
            End Class
        End Class

        '''----------------------------------------------------------------------------------------
        ''' <summary>売上入力</summary>
        '''----------------------------------------------------------------------------------------
        Public Class SellInput
            '''----------------------------------------------------------------------------------------
            ''' <summary>処理区分</summary>
            '''----------------------------------------------------------------------------------------
            Public Class 処理区分
                Public Const 作成 As String = "1"
                Public Const 訂正 As String = "2"
            End Class
        End Class

        '''----------------------------------------------------------------------------------------
        ''' <summary>売上一括作成</summary>
        '''----------------------------------------------------------------------------------------
        Public Class SellMake
            '''----------------------------------------------------------------------------------------
            ''' <summary>処理区分</summary>
            '''----------------------------------------------------------------------------------------
            Public Class 処理区分
                Public Const 作成 As String = "1"
                Public Const 削除 As String = "2"
            End Class
        End Class

        '''----------------------------------------------------------------------------------------
        ''' <summary>売上帳票</summary>
        '''----------------------------------------------------------------------------------------
        Public Class SellMonthReport
            '''----------------------------------------------------------------------------------------
            ''' <summary>帳票区分</summary>
            '''----------------------------------------------------------------------------------------
            Public Class 帳票区分
                Public Const 一覧表 As String = "1"
                Public Const 明細表 As String = "2"
            End Class
        End Class

        '''----------------------------------------------------------------------------------------
        ''' <summary>休日情報</summary>
        '''----------------------------------------------------------------------------------------
        Public Class Holiday
            '''----------------------------------------------------------------------------------------
            ''' <summary>曜日区分</summary>
            '''----------------------------------------------------------------------------------------
            Public Class 曜日区分
                Public Const 日 As String = "日"
                Public Const 月 As String = "月"
                Public Const 火 As String = "火"
                Public Const 水 As String = "水"
                Public Const 木 As String = "木"
                Public Const 金 As String = "金"
                Public Const 土 As String = "土"
            End Class
        End Class

        '''----------------------------------------------------------------------------------------
        ''' <summary>製造工程CD</summary>
        '''----------------------------------------------------------------------------------------
        Public Class 製造工程CD
            Public Const 基本設計 As String = "01"
            Public Const 詳細設計 As String = "02"
            Public Const ﾌﾟﾛｸﾞﾗﾑ製造 As String = "03"
            Public Const 総合ﾃｽﾄ As String = "04"
            Public Const ﾏﾆｭｱﾙ作成 As String = "05"
        End Class

        '''----------------------------------------------------------------------------------------
        ''' <summary>週所定労働日数</summary>
        '''----------------------------------------------------------------------------------------
        Public Class Weekly
            Public Const _1日 As String = "1"
            Public Const _2日 As String = "2"
            Public Const _3日 As String = "3"
            Public Const _4日 As String = "4"
            Public Const _5日 As String = "5"
        End Class
    End Class
End Namespace
