select グループCD
	　,個社CD
	  ,子会社CD
      , 車両番号
      , [日報日付]
      ,[出庫時刻]
      ,[入庫時刻]
      , 累計全走行
	  , ISNULL((SELECT TOP (1) 累計全走行 FROM T乗務記録HD WHERE A.車両番号 = 車両番号 AND (A.日報日付 > 日報日付 OR (A.日報日付 = 日報日付 AND A.出庫時刻 > 出庫時刻))  ORDER BY 日報日付 DESC,出庫時刻 DESC),0) AS 前累計全走行
	  , 累計実車走行
	  , ISNULL((SELECT TOP (1) 累計実車走行 FROM T乗務記録HD WHERE A.車両番号 = 車両番号 AND (A.日報日付 > 日報日付 OR (A.日報日付 = 日報日付 AND A.出庫時刻 > 出庫時刻))  ORDER BY 日報日付 DESC,出庫時刻 DESC),0) AS 前累計実車走行
	  -----------
      , 一日全走行 AS 一日全走行_現行
      , (累計全走行 - ISNULL((SELECT TOP (1) 累計全走行 FROM T乗務記録HD WHERE A.車両番号 = 車両番号 AND (A.日報日付 > 日報日付 OR (A.日報日付 = 日報日付 AND A.出庫時刻 > 出庫時刻))  ORDER BY 日報日付 DESC,出庫時刻 DESC),0)) AS 走行_計算
	  , 一日実車走行 AS 一日実車走行_現行
	  , (累計実車走行 - ISNULL((SELECT TOP (1) 累計実車走行 FROM T乗務記録HD WHERE A.車両番号 = 車両番号 AND (A.日報日付 > 日報日付 OR (A.日報日付 = 日報日付 AND A.出庫時刻 > 出庫時刻))  ORDER BY 日報日付 DESC,出庫時刻 DESC),0)) AS 営業_計算
	  --, FLOOR((累計全走行 - ISNULL((SELECT TOP (1) 累計全走行 FROM T乗務記録HD WHERE A.車両番号 = 車両番号 AND (A.日報日付 > 日報日付 OR (A.日報日付 = 日報日付 AND A.出庫時刻 > 出庫時刻))  ORDER BY 日報日付 DESC,出庫時刻 DESC),0))/1000) AS 走行km
	  --, FLOOR((累計実車走行 - ISNULL((SELECT TOP (1) 累計実車走行 FROM T乗務記録HD WHERE A.車両番号 = 車両番号 AND (A.日報日付 > 日報日付 OR (A.日報日付 = 日報日付 AND A.出庫時刻 > 出庫時刻))  ORDER BY 日報日付 DESC,出庫時刻 DESC),0))/1000) AS 営業km
FROM [TaxiSystem].[dbo].[T乗務記録HD] AS A
ORDER BY 
	  車両番号 ASC
	, [日報日付] DESC
	, [出庫時刻] DESC

