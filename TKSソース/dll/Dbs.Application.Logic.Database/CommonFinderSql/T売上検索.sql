﻿SELECT  T売上HD.売上番号
     ,  T売上HD.搬入日付
     ,  T売上HD.伝票区分
     ,  T売上HD.得意先コード
     ,  T売上HD.工事コード
     ,  T売上HD.工事内部コード
     ,  T売上HD.摘要1
     ,  T売上HD.摘要2
     ,  T売上HD.備考
     ,  T売上HD.伝票発行区分                                AS  _伝票発行区分
     ,  T売上HD.登録日時
     ,  T売上HD.登録者ID
     ,  T売上HD.更新日時
     ,  T売上HD.更新者ID
     ,  M得意先.得意先名                                    AS  得意先名
     ,  M工事.工事名                                        AS  工事名
     ,  dbo.区分名('伝票区分', T売上HD.伝票区分)            AS  伝票区分名
     ,  dbo.区分名('伝票発行区分', T売上HD.伝票発行区分)    AS  _伝票発行区分名
  FROM  T売上HD
  LEFT  JOIN  M得意先   ON  M得意先.得意先コード  =  T売上HD.得意先コード
  LEFT  JOIN  M工事     ON  M工事.工事内部コード  =  T売上HD.工事内部コード

