﻿SELECT
 a.会社ID                       as 会社ID
,a.会社名                       as 会社名
,a.会社名カナ                   as カナ
,a.郵便番号                     as 郵便番号
,a.住所1                        as 住所1
,a.住所2                        as 住所2
,a.電話番号                     as 電話番号
,a.FAX番号                      as FAX番号
,a.登録番号                     as 登録番号
,a.振込先1                      as 振込先1
,a.振込先2                      as 振込先2
,a.振込先3                      as 振込先3
,a.振込先4                      as 振込先4
,a.振込先5                      as 振込先5
,a.登録日時                     as 登録日時
,dbo.ユーザー名(a.登録者ID)     as 登録者名
,a.更新日時                     as 更新日時
,dbo.ユーザー名(a.更新者ID)     as 更新者名
,h.区分名          as 利用
FROM M会社 as a
LEFT JOIN M区分     as h ON (h.データ区分 = '停止状態'   AND
                             h.区分コード = a.使用停止)
