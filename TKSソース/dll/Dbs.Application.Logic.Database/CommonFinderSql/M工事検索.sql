﻿SELECT  M工事.工事内部コード                           AS  工事内部コード
     ,  M工事.得意先コード                             AS  得意先コード
     ,  M工事.工事コード                               AS  工事コード
     ,  M工事.工事名                                   AS  工事名
     ,  M工事.工事名かな                               AS  工事名かな
     ,  M工事.完了区分                                 AS  完了区分
     ,  M工事.住所                                     AS  住所
     ,  M工事.摘要1                                    AS  摘要1
     ,  M工事.摘要2                                    AS  摘要2
     ,  M工事.削除区分                                 AS  削除区分
     ,  M工事.登録日時                                 AS  登録日時
     ,  M工事.登録者ID                                 AS  登録者ID
     ,  M工事.更新日時                                 AS  更新日時
     ,  M工事.更新者ID                                 AS  更新者ID
     ,  M得意先.得意先名                               AS  得意先名
     ,  M工事.得意先コード + ',' + M工事.工事コード    AS  _取得値      -- ←取得したい複合値をカンマ区切りで構成
  FROM  M工事
  LEFT  JOIN  M得意先   ON  M得意先.得意先コード  =  M工事.得意先コード
 WHERE  M工事.削除区分   =  '0'
