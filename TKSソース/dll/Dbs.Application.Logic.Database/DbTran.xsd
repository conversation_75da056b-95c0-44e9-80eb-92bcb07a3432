﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="DbTran" targetNamespace="http://tempuri.org/DbTran.xsd" xmlns:mstns="http://tempuri.org/DbTran.xsd" xmlns="http://tempuri.org/DbTran.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections>
          <Connection AppSettingsObjectName="MySettings" AppSettingsPropertyName="ConnectionString" ConnectionStringObject="" IsAppSettingsProperty="true" Modifier="Assembly" Name="ConnectionString (MySettings)" ParameterPrefix="@" PropertyReference="ApplicationSettings.Dbs.Application.Logic.Database.My.MySettings.GlobalReference.Default.ConnectionString" Provider="System.Data.SqlClient" />
        </Connections>
        <Tables>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="T入金HDTableAdapter" GeneratorDataComponentClassName="T入金HDTableAdapter" Name="T入金HD" UserDataComponentName="T入金HDTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="TokoDB.dbo.T入金HD" DbObjectType="Table" GenerateMethods="Get" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="GetData">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [T入金HD] WHERE (([入金番号] = @Original_入金番号) AND ((@IsNull_入金日付 = 1 AND [入金日付] IS NULL) OR ([入金日付] = @Original_入金日付)) AND ((@IsNull_請求年月 = 1 AND [請求年月] IS NULL) OR ([請求年月] = @Original_請求年月)) AND ((@IsNull_得意先コード = 1 AND [得意先コード] IS NULL) OR ([得意先コード] = @Original_得意先コード)) AND ((@IsNull_工事コード = 1 AND [工事コード] IS NULL) OR ([工事コード] = @Original_工事コード)) AND ((@IsNull_工事内部コード = 1 AND [工事内部コード] IS NULL) OR ([工事内部コード] = @Original_工事内部コード)) AND ((@IsNull_請求番号 = 1 AND [請求番号] IS NULL) OR ([請求番号] = @Original_請求番号)) AND ((@IsNull_登録日時 = 1 AND [登録日時] IS NULL) OR ([登録日時] = @Original_登録日時)) AND ((@IsNull_登録者ID = 1 AND [登録者ID] IS NULL) OR ([登録者ID] = @Original_登録者ID)) AND ((@IsNull_更新日時 = 1 AND [更新日時] IS NULL) OR ([更新日時] = @Original_更新日時)) AND ((@IsNull_更新者ID = 1 AND [更新者ID] IS NULL) OR ([更新者ID] = @Original_更新者ID)))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_入金番号" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="入金番号" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_入金日付" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="入金日付" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_入金日付" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="入金日付" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_請求年月" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="請求年月" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_請求年月" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="請求年月" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_得意先コード" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="得意先コード" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_得意先コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="得意先コード" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_工事コード" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="工事コード" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_工事コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="工事コード" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_工事内部コード" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="工事内部コード" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_工事内部コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="工事内部コード" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_請求番号" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="請求番号" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_請求番号" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="請求番号" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_登録日時" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="登録日時" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_登録日時" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="登録日時" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_登録者ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="登録者ID" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_登録者ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="登録者ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_更新日時" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="更新日時" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_更新日時" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="更新日時" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_更新者ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="更新者ID" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_更新者ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="更新者ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [T入金HD] ([入金番号], [入金日付], [請求年月], [得意先コード], [工事コード], [工事内部コード], [請求番号], [登録日時], [登録者ID], [更新日時], [更新者ID]) VALUES (@入金番号, @入金日付, @請求年月, @得意先コード, @工事コード, @工事内部コード, @請求番号, @登録日時, @登録者ID, @更新日時, @更新者ID);
SELECT 入金番号, 入金日付, 請求年月, 得意先コード, 工事コード, 工事内部コード, 請求番号, 登録日時, 登録者ID, 更新日時, 更新者ID FROM T入金HD WHERE (入金番号 = @入金番号)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@入金番号" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="入金番号" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@入金日付" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="入金日付" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@請求年月" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="請求年月" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@得意先コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="得意先コード" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@工事コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="工事コード" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@工事内部コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="工事内部コード" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@請求番号" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="請求番号" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@登録日時" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="登録日時" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@登録者ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="登録者ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@更新日時" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="更新日時" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@更新者ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="更新者ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT                      入金番号, 入金日付, 請求年月, 得意先コード, 工事コード, 工事内部コード, 請求番号, 登録日時, 登録者ID, 更新日時, 更新者ID
FROM                         T入金HD
WHERE                       (入金番号 = @入金番号)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="入金番号" ColumnName="入金番号" DataSourceName="TokoDB.dbo.T入金HD" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@入金番号" Precision="0" ProviderType="VarChar" Scale="0" Size="10" SourceColumn="入金番号" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [T入金HD] SET [入金番号] = @入金番号, [入金日付] = @入金日付, [請求年月] = @請求年月, [得意先コード] = @得意先コード, [工事コード] = @工事コード, [工事内部コード] = @工事内部コード, [請求番号] = @請求番号, [登録日時] = @登録日時, [登録者ID] = @登録者ID, [更新日時] = @更新日時, [更新者ID] = @更新者ID WHERE (([入金番号] = @Original_入金番号) AND ((@IsNull_入金日付 = 1 AND [入金日付] IS NULL) OR ([入金日付] = @Original_入金日付)) AND ((@IsNull_請求年月 = 1 AND [請求年月] IS NULL) OR ([請求年月] = @Original_請求年月)) AND ((@IsNull_得意先コード = 1 AND [得意先コード] IS NULL) OR ([得意先コード] = @Original_得意先コード)) AND ((@IsNull_工事コード = 1 AND [工事コード] IS NULL) OR ([工事コード] = @Original_工事コード)) AND ((@IsNull_工事内部コード = 1 AND [工事内部コード] IS NULL) OR ([工事内部コード] = @Original_工事内部コード)) AND ((@IsNull_請求番号 = 1 AND [請求番号] IS NULL) OR ([請求番号] = @Original_請求番号)) AND ((@IsNull_登録日時 = 1 AND [登録日時] IS NULL) OR ([登録日時] = @Original_登録日時)) AND ((@IsNull_登録者ID = 1 AND [登録者ID] IS NULL) OR ([登録者ID] = @Original_登録者ID)) AND ((@IsNull_更新日時 = 1 AND [更新日時] IS NULL) OR ([更新日時] = @Original_更新日時)) AND ((@IsNull_更新者ID = 1 AND [更新者ID] IS NULL) OR ([更新者ID] = @Original_更新者ID)));
SELECT 入金番号, 入金日付, 請求年月, 得意先コード, 工事コード, 工事内部コード, 請求番号, 登録日時, 登録者ID, 更新日時, 更新者ID FROM T入金HD WHERE (入金番号 = @入金番号)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@入金番号" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="入金番号" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@入金日付" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="入金日付" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@請求年月" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="請求年月" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@得意先コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="得意先コード" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@工事コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="工事コード" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@工事内部コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="工事内部コード" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@請求番号" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="請求番号" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@登録日時" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="登録日時" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@登録者ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="登録者ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@更新日時" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="更新日時" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@更新者ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="更新者ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_入金番号" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="入金番号" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_入金日付" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="入金日付" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_入金日付" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="入金日付" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_請求年月" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="請求年月" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_請求年月" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="請求年月" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_得意先コード" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="得意先コード" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_得意先コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="得意先コード" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_工事コード" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="工事コード" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_工事コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="工事コード" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_工事内部コード" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="工事内部コード" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_工事内部コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="工事内部コード" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_請求番号" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="請求番号" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_請求番号" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="請求番号" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_登録日時" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="登録日時" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_登録日時" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="登録日時" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_登録者ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="登録者ID" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_登録者ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="登録者ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_更新日時" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="更新日時" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_更新日時" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="更新日時" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_更新者ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="更新者ID" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_更新者ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="更新者ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="入金番号" DataSetColumn="入金番号" />
              <Mapping SourceColumn="入金日付" DataSetColumn="入金日付" />
              <Mapping SourceColumn="請求年月" DataSetColumn="請求年月" />
              <Mapping SourceColumn="得意先コード" DataSetColumn="得意先コード" />
              <Mapping SourceColumn="工事コード" DataSetColumn="工事コード" />
              <Mapping SourceColumn="登録日時" DataSetColumn="登録日時" />
              <Mapping SourceColumn="登録者ID" DataSetColumn="登録者ID" />
              <Mapping SourceColumn="更新日時" DataSetColumn="更新日時" />
              <Mapping SourceColumn="更新者ID" DataSetColumn="更新者ID" />
              <Mapping SourceColumn="工事内部コード" DataSetColumn="工事内部コード" />
              <Mapping SourceColumn="請求番号" DataSetColumn="請求番号" />
            </Mappings>
            <Sources>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="" DbObjectType="Unknown" GenerateShortCommands="true" GeneratorSourceName="DeleteByCode" Modifier="Public" Name="DeleteByCode" QueryType="NoData" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetDataBy" UserSourceName="DeleteByCode">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>DELETE
FROM                         T入金HD
WHERE                       (入金番号 = @入金番号)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="入金番号" ColumnName="入金番号" DataSourceName="TokoDB.dbo.T入金HD" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@入金番号" Precision="0" ProviderType="VarChar" Scale="0" Size="10" SourceColumn="入金番号" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
              </DbSource>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="" DbObjectType="Unknown" GenerateShortCommands="true" GeneratorSourceName="DeleteSeikyuNo" Modifier="Public" Name="DeleteSeikyuNo" QueryType="NoData" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetDataBy1" UserSourceName="DeleteSeikyuNo">
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>
UPDATE  T入金HD
   SET  T入金HD.請求番号  = ''
  FROM  T入金HD
 INNER  JOIN  (
               SELECT  T請求鑑HD.請求番号
                 FROM  T請求鑑HD
                WHERE  T請求鑑HD.締日区分       =  @締日区分
                  AND  T請求鑑HD.請求年月       =  @請求年月
                  AND  T請求鑑HD.請求日付自     =  @請求日付F
                  AND  T請求鑑HD.請求日付至     =  @請求日付T
                  AND  T請求鑑HD.得意先コード  IN  (SELECT Items FROM dbo.Split(@選択リスト,','))
              )    AS  _請求対象    ON  _請求対象.請求番号  =  T入金HD.請求番号
</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="締日区分" ColumnName="" DataSourceName="" DataTypeServer="smallint" DbType="Int32" Direction="Input" ParameterName="@締日区分" Precision="0" Scale="0" Size="2" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="請求年月" ColumnName="" DataSourceName="" DataTypeServer="varchar(7)" DbType="AnsiString" Direction="Input" ParameterName="@請求年月" Precision="0" Scale="0" Size="7" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="請求日付F" ColumnName="" DataSourceName="" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@請求日付F" Precision="0" Scale="0" Size="10" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="請求日付T" ColumnName="" DataSourceName="" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@請求日付T" Precision="0" Scale="0" Size="10" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="選択リスト" ColumnName="" DataSourceName="" DataTypeServer="nvarchar" DbType="String" Direction="Input" ParameterName="@選択リスト" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="" DbObjectType="Unknown" GenerateShortCommands="true" GeneratorSourceName="UpdateSeikyuNo" Modifier="Public" Name="UpdateSeikyuNo" QueryType="NoData" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetDataBy2" UserSourceName="UpdateSeikyuNo">
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>UPDATE  T入金HD
   SET  T入金HD.請求番号    =   _請求鑑HD.請求番号
  FROM  T入金HD
 INNER  JOIN  (
               SELECT  T請求鑑HD.請求番号
                    ,  T請求鑑HD.締日区分
                    ,  T請求鑑HD.請求年月
                    ,  T請求鑑HD.請求日付自
                    ,  T請求鑑HD.請求日付至
                    ,  T請求鑑HD.得意先コード
                 FROM  T請求鑑HD
                WHERE  T請求鑑HD.請求番号    IN  (SELECT Items FROM dbo.Split(@請求リスト,','))
              )    AS  _請求鑑HD   ON  _請求鑑HD.得意先コード   =  T入金HD.得意先コード
			                      AND  _請求鑑HD.請求日付自    &lt;=  T入金HD.入金日付
			                      AND  _請求鑑HD.請求日付至    &gt;=  T入金HD.入金日付
</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="請求リスト" ColumnName="" DataSourceName="" DataTypeServer="nvarchar" DbType="String" Direction="Input" ParameterName="@請求リスト" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </Sources>
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="T入金DTTableAdapter" GeneratorDataComponentClassName="T入金DTTableAdapter" Name="T入金DT" UserDataComponentName="T入金DTTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="TokoDB.dbo.T入金DT" DbObjectType="Table" GenerateMethods="Get" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="GetData">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [T入金DT] WHERE (([入金番号] = @Original_入金番号) AND ([行番号] = @Original_行番号) AND ((@IsNull_入金区分 = 1 AND [入金区分] IS NULL) OR ([入金区分] = @Original_入金区分)) AND ((@IsNull_入金金額 = 1 AND [入金金額] IS NULL) OR ([入金金額] = @Original_入金金額)) AND ((@IsNull_備考 = 1 AND [備考] IS NULL) OR ([備考] = @Original_備考)))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_入金番号" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="入金番号" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_行番号" Precision="3" ProviderType="Decimal" Scale="0" Size="0" SourceColumn="行番号" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_入金区分" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="入金区分" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_入金区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="入金区分" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_入金金額" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="入金金額" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_入金金額" Precision="18" ProviderType="Decimal" Scale="0" Size="0" SourceColumn="入金金額" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_備考" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="備考" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_備考" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="備考" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [T入金DT] ([入金番号], [行番号], [入金区分], [入金金額], [備考]) VALUES (@入金番号, @行番号, @入金区分, @入金金額, @備考);
SELECT 入金番号, 行番号, 入金区分, 入金金額, 備考 FROM T入金DT WHERE (入金番号 = @入金番号) AND (行番号 = @行番号)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@入金番号" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="入金番号" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@行番号" Precision="3" ProviderType="Decimal" Scale="0" Size="0" SourceColumn="行番号" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@入金区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="入金区分" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@入金金額" Precision="18" ProviderType="Decimal" Scale="0" Size="0" SourceColumn="入金金額" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@備考" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="備考" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT                      T入金DT.*
FROM                         T入金DT
WHERE                       (入金番号 = @入金番号)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="入金番号" ColumnName="入金番号" DataSourceName="TokoDB.dbo.T入金DT" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@入金番号" Precision="0" ProviderType="VarChar" Scale="0" Size="10" SourceColumn="入金番号" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [T入金DT] SET [入金番号] = @入金番号, [行番号] = @行番号, [入金区分] = @入金区分, [入金金額] = @入金金額, [備考] = @備考 WHERE (([入金番号] = @Original_入金番号) AND ([行番号] = @Original_行番号) AND ((@IsNull_入金区分 = 1 AND [入金区分] IS NULL) OR ([入金区分] = @Original_入金区分)) AND ((@IsNull_入金金額 = 1 AND [入金金額] IS NULL) OR ([入金金額] = @Original_入金金額)) AND ((@IsNull_備考 = 1 AND [備考] IS NULL) OR ([備考] = @Original_備考)));
SELECT 入金番号, 行番号, 入金区分, 入金金額, 備考 FROM T入金DT WHERE (入金番号 = @入金番号) AND (行番号 = @行番号)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@入金番号" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="入金番号" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@行番号" Precision="3" ProviderType="Decimal" Scale="0" Size="0" SourceColumn="行番号" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@入金区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="入金区分" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@入金金額" Precision="18" ProviderType="Decimal" Scale="0" Size="0" SourceColumn="入金金額" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@備考" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="備考" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_入金番号" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="入金番号" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_行番号" Precision="3" ProviderType="Decimal" Scale="0" Size="0" SourceColumn="行番号" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_入金区分" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="入金区分" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_入金区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="入金区分" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_入金金額" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="入金金額" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_入金金額" Precision="18" ProviderType="Decimal" Scale="0" Size="0" SourceColumn="入金金額" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_備考" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="備考" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_備考" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="備考" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="入金番号" DataSetColumn="入金番号" />
              <Mapping SourceColumn="行番号" DataSetColumn="行番号" />
              <Mapping SourceColumn="入金区分" DataSetColumn="入金区分" />
              <Mapping SourceColumn="入金金額" DataSetColumn="入金金額" />
              <Mapping SourceColumn="備考" DataSetColumn="備考" />
            </Mappings>
            <Sources>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="" DbObjectType="Unknown" GenerateShortCommands="true" GeneratorSourceName="DeleteByCode" Modifier="Public" Name="DeleteByCode" QueryType="NoData" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetDataBy" UserSourceName="DeleteByCode">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>DELETE
 FROM T入金DT
 WHERE 入金番号 = @入金番号</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="入金番号" ColumnName="入金番号" DataSourceName="TokoDB.dbo.T入金DT" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@入金番号" Precision="0" ProviderType="VarChar" Scale="0" Size="10" SourceColumn="入金番号" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
              </DbSource>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="" DbObjectType="Unknown" GenerateShortCommands="true" GeneratorSourceName="UpdateBiko" Modifier="Public" Name="UpdateBiko" QueryType="NoData" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetDataBy1" UserSourceName="UpdateBiko">
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>UPDATE  T入金DT
   SET  T入金DT.備考 = @備考
  FROM  T入金DT
 WHERE  T入金DT.入金番号  =  @伝票番号
   AND  T入金DT.入金区分  =  @行番号
</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="備考" ColumnName="備考" DataSourceName="TokoDB.dbo.T入金DT" DataTypeServer="nvarchar(200)" DbType="String" Direction="Input" ParameterName="@備考" Precision="0" ProviderType="NVarChar" Scale="0" Size="200" SourceColumn="備考" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="伝票番号" ColumnName="入金番号" DataSourceName="TokoDB.dbo.T入金DT" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@伝票番号" Precision="0" ProviderType="VarChar" Scale="0" Size="10" SourceColumn="入金番号" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="行番号" ColumnName="入金区分" DataSourceName="TokoDB.dbo.T入金DT" DataTypeServer="smallint" DbType="Int16" Direction="Input" ParameterName="@行番号" Precision="0" ProviderType="SmallInt" Scale="0" Size="2" SourceColumn="入金区分" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </Sources>
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="T売上HDTableAdapter" GeneratorDataComponentClassName="T売上HDTableAdapter" Name="T売上HD" UserDataComponentName="T売上HDTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="TokoDB.dbo.T売上HD" DbObjectType="Table" GenerateMethods="Get" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="GetData">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [T売上HD] WHERE (([売上番号] = @Original_売上番号) AND ((@IsNull_搬入日付 = 1 AND [搬入日付] IS NULL) OR ([搬入日付] = @Original_搬入日付)) AND ((@IsNull_伝票区分 = 1 AND [伝票区分] IS NULL) OR ([伝票区分] = @Original_伝票区分)) AND ((@IsNull_得意先コード = 1 AND [得意先コード] IS NULL) OR ([得意先コード] = @Original_得意先コード)) AND ((@IsNull_工事コード = 1 AND [工事コード] IS NULL) OR ([工事コード] = @Original_工事コード)) AND ((@IsNull_工事内部コード = 1 AND [工事内部コード] IS NULL) OR ([工事内部コード] = @Original_工事内部コード)) AND ((@IsNull_摘要1 = 1 AND [摘要1] IS NULL) OR ([摘要1] = @Original_摘要1)) AND ((@IsNull_摘要2 = 1 AND [摘要2] IS NULL) OR ([摘要2] = @Original_摘要2)) AND ((@IsNull_備考 = 1 AND [備考] IS NULL) OR ([備考] = @Original_備考)) AND ((@IsNull_伝票発行区分 = 1 AND [伝票発行区分] IS NULL) OR ([伝票発行区分] = @Original_伝票発行区分)) AND ((@IsNull_請求番号 = 1 AND [請求番号] IS NULL) OR ([請求番号] = @Original_請求番号)) AND ((@IsNull_登録日時 = 1 AND [登録日時] IS NULL) OR ([登録日時] = @Original_登録日時)) AND ((@IsNull_登録者ID = 1 AND [登録者ID] IS NULL) OR ([登録者ID] = @Original_登録者ID)) AND ((@IsNull_更新日時 = 1 AND [更新日時] IS NULL) OR ([更新日時] = @Original_更新日時)) AND ((@IsNull_更新者ID = 1 AND [更新者ID] IS NULL) OR ([更新者ID] = @Original_更新者ID)))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_売上番号" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="売上番号" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_搬入日付" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="搬入日付" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_搬入日付" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="搬入日付" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_伝票区分" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="伝票区分" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_伝票区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="伝票区分" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_得意先コード" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="得意先コード" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_得意先コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="得意先コード" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_工事コード" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="工事コード" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_工事コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="工事コード" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_工事内部コード" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="工事内部コード" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_工事内部コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="工事内部コード" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_摘要1" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="摘要1" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_摘要1" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="摘要1" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_摘要2" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="摘要2" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_摘要2" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="摘要2" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_備考" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="備考" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_備考" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="備考" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_伝票発行区分" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="伝票発行区分" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_伝票発行区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="伝票発行区分" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_請求番号" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="請求番号" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_請求番号" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="請求番号" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_登録日時" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="登録日時" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_登録日時" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="登録日時" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_登録者ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="登録者ID" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_登録者ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="登録者ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_更新日時" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="更新日時" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_更新日時" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="更新日時" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_更新者ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="更新者ID" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_更新者ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="更新者ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [T売上HD] ([売上番号], [搬入日付], [伝票区分], [得意先コード], [工事コード], [工事内部コード], [摘要1], [摘要2], [備考], [伝票発行区分], [請求番号], [登録日時], [登録者ID], [更新日時], [更新者ID]) VALUES (@売上番号, @搬入日付, @伝票区分, @得意先コード, @工事コード, @工事内部コード, @摘要1, @摘要2, @備考, @伝票発行区分, @請求番号, @登録日時, @登録者ID, @更新日時, @更新者ID);
SELECT 売上番号, 搬入日付, 伝票区分, 得意先コード, 工事コード, 工事内部コード, 摘要1, 摘要2, 備考, 伝票発行区分, 請求番号, 登録日時, 登録者ID, 更新日時, 更新者ID FROM T売上HD WHERE (売上番号 = @売上番号)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@売上番号" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="売上番号" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@搬入日付" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="搬入日付" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@伝票区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="伝票区分" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@得意先コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="得意先コード" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@工事コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="工事コード" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@工事内部コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="工事内部コード" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@摘要1" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="摘要1" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@摘要2" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="摘要2" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@備考" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="備考" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@伝票発行区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="伝票発行区分" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@請求番号" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="請求番号" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@登録日時" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="登録日時" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@登録者ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="登録者ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@更新日時" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="更新日時" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@更新者ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="更新者ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT                      T売上HD.*
FROM                         T売上HD
WHERE                       (売上番号 = @売上番号)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="売上番号" ColumnName="売上番号" DataSourceName="TokoDB.dbo.T売上HD" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@売上番号" Precision="0" ProviderType="VarChar" Scale="0" Size="10" SourceColumn="売上番号" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [T売上HD] SET [売上番号] = @売上番号, [搬入日付] = @搬入日付, [伝票区分] = @伝票区分, [得意先コード] = @得意先コード, [工事コード] = @工事コード, [工事内部コード] = @工事内部コード, [摘要1] = @摘要1, [摘要2] = @摘要2, [備考] = @備考, [伝票発行区分] = @伝票発行区分, [請求番号] = @請求番号, [登録日時] = @登録日時, [登録者ID] = @登録者ID, [更新日時] = @更新日時, [更新者ID] = @更新者ID WHERE (([売上番号] = @Original_売上番号) AND ((@IsNull_搬入日付 = 1 AND [搬入日付] IS NULL) OR ([搬入日付] = @Original_搬入日付)) AND ((@IsNull_伝票区分 = 1 AND [伝票区分] IS NULL) OR ([伝票区分] = @Original_伝票区分)) AND ((@IsNull_得意先コード = 1 AND [得意先コード] IS NULL) OR ([得意先コード] = @Original_得意先コード)) AND ((@IsNull_工事コード = 1 AND [工事コード] IS NULL) OR ([工事コード] = @Original_工事コード)) AND ((@IsNull_工事内部コード = 1 AND [工事内部コード] IS NULL) OR ([工事内部コード] = @Original_工事内部コード)) AND ((@IsNull_摘要1 = 1 AND [摘要1] IS NULL) OR ([摘要1] = @Original_摘要1)) AND ((@IsNull_摘要2 = 1 AND [摘要2] IS NULL) OR ([摘要2] = @Original_摘要2)) AND ((@IsNull_備考 = 1 AND [備考] IS NULL) OR ([備考] = @Original_備考)) AND ((@IsNull_伝票発行区分 = 1 AND [伝票発行区分] IS NULL) OR ([伝票発行区分] = @Original_伝票発行区分)) AND ((@IsNull_請求番号 = 1 AND [請求番号] IS NULL) OR ([請求番号] = @Original_請求番号)) AND ((@IsNull_登録日時 = 1 AND [登録日時] IS NULL) OR ([登録日時] = @Original_登録日時)) AND ((@IsNull_登録者ID = 1 AND [登録者ID] IS NULL) OR ([登録者ID] = @Original_登録者ID)) AND ((@IsNull_更新日時 = 1 AND [更新日時] IS NULL) OR ([更新日時] = @Original_更新日時)) AND ((@IsNull_更新者ID = 1 AND [更新者ID] IS NULL) OR ([更新者ID] = @Original_更新者ID)));
SELECT 売上番号, 搬入日付, 伝票区分, 得意先コード, 工事コード, 工事内部コード, 摘要1, 摘要2, 備考, 伝票発行区分, 請求番号, 登録日時, 登録者ID, 更新日時, 更新者ID FROM T売上HD WHERE (売上番号 = @売上番号)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@売上番号" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="売上番号" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@搬入日付" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="搬入日付" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@伝票区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="伝票区分" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@得意先コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="得意先コード" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@工事コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="工事コード" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@工事内部コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="工事内部コード" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@摘要1" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="摘要1" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@摘要2" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="摘要2" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@備考" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="備考" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@伝票発行区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="伝票発行区分" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@請求番号" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="請求番号" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@登録日時" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="登録日時" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@登録者ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="登録者ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@更新日時" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="更新日時" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@更新者ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="更新者ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_売上番号" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="売上番号" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_搬入日付" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="搬入日付" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_搬入日付" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="搬入日付" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_伝票区分" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="伝票区分" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_伝票区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="伝票区分" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_得意先コード" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="得意先コード" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_得意先コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="得意先コード" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_工事コード" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="工事コード" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_工事コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="工事コード" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_工事内部コード" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="工事内部コード" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_工事内部コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="工事内部コード" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_摘要1" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="摘要1" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_摘要1" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="摘要1" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_摘要2" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="摘要2" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_摘要2" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="摘要2" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_備考" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="備考" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_備考" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="備考" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_伝票発行区分" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="伝票発行区分" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_伝票発行区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="伝票発行区分" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_請求番号" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="請求番号" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_請求番号" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="請求番号" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_登録日時" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="登録日時" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_登録日時" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="登録日時" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_登録者ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="登録者ID" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_登録者ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="登録者ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_更新日時" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="更新日時" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_更新日時" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="更新日時" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_更新者ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="更新者ID" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_更新者ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="更新者ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="売上番号" DataSetColumn="売上番号" />
              <Mapping SourceColumn="搬入日付" DataSetColumn="搬入日付" />
              <Mapping SourceColumn="伝票区分" DataSetColumn="伝票区分" />
              <Mapping SourceColumn="得意先コード" DataSetColumn="得意先コード" />
              <Mapping SourceColumn="工事コード" DataSetColumn="工事コード" />
              <Mapping SourceColumn="摘要1" DataSetColumn="摘要1" />
              <Mapping SourceColumn="摘要2" DataSetColumn="摘要2" />
              <Mapping SourceColumn="登録日時" DataSetColumn="登録日時" />
              <Mapping SourceColumn="登録者ID" DataSetColumn="登録者ID" />
              <Mapping SourceColumn="更新日時" DataSetColumn="更新日時" />
              <Mapping SourceColumn="更新者ID" DataSetColumn="更新者ID" />
              <Mapping SourceColumn="工事内部コード" DataSetColumn="工事内部コード" />
              <Mapping SourceColumn="備考" DataSetColumn="備考" />
              <Mapping SourceColumn="伝票発行区分" DataSetColumn="伝票発行区分" />
              <Mapping SourceColumn="請求番号" DataSetColumn="請求番号" />
            </Mappings>
            <Sources>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="" DbObjectType="Unknown" GenerateShortCommands="true" GeneratorSourceName="DeleteByCode" Modifier="Public" Name="DeleteByCode" QueryType="NoData" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetDataBy" UserSourceName="DeleteByCode">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>DELETE FROM T売上HD WHERE 売上番号 = @売上番号</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="売上番号" ColumnName="売上番号" DataSourceName="TokoDB.dbo.T売上HD" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@売上番号" Precision="0" ProviderType="VarChar" Scale="0" Size="10" SourceColumn="売上番号" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
              </DbSource>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="" DbObjectType="Unknown" GenerateShortCommands="true" GeneratorSourceName="DeleteSeikyuNo" Modifier="Public" Name="DeleteSeikyuNo" QueryType="NoData" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetDataBy1" UserSourceName="DeleteSeikyuNo">
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>UPDATE  T売上HD
   SET  T売上HD.請求番号  = ''
  FROM  T売上HD
 INNER  JOIN  (
               SELECT  T請求鑑HD.請求番号
                 FROM  T請求鑑HD
                WHERE  T請求鑑HD.締日区分       =  @締日区分
                  AND  T請求鑑HD.請求年月       =  @請求年月
                  AND  T請求鑑HD.請求日付自     =  @請求日付F
                  AND  T請求鑑HD.請求日付至     =  @請求日付T
                  AND  T請求鑑HD.得意先コード  IN  (SELECT Items FROM dbo.Split(@選択リスト,','))
              )    AS  _請求対象    ON  _請求対象.請求番号  =  T売上HD.請求番号
</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="締日区分" ColumnName="" DataSourceName="" DataTypeServer="smallint" DbType="Int32" Direction="Input" ParameterName="@締日区分" Precision="0" Scale="0" Size="2" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="請求年月" ColumnName="" DataSourceName="" DataTypeServer="varchar(7)" DbType="AnsiString" Direction="Input" ParameterName="@請求年月" Precision="0" Scale="0" Size="7" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="請求日付F" ColumnName="" DataSourceName="" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@請求日付F" Precision="0" Scale="0" Size="10" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="請求日付T" ColumnName="" DataSourceName="" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@請求日付T" Precision="0" Scale="0" Size="10" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="選択リスト" ColumnName="" DataSourceName="" DataTypeServer="nvarchar" DbType="String" Direction="Input" ParameterName="@選択リスト" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="" DbObjectType="Unknown" GenerateShortCommands="true" GeneratorSourceName="UpdateSeikyuNo" Modifier="Public" Name="UpdateSeikyuNo" QueryType="NoData" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetDataBy2" UserSourceName="UpdateSeikyuNo">
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>UPDATE  T売上HD
   SET  T売上HD.請求番号 = T請求鑑HD.請求番号
  FROM  T売上HD
 INNER  JOIN  T請求鑑HD     ON  T請求鑑HD.得意先コード  =  T売上HD.得意先コード
                           AND  T請求鑑HD.請求日付自   &lt;=  T売上HD.搬入日付
                           AND  T請求鑑HD.請求日付至   &gt;=  T売上HD.搬入日付
 WHERE  T売上HD.伝票区分       IN  (2, 4)  -- 売切、継続
   AND  T請求鑑HD.請求番号    IN  (SELECT Items FROM dbo.Split(@請求リスト,','))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="請求リスト" ColumnName="" DataSourceName="" DataTypeServer="nvarchar" DbType="String" Direction="Input" ParameterName="@請求リスト" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </Sources>
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="T売上DTTableAdapter" GeneratorDataComponentClassName="T売上DTTableAdapter" Name="T売上DT" UserDataComponentName="T売上DTTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="TokoDB.dbo.T売上DT" DbObjectType="Table" GenerateMethods="Get" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="GetData">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [T売上DT] WHERE (([売上番号] = @Original_売上番号) AND ([行番号] = @Original_行番号) AND ((@IsNull_計算区分 = 1 AND [計算区分] IS NULL) OR ([計算区分] = @Original_計算区分)) AND ((@IsNull_商品コード = 1 AND [商品コード] IS NULL) OR ([商品コード] = @Original_商品コード)) AND ((@IsNull_商品名 = 1 AND [商品名] IS NULL) OR ([商品名] = @Original_商品名)) AND ((@IsNull_数量 = 1 AND [数量] IS NULL) OR ([数量] = @Original_数量)) AND ((@IsNull_単価 = 1 AND [単価] IS NULL) OR ([単価] = @Original_単価)) AND ((@IsNull_基本料金 = 1 AND [基本料金] IS NULL) OR ([基本料金] = @Original_基本料金)) AND ((@IsNull_金額 = 1 AND [金額] IS NULL) OR ([金額] = @Original_金額)) AND ((@IsNull_単位区分 = 1 AND [単位区分] IS NULL) OR ([単位区分] = @Original_単位区分)) AND ((@IsNull_備考明細 = 1 AND [備考明細] IS NULL) OR ([備考明細] = @Original_備考明細)) AND ((@IsNull_備考社内 = 1 AND [備考社内] IS NULL) OR ([備考社内] = @Original_備考社内)) AND ((@IsNull_備考台帳 = 1 AND [備考台帳] IS NULL) OR ([備考台帳] = @Original_備考台帳)) AND ((@IsNull_搬入番号 = 1 AND [搬入番号] IS NULL) OR ([搬入番号] = @Original_搬入番号)) AND ((@IsNull_開始日付 = 1 AND [開始日付] IS NULL) OR ([開始日付] = @Original_開始日付)) AND ((@IsNull_終了日付 = 1 AND [終了日付] IS NULL) OR ([終了日付] = @Original_終了日付)) AND ((@IsNull_返納日付 = 1 AND [返納日付] IS NULL) OR ([返納日付] = @Original_返納日付)))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_売上番号" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="売上番号" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_行番号" Precision="3" ProviderType="Decimal" Scale="0" Size="0" SourceColumn="行番号" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_計算区分" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="計算区分" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_計算区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="計算区分" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_商品コード" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="商品コード" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_商品コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="商品コード" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_商品名" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="商品名" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_商品名" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="商品名" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_数量" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="数量" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_数量" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="数量" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_単価" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="単価" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_単価" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="単価" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_基本料金" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="基本料金" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_基本料金" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="基本料金" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_金額" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="金額" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_金額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="金額" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_単位区分" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="単位区分" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_単位区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="単位区分" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_備考明細" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="備考明細" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_備考明細" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="備考明細" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_備考社内" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="備考社内" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_備考社内" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="備考社内" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_備考台帳" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="備考台帳" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_備考台帳" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="備考台帳" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_搬入番号" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="搬入番号" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_搬入番号" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="搬入番号" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_開始日付" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="開始日付" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_開始日付" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="開始日付" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_終了日付" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="終了日付" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_終了日付" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="終了日付" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_返納日付" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="返納日付" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_返納日付" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="返納日付" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [T売上DT] ([売上番号], [行番号], [計算区分], [商品コード], [商品名], [数量], [単価], [基本料金], [金額], [単位区分], [備考明細], [備考社内], [備考台帳], [搬入番号], [開始日付], [終了日付], [返納日付]) VALUES (@売上番号, @行番号, @計算区分, @商品コード, @商品名, @数量, @単価, @基本料金, @金額, @単位区分, @備考明細, @備考社内, @備考台帳, @搬入番号, @開始日付, @終了日付, @返納日付);
SELECT 売上番号, 行番号, 計算区分, 商品コード, 商品名, 数量, 単価, 基本料金, 金額, 単位区分, 備考明細, 備考社内, 備考台帳, 搬入番号, 開始日付, 終了日付, 返納日付 FROM T売上DT WHERE (売上番号 = @売上番号) AND (行番号 = @行番号)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@売上番号" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="売上番号" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@行番号" Precision="3" ProviderType="Decimal" Scale="0" Size="0" SourceColumn="行番号" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@計算区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="計算区分" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@商品コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="商品コード" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@商品名" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="商品名" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@数量" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="数量" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@単価" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="単価" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@基本料金" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="基本料金" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@金額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="金額" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@単位区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="単位区分" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@備考明細" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="備考明細" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@備考社内" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="備考社内" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@備考台帳" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="備考台帳" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@搬入番号" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="搬入番号" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@開始日付" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="開始日付" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@終了日付" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="終了日付" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@返納日付" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="返納日付" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT                      T売上DT.*
FROM                         T売上DT
WHERE                       (売上番号 = @売上番号)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="売上番号" ColumnName="売上番号" DataSourceName="TokoDB.dbo.T売上DT" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@売上番号" Precision="0" ProviderType="VarChar" Scale="0" Size="10" SourceColumn="売上番号" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [T売上DT] SET [売上番号] = @売上番号, [行番号] = @行番号, [計算区分] = @計算区分, [商品コード] = @商品コード, [商品名] = @商品名, [数量] = @数量, [単価] = @単価, [基本料金] = @基本料金, [金額] = @金額, [単位区分] = @単位区分, [備考明細] = @備考明細, [備考社内] = @備考社内, [備考台帳] = @備考台帳, [搬入番号] = @搬入番号, [開始日付] = @開始日付, [終了日付] = @終了日付, [返納日付] = @返納日付 WHERE (([売上番号] = @Original_売上番号) AND ([行番号] = @Original_行番号) AND ((@IsNull_計算区分 = 1 AND [計算区分] IS NULL) OR ([計算区分] = @Original_計算区分)) AND ((@IsNull_商品コード = 1 AND [商品コード] IS NULL) OR ([商品コード] = @Original_商品コード)) AND ((@IsNull_商品名 = 1 AND [商品名] IS NULL) OR ([商品名] = @Original_商品名)) AND ((@IsNull_数量 = 1 AND [数量] IS NULL) OR ([数量] = @Original_数量)) AND ((@IsNull_単価 = 1 AND [単価] IS NULL) OR ([単価] = @Original_単価)) AND ((@IsNull_基本料金 = 1 AND [基本料金] IS NULL) OR ([基本料金] = @Original_基本料金)) AND ((@IsNull_金額 = 1 AND [金額] IS NULL) OR ([金額] = @Original_金額)) AND ((@IsNull_単位区分 = 1 AND [単位区分] IS NULL) OR ([単位区分] = @Original_単位区分)) AND ((@IsNull_備考明細 = 1 AND [備考明細] IS NULL) OR ([備考明細] = @Original_備考明細)) AND ((@IsNull_備考社内 = 1 AND [備考社内] IS NULL) OR ([備考社内] = @Original_備考社内)) AND ((@IsNull_備考台帳 = 1 AND [備考台帳] IS NULL) OR ([備考台帳] = @Original_備考台帳)) AND ((@IsNull_搬入番号 = 1 AND [搬入番号] IS NULL) OR ([搬入番号] = @Original_搬入番号)) AND ((@IsNull_開始日付 = 1 AND [開始日付] IS NULL) OR ([開始日付] = @Original_開始日付)) AND ((@IsNull_終了日付 = 1 AND [終了日付] IS NULL) OR ([終了日付] = @Original_終了日付)) AND ((@IsNull_返納日付 = 1 AND [返納日付] IS NULL) OR ([返納日付] = @Original_返納日付)));
SELECT 売上番号, 行番号, 計算区分, 商品コード, 商品名, 数量, 単価, 基本料金, 金額, 単位区分, 備考明細, 備考社内, 備考台帳, 搬入番号, 開始日付, 終了日付, 返納日付 FROM T売上DT WHERE (売上番号 = @売上番号) AND (行番号 = @行番号)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@売上番号" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="売上番号" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@行番号" Precision="3" ProviderType="Decimal" Scale="0" Size="0" SourceColumn="行番号" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@計算区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="計算区分" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@商品コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="商品コード" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@商品名" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="商品名" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@数量" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="数量" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@単価" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="単価" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@基本料金" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="基本料金" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@金額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="金額" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@単位区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="単位区分" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@備考明細" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="備考明細" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@備考社内" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="備考社内" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@備考台帳" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="備考台帳" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@搬入番号" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="搬入番号" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@開始日付" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="開始日付" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@終了日付" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="終了日付" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@返納日付" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="返納日付" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_売上番号" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="売上番号" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_行番号" Precision="3" ProviderType="Decimal" Scale="0" Size="0" SourceColumn="行番号" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_計算区分" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="計算区分" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_計算区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="計算区分" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_商品コード" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="商品コード" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_商品コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="商品コード" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_商品名" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="商品名" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_商品名" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="商品名" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_数量" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="数量" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_数量" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="数量" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_単価" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="単価" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_単価" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="単価" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_基本料金" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="基本料金" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_基本料金" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="基本料金" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_金額" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="金額" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_金額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="金額" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_単位区分" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="単位区分" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_単位区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="単位区分" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_備考明細" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="備考明細" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_備考明細" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="備考明細" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_備考社内" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="備考社内" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_備考社内" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="備考社内" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_備考台帳" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="備考台帳" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_備考台帳" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="備考台帳" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_搬入番号" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="搬入番号" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_搬入番号" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="搬入番号" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_開始日付" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="開始日付" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_開始日付" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="開始日付" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_終了日付" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="終了日付" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_終了日付" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="終了日付" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_返納日付" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="返納日付" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_返納日付" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="返納日付" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="売上番号" DataSetColumn="売上番号" />
              <Mapping SourceColumn="行番号" DataSetColumn="行番号" />
              <Mapping SourceColumn="計算区分" DataSetColumn="計算区分" />
              <Mapping SourceColumn="商品コード" DataSetColumn="商品コード" />
              <Mapping SourceColumn="商品名" DataSetColumn="商品名" />
              <Mapping SourceColumn="数量" DataSetColumn="数量" />
              <Mapping SourceColumn="単価" DataSetColumn="単価" />
              <Mapping SourceColumn="金額" DataSetColumn="金額" />
              <Mapping SourceColumn="単位区分" DataSetColumn="単位区分" />
              <Mapping SourceColumn="備考" DataSetColumn="備考" />
              <Mapping SourceColumn="搬入番号" DataSetColumn="搬入番号" />
              <Mapping SourceColumn="開始日付" DataSetColumn="開始日付" />
              <Mapping SourceColumn="終了日付" DataSetColumn="終了日付" />
              <Mapping SourceColumn="返納日付" DataSetColumn="返納日付" />
              <Mapping SourceColumn="備考明細" DataSetColumn="備考明細" />
              <Mapping SourceColumn="備考社内" DataSetColumn="備考社内" />
              <Mapping SourceColumn="基本料金" DataSetColumn="基本料金" />
              <Mapping SourceColumn="備考台帳" DataSetColumn="備考台帳" />
            </Mappings>
            <Sources>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="" DbObjectType="Unknown" GenerateShortCommands="true" GeneratorSourceName="DeleteByCode" Modifier="Public" Name="DeleteByCode" QueryType="NoData" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetDataBy" UserSourceName="DeleteByCode">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>DELETE FROM T売上DT WHERE 売上番号 = @売上番号</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="売上番号" ColumnName="売上番号" DataSourceName="TokoDB.dbo.T売上DT" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@売上番号" Precision="0" ProviderType="VarChar" Scale="0" Size="10" SourceColumn="売上番号" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
              </DbSource>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="" DbObjectType="Unknown" GenerateShortCommands="true" GeneratorSourceName="DeleteByRowNo" Modifier="Public" Name="DeleteByRowNo" QueryType="NoData" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetDataBy1" UserSourceName="DeleteByRowNo">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>DELETE FROM T売上DT WHERE 売上番号 = @売上番号 AND  行番号 = @行番号</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="売上番号" ColumnName="売上番号" DataSourceName="TokoDB.dbo.T売上DT" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@売上番号" Precision="0" ProviderType="VarChar" Scale="0" Size="10" SourceColumn="売上番号" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="行番号" ColumnName="行番号" DataSourceName="TokoDB.dbo.T売上DT" DataTypeServer="decimal(3, 0)" DbType="Decimal" Direction="Input" ParameterName="@行番号" Precision="3" ProviderType="Decimal" Scale="0" Size="5" SourceColumn="行番号" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
              </DbSource>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="TokoDB.dbo.T売上DT" DbObjectType="Table" GenerateMethods="Get" GenerateShortCommands="true" GeneratorGetMethodName="GetDataGyoNO" GetMethodModifier="Public" GetMethodName="GetDataGyoNO" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetDataGyoNO" UserSourceName="GetDataGyoNO">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT                      売上番号, 行番号, 計算区分, 商品コード, 商品名, 数量, 単価, 基本料金, 金額, 単位区分, 備考明細, 備考社内, 備考台帳, 搬入番号, 開始日付, 終了日付, 
                                      返納日付
FROM                         T売上DT
WHERE                       (売上番号 = @売上番号) AND (行番号 = @行番号)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="売上番号" ColumnName="売上番号" DataSourceName="TokoDB.dbo.T売上DT" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@売上番号" Precision="0" ProviderType="VarChar" Scale="0" Size="10" SourceColumn="売上番号" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="行番号" ColumnName="行番号" DataSourceName="TokoDB.dbo.T売上DT" DataTypeServer="decimal(3, 0)" DbType="Decimal" Direction="Input" ParameterName="@行番号" Precision="3" ProviderType="Decimal" Scale="0" Size="5" SourceColumn="行番号" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="" DbObjectType="Unknown" GenerateShortCommands="true" GeneratorSourceName="UpdateBiko" Modifier="Public" Name="UpdateBiko" QueryType="NoData" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetDataBy2" UserSourceName="UpdateBiko">
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>UPDATE  T売上DT
   SET  T売上DT.備考台帳 = @備考
  FROM  T売上DT
 WHERE  T売上DT.売上番号  =  @伝票番号
   AND  T売上DT.行番号    =  @行番号
</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="備考" ColumnName="備考台帳" DataSourceName="TokoDB.dbo.T売上DT" DataTypeServer="nvarchar(200)" DbType="String" Direction="Input" ParameterName="@備考" Precision="0" ProviderType="NVarChar" Scale="0" Size="200" SourceColumn="備考台帳" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="伝票番号" ColumnName="売上番号" DataSourceName="TokoDB.dbo.T売上DT" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@伝票番号" Precision="0" ProviderType="VarChar" Scale="0" Size="10" SourceColumn="売上番号" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="行番号" ColumnName="行番号" DataSourceName="TokoDB.dbo.T売上DT" DataTypeServer="decimal(3, 0)" DbType="Decimal" Direction="Input" ParameterName="@行番号" Precision="3" ProviderType="Decimal" Scale="0" Size="5" SourceColumn="行番号" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </Sources>
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="T請求鑑DTTableAdapter" GeneratorDataComponentClassName="T請求鑑DTTableAdapter" Name="T請求鑑DT" UserDataComponentName="T請求鑑DTTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="TokoDB.dbo.T請求鑑DT" DbObjectType="Table" GenerateMethods="Get" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="GetData">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [T請求鑑DT] WHERE (([請求番号] = @Original_請求番号) AND ([行番号] = @Original_行番号) AND ((@IsNull_得意先コード = 1 AND [得意先コード] IS NULL) OR ([得意先コード] = @Original_得意先コード)) AND ((@IsNull_工事コード = 1 AND [工事コード] IS NULL) OR ([工事コード] = @Original_工事コード)) AND ((@IsNull_工事内部コード = 1 AND [工事内部コード] IS NULL) OR ([工事内部コード] = @Original_工事内部コード)) AND ((@IsNull_前回額 = 1 AND [前回額] IS NULL) OR ([前回額] = @Original_前回額)) AND ((@IsNull_入金額 = 1 AND [入金額] IS NULL) OR ([入金額] = @Original_入金額)) AND ((@IsNull_調整額 = 1 AND [調整額] IS NULL) OR ([調整額] = @Original_調整額)) AND ((@IsNull_繰越額 = 1 AND [繰越額] IS NULL) OR ([繰越額] = @Original_繰越額)) AND ((@IsNull_税抜額 = 1 AND [税抜額] IS NULL) OR ([税抜額] = @Original_税抜額)) AND ((@IsNull_税込額 = 1 AND [税込額] IS NULL) OR ([税込額] = @Original_税込額)) AND ((@IsNull_消費税 = 1 AND [消費税] IS NULL) OR ([消費税] = @Original_消費税)) AND ((@IsNull_請求額 = 1 AND [請求額] IS NULL) OR ([請求額] = @Original_請求額)) AND ((@IsNull_備考 = 1 AND [備考] IS NULL) OR ([備考] = @Original_備考)))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_請求番号" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="請求番号" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_行番号" Precision="3" ProviderType="Decimal" Scale="0" Size="0" SourceColumn="行番号" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_得意先コード" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="得意先コード" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_得意先コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="得意先コード" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_工事コード" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="工事コード" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_工事コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="工事コード" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_工事内部コード" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="工事内部コード" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_工事内部コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="工事内部コード" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_前回額" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="前回額" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_前回額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="前回額" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_入金額" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="入金額" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_入金額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="入金額" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_調整額" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="調整額" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_調整額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="調整額" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_繰越額" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="繰越額" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_繰越額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="繰越額" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_税抜額" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="税抜額" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_税抜額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="税抜額" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_税込額" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="税込額" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_税込額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="税込額" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_消費税" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="消費税" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_消費税" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="消費税" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_請求額" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="請求額" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_請求額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="請求額" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_備考" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="備考" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_備考" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="備考" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [T請求鑑DT] ([請求番号], [行番号], [得意先コード], [工事コード], [工事内部コード], [前回額], [入金額], [調整額], [繰越額], [税抜額], [税込額], [消費税], [請求額], [備考]) VALUES (@請求番号, @行番号, @得意先コード, @工事コード, @工事内部コード, @前回額, @入金額, @調整額, @繰越額, @税抜額, @税込額, @消費税, @請求額, @備考);
SELECT 請求番号, 行番号, 得意先コード, 工事コード, 工事内部コード, 前回額, 入金額, 調整額, 繰越額, 税抜額, 税込額, 消費税, 請求額, 備考 FROM T請求鑑DT WHERE (行番号 = @行番号) AND (請求番号 = @請求番号)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@請求番号" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="請求番号" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@行番号" Precision="3" ProviderType="Decimal" Scale="0" Size="0" SourceColumn="行番号" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@得意先コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="得意先コード" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@工事コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="工事コード" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@工事内部コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="工事内部コード" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@前回額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="前回額" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@入金額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="入金額" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@調整額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="調整額" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@繰越額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="繰越額" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@税抜額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="税抜額" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@税込額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="税込額" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@消費税" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="消費税" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@請求額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="請求額" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@備考" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="備考" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT                      T請求鑑DT.*
FROM                         T請求鑑DT
WHERE                       (請求番号 = @請求番号)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="請求番号" ColumnName="請求番号" DataSourceName="TokoDB.dbo.T請求鑑DT" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@請求番号" Precision="0" ProviderType="VarChar" Scale="0" Size="10" SourceColumn="請求番号" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [T請求鑑DT] SET [請求番号] = @請求番号, [行番号] = @行番号, [得意先コード] = @得意先コード, [工事コード] = @工事コード, [工事内部コード] = @工事内部コード, [前回額] = @前回額, [入金額] = @入金額, [調整額] = @調整額, [繰越額] = @繰越額, [税抜額] = @税抜額, [税込額] = @税込額, [消費税] = @消費税, [請求額] = @請求額, [備考] = @備考 WHERE (([請求番号] = @Original_請求番号) AND ([行番号] = @Original_行番号) AND ((@IsNull_得意先コード = 1 AND [得意先コード] IS NULL) OR ([得意先コード] = @Original_得意先コード)) AND ((@IsNull_工事コード = 1 AND [工事コード] IS NULL) OR ([工事コード] = @Original_工事コード)) AND ((@IsNull_工事内部コード = 1 AND [工事内部コード] IS NULL) OR ([工事内部コード] = @Original_工事内部コード)) AND ((@IsNull_前回額 = 1 AND [前回額] IS NULL) OR ([前回額] = @Original_前回額)) AND ((@IsNull_入金額 = 1 AND [入金額] IS NULL) OR ([入金額] = @Original_入金額)) AND ((@IsNull_調整額 = 1 AND [調整額] IS NULL) OR ([調整額] = @Original_調整額)) AND ((@IsNull_繰越額 = 1 AND [繰越額] IS NULL) OR ([繰越額] = @Original_繰越額)) AND ((@IsNull_税抜額 = 1 AND [税抜額] IS NULL) OR ([税抜額] = @Original_税抜額)) AND ((@IsNull_税込額 = 1 AND [税込額] IS NULL) OR ([税込額] = @Original_税込額)) AND ((@IsNull_消費税 = 1 AND [消費税] IS NULL) OR ([消費税] = @Original_消費税)) AND ((@IsNull_請求額 = 1 AND [請求額] IS NULL) OR ([請求額] = @Original_請求額)) AND ((@IsNull_備考 = 1 AND [備考] IS NULL) OR ([備考] = @Original_備考)));
SELECT 請求番号, 行番号, 得意先コード, 工事コード, 工事内部コード, 前回額, 入金額, 調整額, 繰越額, 税抜額, 税込額, 消費税, 請求額, 備考 FROM T請求鑑DT WHERE (行番号 = @行番号) AND (請求番号 = @請求番号)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@請求番号" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="請求番号" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@行番号" Precision="3" ProviderType="Decimal" Scale="0" Size="0" SourceColumn="行番号" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@得意先コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="得意先コード" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@工事コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="工事コード" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@工事内部コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="工事内部コード" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@前回額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="前回額" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@入金額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="入金額" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@調整額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="調整額" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@繰越額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="繰越額" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@税抜額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="税抜額" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@税込額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="税込額" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@消費税" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="消費税" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@請求額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="請求額" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@備考" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="備考" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_請求番号" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="請求番号" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_行番号" Precision="3" ProviderType="Decimal" Scale="0" Size="0" SourceColumn="行番号" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_得意先コード" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="得意先コード" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_得意先コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="得意先コード" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_工事コード" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="工事コード" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_工事コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="工事コード" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_工事内部コード" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="工事内部コード" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_工事内部コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="工事内部コード" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_前回額" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="前回額" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_前回額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="前回額" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_入金額" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="入金額" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_入金額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="入金額" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_調整額" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="調整額" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_調整額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="調整額" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_繰越額" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="繰越額" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_繰越額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="繰越額" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_税抜額" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="税抜額" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_税抜額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="税抜額" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_税込額" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="税込額" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_税込額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="税込額" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_消費税" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="消費税" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_消費税" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="消費税" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_請求額" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="請求額" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_請求額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="請求額" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_備考" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="備考" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_備考" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="備考" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="請求番号" DataSetColumn="請求番号" />
              <Mapping SourceColumn="行番号" DataSetColumn="行番号" />
              <Mapping SourceColumn="工事内部コード" DataSetColumn="工事内部コード" />
              <Mapping SourceColumn="前回額" DataSetColumn="前回額" />
              <Mapping SourceColumn="入金額" DataSetColumn="入金額" />
              <Mapping SourceColumn="調整額" DataSetColumn="調整額" />
              <Mapping SourceColumn="繰越額" DataSetColumn="繰越額" />
              <Mapping SourceColumn="税抜額" DataSetColumn="税抜額" />
              <Mapping SourceColumn="税込額" DataSetColumn="税込額" />
              <Mapping SourceColumn="消費税" DataSetColumn="消費税" />
              <Mapping SourceColumn="請求額" DataSetColumn="請求額" />
              <Mapping SourceColumn="備考" DataSetColumn="備考" />
              <Mapping SourceColumn="得意先コード" DataSetColumn="得意先コード" />
              <Mapping SourceColumn="工事コード" DataSetColumn="工事コード" />
            </Mappings>
            <Sources>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="" DbObjectType="Unknown" GenerateShortCommands="true" GeneratorSourceName="DeleteByCode" Modifier="Public" Name="DeleteByCode" QueryType="NoData" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetDataBy" UserSourceName="DeleteByCode">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>DELETE FROM [T請求鑑DT] WHERE [請求番号] = @請求番号</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="請求番号" ColumnName="請求番号" DataSourceName="TokoDB.dbo.T請求鑑DT" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@請求番号" Precision="0" ProviderType="VarChar" Scale="0" Size="10" SourceColumn="請求番号" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
              </DbSource>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="" DbObjectType="Unknown" GenerateShortCommands="true" GeneratorSourceName="DeleteSeikyuKeisan" Modifier="Public" Name="DeleteSeikyuKeisan" QueryType="NoData" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetDataBy1" UserSourceName="DeleteSeikyuKeisan">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>DELETE  T請求鑑DT
  FROM  T請求鑑DT
 INNER  JOIN  (
               SELECT  T請求鑑HD.請求番号
                 FROM  T請求鑑HD
                WHERE  T請求鑑HD.締日区分       =  @締日区分
                  AND  T請求鑑HD.請求年月       =  @請求年月
                  AND  T請求鑑HD.請求日付自     =  @請求日付F
                  AND  T請求鑑HD.請求日付至     =  @請求日付T
                  AND  T請求鑑HD.得意先コード  IN  (SELECT Items FROM dbo.Split(@選択リスト,','))
              )    AS  _請求対象    ON  _請求対象.請求番号  =  T請求鑑DT.請求番号
</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="締日区分" ColumnName="" DataSourceName="" DataTypeServer="smallint" DbType="Int32" Direction="Input" ParameterName="@締日区分" Precision="0" Scale="0" Size="2" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="請求年月" ColumnName="" DataSourceName="" DataTypeServer="varchar(7)" DbType="AnsiString" Direction="Input" ParameterName="@請求年月" Precision="0" Scale="0" Size="7" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="請求日付F" ColumnName="" DataSourceName="" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@請求日付F" Precision="0" Scale="0" Size="10" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="請求日付T" ColumnName="" DataSourceName="" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@請求日付T" Precision="0" Scale="0" Size="10" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="選択リスト" ColumnName="" DataSourceName="" DataTypeServer="nvarchar" DbType="String" Direction="Input" ParameterName="@選択リスト" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
              </DbSource>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="" DbObjectType="Unknown" GenerateShortCommands="true" GeneratorSourceName="UpdateBiko" Modifier="Public" Name="UpdateBiko" QueryType="NoData" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetDataBy2" UserSourceName="UpdateBiko">
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>UPDATE  T請求鑑DT
   SET  T請求鑑DT.備考 = @備考
  FROM  T請求鑑DT
 WHERE  T請求鑑DT.請求番号  =  @伝票番号
   AND  T請求鑑DT.行番号    =  @行番号
</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="備考" ColumnName="備考" DataSourceName="TokoDB.dbo.T請求鑑DT" DataTypeServer="nvarchar(200)" DbType="String" Direction="Input" ParameterName="@備考" Precision="0" ProviderType="NVarChar" Scale="0" Size="200" SourceColumn="備考" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="伝票番号" ColumnName="請求番号" DataSourceName="TokoDB.dbo.T請求鑑DT" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@伝票番号" Precision="0" ProviderType="VarChar" Scale="0" Size="10" SourceColumn="請求番号" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="行番号" ColumnName="行番号" DataSourceName="TokoDB.dbo.T請求鑑DT" DataTypeServer="decimal(3, 0)" DbType="Decimal" Direction="Input" ParameterName="@行番号" Precision="3" ProviderType="Decimal" Scale="0" Size="5" SourceColumn="行番号" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </Sources>
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="T請求鑑HDTableAdapter" GeneratorDataComponentClassName="T請求鑑HDTableAdapter" Name="T請求鑑HD" UserDataComponentName="T請求鑑HDTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="TokoDB.dbo.T請求鑑HD" DbObjectType="Table" GenerateMethods="Get" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="GetData">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [T請求鑑HD] WHERE (([請求番号] = @Original_請求番号) AND ((@IsNull_得意先コード = 1 AND [得意先コード] IS NULL) OR ([得意先コード] = @Original_得意先コード)) AND ((@IsNull_請求年月 = 1 AND [請求年月] IS NULL) OR ([請求年月] = @Original_請求年月)) AND ((@IsNull_締日区分 = 1 AND [締日区分] IS NULL) OR ([締日区分] = @Original_締日区分)) AND ((@IsNull_請求日付自 = 1 AND [請求日付自] IS NULL) OR ([請求日付自] = @Original_請求日付自)) AND ((@IsNull_請求日付至 = 1 AND [請求日付至] IS NULL) OR ([請求日付至] = @Original_請求日付至)) AND ((@IsNull_前回額 = 1 AND [前回額] IS NULL) OR ([前回額] = @Original_前回額)) AND ((@IsNull_入金額 = 1 AND [入金額] IS NULL) OR ([入金額] = @Original_入金額)) AND ((@IsNull_調整額 = 1 AND [調整額] IS NULL) OR ([調整額] = @Original_調整額)) AND ((@IsNull_繰越額 = 1 AND [繰越額] IS NULL) OR ([繰越額] = @Original_繰越額)) AND ((@IsNull_税抜額 = 1 AND [税抜額] IS NULL) OR ([税抜額] = @Original_税抜額)) AND ((@IsNull_税込額 = 1 AND [税込額] IS NULL) OR ([税込額] = @Original_税込額)) AND ((@IsNull_消費税 = 1 AND [消費税] IS NULL) OR ([消費税] = @Original_消費税)) AND ((@IsNull_請求額 = 1 AND [請求額] IS NULL) OR ([請求額] = @Original_請求額)) AND ((@IsNull_備考 = 1 AND [備考] IS NULL) OR ([備考] = @Original_備考)) AND ((@IsNull_登録日時 = 1 AND [登録日時] IS NULL) OR ([登録日時] = @Original_登録日時)) AND ((@IsNull_登録者ID = 1 AND [登録者ID] IS NULL) OR ([登録者ID] = @Original_登録者ID)) AND ((@IsNull_更新日時 = 1 AND [更新日時] IS NULL) OR ([更新日時] = @Original_更新日時)) AND ((@IsNull_更新者ID = 1 AND [更新者ID] IS NULL) OR ([更新者ID] = @Original_更新者ID)))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_請求番号" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="請求番号" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_得意先コード" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="得意先コード" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_得意先コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="得意先コード" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_請求年月" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="請求年月" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_請求年月" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="請求年月" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_締日区分" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="締日区分" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_締日区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="締日区分" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_請求日付自" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="請求日付自" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_請求日付自" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="請求日付自" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_請求日付至" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="請求日付至" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_請求日付至" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="請求日付至" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_前回額" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="前回額" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_前回額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="前回額" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_入金額" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="入金額" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_入金額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="入金額" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_調整額" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="調整額" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_調整額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="調整額" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_繰越額" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="繰越額" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_繰越額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="繰越額" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_税抜額" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="税抜額" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_税抜額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="税抜額" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_税込額" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="税込額" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_税込額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="税込額" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_消費税" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="消費税" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_消費税" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="消費税" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_請求額" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="請求額" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_請求額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="請求額" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_備考" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="備考" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_備考" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="備考" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_登録日時" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="登録日時" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_登録日時" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="登録日時" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_登録者ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="登録者ID" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_登録者ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="登録者ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_更新日時" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="更新日時" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_更新日時" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="更新日時" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_更新者ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="更新者ID" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_更新者ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="更新者ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [T請求鑑HD] ([請求番号], [得意先コード], [請求年月], [締日区分], [請求日付自], [請求日付至], [前回額], [入金額], [調整額], [繰越額], [税抜額], [税込額], [消費税], [請求額], [備考], [登録日時], [登録者ID], [更新日時], [更新者ID]) VALUES (@請求番号, @得意先コード, @請求年月, @締日区分, @請求日付自, @請求日付至, @前回額, @入金額, @調整額, @繰越額, @税抜額, @税込額, @消費税, @請求額, @備考, @登録日時, @登録者ID, @更新日時, @更新者ID);
SELECT 請求番号, 得意先コード, 請求年月, 締日区分, 請求日付自, 請求日付至, 前回額, 入金額, 調整額, 繰越額, 税抜額, 税込額, 消費税, 請求額, 備考, 登録日時, 登録者ID, 更新日時, 更新者ID FROM T請求鑑HD WHERE (請求番号 = @請求番号)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@請求番号" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="請求番号" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@得意先コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="得意先コード" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@請求年月" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="請求年月" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@締日区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="締日区分" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@請求日付自" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="請求日付自" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@請求日付至" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="請求日付至" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@前回額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="前回額" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@入金額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="入金額" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@調整額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="調整額" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@繰越額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="繰越額" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@税抜額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="税抜額" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@税込額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="税込額" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@消費税" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="消費税" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@請求額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="請求額" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@備考" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="備考" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@登録日時" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="登録日時" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@登録者ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="登録者ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@更新日時" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="更新日時" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@更新者ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="更新者ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT                      T請求鑑HD.*
FROM                         T請求鑑HD
WHERE                       (請求番号 = @請求番号)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="請求番号" ColumnName="請求番号" DataSourceName="TokoDB.dbo.T請求鑑HD" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@請求番号" Precision="0" ProviderType="VarChar" Scale="0" Size="10" SourceColumn="請求番号" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [T請求鑑HD] SET [請求番号] = @請求番号, [得意先コード] = @得意先コード, [請求年月] = @請求年月, [締日区分] = @締日区分, [請求日付自] = @請求日付自, [請求日付至] = @請求日付至, [前回額] = @前回額, [入金額] = @入金額, [調整額] = @調整額, [繰越額] = @繰越額, [税抜額] = @税抜額, [税込額] = @税込額, [消費税] = @消費税, [請求額] = @請求額, [備考] = @備考, [登録日時] = @登録日時, [登録者ID] = @登録者ID, [更新日時] = @更新日時, [更新者ID] = @更新者ID WHERE (([請求番号] = @Original_請求番号) AND ((@IsNull_得意先コード = 1 AND [得意先コード] IS NULL) OR ([得意先コード] = @Original_得意先コード)) AND ((@IsNull_請求年月 = 1 AND [請求年月] IS NULL) OR ([請求年月] = @Original_請求年月)) AND ((@IsNull_締日区分 = 1 AND [締日区分] IS NULL) OR ([締日区分] = @Original_締日区分)) AND ((@IsNull_請求日付自 = 1 AND [請求日付自] IS NULL) OR ([請求日付自] = @Original_請求日付自)) AND ((@IsNull_請求日付至 = 1 AND [請求日付至] IS NULL) OR ([請求日付至] = @Original_請求日付至)) AND ((@IsNull_前回額 = 1 AND [前回額] IS NULL) OR ([前回額] = @Original_前回額)) AND ((@IsNull_入金額 = 1 AND [入金額] IS NULL) OR ([入金額] = @Original_入金額)) AND ((@IsNull_調整額 = 1 AND [調整額] IS NULL) OR ([調整額] = @Original_調整額)) AND ((@IsNull_繰越額 = 1 AND [繰越額] IS NULL) OR ([繰越額] = @Original_繰越額)) AND ((@IsNull_税抜額 = 1 AND [税抜額] IS NULL) OR ([税抜額] = @Original_税抜額)) AND ((@IsNull_税込額 = 1 AND [税込額] IS NULL) OR ([税込額] = @Original_税込額)) AND ((@IsNull_消費税 = 1 AND [消費税] IS NULL) OR ([消費税] = @Original_消費税)) AND ((@IsNull_請求額 = 1 AND [請求額] IS NULL) OR ([請求額] = @Original_請求額)) AND ((@IsNull_備考 = 1 AND [備考] IS NULL) OR ([備考] = @Original_備考)) AND ((@IsNull_登録日時 = 1 AND [登録日時] IS NULL) OR ([登録日時] = @Original_登録日時)) AND ((@IsNull_登録者ID = 1 AND [登録者ID] IS NULL) OR ([登録者ID] = @Original_登録者ID)) AND ((@IsNull_更新日時 = 1 AND [更新日時] IS NULL) OR ([更新日時] = @Original_更新日時)) AND ((@IsNull_更新者ID = 1 AND [更新者ID] IS NULL) OR ([更新者ID] = @Original_更新者ID)));
SELECT 請求番号, 得意先コード, 請求年月, 締日区分, 請求日付自, 請求日付至, 前回額, 入金額, 調整額, 繰越額, 税抜額, 税込額, 消費税, 請求額, 備考, 登録日時, 登録者ID, 更新日時, 更新者ID FROM T請求鑑HD WHERE (請求番号 = @請求番号)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@請求番号" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="請求番号" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@得意先コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="得意先コード" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@請求年月" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="請求年月" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@締日区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="締日区分" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@請求日付自" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="請求日付自" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@請求日付至" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="請求日付至" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@前回額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="前回額" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@入金額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="入金額" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@調整額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="調整額" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@繰越額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="繰越額" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@税抜額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="税抜額" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@税込額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="税込額" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@消費税" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="消費税" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@請求額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="請求額" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@備考" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="備考" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@登録日時" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="登録日時" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@登録者ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="登録者ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@更新日時" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="更新日時" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@更新者ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="更新者ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_請求番号" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="請求番号" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_得意先コード" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="得意先コード" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_得意先コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="得意先コード" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_請求年月" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="請求年月" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_請求年月" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="請求年月" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_締日区分" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="締日区分" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_締日区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="締日区分" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_請求日付自" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="請求日付自" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_請求日付自" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="請求日付自" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_請求日付至" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="請求日付至" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_請求日付至" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="請求日付至" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_前回額" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="前回額" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_前回額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="前回額" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_入金額" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="入金額" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_入金額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="入金額" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_調整額" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="調整額" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_調整額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="調整額" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_繰越額" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="繰越額" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_繰越額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="繰越額" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_税抜額" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="税抜額" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_税抜額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="税抜額" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_税込額" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="税込額" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_税込額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="税込額" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_消費税" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="消費税" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_消費税" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="消費税" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_請求額" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="請求額" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_請求額" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="請求額" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_備考" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="備考" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_備考" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="備考" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_登録日時" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="登録日時" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_登録日時" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="登録日時" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_登録者ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="登録者ID" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_登録者ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="登録者ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_更新日時" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="更新日時" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_更新日時" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="更新日時" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_更新者ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="更新者ID" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_更新者ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="更新者ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="請求番号" DataSetColumn="請求番号" />
              <Mapping SourceColumn="得意先コード" DataSetColumn="得意先コード" />
              <Mapping SourceColumn="請求年月" DataSetColumn="請求年月" />
              <Mapping SourceColumn="締日区分" DataSetColumn="締日区分" />
              <Mapping SourceColumn="請求日付自" DataSetColumn="請求日付自" />
              <Mapping SourceColumn="請求日付至" DataSetColumn="請求日付至" />
              <Mapping SourceColumn="前回額" DataSetColumn="前回額" />
              <Mapping SourceColumn="入金額" DataSetColumn="入金額" />
              <Mapping SourceColumn="調整額" DataSetColumn="調整額" />
              <Mapping SourceColumn="繰越額" DataSetColumn="繰越額" />
              <Mapping SourceColumn="税抜額" DataSetColumn="税抜額" />
              <Mapping SourceColumn="税込額" DataSetColumn="税込額" />
              <Mapping SourceColumn="消費税" DataSetColumn="消費税" />
              <Mapping SourceColumn="請求額" DataSetColumn="請求額" />
              <Mapping SourceColumn="備考" DataSetColumn="備考" />
              <Mapping SourceColumn="登録日時" DataSetColumn="登録日時" />
              <Mapping SourceColumn="登録者ID" DataSetColumn="登録者ID" />
              <Mapping SourceColumn="更新日時" DataSetColumn="更新日時" />
              <Mapping SourceColumn="更新者ID" DataSetColumn="更新者ID" />
            </Mappings>
            <Sources>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="" DbObjectType="Unknown" GenerateShortCommands="true" GeneratorSourceName="DeleteByCode" Modifier="Public" Name="DeleteByCode" QueryType="NoData" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetDataBy" UserSourceName="DeleteByCode">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>DELETE FROM [T請求鑑HD] WHERE [請求番号] = @請求番号</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="請求番号" ColumnName="請求番号" DataSourceName="TokoDB.dbo.T請求鑑HD" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@請求番号" Precision="0" ProviderType="VarChar" Scale="0" Size="10" SourceColumn="請求番号" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
              </DbSource>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="" DbObjectType="Unknown" GenerateShortCommands="true" GeneratorSourceName="DeleteSeikyuKeisan" Modifier="Public" Name="DeleteSeikyuKeisan" QueryType="NoData" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetDataBy1" UserSourceName="DeleteSeikyuKeisan">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>DELETE
  FROM  T請求鑑HD
 WHERE  T請求鑑HD.締日区分       =  @締日区分
   AND  T請求鑑HD.請求年月       =  @請求年月
   AND  T請求鑑HD.請求日付自     =  @請求日付F
   AND  T請求鑑HD.請求日付至     =  @請求日付T
   AND  T請求鑑HD.得意先コード  IN  (SELECT Items FROM dbo.Split(@選択リスト,','))
</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="締日区分" ColumnName="締日区分" DataSourceName="TokoDB.dbo.T請求鑑HD" DataTypeServer="smallint" DbType="Int16" Direction="Input" ParameterName="@締日区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="2" SourceColumn="締日区分" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="請求年月" ColumnName="請求年月" DataSourceName="TokoDB.dbo.T請求鑑HD" DataTypeServer="varchar(7)" DbType="AnsiString" Direction="Input" ParameterName="@請求年月" Precision="0" ProviderType="VarChar" Scale="0" Size="7" SourceColumn="請求年月" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="請求日付F" ColumnName="請求日付自" DataSourceName="TokoDB.dbo.T請求鑑HD" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@請求日付F" Precision="0" ProviderType="VarChar" Scale="0" Size="10" SourceColumn="請求日付自" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="請求日付T" ColumnName="請求日付至" DataSourceName="TokoDB.dbo.T請求鑑HD" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@請求日付T" Precision="0" ProviderType="VarChar" Scale="0" Size="10" SourceColumn="請求日付至" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="選択リスト" ColumnName="" DataSourceName="" DataTypeServer="nvarchar" DbType="String" Direction="Input" ParameterName="@選択リスト" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
              </DbSource>
            </Sources>
          </TableAdapter>
        </Tables>
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="DbTran" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:EnableTableAdapterManager="true" msprop:Generator_DataSetName="DbTran" msprop:Generator_UserDSName="DbTran">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="T入金HD" msprop:Generator_TableClassName="T入金HDDataTable" msprop:Generator_TableVarName="tableT入金HD" msprop:Generator_TablePropName="T入金HD" msprop:Generator_RowDeletingName="T入金HDRowDeleting" msprop:Generator_RowChangingName="T入金HDRowChanging" msprop:Generator_RowEvHandlerName="T入金HDRowChangeEventHandler" msprop:Generator_RowDeletedName="T入金HDRowDeleted" msprop:Generator_UserTableName="T入金HD" msprop:Generator_RowChangedName="T入金HDRowChanged" msprop:Generator_RowEvArgName="T入金HDRowChangeEvent" msprop:Generator_RowClassName="T入金HDRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="入金番号" msprop:Generator_ColumnVarNameInTable="column入金番号" msprop:Generator_ColumnPropNameInRow="入金番号" msprop:Generator_ColumnPropNameInTable="入金番号Column" msprop:Generator_UserColumnName="入金番号">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="入金日付" msprop:Generator_ColumnVarNameInTable="column入金日付" msprop:Generator_ColumnPropNameInRow="入金日付" msprop:Generator_ColumnPropNameInTable="入金日付Column" msprop:Generator_UserColumnName="入金日付" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="請求年月" msprop:Generator_ColumnVarNameInTable="column請求年月" msprop:Generator_ColumnPropNameInRow="請求年月" msprop:Generator_ColumnPropNameInTable="請求年月Column" msprop:Generator_UserColumnName="請求年月" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="7" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="得意先コード" msprop:Generator_ColumnVarNameInTable="column得意先コード" msprop:Generator_ColumnPropNameInRow="得意先コード" msprop:Generator_ColumnPropNameInTable="得意先コードColumn" msprop:Generator_UserColumnName="得意先コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="工事コード" msprop:Generator_ColumnVarNameInTable="column工事コード" msprop:Generator_ColumnPropNameInRow="工事コード" msprop:Generator_ColumnPropNameInTable="工事コードColumn" msprop:Generator_UserColumnName="工事コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="登録日時" msprop:Generator_ColumnVarNameInTable="column登録日時" msprop:Generator_ColumnPropNameInRow="登録日時" msprop:Generator_ColumnPropNameInTable="登録日時Column" msprop:Generator_UserColumnName="登録日時" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="登録者ID" msprop:Generator_ColumnVarNameInTable="column登録者ID" msprop:Generator_ColumnPropNameInRow="登録者ID" msprop:Generator_ColumnPropNameInTable="登録者IDColumn" msprop:Generator_UserColumnName="登録者ID" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="更新日時" msprop:Generator_ColumnVarNameInTable="column更新日時" msprop:Generator_ColumnPropNameInRow="更新日時" msprop:Generator_ColumnPropNameInTable="更新日時Column" msprop:Generator_UserColumnName="更新日時" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="更新者ID" msprop:Generator_ColumnVarNameInTable="column更新者ID" msprop:Generator_ColumnPropNameInRow="更新者ID" msprop:Generator_ColumnPropNameInTable="更新者IDColumn" msprop:Generator_UserColumnName="更新者ID" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="工事内部コード" msprop:Generator_ColumnVarNameInTable="column工事内部コード" msprop:Generator_ColumnPropNameInRow="工事内部コード" msprop:Generator_ColumnPropNameInTable="工事内部コードColumn" msprop:Generator_UserColumnName="工事内部コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="請求番号" msprop:Generator_ColumnVarNameInTable="column請求番号" msprop:Generator_ColumnPropNameInRow="請求番号" msprop:Generator_ColumnPropNameInTable="請求番号Column" msprop:Generator_UserColumnName="請求番号" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="T入金DT" msprop:Generator_TableClassName="T入金DTDataTable" msprop:Generator_TableVarName="tableT入金DT" msprop:Generator_TablePropName="T入金DT" msprop:Generator_RowDeletingName="T入金DTRowDeleting" msprop:Generator_RowChangingName="T入金DTRowChanging" msprop:Generator_RowEvHandlerName="T入金DTRowChangeEventHandler" msprop:Generator_RowDeletedName="T入金DTRowDeleted" msprop:Generator_UserTableName="T入金DT" msprop:Generator_RowChangedName="T入金DTRowChanged" msprop:Generator_RowEvArgName="T入金DTRowChangeEvent" msprop:Generator_RowClassName="T入金DTRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="入金番号" msprop:Generator_ColumnVarNameInTable="column入金番号" msprop:Generator_ColumnPropNameInRow="入金番号" msprop:Generator_ColumnPropNameInTable="入金番号Column" msprop:Generator_UserColumnName="入金番号">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="行番号" msprop:Generator_ColumnVarNameInTable="column行番号" msprop:Generator_ColumnPropNameInRow="行番号" msprop:Generator_ColumnPropNameInTable="行番号Column" msprop:Generator_UserColumnName="行番号" type="xs:decimal" />
              <xs:element name="入金区分" msprop:Generator_ColumnVarNameInTable="column入金区分" msprop:Generator_ColumnPropNameInRow="入金区分" msprop:Generator_ColumnPropNameInTable="入金区分Column" msprop:Generator_UserColumnName="入金区分" type="xs:short" minOccurs="0" />
              <xs:element name="入金金額" msprop:Generator_ColumnVarNameInTable="column入金金額" msprop:Generator_ColumnPropNameInRow="入金金額" msprop:Generator_ColumnPropNameInTable="入金金額Column" msprop:Generator_UserColumnName="入金金額" type="xs:decimal" minOccurs="0" />
              <xs:element name="備考" msprop:Generator_ColumnVarNameInTable="column備考" msprop:Generator_ColumnPropNameInRow="備考" msprop:Generator_ColumnPropNameInTable="備考Column" msprop:Generator_UserColumnName="備考" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="T売上DT" msprop:Generator_TableClassName="T売上DTDataTable" msprop:Generator_TableVarName="tableT売上DT" msprop:Generator_RowChangedName="T売上DTRowChanged" msprop:Generator_TablePropName="T売上DT" msprop:Generator_RowDeletingName="T売上DTRowDeleting" msprop:Generator_RowChangingName="T売上DTRowChanging" msprop:Generator_RowEvHandlerName="T売上DTRowChangeEventHandler" msprop:Generator_RowDeletedName="T売上DTRowDeleted" msprop:Generator_RowClassName="T売上DTRow" msprop:Generator_UserTableName="T売上DT" msprop:Generator_RowEvArgName="T売上DTRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="売上番号" msprop:Generator_ColumnVarNameInTable="column売上番号" msprop:Generator_ColumnPropNameInRow="売上番号" msprop:Generator_ColumnPropNameInTable="売上番号Column" msprop:Generator_UserColumnName="売上番号">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="行番号" msprop:Generator_ColumnVarNameInTable="column行番号" msprop:Generator_ColumnPropNameInRow="行番号" msprop:Generator_ColumnPropNameInTable="行番号Column" msprop:Generator_UserColumnName="行番号" type="xs:decimal" />
              <xs:element name="計算区分" msprop:Generator_ColumnVarNameInTable="column計算区分" msprop:Generator_ColumnPropNameInRow="計算区分" msprop:Generator_ColumnPropNameInTable="計算区分Column" msprop:Generator_UserColumnName="計算区分" type="xs:short" minOccurs="0" />
              <xs:element name="商品コード" msprop:Generator_ColumnVarNameInTable="column商品コード" msprop:Generator_ColumnPropNameInRow="商品コード" msprop:Generator_ColumnPropNameInTable="商品コードColumn" msprop:Generator_UserColumnName="商品コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="商品名" msprop:Generator_ColumnVarNameInTable="column商品名" msprop:Generator_ColumnPropNameInRow="商品名" msprop:Generator_ColumnPropNameInTable="商品名Column" msprop:Generator_UserColumnName="商品名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="数量" msprop:Generator_ColumnVarNameInTable="column数量" msprop:Generator_ColumnPropNameInRow="数量" msprop:Generator_ColumnPropNameInTable="数量Column" msprop:Generator_UserColumnName="数量" type="xs:decimal" minOccurs="0" />
              <xs:element name="単価" msprop:Generator_ColumnVarNameInTable="column単価" msprop:Generator_ColumnPropNameInRow="単価" msprop:Generator_ColumnPropNameInTable="単価Column" msprop:Generator_UserColumnName="単価" type="xs:decimal" minOccurs="0" />
              <xs:element name="金額" msprop:Generator_ColumnVarNameInTable="column金額" msprop:Generator_ColumnPropNameInRow="金額" msprop:Generator_ColumnPropNameInTable="金額Column" msprop:Generator_UserColumnName="金額" type="xs:decimal" minOccurs="0" />
              <xs:element name="単位区分" msprop:Generator_ColumnVarNameInTable="column単位区分" msprop:Generator_ColumnPropNameInRow="単位区分" msprop:Generator_ColumnPropNameInTable="単位区分Column" msprop:Generator_UserColumnName="単位区分" type="xs:short" minOccurs="0" />
              <xs:element name="備考" msprop:Generator_ColumnVarNameInTable="column備考" msprop:Generator_ColumnPropNameInRow="備考" msprop:Generator_ColumnPropNameInTable="備考Column" msprop:Generator_UserColumnName="備考" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="搬入番号" msprop:Generator_ColumnVarNameInTable="column搬入番号" msprop:Generator_ColumnPropNameInRow="搬入番号" msprop:Generator_ColumnPropNameInTable="搬入番号Column" msprop:Generator_UserColumnName="搬入番号" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="開始日付" msprop:Generator_ColumnVarNameInTable="column開始日付" msprop:Generator_ColumnPropNameInRow="開始日付" msprop:Generator_ColumnPropNameInTable="開始日付Column" msprop:Generator_UserColumnName="開始日付" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="終了日付" msprop:Generator_ColumnVarNameInTable="column終了日付" msprop:Generator_ColumnPropNameInRow="終了日付" msprop:Generator_ColumnPropNameInTable="終了日付Column" msprop:Generator_UserColumnName="終了日付" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="返納日付" msprop:Generator_ColumnVarNameInTable="column返納日付" msprop:Generator_ColumnPropNameInRow="返納日付" msprop:Generator_ColumnPropNameInTable="返納日付Column" msprop:Generator_UserColumnName="返納日付" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="備考明細" msprop:Generator_ColumnVarNameInTable="column備考明細" msprop:Generator_ColumnPropNameInRow="備考明細" msprop:Generator_ColumnPropNameInTable="備考明細Column" msprop:Generator_UserColumnName="備考明細" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="備考社内" msprop:Generator_ColumnVarNameInTable="column備考社内" msprop:Generator_ColumnPropNameInRow="備考社内" msprop:Generator_ColumnPropNameInTable="備考社内Column" msprop:Generator_UserColumnName="備考社内" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="基本料金" msprop:Generator_ColumnVarNameInTable="column基本料金" msprop:Generator_ColumnPropNameInRow="基本料金" msprop:Generator_ColumnPropNameInTable="基本料金Column" msprop:Generator_UserColumnName="基本料金" type="xs:decimal" minOccurs="0" />
              <xs:element name="備考台帳" msprop:Generator_ColumnVarNameInTable="column備考台帳" msprop:Generator_ColumnPropNameInRow="備考台帳" msprop:Generator_ColumnPropNameInTable="備考台帳Column" msprop:Generator_UserColumnName="備考台帳" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="T売上HD" msprop:Generator_TableClassName="T売上HDDataTable" msprop:Generator_TableVarName="tableT売上HD" msprop:Generator_RowChangedName="T売上HDRowChanged" msprop:Generator_TablePropName="T売上HD" msprop:Generator_RowDeletingName="T売上HDRowDeleting" msprop:Generator_RowChangingName="T売上HDRowChanging" msprop:Generator_RowEvHandlerName="T売上HDRowChangeEventHandler" msprop:Generator_RowDeletedName="T売上HDRowDeleted" msprop:Generator_RowClassName="T売上HDRow" msprop:Generator_UserTableName="T売上HD" msprop:Generator_RowEvArgName="T売上HDRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="売上番号" msprop:Generator_ColumnVarNameInTable="column売上番号" msprop:Generator_ColumnPropNameInRow="売上番号" msprop:Generator_ColumnPropNameInTable="売上番号Column" msprop:Generator_UserColumnName="売上番号">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="搬入日付" msprop:Generator_ColumnVarNameInTable="column搬入日付" msprop:Generator_ColumnPropNameInRow="搬入日付" msprop:Generator_ColumnPropNameInTable="搬入日付Column" msprop:Generator_UserColumnName="搬入日付" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="伝票区分" msprop:Generator_ColumnVarNameInTable="column伝票区分" msprop:Generator_ColumnPropNameInRow="伝票区分" msprop:Generator_ColumnPropNameInTable="伝票区分Column" msprop:Generator_UserColumnName="伝票区分" type="xs:short" minOccurs="0" />
              <xs:element name="得意先コード" msprop:Generator_ColumnVarNameInTable="column得意先コード" msprop:Generator_ColumnPropNameInRow="得意先コード" msprop:Generator_ColumnPropNameInTable="得意先コードColumn" msprop:Generator_UserColumnName="得意先コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="工事コード" msprop:Generator_ColumnVarNameInTable="column工事コード" msprop:Generator_ColumnPropNameInRow="工事コード" msprop:Generator_ColumnPropNameInTable="工事コードColumn" msprop:Generator_UserColumnName="工事コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="摘要1" msprop:Generator_ColumnVarNameInTable="column摘要1" msprop:Generator_ColumnPropNameInRow="摘要1" msprop:Generator_ColumnPropNameInTable="摘要1Column" msprop:Generator_UserColumnName="摘要1" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="摘要2" msprop:Generator_ColumnVarNameInTable="column摘要2" msprop:Generator_ColumnPropNameInRow="摘要2" msprop:Generator_ColumnPropNameInTable="摘要2Column" msprop:Generator_UserColumnName="摘要2" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="登録日時" msprop:Generator_ColumnVarNameInTable="column登録日時" msprop:Generator_ColumnPropNameInRow="登録日時" msprop:Generator_ColumnPropNameInTable="登録日時Column" msprop:Generator_UserColumnName="登録日時" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="登録者ID" msprop:Generator_ColumnVarNameInTable="column登録者ID" msprop:Generator_ColumnPropNameInRow="登録者ID" msprop:Generator_ColumnPropNameInTable="登録者IDColumn" msprop:Generator_UserColumnName="登録者ID" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="更新日時" msprop:Generator_ColumnVarNameInTable="column更新日時" msprop:Generator_ColumnPropNameInRow="更新日時" msprop:Generator_ColumnPropNameInTable="更新日時Column" msprop:Generator_UserColumnName="更新日時" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="更新者ID" msprop:Generator_ColumnVarNameInTable="column更新者ID" msprop:Generator_ColumnPropNameInRow="更新者ID" msprop:Generator_ColumnPropNameInTable="更新者IDColumn" msprop:Generator_UserColumnName="更新者ID" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="工事内部コード" msprop:Generator_ColumnVarNameInTable="column工事内部コード" msprop:Generator_ColumnPropNameInRow="工事内部コード" msprop:Generator_ColumnPropNameInTable="工事内部コードColumn" msprop:Generator_UserColumnName="工事内部コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="備考" msprop:Generator_ColumnVarNameInTable="column備考" msprop:Generator_ColumnPropNameInRow="備考" msprop:Generator_ColumnPropNameInTable="備考Column" msprop:Generator_UserColumnName="備考" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="伝票発行区分" msprop:Generator_ColumnVarNameInTable="column伝票発行区分" msprop:Generator_ColumnPropNameInRow="伝票発行区分" msprop:Generator_ColumnPropNameInTable="伝票発行区分Column" msprop:Generator_UserColumnName="伝票発行区分" type="xs:short" minOccurs="0" />
              <xs:element name="請求番号" msprop:Generator_ColumnVarNameInTable="column請求番号" msprop:Generator_ColumnPropNameInRow="請求番号" msprop:Generator_ColumnPropNameInTable="請求番号Column" msprop:Generator_UserColumnName="請求番号" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="T請求鑑DT" msprop:Generator_TableClassName="T請求鑑DTDataTable" msprop:Generator_TableVarName="tableT請求鑑DT" msprop:Generator_TablePropName="T請求鑑DT" msprop:Generator_RowDeletingName="T請求鑑DTRowDeleting" msprop:Generator_RowChangingName="T請求鑑DTRowChanging" msprop:Generator_RowEvHandlerName="T請求鑑DTRowChangeEventHandler" msprop:Generator_RowDeletedName="T請求鑑DTRowDeleted" msprop:Generator_UserTableName="T請求鑑DT" msprop:Generator_RowChangedName="T請求鑑DTRowChanged" msprop:Generator_RowEvArgName="T請求鑑DTRowChangeEvent" msprop:Generator_RowClassName="T請求鑑DTRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="請求番号" msprop:Generator_ColumnVarNameInTable="column請求番号" msprop:Generator_ColumnPropNameInRow="請求番号" msprop:Generator_ColumnPropNameInTable="請求番号Column" msprop:Generator_UserColumnName="請求番号">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="行番号" msprop:Generator_ColumnVarNameInTable="column行番号" msprop:Generator_ColumnPropNameInRow="行番号" msprop:Generator_ColumnPropNameInTable="行番号Column" msprop:Generator_UserColumnName="行番号" type="xs:decimal" />
              <xs:element name="工事内部コード" msprop:Generator_ColumnVarNameInTable="column工事内部コード" msprop:Generator_ColumnPropNameInRow="工事内部コード" msprop:Generator_ColumnPropNameInTable="工事内部コードColumn" msprop:Generator_UserColumnName="工事内部コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="前回額" msprop:Generator_ColumnVarNameInTable="column前回額" msprop:Generator_ColumnPropNameInRow="前回額" msprop:Generator_ColumnPropNameInTable="前回額Column" msprop:Generator_UserColumnName="前回額" type="xs:decimal" minOccurs="0" />
              <xs:element name="入金額" msprop:Generator_ColumnVarNameInTable="column入金額" msprop:Generator_ColumnPropNameInRow="入金額" msprop:Generator_ColumnPropNameInTable="入金額Column" msprop:Generator_UserColumnName="入金額" type="xs:decimal" minOccurs="0" />
              <xs:element name="調整額" msprop:Generator_ColumnVarNameInTable="column調整額" msprop:Generator_ColumnPropNameInRow="調整額" msprop:Generator_ColumnPropNameInTable="調整額Column" msprop:Generator_UserColumnName="調整額" type="xs:decimal" minOccurs="0" />
              <xs:element name="繰越額" msprop:Generator_ColumnVarNameInTable="column繰越額" msprop:Generator_ColumnPropNameInRow="繰越額" msprop:Generator_ColumnPropNameInTable="繰越額Column" msprop:Generator_UserColumnName="繰越額" type="xs:decimal" minOccurs="0" />
              <xs:element name="税抜額" msprop:Generator_ColumnVarNameInTable="column税抜額" msprop:Generator_ColumnPropNameInRow="税抜額" msprop:Generator_ColumnPropNameInTable="税抜額Column" msprop:Generator_UserColumnName="税抜額" type="xs:decimal" minOccurs="0" />
              <xs:element name="税込額" msprop:Generator_ColumnVarNameInTable="column税込額" msprop:Generator_ColumnPropNameInRow="税込額" msprop:Generator_ColumnPropNameInTable="税込額Column" msprop:Generator_UserColumnName="税込額" type="xs:decimal" minOccurs="0" />
              <xs:element name="消費税" msprop:Generator_ColumnVarNameInTable="column消費税" msprop:Generator_ColumnPropNameInRow="消費税" msprop:Generator_ColumnPropNameInTable="消費税Column" msprop:Generator_UserColumnName="消費税" type="xs:decimal" minOccurs="0" />
              <xs:element name="請求額" msprop:Generator_ColumnVarNameInTable="column請求額" msprop:Generator_ColumnPropNameInRow="請求額" msprop:Generator_ColumnPropNameInTable="請求額Column" msprop:Generator_UserColumnName="請求額" type="xs:decimal" minOccurs="0" />
              <xs:element name="備考" msprop:Generator_ColumnVarNameInTable="column備考" msprop:Generator_ColumnPropNameInRow="備考" msprop:Generator_ColumnPropNameInTable="備考Column" msprop:Generator_UserColumnName="備考" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="得意先コード" msprop:Generator_ColumnVarNameInTable="column得意先コード" msprop:Generator_ColumnPropNameInRow="得意先コード" msprop:Generator_ColumnPropNameInTable="得意先コードColumn" msprop:Generator_UserColumnName="得意先コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="工事コード" msprop:Generator_ColumnVarNameInTable="column工事コード" msprop:Generator_ColumnPropNameInRow="工事コード" msprop:Generator_ColumnPropNameInTable="工事コードColumn" msprop:Generator_UserColumnName="工事コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="T請求鑑HD" msprop:Generator_TableClassName="T請求鑑HDDataTable" msprop:Generator_TableVarName="tableT請求鑑HD" msprop:Generator_TablePropName="T請求鑑HD" msprop:Generator_RowDeletingName="T請求鑑HDRowDeleting" msprop:Generator_RowChangingName="T請求鑑HDRowChanging" msprop:Generator_RowEvHandlerName="T請求鑑HDRowChangeEventHandler" msprop:Generator_RowDeletedName="T請求鑑HDRowDeleted" msprop:Generator_UserTableName="T請求鑑HD" msprop:Generator_RowChangedName="T請求鑑HDRowChanged" msprop:Generator_RowEvArgName="T請求鑑HDRowChangeEvent" msprop:Generator_RowClassName="T請求鑑HDRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="請求番号" msprop:Generator_ColumnVarNameInTable="column請求番号" msprop:Generator_ColumnPropNameInRow="請求番号" msprop:Generator_ColumnPropNameInTable="請求番号Column" msprop:Generator_UserColumnName="請求番号">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="得意先コード" msprop:Generator_ColumnVarNameInTable="column得意先コード" msprop:Generator_ColumnPropNameInRow="得意先コード" msprop:Generator_ColumnPropNameInTable="得意先コードColumn" msprop:Generator_UserColumnName="得意先コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="請求年月" msprop:Generator_ColumnVarNameInTable="column請求年月" msprop:Generator_ColumnPropNameInRow="請求年月" msprop:Generator_ColumnPropNameInTable="請求年月Column" msprop:Generator_UserColumnName="請求年月" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="7" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="締日区分" msprop:Generator_ColumnVarNameInTable="column締日区分" msprop:Generator_ColumnPropNameInRow="締日区分" msprop:Generator_ColumnPropNameInTable="締日区分Column" msprop:Generator_UserColumnName="締日区分" type="xs:short" minOccurs="0" />
              <xs:element name="請求日付自" msprop:Generator_ColumnVarNameInTable="column請求日付自" msprop:Generator_ColumnPropNameInRow="請求日付自" msprop:Generator_ColumnPropNameInTable="請求日付自Column" msprop:Generator_UserColumnName="請求日付自" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="請求日付至" msprop:Generator_ColumnVarNameInTable="column請求日付至" msprop:Generator_ColumnPropNameInRow="請求日付至" msprop:Generator_ColumnPropNameInTable="請求日付至Column" msprop:Generator_UserColumnName="請求日付至" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="前回額" msprop:Generator_ColumnVarNameInTable="column前回額" msprop:Generator_ColumnPropNameInRow="前回額" msprop:Generator_ColumnPropNameInTable="前回額Column" msprop:Generator_UserColumnName="前回額" type="xs:decimal" minOccurs="0" />
              <xs:element name="入金額" msprop:Generator_ColumnVarNameInTable="column入金額" msprop:Generator_ColumnPropNameInRow="入金額" msprop:Generator_ColumnPropNameInTable="入金額Column" msprop:Generator_UserColumnName="入金額" type="xs:decimal" minOccurs="0" />
              <xs:element name="調整額" msprop:Generator_ColumnVarNameInTable="column調整額" msprop:Generator_ColumnPropNameInRow="調整額" msprop:Generator_ColumnPropNameInTable="調整額Column" msprop:Generator_UserColumnName="調整額" type="xs:decimal" minOccurs="0" />
              <xs:element name="繰越額" msprop:Generator_ColumnVarNameInTable="column繰越額" msprop:Generator_ColumnPropNameInRow="繰越額" msprop:Generator_ColumnPropNameInTable="繰越額Column" msprop:Generator_UserColumnName="繰越額" type="xs:decimal" minOccurs="0" />
              <xs:element name="税抜額" msprop:Generator_ColumnVarNameInTable="column税抜額" msprop:Generator_ColumnPropNameInRow="税抜額" msprop:Generator_ColumnPropNameInTable="税抜額Column" msprop:Generator_UserColumnName="税抜額" type="xs:decimal" minOccurs="0" />
              <xs:element name="税込額" msprop:Generator_ColumnVarNameInTable="column税込額" msprop:Generator_ColumnPropNameInRow="税込額" msprop:Generator_ColumnPropNameInTable="税込額Column" msprop:Generator_UserColumnName="税込額" type="xs:decimal" minOccurs="0" />
              <xs:element name="消費税" msprop:Generator_ColumnVarNameInTable="column消費税" msprop:Generator_ColumnPropNameInRow="消費税" msprop:Generator_ColumnPropNameInTable="消費税Column" msprop:Generator_UserColumnName="消費税" type="xs:decimal" minOccurs="0" />
              <xs:element name="請求額" msprop:Generator_ColumnVarNameInTable="column請求額" msprop:Generator_ColumnPropNameInRow="請求額" msprop:Generator_ColumnPropNameInTable="請求額Column" msprop:Generator_UserColumnName="請求額" type="xs:decimal" minOccurs="0" />
              <xs:element name="備考" msprop:Generator_ColumnVarNameInTable="column備考" msprop:Generator_ColumnPropNameInRow="備考" msprop:Generator_ColumnPropNameInTable="備考Column" msprop:Generator_UserColumnName="備考" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="登録日時" msprop:Generator_ColumnVarNameInTable="column登録日時" msprop:Generator_ColumnPropNameInRow="登録日時" msprop:Generator_ColumnPropNameInTable="登録日時Column" msprop:Generator_UserColumnName="登録日時" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="登録者ID" msprop:Generator_ColumnVarNameInTable="column登録者ID" msprop:Generator_ColumnPropNameInRow="登録者ID" msprop:Generator_ColumnPropNameInTable="登録者IDColumn" msprop:Generator_UserColumnName="登録者ID" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="更新日時" msprop:Generator_ColumnVarNameInTable="column更新日時" msprop:Generator_ColumnPropNameInRow="更新日時" msprop:Generator_ColumnPropNameInTable="更新日時Column" msprop:Generator_UserColumnName="更新日時" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="更新者ID" msprop:Generator_ColumnVarNameInTable="column更新者ID" msprop:Generator_ColumnPropNameInRow="更新者ID" msprop:Generator_ColumnPropNameInTable="更新者IDColumn" msprop:Generator_UserColumnName="更新者ID" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
    <xs:unique name="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:T入金HD" />
      <xs:field xpath="mstns:入金番号" />
    </xs:unique>
    <xs:unique name="T入金DT_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:T入金DT" />
      <xs:field xpath="mstns:入金番号" />
      <xs:field xpath="mstns:行番号" />
    </xs:unique>
    <xs:unique name="T売上DT_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:T売上DT" />
      <xs:field xpath="mstns:売上番号" />
      <xs:field xpath="mstns:行番号" />
    </xs:unique>
    <xs:unique name="T売上HD_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:T売上HD" />
      <xs:field xpath="mstns:売上番号" />
    </xs:unique>
    <xs:unique name="T請求鑑DT_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:T請求鑑DT" />
      <xs:field xpath="mstns:請求番号" />
      <xs:field xpath="mstns:行番号" />
    </xs:unique>
    <xs:unique name="T請求鑑HD_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:T請求鑑HD" />
      <xs:field xpath="mstns:請求番号" />
    </xs:unique>
  </xs:element>
</xs:schema>