﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{A11A29B4-DF9C-4925-9A5B-FCA40A27219A}</ProjectGuid>
    <OutputType>Library</OutputType>
    <RootNamespace>Dbs.Application.Logic.Database</RootNamespace>
    <AssemblyName>Dbs.Application.Logic.Database</AssemblyName>
    <FileAlignment>512</FileAlignment>
    <MyType>Windows</MyType>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <Deterministic>true</Deterministic>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <OutputPath>..\..\web\bin\</OutputPath>
    <DocumentationFile>Dbs.Application.Logic.Database.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <Optimize>true</Optimize>
    <OutputPath>..\..\web\bin\</OutputPath>
    <DocumentationFile>Dbs.Application.Logic.Database.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
  </PropertyGroup>
  <PropertyGroup>
    <OptionExplicit>On</OptionExplicit>
  </PropertyGroup>
  <PropertyGroup>
    <OptionCompare>Binary</OptionCompare>
  </PropertyGroup>
  <PropertyGroup>
    <OptionStrict>Off</OptionStrict>
  </PropertyGroup>
  <PropertyGroup>
    <OptionInfer>On</OptionInfer>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Dbs.Asphalt.Database, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\web\bin\Dbs.Asphalt.Database.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Transactions" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Net.Http" />
  </ItemGroup>
  <ItemGroup>
    <Import Include="Microsoft.VisualBasic" />
    <Import Include="System" />
    <Import Include="System.Collections" />
    <Import Include="System.Collections.Generic" />
    <Import Include="System.Data" />
    <Import Include="System.Diagnostics" />
    <Import Include="System.Linq" />
    <Import Include="System.Xml.Linq" />
    <Import Include="System.Threading.Tasks" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Common\ConnectionString.vb" />
    <Compile Include="DbCalc.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>DbCalc.xsd</DependentUpon>
    </Compile>
    <Compile Include="DbMaster.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>DbMaster.xsd</DependentUpon>
    </Compile>
    <Compile Include="DbMaster.vb">
      <DependentUpon>DbMaster.xsd</DependentUpon>
    </Compile>
    <Compile Include="DbReport.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>DbReport.xsd</DependentUpon>
    </Compile>
    <Compile Include="DbTran.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>DbTran.xsd</DependentUpon>
    </Compile>
    <Compile Include="DbTran.vb">
      <DependentUpon>DbTran.xsd</DependentUpon>
    </Compile>
    <Compile Include="DbWork.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>DbWork.xsd</DependentUpon>
    </Compile>
    <Compile Include="My Project\AssemblyInfo.vb" />
    <Compile Include="My Project\Application.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Application.myapp</DependentUpon>
    </Compile>
    <Compile Include="My Project\Resources.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="My Project\Settings.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="My Project\Resources.resx">
      <Generator>VbMyResourcesResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.vb</LastGenOutput>
      <CustomToolNamespace>My.Resources</CustomToolNamespace>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="DbCalc.xsc">
      <DependentUpon>DbCalc.xsd</DependentUpon>
    </None>
    <None Include="DbCalc.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>DbCalc.Designer.vb</LastGenOutput>
    </None>
    <None Include="DbCalc.xss">
      <DependentUpon>DbCalc.xsd</DependentUpon>
    </None>
    <None Include="DbMaster.xsc">
      <DependentUpon>DbMaster.xsd</DependentUpon>
    </None>
    <None Include="DbMaster.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>DbMaster.Designer.vb</LastGenOutput>
    </None>
    <None Include="DbMaster.xss">
      <DependentUpon>DbMaster.xsd</DependentUpon>
    </None>
    <None Include="DbReport.xsc">
      <DependentUpon>DbReport.xsd</DependentUpon>
    </None>
    <None Include="DbReport.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>DbReport.Designer.vb</LastGenOutput>
    </None>
    <None Include="DbReport.xss">
      <DependentUpon>DbReport.xsd</DependentUpon>
    </None>
    <None Include="DbTran.xsc">
      <DependentUpon>DbTran.xsd</DependentUpon>
    </None>
    <None Include="DbTran.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>DbTran.Designer.vb</LastGenOutput>
    </None>
    <None Include="DbTran.xss">
      <DependentUpon>DbTran.xsd</DependentUpon>
    </None>
    <None Include="DbWork.xsc">
      <DependentUpon>DbWork.xsd</DependentUpon>
    </None>
    <None Include="DbWork.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>DbWork.Designer.vb</LastGenOutput>
    </None>
    <None Include="DbWork.xss">
      <DependentUpon>DbWork.xsd</DependentUpon>
    </None>
    <None Include="My Project\Application.myapp">
      <Generator>MyApplicationCodeGenerator</Generator>
      <LastGenOutput>Application.Designer.vb</LastGenOutput>
    </None>
    <None Include="My Project\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <CustomToolNamespace>My</CustomToolNamespace>
      <LastGenOutput>Settings.Designer.vb</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="CommonFinderSql\M会社検索.sql" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="CommonFinderSql\M得意先検索.sql" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="CommonFinderSql\M工事検索.sql" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="CommonFinderSql\M商品検索.sql" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="CommonFinderSql\T入金検索.sql" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="CommonFinderSql\T売上検索.sql" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.VisualBasic.targets" />
</Project>