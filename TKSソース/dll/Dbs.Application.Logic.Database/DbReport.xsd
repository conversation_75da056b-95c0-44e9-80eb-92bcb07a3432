﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="DbReport" targetNamespace="http://tempuri.org/DbReport.xsd" xmlns:mstns="http://tempuri.org/DbReport.xsd" xmlns="http://tempuri.org/DbReport.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections>
          <Connection AppSettingsObjectName="MySettings" AppSettingsPropertyName="ConnectionString" ConnectionStringObject="" IsAppSettingsProperty="true" Modifier="Assembly" Name="ConnectionString (MySettings)" ParameterPrefix="@" PropertyReference="ApplicationSettings.Dbs.Application.Logic.Database.My.MySettings.GlobalReference.Default.ConnectionString" Provider="System.Data.SqlClient" />
        </Connections>
        <Tables>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="R売上伝票一覧TableAdapter" GeneratorDataComponentClassName="R売上伝票一覧TableAdapter" Name="R売上伝票一覧" UserDataComponentName="R売上伝票一覧TableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="TokoDB.dbo.R売上伝票一覧" DbObjectType="Function" GenerateMethods="Get" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="GetData">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT                      R売上伝票一覧.*
FROM                         dbo.R売上伝票一覧(@売上番号リスト)
ORDER
   BY  売上番号
 ,  行NO</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="売上番号リスト" ColumnName="" DataSourceName="" DataTypeServer="varchar(4000)" DbType="AnsiString" Direction="Input" ParameterName="@売上番号リスト" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="売上番号" DataSetColumn="売上番号" />
              <Mapping SourceColumn="行NO" DataSetColumn="行NO" />
              <Mapping SourceColumn="伝票区分" DataSetColumn="伝票区分" />
              <Mapping SourceColumn="伝票区分名" DataSetColumn="伝票区分名" />
              <Mapping SourceColumn="搬入日付" DataSetColumn="搬入日付" />
              <Mapping SourceColumn="得意先コード" DataSetColumn="得意先コード" />
              <Mapping SourceColumn="得意先名" DataSetColumn="得意先名" />
              <Mapping SourceColumn="工事コード" DataSetColumn="工事コード" />
              <Mapping SourceColumn="工事内部コード" DataSetColumn="工事内部コード" />
              <Mapping SourceColumn="工事名" DataSetColumn="工事名" />
              <Mapping SourceColumn="住所" DataSetColumn="住所" />
              <Mapping SourceColumn="摘要1" DataSetColumn="摘要1" />
              <Mapping SourceColumn="摘要2" DataSetColumn="摘要2" />
              <Mapping SourceColumn="計算区分" DataSetColumn="計算区分" />
              <Mapping SourceColumn="商品コード" DataSetColumn="商品コード" />
              <Mapping SourceColumn="商品名" DataSetColumn="商品名" />
              <Mapping SourceColumn="備考明細" DataSetColumn="備考明細" />
              <Mapping SourceColumn="備考社内" DataSetColumn="備考社内" />
              <Mapping SourceColumn="数量" DataSetColumn="数量" />
              <Mapping SourceColumn="単位区分" DataSetColumn="単位区分" />
              <Mapping SourceColumn="単位区分名" DataSetColumn="単位区分名" />
              <Mapping SourceColumn="単価" DataSetColumn="単価" />
              <Mapping SourceColumn="基本料金" DataSetColumn="基本料金" />
              <Mapping SourceColumn="金額" DataSetColumn="金額" />
              <Mapping SourceColumn="搬入番号" DataSetColumn="搬入番号" />
              <Mapping SourceColumn="開始日付" DataSetColumn="開始日付" />
              <Mapping SourceColumn="終了日付" DataSetColumn="終了日付" />
              <Mapping SourceColumn="返納日付" DataSetColumn="返納日付" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="R請求書鑑TableAdapter" GeneratorDataComponentClassName="R請求書鑑TableAdapter" Name="R請求書鑑" UserDataComponentName="R請求書鑑TableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="TokoDB.dbo.R請求書鑑" DbObjectType="Function" GenerateMethods="Get" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="GetData">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT                      R請求書鑑.*
FROM                         dbo.R請求書鑑(@請求リスト)
ORDER
   BY  請求番号
   ,  行番号</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="請求リスト" ColumnName="" DataSourceName="" DataTypeServer="varchar(4000)" DbType="AnsiString" Direction="Input" ParameterName="@請求リスト" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="請求番号" DataSetColumn="請求番号" />
              <Mapping SourceColumn="行番号" DataSetColumn="行番号" />
              <Mapping SourceColumn="請求年月" DataSetColumn="請求年月" />
              <Mapping SourceColumn="締日区分" DataSetColumn="締日区分" />
              <Mapping SourceColumn="得意先コード" DataSetColumn="得意先コード" />
              <Mapping SourceColumn="得意先名" DataSetColumn="得意先名" />
              <Mapping SourceColumn="請求日付自" DataSetColumn="請求日付自" />
              <Mapping SourceColumn="請求日付至" DataSetColumn="請求日付至" />
              <Mapping SourceColumn="前回額" DataSetColumn="前回額" />
              <Mapping SourceColumn="入金額" DataSetColumn="入金額" />
              <Mapping SourceColumn="調整額" DataSetColumn="調整額" />
              <Mapping SourceColumn="繰越額" DataSetColumn="繰越額" />
              <Mapping SourceColumn="税抜額" DataSetColumn="税抜額" />
              <Mapping SourceColumn="税込額" DataSetColumn="税込額" />
              <Mapping SourceColumn="消費税" DataSetColumn="消費税" />
              <Mapping SourceColumn="請求額" DataSetColumn="請求額" />
              <Mapping SourceColumn="工事内部コード" DataSetColumn="工事内部コード" />
              <Mapping SourceColumn="工事税抜額" DataSetColumn="工事税抜額" />
              <Mapping SourceColumn="工事消費税" DataSetColumn="工事消費税" />
              <Mapping SourceColumn="工事税込額" DataSetColumn="工事税込額" />
              <Mapping SourceColumn="郵便番号" DataSetColumn="郵便番号" />
              <Mapping SourceColumn="住所1" DataSetColumn="住所1" />
              <Mapping SourceColumn="住所2" DataSetColumn="住所2" />
              <Mapping SourceColumn="工事コード" DataSetColumn="工事コード" />
              <Mapping SourceColumn="工事名" DataSetColumn="工事名" />
              <Mapping SourceColumn="会社名" DataSetColumn="会社名" />
              <Mapping SourceColumn="登録番号" DataSetColumn="登録番号" />
              <Mapping SourceColumn="振込先1" DataSetColumn="振込先1" />
              <Mapping SourceColumn="グループNO" DataSetColumn="グループNO" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="R請求書明細TableAdapter" GeneratorDataComponentClassName="R請求書明細TableAdapter" Name="R請求書明細" UserDataComponentName="R請求書明細TableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="TokoDB.dbo.R請求書明細" DbObjectType="Function" GenerateMethods="Get" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="GetData">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT                      R請求書明細.*
FROM                         dbo.R請求書明細(@請求リスト)
ORDER
   BY  請求番号
     ,  行番号
     ,  売上番号
     ,  売上行番号
</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="請求リスト" ColumnName="" DataSourceName="" DataTypeServer="varchar(4000)" DbType="AnsiString" Direction="Input" ParameterName="@請求リスト" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="請求番号" DataSetColumn="請求番号" />
              <Mapping SourceColumn="行番号" DataSetColumn="行番号" />
              <Mapping SourceColumn="請求年月" DataSetColumn="請求年月" />
              <Mapping SourceColumn="締日区分" DataSetColumn="締日区分" />
              <Mapping SourceColumn="得意先コード" DataSetColumn="得意先コード" />
              <Mapping SourceColumn="得意先名" DataSetColumn="得意先名" />
              <Mapping SourceColumn="請求日付自" DataSetColumn="請求日付自" />
              <Mapping SourceColumn="請求日付至" DataSetColumn="請求日付至" />
              <Mapping SourceColumn="工事内部コード" DataSetColumn="工事内部コード" />
              <Mapping SourceColumn="前回額" DataSetColumn="前回額" />
              <Mapping SourceColumn="入金額" DataSetColumn="入金額" />
              <Mapping SourceColumn="調整額" DataSetColumn="調整額" />
              <Mapping SourceColumn="繰越額" DataSetColumn="繰越額" />
              <Mapping SourceColumn="税抜額" DataSetColumn="税抜額" />
              <Mapping SourceColumn="税込額" DataSetColumn="税込額" />
              <Mapping SourceColumn="消費税" DataSetColumn="消費税" />
              <Mapping SourceColumn="請求額" DataSetColumn="請求額" />
              <Mapping SourceColumn="工事コード" DataSetColumn="工事コード" />
              <Mapping SourceColumn="工事名" DataSetColumn="工事名" />
              <Mapping SourceColumn="会社名" DataSetColumn="会社名" />
              <Mapping SourceColumn="登録番号" DataSetColumn="登録番号" />
              <Mapping SourceColumn="振込先1" DataSetColumn="振込先1" />
              <Mapping SourceColumn="グループNO" DataSetColumn="グループNO" />
              <Mapping SourceColumn="売上番号" DataSetColumn="売上番号" />
              <Mapping SourceColumn="売上行番号" DataSetColumn="売上行番号" />
              <Mapping SourceColumn="搬入日付" DataSetColumn="搬入日付" />
              <Mapping SourceColumn="商品コード" DataSetColumn="商品コード" />
              <Mapping SourceColumn="商品名" DataSetColumn="商品名" />
              <Mapping SourceColumn="開始日付" DataSetColumn="開始日付" />
              <Mapping SourceColumn="終了日付" DataSetColumn="終了日付" />
              <Mapping SourceColumn="返納日付" DataSetColumn="返納日付" />
              <Mapping SourceColumn="日数" DataSetColumn="日数" />
              <Mapping SourceColumn="数量" DataSetColumn="数量" />
              <Mapping SourceColumn="単位区分" DataSetColumn="単位区分" />
              <Mapping SourceColumn="単位区分名" DataSetColumn="単位区分名" />
              <Mapping SourceColumn="単価" DataSetColumn="単価" />
              <Mapping SourceColumn="基本料金" DataSetColumn="基本料金" />
              <Mapping SourceColumn="金額" DataSetColumn="金額" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="R請求一覧表TableAdapter" GeneratorDataComponentClassName="R請求一覧表TableAdapter" Name="R請求一覧表" UserDataComponentName="R請求一覧表TableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="TokoDB.dbo.R請求一覧表" DbObjectType="Function" GenerateMethods="Get" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="GetData">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT                      R請求一覧表.*
FROM                         dbo.R請求一覧表(@請求リスト)
ORDER
   BY  請求番号
     ,  行番号
</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="請求リスト" ColumnName="" DataSourceName="" DataTypeServer="varchar(4000)" DbType="AnsiString" Direction="Input" ParameterName="@請求リスト" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="請求番号" DataSetColumn="請求番号" />
              <Mapping SourceColumn="行番号" DataSetColumn="行番号" />
              <Mapping SourceColumn="請求年月" DataSetColumn="請求年月" />
              <Mapping SourceColumn="締日区分" DataSetColumn="締日区分" />
              <Mapping SourceColumn="請求日付自" DataSetColumn="請求日付自" />
              <Mapping SourceColumn="請求日付至" DataSetColumn="請求日付至" />
              <Mapping SourceColumn="得意先コード" DataSetColumn="得意先コード" />
              <Mapping SourceColumn="得意先名" DataSetColumn="得意先名" />
              <Mapping SourceColumn="工事内部コード" DataSetColumn="工事内部コード" />
              <Mapping SourceColumn="工事コード" DataSetColumn="工事コード" />
              <Mapping SourceColumn="工事名" DataSetColumn="工事名" />
              <Mapping SourceColumn="前回額" DataSetColumn="前回額" />
              <Mapping SourceColumn="入金額" DataSetColumn="入金額" />
              <Mapping SourceColumn="調整額" DataSetColumn="調整額" />
              <Mapping SourceColumn="繰越額" DataSetColumn="繰越額" />
              <Mapping SourceColumn="税抜額" DataSetColumn="税抜額" />
              <Mapping SourceColumn="税込額" DataSetColumn="税込額" />
              <Mapping SourceColumn="消費税" DataSetColumn="消費税" />
              <Mapping SourceColumn="請求額" DataSetColumn="請求額" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="R納品書TableAdapter" GeneratorDataComponentClassName="R納品書TableAdapter" Name="R納品書" UserDataComponentName="R納品書TableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="TokoDB.dbo.R納品書" DbObjectType="Function" GenerateMethods="Get" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="GetData">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT                      R納品書.*
FROM                         dbo.R納品書(@売上番号リスト) 
ORDER
       BY  売上番号  , 行番号</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="売上番号リスト" ColumnName="" DataSourceName="" DataTypeServer="varchar(4000)" DbType="AnsiString" Direction="Input" ParameterName="@売上番号リスト" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="売上番号" DataSetColumn="売上番号" />
              <Mapping SourceColumn="行番号" DataSetColumn="行番号" />
              <Mapping SourceColumn="計算区分" DataSetColumn="計算区分" />
              <Mapping SourceColumn="商品コード" DataSetColumn="商品コード" />
              <Mapping SourceColumn="商品名" DataSetColumn="商品名" />
              <Mapping SourceColumn="数量" DataSetColumn="数量" />
              <Mapping SourceColumn="単価" DataSetColumn="単価" />
              <Mapping SourceColumn="基本料金" DataSetColumn="基本料金" />
              <Mapping SourceColumn="金額" DataSetColumn="金額" />
              <Mapping SourceColumn="単位区分" DataSetColumn="単位区分" />
              <Mapping SourceColumn="備考明細" DataSetColumn="備考明細" />
              <Mapping SourceColumn="備考社内" DataSetColumn="備考社内" />
              <Mapping SourceColumn="搬入番号" DataSetColumn="搬入番号" />
              <Mapping SourceColumn="開始日付" DataSetColumn="開始日付" />
              <Mapping SourceColumn="終了日付" DataSetColumn="終了日付" />
              <Mapping SourceColumn="返納日付" DataSetColumn="返納日付" />
              <Mapping SourceColumn="搬入日付" DataSetColumn="搬入日付" />
              <Mapping SourceColumn="伝票区分" DataSetColumn="伝票区分" />
              <Mapping SourceColumn="得意先コード" DataSetColumn="得意先コード" />
              <Mapping SourceColumn="工事コード" DataSetColumn="工事コード" />
              <Mapping SourceColumn="工事内部コード" DataSetColumn="工事内部コード" />
              <Mapping SourceColumn="伝票発行区分" DataSetColumn="伝票発行区分" />
              <Mapping SourceColumn="摘要1" DataSetColumn="摘要1" />
              <Mapping SourceColumn="摘要2" DataSetColumn="摘要2" />
              <Mapping SourceColumn="得意先名" DataSetColumn="得意先名" />
              <Mapping SourceColumn="工事名" DataSetColumn="工事名" />
              <Mapping SourceColumn="工事住所" DataSetColumn="工事住所" />
              <Mapping SourceColumn="伝票区分名" DataSetColumn="伝票区分名" />
              <Mapping SourceColumn="伝票発行区分名" DataSetColumn="伝票発行区分名" />
              <Mapping SourceColumn="計算区分名" DataSetColumn="計算区分名" />
              <Mapping SourceColumn="単位区分名" DataSetColumn="単位区分名" />
              <Mapping SourceColumn="会社名" DataSetColumn="会社名" />
              <Mapping SourceColumn="会社郵便番号" DataSetColumn="会社郵便番号" />
              <Mapping SourceColumn="会社住所1" DataSetColumn="会社住所1" />
              <Mapping SourceColumn="会社住所2" DataSetColumn="会社住所2" />
              <Mapping SourceColumn="会社電話番号" DataSetColumn="会社電話番号" />
              <Mapping SourceColumn="会社FAX番号" DataSetColumn="会社FAX番号" />
              <Mapping SourceColumn="明細件数" DataSetColumn="明細件数" />
            </Mappings>
            <Sources />
          </TableAdapter>
        </Tables>
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="DbReport" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:EnableTableAdapterManager="true" msprop:Generator_DataSetName="DbReport" msprop:Generator_UserDSName="DbReport">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="R売上伝票一覧" msprop:Generator_TableClassName="R売上伝票一覧DataTable" msprop:Generator_TableVarName="tableR売上伝票一覧" msprop:Generator_TablePropName="R売上伝票一覧" msprop:Generator_RowDeletingName="R売上伝票一覧RowDeleting" msprop:Generator_RowChangingName="R売上伝票一覧RowChanging" msprop:Generator_RowEvHandlerName="R売上伝票一覧RowChangeEventHandler" msprop:Generator_RowDeletedName="R売上伝票一覧RowDeleted" msprop:Generator_UserTableName="R売上伝票一覧" msprop:Generator_RowChangedName="R売上伝票一覧RowChanged" msprop:Generator_RowEvArgName="R売上伝票一覧RowChangeEvent" msprop:Generator_RowClassName="R売上伝票一覧Row">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="売上番号" msprop:Generator_ColumnVarNameInTable="column売上番号" msprop:Generator_ColumnPropNameInRow="売上番号" msprop:Generator_ColumnPropNameInTable="売上番号Column" msprop:Generator_UserColumnName="売上番号">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="行NO" msprop:Generator_ColumnVarNameInTable="column行NO" msprop:Generator_ColumnPropNameInRow="行NO" msprop:Generator_ColumnPropNameInTable="行NOColumn" msprop:Generator_UserColumnName="行NO" type="xs:decimal" />
              <xs:element name="伝票区分" msprop:Generator_ColumnVarNameInTable="column伝票区分" msprop:Generator_ColumnPropNameInRow="伝票区分" msprop:Generator_ColumnPropNameInTable="伝票区分Column" msprop:Generator_UserColumnName="伝票区分" type="xs:short" minOccurs="0" />
              <xs:element name="伝票区分名" msprop:Generator_ColumnVarNameInTable="column伝票区分名" msprop:Generator_ColumnPropNameInRow="伝票区分名" msprop:Generator_ColumnPropNameInTable="伝票区分名Column" msprop:Generator_UserColumnName="伝票区分名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="搬入日付" msprop:Generator_ColumnVarNameInTable="column搬入日付" msprop:Generator_ColumnPropNameInRow="搬入日付" msprop:Generator_ColumnPropNameInTable="搬入日付Column" msprop:Generator_UserColumnName="搬入日付" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="得意先コード" msprop:Generator_ColumnVarNameInTable="column得意先コード" msprop:Generator_ColumnPropNameInRow="得意先コード" msprop:Generator_ColumnPropNameInTable="得意先コードColumn" msprop:Generator_UserColumnName="得意先コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="得意先名" msprop:Generator_ColumnVarNameInTable="column得意先名" msprop:Generator_ColumnPropNameInRow="得意先名" msprop:Generator_ColumnPropNameInTable="得意先名Column" msprop:Generator_UserColumnName="得意先名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="工事コード" msprop:Generator_ColumnVarNameInTable="column工事コード" msprop:Generator_ColumnPropNameInRow="工事コード" msprop:Generator_ColumnPropNameInTable="工事コードColumn" msprop:Generator_UserColumnName="工事コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="工事内部コード" msprop:Generator_ColumnVarNameInTable="column工事内部コード" msprop:Generator_ColumnPropNameInRow="工事内部コード" msprop:Generator_ColumnPropNameInTable="工事内部コードColumn" msprop:Generator_UserColumnName="工事内部コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="工事名" msprop:Generator_ColumnVarNameInTable="column工事名" msprop:Generator_ColumnPropNameInRow="工事名" msprop:Generator_ColumnPropNameInTable="工事名Column" msprop:Generator_UserColumnName="工事名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="住所" msprop:Generator_ColumnVarNameInTable="column住所" msprop:Generator_ColumnPropNameInRow="住所" msprop:Generator_ColumnPropNameInTable="住所Column" msprop:Generator_UserColumnName="住所" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="摘要1" msprop:Generator_ColumnVarNameInTable="column摘要1" msprop:Generator_ColumnPropNameInRow="摘要1" msprop:Generator_ColumnPropNameInTable="摘要1Column" msprop:Generator_UserColumnName="摘要1" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="摘要2" msprop:Generator_ColumnVarNameInTable="column摘要2" msprop:Generator_ColumnPropNameInRow="摘要2" msprop:Generator_ColumnPropNameInTable="摘要2Column" msprop:Generator_UserColumnName="摘要2" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="計算区分" msprop:Generator_ColumnVarNameInTable="column計算区分" msprop:Generator_ColumnPropNameInRow="計算区分" msprop:Generator_ColumnPropNameInTable="計算区分Column" msprop:Generator_UserColumnName="計算区分" type="xs:int" minOccurs="0" />
              <xs:element name="商品コード" msprop:Generator_ColumnVarNameInTable="column商品コード" msprop:Generator_ColumnPropNameInRow="商品コード" msprop:Generator_ColumnPropNameInTable="商品コードColumn" msprop:Generator_UserColumnName="商品コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="商品名" msprop:Generator_ColumnVarNameInTable="column商品名" msprop:Generator_ColumnPropNameInRow="商品名" msprop:Generator_ColumnPropNameInTable="商品名Column" msprop:Generator_UserColumnName="商品名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="備考明細" msprop:Generator_ColumnVarNameInTable="column備考明細" msprop:Generator_ColumnPropNameInRow="備考明細" msprop:Generator_ColumnPropNameInTable="備考明細Column" msprop:Generator_UserColumnName="備考明細" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="備考社内" msprop:Generator_ColumnVarNameInTable="column備考社内" msprop:Generator_ColumnPropNameInRow="備考社内" msprop:Generator_ColumnPropNameInTable="備考社内Column" msprop:Generator_UserColumnName="備考社内" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="数量" msprop:Generator_ColumnVarNameInTable="column数量" msprop:Generator_ColumnPropNameInRow="数量" msprop:Generator_ColumnPropNameInTable="数量Column" msprop:Generator_UserColumnName="数量" type="xs:decimal" minOccurs="0" />
              <xs:element name="単位区分" msprop:Generator_ColumnVarNameInTable="column単位区分" msprop:Generator_ColumnPropNameInRow="単位区分" msprop:Generator_ColumnPropNameInTable="単位区分Column" msprop:Generator_UserColumnName="単位区分" type="xs:int" minOccurs="0" />
              <xs:element name="単位区分名" msprop:Generator_ColumnVarNameInTable="column単位区分名" msprop:Generator_ColumnPropNameInRow="単位区分名" msprop:Generator_ColumnPropNameInTable="単位区分名Column" msprop:Generator_UserColumnName="単位区分名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="単価" msprop:Generator_ColumnVarNameInTable="column単価" msprop:Generator_ColumnPropNameInRow="単価" msprop:Generator_ColumnPropNameInTable="単価Column" msprop:Generator_UserColumnName="単価" type="xs:decimal" minOccurs="0" />
              <xs:element name="基本料金" msprop:Generator_ColumnVarNameInTable="column基本料金" msprop:Generator_ColumnPropNameInRow="基本料金" msprop:Generator_ColumnPropNameInTable="基本料金Column" msprop:Generator_UserColumnName="基本料金" type="xs:decimal" minOccurs="0" />
              <xs:element name="金額" msprop:Generator_ColumnVarNameInTable="column金額" msprop:Generator_ColumnPropNameInRow="金額" msprop:Generator_ColumnPropNameInTable="金額Column" msprop:Generator_UserColumnName="金額" type="xs:decimal" minOccurs="0" />
              <xs:element name="搬入番号" msprop:Generator_ColumnVarNameInTable="column搬入番号" msprop:Generator_ColumnPropNameInRow="搬入番号" msprop:Generator_ColumnPropNameInTable="搬入番号Column" msprop:Generator_UserColumnName="搬入番号" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="開始日付" msprop:Generator_ColumnVarNameInTable="column開始日付" msprop:Generator_ColumnPropNameInRow="開始日付" msprop:Generator_ColumnPropNameInTable="開始日付Column" msprop:Generator_UserColumnName="開始日付" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="終了日付" msprop:Generator_ColumnVarNameInTable="column終了日付" msprop:Generator_ColumnPropNameInRow="終了日付" msprop:Generator_ColumnPropNameInTable="終了日付Column" msprop:Generator_UserColumnName="終了日付" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="返納日付" msprop:Generator_ColumnVarNameInTable="column返納日付" msprop:Generator_ColumnPropNameInRow="返納日付" msprop:Generator_ColumnPropNameInTable="返納日付Column" msprop:Generator_UserColumnName="返納日付" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="R請求書鑑" msprop:Generator_TableClassName="R請求書鑑DataTable" msprop:Generator_TableVarName="tableR請求書鑑" msprop:Generator_TablePropName="R請求書鑑" msprop:Generator_RowDeletingName="R請求書鑑RowDeleting" msprop:Generator_RowChangingName="R請求書鑑RowChanging" msprop:Generator_RowEvHandlerName="R請求書鑑RowChangeEventHandler" msprop:Generator_RowDeletedName="R請求書鑑RowDeleted" msprop:Generator_UserTableName="R請求書鑑" msprop:Generator_RowChangedName="R請求書鑑RowChanged" msprop:Generator_RowEvArgName="R請求書鑑RowChangeEvent" msprop:Generator_RowClassName="R請求書鑑Row">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="請求番号" msprop:Generator_ColumnVarNameInTable="column請求番号" msprop:Generator_ColumnPropNameInRow="請求番号" msprop:Generator_ColumnPropNameInTable="請求番号Column" msprop:Generator_UserColumnName="請求番号">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="行番号" msprop:Generator_ColumnVarNameInTable="column行番号" msprop:Generator_ColumnPropNameInRow="行番号" msprop:Generator_ColumnPropNameInTable="行番号Column" msprop:Generator_UserColumnName="行番号" type="xs:decimal" />
              <xs:element name="請求年月" msprop:Generator_ColumnVarNameInTable="column請求年月" msprop:Generator_ColumnPropNameInRow="請求年月" msprop:Generator_ColumnPropNameInTable="請求年月Column" msprop:Generator_UserColumnName="請求年月" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="7" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="締日区分" msprop:Generator_ColumnVarNameInTable="column締日区分" msprop:Generator_ColumnPropNameInRow="締日区分" msprop:Generator_ColumnPropNameInTable="締日区分Column" msprop:Generator_UserColumnName="締日区分" type="xs:short" minOccurs="0" />
              <xs:element name="得意先コード" msprop:Generator_ColumnVarNameInTable="column得意先コード" msprop:Generator_ColumnPropNameInRow="得意先コード" msprop:Generator_ColumnPropNameInTable="得意先コードColumn" msprop:Generator_UserColumnName="得意先コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="得意先名" msprop:Generator_ColumnVarNameInTable="column得意先名" msprop:Generator_ColumnPropNameInRow="得意先名" msprop:Generator_ColumnPropNameInTable="得意先名Column" msprop:Generator_UserColumnName="得意先名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="請求日付自" msprop:Generator_ColumnVarNameInTable="column請求日付自" msprop:Generator_ColumnPropNameInRow="請求日付自" msprop:Generator_ColumnPropNameInTable="請求日付自Column" msprop:Generator_UserColumnName="請求日付自" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="請求日付至" msprop:Generator_ColumnVarNameInTable="column請求日付至" msprop:Generator_ColumnPropNameInRow="請求日付至" msprop:Generator_ColumnPropNameInTable="請求日付至Column" msprop:Generator_UserColumnName="請求日付至" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="前回額" msprop:Generator_ColumnVarNameInTable="column前回額" msprop:Generator_ColumnPropNameInRow="前回額" msprop:Generator_ColumnPropNameInTable="前回額Column" msprop:Generator_UserColumnName="前回額" type="xs:decimal" minOccurs="0" />
              <xs:element name="入金額" msprop:Generator_ColumnVarNameInTable="column入金額" msprop:Generator_ColumnPropNameInRow="入金額" msprop:Generator_ColumnPropNameInTable="入金額Column" msprop:Generator_UserColumnName="入金額" type="xs:decimal" minOccurs="0" />
              <xs:element name="調整額" msprop:Generator_ColumnVarNameInTable="column調整額" msprop:Generator_ColumnPropNameInRow="調整額" msprop:Generator_ColumnPropNameInTable="調整額Column" msprop:Generator_UserColumnName="調整額" type="xs:decimal" minOccurs="0" />
              <xs:element name="繰越額" msprop:Generator_ColumnVarNameInTable="column繰越額" msprop:Generator_ColumnPropNameInRow="繰越額" msprop:Generator_ColumnPropNameInTable="繰越額Column" msprop:Generator_UserColumnName="繰越額" type="xs:decimal" minOccurs="0" />
              <xs:element name="税抜額" msprop:Generator_ColumnVarNameInTable="column税抜額" msprop:Generator_ColumnPropNameInRow="税抜額" msprop:Generator_ColumnPropNameInTable="税抜額Column" msprop:Generator_UserColumnName="税抜額" type="xs:decimal" minOccurs="0" />
              <xs:element name="税込額" msprop:Generator_ColumnVarNameInTable="column税込額" msprop:Generator_ColumnPropNameInRow="税込額" msprop:Generator_ColumnPropNameInTable="税込額Column" msprop:Generator_UserColumnName="税込額" type="xs:decimal" minOccurs="0" />
              <xs:element name="消費税" msprop:Generator_ColumnVarNameInTable="column消費税" msprop:Generator_ColumnPropNameInRow="消費税" msprop:Generator_ColumnPropNameInTable="消費税Column" msprop:Generator_UserColumnName="消費税" type="xs:decimal" minOccurs="0" />
              <xs:element name="請求額" msprop:Generator_ColumnVarNameInTable="column請求額" msprop:Generator_ColumnPropNameInRow="請求額" msprop:Generator_ColumnPropNameInTable="請求額Column" msprop:Generator_UserColumnName="請求額" type="xs:decimal" minOccurs="0" />
              <xs:element name="工事内部コード" msprop:Generator_ColumnVarNameInTable="column工事内部コード" msprop:Generator_ColumnPropNameInRow="工事内部コード" msprop:Generator_ColumnPropNameInTable="工事内部コードColumn" msprop:Generator_UserColumnName="工事内部コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="工事税抜額" msprop:Generator_ColumnVarNameInTable="column工事税抜額" msprop:Generator_ColumnPropNameInRow="工事税抜額" msprop:Generator_ColumnPropNameInTable="工事税抜額Column" msprop:Generator_UserColumnName="工事税抜額" type="xs:decimal" minOccurs="0" />
              <xs:element name="工事消費税" msprop:Generator_ColumnVarNameInTable="column工事消費税" msprop:Generator_ColumnPropNameInRow="工事消費税" msprop:Generator_ColumnPropNameInTable="工事消費税Column" msprop:Generator_UserColumnName="工事消費税" type="xs:decimal" minOccurs="0" />
              <xs:element name="工事税込額" msprop:Generator_ColumnVarNameInTable="column工事税込額" msprop:Generator_ColumnPropNameInRow="工事税込額" msprop:Generator_ColumnPropNameInTable="工事税込額Column" msprop:Generator_UserColumnName="工事税込額" type="xs:decimal" minOccurs="0" />
              <xs:element name="郵便番号" msprop:Generator_ColumnVarNameInTable="column郵便番号" msprop:Generator_ColumnPropNameInRow="郵便番号" msprop:Generator_ColumnPropNameInTable="郵便番号Column" msprop:Generator_UserColumnName="郵便番号" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="住所1" msprop:Generator_ColumnVarNameInTable="column住所1" msprop:Generator_ColumnPropNameInRow="住所1" msprop:Generator_ColumnPropNameInTable="住所1Column" msprop:Generator_UserColumnName="住所1" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="住所2" msprop:Generator_ColumnVarNameInTable="column住所2" msprop:Generator_ColumnPropNameInRow="住所2" msprop:Generator_ColumnPropNameInTable="住所2Column" msprop:Generator_UserColumnName="住所2" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="工事コード" msprop:Generator_ColumnVarNameInTable="column工事コード" msprop:Generator_ColumnPropNameInRow="工事コード" msprop:Generator_ColumnPropNameInTable="工事コードColumn" msprop:Generator_UserColumnName="工事コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="工事名" msprop:Generator_ColumnVarNameInTable="column工事名" msprop:Generator_ColumnPropNameInRow="工事名" msprop:Generator_ColumnPropNameInTable="工事名Column" msprop:Generator_UserColumnName="工事名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="会社名" msprop:Generator_ColumnVarNameInTable="column会社名" msprop:Generator_ColumnPropNameInRow="会社名" msprop:Generator_ColumnPropNameInTable="会社名Column" msprop:Generator_UserColumnName="会社名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="登録番号" msprop:Generator_ColumnVarNameInTable="column登録番号" msprop:Generator_ColumnPropNameInRow="登録番号" msprop:Generator_ColumnPropNameInTable="登録番号Column" msprop:Generator_UserColumnName="登録番号" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="振込先1" msprop:Generator_ColumnVarNameInTable="column振込先1" msprop:Generator_ColumnPropNameInRow="振込先1" msprop:Generator_ColumnPropNameInTable="振込先1Column" msprop:Generator_UserColumnName="振込先1" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="グループNO" msprop:Generator_ColumnVarNameInTable="columnグループNO" msprop:Generator_ColumnPropNameInRow="グループNO" msprop:Generator_ColumnPropNameInTable="グループNOColumn" msprop:Generator_UserColumnName="グループNO" type="xs:long" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="R請求一覧表" msprop:Generator_TableClassName="R請求一覧表DataTable" msprop:Generator_TableVarName="tableR請求一覧表" msprop:Generator_TablePropName="R請求一覧表" msprop:Generator_RowDeletingName="R請求一覧表RowDeleting" msprop:Generator_RowChangingName="R請求一覧表RowChanging" msprop:Generator_RowEvHandlerName="R請求一覧表RowChangeEventHandler" msprop:Generator_RowDeletedName="R請求一覧表RowDeleted" msprop:Generator_UserTableName="R請求一覧表" msprop:Generator_RowChangedName="R請求一覧表RowChanged" msprop:Generator_RowEvArgName="R請求一覧表RowChangeEvent" msprop:Generator_RowClassName="R請求一覧表Row">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="請求番号" msprop:Generator_ColumnVarNameInTable="column請求番号" msprop:Generator_ColumnPropNameInRow="請求番号" msprop:Generator_ColumnPropNameInTable="請求番号Column" msprop:Generator_UserColumnName="請求番号">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="行番号" msprop:Generator_ColumnVarNameInTable="column行番号" msprop:Generator_ColumnPropNameInRow="行番号" msprop:Generator_ColumnPropNameInTable="行番号Column" msprop:Generator_UserColumnName="行番号" type="xs:decimal" />
              <xs:element name="請求年月" msprop:Generator_ColumnVarNameInTable="column請求年月" msprop:Generator_ColumnPropNameInRow="請求年月" msprop:Generator_ColumnPropNameInTable="請求年月Column" msprop:Generator_UserColumnName="請求年月" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="7" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="締日区分" msprop:Generator_ColumnVarNameInTable="column締日区分" msprop:Generator_ColumnPropNameInRow="締日区分" msprop:Generator_ColumnPropNameInTable="締日区分Column" msprop:Generator_UserColumnName="締日区分" type="xs:short" minOccurs="0" />
              <xs:element name="請求日付自" msprop:Generator_ColumnVarNameInTable="column請求日付自" msprop:Generator_ColumnPropNameInRow="請求日付自" msprop:Generator_ColumnPropNameInTable="請求日付自Column" msprop:Generator_UserColumnName="請求日付自" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="請求日付至" msprop:Generator_ColumnVarNameInTable="column請求日付至" msprop:Generator_ColumnPropNameInRow="請求日付至" msprop:Generator_ColumnPropNameInTable="請求日付至Column" msprop:Generator_UserColumnName="請求日付至" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="得意先コード" msprop:Generator_ColumnVarNameInTable="column得意先コード" msprop:Generator_ColumnPropNameInRow="得意先コード" msprop:Generator_ColumnPropNameInTable="得意先コードColumn" msprop:Generator_UserColumnName="得意先コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="得意先名" msprop:Generator_ColumnVarNameInTable="column得意先名" msprop:Generator_ColumnPropNameInRow="得意先名" msprop:Generator_ColumnPropNameInTable="得意先名Column" msprop:Generator_UserColumnName="得意先名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="工事内部コード" msprop:Generator_ColumnVarNameInTable="column工事内部コード" msprop:Generator_ColumnPropNameInRow="工事内部コード" msprop:Generator_ColumnPropNameInTable="工事内部コードColumn" msprop:Generator_UserColumnName="工事内部コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="工事コード" msprop:Generator_ColumnVarNameInTable="column工事コード" msprop:Generator_ColumnPropNameInRow="工事コード" msprop:Generator_ColumnPropNameInTable="工事コードColumn" msprop:Generator_UserColumnName="工事コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="工事名" msprop:Generator_ColumnVarNameInTable="column工事名" msprop:Generator_ColumnPropNameInRow="工事名" msprop:Generator_ColumnPropNameInTable="工事名Column" msprop:Generator_UserColumnName="工事名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="前回額" msprop:Generator_ColumnVarNameInTable="column前回額" msprop:Generator_ColumnPropNameInRow="前回額" msprop:Generator_ColumnPropNameInTable="前回額Column" msprop:Generator_UserColumnName="前回額" type="xs:decimal" minOccurs="0" />
              <xs:element name="入金額" msprop:Generator_ColumnVarNameInTable="column入金額" msprop:Generator_ColumnPropNameInRow="入金額" msprop:Generator_ColumnPropNameInTable="入金額Column" msprop:Generator_UserColumnName="入金額" type="xs:decimal" minOccurs="0" />
              <xs:element name="調整額" msprop:Generator_ColumnVarNameInTable="column調整額" msprop:Generator_ColumnPropNameInRow="調整額" msprop:Generator_ColumnPropNameInTable="調整額Column" msprop:Generator_UserColumnName="調整額" type="xs:decimal" minOccurs="0" />
              <xs:element name="繰越額" msprop:Generator_ColumnVarNameInTable="column繰越額" msprop:Generator_ColumnPropNameInRow="繰越額" msprop:Generator_ColumnPropNameInTable="繰越額Column" msprop:Generator_UserColumnName="繰越額" type="xs:decimal" minOccurs="0" />
              <xs:element name="税抜額" msprop:Generator_ColumnVarNameInTable="column税抜額" msprop:Generator_ColumnPropNameInRow="税抜額" msprop:Generator_ColumnPropNameInTable="税抜額Column" msprop:Generator_UserColumnName="税抜額" type="xs:decimal" minOccurs="0" />
              <xs:element name="税込額" msprop:Generator_ColumnVarNameInTable="column税込額" msprop:Generator_ColumnPropNameInRow="税込額" msprop:Generator_ColumnPropNameInTable="税込額Column" msprop:Generator_UserColumnName="税込額" type="xs:decimal" minOccurs="0" />
              <xs:element name="消費税" msprop:Generator_ColumnVarNameInTable="column消費税" msprop:Generator_ColumnPropNameInRow="消費税" msprop:Generator_ColumnPropNameInTable="消費税Column" msprop:Generator_UserColumnName="消費税" type="xs:decimal" minOccurs="0" />
              <xs:element name="請求額" msprop:Generator_ColumnVarNameInTable="column請求額" msprop:Generator_ColumnPropNameInRow="請求額" msprop:Generator_ColumnPropNameInTable="請求額Column" msprop:Generator_UserColumnName="請求額" type="xs:decimal" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="R請求書明細" msprop:Generator_TableClassName="R請求書明細DataTable" msprop:Generator_TableVarName="tableR請求書明細" msprop:Generator_RowChangedName="R請求書明細RowChanged" msprop:Generator_TablePropName="R請求書明細" msprop:Generator_RowDeletingName="R請求書明細RowDeleting" msprop:Generator_RowChangingName="R請求書明細RowChanging" msprop:Generator_RowEvHandlerName="R請求書明細RowChangeEventHandler" msprop:Generator_RowDeletedName="R請求書明細RowDeleted" msprop:Generator_RowClassName="R請求書明細Row" msprop:Generator_UserTableName="R請求書明細" msprop:Generator_RowEvArgName="R請求書明細RowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="請求番号" msprop:Generator_ColumnVarNameInTable="column請求番号" msprop:Generator_ColumnPropNameInRow="請求番号" msprop:Generator_ColumnPropNameInTable="請求番号Column" msprop:Generator_UserColumnName="請求番号">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="行番号" msprop:Generator_ColumnVarNameInTable="column行番号" msprop:Generator_ColumnPropNameInRow="行番号" msprop:Generator_ColumnPropNameInTable="行番号Column" msprop:Generator_UserColumnName="行番号" type="xs:decimal" />
              <xs:element name="請求年月" msprop:Generator_ColumnVarNameInTable="column請求年月" msprop:Generator_ColumnPropNameInRow="請求年月" msprop:Generator_ColumnPropNameInTable="請求年月Column" msprop:Generator_UserColumnName="請求年月" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="7" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="締日区分" msprop:Generator_ColumnVarNameInTable="column締日区分" msprop:Generator_ColumnPropNameInRow="締日区分" msprop:Generator_ColumnPropNameInTable="締日区分Column" msprop:Generator_UserColumnName="締日区分" type="xs:short" minOccurs="0" />
              <xs:element name="得意先コード" msprop:Generator_ColumnVarNameInTable="column得意先コード" msprop:Generator_ColumnPropNameInRow="得意先コード" msprop:Generator_ColumnPropNameInTable="得意先コードColumn" msprop:Generator_UserColumnName="得意先コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="得意先名" msprop:Generator_ColumnVarNameInTable="column得意先名" msprop:Generator_ColumnPropNameInRow="得意先名" msprop:Generator_ColumnPropNameInTable="得意先名Column" msprop:Generator_UserColumnName="得意先名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="請求日付自" msprop:Generator_ColumnVarNameInTable="column請求日付自" msprop:Generator_ColumnPropNameInRow="請求日付自" msprop:Generator_ColumnPropNameInTable="請求日付自Column" msprop:Generator_UserColumnName="請求日付自" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="請求日付至" msprop:Generator_ColumnVarNameInTable="column請求日付至" msprop:Generator_ColumnPropNameInRow="請求日付至" msprop:Generator_ColumnPropNameInTable="請求日付至Column" msprop:Generator_UserColumnName="請求日付至" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="工事内部コード" msprop:Generator_ColumnVarNameInTable="column工事内部コード" msprop:Generator_ColumnPropNameInRow="工事内部コード" msprop:Generator_ColumnPropNameInTable="工事内部コードColumn" msprop:Generator_UserColumnName="工事内部コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="前回額" msprop:Generator_ColumnVarNameInTable="column前回額" msprop:Generator_ColumnPropNameInRow="前回額" msprop:Generator_ColumnPropNameInTable="前回額Column" msprop:Generator_UserColumnName="前回額" type="xs:decimal" minOccurs="0" />
              <xs:element name="入金額" msprop:Generator_ColumnVarNameInTable="column入金額" msprop:Generator_ColumnPropNameInRow="入金額" msprop:Generator_ColumnPropNameInTable="入金額Column" msprop:Generator_UserColumnName="入金額" type="xs:decimal" minOccurs="0" />
              <xs:element name="調整額" msprop:Generator_ColumnVarNameInTable="column調整額" msprop:Generator_ColumnPropNameInRow="調整額" msprop:Generator_ColumnPropNameInTable="調整額Column" msprop:Generator_UserColumnName="調整額" type="xs:decimal" minOccurs="0" />
              <xs:element name="繰越額" msprop:Generator_ColumnVarNameInTable="column繰越額" msprop:Generator_ColumnPropNameInRow="繰越額" msprop:Generator_ColumnPropNameInTable="繰越額Column" msprop:Generator_UserColumnName="繰越額" type="xs:decimal" minOccurs="0" />
              <xs:element name="税抜額" msprop:Generator_ColumnVarNameInTable="column税抜額" msprop:Generator_ColumnPropNameInRow="税抜額" msprop:Generator_ColumnPropNameInTable="税抜額Column" msprop:Generator_UserColumnName="税抜額" type="xs:decimal" minOccurs="0" />
              <xs:element name="税込額" msprop:Generator_ColumnVarNameInTable="column税込額" msprop:Generator_ColumnPropNameInRow="税込額" msprop:Generator_ColumnPropNameInTable="税込額Column" msprop:Generator_UserColumnName="税込額" type="xs:decimal" minOccurs="0" />
              <xs:element name="消費税" msprop:Generator_ColumnVarNameInTable="column消費税" msprop:Generator_ColumnPropNameInRow="消費税" msprop:Generator_ColumnPropNameInTable="消費税Column" msprop:Generator_UserColumnName="消費税" type="xs:decimal" minOccurs="0" />
              <xs:element name="請求額" msprop:Generator_ColumnVarNameInTable="column請求額" msprop:Generator_ColumnPropNameInRow="請求額" msprop:Generator_ColumnPropNameInTable="請求額Column" msprop:Generator_UserColumnName="請求額" type="xs:decimal" minOccurs="0" />
              <xs:element name="工事コード" msprop:Generator_ColumnVarNameInTable="column工事コード" msprop:Generator_ColumnPropNameInRow="工事コード" msprop:Generator_ColumnPropNameInTable="工事コードColumn" msprop:Generator_UserColumnName="工事コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="工事名" msprop:Generator_ColumnVarNameInTable="column工事名" msprop:Generator_ColumnPropNameInRow="工事名" msprop:Generator_ColumnPropNameInTable="工事名Column" msprop:Generator_UserColumnName="工事名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="会社名" msprop:Generator_ColumnVarNameInTable="column会社名" msprop:Generator_ColumnPropNameInRow="会社名" msprop:Generator_ColumnPropNameInTable="会社名Column" msprop:Generator_UserColumnName="会社名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="登録番号" msprop:Generator_ColumnVarNameInTable="column登録番号" msprop:Generator_ColumnPropNameInRow="登録番号" msprop:Generator_ColumnPropNameInTable="登録番号Column" msprop:Generator_UserColumnName="登録番号" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="振込先1" msprop:Generator_ColumnVarNameInTable="column振込先1" msprop:Generator_ColumnPropNameInRow="振込先1" msprop:Generator_ColumnPropNameInTable="振込先1Column" msprop:Generator_UserColumnName="振込先1" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="グループNO" msprop:Generator_ColumnVarNameInTable="columnグループNO" msprop:Generator_ColumnPropNameInRow="グループNO" msprop:Generator_ColumnPropNameInTable="グループNOColumn" msprop:Generator_UserColumnName="グループNO" type="xs:long" minOccurs="0" />
              <xs:element name="売上番号" msprop:Generator_ColumnVarNameInTable="column売上番号" msprop:Generator_ColumnPropNameInRow="売上番号" msprop:Generator_ColumnPropNameInTable="売上番号Column" msprop:Generator_UserColumnName="売上番号" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="売上行番号" msprop:Generator_ColumnVarNameInTable="column売上行番号" msprop:Generator_ColumnPropNameInRow="売上行番号" msprop:Generator_ColumnPropNameInTable="売上行番号Column" msprop:Generator_UserColumnName="売上行番号" type="xs:decimal" minOccurs="0" />
              <xs:element name="搬入日付" msprop:Generator_ColumnVarNameInTable="column搬入日付" msprop:Generator_ColumnPropNameInRow="搬入日付" msprop:Generator_ColumnPropNameInTable="搬入日付Column" msprop:Generator_UserColumnName="搬入日付" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="商品コード" msprop:Generator_ColumnVarNameInTable="column商品コード" msprop:Generator_ColumnPropNameInRow="商品コード" msprop:Generator_ColumnPropNameInTable="商品コードColumn" msprop:Generator_UserColumnName="商品コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="商品名" msprop:Generator_ColumnVarNameInTable="column商品名" msprop:Generator_ColumnPropNameInRow="商品名" msprop:Generator_ColumnPropNameInTable="商品名Column" msprop:Generator_UserColumnName="商品名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="開始日付" msprop:Generator_ColumnVarNameInTable="column開始日付" msprop:Generator_ColumnPropNameInRow="開始日付" msprop:Generator_ColumnPropNameInTable="開始日付Column" msprop:Generator_UserColumnName="開始日付" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="終了日付" msprop:Generator_ColumnVarNameInTable="column終了日付" msprop:Generator_ColumnPropNameInRow="終了日付" msprop:Generator_ColumnPropNameInTable="終了日付Column" msprop:Generator_UserColumnName="終了日付" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="返納日付" msprop:Generator_ColumnVarNameInTable="column返納日付" msprop:Generator_ColumnPropNameInRow="返納日付" msprop:Generator_ColumnPropNameInTable="返納日付Column" msprop:Generator_UserColumnName="返納日付" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="日数" msprop:Generator_ColumnVarNameInTable="column日数" msprop:Generator_ColumnPropNameInRow="日数" msprop:Generator_ColumnPropNameInTable="日数Column" msprop:Generator_UserColumnName="日数" type="xs:int" minOccurs="0" />
              <xs:element name="数量" msprop:Generator_ColumnVarNameInTable="column数量" msprop:Generator_ColumnPropNameInRow="数量" msprop:Generator_ColumnPropNameInTable="数量Column" msprop:Generator_UserColumnName="数量" type="xs:decimal" minOccurs="0" />
              <xs:element name="単位区分" msprop:Generator_ColumnVarNameInTable="column単位区分" msprop:Generator_ColumnPropNameInRow="単位区分" msprop:Generator_ColumnPropNameInTable="単位区分Column" msprop:Generator_UserColumnName="単位区分" type="xs:short" minOccurs="0" />
              <xs:element name="単位区分名" msprop:Generator_ColumnVarNameInTable="column単位区分名" msprop:Generator_ColumnPropNameInRow="単位区分名" msprop:Generator_ColumnPropNameInTable="単位区分名Column" msprop:Generator_UserColumnName="単位区分名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="単価" msprop:Generator_ColumnVarNameInTable="column単価" msprop:Generator_ColumnPropNameInRow="単価" msprop:Generator_ColumnPropNameInTable="単価Column" msprop:Generator_UserColumnName="単価" type="xs:decimal" minOccurs="0" />
              <xs:element name="基本料金" msprop:Generator_ColumnVarNameInTable="column基本料金" msprop:Generator_ColumnPropNameInRow="基本料金" msprop:Generator_ColumnPropNameInTable="基本料金Column" msprop:Generator_UserColumnName="基本料金" type="xs:decimal" minOccurs="0" />
              <xs:element name="金額" msprop:Generator_ColumnVarNameInTable="column金額" msprop:Generator_ColumnPropNameInRow="金額" msprop:Generator_ColumnPropNameInTable="金額Column" msprop:Generator_UserColumnName="金額" type="xs:decimal" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="R納品書" msprop:Generator_TableClassName="R納品書DataTable" msprop:Generator_TableVarName="tableR納品書" msprop:Generator_TablePropName="R納品書" msprop:Generator_RowDeletingName="R納品書RowDeleting" msprop:Generator_RowChangingName="R納品書RowChanging" msprop:Generator_RowEvHandlerName="R納品書RowChangeEventHandler" msprop:Generator_RowDeletedName="R納品書RowDeleted" msprop:Generator_UserTableName="R納品書" msprop:Generator_RowChangedName="R納品書RowChanged" msprop:Generator_RowEvArgName="R納品書RowChangeEvent" msprop:Generator_RowClassName="R納品書Row">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="売上番号" msprop:Generator_ColumnVarNameInTable="column売上番号" msprop:Generator_ColumnPropNameInRow="売上番号" msprop:Generator_ColumnPropNameInTable="売上番号Column" msprop:Generator_UserColumnName="売上番号">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="行番号" msprop:Generator_ColumnVarNameInTable="column行番号" msprop:Generator_ColumnPropNameInRow="行番号" msprop:Generator_ColumnPropNameInTable="行番号Column" msprop:Generator_UserColumnName="行番号" type="xs:decimal" />
              <xs:element name="計算区分" msprop:Generator_ColumnVarNameInTable="column計算区分" msprop:Generator_ColumnPropNameInRow="計算区分" msprop:Generator_ColumnPropNameInTable="計算区分Column" msprop:Generator_UserColumnName="計算区分" type="xs:short" minOccurs="0" />
              <xs:element name="商品コード" msprop:Generator_ColumnVarNameInTable="column商品コード" msprop:Generator_ColumnPropNameInRow="商品コード" msprop:Generator_ColumnPropNameInTable="商品コードColumn" msprop:Generator_UserColumnName="商品コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="商品名" msprop:Generator_ColumnVarNameInTable="column商品名" msprop:Generator_ColumnPropNameInRow="商品名" msprop:Generator_ColumnPropNameInTable="商品名Column" msprop:Generator_UserColumnName="商品名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="数量" msprop:Generator_ColumnVarNameInTable="column数量" msprop:Generator_ColumnPropNameInRow="数量" msprop:Generator_ColumnPropNameInTable="数量Column" msprop:Generator_UserColumnName="数量" type="xs:decimal" minOccurs="0" />
              <xs:element name="単価" msprop:Generator_ColumnVarNameInTable="column単価" msprop:Generator_ColumnPropNameInRow="単価" msprop:Generator_ColumnPropNameInTable="単価Column" msprop:Generator_UserColumnName="単価" type="xs:decimal" minOccurs="0" />
              <xs:element name="基本料金" msprop:Generator_ColumnVarNameInTable="column基本料金" msprop:Generator_ColumnPropNameInRow="基本料金" msprop:Generator_ColumnPropNameInTable="基本料金Column" msprop:Generator_UserColumnName="基本料金" type="xs:decimal" minOccurs="0" />
              <xs:element name="金額" msprop:Generator_ColumnVarNameInTable="column金額" msprop:Generator_ColumnPropNameInRow="金額" msprop:Generator_ColumnPropNameInTable="金額Column" msprop:Generator_UserColumnName="金額" type="xs:decimal" minOccurs="0" />
              <xs:element name="単位区分" msprop:Generator_ColumnVarNameInTable="column単位区分" msprop:Generator_ColumnPropNameInRow="単位区分" msprop:Generator_ColumnPropNameInTable="単位区分Column" msprop:Generator_UserColumnName="単位区分" type="xs:short" minOccurs="0" />
              <xs:element name="備考明細" msprop:Generator_ColumnVarNameInTable="column備考明細" msprop:Generator_ColumnPropNameInRow="備考明細" msprop:Generator_ColumnPropNameInTable="備考明細Column" msprop:Generator_UserColumnName="備考明細" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="備考社内" msprop:Generator_ColumnVarNameInTable="column備考社内" msprop:Generator_ColumnPropNameInRow="備考社内" msprop:Generator_ColumnPropNameInTable="備考社内Column" msprop:Generator_UserColumnName="備考社内" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="搬入番号" msprop:Generator_ColumnVarNameInTable="column搬入番号" msprop:Generator_ColumnPropNameInRow="搬入番号" msprop:Generator_ColumnPropNameInTable="搬入番号Column" msprop:Generator_UserColumnName="搬入番号" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="開始日付" msprop:Generator_ColumnVarNameInTable="column開始日付" msprop:Generator_ColumnPropNameInRow="開始日付" msprop:Generator_ColumnPropNameInTable="開始日付Column" msprop:Generator_UserColumnName="開始日付" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="終了日付" msprop:Generator_ColumnVarNameInTable="column終了日付" msprop:Generator_ColumnPropNameInRow="終了日付" msprop:Generator_ColumnPropNameInTable="終了日付Column" msprop:Generator_UserColumnName="終了日付" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="返納日付" msprop:Generator_ColumnVarNameInTable="column返納日付" msprop:Generator_ColumnPropNameInRow="返納日付" msprop:Generator_ColumnPropNameInTable="返納日付Column" msprop:Generator_UserColumnName="返納日付" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="搬入日付" msprop:Generator_ColumnVarNameInTable="column搬入日付" msprop:Generator_ColumnPropNameInRow="搬入日付" msprop:Generator_ColumnPropNameInTable="搬入日付Column" msprop:Generator_UserColumnName="搬入日付" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="伝票区分" msprop:Generator_ColumnVarNameInTable="column伝票区分" msprop:Generator_ColumnPropNameInRow="伝票区分" msprop:Generator_ColumnPropNameInTable="伝票区分Column" msprop:Generator_UserColumnName="伝票区分" type="xs:short" minOccurs="0" />
              <xs:element name="得意先コード" msprop:Generator_ColumnVarNameInTable="column得意先コード" msprop:Generator_ColumnPropNameInRow="得意先コード" msprop:Generator_ColumnPropNameInTable="得意先コードColumn" msprop:Generator_UserColumnName="得意先コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="工事コード" msprop:Generator_ColumnVarNameInTable="column工事コード" msprop:Generator_ColumnPropNameInRow="工事コード" msprop:Generator_ColumnPropNameInTable="工事コードColumn" msprop:Generator_UserColumnName="工事コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="工事内部コード" msprop:Generator_ColumnVarNameInTable="column工事内部コード" msprop:Generator_ColumnPropNameInRow="工事内部コード" msprop:Generator_ColumnPropNameInTable="工事内部コードColumn" msprop:Generator_UserColumnName="工事内部コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="伝票発行区分" msprop:Generator_ColumnVarNameInTable="column伝票発行区分" msprop:Generator_ColumnPropNameInRow="伝票発行区分" msprop:Generator_ColumnPropNameInTable="伝票発行区分Column" msprop:Generator_UserColumnName="伝票発行区分" type="xs:short" minOccurs="0" />
              <xs:element name="摘要1" msprop:Generator_ColumnVarNameInTable="column摘要1" msprop:Generator_ColumnPropNameInRow="摘要1" msprop:Generator_ColumnPropNameInTable="摘要1Column" msprop:Generator_UserColumnName="摘要1" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="摘要2" msprop:Generator_ColumnVarNameInTable="column摘要2" msprop:Generator_ColumnPropNameInRow="摘要2" msprop:Generator_ColumnPropNameInTable="摘要2Column" msprop:Generator_UserColumnName="摘要2" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="得意先名" msprop:Generator_ColumnVarNameInTable="column得意先名" msprop:Generator_ColumnPropNameInRow="得意先名" msprop:Generator_ColumnPropNameInTable="得意先名Column" msprop:Generator_UserColumnName="得意先名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="工事名" msprop:Generator_ColumnVarNameInTable="column工事名" msprop:Generator_ColumnPropNameInRow="工事名" msprop:Generator_ColumnPropNameInTable="工事名Column" msprop:Generator_UserColumnName="工事名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="工事住所" msprop:Generator_ColumnVarNameInTable="column工事住所" msprop:Generator_ColumnPropNameInRow="工事住所" msprop:Generator_ColumnPropNameInTable="工事住所Column" msprop:Generator_UserColumnName="工事住所" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="伝票区分名" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column伝票区分名" msprop:Generator_ColumnPropNameInRow="伝票区分名" msprop:Generator_ColumnPropNameInTable="伝票区分名Column" msprop:Generator_UserColumnName="伝票区分名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="伝票発行区分名" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column伝票発行区分名" msprop:Generator_ColumnPropNameInRow="伝票発行区分名" msprop:Generator_ColumnPropNameInTable="伝票発行区分名Column" msprop:Generator_UserColumnName="伝票発行区分名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="計算区分名" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column計算区分名" msprop:Generator_ColumnPropNameInRow="計算区分名" msprop:Generator_ColumnPropNameInTable="計算区分名Column" msprop:Generator_UserColumnName="計算区分名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="単位区分名" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column単位区分名" msprop:Generator_ColumnPropNameInRow="単位区分名" msprop:Generator_ColumnPropNameInTable="単位区分名Column" msprop:Generator_UserColumnName="単位区分名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="会社名" msprop:Generator_ColumnVarNameInTable="column会社名" msprop:Generator_ColumnPropNameInRow="会社名" msprop:Generator_ColumnPropNameInTable="会社名Column" msprop:Generator_UserColumnName="会社名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="会社郵便番号" msprop:Generator_ColumnVarNameInTable="column会社郵便番号" msprop:Generator_ColumnPropNameInRow="会社郵便番号" msprop:Generator_ColumnPropNameInTable="会社郵便番号Column" msprop:Generator_UserColumnName="会社郵便番号" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="会社住所1" msprop:Generator_ColumnVarNameInTable="column会社住所1" msprop:Generator_ColumnPropNameInRow="会社住所1" msprop:Generator_ColumnPropNameInTable="会社住所1Column" msprop:Generator_UserColumnName="会社住所1" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="会社住所2" msprop:Generator_ColumnVarNameInTable="column会社住所2" msprop:Generator_ColumnPropNameInRow="会社住所2" msprop:Generator_ColumnPropNameInTable="会社住所2Column" msprop:Generator_UserColumnName="会社住所2" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="会社電話番号" msprop:Generator_ColumnVarNameInTable="column会社電話番号" msprop:Generator_ColumnPropNameInRow="会社電話番号" msprop:Generator_ColumnPropNameInTable="会社電話番号Column" msprop:Generator_UserColumnName="会社電話番号" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="会社FAX番号" msprop:Generator_ColumnVarNameInTable="column会社FAX番号" msprop:Generator_ColumnPropNameInRow="会社FAX番号" msprop:Generator_ColumnPropNameInTable="会社FAX番号Column" msprop:Generator_UserColumnName="会社FAX番号" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="明細件数" msprop:Generator_ColumnVarNameInTable="column明細件数" msprop:Generator_ColumnPropNameInRow="明細件数" msprop:Generator_ColumnPropNameInTable="明細件数Column" msprop:Generator_UserColumnName="明細件数" type="xs:int" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
    <xs:unique name="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:R請求書鑑" />
      <xs:field xpath="mstns:請求番号" />
      <xs:field xpath="mstns:行番号" />
    </xs:unique>
    <xs:unique name="R請求一覧表_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:R請求一覧表" />
      <xs:field xpath="mstns:請求番号" />
      <xs:field xpath="mstns:行番号" />
    </xs:unique>
    <xs:unique name="R納品書_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:R納品書" />
      <xs:field xpath="mstns:売上番号" />
      <xs:field xpath="mstns:行番号" />
    </xs:unique>
  </xs:element>
</xs:schema>