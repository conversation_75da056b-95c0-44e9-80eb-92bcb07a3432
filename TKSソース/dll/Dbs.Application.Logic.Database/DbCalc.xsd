﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="DbCalc" targetNamespace="http://tempuri.org/DbCalc.xsd" xmlns:mstns="http://tempuri.org/DbCalc.xsd" xmlns="http://tempuri.org/DbCalc.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections>
          <Connection AppSettingsObjectName="MySettings" AppSettingsPropertyName="ConnectionString" ConnectionStringObject="" IsAppSettingsProperty="true" Modifier="Assembly" Name="ConnectionString (MySettings)" ParameterPrefix="@" PropertyReference="ApplicationSettings.Dbs.Application.Logic.Database.My.MySettings.GlobalReference.Default.ConnectionString" Provider="System.Data.SqlClient" />
        </Connections>
        <Tables>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="F売上伝票検索TableAdapter" GeneratorDataComponentClassName="F売上伝票検索TableAdapter" Name="F売上伝票検索" UserDataComponentName="F売上伝票検索TableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="TokoDB.dbo.F売上伝票検索" DbObjectType="Function" GenerateMethods="Get" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="GetData">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT                      F売上伝票検索.*
FROM                         dbo.F売上伝票検索(@得意先コード,@工事内部コード,@搬入日付F,@搬入日付T,@売上番号)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="得意先コード" ColumnName="" DataSourceName="" DataTypeServer="varchar(6)" DbType="AnsiString" Direction="Input" ParameterName="@得意先コード" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="工事内部コード" ColumnName="" DataSourceName="" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@工事内部コード" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="搬入日付F" ColumnName="" DataSourceName="" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@搬入日付F" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="搬入日付T" ColumnName="" DataSourceName="" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@搬入日付T" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="売上番号" ColumnName="" DataSourceName="" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@売上番号" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="売上番号" DataSetColumn="売上番号" />
              <Mapping SourceColumn="搬入日付" DataSetColumn="搬入日付" />
              <Mapping SourceColumn="伝票区分" DataSetColumn="伝票区分" />
              <Mapping SourceColumn="得意先コード" DataSetColumn="得意先コード" />
              <Mapping SourceColumn="工事コード" DataSetColumn="工事コード" />
              <Mapping SourceColumn="工事内部コード" DataSetColumn="工事内部コード" />
              <Mapping SourceColumn="摘要1" DataSetColumn="摘要1" />
              <Mapping SourceColumn="摘要2" DataSetColumn="摘要2" />
              <Mapping SourceColumn="備考" DataSetColumn="備考" />
              <Mapping SourceColumn="伝票発行区分" DataSetColumn="伝票発行区分" />
              <Mapping SourceColumn="登録日時" DataSetColumn="登録日時" />
              <Mapping SourceColumn="登録者ID" DataSetColumn="登録者ID" />
              <Mapping SourceColumn="更新日時" DataSetColumn="更新日時" />
              <Mapping SourceColumn="更新者ID" DataSetColumn="更新者ID" />
              <Mapping SourceColumn="得意先名" DataSetColumn="得意先名" />
              <Mapping SourceColumn="工事名" DataSetColumn="工事名" />
              <Mapping SourceColumn="伝票区分名" DataSetColumn="伝票区分名" />
              <Mapping SourceColumn="伝票発行区分名" DataSetColumn="伝票発行区分名" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="F売上定期処理TableAdapter" GeneratorDataComponentClassName="F売上定期処理TableAdapter" Name="F売上定期処理" UserDataComponentName="F売上定期処理TableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="TokoDB.dbo.F売上定期処理" DbObjectType="Function" GenerateMethods="Get" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="GetData">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT                      F売上定期処理.*
FROM                         dbo.F売上定期処理(@セッションID,@売上日付)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="セッションID" ColumnName="" DataSourceName="" DataTypeServer="varchar(255)" DbType="AnsiString" Direction="Input" ParameterName="@セッションID" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="売上日付" ColumnName="" DataSourceName="" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@売上日付" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="売上番号" DataSetColumn="売上番号" />
              <Mapping SourceColumn="行番号" DataSetColumn="行番号" />
              <Mapping SourceColumn="伝票区分" DataSetColumn="伝票区分" />
              <Mapping SourceColumn="搬入日付" DataSetColumn="搬入日付" />
              <Mapping SourceColumn="得意先コード" DataSetColumn="得意先コード" />
              <Mapping SourceColumn="工事コード" DataSetColumn="工事コード" />
              <Mapping SourceColumn="工事内部コード" DataSetColumn="工事内部コード" />
              <Mapping SourceColumn="摘要1" DataSetColumn="摘要1" />
              <Mapping SourceColumn="摘要2" DataSetColumn="摘要2" />
              <Mapping SourceColumn="備考" DataSetColumn="備考" />
              <Mapping SourceColumn="伝票発行区分" DataSetColumn="伝票発行区分" />
              <Mapping SourceColumn="請求番号" DataSetColumn="請求番号" />
              <Mapping SourceColumn="計算区分" DataSetColumn="計算区分" />
              <Mapping SourceColumn="商品コード" DataSetColumn="商品コード" />
              <Mapping SourceColumn="商品名" DataSetColumn="商品名" />
              <Mapping SourceColumn="備考明細" DataSetColumn="備考明細" />
              <Mapping SourceColumn="備考社内" DataSetColumn="備考社内" />
              <Mapping SourceColumn="数量" DataSetColumn="数量" />
              <Mapping SourceColumn="単位区分" DataSetColumn="単位区分" />
              <Mapping SourceColumn="単価" DataSetColumn="単価" />
              <Mapping SourceColumn="基本料金" DataSetColumn="基本料金" />
              <Mapping SourceColumn="搬入番号" DataSetColumn="搬入番号" />
              <Mapping SourceColumn="開始日付" DataSetColumn="開始日付" />
              <Mapping SourceColumn="終了日付" DataSetColumn="終了日付" />
              <Mapping SourceColumn="返納日付" DataSetColumn="返納日付" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="F請求締期間TableAdapter" GeneratorDataComponentClassName="F請求締期間TableAdapter" Name="F請求締期間" UserDataComponentName="F請求締期間TableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="TokoDB.dbo.F請求締期間" DbObjectType="Function" GenerateMethods="Get" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="GetData">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT                      F請求締期間.*
FROM                         dbo.F請求締期間(@締日区分,@請求年月)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="締日区分" ColumnName="" DataSourceName="" DataTypeServer="smallint" DbType="Int32" Direction="Input" ParameterName="@締日区分" Precision="0" Scale="0" Size="0" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="請求年月" ColumnName="" DataSourceName="" DataTypeServer="varchar(7)" DbType="AnsiString" Direction="Input" ParameterName="@請求年月" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="請求番号" DataSetColumn="請求番号" />
              <Mapping SourceColumn="得意先コード" DataSetColumn="得意先コード" />
              <Mapping SourceColumn="請求年月" DataSetColumn="請求年月" />
              <Mapping SourceColumn="締日区分" DataSetColumn="締日区分" />
              <Mapping SourceColumn="請求日付自" DataSetColumn="請求日付自" />
              <Mapping SourceColumn="請求日付至" DataSetColumn="請求日付至" />
              <Mapping SourceColumn="順序" DataSetColumn="順序" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="F請求計算検索TableAdapter" GeneratorDataComponentClassName="F請求計算検索TableAdapter" Name="F請求計算検索" UserDataComponentName="F請求計算検索TableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="TokoDB.dbo.F請求計算検索" DbObjectType="Function" GenerateMethods="Get" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="GetData">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT                      F請求計算検索.*
FROM                         dbo.F請求計算検索(@締日区分,@請求年月,@請求日付F,@請求日付T,@得意先コードF,@得意先コードT)
ORDER
   BY  得意先コード</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="締日区分" ColumnName="" DataSourceName="" DataTypeServer="smallint" DbType="Int32" Direction="Input" ParameterName="@締日区分" Precision="0" Scale="0" Size="0" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="請求年月" ColumnName="" DataSourceName="" DataTypeServer="varchar(7)" DbType="AnsiString" Direction="Input" ParameterName="@請求年月" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="請求日付F" ColumnName="" DataSourceName="" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@請求日付F" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="請求日付T" ColumnName="" DataSourceName="" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@請求日付T" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="得意先コードF" ColumnName="" DataSourceName="" DataTypeServer="varchar(6)" DbType="AnsiString" Direction="Input" ParameterName="@得意先コードF" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="得意先コードT" ColumnName="" DataSourceName="" DataTypeServer="varchar(6)" DbType="AnsiString" Direction="Input" ParameterName="@得意先コードT" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="得意先コード" DataSetColumn="得意先コード" />
              <Mapping SourceColumn="得意先名" DataSetColumn="得意先名" />
              <Mapping SourceColumn="請求番号" DataSetColumn="請求番号" />
              <Mapping SourceColumn="請求日付自" DataSetColumn="請求日付自" />
              <Mapping SourceColumn="請求日付至" DataSetColumn="請求日付至" />
              <Mapping SourceColumn="前回額" DataSetColumn="前回額" />
              <Mapping SourceColumn="入金額" DataSetColumn="入金額" />
              <Mapping SourceColumn="調整額" DataSetColumn="調整額" />
              <Mapping SourceColumn="繰越額" DataSetColumn="繰越額" />
              <Mapping SourceColumn="税抜額" DataSetColumn="税抜額" />
              <Mapping SourceColumn="税込額" DataSetColumn="税込額" />
              <Mapping SourceColumn="消費税" DataSetColumn="消費税" />
              <Mapping SourceColumn="請求額" DataSetColumn="請求額" />
              <Mapping SourceColumn="備考" DataSetColumn="備考" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="F請求計算DTTableAdapter" GeneratorDataComponentClassName="F請求計算DTTableAdapter" Name="F請求計算DT" UserDataComponentName="F請求計算DTTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="TokoDB.dbo.F請求計算DT" DbObjectType="Function" GenerateMethods="Get" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="GetData">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT                      F請求計算DT.*
FROM                         dbo.F請求計算DT(@締日区分,@請求日付F,@請求日付T,@選択リスト)
ORDER
  BY  得意先コード
  ,  工事コード</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="締日区分" ColumnName="" DataSourceName="" DataTypeServer="smallint" DbType="Int32" Direction="Input" ParameterName="@締日区分" Precision="0" Scale="0" Size="0" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="請求日付F" ColumnName="" DataSourceName="" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@請求日付F" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="請求日付T" ColumnName="" DataSourceName="" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@請求日付T" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="選択リスト" ColumnName="" DataSourceName="" DataTypeServer="varchar(4000)" DbType="AnsiString" Direction="Input" ParameterName="@選択リスト" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="工事内部コード" DataSetColumn="工事内部コード" />
              <Mapping SourceColumn="得意先コード" DataSetColumn="得意先コード" />
              <Mapping SourceColumn="工事コード" DataSetColumn="工事コード" />
              <Mapping SourceColumn="工事名" DataSetColumn="工事名" />
              <Mapping SourceColumn="前回額" DataSetColumn="前回額" />
              <Mapping SourceColumn="入金額" DataSetColumn="入金額" />
              <Mapping SourceColumn="調整額" DataSetColumn="調整額" />
              <Mapping SourceColumn="繰越額" DataSetColumn="繰越額" />
              <Mapping SourceColumn="税抜額" DataSetColumn="税抜額" />
              <Mapping SourceColumn="消費税" DataSetColumn="消費税" />
              <Mapping SourceColumn="税込額" DataSetColumn="税込額" />
              <Mapping SourceColumn="請求額" DataSetColumn="請求額" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="F請求計算HDTableAdapter" GeneratorDataComponentClassName="F請求計算HDTableAdapter" Name="F請求計算HD" UserDataComponentName="F請求計算HDTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="TokoDB.dbo.F請求計算HD" DbObjectType="Function" GenerateMethods="Get" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="GetData">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT                      F請求計算HD.*
FROM                         dbo.F請求計算HD(@請求リスト)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="請求リスト" ColumnName="" DataSourceName="" DataTypeServer="varchar(4000)" DbType="AnsiString" Direction="Input" ParameterName="@請求リスト" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="請求番号" DataSetColumn="請求番号" />
              <Mapping SourceColumn="得意先コード" DataSetColumn="得意先コード" />
              <Mapping SourceColumn="前回額" DataSetColumn="前回額" />
              <Mapping SourceColumn="入金額" DataSetColumn="入金額" />
              <Mapping SourceColumn="調整額" DataSetColumn="調整額" />
              <Mapping SourceColumn="繰越額" DataSetColumn="繰越額" />
              <Mapping SourceColumn="税抜額" DataSetColumn="税抜額" />
              <Mapping SourceColumn="税込額" DataSetColumn="税込額" />
              <Mapping SourceColumn="消費税" DataSetColumn="消費税" />
              <Mapping SourceColumn="請求額" DataSetColumn="請求額" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="F売上一覧表TableAdapter" GeneratorDataComponentClassName="F売上一覧表TableAdapter" Name="F売上一覧表" UserDataComponentName="F売上一覧表TableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="TokoDB.dbo.F売上一覧表" DbObjectType="Function" GenerateMethods="Get" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="GetData">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT                      F売上一覧表.*
FROM                         dbo.F売上一覧表(@売上番号リスト)
ORDER
  BY  売上番号
   ,  行番号</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="売上番号リスト" ColumnName="" DataSourceName="" DataTypeServer="varchar(4000)" DbType="AnsiString" Direction="Input" ParameterName="@売上番号リスト" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="売上番号" DataSetColumn="売上番号" />
              <Mapping SourceColumn="行番号" DataSetColumn="行番号" />
              <Mapping SourceColumn="伝票区分" DataSetColumn="伝票区分" />
              <Mapping SourceColumn="伝票区分名" DataSetColumn="伝票区分名" />
              <Mapping SourceColumn="搬入日付" DataSetColumn="搬入日付" />
              <Mapping SourceColumn="得意先コード" DataSetColumn="得意先コード" />
              <Mapping SourceColumn="得意先名" DataSetColumn="得意先名" />
              <Mapping SourceColumn="工事コード" DataSetColumn="工事コード" />
              <Mapping SourceColumn="工事内部コード" DataSetColumn="工事内部コード" />
              <Mapping SourceColumn="工事名" DataSetColumn="工事名" />
              <Mapping SourceColumn="住所" DataSetColumn="住所" />
              <Mapping SourceColumn="摘要1" DataSetColumn="摘要1" />
              <Mapping SourceColumn="摘要2" DataSetColumn="摘要2" />
              <Mapping SourceColumn="計算区分" DataSetColumn="計算区分" />
              <Mapping SourceColumn="商品コード" DataSetColumn="商品コード" />
              <Mapping SourceColumn="商品名" DataSetColumn="商品名" />
              <Mapping SourceColumn="備考明細" DataSetColumn="備考明細" />
              <Mapping SourceColumn="備考社内" DataSetColumn="備考社内" />
              <Mapping SourceColumn="数量" DataSetColumn="数量" />
              <Mapping SourceColumn="単位区分" DataSetColumn="単位区分" />
              <Mapping SourceColumn="単位区分名" DataSetColumn="単位区分名" />
              <Mapping SourceColumn="単価" DataSetColumn="単価" />
              <Mapping SourceColumn="基本料金" DataSetColumn="基本料金" />
              <Mapping SourceColumn="金額" DataSetColumn="金額" />
              <Mapping SourceColumn="搬入番号" DataSetColumn="搬入番号" />
              <Mapping SourceColumn="開始日付" DataSetColumn="開始日付" />
              <Mapping SourceColumn="終了日付" DataSetColumn="終了日付" />
              <Mapping SourceColumn="返納日付" DataSetColumn="返納日付" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="F売上定期検索TableAdapter" GeneratorDataComponentClassName="F売上定期検索TableAdapter" Name="F売上定期検索" UserDataComponentName="F売上定期検索TableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="TokoDB.dbo.F売上定期検索" DbObjectType="Function" GenerateMethods="Get" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="GetData">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT                      F売上定期検索.*
FROM                         dbo.F売上定期検索(@売上日付,@得意先コード)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="売上日付" ColumnName="" DataSourceName="" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@売上日付" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="得意先コード" ColumnName="" DataSourceName="" DataTypeServer="varchar(6)" DbType="AnsiString" Direction="Input" ParameterName="@得意先コード" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="売上番号" DataSetColumn="売上番号" />
              <Mapping SourceColumn="行番号" DataSetColumn="行番号" />
              <Mapping SourceColumn="伝票区分" DataSetColumn="伝票区分" />
              <Mapping SourceColumn="搬入日付" DataSetColumn="搬入日付" />
              <Mapping SourceColumn="得意先コード" DataSetColumn="得意先コード" />
              <Mapping SourceColumn="工事コード" DataSetColumn="工事コード" />
              <Mapping SourceColumn="工事内部コード" DataSetColumn="工事内部コード" />
              <Mapping SourceColumn="摘要1" DataSetColumn="摘要1" />
              <Mapping SourceColumn="摘要2" DataSetColumn="摘要2" />
              <Mapping SourceColumn="計算区分" DataSetColumn="計算区分" />
              <Mapping SourceColumn="商品コード" DataSetColumn="商品コード" />
              <Mapping SourceColumn="商品名" DataSetColumn="商品名" />
              <Mapping SourceColumn="備考明細" DataSetColumn="備考明細" />
              <Mapping SourceColumn="備考社内" DataSetColumn="備考社内" />
              <Mapping SourceColumn="数量" DataSetColumn="数量" />
              <Mapping SourceColumn="単位区分" DataSetColumn="単位区分" />
              <Mapping SourceColumn="単価" DataSetColumn="単価" />
              <Mapping SourceColumn="基本料金" DataSetColumn="基本料金" />
              <Mapping SourceColumn="金額" DataSetColumn="金額" />
              <Mapping SourceColumn="搬入番号" DataSetColumn="搬入番号" />
              <Mapping SourceColumn="開始日付" DataSetColumn="開始日付" />
              <Mapping SourceColumn="終了日付" DataSetColumn="終了日付" />
              <Mapping SourceColumn="返納日付" DataSetColumn="返納日付" />
              <Mapping SourceColumn="得意先名" DataSetColumn="得意先名" />
              <Mapping SourceColumn="工事名" DataSetColumn="工事名" />
              <Mapping SourceColumn="締日区分" DataSetColumn="締日区分" />
              <Mapping SourceColumn="売上行番号" DataSetColumn="売上行番号" />
              <Mapping SourceColumn="伝票区分名" DataSetColumn="伝票区分名" />
              <Mapping SourceColumn="単位区分名" DataSetColumn="単位区分名" />
              <Mapping SourceColumn="締日区分名" DataSetColumn="締日区分名" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="Fリース商品一覧TableAdapter" GeneratorDataComponentClassName="Fリース商品一覧TableAdapter" Name="Fリース商品一覧" UserDataComponentName="Fリース商品一覧TableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="TokoDB.dbo.Fリース商品一覧" DbObjectType="Function" GenerateMethods="Get" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="GetData">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT                      Fリース商品一覧.*
FROM                         dbo.Fリース商品一覧(@商品コードF,@商品コードT,@得意先コード,@工事内部コード)
ORDER
  BY  商品コード
   ,  _行区分

</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="商品コードF" ColumnName="" DataSourceName="" DataTypeServer="varchar(6)" DbType="AnsiString" Direction="Input" ParameterName="@商品コードF" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="商品コードT" ColumnName="" DataSourceName="" DataTypeServer="varchar(6)" DbType="AnsiString" Direction="Input" ParameterName="@商品コードT" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="得意先コード" ColumnName="" DataSourceName="" DataTypeServer="varchar(6)" DbType="AnsiString" Direction="Input" ParameterName="@得意先コード" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="工事内部コード" ColumnName="" DataSourceName="" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@工事内部コード" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="商品コード" DataSetColumn="商品コード" />
              <Mapping SourceColumn="商品名" DataSetColumn="商品名" />
              <Mapping SourceColumn="得意先コード" DataSetColumn="得意先コード" />
              <Mapping SourceColumn="得意先名" DataSetColumn="得意先名" />
              <Mapping SourceColumn="工事コード" DataSetColumn="工事コード" />
              <Mapping SourceColumn="工事名" DataSetColumn="工事名" />
              <Mapping SourceColumn="数量" DataSetColumn="数量" />
              <Mapping SourceColumn="開始日付" DataSetColumn="開始日付" />
              <Mapping SourceColumn="返納日付" DataSetColumn="返納日付" />
              <Mapping SourceColumn="_工事内部コード" DataSetColumn="_工事内部コード" />
              <Mapping SourceColumn="_行区分" DataSetColumn="_行区分" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="F工事残高TableAdapter" GeneratorDataComponentClassName="F工事残高TableAdapter" Name="F工事残高" UserDataComponentName="F工事残高TableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="TokoDB.dbo.F工事残高" DbObjectType="Function" GenerateMethods="Get" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="GetData">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT                      F工事残高.*
FROM                         dbo.F工事残高(@得意先コード,@工事内部コード)
ORDER
     BY  _得意先コード
   ,  請求年月
  ,  _工事コード</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="得意先コード" ColumnName="" DataSourceName="" DataTypeServer="varchar(6)" DbType="AnsiString" Direction="Input" ParameterName="@得意先コード" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="工事内部コード" ColumnName="" DataSourceName="" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@工事内部コード" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="請求年月" DataSetColumn="請求年月" />
              <Mapping SourceColumn="得意先名" DataSetColumn="得意先名" />
              <Mapping SourceColumn="工事名" DataSetColumn="工事名" />
              <Mapping SourceColumn="残高額" DataSetColumn="残高額" />
              <Mapping SourceColumn="_得意先コード" DataSetColumn="_得意先コード" />
              <Mapping SourceColumn="_工事コード" DataSetColumn="_工事コード" />
              <Mapping SourceColumn="_工事内部" DataSetColumn="_工事内部" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="F入金一覧表TableAdapter" GeneratorDataComponentClassName="F入金一覧表TableAdapter" Name="F入金一覧表" UserDataComponentName="F入金一覧表TableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="TokoDB.dbo.F入金一覧表" DbObjectType="Function" GenerateMethods="Get" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="GetData">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT                      F入金一覧表.*
FROM                         dbo.F入金一覧表(@得意先コード,@工事内部コード,@入金日付F,@入金日付T,@入金番号)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="得意先コード" ColumnName="" DataSourceName="" DataTypeServer="varchar(6)" DbType="AnsiString" Direction="Input" ParameterName="@得意先コード" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="工事内部コード" ColumnName="" DataSourceName="" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@工事内部コード" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="入金日付F" ColumnName="" DataSourceName="" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@入金日付F" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="入金日付T" ColumnName="" DataSourceName="" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@入金日付T" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="入金番号" ColumnName="" DataSourceName="" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@入金番号" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="入金番号" DataSetColumn="入金番号" />
              <Mapping SourceColumn="入金日付" DataSetColumn="入金日付" />
              <Mapping SourceColumn="請求年月" DataSetColumn="請求年月" />
              <Mapping SourceColumn="得意先コード" DataSetColumn="得意先コード" />
              <Mapping SourceColumn="得意先名" DataSetColumn="得意先名" />
              <Mapping SourceColumn="工事コード" DataSetColumn="工事コード" />
              <Mapping SourceColumn="工事名" DataSetColumn="工事名" />
              <Mapping SourceColumn="入金額" DataSetColumn="入金額" />
              <Mapping SourceColumn="調整額" DataSetColumn="調整額" />
              <Mapping SourceColumn="_工事内部コード" DataSetColumn="_工事内部コード" />
              <Mapping SourceColumn="_行区分" DataSetColumn="_行区分" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="Fリース商品残数TableAdapter" GeneratorDataComponentClassName="Fリース商品残数TableAdapter" Name="Fリース商品残数" UserDataComponentName="Fリース商品残数TableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="TokoDB.dbo.Fリース商品残数" DbObjectType="Function" GenerateMethods="Get" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="GetData">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT                      Fリース商品残数.*
FROM                         dbo.Fリース商品残数(@搬入伝票NO,@返納伝票NO,@商品コード)
</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="搬入伝票NO" ColumnName="" DataSourceName="" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@搬入伝票NO" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="返納伝票NO" ColumnName="" DataSourceName="" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@返納伝票NO" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="商品コード" ColumnName="" DataSourceName="" DataTypeServer="varchar(6)" DbType="AnsiString" Direction="Input" ParameterName="@商品コード" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="売上番号" DataSetColumn="売上番号" />
              <Mapping SourceColumn="行番号" DataSetColumn="行番号" />
              <Mapping SourceColumn="商品コード" DataSetColumn="商品コード" />
              <Mapping SourceColumn="搬入数量" DataSetColumn="搬入数量" />
              <Mapping SourceColumn="返納数量" DataSetColumn="返納数量" />
              <Mapping SourceColumn="残数量" DataSetColumn="残数量" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="F請求書検索TableAdapter" GeneratorDataComponentClassName="F請求書検索TableAdapter" Name="F請求書検索" UserDataComponentName="F請求書検索TableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="tokoDB.dbo.F請求書検索" DbObjectType="Function" GenerateMethods="Get" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="GetData">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT                      F請求書検索.*
FROM                         dbo.F請求書検索(@締日区分,@請求年月,@得意先コードF,@得意先コードT,@担当者コード,@諸口区分)
</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="締日区分" ColumnName="" DataSourceName="" DataTypeServer="smallint" DbType="Int32" Direction="Input" ParameterName="@締日区分" Precision="0" Scale="0" Size="0" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="請求年月" ColumnName="" DataSourceName="" DataTypeServer="varchar(7)" DbType="AnsiString" Direction="Input" ParameterName="@請求年月" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="得意先コードF" ColumnName="" DataSourceName="" DataTypeServer="varchar(6)" DbType="AnsiString" Direction="Input" ParameterName="@得意先コードF" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="得意先コードT" ColumnName="" DataSourceName="" DataTypeServer="varchar(6)" DbType="AnsiString" Direction="Input" ParameterName="@得意先コードT" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="担当者コード" ColumnName="" DataSourceName="" DataTypeServer="varchar(50)" DbType="AnsiString" Direction="Input" ParameterName="@担当者コード" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="諸口区分" ColumnName="" DataSourceName="" DataTypeServer="smallint" DbType="Int32" Direction="Input" ParameterName="@諸口区分" Precision="0" Scale="0" Size="0" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="請求番号" DataSetColumn="請求番号" />
              <Mapping SourceColumn="請求年月" DataSetColumn="請求年月" />
              <Mapping SourceColumn="締日区分" DataSetColumn="締日区分" />
              <Mapping SourceColumn="得意先コード" DataSetColumn="得意先コード" />
              <Mapping SourceColumn="得意先名" DataSetColumn="得意先名" />
              <Mapping SourceColumn="請求日付自" DataSetColumn="請求日付自" />
              <Mapping SourceColumn="請求日付至" DataSetColumn="請求日付至" />
              <Mapping SourceColumn="前回額" DataSetColumn="前回額" />
              <Mapping SourceColumn="入金額" DataSetColumn="入金額" />
              <Mapping SourceColumn="調整額" DataSetColumn="調整額" />
              <Mapping SourceColumn="繰越額" DataSetColumn="繰越額" />
              <Mapping SourceColumn="税抜額" DataSetColumn="税抜額" />
              <Mapping SourceColumn="税込額" DataSetColumn="税込額" />
              <Mapping SourceColumn="消費税" DataSetColumn="消費税" />
              <Mapping SourceColumn="請求額" DataSetColumn="請求額" />
              <Mapping SourceColumn="備考" DataSetColumn="備考" />
              <Mapping SourceColumn="担当者コード" DataSetColumn="担当者コード" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="F1工事台帳TableAdapter" GeneratorDataComponentClassName="F1工事台帳TableAdapter" Name="F1工事台帳" UserDataComponentName="F1工事台帳TableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="tokoDB.dbo.F1工事台帳" DbObjectType="Function" GenerateMethods="Get" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="GetData">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT                      F1工事台帳.*
FROM                         dbo.F1工事台帳(@得意先コード,@工事コードF,@工事コードT,@対象期間F,@対象期間T)
ORDER
     BY  得意先コード
      ,  工事コード
      ,  集計年月
      ,  日付
      ,  伝票区分
</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="得意先コード" ColumnName="" DataSourceName="" DataTypeServer="varchar(6)" DbType="AnsiString" Direction="Input" ParameterName="@得意先コード" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="工事コードF" ColumnName="" DataSourceName="" DataTypeServer="varchar(4)" DbType="AnsiString" Direction="Input" ParameterName="@工事コードF" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="工事コードT" ColumnName="" DataSourceName="" DataTypeServer="varchar(4)" DbType="AnsiString" Direction="Input" ParameterName="@工事コードT" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="対象期間F" ColumnName="" DataSourceName="" DataTypeServer="varchar(7)" DbType="AnsiString" Direction="Input" ParameterName="@対象期間F" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="対象期間T" ColumnName="" DataSourceName="" DataTypeServer="varchar(7)" DbType="AnsiString" Direction="Input" ParameterName="@対象期間T" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="伝票番号" DataSetColumn="伝票番号" />
              <Mapping SourceColumn="行番号" DataSetColumn="行番号" />
              <Mapping SourceColumn="得意先コード" DataSetColumn="得意先コード" />
              <Mapping SourceColumn="得意先名" DataSetColumn="得意先名" />
              <Mapping SourceColumn="工事内部コード" DataSetColumn="工事内部コード" />
              <Mapping SourceColumn="工事コード" DataSetColumn="工事コード" />
              <Mapping SourceColumn="工事名" DataSetColumn="工事名" />
              <Mapping SourceColumn="集計年月" DataSetColumn="集計年月" />
              <Mapping SourceColumn="日付" DataSetColumn="日付" />
              <Mapping SourceColumn="商品コード" DataSetColumn="商品コード" />
              <Mapping SourceColumn="商品名" DataSetColumn="商品名" />
              <Mapping SourceColumn="数量" DataSetColumn="数量" />
              <Mapping SourceColumn="単位区分" DataSetColumn="単位区分" />
              <Mapping SourceColumn="単位区分名" DataSetColumn="単位区分名" />
              <Mapping SourceColumn="単価" DataSetColumn="単価" />
              <Mapping SourceColumn="基本料金" DataSetColumn="基本料金" />
              <Mapping SourceColumn="売上金額" DataSetColumn="売上金額" />
              <Mapping SourceColumn="入金金額" DataSetColumn="入金金額" />
              <Mapping SourceColumn="請求残高" DataSetColumn="請求残高" />
              <Mapping SourceColumn="開始日付" DataSetColumn="開始日付" />
              <Mapping SourceColumn="終了日付" DataSetColumn="終了日付" />
              <Mapping SourceColumn="備考" DataSetColumn="備考" />
              <Mapping SourceColumn="伝票区分" DataSetColumn="伝票区分" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="Fリース商品検索TableAdapter" GeneratorDataComponentClassName="Fリース商品検索TableAdapter" Name="Fリース商品検索" UserDataComponentName="Fリース商品検索TableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="tokoDB.dbo.Fリース商品検索" DbObjectType="Function" GenerateMethods="Get" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="GetData">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT                      Fリース商品検索.*
FROM                         dbo.Fリース商品検索(@得意先コード,@工事コードF,@工事コードT)
ORDER
     BY  売上番号
     ,  行番号

</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="得意先コード" ColumnName="" DataSourceName="" DataTypeServer="varchar(6)" DbType="AnsiString" Direction="Input" ParameterName="@得意先コード" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="工事コードF" ColumnName="" DataSourceName="" DataTypeServer="varchar(4)" DbType="AnsiString" Direction="Input" ParameterName="@工事コードF" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="工事コードT" ColumnName="" DataSourceName="" DataTypeServer="varchar(4)" DbType="AnsiString" Direction="Input" ParameterName="@工事コードT" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="売上番号" DataSetColumn="売上番号" />
              <Mapping SourceColumn="行番号" DataSetColumn="行番号" />
              <Mapping SourceColumn="得意先コード" DataSetColumn="得意先コード" />
              <Mapping SourceColumn="得意先名" DataSetColumn="得意先名" />
              <Mapping SourceColumn="工事コード" DataSetColumn="工事コード" />
              <Mapping SourceColumn="工事内部コード" DataSetColumn="工事内部コード" />
              <Mapping SourceColumn="工事名" DataSetColumn="工事名" />
              <Mapping SourceColumn="商品コード" DataSetColumn="商品コード" />
              <Mapping SourceColumn="商品名" DataSetColumn="商品名" />
              <Mapping SourceColumn="数量" DataSetColumn="数量" />
              <Mapping SourceColumn="単価" DataSetColumn="単価" />
              <Mapping SourceColumn="開始日付" DataSetColumn="開始日付" />
              <Mapping SourceColumn="終了日付" DataSetColumn="終了日付" />
              <Mapping SourceColumn="売上行番号" DataSetColumn="売上行番号" />
            </Mappings>
            <Sources />
          </TableAdapter>
        </Tables>
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="DbCalc" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:EnableTableAdapterManager="true" msprop:Generator_DataSetName="DbCalc" msprop:Generator_UserDSName="DbCalc">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="F売上伝票検索" msprop:Generator_TableClassName="F売上伝票検索DataTable" msprop:Generator_TableVarName="tableF売上伝票検索" msprop:Generator_TablePropName="F売上伝票検索" msprop:Generator_RowDeletingName="F売上伝票検索RowDeleting" msprop:Generator_RowChangingName="F売上伝票検索RowChanging" msprop:Generator_RowEvHandlerName="F売上伝票検索RowChangeEventHandler" msprop:Generator_RowDeletedName="F売上伝票検索RowDeleted" msprop:Generator_UserTableName="F売上伝票検索" msprop:Generator_RowChangedName="F売上伝票検索RowChanged" msprop:Generator_RowEvArgName="F売上伝票検索RowChangeEvent" msprop:Generator_RowClassName="F売上伝票検索Row">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="売上番号" msprop:Generator_ColumnVarNameInTable="column売上番号" msprop:Generator_ColumnPropNameInRow="売上番号" msprop:Generator_ColumnPropNameInTable="売上番号Column" msprop:Generator_UserColumnName="売上番号">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="搬入日付" msprop:Generator_ColumnVarNameInTable="column搬入日付" msprop:Generator_ColumnPropNameInRow="搬入日付" msprop:Generator_ColumnPropNameInTable="搬入日付Column" msprop:Generator_UserColumnName="搬入日付" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="伝票区分" msprop:Generator_ColumnVarNameInTable="column伝票区分" msprop:Generator_ColumnPropNameInRow="伝票区分" msprop:Generator_ColumnPropNameInTable="伝票区分Column" msprop:Generator_UserColumnName="伝票区分" type="xs:short" minOccurs="0" />
              <xs:element name="得意先コード" msprop:Generator_ColumnVarNameInTable="column得意先コード" msprop:Generator_ColumnPropNameInRow="得意先コード" msprop:Generator_ColumnPropNameInTable="得意先コードColumn" msprop:Generator_UserColumnName="得意先コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="工事コード" msprop:Generator_ColumnVarNameInTable="column工事コード" msprop:Generator_ColumnPropNameInRow="工事コード" msprop:Generator_ColumnPropNameInTable="工事コードColumn" msprop:Generator_UserColumnName="工事コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="工事内部コード" msprop:Generator_ColumnVarNameInTable="column工事内部コード" msprop:Generator_ColumnPropNameInRow="工事内部コード" msprop:Generator_ColumnPropNameInTable="工事内部コードColumn" msprop:Generator_UserColumnName="工事内部コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="摘要1" msprop:Generator_ColumnVarNameInTable="column摘要1" msprop:Generator_ColumnPropNameInRow="摘要1" msprop:Generator_ColumnPropNameInTable="摘要1Column" msprop:Generator_UserColumnName="摘要1" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="摘要2" msprop:Generator_ColumnVarNameInTable="column摘要2" msprop:Generator_ColumnPropNameInRow="摘要2" msprop:Generator_ColumnPropNameInTable="摘要2Column" msprop:Generator_UserColumnName="摘要2" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="備考" msprop:Generator_ColumnVarNameInTable="column備考" msprop:Generator_ColumnPropNameInRow="備考" msprop:Generator_ColumnPropNameInTable="備考Column" msprop:Generator_UserColumnName="備考" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="伝票発行区分" msprop:Generator_ColumnVarNameInTable="column伝票発行区分" msprop:Generator_ColumnPropNameInRow="伝票発行区分" msprop:Generator_ColumnPropNameInTable="伝票発行区分Column" msprop:Generator_UserColumnName="伝票発行区分" type="xs:short" minOccurs="0" />
              <xs:element name="登録日時" msprop:Generator_ColumnVarNameInTable="column登録日時" msprop:Generator_ColumnPropNameInRow="登録日時" msprop:Generator_ColumnPropNameInTable="登録日時Column" msprop:Generator_UserColumnName="登録日時" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="登録者ID" msprop:Generator_ColumnVarNameInTable="column登録者ID" msprop:Generator_ColumnPropNameInRow="登録者ID" msprop:Generator_ColumnPropNameInTable="登録者IDColumn" msprop:Generator_UserColumnName="登録者ID" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="更新日時" msprop:Generator_ColumnVarNameInTable="column更新日時" msprop:Generator_ColumnPropNameInRow="更新日時" msprop:Generator_ColumnPropNameInTable="更新日時Column" msprop:Generator_UserColumnName="更新日時" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="更新者ID" msprop:Generator_ColumnVarNameInTable="column更新者ID" msprop:Generator_ColumnPropNameInRow="更新者ID" msprop:Generator_ColumnPropNameInTable="更新者IDColumn" msprop:Generator_UserColumnName="更新者ID" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="得意先名" msprop:Generator_ColumnVarNameInTable="column得意先名" msprop:Generator_ColumnPropNameInRow="得意先名" msprop:Generator_ColumnPropNameInTable="得意先名Column" msprop:Generator_UserColumnName="得意先名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="工事名" msprop:Generator_ColumnVarNameInTable="column工事名" msprop:Generator_ColumnPropNameInRow="工事名" msprop:Generator_ColumnPropNameInTable="工事名Column" msprop:Generator_UserColumnName="工事名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="伝票区分名" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column伝票区分名" msprop:Generator_ColumnPropNameInRow="伝票区分名" msprop:Generator_ColumnPropNameInTable="伝票区分名Column" msprop:Generator_UserColumnName="伝票区分名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="伝票発行区分名" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column伝票発行区分名" msprop:Generator_ColumnPropNameInRow="伝票発行区分名" msprop:Generator_ColumnPropNameInTable="伝票発行区分名Column" msprop:Generator_UserColumnName="伝票発行区分名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="F売上定期処理" msprop:Generator_TableClassName="F売上定期処理DataTable" msprop:Generator_TableVarName="tableF売上定期処理" msprop:Generator_TablePropName="F売上定期処理" msprop:Generator_RowDeletingName="F売上定期処理RowDeleting" msprop:Generator_RowChangingName="F売上定期処理RowChanging" msprop:Generator_RowEvHandlerName="F売上定期処理RowChangeEventHandler" msprop:Generator_RowDeletedName="F売上定期処理RowDeleted" msprop:Generator_UserTableName="F売上定期処理" msprop:Generator_RowChangedName="F売上定期処理RowChanged" msprop:Generator_RowEvArgName="F売上定期処理RowChangeEvent" msprop:Generator_RowClassName="F売上定期処理Row">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="売上番号" msprop:Generator_ColumnVarNameInTable="column売上番号" msprop:Generator_ColumnPropNameInRow="売上番号" msprop:Generator_ColumnPropNameInTable="売上番号Column" msprop:Generator_UserColumnName="売上番号">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="行番号" msprop:Generator_ColumnVarNameInTable="column行番号" msprop:Generator_ColumnPropNameInRow="行番号" msprop:Generator_ColumnPropNameInTable="行番号Column" msprop:Generator_UserColumnName="行番号" type="xs:decimal" />
              <xs:element name="伝票区分" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column伝票区分" msprop:Generator_ColumnPropNameInRow="伝票区分" msprop:Generator_ColumnPropNameInTable="伝票区分Column" msprop:Generator_UserColumnName="伝票区分" type="xs:int" minOccurs="0" />
              <xs:element name="搬入日付" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column搬入日付" msprop:Generator_ColumnPropNameInRow="搬入日付" msprop:Generator_ColumnPropNameInTable="搬入日付Column" msprop:Generator_UserColumnName="搬入日付" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="得意先コード" msprop:Generator_ColumnVarNameInTable="column得意先コード" msprop:Generator_ColumnPropNameInRow="得意先コード" msprop:Generator_ColumnPropNameInTable="得意先コードColumn" msprop:Generator_UserColumnName="得意先コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="工事コード" msprop:Generator_ColumnVarNameInTable="column工事コード" msprop:Generator_ColumnPropNameInRow="工事コード" msprop:Generator_ColumnPropNameInTable="工事コードColumn" msprop:Generator_UserColumnName="工事コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="工事内部コード" msprop:Generator_ColumnVarNameInTable="column工事内部コード" msprop:Generator_ColumnPropNameInRow="工事内部コード" msprop:Generator_ColumnPropNameInTable="工事内部コードColumn" msprop:Generator_UserColumnName="工事内部コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="摘要1" msprop:Generator_ColumnVarNameInTable="column摘要1" msprop:Generator_ColumnPropNameInRow="摘要1" msprop:Generator_ColumnPropNameInTable="摘要1Column" msprop:Generator_UserColumnName="摘要1" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="摘要2" msprop:Generator_ColumnVarNameInTable="column摘要2" msprop:Generator_ColumnPropNameInRow="摘要2" msprop:Generator_ColumnPropNameInTable="摘要2Column" msprop:Generator_UserColumnName="摘要2" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="備考" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column備考" msprop:Generator_ColumnPropNameInRow="備考" msprop:Generator_ColumnPropNameInTable="備考Column" msprop:Generator_UserColumnName="備考" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="伝票発行区分" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column伝票発行区分" msprop:Generator_ColumnPropNameInRow="伝票発行区分" msprop:Generator_ColumnPropNameInTable="伝票発行区分Column" msprop:Generator_UserColumnName="伝票発行区分" type="xs:int" minOccurs="0" />
              <xs:element name="請求番号" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column請求番号" msprop:Generator_ColumnPropNameInRow="請求番号" msprop:Generator_ColumnPropNameInTable="請求番号Column" msprop:Generator_UserColumnName="請求番号" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="計算区分" msprop:Generator_ColumnVarNameInTable="column計算区分" msprop:Generator_ColumnPropNameInRow="計算区分" msprop:Generator_ColumnPropNameInTable="計算区分Column" msprop:Generator_UserColumnName="計算区分" type="xs:short" minOccurs="0" />
              <xs:element name="商品コード" msprop:Generator_ColumnVarNameInTable="column商品コード" msprop:Generator_ColumnPropNameInRow="商品コード" msprop:Generator_ColumnPropNameInTable="商品コードColumn" msprop:Generator_UserColumnName="商品コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="商品名" msprop:Generator_ColumnVarNameInTable="column商品名" msprop:Generator_ColumnPropNameInRow="商品名" msprop:Generator_ColumnPropNameInTable="商品名Column" msprop:Generator_UserColumnName="商品名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="備考明細" msprop:Generator_ColumnVarNameInTable="column備考明細" msprop:Generator_ColumnPropNameInRow="備考明細" msprop:Generator_ColumnPropNameInTable="備考明細Column" msprop:Generator_UserColumnName="備考明細" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="備考社内" msprop:Generator_ColumnVarNameInTable="column備考社内" msprop:Generator_ColumnPropNameInRow="備考社内" msprop:Generator_ColumnPropNameInTable="備考社内Column" msprop:Generator_UserColumnName="備考社内" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="数量" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column数量" msprop:Generator_ColumnPropNameInRow="数量" msprop:Generator_ColumnPropNameInTable="数量Column" msprop:Generator_UserColumnName="数量" type="xs:decimal" minOccurs="0" />
              <xs:element name="単位区分" msprop:Generator_ColumnVarNameInTable="column単位区分" msprop:Generator_ColumnPropNameInRow="単位区分" msprop:Generator_ColumnPropNameInTable="単位区分Column" msprop:Generator_UserColumnName="単位区分" type="xs:short" minOccurs="0" />
              <xs:element name="単価" msprop:Generator_ColumnVarNameInTable="column単価" msprop:Generator_ColumnPropNameInRow="単価" msprop:Generator_ColumnPropNameInTable="単価Column" msprop:Generator_UserColumnName="単価" type="xs:decimal" minOccurs="0" />
              <xs:element name="基本料金" msprop:Generator_ColumnVarNameInTable="column基本料金" msprop:Generator_ColumnPropNameInRow="基本料金" msprop:Generator_ColumnPropNameInTable="基本料金Column" msprop:Generator_UserColumnName="基本料金" type="xs:decimal" minOccurs="0" />
              <xs:element name="搬入番号" msprop:Generator_ColumnVarNameInTable="column搬入番号" msprop:Generator_ColumnPropNameInRow="搬入番号" msprop:Generator_ColumnPropNameInTable="搬入番号Column" msprop:Generator_UserColumnName="搬入番号" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="開始日付" msprop:Generator_ColumnVarNameInTable="column開始日付" msprop:Generator_ColumnPropNameInRow="開始日付" msprop:Generator_ColumnPropNameInTable="開始日付Column" msprop:Generator_UserColumnName="開始日付" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="終了日付" msprop:Generator_ColumnVarNameInTable="column終了日付" msprop:Generator_ColumnPropNameInRow="終了日付" msprop:Generator_ColumnPropNameInTable="終了日付Column" msprop:Generator_UserColumnName="終了日付" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="返納日付" msprop:Generator_ColumnVarNameInTable="column返納日付" msprop:Generator_ColumnPropNameInRow="返納日付" msprop:Generator_ColumnPropNameInTable="返納日付Column" msprop:Generator_UserColumnName="返納日付" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="F請求締期間" msprop:Generator_TableClassName="F請求締期間DataTable" msprop:Generator_TableVarName="tableF請求締期間" msprop:Generator_RowChangedName="F請求締期間RowChanged" msprop:Generator_TablePropName="F請求締期間" msprop:Generator_RowDeletingName="F請求締期間RowDeleting" msprop:Generator_RowChangingName="F請求締期間RowChanging" msprop:Generator_RowEvHandlerName="F請求締期間RowChangeEventHandler" msprop:Generator_RowDeletedName="F請求締期間RowDeleted" msprop:Generator_RowClassName="F請求締期間Row" msprop:Generator_UserTableName="F請求締期間" msprop:Generator_RowEvArgName="F請求締期間RowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="請求番号" msprop:Generator_ColumnVarNameInTable="column請求番号" msprop:Generator_ColumnPropNameInRow="請求番号" msprop:Generator_ColumnPropNameInTable="請求番号Column" msprop:Generator_UserColumnName="請求番号">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="得意先コード" msprop:Generator_ColumnVarNameInTable="column得意先コード" msprop:Generator_ColumnPropNameInRow="得意先コード" msprop:Generator_ColumnPropNameInTable="得意先コードColumn" msprop:Generator_UserColumnName="得意先コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="請求年月" msprop:Generator_ColumnVarNameInTable="column請求年月" msprop:Generator_ColumnPropNameInRow="請求年月" msprop:Generator_ColumnPropNameInTable="請求年月Column" msprop:Generator_UserColumnName="請求年月" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="7" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="締日区分" msprop:Generator_ColumnVarNameInTable="column締日区分" msprop:Generator_ColumnPropNameInRow="締日区分" msprop:Generator_ColumnPropNameInTable="締日区分Column" msprop:Generator_UserColumnName="締日区分" type="xs:short" minOccurs="0" />
              <xs:element name="請求日付自" msprop:Generator_ColumnVarNameInTable="column請求日付自" msprop:Generator_ColumnPropNameInRow="請求日付自" msprop:Generator_ColumnPropNameInTable="請求日付自Column" msprop:Generator_UserColumnName="請求日付自" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="請求日付至" msprop:Generator_ColumnVarNameInTable="column請求日付至" msprop:Generator_ColumnPropNameInRow="請求日付至" msprop:Generator_ColumnPropNameInTable="請求日付至Column" msprop:Generator_UserColumnName="請求日付至" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="順序" msprop:Generator_ColumnVarNameInTable="column順序" msprop:Generator_ColumnPropNameInRow="順序" msprop:Generator_ColumnPropNameInTable="順序Column" msprop:Generator_UserColumnName="順序" type="xs:long" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="F請求計算検索" msprop:Generator_TableClassName="F請求計算検索DataTable" msprop:Generator_TableVarName="tableF請求計算検索" msprop:Generator_TablePropName="F請求計算検索" msprop:Generator_RowDeletingName="F請求計算検索RowDeleting" msprop:Generator_RowChangingName="F請求計算検索RowChanging" msprop:Generator_RowEvHandlerName="F請求計算検索RowChangeEventHandler" msprop:Generator_RowDeletedName="F請求計算検索RowDeleted" msprop:Generator_UserTableName="F請求計算検索" msprop:Generator_RowChangedName="F請求計算検索RowChanged" msprop:Generator_RowEvArgName="F請求計算検索RowChangeEvent" msprop:Generator_RowClassName="F請求計算検索Row">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="得意先コード" msprop:Generator_ColumnVarNameInTable="column得意先コード" msprop:Generator_ColumnPropNameInRow="得意先コード" msprop:Generator_ColumnPropNameInTable="得意先コードColumn" msprop:Generator_UserColumnName="得意先コード">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="得意先名" msprop:Generator_ColumnVarNameInTable="column得意先名" msprop:Generator_ColumnPropNameInRow="得意先名" msprop:Generator_ColumnPropNameInTable="得意先名Column" msprop:Generator_UserColumnName="得意先名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="請求番号" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column請求番号" msprop:Generator_ColumnPropNameInRow="請求番号" msprop:Generator_ColumnPropNameInTable="請求番号Column" msprop:Generator_UserColumnName="請求番号" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="請求日付自" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column請求日付自" msprop:Generator_ColumnPropNameInRow="請求日付自" msprop:Generator_ColumnPropNameInTable="請求日付自Column" msprop:Generator_UserColumnName="請求日付自" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="請求日付至" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column請求日付至" msprop:Generator_ColumnPropNameInRow="請求日付至" msprop:Generator_ColumnPropNameInTable="請求日付至Column" msprop:Generator_UserColumnName="請求日付至" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="前回額" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column前回額" msprop:Generator_ColumnPropNameInRow="前回額" msprop:Generator_ColumnPropNameInTable="前回額Column" msprop:Generator_UserColumnName="前回額" type="xs:decimal" minOccurs="0" />
              <xs:element name="入金額" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column入金額" msprop:Generator_ColumnPropNameInRow="入金額" msprop:Generator_ColumnPropNameInTable="入金額Column" msprop:Generator_UserColumnName="入金額" type="xs:decimal" minOccurs="0" />
              <xs:element name="調整額" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column調整額" msprop:Generator_ColumnPropNameInRow="調整額" msprop:Generator_ColumnPropNameInTable="調整額Column" msprop:Generator_UserColumnName="調整額" type="xs:decimal" minOccurs="0" />
              <xs:element name="繰越額" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column繰越額" msprop:Generator_ColumnPropNameInRow="繰越額" msprop:Generator_ColumnPropNameInTable="繰越額Column" msprop:Generator_UserColumnName="繰越額" type="xs:decimal" minOccurs="0" />
              <xs:element name="税抜額" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column税抜額" msprop:Generator_ColumnPropNameInRow="税抜額" msprop:Generator_ColumnPropNameInTable="税抜額Column" msprop:Generator_UserColumnName="税抜額" type="xs:decimal" minOccurs="0" />
              <xs:element name="税込額" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column税込額" msprop:Generator_ColumnPropNameInRow="税込額" msprop:Generator_ColumnPropNameInTable="税込額Column" msprop:Generator_UserColumnName="税込額" type="xs:decimal" minOccurs="0" />
              <xs:element name="消費税" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column消費税" msprop:Generator_ColumnPropNameInRow="消費税" msprop:Generator_ColumnPropNameInTable="消費税Column" msprop:Generator_UserColumnName="消費税" type="xs:decimal" minOccurs="0" />
              <xs:element name="請求額" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column請求額" msprop:Generator_ColumnPropNameInRow="請求額" msprop:Generator_ColumnPropNameInTable="請求額Column" msprop:Generator_UserColumnName="請求額" type="xs:decimal" minOccurs="0" />
              <xs:element name="備考" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column備考" msprop:Generator_ColumnPropNameInRow="備考" msprop:Generator_ColumnPropNameInTable="備考Column" msprop:Generator_UserColumnName="備考" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="F請求計算DT" msprop:Generator_TableClassName="F請求計算DTDataTable" msprop:Generator_TableVarName="tableF請求計算DT" msprop:Generator_RowChangedName="F請求計算DTRowChanged" msprop:Generator_TablePropName="F請求計算DT" msprop:Generator_RowDeletingName="F請求計算DTRowDeleting" msprop:Generator_RowChangingName="F請求計算DTRowChanging" msprop:Generator_RowEvHandlerName="F請求計算DTRowChangeEventHandler" msprop:Generator_RowDeletedName="F請求計算DTRowDeleted" msprop:Generator_RowClassName="F請求計算DTRow" msprop:Generator_UserTableName="F請求計算DT" msprop:Generator_RowEvArgName="F請求計算DTRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="工事内部コード" msprop:Generator_ColumnVarNameInTable="column工事内部コード" msprop:Generator_ColumnPropNameInRow="工事内部コード" msprop:Generator_ColumnPropNameInTable="工事内部コードColumn" msprop:Generator_UserColumnName="工事内部コード">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="得意先コード" msprop:Generator_ColumnVarNameInTable="column得意先コード" msprop:Generator_ColumnPropNameInRow="得意先コード" msprop:Generator_ColumnPropNameInTable="得意先コードColumn" msprop:Generator_UserColumnName="得意先コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="工事コード" msprop:Generator_ColumnVarNameInTable="column工事コード" msprop:Generator_ColumnPropNameInRow="工事コード" msprop:Generator_ColumnPropNameInTable="工事コードColumn" msprop:Generator_UserColumnName="工事コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="工事名" msprop:Generator_ColumnVarNameInTable="column工事名" msprop:Generator_ColumnPropNameInRow="工事名" msprop:Generator_ColumnPropNameInTable="工事名Column" msprop:Generator_UserColumnName="工事名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="前回額" msprop:Generator_ColumnVarNameInTable="column前回額" msprop:Generator_ColumnPropNameInRow="前回額" msprop:Generator_ColumnPropNameInTable="前回額Column" msprop:Generator_UserColumnName="前回額" type="xs:decimal" />
              <xs:element name="入金額" msprop:Generator_ColumnVarNameInTable="column入金額" msprop:Generator_ColumnPropNameInRow="入金額" msprop:Generator_ColumnPropNameInTable="入金額Column" msprop:Generator_UserColumnName="入金額" type="xs:decimal" />
              <xs:element name="調整額" msprop:Generator_ColumnVarNameInTable="column調整額" msprop:Generator_ColumnPropNameInRow="調整額" msprop:Generator_ColumnPropNameInTable="調整額Column" msprop:Generator_UserColumnName="調整額" type="xs:decimal" />
              <xs:element name="繰越額" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column繰越額" msprop:Generator_ColumnPropNameInRow="繰越額" msprop:Generator_ColumnPropNameInTable="繰越額Column" msprop:Generator_UserColumnName="繰越額" type="xs:decimal" minOccurs="0" />
              <xs:element name="税抜額" msprop:Generator_ColumnVarNameInTable="column税抜額" msprop:Generator_ColumnPropNameInRow="税抜額" msprop:Generator_ColumnPropNameInTable="税抜額Column" msprop:Generator_UserColumnName="税抜額" type="xs:decimal" />
              <xs:element name="消費税" msprop:Generator_ColumnVarNameInTable="column消費税" msprop:Generator_ColumnPropNameInRow="消費税" msprop:Generator_ColumnPropNameInTable="消費税Column" msprop:Generator_UserColumnName="消費税" type="xs:decimal" minOccurs="0" />
              <xs:element name="税込額" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column税込額" msprop:Generator_ColumnPropNameInRow="税込額" msprop:Generator_ColumnPropNameInTable="税込額Column" msprop:Generator_UserColumnName="税込額" type="xs:decimal" minOccurs="0" />
              <xs:element name="請求額" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column請求額" msprop:Generator_ColumnPropNameInRow="請求額" msprop:Generator_ColumnPropNameInTable="請求額Column" msprop:Generator_UserColumnName="請求額" type="xs:decimal" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="F請求計算HD" msprop:Generator_TableClassName="F請求計算HDDataTable" msprop:Generator_TableVarName="tableF請求計算HD" msprop:Generator_TablePropName="F請求計算HD" msprop:Generator_RowDeletingName="F請求計算HDRowDeleting" msprop:Generator_RowChangingName="F請求計算HDRowChanging" msprop:Generator_RowEvHandlerName="F請求計算HDRowChangeEventHandler" msprop:Generator_RowDeletedName="F請求計算HDRowDeleted" msprop:Generator_UserTableName="F請求計算HD" msprop:Generator_RowChangedName="F請求計算HDRowChanged" msprop:Generator_RowEvArgName="F請求計算HDRowChangeEvent" msprop:Generator_RowClassName="F請求計算HDRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="請求番号" msprop:Generator_ColumnVarNameInTable="column請求番号" msprop:Generator_ColumnPropNameInRow="請求番号" msprop:Generator_ColumnPropNameInTable="請求番号Column" msprop:Generator_UserColumnName="請求番号">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="得意先コード" msprop:Generator_ColumnVarNameInTable="column得意先コード" msprop:Generator_ColumnPropNameInRow="得意先コード" msprop:Generator_ColumnPropNameInTable="得意先コードColumn" msprop:Generator_UserColumnName="得意先コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="前回額" msprop:Generator_ColumnVarNameInTable="column前回額" msprop:Generator_ColumnPropNameInRow="前回額" msprop:Generator_ColumnPropNameInTable="前回額Column" msprop:Generator_UserColumnName="前回額" type="xs:decimal" minOccurs="0" />
              <xs:element name="入金額" msprop:Generator_ColumnVarNameInTable="column入金額" msprop:Generator_ColumnPropNameInRow="入金額" msprop:Generator_ColumnPropNameInTable="入金額Column" msprop:Generator_UserColumnName="入金額" type="xs:decimal" minOccurs="0" />
              <xs:element name="調整額" msprop:Generator_ColumnVarNameInTable="column調整額" msprop:Generator_ColumnPropNameInRow="調整額" msprop:Generator_ColumnPropNameInTable="調整額Column" msprop:Generator_UserColumnName="調整額" type="xs:decimal" minOccurs="0" />
              <xs:element name="繰越額" msprop:Generator_ColumnVarNameInTable="column繰越額" msprop:Generator_ColumnPropNameInRow="繰越額" msprop:Generator_ColumnPropNameInTable="繰越額Column" msprop:Generator_UserColumnName="繰越額" type="xs:decimal" minOccurs="0" />
              <xs:element name="税抜額" msprop:Generator_ColumnVarNameInTable="column税抜額" msprop:Generator_ColumnPropNameInRow="税抜額" msprop:Generator_ColumnPropNameInTable="税抜額Column" msprop:Generator_UserColumnName="税抜額" type="xs:decimal" minOccurs="0" />
              <xs:element name="税込額" msprop:Generator_ColumnVarNameInTable="column税込額" msprop:Generator_ColumnPropNameInRow="税込額" msprop:Generator_ColumnPropNameInTable="税込額Column" msprop:Generator_UserColumnName="税込額" type="xs:decimal" minOccurs="0" />
              <xs:element name="消費税" msprop:Generator_ColumnVarNameInTable="column消費税" msprop:Generator_ColumnPropNameInRow="消費税" msprop:Generator_ColumnPropNameInTable="消費税Column" msprop:Generator_UserColumnName="消費税" type="xs:decimal" minOccurs="0" />
              <xs:element name="請求額" msprop:Generator_ColumnVarNameInTable="column請求額" msprop:Generator_ColumnPropNameInRow="請求額" msprop:Generator_ColumnPropNameInTable="請求額Column" msprop:Generator_UserColumnName="請求額" type="xs:decimal" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="F売上一覧表" msprop:Generator_TableClassName="F売上一覧表DataTable" msprop:Generator_TableVarName="tableF売上一覧表" msprop:Generator_RowChangedName="F売上一覧表RowChanged" msprop:Generator_TablePropName="F売上一覧表" msprop:Generator_RowDeletingName="F売上一覧表RowDeleting" msprop:Generator_RowChangingName="F売上一覧表RowChanging" msprop:Generator_RowEvHandlerName="F売上一覧表RowChangeEventHandler" msprop:Generator_RowDeletedName="F売上一覧表RowDeleted" msprop:Generator_RowClassName="F売上一覧表Row" msprop:Generator_UserTableName="F売上一覧表" msprop:Generator_RowEvArgName="F売上一覧表RowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="売上番号" msprop:Generator_ColumnVarNameInTable="column売上番号" msprop:Generator_ColumnPropNameInRow="売上番号" msprop:Generator_ColumnPropNameInTable="売上番号Column" msprop:Generator_UserColumnName="売上番号">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="行番号" msprop:Generator_ColumnVarNameInTable="column行番号" msprop:Generator_ColumnPropNameInRow="行番号" msprop:Generator_ColumnPropNameInTable="行番号Column" msprop:Generator_UserColumnName="行番号" type="xs:decimal" />
              <xs:element name="伝票区分" msprop:Generator_ColumnVarNameInTable="column伝票区分" msprop:Generator_ColumnPropNameInRow="伝票区分" msprop:Generator_ColumnPropNameInTable="伝票区分Column" msprop:Generator_UserColumnName="伝票区分" type="xs:short" minOccurs="0" />
              <xs:element name="伝票区分名" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column伝票区分名" msprop:Generator_ColumnPropNameInRow="伝票区分名" msprop:Generator_ColumnPropNameInTable="伝票区分名Column" msprop:Generator_UserColumnName="伝票区分名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="搬入日付" msprop:Generator_ColumnVarNameInTable="column搬入日付" msprop:Generator_ColumnPropNameInRow="搬入日付" msprop:Generator_ColumnPropNameInTable="搬入日付Column" msprop:Generator_UserColumnName="搬入日付" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="得意先コード" msprop:Generator_ColumnVarNameInTable="column得意先コード" msprop:Generator_ColumnPropNameInRow="得意先コード" msprop:Generator_ColumnPropNameInTable="得意先コードColumn" msprop:Generator_UserColumnName="得意先コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="得意先名" msprop:Generator_ColumnVarNameInTable="column得意先名" msprop:Generator_ColumnPropNameInRow="得意先名" msprop:Generator_ColumnPropNameInTable="得意先名Column" msprop:Generator_UserColumnName="得意先名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="工事コード" msprop:Generator_ColumnVarNameInTable="column工事コード" msprop:Generator_ColumnPropNameInRow="工事コード" msprop:Generator_ColumnPropNameInTable="工事コードColumn" msprop:Generator_UserColumnName="工事コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="工事内部コード" msprop:Generator_ColumnVarNameInTable="column工事内部コード" msprop:Generator_ColumnPropNameInRow="工事内部コード" msprop:Generator_ColumnPropNameInTable="工事内部コードColumn" msprop:Generator_UserColumnName="工事内部コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="工事名" msprop:Generator_ColumnVarNameInTable="column工事名" msprop:Generator_ColumnPropNameInRow="工事名" msprop:Generator_ColumnPropNameInTable="工事名Column" msprop:Generator_UserColumnName="工事名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="住所" msprop:Generator_ColumnVarNameInTable="column住所" msprop:Generator_ColumnPropNameInRow="住所" msprop:Generator_ColumnPropNameInTable="住所Column" msprop:Generator_UserColumnName="住所" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="摘要1" msprop:Generator_ColumnVarNameInTable="column摘要1" msprop:Generator_ColumnPropNameInRow="摘要1" msprop:Generator_ColumnPropNameInTable="摘要1Column" msprop:Generator_UserColumnName="摘要1" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="摘要2" msprop:Generator_ColumnVarNameInTable="column摘要2" msprop:Generator_ColumnPropNameInRow="摘要2" msprop:Generator_ColumnPropNameInTable="摘要2Column" msprop:Generator_UserColumnName="摘要2" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="計算区分" msprop:Generator_ColumnVarNameInTable="column計算区分" msprop:Generator_ColumnPropNameInRow="計算区分" msprop:Generator_ColumnPropNameInTable="計算区分Column" msprop:Generator_UserColumnName="計算区分" type="xs:short" minOccurs="0" />
              <xs:element name="商品コード" msprop:Generator_ColumnVarNameInTable="column商品コード" msprop:Generator_ColumnPropNameInRow="商品コード" msprop:Generator_ColumnPropNameInTable="商品コードColumn" msprop:Generator_UserColumnName="商品コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="商品名" msprop:Generator_ColumnVarNameInTable="column商品名" msprop:Generator_ColumnPropNameInRow="商品名" msprop:Generator_ColumnPropNameInTable="商品名Column" msprop:Generator_UserColumnName="商品名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="備考明細" msprop:Generator_ColumnVarNameInTable="column備考明細" msprop:Generator_ColumnPropNameInRow="備考明細" msprop:Generator_ColumnPropNameInTable="備考明細Column" msprop:Generator_UserColumnName="備考明細" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="備考社内" msprop:Generator_ColumnVarNameInTable="column備考社内" msprop:Generator_ColumnPropNameInRow="備考社内" msprop:Generator_ColumnPropNameInTable="備考社内Column" msprop:Generator_UserColumnName="備考社内" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="数量" msprop:Generator_ColumnVarNameInTable="column数量" msprop:Generator_ColumnPropNameInRow="数量" msprop:Generator_ColumnPropNameInTable="数量Column" msprop:Generator_UserColumnName="数量" type="xs:decimal" minOccurs="0" />
              <xs:element name="単位区分" msprop:Generator_ColumnVarNameInTable="column単位区分" msprop:Generator_ColumnPropNameInRow="単位区分" msprop:Generator_ColumnPropNameInTable="単位区分Column" msprop:Generator_UserColumnName="単位区分" type="xs:short" minOccurs="0" />
              <xs:element name="単位区分名" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column単位区分名" msprop:Generator_ColumnPropNameInRow="単位区分名" msprop:Generator_ColumnPropNameInTable="単位区分名Column" msprop:Generator_UserColumnName="単位区分名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="単価" msprop:Generator_ColumnVarNameInTable="column単価" msprop:Generator_ColumnPropNameInRow="単価" msprop:Generator_ColumnPropNameInTable="単価Column" msprop:Generator_UserColumnName="単価" type="xs:decimal" minOccurs="0" />
              <xs:element name="基本料金" msprop:Generator_ColumnVarNameInTable="column基本料金" msprop:Generator_ColumnPropNameInRow="基本料金" msprop:Generator_ColumnPropNameInTable="基本料金Column" msprop:Generator_UserColumnName="基本料金" type="xs:decimal" minOccurs="0" />
              <xs:element name="金額" msprop:Generator_ColumnVarNameInTable="column金額" msprop:Generator_ColumnPropNameInRow="金額" msprop:Generator_ColumnPropNameInTable="金額Column" msprop:Generator_UserColumnName="金額" type="xs:decimal" minOccurs="0" />
              <xs:element name="搬入番号" msprop:Generator_ColumnVarNameInTable="column搬入番号" msprop:Generator_ColumnPropNameInRow="搬入番号" msprop:Generator_ColumnPropNameInTable="搬入番号Column" msprop:Generator_UserColumnName="搬入番号" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="開始日付" msprop:Generator_ColumnVarNameInTable="column開始日付" msprop:Generator_ColumnPropNameInRow="開始日付" msprop:Generator_ColumnPropNameInTable="開始日付Column" msprop:Generator_UserColumnName="開始日付" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="終了日付" msprop:Generator_ColumnVarNameInTable="column終了日付" msprop:Generator_ColumnPropNameInRow="終了日付" msprop:Generator_ColumnPropNameInTable="終了日付Column" msprop:Generator_UserColumnName="終了日付" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="返納日付" msprop:Generator_ColumnVarNameInTable="column返納日付" msprop:Generator_ColumnPropNameInRow="返納日付" msprop:Generator_ColumnPropNameInTable="返納日付Column" msprop:Generator_UserColumnName="返納日付" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="F売上定期検索" msprop:Generator_TableClassName="F売上定期検索DataTable" msprop:Generator_TableVarName="tableF売上定期検索" msprop:Generator_RowChangedName="F売上定期検索RowChanged" msprop:Generator_TablePropName="F売上定期検索" msprop:Generator_RowDeletingName="F売上定期検索RowDeleting" msprop:Generator_RowChangingName="F売上定期検索RowChanging" msprop:Generator_RowEvHandlerName="F売上定期検索RowChangeEventHandler" msprop:Generator_RowDeletedName="F売上定期検索RowDeleted" msprop:Generator_RowClassName="F売上定期検索Row" msprop:Generator_UserTableName="F売上定期検索" msprop:Generator_RowEvArgName="F売上定期検索RowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="売上番号" msprop:Generator_ColumnVarNameInTable="column売上番号" msprop:Generator_ColumnPropNameInRow="売上番号" msprop:Generator_ColumnPropNameInTable="売上番号Column" msprop:Generator_UserColumnName="売上番号">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="行番号" msprop:Generator_ColumnVarNameInTable="column行番号" msprop:Generator_ColumnPropNameInRow="行番号" msprop:Generator_ColumnPropNameInTable="行番号Column" msprop:Generator_UserColumnName="行番号" type="xs:decimal" />
              <xs:element name="伝票区分" msprop:Generator_ColumnVarNameInTable="column伝票区分" msprop:Generator_ColumnPropNameInRow="伝票区分" msprop:Generator_ColumnPropNameInTable="伝票区分Column" msprop:Generator_UserColumnName="伝票区分" type="xs:short" minOccurs="0" />
              <xs:element name="搬入日付" msprop:Generator_ColumnVarNameInTable="column搬入日付" msprop:Generator_ColumnPropNameInRow="搬入日付" msprop:Generator_ColumnPropNameInTable="搬入日付Column" msprop:Generator_UserColumnName="搬入日付" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="得意先コード" msprop:Generator_ColumnVarNameInTable="column得意先コード" msprop:Generator_ColumnPropNameInRow="得意先コード" msprop:Generator_ColumnPropNameInTable="得意先コードColumn" msprop:Generator_UserColumnName="得意先コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="工事コード" msprop:Generator_ColumnVarNameInTable="column工事コード" msprop:Generator_ColumnPropNameInRow="工事コード" msprop:Generator_ColumnPropNameInTable="工事コードColumn" msprop:Generator_UserColumnName="工事コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="工事内部コード" msprop:Generator_ColumnVarNameInTable="column工事内部コード" msprop:Generator_ColumnPropNameInRow="工事内部コード" msprop:Generator_ColumnPropNameInTable="工事内部コードColumn" msprop:Generator_UserColumnName="工事内部コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="摘要1" msprop:Generator_ColumnVarNameInTable="column摘要1" msprop:Generator_ColumnPropNameInRow="摘要1" msprop:Generator_ColumnPropNameInTable="摘要1Column" msprop:Generator_UserColumnName="摘要1" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="摘要2" msprop:Generator_ColumnVarNameInTable="column摘要2" msprop:Generator_ColumnPropNameInRow="摘要2" msprop:Generator_ColumnPropNameInTable="摘要2Column" msprop:Generator_UserColumnName="摘要2" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="計算区分" msprop:Generator_ColumnVarNameInTable="column計算区分" msprop:Generator_ColumnPropNameInRow="計算区分" msprop:Generator_ColumnPropNameInTable="計算区分Column" msprop:Generator_UserColumnName="計算区分" type="xs:short" minOccurs="0" />
              <xs:element name="商品コード" msprop:Generator_ColumnVarNameInTable="column商品コード" msprop:Generator_ColumnPropNameInRow="商品コード" msprop:Generator_ColumnPropNameInTable="商品コードColumn" msprop:Generator_UserColumnName="商品コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="商品名" msprop:Generator_ColumnVarNameInTable="column商品名" msprop:Generator_ColumnPropNameInRow="商品名" msprop:Generator_ColumnPropNameInTable="商品名Column" msprop:Generator_UserColumnName="商品名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="備考明細" msprop:Generator_ColumnVarNameInTable="column備考明細" msprop:Generator_ColumnPropNameInRow="備考明細" msprop:Generator_ColumnPropNameInTable="備考明細Column" msprop:Generator_UserColumnName="備考明細" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="備考社内" msprop:Generator_ColumnVarNameInTable="column備考社内" msprop:Generator_ColumnPropNameInRow="備考社内" msprop:Generator_ColumnPropNameInTable="備考社内Column" msprop:Generator_UserColumnName="備考社内" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="数量" msprop:Generator_ColumnVarNameInTable="column数量" msprop:Generator_ColumnPropNameInRow="数量" msprop:Generator_ColumnPropNameInTable="数量Column" msprop:Generator_UserColumnName="数量" type="xs:decimal" minOccurs="0" />
              <xs:element name="単位区分" msprop:Generator_ColumnVarNameInTable="column単位区分" msprop:Generator_ColumnPropNameInRow="単位区分" msprop:Generator_ColumnPropNameInTable="単位区分Column" msprop:Generator_UserColumnName="単位区分" type="xs:short" minOccurs="0" />
              <xs:element name="単価" msprop:Generator_ColumnVarNameInTable="column単価" msprop:Generator_ColumnPropNameInRow="単価" msprop:Generator_ColumnPropNameInTable="単価Column" msprop:Generator_UserColumnName="単価" type="xs:decimal" minOccurs="0" />
              <xs:element name="基本料金" msprop:Generator_ColumnVarNameInTable="column基本料金" msprop:Generator_ColumnPropNameInRow="基本料金" msprop:Generator_ColumnPropNameInTable="基本料金Column" msprop:Generator_UserColumnName="基本料金" type="xs:decimal" minOccurs="0" />
              <xs:element name="金額" msprop:Generator_ColumnVarNameInTable="column金額" msprop:Generator_ColumnPropNameInRow="金額" msprop:Generator_ColumnPropNameInTable="金額Column" msprop:Generator_UserColumnName="金額" type="xs:decimal" minOccurs="0" />
              <xs:element name="搬入番号" msprop:Generator_ColumnVarNameInTable="column搬入番号" msprop:Generator_ColumnPropNameInRow="搬入番号" msprop:Generator_ColumnPropNameInTable="搬入番号Column" msprop:Generator_UserColumnName="搬入番号" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="開始日付" msprop:Generator_ColumnVarNameInTable="column開始日付" msprop:Generator_ColumnPropNameInRow="開始日付" msprop:Generator_ColumnPropNameInTable="開始日付Column" msprop:Generator_UserColumnName="開始日付" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="終了日付" msprop:Generator_ColumnVarNameInTable="column終了日付" msprop:Generator_ColumnPropNameInRow="終了日付" msprop:Generator_ColumnPropNameInTable="終了日付Column" msprop:Generator_UserColumnName="終了日付" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="返納日付" msprop:Generator_ColumnVarNameInTable="column返納日付" msprop:Generator_ColumnPropNameInRow="返納日付" msprop:Generator_ColumnPropNameInTable="返納日付Column" msprop:Generator_UserColumnName="返納日付" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="得意先名" msprop:Generator_ColumnVarNameInTable="column得意先名" msprop:Generator_ColumnPropNameInRow="得意先名" msprop:Generator_ColumnPropNameInTable="得意先名Column" msprop:Generator_UserColumnName="得意先名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="工事名" msprop:Generator_ColumnVarNameInTable="column工事名" msprop:Generator_ColumnPropNameInRow="工事名" msprop:Generator_ColumnPropNameInTable="工事名Column" msprop:Generator_UserColumnName="工事名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="締日区分" msprop:Generator_ColumnVarNameInTable="column締日区分" msprop:Generator_ColumnPropNameInRow="締日区分" msprop:Generator_ColumnPropNameInTable="締日区分Column" msprop:Generator_UserColumnName="締日区分" type="xs:short" minOccurs="0" />
              <xs:element name="売上行番号" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column売上行番号" msprop:Generator_ColumnPropNameInRow="売上行番号" msprop:Generator_ColumnPropNameInTable="売上行番号Column" msprop:Generator_UserColumnName="売上行番号" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="15" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="伝票区分名" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column伝票区分名" msprop:Generator_ColumnPropNameInRow="伝票区分名" msprop:Generator_ColumnPropNameInTable="伝票区分名Column" msprop:Generator_UserColumnName="伝票区分名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="単位区分名" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column単位区分名" msprop:Generator_ColumnPropNameInRow="単位区分名" msprop:Generator_ColumnPropNameInTable="単位区分名Column" msprop:Generator_UserColumnName="単位区分名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="締日区分名" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column締日区分名" msprop:Generator_ColumnPropNameInRow="締日区分名" msprop:Generator_ColumnPropNameInTable="締日区分名Column" msprop:Generator_UserColumnName="締日区分名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Fリース商品一覧" msprop:Generator_TableClassName="Fリース商品一覧DataTable" msprop:Generator_TableVarName="tableFリース商品一覧" msprop:Generator_TablePropName="Fリース商品一覧" msprop:Generator_RowDeletingName="Fリース商品一覧RowDeleting" msprop:Generator_RowChangingName="Fリース商品一覧RowChanging" msprop:Generator_RowEvHandlerName="Fリース商品一覧RowChangeEventHandler" msprop:Generator_RowDeletedName="Fリース商品一覧RowDeleted" msprop:Generator_UserTableName="Fリース商品一覧" msprop:Generator_RowChangedName="Fリース商品一覧RowChanged" msprop:Generator_RowEvArgName="Fリース商品一覧RowChangeEvent" msprop:Generator_RowClassName="Fリース商品一覧Row">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="商品コード" msprop:Generator_ColumnVarNameInTable="column商品コード" msprop:Generator_ColumnPropNameInRow="商品コード" msprop:Generator_ColumnPropNameInTable="商品コードColumn" msprop:Generator_UserColumnName="商品コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="商品名" msprop:Generator_ColumnVarNameInTable="column商品名" msprop:Generator_ColumnPropNameInRow="商品名" msprop:Generator_ColumnPropNameInTable="商品名Column" msprop:Generator_UserColumnName="商品名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="得意先コード" msprop:Generator_ColumnVarNameInTable="column得意先コード" msprop:Generator_ColumnPropNameInRow="得意先コード" msprop:Generator_ColumnPropNameInTable="得意先コードColumn" msprop:Generator_UserColumnName="得意先コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="得意先名" msprop:Generator_ColumnVarNameInTable="column得意先名" msprop:Generator_ColumnPropNameInRow="得意先名" msprop:Generator_ColumnPropNameInTable="得意先名Column" msprop:Generator_UserColumnName="得意先名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="工事コード" msprop:Generator_ColumnVarNameInTable="column工事コード" msprop:Generator_ColumnPropNameInRow="工事コード" msprop:Generator_ColumnPropNameInTable="工事コードColumn" msprop:Generator_UserColumnName="工事コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="工事名" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column工事名" msprop:Generator_ColumnPropNameInRow="工事名" msprop:Generator_ColumnPropNameInTable="工事名Column" msprop:Generator_UserColumnName="工事名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="数量" msprop:Generator_ColumnVarNameInTable="column数量" msprop:Generator_ColumnPropNameInRow="数量" msprop:Generator_ColumnPropNameInTable="数量Column" msprop:Generator_UserColumnName="数量" type="xs:decimal" minOccurs="0" />
              <xs:element name="開始日付" msprop:Generator_ColumnVarNameInTable="column開始日付" msprop:Generator_ColumnPropNameInRow="開始日付" msprop:Generator_ColumnPropNameInTable="開始日付Column" msprop:Generator_UserColumnName="開始日付" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="返納日付" msprop:Generator_ColumnVarNameInTable="column返納日付" msprop:Generator_ColumnPropNameInRow="返納日付" msprop:Generator_ColumnPropNameInTable="返納日付Column" msprop:Generator_UserColumnName="返納日付" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="_工事内部コード" msprop:Generator_ColumnVarNameInTable="column_工事内部コード" msprop:Generator_ColumnPropNameInRow="_工事内部コード" msprop:Generator_ColumnPropNameInTable="_工事内部コードColumn" msprop:Generator_UserColumnName="_工事内部コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="_行区分" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column_行区分" msprop:Generator_ColumnPropNameInRow="_行区分" msprop:Generator_ColumnPropNameInTable="_行区分Column" msprop:Generator_UserColumnName="_行区分" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="F工事残高" msprop:Generator_TableClassName="F工事残高DataTable" msprop:Generator_TableVarName="tableF工事残高" msprop:Generator_TablePropName="F工事残高" msprop:Generator_RowDeletingName="F工事残高RowDeleting" msprop:Generator_RowChangingName="F工事残高RowChanging" msprop:Generator_RowEvHandlerName="F工事残高RowChangeEventHandler" msprop:Generator_RowDeletedName="F工事残高RowDeleted" msprop:Generator_UserTableName="F工事残高" msprop:Generator_RowChangedName="F工事残高RowChanged" msprop:Generator_RowEvArgName="F工事残高RowChangeEvent" msprop:Generator_RowClassName="F工事残高Row">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="請求年月" msprop:Generator_ColumnVarNameInTable="column請求年月" msprop:Generator_ColumnPropNameInRow="請求年月" msprop:Generator_ColumnPropNameInTable="請求年月Column" msprop:Generator_UserColumnName="請求年月" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="7" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="得意先名" msprop:Generator_ColumnVarNameInTable="column得意先名" msprop:Generator_ColumnPropNameInRow="得意先名" msprop:Generator_ColumnPropNameInTable="得意先名Column" msprop:Generator_UserColumnName="得意先名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="工事名" msprop:Generator_ColumnVarNameInTable="column工事名" msprop:Generator_ColumnPropNameInRow="工事名" msprop:Generator_ColumnPropNameInTable="工事名Column" msprop:Generator_UserColumnName="工事名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="残高額" msprop:Generator_ColumnVarNameInTable="column残高額" msprop:Generator_ColumnPropNameInRow="残高額" msprop:Generator_ColumnPropNameInTable="残高額Column" msprop:Generator_UserColumnName="残高額" type="xs:decimal" minOccurs="0" />
              <xs:element name="_得意先コード" msprop:Generator_ColumnVarNameInTable="column_得意先コード" msprop:Generator_ColumnPropNameInRow="_得意先コード" msprop:Generator_ColumnPropNameInTable="_得意先コードColumn" msprop:Generator_UserColumnName="_得意先コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="_工事コード" msprop:Generator_ColumnVarNameInTable="column_工事コード" msprop:Generator_ColumnPropNameInRow="_工事コード" msprop:Generator_ColumnPropNameInTable="_工事コードColumn" msprop:Generator_UserColumnName="_工事コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="_工事内部" msprop:Generator_ColumnVarNameInTable="column_工事内部" msprop:Generator_ColumnPropNameInRow="_工事内部" msprop:Generator_ColumnPropNameInTable="_工事内部Column" msprop:Generator_UserColumnName="_工事内部" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="F入金一覧表" msprop:Generator_TableClassName="F入金一覧表DataTable" msprop:Generator_TableVarName="tableF入金一覧表" msprop:Generator_TablePropName="F入金一覧表" msprop:Generator_RowDeletingName="F入金一覧表RowDeleting" msprop:Generator_RowChangingName="F入金一覧表RowChanging" msprop:Generator_RowEvHandlerName="F入金一覧表RowChangeEventHandler" msprop:Generator_RowDeletedName="F入金一覧表RowDeleted" msprop:Generator_UserTableName="F入金一覧表" msprop:Generator_RowChangedName="F入金一覧表RowChanged" msprop:Generator_RowEvArgName="F入金一覧表RowChangeEvent" msprop:Generator_RowClassName="F入金一覧表Row">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="入金番号" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column入金番号" msprop:Generator_ColumnPropNameInRow="入金番号" msprop:Generator_ColumnPropNameInTable="入金番号Column" msprop:Generator_UserColumnName="入金番号" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="入金日付" msprop:Generator_ColumnVarNameInTable="column入金日付" msprop:Generator_ColumnPropNameInRow="入金日付" msprop:Generator_ColumnPropNameInTable="入金日付Column" msprop:Generator_UserColumnName="入金日付" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="請求年月" msprop:Generator_ColumnVarNameInTable="column請求年月" msprop:Generator_ColumnPropNameInRow="請求年月" msprop:Generator_ColumnPropNameInTable="請求年月Column" msprop:Generator_UserColumnName="請求年月" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="7" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="得意先コード" msprop:Generator_ColumnVarNameInTable="column得意先コード" msprop:Generator_ColumnPropNameInRow="得意先コード" msprop:Generator_ColumnPropNameInTable="得意先コードColumn" msprop:Generator_UserColumnName="得意先コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="得意先名" msprop:Generator_ColumnVarNameInTable="column得意先名" msprop:Generator_ColumnPropNameInRow="得意先名" msprop:Generator_ColumnPropNameInTable="得意先名Column" msprop:Generator_UserColumnName="得意先名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="工事コード" msprop:Generator_ColumnVarNameInTable="column工事コード" msprop:Generator_ColumnPropNameInRow="工事コード" msprop:Generator_ColumnPropNameInTable="工事コードColumn" msprop:Generator_UserColumnName="工事コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="工事名" msprop:Generator_ColumnVarNameInTable="column工事名" msprop:Generator_ColumnPropNameInRow="工事名" msprop:Generator_ColumnPropNameInTable="工事名Column" msprop:Generator_UserColumnName="工事名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="入金額" msprop:Generator_ColumnVarNameInTable="column入金額" msprop:Generator_ColumnPropNameInRow="入金額" msprop:Generator_ColumnPropNameInTable="入金額Column" msprop:Generator_UserColumnName="入金額" type="xs:decimal" minOccurs="0" />
              <xs:element name="調整額" msprop:Generator_ColumnVarNameInTable="column調整額" msprop:Generator_ColumnPropNameInRow="調整額" msprop:Generator_ColumnPropNameInTable="調整額Column" msprop:Generator_UserColumnName="調整額" type="xs:decimal" minOccurs="0" />
              <xs:element name="_工事内部コード" msprop:Generator_ColumnVarNameInTable="column_工事内部コード" msprop:Generator_ColumnPropNameInRow="_工事内部コード" msprop:Generator_ColumnPropNameInTable="_工事内部コードColumn" msprop:Generator_UserColumnName="_工事内部コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="_行区分" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column_行区分" msprop:Generator_ColumnPropNameInRow="_行区分" msprop:Generator_ColumnPropNameInTable="_行区分Column" msprop:Generator_UserColumnName="_行区分" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Fリース商品残数" msprop:Generator_TableClassName="Fリース商品残数DataTable" msprop:Generator_TableVarName="tableFリース商品残数" msprop:Generator_RowChangedName="Fリース商品残数RowChanged" msprop:Generator_TablePropName="Fリース商品残数" msprop:Generator_RowDeletingName="Fリース商品残数RowDeleting" msprop:Generator_RowChangingName="Fリース商品残数RowChanging" msprop:Generator_RowEvHandlerName="Fリース商品残数RowChangeEventHandler" msprop:Generator_RowDeletedName="Fリース商品残数RowDeleted" msprop:Generator_RowClassName="Fリース商品残数Row" msprop:Generator_UserTableName="Fリース商品残数" msprop:Generator_RowEvArgName="Fリース商品残数RowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="売上番号" msprop:Generator_ColumnVarNameInTable="column売上番号" msprop:Generator_ColumnPropNameInRow="売上番号" msprop:Generator_ColumnPropNameInTable="売上番号Column" msprop:Generator_UserColumnName="売上番号">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="行番号" msprop:Generator_ColumnVarNameInTable="column行番号" msprop:Generator_ColumnPropNameInRow="行番号" msprop:Generator_ColumnPropNameInTable="行番号Column" msprop:Generator_UserColumnName="行番号" type="xs:decimal" />
              <xs:element name="商品コード" msprop:Generator_ColumnVarNameInTable="column商品コード" msprop:Generator_ColumnPropNameInRow="商品コード" msprop:Generator_ColumnPropNameInTable="商品コードColumn" msprop:Generator_UserColumnName="商品コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="搬入数量" msprop:Generator_ColumnVarNameInTable="column搬入数量" msprop:Generator_ColumnPropNameInRow="搬入数量" msprop:Generator_ColumnPropNameInTable="搬入数量Column" msprop:Generator_UserColumnName="搬入数量" type="xs:decimal" minOccurs="0" />
              <xs:element name="返納数量" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column返納数量" msprop:Generator_ColumnPropNameInRow="返納数量" msprop:Generator_ColumnPropNameInTable="返納数量Column" msprop:Generator_UserColumnName="返納数量" type="xs:decimal" minOccurs="0" />
              <xs:element name="残数量" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column残数量" msprop:Generator_ColumnPropNameInRow="残数量" msprop:Generator_ColumnPropNameInTable="残数量Column" msprop:Generator_UserColumnName="残数量" type="xs:decimal" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="F請求書検索" msprop:Generator_TableClassName="F請求書検索DataTable" msprop:Generator_TableVarName="tableF請求書検索" msprop:Generator_RowChangedName="F請求書検索RowChanged" msprop:Generator_TablePropName="F請求書検索" msprop:Generator_RowDeletingName="F請求書検索RowDeleting" msprop:Generator_RowChangingName="F請求書検索RowChanging" msprop:Generator_RowEvHandlerName="F請求書検索RowChangeEventHandler" msprop:Generator_RowDeletedName="F請求書検索RowDeleted" msprop:Generator_RowClassName="F請求書検索Row" msprop:Generator_UserTableName="F請求書検索" msprop:Generator_RowEvArgName="F請求書検索RowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="請求番号" msprop:Generator_ColumnVarNameInTable="column請求番号" msprop:Generator_ColumnPropNameInRow="請求番号" msprop:Generator_ColumnPropNameInTable="請求番号Column" msprop:Generator_UserColumnName="請求番号">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="請求年月" msprop:Generator_ColumnVarNameInTable="column請求年月" msprop:Generator_ColumnPropNameInRow="請求年月" msprop:Generator_ColumnPropNameInTable="請求年月Column" msprop:Generator_UserColumnName="請求年月" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="7" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="締日区分" msprop:Generator_ColumnVarNameInTable="column締日区分" msprop:Generator_ColumnPropNameInRow="締日区分" msprop:Generator_ColumnPropNameInTable="締日区分Column" msprop:Generator_UserColumnName="締日区分" type="xs:short" minOccurs="0" />
              <xs:element name="得意先コード" msprop:Generator_ColumnVarNameInTable="column得意先コード" msprop:Generator_ColumnPropNameInRow="得意先コード" msprop:Generator_ColumnPropNameInTable="得意先コードColumn" msprop:Generator_UserColumnName="得意先コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="得意先名" msprop:Generator_ColumnVarNameInTable="column得意先名" msprop:Generator_ColumnPropNameInRow="得意先名" msprop:Generator_ColumnPropNameInTable="得意先名Column" msprop:Generator_UserColumnName="得意先名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="請求日付自" msprop:Generator_ColumnVarNameInTable="column請求日付自" msprop:Generator_ColumnPropNameInRow="請求日付自" msprop:Generator_ColumnPropNameInTable="請求日付自Column" msprop:Generator_UserColumnName="請求日付自" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="請求日付至" msprop:Generator_ColumnVarNameInTable="column請求日付至" msprop:Generator_ColumnPropNameInRow="請求日付至" msprop:Generator_ColumnPropNameInTable="請求日付至Column" msprop:Generator_UserColumnName="請求日付至" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="前回額" msprop:Generator_ColumnVarNameInTable="column前回額" msprop:Generator_ColumnPropNameInRow="前回額" msprop:Generator_ColumnPropNameInTable="前回額Column" msprop:Generator_UserColumnName="前回額" type="xs:decimal" minOccurs="0" />
              <xs:element name="入金額" msprop:Generator_ColumnVarNameInTable="column入金額" msprop:Generator_ColumnPropNameInRow="入金額" msprop:Generator_ColumnPropNameInTable="入金額Column" msprop:Generator_UserColumnName="入金額" type="xs:decimal" minOccurs="0" />
              <xs:element name="調整額" msprop:Generator_ColumnVarNameInTable="column調整額" msprop:Generator_ColumnPropNameInRow="調整額" msprop:Generator_ColumnPropNameInTable="調整額Column" msprop:Generator_UserColumnName="調整額" type="xs:decimal" minOccurs="0" />
              <xs:element name="繰越額" msprop:Generator_ColumnVarNameInTable="column繰越額" msprop:Generator_ColumnPropNameInRow="繰越額" msprop:Generator_ColumnPropNameInTable="繰越額Column" msprop:Generator_UserColumnName="繰越額" type="xs:decimal" minOccurs="0" />
              <xs:element name="税抜額" msprop:Generator_ColumnVarNameInTable="column税抜額" msprop:Generator_ColumnPropNameInRow="税抜額" msprop:Generator_ColumnPropNameInTable="税抜額Column" msprop:Generator_UserColumnName="税抜額" type="xs:decimal" minOccurs="0" />
              <xs:element name="税込額" msprop:Generator_ColumnVarNameInTable="column税込額" msprop:Generator_ColumnPropNameInRow="税込額" msprop:Generator_ColumnPropNameInTable="税込額Column" msprop:Generator_UserColumnName="税込額" type="xs:decimal" minOccurs="0" />
              <xs:element name="消費税" msprop:Generator_ColumnVarNameInTable="column消費税" msprop:Generator_ColumnPropNameInRow="消費税" msprop:Generator_ColumnPropNameInTable="消費税Column" msprop:Generator_UserColumnName="消費税" type="xs:decimal" minOccurs="0" />
              <xs:element name="請求額" msprop:Generator_ColumnVarNameInTable="column請求額" msprop:Generator_ColumnPropNameInRow="請求額" msprop:Generator_ColumnPropNameInTable="請求額Column" msprop:Generator_UserColumnName="請求額" type="xs:decimal" minOccurs="0" />
              <xs:element name="備考" msprop:Generator_ColumnVarNameInTable="column備考" msprop:Generator_ColumnPropNameInRow="備考" msprop:Generator_ColumnPropNameInTable="備考Column" msprop:Generator_UserColumnName="備考" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="担当者コード" msprop:Generator_ColumnVarNameInTable="column担当者コード" msprop:Generator_ColumnPropNameInRow="担当者コード" msprop:Generator_ColumnPropNameInTable="担当者コードColumn" msprop:Generator_UserColumnName="担当者コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="F1工事台帳" msprop:Generator_TableClassName="F1工事台帳DataTable" msprop:Generator_TableVarName="tableF1工事台帳" msprop:Generator_RowChangedName="F1工事台帳RowChanged" msprop:Generator_TablePropName="F1工事台帳" msprop:Generator_RowDeletingName="F1工事台帳RowDeleting" msprop:Generator_RowChangingName="F1工事台帳RowChanging" msprop:Generator_RowEvHandlerName="F1工事台帳RowChangeEventHandler" msprop:Generator_RowDeletedName="F1工事台帳RowDeleted" msprop:Generator_RowClassName="F1工事台帳Row" msprop:Generator_UserTableName="F1工事台帳" msprop:Generator_RowEvArgName="F1工事台帳RowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="伝票番号" msprop:Generator_ColumnVarNameInTable="column伝票番号" msprop:Generator_ColumnPropNameInRow="伝票番号" msprop:Generator_ColumnPropNameInTable="伝票番号Column" msprop:Generator_UserColumnName="伝票番号">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="行番号" msprop:Generator_ColumnVarNameInTable="column行番号" msprop:Generator_ColumnPropNameInRow="行番号" msprop:Generator_ColumnPropNameInTable="行番号Column" msprop:Generator_UserColumnName="行番号" type="xs:decimal" />
              <xs:element name="得意先コード" msprop:Generator_ColumnVarNameInTable="column得意先コード" msprop:Generator_ColumnPropNameInRow="得意先コード" msprop:Generator_ColumnPropNameInTable="得意先コードColumn" msprop:Generator_UserColumnName="得意先コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="得意先名" msprop:Generator_ColumnVarNameInTable="column得意先名" msprop:Generator_ColumnPropNameInRow="得意先名" msprop:Generator_ColumnPropNameInTable="得意先名Column" msprop:Generator_UserColumnName="得意先名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="工事内部コード" msprop:Generator_ColumnVarNameInTable="column工事内部コード" msprop:Generator_ColumnPropNameInRow="工事内部コード" msprop:Generator_ColumnPropNameInTable="工事内部コードColumn" msprop:Generator_UserColumnName="工事内部コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="工事コード" msprop:Generator_ColumnVarNameInTable="column工事コード" msprop:Generator_ColumnPropNameInRow="工事コード" msprop:Generator_ColumnPropNameInTable="工事コードColumn" msprop:Generator_UserColumnName="工事コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="工事名" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column工事名" msprop:Generator_ColumnPropNameInRow="工事名" msprop:Generator_ColumnPropNameInTable="工事名Column" msprop:Generator_UserColumnName="工事名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="101" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="集計年月" msprop:Generator_ColumnVarNameInTable="column集計年月" msprop:Generator_ColumnPropNameInRow="集計年月" msprop:Generator_ColumnPropNameInTable="集計年月Column" msprop:Generator_UserColumnName="集計年月" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="7" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="日付" msprop:Generator_ColumnVarNameInTable="column日付" msprop:Generator_ColumnPropNameInRow="日付" msprop:Generator_ColumnPropNameInTable="日付Column" msprop:Generator_UserColumnName="日付" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="商品コード" msprop:Generator_ColumnVarNameInTable="column商品コード" msprop:Generator_ColumnPropNameInRow="商品コード" msprop:Generator_ColumnPropNameInTable="商品コードColumn" msprop:Generator_UserColumnName="商品コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="商品名" msprop:Generator_ColumnVarNameInTable="column商品名" msprop:Generator_ColumnPropNameInRow="商品名" msprop:Generator_ColumnPropNameInTable="商品名Column" msprop:Generator_UserColumnName="商品名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="数量" msprop:Generator_ColumnVarNameInTable="column数量" msprop:Generator_ColumnPropNameInRow="数量" msprop:Generator_ColumnPropNameInTable="数量Column" msprop:Generator_UserColumnName="数量" type="xs:decimal" minOccurs="0" />
              <xs:element name="単位区分" msprop:Generator_ColumnVarNameInTable="column単位区分" msprop:Generator_ColumnPropNameInRow="単位区分" msprop:Generator_ColumnPropNameInTable="単位区分Column" msprop:Generator_UserColumnName="単位区分" type="xs:int" minOccurs="0" />
              <xs:element name="単位区分名" msprop:Generator_ColumnVarNameInTable="column単位区分名" msprop:Generator_ColumnPropNameInRow="単位区分名" msprop:Generator_ColumnPropNameInTable="単位区分名Column" msprop:Generator_UserColumnName="単位区分名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="単価" msprop:Generator_ColumnVarNameInTable="column単価" msprop:Generator_ColumnPropNameInRow="単価" msprop:Generator_ColumnPropNameInTable="単価Column" msprop:Generator_UserColumnName="単価" type="xs:decimal" minOccurs="0" />
              <xs:element name="基本料金" msprop:Generator_ColumnVarNameInTable="column基本料金" msprop:Generator_ColumnPropNameInRow="基本料金" msprop:Generator_ColumnPropNameInTable="基本料金Column" msprop:Generator_UserColumnName="基本料金" type="xs:decimal" minOccurs="0" />
              <xs:element name="売上金額" msprop:Generator_ColumnVarNameInTable="column売上金額" msprop:Generator_ColumnPropNameInRow="売上金額" msprop:Generator_ColumnPropNameInTable="売上金額Column" msprop:Generator_UserColumnName="売上金額" type="xs:decimal" minOccurs="0" />
              <xs:element name="入金金額" msprop:Generator_ColumnVarNameInTable="column入金金額" msprop:Generator_ColumnPropNameInRow="入金金額" msprop:Generator_ColumnPropNameInTable="入金金額Column" msprop:Generator_UserColumnName="入金金額" type="xs:decimal" minOccurs="0" />
              <xs:element name="請求残高" msprop:Generator_ColumnVarNameInTable="column請求残高" msprop:Generator_ColumnPropNameInRow="請求残高" msprop:Generator_ColumnPropNameInTable="請求残高Column" msprop:Generator_UserColumnName="請求残高" type="xs:decimal" minOccurs="0" />
              <xs:element name="開始日付" msprop:Generator_ColumnVarNameInTable="column開始日付" msprop:Generator_ColumnPropNameInRow="開始日付" msprop:Generator_ColumnPropNameInTable="開始日付Column" msprop:Generator_UserColumnName="開始日付" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="終了日付" msprop:Generator_ColumnVarNameInTable="column終了日付" msprop:Generator_ColumnPropNameInRow="終了日付" msprop:Generator_ColumnPropNameInTable="終了日付Column" msprop:Generator_UserColumnName="終了日付" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="備考" msprop:Generator_ColumnVarNameInTable="column備考" msprop:Generator_ColumnPropNameInRow="備考" msprop:Generator_ColumnPropNameInTable="備考Column" msprop:Generator_UserColumnName="備考" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="伝票区分" msprop:Generator_ColumnVarNameInTable="column伝票区分" msprop:Generator_ColumnPropNameInRow="伝票区分" msprop:Generator_ColumnPropNameInTable="伝票区分Column" msprop:Generator_UserColumnName="伝票区分">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Fリース商品検索" msprop:Generator_TableClassName="Fリース商品検索DataTable" msprop:Generator_TableVarName="tableFリース商品検索" msprop:Generator_TablePropName="Fリース商品検索" msprop:Generator_RowDeletingName="Fリース商品検索RowDeleting" msprop:Generator_RowChangingName="Fリース商品検索RowChanging" msprop:Generator_RowEvHandlerName="Fリース商品検索RowChangeEventHandler" msprop:Generator_RowDeletedName="Fリース商品検索RowDeleted" msprop:Generator_UserTableName="Fリース商品検索" msprop:Generator_RowChangedName="Fリース商品検索RowChanged" msprop:Generator_RowEvArgName="Fリース商品検索RowChangeEvent" msprop:Generator_RowClassName="Fリース商品検索Row">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="売上番号" msprop:Generator_ColumnVarNameInTable="column売上番号" msprop:Generator_ColumnPropNameInRow="売上番号" msprop:Generator_ColumnPropNameInTable="売上番号Column" msprop:Generator_UserColumnName="売上番号" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="行番号" msprop:Generator_ColumnVarNameInTable="column行番号" msprop:Generator_ColumnPropNameInRow="行番号" msprop:Generator_ColumnPropNameInTable="行番号Column" msprop:Generator_UserColumnName="行番号" type="xs:decimal" minOccurs="0" />
              <xs:element name="得意先コード" msprop:Generator_ColumnVarNameInTable="column得意先コード" msprop:Generator_ColumnPropNameInRow="得意先コード" msprop:Generator_ColumnPropNameInTable="得意先コードColumn" msprop:Generator_UserColumnName="得意先コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="得意先名" msprop:Generator_ColumnVarNameInTable="column得意先名" msprop:Generator_ColumnPropNameInRow="得意先名" msprop:Generator_ColumnPropNameInTable="得意先名Column" msprop:Generator_UserColumnName="得意先名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="工事コード" msprop:Generator_ColumnVarNameInTable="column工事コード" msprop:Generator_ColumnPropNameInRow="工事コード" msprop:Generator_ColumnPropNameInTable="工事コードColumn" msprop:Generator_UserColumnName="工事コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="工事内部コード" msprop:Generator_ColumnVarNameInTable="column工事内部コード" msprop:Generator_ColumnPropNameInRow="工事内部コード" msprop:Generator_ColumnPropNameInTable="工事内部コードColumn" msprop:Generator_UserColumnName="工事内部コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="工事名" msprop:Generator_ColumnVarNameInTable="column工事名" msprop:Generator_ColumnPropNameInRow="工事名" msprop:Generator_ColumnPropNameInTable="工事名Column" msprop:Generator_UserColumnName="工事名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="商品コード" msprop:Generator_ColumnVarNameInTable="column商品コード" msprop:Generator_ColumnPropNameInRow="商品コード" msprop:Generator_ColumnPropNameInTable="商品コードColumn" msprop:Generator_UserColumnName="商品コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="商品名" msprop:Generator_ColumnVarNameInTable="column商品名" msprop:Generator_ColumnPropNameInRow="商品名" msprop:Generator_ColumnPropNameInTable="商品名Column" msprop:Generator_UserColumnName="商品名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="数量" msprop:Generator_ColumnVarNameInTable="column数量" msprop:Generator_ColumnPropNameInRow="数量" msprop:Generator_ColumnPropNameInTable="数量Column" msprop:Generator_UserColumnName="数量" type="xs:decimal" minOccurs="0" />
              <xs:element name="単価" msprop:Generator_ColumnVarNameInTable="column単価" msprop:Generator_ColumnPropNameInRow="単価" msprop:Generator_ColumnPropNameInTable="単価Column" msprop:Generator_UserColumnName="単価" type="xs:decimal" minOccurs="0" />
              <xs:element name="開始日付" msprop:Generator_ColumnVarNameInTable="column開始日付" msprop:Generator_ColumnPropNameInRow="開始日付" msprop:Generator_ColumnPropNameInTable="開始日付Column" msprop:Generator_UserColumnName="開始日付" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="終了日付" msprop:Generator_ColumnVarNameInTable="column終了日付" msprop:Generator_ColumnPropNameInRow="終了日付" msprop:Generator_ColumnPropNameInTable="終了日付Column" msprop:Generator_UserColumnName="終了日付" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="売上行番号" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="column売上行番号" msprop:Generator_ColumnPropNameInRow="売上行番号" msprop:Generator_ColumnPropNameInTable="売上行番号Column" msprop:Generator_UserColumnName="売上行番号" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="15" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
    <xs:unique name="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:F売上伝票検索" />
      <xs:field xpath="mstns:売上番号" />
    </xs:unique>
    <xs:unique name="F売上定期処理_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:F売上定期処理" />
      <xs:field xpath="mstns:売上番号" />
      <xs:field xpath="mstns:行番号" />
    </xs:unique>
    <xs:unique name="F請求締期間_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:F請求締期間" />
      <xs:field xpath="mstns:請求番号" />
    </xs:unique>
    <xs:unique name="F請求計算検索_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:F請求計算検索" />
      <xs:field xpath="mstns:得意先コード" />
    </xs:unique>
    <xs:unique name="F請求計算DT_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:F請求計算DT" />
      <xs:field xpath="mstns:工事内部コード" />
    </xs:unique>
    <xs:unique name="F売上一覧表_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:F売上一覧表" />
      <xs:field xpath="mstns:売上番号" />
      <xs:field xpath="mstns:行番号" />
    </xs:unique>
    <xs:unique name="Fリース商品残数_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Fリース商品残数" />
      <xs:field xpath="mstns:売上番号" />
      <xs:field xpath="mstns:行番号" />
    </xs:unique>
    <xs:unique name="F請求書検索_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:F請求書検索" />
      <xs:field xpath="mstns:請求番号" />
    </xs:unique>
  </xs:element>
</xs:schema>