﻿Namespace Common

    '''====================================================================================
    ''' <summary>実行環境(Web.config)からDLLの接続文字列を更新するためのクラス(実装必須)</summary>
    '''====================================================================================
    Public Class ConnectionString
        Inherits Dbs.Asphalt.Database.BaseClass.ConnectionStringBase

        '''====================================================================================
        ''' <summary>データベースに接続するための接続文字列</summary>
        '''====================================================================================
        Public Overrides Property ConnectionString As String
            Get
                Return My.Settings.Item("ConnectionString")
            End Get
            Set(value As String)
                My.Settings.Item("ConnectionString") = value
            End Set
        End Property
    End Class

End Namespace
