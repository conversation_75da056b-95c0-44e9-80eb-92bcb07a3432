﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="DbMaster" targetNamespace="http://tempuri.org/DbMaster.xsd" xmlns:mstns="http://tempuri.org/DbMaster.xsd" xmlns="http://tempuri.org/DbMaster.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections>
          <Connection AppSettingsObjectName="MySettings" AppSettingsPropertyName="ConnectionString" ConnectionStringObject="" IsAppSettingsProperty="true" Modifier="Assembly" Name="ConnectionString (MySettings)" ParameterPrefix="@" PropertyReference="ApplicationSettings.Dbs.Application.Logic.Database.My.MySettings.GlobalReference.Default.ConnectionString" Provider="System.Data.SqlClient" />
        </Connections>
        <Tables>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="データリストTableAdapter" GeneratorDataComponentClassName="データリストTableAdapter" Name="データリスト" UserDataComponentName="データリストTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="AsphaltDB.dbo.M会社" DbObjectType="Table" GenerateMethods="Get" GenerateShortCommands="false" GeneratorGetMethodName="CompanyList" GetMethodModifier="Public" GetMethodName="CompanyList" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="CompanyList" UserSourceName="CompanyList">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT
会社ID as コード,
FORMAT(会社ID, '00') + ':' + 会社名 as 名前
FROM M会社
ORDER BY 会社ID
</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="名前" DataSetColumn="名前" />
              <Mapping SourceColumn="コード" DataSetColumn="コード" />
            </Mappings>
            <Sources>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="AsphaltDB.dbo.Sテンプレート" DbObjectType="Table" GenerateMethods="Get" GenerateShortCommands="true" GeneratorGetMethodName="TemplateList" GetMethodModifier="Public" GetMethodName="TemplateList" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="TemplateList" UserSourceName="TemplateList">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT
テンプレート名 as コード,
テンプレート名 as 名前
FROM Sテンプレート
WHERE カテゴリ = @カテゴリ
ORDER BY テンプレート名
</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="カテゴリ" ColumnName="カテゴリ" DataSourceName="AsphaltDB.dbo.Sテンプレート" DataTypeServer="nvarchar(100)" DbType="String" Direction="Input" ParameterName="@カテゴリ" Precision="0" ProviderType="NVarChar" Scale="0" Size="100" SourceColumn="カテゴリ" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </Sources>
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="オートコンプリートTableAdapter" GeneratorDataComponentClassName="オートコンプリートTableAdapter" Name="オートコンプリート" UserDataComponentName="オートコンプリートTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="AsphaltDB.dbo.M会社" DbObjectType="Table" GenerateMethods="Get" GenerateShortCommands="false" GeneratorGetMethodName="CompanyMaster" GetMethodModifier="Public" GetMethodName="CompanyMaster" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="CompanyMaster" UserSourceName="CompanyMaster">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT
会社ID as 入力値,
RIGHT('00000' + CAST(会社ID as varchar), 5) + ':' + 会社名 as リスト
FROM M会社
WHERE RIGHT('00000' + CAST(会社ID as varchar), 5) LIKE '%' + @キーワード + '%'
   OR 会社名 LIKE '%' + @キーワード + '%'
   OR 会社名カナ LIKE '%' + @キーワード + '%'
ORDER BY 会社ID
</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="キーワード" ColumnName="" DataSourceName="" DataTypeServer="nvarchar(50)" DbType="AnsiString" Direction="Input" ParameterName="@キーワード" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="入力値" DataSetColumn="入力値" />
              <Mapping SourceColumn="リスト" DataSetColumn="リスト" />
            </Mappings>
            <Sources>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="TokoDB.dbo.M工事" DbObjectType="Table" GenerateMethods="Get" GenerateShortCommands="true" GeneratorGetMethodName="KoujiCode" GetMethodModifier="Public" GetMethodName="KoujiCode" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="KoujiCode" UserSourceName="KoujiCode">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT  TOP(50) M工事.工事コード                  AS  入力値
     ,  M工事.工事コード + ':' +  M工事.工事名    AS  リスト
  FROM  M工事
 WHERE  M工事.得意先コード    =  @得意先コード
   AND (M工事.工事名       LIKE  @キーワード + '%'
    OR  M工事.工事名かな   LIKE  @キーワード + '%')
 ORDER
    BY  M工事.工事コード
 </CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="得意先コード" ColumnName="得意先コード" DataSourceName="TokoDB.dbo.M工事" DataTypeServer="varchar(6)" DbType="AnsiString" Direction="Input" ParameterName="@得意先コード" Precision="0" ProviderType="VarChar" Scale="0" Size="6" SourceColumn="得意先コード" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="キーワード" ColumnName="工事名" DataSourceName="TokoDB.dbo.M工事" DataTypeServer="nvarchar(100)" DbType="String" Direction="Input" ParameterName="@キーワード" Precision="0" ProviderType="NVarChar" Scale="0" Size="100" SourceColumn="工事名" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="TokoDB.dbo.Fリース商品検索" DbObjectType="Function" GenerateMethods="Get" GenerateShortCommands="true" GeneratorGetMethodName="LeaseItem" GetMethodModifier="Public" GetMethodName="LeaseItem" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="LeaseItem" UserSourceName="LeaseItem">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>
SELECT  TOP(50) _リース商品.売上行番号                        AS  入力値
     ,  _リース商品.商品コード + ':' +  _リース商品.商品名    AS  リスト
  FROM  Fリース商品検索(@得意先コード,@工事内部コード)  AS  _リース商品
 WHERE  _リース商品.商品名   LIKE  @キーワード + '%'
</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="得意先コード" ColumnName="" DataSourceName="" DataTypeServer="varchar(6)" DbType="AnsiString" Direction="Input" ParameterName="@得意先コード" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="工事内部コード" ColumnName="" DataSourceName="" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@工事内部コード" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="キーワード" ColumnName="商品名" DataSourceName="TokoDB.dbo.Fリース商品検索" DataTypeServer="nvarchar(100)" DbType="String" Direction="Input" ParameterName="@キーワード" Precision="0" ProviderType="NVarChar" Scale="0" Size="100" SourceColumn="商品名" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="AsphaltDB.dbo.M郵便番号" DbObjectType="Table" GenerateMethods="Get" GenerateShortCommands="true" GeneratorGetMethodName="PostNo" GetMethodModifier="Public" GetMethodName="PostNo" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="PostNo" UserSourceName="PostNo">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT TOP 50
郵便番号 as 入力値,
郵便番号 + ':' + 都道府県 + 市区郡 + 番地 as リスト
FROM M郵便番号
WHERE (郵便番号 LIKE @キーワード + '%' OR 都道府県 + 市区郡 + 番地 LIKE '%' + @キーワード + '%' OR 都道府県_カナ + 市区郡_カナ + 番地_カナ LIKE '%' + @キーワード + '%')
ORDER BY 郵便番号
</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="キーワード" ColumnName="郵便番号" DataSourceName="AsphaltDB.dbo.M郵便番号" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@キーワード" Precision="0" ProviderType="VarChar" Scale="0" Size="10" SourceColumn="入力値" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="TokoDB.dbo.M商品" DbObjectType="Table" GenerateMethods="Get" GenerateShortCommands="true" GeneratorGetMethodName="ShouhinCode" GetMethodModifier="Public" GetMethodName="ShouhinCode" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="ShouhinCode" UserSourceName="ShouhinCode">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT  TOP(50) M商品.商品コード                  AS  入力値
     ,  M商品.商品コード + ':' +  M商品.商品名    AS  リスト
  FROM  M商品
 WHERE  M商品.商品名       LIKE  @キーワード + '%'
    OR  M商品.商品名かな   LIKE  @キーワード + '%'
 ORDER
    BY  商品コード</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="キーワード" ColumnName="商品名" DataSourceName="TokoDB.dbo.M商品" DataTypeServer="nvarchar(100)" DbType="String" Direction="Input" ParameterName="@キーワード" Precision="0" ProviderType="NVarChar" Scale="0" Size="100" SourceColumn="商品名" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="TokoDB.dbo.M得意先" DbObjectType="Table" GenerateMethods="Get" GenerateShortCommands="true" GeneratorGetMethodName="TokuisakiCode" GetMethodModifier="Public" GetMethodName="TokuisakiCode" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="TokuisakiCode" UserSourceName="TokuisakiCode">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT  TOP(50) M得意先.得意先コード                      AS  入力値
     ,  M得意先.得意先コード + ':' +  M得意先.得意先名    AS  リスト
  FROM  M得意先
 WHERE  M得意先.得意先名     LIKE  @キーワード + '%'
    OR  M得意先.得意先かな   LIKE  @キーワード + '%'
ORDER
   BY  得意先コード</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="キーワード" ColumnName="得意先名" DataSourceName="TokoDB.dbo.M得意先" DataTypeServer="nvarchar(100)" DbType="String" Direction="Input" ParameterName="@キーワード" Precision="0" ProviderType="NVarChar" Scale="0" Size="100" SourceColumn="得意先名" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </Sources>
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="M工事TableAdapter" GeneratorDataComponentClassName="M工事TableAdapter" Name="M工事" UserDataComponentName="M工事TableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="TokoDB.dbo.M工事" DbObjectType="Table" GenerateMethods="Get" GenerateShortCommands="true" GeneratorGetMethodName="GetDataPK" GetMethodModifier="Public" GetMethodName="GetDataPK" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetDataPK" UserSourceName="GetDataPK">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [M工事] WHERE (([工事内部コード] = @Original_工事内部コード) AND ((@IsNull_得意先コード = 1 AND [得意先コード] IS NULL) OR ([得意先コード] = @Original_得意先コード)) AND ((@IsNull_工事コード = 1 AND [工事コード] IS NULL) OR ([工事コード] = @Original_工事コード)) AND ((@IsNull_工事名 = 1 AND [工事名] IS NULL) OR ([工事名] = @Original_工事名)) AND ((@IsNull_工事名かな = 1 AND [工事名かな] IS NULL) OR ([工事名かな] = @Original_工事名かな)) AND ((@IsNull_完了区分 = 1 AND [完了区分] IS NULL) OR ([完了区分] = @Original_完了区分)) AND ((@IsNull_住所 = 1 AND [住所] IS NULL) OR ([住所] = @Original_住所)) AND ((@IsNull_摘要1 = 1 AND [摘要1] IS NULL) OR ([摘要1] = @Original_摘要1)) AND ((@IsNull_摘要2 = 1 AND [摘要2] IS NULL) OR ([摘要2] = @Original_摘要2)) AND ((@IsNull_削除区分 = 1 AND [削除区分] IS NULL) OR ([削除区分] = @Original_削除区分)) AND ((@IsNull_登録日時 = 1 AND [登録日時] IS NULL) OR ([登録日時] = @Original_登録日時)) AND ((@IsNull_登録者ID = 1 AND [登録者ID] IS NULL) OR ([登録者ID] = @Original_登録者ID)) AND ((@IsNull_更新日時 = 1 AND [更新日時] IS NULL) OR ([更新日時] = @Original_更新日時)) AND ((@IsNull_更新者ID = 1 AND [更新者ID] IS NULL) OR ([更新者ID] = @Original_更新者ID)))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_工事内部コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="工事内部コード" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_得意先コード" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="得意先コード" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_得意先コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="得意先コード" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_工事コード" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="工事コード" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_工事コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="工事コード" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_工事名" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="工事名" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_工事名" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="工事名" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_工事名かな" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="工事名かな" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_工事名かな" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="工事名かな" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_完了区分" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="完了区分" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_完了区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="完了区分" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_住所" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="住所" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_住所" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="住所" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_摘要1" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="摘要1" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_摘要1" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="摘要1" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_摘要2" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="摘要2" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_摘要2" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="摘要2" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_削除区分" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="削除区分" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_削除区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="削除区分" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_登録日時" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="登録日時" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_登録日時" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="登録日時" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_登録者ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="登録者ID" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_登録者ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="登録者ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_更新日時" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="更新日時" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_更新日時" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="更新日時" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_更新者ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="更新者ID" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_更新者ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="更新者ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [M工事] ([工事内部コード], [得意先コード], [工事コード], [工事名], [工事名かな], [完了区分], [住所], [摘要1], [摘要2], [削除区分], [登録日時], [登録者ID], [更新日時], [更新者ID]) VALUES (@工事内部コード, @得意先コード, @工事コード, @工事名, @工事名かな, @完了区分, @住所, @摘要1, @摘要2, @削除区分, @登録日時, @登録者ID, @更新日時, @更新者ID);
SELECT 工事内部コード, 得意先コード, 工事コード, 工事名, 工事名かな, 完了区分, 住所, 摘要1, 摘要2, 削除区分, 登録日時, 登録者ID, 更新日時, 更新者ID FROM M工事 WHERE (工事内部コード = @工事内部コード)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@工事内部コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="工事内部コード" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@得意先コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="得意先コード" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@工事コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="工事コード" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@工事名" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="工事名" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@工事名かな" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="工事名かな" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@完了区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="完了区分" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@住所" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="住所" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@摘要1" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="摘要1" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@摘要2" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="摘要2" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@削除区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="削除区分" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@登録日時" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="登録日時" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@登録者ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="登録者ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@更新日時" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="更新日時" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@更新者ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="更新者ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT                      工事内部コード, 得意先コード, 工事コード, 工事名, 工事名かな, 完了区分, 住所, 摘要1, 摘要2, 削除区分, 登録日時, 登録者ID, 更新日時, 更新者ID
FROM                         M工事
WHERE                       (工事内部コード = @工事内部コード) AND (削除区分 = 0)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="工事内部コード" ColumnName="工事内部コード" DataSourceName="TokoDB.dbo.M工事" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@工事内部コード" Precision="0" ProviderType="VarChar" Scale="0" Size="10" SourceColumn="工事内部コード" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [M工事] SET [工事内部コード] = @工事内部コード, [得意先コード] = @得意先コード, [工事コード] = @工事コード, [工事名] = @工事名, [工事名かな] = @工事名かな, [完了区分] = @完了区分, [住所] = @住所, [摘要1] = @摘要1, [摘要2] = @摘要2, [削除区分] = @削除区分, [登録日時] = @登録日時, [登録者ID] = @登録者ID, [更新日時] = @更新日時, [更新者ID] = @更新者ID WHERE (([工事内部コード] = @Original_工事内部コード) AND ((@IsNull_得意先コード = 1 AND [得意先コード] IS NULL) OR ([得意先コード] = @Original_得意先コード)) AND ((@IsNull_工事コード = 1 AND [工事コード] IS NULL) OR ([工事コード] = @Original_工事コード)) AND ((@IsNull_工事名 = 1 AND [工事名] IS NULL) OR ([工事名] = @Original_工事名)) AND ((@IsNull_工事名かな = 1 AND [工事名かな] IS NULL) OR ([工事名かな] = @Original_工事名かな)) AND ((@IsNull_完了区分 = 1 AND [完了区分] IS NULL) OR ([完了区分] = @Original_完了区分)) AND ((@IsNull_住所 = 1 AND [住所] IS NULL) OR ([住所] = @Original_住所)) AND ((@IsNull_摘要1 = 1 AND [摘要1] IS NULL) OR ([摘要1] = @Original_摘要1)) AND ((@IsNull_摘要2 = 1 AND [摘要2] IS NULL) OR ([摘要2] = @Original_摘要2)) AND ((@IsNull_削除区分 = 1 AND [削除区分] IS NULL) OR ([削除区分] = @Original_削除区分)) AND ((@IsNull_登録日時 = 1 AND [登録日時] IS NULL) OR ([登録日時] = @Original_登録日時)) AND ((@IsNull_登録者ID = 1 AND [登録者ID] IS NULL) OR ([登録者ID] = @Original_登録者ID)) AND ((@IsNull_更新日時 = 1 AND [更新日時] IS NULL) OR ([更新日時] = @Original_更新日時)) AND ((@IsNull_更新者ID = 1 AND [更新者ID] IS NULL) OR ([更新者ID] = @Original_更新者ID)));
SELECT 工事内部コード, 得意先コード, 工事コード, 工事名, 工事名かな, 完了区分, 住所, 摘要1, 摘要2, 削除区分, 登録日時, 登録者ID, 更新日時, 更新者ID FROM M工事 WHERE (工事内部コード = @工事内部コード)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@工事内部コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="工事内部コード" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@得意先コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="得意先コード" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@工事コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="工事コード" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@工事名" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="工事名" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@工事名かな" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="工事名かな" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@完了区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="完了区分" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@住所" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="住所" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@摘要1" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="摘要1" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@摘要2" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="摘要2" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@削除区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="削除区分" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@登録日時" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="登録日時" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@登録者ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="登録者ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@更新日時" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="更新日時" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@更新者ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="更新者ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_工事内部コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="工事内部コード" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_得意先コード" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="得意先コード" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_得意先コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="得意先コード" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_工事コード" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="工事コード" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_工事コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="工事コード" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_工事名" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="工事名" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_工事名" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="工事名" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_工事名かな" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="工事名かな" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_工事名かな" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="工事名かな" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_完了区分" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="完了区分" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_完了区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="完了区分" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_住所" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="住所" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_住所" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="住所" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_摘要1" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="摘要1" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_摘要1" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="摘要1" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_摘要2" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="摘要2" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_摘要2" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="摘要2" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_削除区分" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="削除区分" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_削除区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="削除区分" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_登録日時" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="登録日時" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_登録日時" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="登録日時" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_登録者ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="登録者ID" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_登録者ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="登録者ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_更新日時" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="更新日時" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_更新日時" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="更新日時" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_更新者ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="更新者ID" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_更新者ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="更新者ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="工事内部コード" DataSetColumn="工事内部コード" />
              <Mapping SourceColumn="得意先コード" DataSetColumn="得意先コード" />
              <Mapping SourceColumn="工事コード" DataSetColumn="工事コード" />
              <Mapping SourceColumn="工事名" DataSetColumn="工事名" />
              <Mapping SourceColumn="工事名かな" DataSetColumn="工事名かな" />
              <Mapping SourceColumn="完了区分" DataSetColumn="完了区分" />
              <Mapping SourceColumn="住所" DataSetColumn="住所" />
              <Mapping SourceColumn="摘要1" DataSetColumn="摘要1" />
              <Mapping SourceColumn="摘要2" DataSetColumn="摘要2" />
              <Mapping SourceColumn="削除区分" DataSetColumn="削除区分" />
              <Mapping SourceColumn="登録日時" DataSetColumn="登録日時" />
              <Mapping SourceColumn="登録者ID" DataSetColumn="登録者ID" />
              <Mapping SourceColumn="更新日時" DataSetColumn="更新日時" />
              <Mapping SourceColumn="更新者ID" DataSetColumn="更新者ID" />
            </Mappings>
            <Sources>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="" DbObjectType="Unknown" GenerateShortCommands="true" GeneratorSourceName="DeleteByCode" Modifier="Public" Name="DeleteByCode" QueryType="NoData" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetDataBy" UserSourceName="DeleteByCode">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>DELETE FROM [M工事] WHERE [工事内部コード] = @工事内部コード</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="工事内部コード" ColumnName="工事内部コード" DataSourceName="TokoDB.dbo.M工事" DataTypeServer="varchar(10)" DbType="AnsiString" Direction="Input" ParameterName="@工事内部コード" Precision="0" ProviderType="VarChar" Scale="0" Size="10" SourceColumn="工事内部コード" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
              </DbSource>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="TokoDB.dbo.M工事" DbObjectType="Table" GenerateMethods="Get" GenerateShortCommands="true" GeneratorGetMethodName="GetDataAll" GetMethodModifier="Public" GetMethodName="GetDataAll" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetDataAll" UserSourceName="GetDataAll">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT                      M工事.*
FROM                         M工事</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
              </DbSource>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="TokoDB.dbo.M工事" DbObjectType="Table" GenerateMethods="Get" GenerateShortCommands="true" GeneratorGetMethodName="GetDataUK" GetMethodModifier="Public" GetMethodName="GetDataUK" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetDataUK" UserSourceName="GetDataUK">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT                      M工事.*
FROM                         M工事
WHERE                       (削除区分 = 0) AND (得意先コード = @得意先コード) AND (工事コード = @工事コード)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="得意先コード" ColumnName="得意先コード" DataSourceName="TokoDB.dbo.M工事" DataTypeServer="varchar(6)" DbType="AnsiString" Direction="Input" ParameterName="@得意先コード" Precision="0" ProviderType="VarChar" Scale="0" Size="6" SourceColumn="得意先コード" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="工事コード" ColumnName="工事コード" DataSourceName="TokoDB.dbo.M工事" DataTypeServer="varchar(6)" DbType="AnsiString" Direction="Input" ParameterName="@工事コード" Precision="0" ProviderType="VarChar" Scale="0" Size="6" SourceColumn="工事コード" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </Sources>
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="M商品TableAdapter" GeneratorDataComponentClassName="M商品TableAdapter" Name="M商品" UserDataComponentName="M商品TableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="TokoDB.dbo.M商品" DbObjectType="Table" GenerateMethods="Get" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="GetData">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [M商品] WHERE (([商品コード] = @Original_商品コード) AND ((@IsNull_商品名 = 1 AND [商品名] IS NULL) OR ([商品名] = @Original_商品名)) AND ((@IsNull_商品名かな = 1 AND [商品名かな] IS NULL) OR ([商品名かな] = @Original_商品名かな)) AND ((@IsNull_単位区分 = 1 AND [単位区分] IS NULL) OR ([単位区分] = @Original_単位区分)) AND ((@IsNull_基本料金 = 1 AND [基本料金] IS NULL) OR ([基本料金] = @Original_基本料金)) AND ((@IsNull_単価 = 1 AND [単価] IS NULL) OR ([単価] = @Original_単価)) AND ((@IsNull_商品分類区分 = 1 AND [商品分類区分] IS NULL) OR ([商品分類区分] = @Original_商品分類区分)) AND ((@IsNull_備考 = 1 AND [備考] IS NULL) OR ([備考] = @Original_備考)) AND ((@IsNull_保証日数 = 1 AND [保証日数] IS NULL) OR ([保証日数] = @Original_保証日数)) AND ((@IsNull_登録日時 = 1 AND [登録日時] IS NULL) OR ([登録日時] = @Original_登録日時)) AND ((@IsNull_登録者ID = 1 AND [登録者ID] IS NULL) OR ([登録者ID] = @Original_登録者ID)) AND ((@IsNull_更新日時 = 1 AND [更新日時] IS NULL) OR ([更新日時] = @Original_更新日時)) AND ((@IsNull_更新者ID = 1 AND [更新者ID] IS NULL) OR ([更新者ID] = @Original_更新者ID)))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_商品コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="商品コード" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_商品名" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="商品名" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_商品名" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="商品名" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_商品名かな" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="商品名かな" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_商品名かな" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="商品名かな" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_単位区分" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="単位区分" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_単位区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="単位区分" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_基本料金" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="基本料金" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_基本料金" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="基本料金" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_単価" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="単価" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_単価" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="単価" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_商品分類区分" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="商品分類区分" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_商品分類区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="商品分類区分" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_備考" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="備考" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_備考" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="備考" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_保証日数" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="保証日数" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_保証日数" Precision="9" ProviderType="Decimal" Scale="0" Size="0" SourceColumn="保証日数" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_登録日時" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="登録日時" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_登録日時" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="登録日時" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_登録者ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="登録者ID" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_登録者ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="登録者ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_更新日時" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="更新日時" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_更新日時" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="更新日時" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_更新者ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="更新者ID" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_更新者ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="更新者ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [M商品] ([商品コード], [商品名], [商品名かな], [単位区分], [基本料金], [単価], [商品分類区分], [備考], [保証日数], [登録日時], [登録者ID], [更新日時], [更新者ID]) VALUES (@商品コード, @商品名, @商品名かな, @単位区分, @基本料金, @単価, @商品分類区分, @備考, @保証日数, @登録日時, @登録者ID, @更新日時, @更新者ID);
SELECT 商品コード, 商品名, 商品名かな, 単位区分, 基本料金, 単価, 商品分類区分, 備考, 保証日数, 登録日時, 登録者ID, 更新日時, 更新者ID FROM M商品 WHERE (商品コード = @商品コード)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@商品コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="商品コード" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@商品名" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="商品名" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@商品名かな" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="商品名かな" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@単位区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="単位区分" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@基本料金" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="基本料金" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@単価" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="単価" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@商品分類区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="商品分類区分" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@備考" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="備考" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@保証日数" Precision="9" ProviderType="Decimal" Scale="0" Size="0" SourceColumn="保証日数" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@登録日時" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="登録日時" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@登録者ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="登録者ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@更新日時" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="更新日時" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@更新者ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="更新者ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT                      M商品.*
FROM                         M商品
WHERE                       (商品コード = @商品コード)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="商品コード" ColumnName="商品コード" DataSourceName="TokoDB.dbo.M商品" DataTypeServer="varchar(6)" DbType="AnsiString" Direction="Input" ParameterName="@商品コード" Precision="0" ProviderType="VarChar" Scale="0" Size="6" SourceColumn="商品コード" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [M商品] SET [商品コード] = @商品コード, [商品名] = @商品名, [商品名かな] = @商品名かな, [単位区分] = @単位区分, [基本料金] = @基本料金, [単価] = @単価, [商品分類区分] = @商品分類区分, [備考] = @備考, [保証日数] = @保証日数, [登録日時] = @登録日時, [登録者ID] = @登録者ID, [更新日時] = @更新日時, [更新者ID] = @更新者ID WHERE (([商品コード] = @Original_商品コード) AND ((@IsNull_商品名 = 1 AND [商品名] IS NULL) OR ([商品名] = @Original_商品名)) AND ((@IsNull_商品名かな = 1 AND [商品名かな] IS NULL) OR ([商品名かな] = @Original_商品名かな)) AND ((@IsNull_単位区分 = 1 AND [単位区分] IS NULL) OR ([単位区分] = @Original_単位区分)) AND ((@IsNull_基本料金 = 1 AND [基本料金] IS NULL) OR ([基本料金] = @Original_基本料金)) AND ((@IsNull_単価 = 1 AND [単価] IS NULL) OR ([単価] = @Original_単価)) AND ((@IsNull_商品分類区分 = 1 AND [商品分類区分] IS NULL) OR ([商品分類区分] = @Original_商品分類区分)) AND ((@IsNull_備考 = 1 AND [備考] IS NULL) OR ([備考] = @Original_備考)) AND ((@IsNull_保証日数 = 1 AND [保証日数] IS NULL) OR ([保証日数] = @Original_保証日数)) AND ((@IsNull_登録日時 = 1 AND [登録日時] IS NULL) OR ([登録日時] = @Original_登録日時)) AND ((@IsNull_登録者ID = 1 AND [登録者ID] IS NULL) OR ([登録者ID] = @Original_登録者ID)) AND ((@IsNull_更新日時 = 1 AND [更新日時] IS NULL) OR ([更新日時] = @Original_更新日時)) AND ((@IsNull_更新者ID = 1 AND [更新者ID] IS NULL) OR ([更新者ID] = @Original_更新者ID)));
SELECT 商品コード, 商品名, 商品名かな, 単位区分, 基本料金, 単価, 商品分類区分, 備考, 保証日数, 登録日時, 登録者ID, 更新日時, 更新者ID FROM M商品 WHERE (商品コード = @商品コード)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@商品コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="商品コード" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@商品名" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="商品名" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@商品名かな" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="商品名かな" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@単位区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="単位区分" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@基本料金" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="基本料金" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@単価" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="単価" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@商品分類区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="商品分類区分" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@備考" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="備考" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@保証日数" Precision="9" ProviderType="Decimal" Scale="0" Size="0" SourceColumn="保証日数" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@登録日時" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="登録日時" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@登録者ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="登録者ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@更新日時" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="更新日時" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@更新者ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="更新者ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_商品コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="商品コード" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_商品名" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="商品名" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_商品名" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="商品名" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_商品名かな" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="商品名かな" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_商品名かな" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="商品名かな" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_単位区分" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="単位区分" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_単位区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="単位区分" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_基本料金" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="基本料金" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_基本料金" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="基本料金" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_単価" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="単価" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_単価" Precision="18" ProviderType="Decimal" Scale="5" Size="0" SourceColumn="単価" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_商品分類区分" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="商品分類区分" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_商品分類区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="商品分類区分" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_備考" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="備考" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_備考" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="備考" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_保証日数" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="保証日数" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_保証日数" Precision="9" ProviderType="Decimal" Scale="0" Size="0" SourceColumn="保証日数" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_登録日時" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="登録日時" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_登録日時" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="登録日時" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_登録者ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="登録者ID" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_登録者ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="登録者ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_更新日時" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="更新日時" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_更新日時" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="更新日時" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_更新者ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="更新者ID" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_更新者ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="更新者ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="商品コード" DataSetColumn="商品コード" />
              <Mapping SourceColumn="商品名" DataSetColumn="商品名" />
              <Mapping SourceColumn="商品名かな" DataSetColumn="商品名かな" />
              <Mapping SourceColumn="単位区分" DataSetColumn="単位区分" />
              <Mapping SourceColumn="基本料金" DataSetColumn="基本料金" />
              <Mapping SourceColumn="単価" DataSetColumn="単価" />
              <Mapping SourceColumn="商品分類区分" DataSetColumn="商品分類区分" />
              <Mapping SourceColumn="備考" DataSetColumn="備考" />
              <Mapping SourceColumn="保証日数" DataSetColumn="保証日数" />
              <Mapping SourceColumn="登録日時" DataSetColumn="登録日時" />
              <Mapping SourceColumn="登録者ID" DataSetColumn="登録者ID" />
              <Mapping SourceColumn="更新日時" DataSetColumn="更新日時" />
              <Mapping SourceColumn="更新者ID" DataSetColumn="更新者ID" />
            </Mappings>
            <Sources>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="" DbObjectType="Unknown" GenerateShortCommands="true" GeneratorSourceName="DeleteByCode" Modifier="Public" Name="DeleteByCode" QueryType="NoData" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetDataBy" UserSourceName="DeleteByCode">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>DELETE FROM [M商品] WHERE 商品コード = @商品コード</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="商品コード" ColumnName="商品コード" DataSourceName="TokoDB.dbo.M商品" DataTypeServer="varchar(6)" DbType="AnsiString" Direction="Input" ParameterName="@商品コード" Precision="0" ProviderType="VarChar" Scale="0" Size="6" SourceColumn="商品コード" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
              </DbSource>
            </Sources>
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="M得意先TableAdapter" GeneratorDataComponentClassName="M得意先TableAdapter" Name="M得意先" UserDataComponentName="M得意先TableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="TokoDB.dbo.M得意先" DbObjectType="Table" GenerateMethods="Get" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="GetData">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [M得意先] WHERE (([得意先コード] = @Original_得意先コード) AND ((@IsNull_得意先名 = 1 AND [得意先名] IS NULL) OR ([得意先名] = @Original_得意先名)) AND ((@IsNull_得意先かな = 1 AND [得意先かな] IS NULL) OR ([得意先かな] = @Original_得意先かな)) AND ((@IsNull_郵便番号 = 1 AND [郵便番号] IS NULL) OR ([郵便番号] = @Original_郵便番号)) AND ((@IsNull_住所1 = 1 AND [住所1] IS NULL) OR ([住所1] = @Original_住所1)) AND ((@IsNull_住所2 = 1 AND [住所2] IS NULL) OR ([住所2] = @Original_住所2)) AND ((@IsNull_電話番号 = 1 AND [電話番号] IS NULL) OR ([電話番号] = @Original_電話番号)) AND ((@IsNull_FAX番号 = 1 AND [FAX番号] IS NULL) OR ([FAX番号] = @Original_FAX番号)) AND ((@IsNull_締日区分 = 1 AND [締日区分] IS NULL) OR ([締日区分] = @Original_締日区分)) AND ((@IsNull_端数区分 = 1 AND [端数区分] IS NULL) OR ([端数区分] = @Original_端数区分)) AND ((@IsNull_請求書区分 = 1 AND [請求書区分] IS NULL) OR ([請求書区分] = @Original_請求書区分)) AND ((@IsNull_担当者コード = 1 AND [担当者コード] IS NULL) OR ([担当者コード] = @Original_担当者コード)) AND ((@IsNull_諸口区分 = 1 AND [諸口区分] IS NULL) OR ([諸口区分] = @Original_諸口区分)) AND ((@IsNull_消費税区分 = 1 AND [消費税区分] IS NULL) OR ([消費税区分] = @Original_消費税区分)) AND ((@IsNull_登録日時 = 1 AND [登録日時] IS NULL) OR ([登録日時] = @Original_登録日時)) AND ((@IsNull_登録者ID = 1 AND [登録者ID] IS NULL) OR ([登録者ID] = @Original_登録者ID)) AND ((@IsNull_更新日時 = 1 AND [更新日時] IS NULL) OR ([更新日時] = @Original_更新日時)) AND ((@IsNull_更新者ID = 1 AND [更新者ID] IS NULL) OR ([更新者ID] = @Original_更新者ID)))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_得意先コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="得意先コード" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_得意先名" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="得意先名" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_得意先名" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="得意先名" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_得意先かな" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="得意先かな" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_得意先かな" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="得意先かな" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_郵便番号" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="郵便番号" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_郵便番号" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="郵便番号" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_住所1" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="住所1" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_住所1" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="住所1" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_住所2" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="住所2" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_住所2" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="住所2" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_電話番号" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="電話番号" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_電話番号" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="電話番号" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_FAX番号" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="FAX番号" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_FAX番号" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="FAX番号" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_締日区分" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="締日区分" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_締日区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="締日区分" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_端数区分" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="端数区分" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_端数区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="端数区分" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_請求書区分" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="請求書区分" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_請求書区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="請求書区分" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_担当者コード" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="担当者コード" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_担当者コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="担当者コード" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_諸口区分" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="諸口区分" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_諸口区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="諸口区分" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_消費税区分" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="消費税区分" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_消費税区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="消費税区分" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_登録日時" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="登録日時" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_登録日時" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="登録日時" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_登録者ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="登録者ID" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_登録者ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="登録者ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_更新日時" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="更新日時" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_更新日時" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="更新日時" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_更新者ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="更新者ID" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_更新者ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="更新者ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [M得意先] ([得意先コード], [得意先名], [得意先かな], [郵便番号], [住所1], [住所2], [電話番号], [FAX番号], [締日区分], [端数区分], [請求書区分], [担当者コード], [諸口区分], [消費税区分], [登録日時], [登録者ID], [更新日時], [更新者ID]) VALUES (@得意先コード, @得意先名, @得意先かな, @郵便番号, @住所1, @住所2, @電話番号, @FAX番号, @締日区分, @端数区分, @請求書区分, @担当者コード, @諸口区分, @消費税区分, @登録日時, @登録者ID, @更新日時, @更新者ID);
SELECT 得意先コード, 得意先名, 得意先かな, 郵便番号, 住所1, 住所2, 電話番号, FAX番号, 締日区分, 端数区分, 請求書区分, 担当者コード, 諸口区分, 消費税区分, 登録日時, 登録者ID, 更新日時, 更新者ID FROM M得意先 WHERE (得意先コード = @得意先コード)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@得意先コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="得意先コード" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@得意先名" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="得意先名" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@得意先かな" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="得意先かな" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@郵便番号" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="郵便番号" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@住所1" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="住所1" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@住所2" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="住所2" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@電話番号" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="電話番号" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@FAX番号" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="FAX番号" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@締日区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="締日区分" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@端数区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="端数区分" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@請求書区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="請求書区分" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@担当者コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="担当者コード" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@諸口区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="諸口区分" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@消費税区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="消費税区分" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@登録日時" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="登録日時" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@登録者ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="登録者ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@更新日時" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="更新日時" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@更新者ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="更新者ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT * FROM M得意先
WHERE 得意先コード = @得意先コード
</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="得意先コード" ColumnName="得意先コード" DataSourceName="TokoDB.dbo.M得意先" DataTypeServer="varchar(6)" DbType="AnsiString" Direction="Input" ParameterName="@得意先コード" Precision="0" ProviderType="VarChar" Scale="0" Size="6" SourceColumn="得意先コード" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [M得意先] SET [得意先コード] = @得意先コード, [得意先名] = @得意先名, [得意先かな] = @得意先かな, [郵便番号] = @郵便番号, [住所1] = @住所1, [住所2] = @住所2, [電話番号] = @電話番号, [FAX番号] = @FAX番号, [締日区分] = @締日区分, [端数区分] = @端数区分, [請求書区分] = @請求書区分, [担当者コード] = @担当者コード, [諸口区分] = @諸口区分, [消費税区分] = @消費税区分, [登録日時] = @登録日時, [登録者ID] = @登録者ID, [更新日時] = @更新日時, [更新者ID] = @更新者ID WHERE (([得意先コード] = @Original_得意先コード) AND ((@IsNull_得意先名 = 1 AND [得意先名] IS NULL) OR ([得意先名] = @Original_得意先名)) AND ((@IsNull_得意先かな = 1 AND [得意先かな] IS NULL) OR ([得意先かな] = @Original_得意先かな)) AND ((@IsNull_郵便番号 = 1 AND [郵便番号] IS NULL) OR ([郵便番号] = @Original_郵便番号)) AND ((@IsNull_住所1 = 1 AND [住所1] IS NULL) OR ([住所1] = @Original_住所1)) AND ((@IsNull_住所2 = 1 AND [住所2] IS NULL) OR ([住所2] = @Original_住所2)) AND ((@IsNull_電話番号 = 1 AND [電話番号] IS NULL) OR ([電話番号] = @Original_電話番号)) AND ((@IsNull_FAX番号 = 1 AND [FAX番号] IS NULL) OR ([FAX番号] = @Original_FAX番号)) AND ((@IsNull_締日区分 = 1 AND [締日区分] IS NULL) OR ([締日区分] = @Original_締日区分)) AND ((@IsNull_端数区分 = 1 AND [端数区分] IS NULL) OR ([端数区分] = @Original_端数区分)) AND ((@IsNull_請求書区分 = 1 AND [請求書区分] IS NULL) OR ([請求書区分] = @Original_請求書区分)) AND ((@IsNull_担当者コード = 1 AND [担当者コード] IS NULL) OR ([担当者コード] = @Original_担当者コード)) AND ((@IsNull_諸口区分 = 1 AND [諸口区分] IS NULL) OR ([諸口区分] = @Original_諸口区分)) AND ((@IsNull_消費税区分 = 1 AND [消費税区分] IS NULL) OR ([消費税区分] = @Original_消費税区分)) AND ((@IsNull_登録日時 = 1 AND [登録日時] IS NULL) OR ([登録日時] = @Original_登録日時)) AND ((@IsNull_登録者ID = 1 AND [登録者ID] IS NULL) OR ([登録者ID] = @Original_登録者ID)) AND ((@IsNull_更新日時 = 1 AND [更新日時] IS NULL) OR ([更新日時] = @Original_更新日時)) AND ((@IsNull_更新者ID = 1 AND [更新者ID] IS NULL) OR ([更新者ID] = @Original_更新者ID)));
SELECT 得意先コード, 得意先名, 得意先かな, 郵便番号, 住所1, 住所2, 電話番号, FAX番号, 締日区分, 端数区分, 請求書区分, 担当者コード, 諸口区分, 消費税区分, 登録日時, 登録者ID, 更新日時, 更新者ID FROM M得意先 WHERE (得意先コード = @得意先コード)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@得意先コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="得意先コード" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@得意先名" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="得意先名" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@得意先かな" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="得意先かな" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@郵便番号" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="郵便番号" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@住所1" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="住所1" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@住所2" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="住所2" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@電話番号" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="電話番号" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@FAX番号" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="FAX番号" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@締日区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="締日区分" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@端数区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="端数区分" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@請求書区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="請求書区分" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@担当者コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="担当者コード" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@諸口区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="諸口区分" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@消費税区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="消費税区分" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@登録日時" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="登録日時" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@登録者ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="登録者ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@更新日時" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="更新日時" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@更新者ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="更新者ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_得意先コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="得意先コード" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_得意先名" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="得意先名" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_得意先名" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="得意先名" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_得意先かな" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="得意先かな" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_得意先かな" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="得意先かな" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_郵便番号" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="郵便番号" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_郵便番号" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="郵便番号" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_住所1" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="住所1" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_住所1" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="住所1" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_住所2" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="住所2" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_住所2" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="住所2" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_電話番号" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="電話番号" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_電話番号" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="電話番号" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_FAX番号" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="FAX番号" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_FAX番号" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="FAX番号" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_締日区分" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="締日区分" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_締日区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="締日区分" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_端数区分" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="端数区分" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_端数区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="端数区分" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_請求書区分" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="請求書区分" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_請求書区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="請求書区分" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_担当者コード" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="担当者コード" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_担当者コード" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="担当者コード" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_諸口区分" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="諸口区分" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_諸口区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="諸口区分" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_消費税区分" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="消費税区分" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_消費税区分" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="消費税区分" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_登録日時" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="登録日時" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_登録日時" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="登録日時" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_登録者ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="登録者ID" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_登録者ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="登録者ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_更新日時" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="更新日時" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_更新日時" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="更新日時" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_更新者ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="更新者ID" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_更新者ID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="更新者ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="得意先コード" DataSetColumn="得意先コード" />
              <Mapping SourceColumn="得意先名" DataSetColumn="得意先名" />
              <Mapping SourceColumn="得意先かな" DataSetColumn="得意先かな" />
              <Mapping SourceColumn="郵便番号" DataSetColumn="郵便番号" />
              <Mapping SourceColumn="住所1" DataSetColumn="住所1" />
              <Mapping SourceColumn="住所2" DataSetColumn="住所2" />
              <Mapping SourceColumn="電話番号" DataSetColumn="電話番号" />
              <Mapping SourceColumn="FAX番号" DataSetColumn="FAX番号" />
              <Mapping SourceColumn="締日区分" DataSetColumn="締日区分" />
              <Mapping SourceColumn="端数区分" DataSetColumn="端数区分" />
              <Mapping SourceColumn="請求書区分" DataSetColumn="請求書区分" />
              <Mapping SourceColumn="担当者コード" DataSetColumn="担当者コード" />
              <Mapping SourceColumn="諸口区分" DataSetColumn="諸口区分" />
              <Mapping SourceColumn="消費税区分" DataSetColumn="消費税区分" />
              <Mapping SourceColumn="登録日時" DataSetColumn="登録日時" />
              <Mapping SourceColumn="登録者ID" DataSetColumn="登録者ID" />
              <Mapping SourceColumn="更新日時" DataSetColumn="更新日時" />
              <Mapping SourceColumn="更新者ID" DataSetColumn="更新者ID" />
            </Mappings>
            <Sources>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="" DbObjectType="Unknown" GenerateShortCommands="true" GeneratorSourceName="DeleteByCode" Modifier="Public" Name="DeleteByCode" QueryType="NoData" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetDataBy" UserSourceName="DeleteByCode">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>DELETE FROM [M得意先] WHERE [得意先コード] = @得意先コード</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="得意先コード" ColumnName="得意先コード" DataSourceName="TokoDB.dbo.M得意先" DataTypeServer="varchar(6)" DbType="AnsiString" Direction="Input" ParameterName="@得意先コード" Precision="0" ProviderType="VarChar" Scale="0" Size="6" SourceColumn="得意先コード" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
              </DbSource>
            </Sources>
          </TableAdapter>
        </Tables>
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="DbMaster" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:EnableTableAdapterManager="true" msprop:Generator_DataSetName="DbMaster" msprop:Generator_UserDSName="DbMaster">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="データリスト" msprop:Generator_TableClassName="データリストDataTable" msprop:Generator_TableVarName="tableデータリスト" msprop:Generator_TablePropName="データリスト" msprop:Generator_RowDeletingName="データリストRowDeleting" msprop:Generator_RowChangingName="データリストRowChanging" msprop:Generator_RowEvHandlerName="データリストRowChangeEventHandler" msprop:Generator_RowDeletedName="データリストRowDeleted" msprop:Generator_UserTableName="データリスト" msprop:Generator_RowChangedName="データリストRowChanged" msprop:Generator_RowEvArgName="データリストRowChangeEvent" msprop:Generator_RowClassName="データリストRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="名前" msprop:Generator_ColumnVarNameInTable="column名前" msprop:Generator_ColumnPropNameInRow="名前" msprop:Generator_ColumnPropNameInTable="名前Column" msprop:Generator_UserColumnName="名前" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="4000" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="コード" msprop:Generator_ColumnVarNameInTable="columnコード" msprop:Generator_ColumnPropNameInRow="コード" msprop:Generator_ColumnPropNameInTable="コードColumn" msprop:Generator_UserColumnName="コード" type="xs:decimal" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="オートコンプリート" msprop:Generator_TableClassName="オートコンプリートDataTable" msprop:Generator_TableVarName="tableオートコンプリート" msprop:Generator_RowChangedName="オートコンプリートRowChanged" msprop:Generator_TablePropName="オートコンプリート" msprop:Generator_RowDeletingName="オートコンプリートRowDeleting" msprop:Generator_RowChangingName="オートコンプリートRowChanging" msprop:Generator_RowEvHandlerName="オートコンプリートRowChangeEventHandler" msprop:Generator_RowDeletedName="オートコンプリートRowDeleted" msprop:Generator_RowClassName="オートコンプリートRow" msprop:Generator_UserTableName="オートコンプリート" msprop:Generator_RowEvArgName="オートコンプリートRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="入力値" msprop:Generator_ColumnVarNameInTable="column入力値" msprop:Generator_ColumnPropNameInRow="入力値" msprop:Generator_ColumnPropNameInTable="入力値Column" msprop:Generator_UserColumnName="入力値">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="4000" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="リスト" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnリスト" msprop:Generator_ColumnPropNameInRow="リスト" msprop:Generator_ColumnPropNameInTable="リストColumn" msprop:Generator_UserColumnName="リスト" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="4000" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="M工事" msprop:Generator_TableClassName="M工事DataTable" msprop:Generator_TableVarName="tableM工事" msprop:Generator_RowChangedName="M工事RowChanged" msprop:Generator_TablePropName="M工事" msprop:Generator_RowDeletingName="M工事RowDeleting" msprop:Generator_RowChangingName="M工事RowChanging" msprop:Generator_RowEvHandlerName="M工事RowChangeEventHandler" msprop:Generator_RowDeletedName="M工事RowDeleted" msprop:Generator_RowClassName="M工事Row" msprop:Generator_UserTableName="M工事" msprop:Generator_RowEvArgName="M工事RowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="工事内部コード" msprop:Generator_ColumnVarNameInTable="column工事内部コード" msprop:Generator_ColumnPropNameInRow="工事内部コード" msprop:Generator_ColumnPropNameInTable="工事内部コードColumn" msprop:Generator_UserColumnName="工事内部コード">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="得意先コード" msprop:Generator_ColumnVarNameInTable="column得意先コード" msprop:Generator_ColumnPropNameInRow="得意先コード" msprop:Generator_ColumnPropNameInTable="得意先コードColumn" msprop:Generator_UserColumnName="得意先コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="工事コード" msprop:Generator_ColumnVarNameInTable="column工事コード" msprop:Generator_ColumnPropNameInRow="工事コード" msprop:Generator_ColumnPropNameInTable="工事コードColumn" msprop:Generator_UserColumnName="工事コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="工事名" msprop:Generator_ColumnVarNameInTable="column工事名" msprop:Generator_ColumnPropNameInRow="工事名" msprop:Generator_ColumnPropNameInTable="工事名Column" msprop:Generator_UserColumnName="工事名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="工事名かな" msprop:Generator_ColumnVarNameInTable="column工事名かな" msprop:Generator_ColumnPropNameInRow="工事名かな" msprop:Generator_ColumnPropNameInTable="工事名かなColumn" msprop:Generator_UserColumnName="工事名かな" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="完了区分" msprop:Generator_ColumnVarNameInTable="column完了区分" msprop:Generator_ColumnPropNameInRow="完了区分" msprop:Generator_ColumnPropNameInTable="完了区分Column" msprop:Generator_UserColumnName="完了区分" type="xs:short" minOccurs="0" />
              <xs:element name="住所" msprop:Generator_ColumnVarNameInTable="column住所" msprop:Generator_ColumnPropNameInRow="住所" msprop:Generator_ColumnPropNameInTable="住所Column" msprop:Generator_UserColumnName="住所" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="摘要1" msprop:Generator_ColumnVarNameInTable="column摘要1" msprop:Generator_ColumnPropNameInRow="摘要1" msprop:Generator_ColumnPropNameInTable="摘要1Column" msprop:Generator_UserColumnName="摘要1" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="摘要2" msprop:Generator_ColumnVarNameInTable="column摘要2" msprop:Generator_ColumnPropNameInRow="摘要2" msprop:Generator_ColumnPropNameInTable="摘要2Column" msprop:Generator_UserColumnName="摘要2" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="削除区分" msprop:Generator_ColumnVarNameInTable="column削除区分" msprop:Generator_ColumnPropNameInRow="削除区分" msprop:Generator_ColumnPropNameInTable="削除区分Column" msprop:Generator_UserColumnName="削除区分" type="xs:short" minOccurs="0" />
              <xs:element name="登録日時" msprop:Generator_ColumnVarNameInTable="column登録日時" msprop:Generator_ColumnPropNameInRow="登録日時" msprop:Generator_ColumnPropNameInTable="登録日時Column" msprop:Generator_UserColumnName="登録日時" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="登録者ID" msprop:Generator_ColumnVarNameInTable="column登録者ID" msprop:Generator_ColumnPropNameInRow="登録者ID" msprop:Generator_ColumnPropNameInTable="登録者IDColumn" msprop:Generator_UserColumnName="登録者ID" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="更新日時" msprop:Generator_ColumnVarNameInTable="column更新日時" msprop:Generator_ColumnPropNameInRow="更新日時" msprop:Generator_ColumnPropNameInTable="更新日時Column" msprop:Generator_UserColumnName="更新日時" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="更新者ID" msprop:Generator_ColumnVarNameInTable="column更新者ID" msprop:Generator_ColumnPropNameInRow="更新者ID" msprop:Generator_ColumnPropNameInTable="更新者IDColumn" msprop:Generator_UserColumnName="更新者ID" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="M商品" msprop:Generator_TableClassName="M商品DataTable" msprop:Generator_TableVarName="tableM商品" msprop:Generator_RowChangedName="M商品RowChanged" msprop:Generator_TablePropName="M商品" msprop:Generator_RowDeletingName="M商品RowDeleting" msprop:Generator_RowChangingName="M商品RowChanging" msprop:Generator_RowEvHandlerName="M商品RowChangeEventHandler" msprop:Generator_RowDeletedName="M商品RowDeleted" msprop:Generator_RowClassName="M商品Row" msprop:Generator_UserTableName="M商品" msprop:Generator_RowEvArgName="M商品RowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="商品コード" msprop:Generator_ColumnVarNameInTable="column商品コード" msprop:Generator_ColumnPropNameInRow="商品コード" msprop:Generator_ColumnPropNameInTable="商品コードColumn" msprop:Generator_UserColumnName="商品コード">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="商品名" msprop:Generator_ColumnVarNameInTable="column商品名" msprop:Generator_ColumnPropNameInRow="商品名" msprop:Generator_ColumnPropNameInTable="商品名Column" msprop:Generator_UserColumnName="商品名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="商品名かな" msprop:Generator_ColumnVarNameInTable="column商品名かな" msprop:Generator_ColumnPropNameInRow="商品名かな" msprop:Generator_ColumnPropNameInTable="商品名かなColumn" msprop:Generator_UserColumnName="商品名かな" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="単位区分" msprop:Generator_ColumnVarNameInTable="column単位区分" msprop:Generator_ColumnPropNameInRow="単位区分" msprop:Generator_ColumnPropNameInTable="単位区分Column" msprop:Generator_UserColumnName="単位区分" type="xs:short" minOccurs="0" />
              <xs:element name="基本料金" msprop:Generator_ColumnVarNameInTable="column基本料金" msprop:Generator_ColumnPropNameInRow="基本料金" msprop:Generator_ColumnPropNameInTable="基本料金Column" msprop:Generator_UserColumnName="基本料金" type="xs:decimal" minOccurs="0" />
              <xs:element name="単価" msprop:Generator_ColumnVarNameInTable="column単価" msprop:Generator_ColumnPropNameInRow="単価" msprop:Generator_ColumnPropNameInTable="単価Column" msprop:Generator_UserColumnName="単価" type="xs:decimal" minOccurs="0" />
              <xs:element name="商品分類区分" msprop:Generator_ColumnVarNameInTable="column商品分類区分" msprop:Generator_ColumnPropNameInRow="商品分類区分" msprop:Generator_ColumnPropNameInTable="商品分類区分Column" msprop:Generator_UserColumnName="商品分類区分" type="xs:short" minOccurs="0" />
              <xs:element name="備考" msprop:Generator_ColumnVarNameInTable="column備考" msprop:Generator_ColumnPropNameInRow="備考" msprop:Generator_ColumnPropNameInTable="備考Column" msprop:Generator_UserColumnName="備考" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="保証日数" msprop:Generator_ColumnVarNameInTable="column保証日数" msprop:Generator_ColumnPropNameInRow="保証日数" msprop:Generator_ColumnPropNameInTable="保証日数Column" msprop:Generator_UserColumnName="保証日数" type="xs:decimal" minOccurs="0" />
              <xs:element name="登録日時" msprop:Generator_ColumnVarNameInTable="column登録日時" msprop:Generator_ColumnPropNameInRow="登録日時" msprop:Generator_ColumnPropNameInTable="登録日時Column" msprop:Generator_UserColumnName="登録日時" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="登録者ID" msprop:Generator_ColumnVarNameInTable="column登録者ID" msprop:Generator_ColumnPropNameInRow="登録者ID" msprop:Generator_ColumnPropNameInTable="登録者IDColumn" msprop:Generator_UserColumnName="登録者ID" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="更新日時" msprop:Generator_ColumnVarNameInTable="column更新日時" msprop:Generator_ColumnPropNameInRow="更新日時" msprop:Generator_ColumnPropNameInTable="更新日時Column" msprop:Generator_UserColumnName="更新日時" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="更新者ID" msprop:Generator_ColumnVarNameInTable="column更新者ID" msprop:Generator_ColumnPropNameInRow="更新者ID" msprop:Generator_ColumnPropNameInTable="更新者IDColumn" msprop:Generator_UserColumnName="更新者ID" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="M得意先" msprop:Generator_TableClassName="M得意先DataTable" msprop:Generator_TableVarName="tableM得意先" msprop:Generator_TablePropName="M得意先" msprop:Generator_RowDeletingName="M得意先RowDeleting" msprop:Generator_RowChangingName="M得意先RowChanging" msprop:Generator_RowEvHandlerName="M得意先RowChangeEventHandler" msprop:Generator_RowDeletedName="M得意先RowDeleted" msprop:Generator_UserTableName="M得意先" msprop:Generator_RowChangedName="M得意先RowChanged" msprop:Generator_RowEvArgName="M得意先RowChangeEvent" msprop:Generator_RowClassName="M得意先Row">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="得意先コード" msprop:Generator_ColumnVarNameInTable="column得意先コード" msprop:Generator_ColumnPropNameInRow="得意先コード" msprop:Generator_ColumnPropNameInTable="得意先コードColumn" msprop:Generator_UserColumnName="得意先コード">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="得意先名" msprop:Generator_ColumnVarNameInTable="column得意先名" msprop:Generator_ColumnPropNameInRow="得意先名" msprop:Generator_ColumnPropNameInTable="得意先名Column" msprop:Generator_UserColumnName="得意先名" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="得意先かな" msprop:Generator_ColumnVarNameInTable="column得意先かな" msprop:Generator_ColumnPropNameInRow="得意先かな" msprop:Generator_ColumnPropNameInTable="得意先かなColumn" msprop:Generator_UserColumnName="得意先かな" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="郵便番号" msprop:Generator_ColumnVarNameInTable="column郵便番号" msprop:Generator_ColumnPropNameInRow="郵便番号" msprop:Generator_ColumnPropNameInTable="郵便番号Column" msprop:Generator_UserColumnName="郵便番号" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="住所1" msprop:Generator_ColumnVarNameInTable="column住所1" msprop:Generator_ColumnPropNameInRow="住所1" msprop:Generator_ColumnPropNameInTable="住所1Column" msprop:Generator_UserColumnName="住所1" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="住所2" msprop:Generator_ColumnVarNameInTable="column住所2" msprop:Generator_ColumnPropNameInRow="住所2" msprop:Generator_ColumnPropNameInTable="住所2Column" msprop:Generator_UserColumnName="住所2" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="電話番号" msprop:Generator_ColumnVarNameInTable="column電話番号" msprop:Generator_ColumnPropNameInRow="電話番号" msprop:Generator_ColumnPropNameInTable="電話番号Column" msprop:Generator_UserColumnName="電話番号" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="FAX番号" msprop:Generator_ColumnVarNameInTable="columnFAX番号" msprop:Generator_ColumnPropNameInRow="FAX番号" msprop:Generator_ColumnPropNameInTable="FAX番号Column" msprop:Generator_UserColumnName="FAX番号" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="締日区分" msprop:Generator_ColumnVarNameInTable="column締日区分" msprop:Generator_ColumnPropNameInRow="締日区分" msprop:Generator_ColumnPropNameInTable="締日区分Column" msprop:Generator_UserColumnName="締日区分" type="xs:short" minOccurs="0" />
              <xs:element name="端数区分" msprop:Generator_ColumnVarNameInTable="column端数区分" msprop:Generator_ColumnPropNameInRow="端数区分" msprop:Generator_ColumnPropNameInTable="端数区分Column" msprop:Generator_UserColumnName="端数区分" type="xs:short" minOccurs="0" />
              <xs:element name="請求書区分" msprop:Generator_ColumnVarNameInTable="column請求書区分" msprop:Generator_ColumnPropNameInRow="請求書区分" msprop:Generator_ColumnPropNameInTable="請求書区分Column" msprop:Generator_UserColumnName="請求書区分" type="xs:short" minOccurs="0" />
              <xs:element name="担当者コード" msprop:Generator_ColumnVarNameInTable="column担当者コード" msprop:Generator_ColumnPropNameInRow="担当者コード" msprop:Generator_ColumnPropNameInTable="担当者コードColumn" msprop:Generator_UserColumnName="担当者コード" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="諸口区分" msprop:Generator_ColumnVarNameInTable="column諸口区分" msprop:Generator_ColumnPropNameInRow="諸口区分" msprop:Generator_ColumnPropNameInTable="諸口区分Column" msprop:Generator_UserColumnName="諸口区分" type="xs:short" minOccurs="0" />
              <xs:element name="消費税区分" msprop:Generator_ColumnVarNameInTable="column消費税区分" msprop:Generator_ColumnPropNameInRow="消費税区分" msprop:Generator_ColumnPropNameInTable="消費税区分Column" msprop:Generator_UserColumnName="消費税区分" type="xs:short" minOccurs="0" />
              <xs:element name="登録日時" msprop:Generator_ColumnVarNameInTable="column登録日時" msprop:Generator_ColumnPropNameInRow="登録日時" msprop:Generator_ColumnPropNameInTable="登録日時Column" msprop:Generator_UserColumnName="登録日時" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="登録者ID" msprop:Generator_ColumnVarNameInTable="column登録者ID" msprop:Generator_ColumnPropNameInRow="登録者ID" msprop:Generator_ColumnPropNameInTable="登録者IDColumn" msprop:Generator_UserColumnName="登録者ID" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="更新日時" msprop:Generator_ColumnVarNameInTable="column更新日時" msprop:Generator_ColumnPropNameInRow="更新日時" msprop:Generator_ColumnPropNameInTable="更新日時Column" msprop:Generator_UserColumnName="更新日時" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="更新者ID" msprop:Generator_ColumnVarNameInTable="column更新者ID" msprop:Generator_ColumnPropNameInRow="更新者ID" msprop:Generator_ColumnPropNameInTable="更新者IDColumn" msprop:Generator_UserColumnName="更新者ID" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
    <xs:unique name="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:データリスト" />
      <xs:field xpath="mstns:コード" />
    </xs:unique>
    <xs:unique name="M工事_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:M工事" />
      <xs:field xpath="mstns:工事内部コード" />
    </xs:unique>
    <xs:unique name="M商品_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:M商品" />
      <xs:field xpath="mstns:商品コード" />
    </xs:unique>
    <xs:unique name="M得意先_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:M得意先" />
      <xs:field xpath="mstns:得意先コード" />
    </xs:unique>
  </xs:element>
</xs:schema>