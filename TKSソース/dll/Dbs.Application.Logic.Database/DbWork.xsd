﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="DbWork" targetNamespace="http://tempuri.org/DbWork.xsd" xmlns:mstns="http://tempuri.org/DbWork.xsd" xmlns="http://tempuri.org/DbWork.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections>
          <Connection AppSettingsObjectName="MySettings" AppSettingsPropertyName="ConnectionString" ConnectionStringObject="" IsAppSettingsProperty="true" Modifier="Assembly" Name="ConnectionString (MySettings)" ParameterPrefix="@" PropertyReference="ApplicationSettings.Dbs.Application.Logic.Database.My.MySettings.GlobalReference.Default.ConnectionString" Provider="System.Data.SqlClient" />
        </Connections>
        <Tables>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="W売上選択TableAdapter" GeneratorDataComponentClassName="W売上選択TableAdapter" Name="W売上選択" UserDataComponentName="W売上選択TableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="TokoDB.dbo.W売上選択" DbObjectType="Table" GenerateMethods="Get" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="GetData">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [W売上選択] WHERE (([ID] = @Original_ID) AND ((@IsNull_セッションID = 1 AND [セッションID] IS NULL) OR ([セッションID] = @Original_セッションID)) AND ((@IsNull_売上番号 = 1 AND [売上番号] IS NULL) OR ([売上番号] = @Original_売上番号)) AND ((@IsNull_行番号 = 1 AND [行番号] IS NULL) OR ([行番号] = @Original_行番号)))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_ID" Precision="18" ProviderType="Decimal" Scale="0" Size="0" SourceColumn="ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_セッションID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="セッションID" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_セッションID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="セッションID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_売上番号" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="売上番号" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_売上番号" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="売上番号" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_行番号" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="行番号" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_行番号" Precision="3" ProviderType="Decimal" Scale="0" Size="0" SourceColumn="行番号" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [W売上選択] ([セッションID], [売上番号], [行番号]) VALUES (@セッションID, @売上番号, @行番号);
SELECT ID, セッションID, 売上番号, 行番号 FROM W売上選択 WHERE (ID = SCOPE_IDENTITY())</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@セッションID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="セッションID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@売上番号" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="売上番号" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@行番号" Precision="3" ProviderType="Decimal" Scale="0" Size="0" SourceColumn="行番号" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT                      ID, セッションID, 売上番号, 行番号
FROM                         W売上選択
WHERE                       (セッションID = @セッションID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="セッションID" ColumnName="セッションID" DataSourceName="TokoDB.dbo.W売上選択" DataTypeServer="varchar(200)" DbType="AnsiString" Direction="Input" ParameterName="@セッションID" Precision="0" ProviderType="VarChar" Scale="0" Size="200" SourceColumn="セッションID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [W売上選択] SET [セッションID] = @セッションID, [売上番号] = @売上番号, [行番号] = @行番号 WHERE (([ID] = @Original_ID) AND ((@IsNull_セッションID = 1 AND [セッションID] IS NULL) OR ([セッションID] = @Original_セッションID)) AND ((@IsNull_売上番号 = 1 AND [売上番号] IS NULL) OR ([売上番号] = @Original_売上番号)) AND ((@IsNull_行番号 = 1 AND [行番号] IS NULL) OR ([行番号] = @Original_行番号)));
SELECT ID, セッションID, 売上番号, 行番号 FROM W売上選択 WHERE (ID = @ID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@セッションID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="セッションID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@売上番号" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="売上番号" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@行番号" Precision="3" ProviderType="Decimal" Scale="0" Size="0" SourceColumn="行番号" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_ID" Precision="18" ProviderType="Decimal" Scale="0" Size="0" SourceColumn="ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_セッションID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="セッションID" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_セッションID" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="セッションID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_売上番号" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="売上番号" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_売上番号" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="売上番号" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_行番号" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="行番号" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_行番号" Precision="3" ProviderType="Decimal" Scale="0" Size="0" SourceColumn="行番号" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ID" ColumnName="ID" DataSourceName="TokoDB.dbo.W売上選択" DataTypeServer="numeric" DbType="Decimal" Direction="Input" ParameterName="@ID" Precision="18" ProviderType="Decimal" Scale="0" Size="9" SourceColumn="ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="ID" DataSetColumn="ID" />
              <Mapping SourceColumn="セッションID" DataSetColumn="セッションID" />
              <Mapping SourceColumn="売上番号" DataSetColumn="売上番号" />
              <Mapping SourceColumn="行番号" DataSetColumn="行番号" />
            </Mappings>
            <Sources>
              <DbSource ConnectionRef="ConnectionString (MySettings)" DbObjectName="" DbObjectType="Unknown" GenerateShortCommands="true" GeneratorSourceName="DeleteBySessionID" Modifier="Public" Name="DeleteBySessionID" QueryType="NoData" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetDataBy" UserSourceName="DeleteBySessionID">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>DELETE FROM [W売上選択] WHERE セッションID = @セッションID</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="セッションID" ColumnName="セッションID" DataSourceName="TokoDB.dbo.W売上選択" DataTypeServer="varchar(200)" DbType="AnsiString" Direction="Input" ParameterName="@セッションID" Precision="0" ProviderType="VarChar" Scale="0" Size="200" SourceColumn="セッションID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
              </DbSource>
            </Sources>
          </TableAdapter>
        </Tables>
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="DbWork" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:EnableTableAdapterManager="true" msprop:Generator_DataSetName="DbWork" msprop:Generator_UserDSName="DbWork">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="W売上選択" msprop:Generator_TableClassName="W売上選択DataTable" msprop:Generator_TableVarName="tableW売上選択" msprop:Generator_TablePropName="W売上選択" msprop:Generator_RowDeletingName="W売上選択RowDeleting" msprop:Generator_RowChangingName="W売上選択RowChanging" msprop:Generator_RowEvHandlerName="W売上選択RowChangeEventHandler" msprop:Generator_RowDeletedName="W売上選択RowDeleted" msprop:Generator_UserTableName="W売上選択" msprop:Generator_RowChangedName="W売上選択RowChanged" msprop:Generator_RowEvArgName="W売上選択RowChangeEvent" msprop:Generator_RowClassName="W売上選択Row">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ID" msdata:ReadOnly="true" msdata:AutoIncrement="true" msdata:AutoIncrementSeed="-1" msdata:AutoIncrementStep="-1" msprop:Generator_ColumnVarNameInTable="columnID" msprop:Generator_ColumnPropNameInRow="ID" msprop:Generator_ColumnPropNameInTable="IDColumn" msprop:Generator_UserColumnName="ID" type="xs:decimal" />
              <xs:element name="セッションID" msprop:Generator_ColumnVarNameInTable="columnセッションID" msprop:Generator_ColumnPropNameInRow="セッションID" msprop:Generator_ColumnPropNameInTable="セッションIDColumn" msprop:Generator_UserColumnName="セッションID" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="売上番号" msprop:Generator_ColumnVarNameInTable="column売上番号" msprop:Generator_ColumnPropNameInRow="売上番号" msprop:Generator_ColumnPropNameInTable="売上番号Column" msprop:Generator_UserColumnName="売上番号" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="行番号" msprop:Generator_ColumnVarNameInTable="column行番号" msprop:Generator_ColumnPropNameInRow="行番号" msprop:Generator_ColumnPropNameInTable="行番号Column" msprop:Generator_UserColumnName="行番号" type="xs:decimal" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
    <xs:unique name="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:W売上選択" />
      <xs:field xpath="mstns:ID" />
    </xs:unique>
  </xs:element>
</xs:schema>