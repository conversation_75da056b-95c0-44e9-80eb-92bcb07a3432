﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{2937E36E-94A5-4D96-B769-22FC2575D863}</ProjectGuid>
    <OutputType>Library</OutputType>
    <RootNamespace>Dbs.Application.Logic</RootNamespace>
    <AssemblyName>Dbs.Application.Logic</AssemblyName>
    <FileAlignment>512</FileAlignment>
    <MyType>Windows</MyType>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <Deterministic>true</Deterministic>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <OutputPath>..\..\web\bin\</OutputPath>
    <DocumentationFile>Dbs.Application.Logic.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <Optimize>true</Optimize>
    <OutputPath>..\..\web\bin\</OutputPath>
    <DocumentationFile>Dbs.Application.Logic.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
  </PropertyGroup>
  <PropertyGroup>
    <OptionExplicit>On</OptionExplicit>
  </PropertyGroup>
  <PropertyGroup>
    <OptionCompare>Binary</OptionCompare>
  </PropertyGroup>
  <PropertyGroup>
    <OptionStrict>Off</OptionStrict>
  </PropertyGroup>
  <PropertyGroup>
    <OptionInfer>On</OptionInfer>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Dbs.Asphalt.Core, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\web\bin\Dbs.Asphalt.Core.dll</HintPath>
    </Reference>
    <Reference Include="Dbs.Asphalt.Database">
      <HintPath>..\..\web\bin\Dbs.Asphalt.Database.dll</HintPath>
    </Reference>
    <Reference Include="GrapeCity.ActiveReports.Chart.v12, Version=12.4.18445.1, Culture=neutral, PublicKeyToken=cc4967777c49a3ff, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\web\bin\GrapeCity.ActiveReports.Chart.v12.dll</HintPath>
    </Reference>
    <Reference Include="GrapeCity.ActiveReports.Design.Win.v12, Version=12.4.18445.1, Culture=neutral, PublicKeyToken=cc4967777c49a3ff, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\web\bin\GrapeCity.ActiveReports.Design.Win.v12.dll</HintPath>
    </Reference>
    <Reference Include="GrapeCity.ActiveReports.Diagnostics.v12, Version=12.4.18445.1, Culture=neutral, PublicKeyToken=cc4967777c49a3ff, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\web\bin\GrapeCity.ActiveReports.Diagnostics.v12.dll</HintPath>
    </Reference>
    <Reference Include="GrapeCity.ActiveReports.Document.v12, Version=12.4.18445.1, Culture=neutral, PublicKeyToken=cc4967777c49a3ff, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\web\bin\GrapeCity.ActiveReports.Document.v12.dll</HintPath>
    </Reference>
    <Reference Include="GrapeCity.ActiveReports.Export.Document.v12, Version=12.4.18445.1, Culture=neutral, PublicKeyToken=cc4967777c49a3ff, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\web\bin\GrapeCity.ActiveReports.Export.Document.v12.dll</HintPath>
    </Reference>
    <Reference Include="GrapeCity.ActiveReports.Export.Excel.v12, Version=12.4.18445.1, Culture=neutral, PublicKeyToken=cc4967777c49a3ff, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\web\bin\GrapeCity.ActiveReports.Export.Excel.v12.dll</HintPath>
    </Reference>
    <Reference Include="GrapeCity.ActiveReports.Export.Html.v12, Version=12.4.18445.1, Culture=neutral, PublicKeyToken=cc4967777c49a3ff, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\web\bin\GrapeCity.ActiveReports.Export.Html.v12.dll</HintPath>
    </Reference>
    <Reference Include="GrapeCity.ActiveReports.Export.Image.Unsafe.v12, Version=12.4.18445.1, Culture=neutral, PublicKeyToken=cc4967777c49a3ff, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\web\bin\GrapeCity.ActiveReports.Export.Image.Unsafe.v12.dll</HintPath>
    </Reference>
    <Reference Include="GrapeCity.ActiveReports.Export.Image.v12, Version=12.4.18445.1, Culture=neutral, PublicKeyToken=cc4967777c49a3ff, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\web\bin\GrapeCity.ActiveReports.Export.Image.v12.dll</HintPath>
    </Reference>
    <Reference Include="GrapeCity.ActiveReports.Export.Pdf.v12, Version=12.4.18445.1, Culture=neutral, PublicKeyToken=cc4967777c49a3ff, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\web\bin\GrapeCity.ActiveReports.Export.Pdf.v12.dll</HintPath>
    </Reference>
    <Reference Include="GrapeCity.ActiveReports.Export.Rdf.v12, Version=12.4.18445.1, Culture=neutral, PublicKeyToken=cc4967777c49a3ff, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\web\bin\GrapeCity.ActiveReports.Export.Rdf.v12.dll</HintPath>
    </Reference>
    <Reference Include="GrapeCity.ActiveReports.Export.Xaml.v12, Version=12.4.18445.1, Culture=neutral, PublicKeyToken=cc4967777c49a3ff, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\web\bin\GrapeCity.ActiveReports.Export.Xaml.v12.dll</HintPath>
    </Reference>
    <Reference Include="GrapeCity.ActiveReports.Extensibility.v12, Version=12.4.18445.1, Culture=neutral, PublicKeyToken=cc4967777c49a3ff, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\web\bin\GrapeCity.ActiveReports.Extensibility.v12.dll</HintPath>
    </Reference>
    <Reference Include="GrapeCity.ActiveReports.Interop.v12, Version=12.4.18445.1, Culture=neutral, PublicKeyToken=cc4967777c49a3ff, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\web\bin\GrapeCity.ActiveReports.Interop.v12.dll</HintPath>
    </Reference>
    <Reference Include="GrapeCity.ActiveReports.v12, Version=12.4.18445.1, Culture=neutral, PublicKeyToken=cc4967777c49a3ff, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\web\bin\GrapeCity.ActiveReports.v12.dll</HintPath>
    </Reference>
    <Reference Include="GrapeCity.ActiveReports.Viewer.Win.v12, Version=12.4.18445.1, Culture=neutral, PublicKeyToken=cc4967777c49a3ff, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\web\bin\GrapeCity.ActiveReports.Viewer.Win.v12.dll</HintPath>
    </Reference>
    <Reference Include="GrapeCity.ActiveReports.Web.v12, Version=12.4.18445.1, Culture=neutral, PublicKeyToken=cc4967777c49a3ff, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\web\bin\GrapeCity.ActiveReports.Web.v12.dll</HintPath>
    </Reference>
    <Reference Include="SpreadsheetGear2017.Core, Version=8.3.5.102, Culture=neutral, PublicKeyToken=39c186f5904944ec, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\web\bin\SpreadsheetGear2017.Core.dll</HintPath>
    </Reference>
    <Reference Include="SpreadsheetGear2017.Drawing, Version=8.3.5.102, Culture=neutral, PublicKeyToken=39c186f5904944ec, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\web\bin\SpreadsheetGear2017.Drawing.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Transactions" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Net.Http" />
  </ItemGroup>
  <ItemGroup>
    <Import Include="Microsoft.VisualBasic" />
    <Import Include="System" />
    <Import Include="System.Collections" />
    <Import Include="System.Collections.Generic" />
    <Import Include="System.Data" />
    <Import Include="System.Diagnostics" />
    <Import Include="System.Linq" />
    <Import Include="System.Xml.Linq" />
    <Import Include="System.Threading.Tasks" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Api\TokuisakiMasterApiManager.vb" />
    <Compile Include="Logic\Anytime\KoujiListManager.vb" />
    <Compile Include="Logic\Anytime\LeaseListManager.vb" />
    <Compile Include="Logic\Anytime\UriageTeikiManager.vb" />
    <Compile Include="Logic\Bill\SeikyulistManager.vb" />
    <Compile Include="Logic\Bill\SeikyuPrintManager.vb" />
    <Compile Include="Logic\Bill\SeikyuKeisanManager.vb" />
    <Compile Include="Logic\Daily\NyukinDenpyoPrintManager.vb" />
    <Compile Include="Logic\Daily\UriageNohinshoManager.vb" />
    <Compile Include="Logic\Daily\UriageDenpyoPrintManager.vb" />
    <Compile Include="Logic\Daily\UriageDenpyoManager.vb" />
    <Compile Include="Logic\Daily\NyukinDenpyoManager.vb" />
    <Compile Include="Logic\Master\KoujiMasterManager.vb" />
    <Compile Include="Logic\Master\ShouhinMasterManager.vb" />
    <Compile Include="Logic\Master\TokuisakiMasterManager.vb" />
    <Compile Include="Logic\Master\ZeirituMasterManager.vb" />
    <Compile Include="My Project\AssemblyInfo.vb" />
    <Compile Include="My Project\Application.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Application.myapp</DependentUpon>
    </Compile>
    <Compile Include="My Project\Resources.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="My Project\Settings.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <Compile Include="Report\Bill\SeikyushoMeisai.Designer.vb">
      <DependentUpon>SeikyushoMeisai.vb</DependentUpon>
    </Compile>
    <Compile Include="Report\Bill\SeikyushoMeisai.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Report\Bill\SeikyushoKagami.Designer.vb">
      <DependentUpon>SeikyushoKagami.vb</DependentUpon>
    </Compile>
    <Compile Include="Report\Bill\SeikyushoKagami.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Report\Bill\SeikyuList.Designer.vb">
      <DependentUpon>SeikyuList.vb</DependentUpon>
    </Compile>
    <Compile Include="Report\Bill\SeikyuList.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Report\Daily\UriageDenpyoList.Designer.vb">
      <DependentUpon>UriageDenpyoList.vb</DependentUpon>
    </Compile>
    <Compile Include="Report\Daily\UriageDenpyoList.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Report\Daily\UriageNohinshoBase.Designer.vb">
      <DependentUpon>UriageNohinshoBase.vb</DependentUpon>
    </Compile>
    <Compile Include="Report\Daily\UriageNohinshoBase.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Report\Daily\UriageNohinshoSub.Designer.vb">
      <DependentUpon>UriageNohinshoSub.vb</DependentUpon>
    </Compile>
    <Compile Include="Report\Daily\UriageNohinshoSub.vb">
      <SubType>Component</SubType>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="My Project\licenses.licx" />
    <EmbeddedResource Include="My Project\Resources.resx">
      <Generator>VbMyResourcesResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.vb</LastGenOutput>
      <CustomToolNamespace>My.Resources</CustomToolNamespace>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Report\Bill\SeikyushoMeisai.resx">
      <DependentUpon>SeikyushoMeisai.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Report\Bill\SeikyushoKagami.resx">
      <DependentUpon>SeikyushoKagami.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Report\Bill\SeikyuList.resx">
      <DependentUpon>SeikyuList.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Report\Daily\UriageDenpyoList.resx">
      <DependentUpon>UriageDenpyoList.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Report\Daily\UriageNohinshoBase.resx">
      <DependentUpon>UriageNohinshoBase.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Report\Daily\UriageNohinshoSub.resx">
      <DependentUpon>UriageNohinshoSub.vb</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="My Project\Application.myapp">
      <Generator>MyApplicationCodeGenerator</Generator>
      <LastGenOutput>Application.Designer.vb</LastGenOutput>
    </None>
    <None Include="My Project\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <CustomToolNamespace>My</CustomToolNamespace>
      <LastGenOutput>Settings.Designer.vb</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Dbs.Application.Logic.Database\Dbs.Application.Logic.Database.vbproj">
      <Project>{a11a29b4-df9c-4925-9a5b-fca40a27219a}</Project>
      <Name>Dbs.Application.Logic.Database</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.VisualBasic.targets" />
</Project>