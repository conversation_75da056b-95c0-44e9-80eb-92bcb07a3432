﻿Imports System.ComponentModel
Imports System.Reflection
Imports Dbs.Asphalt.Database
Imports Dbs.Asphalt.Core.Common
Imports Dbs.Asphalt.Core.BaseClass
Imports Dbs.Asphalt.Core.SystemLogic

Namespace Api

    ''' <summary>
    ''' サンプルAPIマネージャ
    ''' </summary>
    Public Class TokuisakiMasterApiManager
        Inherits ApiBase

        Sub New(security As Security)
            MyBase.New(security)
        End Sub

        Public Overrides Function Read(requestdata As Object, ByRef responsedata As Object) As Boolean
            Dim logic As New Logic.Master.TokuisakiMasterManager(Security)
            logic.Start()
            ' リクエストデータの"得意先コード"をセット
            logic.得意先コード.Value = requestdata("得意先コード")
            ' 得意先マスターを読み込み
            If Not logic.ReadMainData(True) Then
                LastError = logic.LastError
                Return False
            End If
            ' レコードが存在しない場合はエラー
            If logic.EditMode.Value <> Field.EditModeContents.emUpdateMode Then
                LastError = Message.MessageText("EM_NODATA")
                Return False
            End If
            ' 各項目をレスポンスデータにセット
            responsedata("得意先名") = logic.得意先名.Value
            responsedata("郵便番号") = logic.郵便番号.Value
            responsedata("住所1") = logic.住所1.Value
            responsedata("住所2") = logic.住所2.Value
            responsedata("電話番号") = logic.電話番号.Value
            responsedata("FAX番号") = logic.FAX番号.Value
            Return True
        End Function

        Public Overrides Function Write(requestdata As Object, ByRef responsedata As Object) As Boolean
            Throw New NotImplementedException()
        End Function

        Public Overrides Function Delete(requestdata As Object, ByRef responsedata As Object) As Boolean
            Throw New NotImplementedException()
        End Function

        Public Overrides Function Print(requestdata As Object, ByRef responsedata As Object) As Boolean
            Throw New NotImplementedException()
        End Function

        Public Overrides Function OtherMethod(method As String, requestdata As Object, ByRef responsedata As Object) As Boolean
            Throw New NotImplementedException()
        End Function
    End Class

End Namespace
