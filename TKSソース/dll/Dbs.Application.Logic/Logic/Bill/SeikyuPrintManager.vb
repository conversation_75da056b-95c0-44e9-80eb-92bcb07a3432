﻿Imports System
Imports System.Text
Imports Dbs.Asphalt.Core
Imports Dbs.Asphalt.Core.BaseClass
Imports Dbs.Asphalt.Core.SystemLogic
Imports Dbs.Asphalt.Core.Common
Imports Dbs.Asphalt.Database
Imports Dbs.Application.Logic.Database

Namespace Logic.bill

    ''' <summary>
    ''' 印刷処理サンプルマネージャ
    ''' (UIからの使用方法はweb/extension/ExtensionSample1.ascxを参照)
    ''' </summary>
    Public Class SeikyuPrintManager
        Inherits PrinterLogicBase

        Public 締日区分 As New Field.ItemData("締日区分", "", Field.ItemType.DataItem, True, "締日区分", False)
        Public 請求年月 As New Field.ItemData("請求年月", "", Field.ItemType.DataItem, True, 7,, Text.FormatContents.tbDateYM)
        Public 得意先コードF As New Field.ItemData("得意先コード自", "", Field.ItemType.DataItem, False, 6,, Text.FormatContents.tbCode)
        Public 得意先コードT As New Field.ItemData("得意先コード至", "", Field.ItemType.DataItem, False, 6,, Text.FormatContents.tbCode)
        Public 選択リスト As New Field.ItemData("選択リスト", "", Field.ItemType.DataItem, False, 4000,, Text.FormatContents.tbNone)

        Public 請求書帳票区分 As New Field.ItemData("請求書帳票区分", 1, Field.ItemType.DataItem, True, "請求書帳票区分", False)
        Public 控フラグ As New Field.ItemData("控フラグ", "", Field.ItemType.DataItem, False, 1)

        Sub New(security As Security)
            MyBase.New(security)
        End Sub

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' フィールドの検証処理をを記述します。
        ''' フィールドの検証処理。基底クラスにて、WriteMainDataSubが呼ばれる前にコールされます。
        ''' 基本的な検証(文字数・書式・区分値)は基底クラスで検証されます。
        ''' </summary>
        '''------------------------------------------------------------------------------
        Protected Overrides Sub EnterValidateSub(ByRef chk As Validator)

            '----検証サンプル--------------------------------------------------------------
            ' コードの開始終了の範囲チェック
            'chk.FromTo(搬入日付F, 搬入日付T)
            '----検証サンプル--------------------------------------------------------------

            If chk.Empty(選択リスト) Then
                chk.AnyCheck(選択リスト, True, "一件も選択されていません")
            End If

        End Sub

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' 印刷データの作成処理を記述します。
        ''' </summary>
        ''' <param name="reportfilename">レポートファイルの名前が渡される場合があります。</param>
        '''------------------------------------------------------------------------------
        Protected Overrides Function PrintMainDataSub(Optional reportfilename As String = "") As Boolean

            Dim strタイトル As String = ""
            Dim strファイル名 As String = ""

            Select Case 請求書帳票区分.Value
                Case 1    ' 鑑

                    strタイトル = "請求書鑑"
                    If 控フラグ.Value = 1 Then : strタイトル &= "(控)" : End If

                    strファイル名 = strタイトル & "_" & 請求年月.Value.ToString.Replace("/", "") & Right("00" & 締日区分.Value.ToString, 2) & Format(Now, "HHmmss")

                    ' セクションレポートの場合はインスタンス生成
                    Dim rep鑑 As New SeikyushoKagami
                    rep鑑.txt控.Visible = (控フラグ.Value = 1)

                    Using db As New DbReportTableAdapters.R請求書鑑TableAdapter
                        Using rs As DbReport.R請求書鑑DataTable = db.GetData(選択リスト.Value)
                            If rs.Count > 0 Then
                                ' 基底クラスにレポートオブジェクトとデータを渡すだけ
                                If Not MakeReport(rep鑑, strファイル名, rs.DefaultView) Then
                                    Return False
                                End If
                            Else
                                ' データがない場合はエラーメッセージをセットして戻る
                                LastError = Message.MessageText("EM_NODATA")
                                Return False
                            End If
                        End Using
                    End Using

                Case 2    ' 明細

                    strタイトル = "請求書明細"
                    If 控フラグ.Value = 1 Then : strタイトル &= "(控)" : End If

                    strファイル名 = strタイトル & "_" & 請求年月.Value.ToString.Replace("/", "") & Right("00" & 締日区分.Value.ToString, 2) & Format(Now, "HHmmss")

                    ' セクションレポートの場合はインスタンス生成
                    Dim rep明細 As New SeikyushoMeisai
                    rep明細.txt控.Visible = (控フラグ.Value = 1)

                    Using db As New DbReportTableAdapters.R請求書明細TableAdapter
                        Using rs As DbReport.R請求書明細DataTable = db.GetData(選択リスト.Value)
                            If rs.Count > 0 Then
                                ' 基底クラスにレポートオブジェクトとデータを渡すだけ
                                If Not MakeReport(rep明細, strファイル名, rs.DefaultView) Then
                                    Return False
                                End If
                            Else
                                ' データがない場合はエラーメッセージをセットして戻る
                                LastError = Message.MessageText("EM_NODATA")
                                Return False
                            End If
                        End Using
                    End Using

            End Select

            Return True
        End Function

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' 締期間の取得
        ''' </summary>
        '''------------------------------------------------------------------------------
        Public Function GetShimekikan(Shimebi_Clear As Boolean) As Boolean

            Dim dec締日 As Decimal = Text.CVal(締日区分.Value)
            If Shimebi_Clear Then
                dec締日 = 0
            End If

            Using db As New DbCalcTableAdapters.F請求締期間TableAdapter
                Using rs As DbCalc.F請求締期間DataTable = db.GetData(dec締日, 請求年月.Value)
                    If rs.Count > 0 Then
                        締日区分.Value = rs(0).締日区分
                        請求年月.Value = rs(0).請求年月
                    End If
                End Using
            End Using

            Return True
        End Function

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' 専用帳票区分
        ''' </summary>
        '''------------------------------------------------------------------------------
        Public ReadOnly Property GetDataKubun() As DataView
            Get
                Dim db As New DbCoreMasterTableAdapters.M区分TableAdapter
                Dim rs As New DbCoreMaster.M区分DataTable
                rs = db.GetData("専用帳票区分", 0)
                Return rs.DefaultView
            End Get
        End Property

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' マネージャの名称(メニューや画面タイトル、権限モジュールリストで共通使用されます)
        ''' </summary>
        '''------------------------------------------------------------------------------
        Public Overrides ReadOnly Property Title As String
            Get
                Return "請求書印刷"
            End Get
        End Property

    End Class

End Namespace
