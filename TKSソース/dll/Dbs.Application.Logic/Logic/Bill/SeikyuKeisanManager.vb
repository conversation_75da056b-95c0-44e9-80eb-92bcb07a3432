﻿Imports Dbs.Asphalt.Core
Imports Dbs.Asphalt.Core.Common
Imports Dbs.Asphalt.Core.SystemLogic
Imports Dbs.Asphalt.Core.BaseClass
Imports Dbs.Asphalt.Database
Imports Dbs.Application.Logic.Database

Namespace Logic.bill

    ''' <summary>
    ''' 登録処理サンプルマネージャ
    ''' </summary>
    Public Class SeikyuKeisanManager
        Inherits RegisterLogicBase

        ' 使用するフィールドを初期化
        Public 締日区分 As New Field.ItemData("締日区分", "", Field.ItemType.DataItem, True, "締日区分", False)
        Public 請求年月 As New Field.ItemData("請求年月", "", Field.ItemType.DataItem, True, 7,, Text.FormatContents.tbDateYM)
        Public 請求日付F As New Field.ItemData("搬入日付自", "", Field.ItemType.DataItem, True, 10,, Text.FormatContents.tbDate)
        Public 請求日付T As New Field.ItemData("請求日付至", "", Field.ItemType.DataItem, True, 10,, Text.FormatContents.tbDate)
        Public 得意先コードF As New Field.ItemData("得意先コード自", "", Field.ItemType.DataItem, False, 6,, Text.FormatContents.tbCode)
        Public 得意先コードT As New Field.ItemData("得意先コード至", "", Field.ItemType.DataItem, False, 6,, Text.FormatContents.tbCode)
        Public 選択リスト As New Field.ItemData("選択リスト", "", Field.ItemType.DataItem, False, 4000,, Text.FormatContents.tbNone)

        Sub New(Security As Security)
            MyBase.New(Security)
        End Sub

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' DBからフィールドに読み込む処理を記述します。
        ''' エラーが発生した場合はLastErrorにエラーメッセージを格納しFalseを返します。
        ''' </summary>
        ''' <param name="read_after_clear">読み込む前にフィールドをクリアするかどうか</param>
        '''------------------------------------------------------------------------------
        Protected Overrides Function ReadMainDataSub(read_after_clear As Boolean) As Boolean
            If read_after_clear Then

            End If

            締日区分.Value = ""
            請求日付F.Value = ""
            請求日付T.Value = ""
            得意先コードF.Value = ""
            得意先コードT.Value = ""
            選択リスト.Value = ""

            GetShimekikan(read_after_clear)

            Return True
        End Function

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' フィールドからDBに書き込み処理を記述します。
        ''' エラーが発生した場合はLastErrorにエラーメッセージを格納しFalseを返します。
        ''' </summary>
        '''------------------------------------------------------------------------------
        Protected Overrides Function WriteMainDataSub() As Boolean
            Dim strNow As String = Format(Now, "yyyy/MM/dd HH:mm:ss")

            Dim str請求番号 As String = ""
            Dim dec行NO As Decimal = 0

            Dim strKeyNew As String = ""
            Dim strKeyOld As String = ""

            Dim strBillNo As String = ""
            Dim strDelim As String = ""
            Try
                Using tran As New Transactions.TransactionScope(Transactions.TransactionScopeOption.Required, TimeSpan.Zero)

                    ' 事前削除
                    Me.DeleteMainData()

                    ' 請求鑑DT作成
                    Using db計算DT As New DbCalcTableAdapters.F請求計算DTTableAdapter
                        Using rs計算DT As DbCalc.F請求計算DTDataTable = db計算DT.GetData(締日区分.Value, 請求日付F.Value, 請求日付T.Value, 選択リスト.Value)
                            For i As Integer = 0 To rs計算DT.Count - 1

                                ' ﾌﾞﾚｰｸｷｰ設定
                                strKeyNew = rs計算DT(i).得意先コード

                                ' ｷｰの切り替わりでの処理
                                If strKeyNew <> strKeyOld Then

                                    ' 新しい請求番号の取得
                                    str請求番号 = MasterCode.AnyTableNewNumber("T請求鑑DT", "請求番号").ToString
                                    str請求番号 = Right("0000000000" + str請求番号, 10)

                                    strBillNo += strDelim & str請求番号 : strDelim = ","
                                    dec行NO = 1
                                End If

                                Using db請求DT As New DbTranTableAdapters.T請求鑑DTTableAdapter
                                    db請求DT.Insert(str請求番号 _
                                              , dec行NO _
                                              , rs計算DT(i).得意先コード _
                                              , rs計算DT(i).工事コード _
                                              , rs計算DT(i).工事内部コード _
                                              , rs計算DT(i).前回額 _
                                              , rs計算DT(i).入金額 _
                                              , rs計算DT(i).調整額 _
                                              , rs計算DT(i).繰越額 _
                                              , rs計算DT(i).税抜額 _
                                              , rs計算DT(i).税込額 _
                                              , rs計算DT(i).消費税 _
                                              , rs計算DT(i).請求額 _
                                              , ""
                                              )
                                End Using

                                strKeyOld = strKeyNew
                                dec行NO += 1
                            Next
                        End Using
                    End Using

                    ' 鑑HDの作成
                    Dim int締日 As Integer = Text.CVal(締日区分.Value)

                    Using db計算HD As New DbCalcTableAdapters.F請求計算HDTableAdapter
                        Using rs計算HD As DbCalc.F請求計算HDDataTable = db計算HD.GetData(strBillNo)
                            For i As Integer = 0 To rs計算HD.Count - 1

                                Using db請求HD As New DbTranTableAdapters.T請求鑑HDTableAdapter
                                    db請求HD.Insert(rs計算HD(i).請求番号 _
                                                  , rs計算HD(i).得意先コード _
                                                  , 請求年月.Value _
                                                  , int締日 _
                                                  , 請求日付F.Value _
                                                  , 請求日付T.Value _
                                                  , Text.CVal(rs計算HD(i).前回額) _
                                                  , Text.CVal(rs計算HD(i).入金額) _
                                                  , Text.CVal(rs計算HD(i).調整額) _
                                                  , Text.CVal(rs計算HD(i).繰越額) _
                                                  , Text.CVal(rs計算HD(i).税抜額) _
                                                  , Text.CVal(rs計算HD(i).税込額) _
                                                  , Text.CVal(rs計算HD(i).消費税) _
                                                  , Text.CVal(rs計算HD(i).請求額) _
                                                  , "" _
                                                  , strNow _
                                                  , Security.UserID _
                                                  , strNow _
                                                  , Security.UserID
                                                  )
                                End Using

                            Next

                        End Using
                    End Using

                    ' T売上HDの請求番号を更新
                    Using db売上 As New DbTranTableAdapters.T売上HDTableAdapter
                        db売上.UpdateSeikyuNo(strBillNo)
                    End Using

                    ' T入金HDの請求番号を更新
                    Using db入金 As New DbTranTableAdapters.T入金HDTableAdapter
                        db入金.UpdateSeikyuNo(strBillNo)
                    End Using

                    tran.Complete()
                End Using

            Catch ex As Exception
                Me.LastError = ex.Message
                Return False
            Finally

            End Try

            Return True
        End Function

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' DBから削除する処理を記述します。
        ''' エラーが発生した場合はLastErrorにエラーメッセージを格納しFalseを返します。
        ''' </summary>
        '''------------------------------------------------------------------------------
        Protected Overrides Function DeleteMainDataSub() As Boolean
            Dim int締日 As Integer = Text.CVal(締日区分.Value)

            Using db As New DbTranTableAdapters.T売上HDTableAdapter
                db.DeleteSeikyuNo(int締日, 請求年月.Value, 請求日付F.Value, 請求日付T.Value, 選択リスト.Value)
            End Using
            Using db As New DbTranTableAdapters.T入金HDTableAdapter
                db.DeleteSeikyuNo(int締日, 請求年月.Value, 請求日付F.Value, 請求日付T.Value, 選択リスト.Value)
            End Using
            Using db As New DbTranTableAdapters.T請求鑑DTTableAdapter
                db.DeleteSeikyuKeisan(int締日, 請求年月.Value, 請求日付F.Value, 請求日付T.Value, 選択リスト.Value)
            End Using
            Using db As New DbTranTableAdapters.T請求鑑HDTableAdapter
                db.DeleteSeikyuKeisan(int締日, 請求年月.Value, 請求日付F.Value, 請求日付T.Value, 選択リスト.Value)
            End Using

            Return True
        End Function

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' フィールドの検証処理をを記述します。
        ''' フィールドの検証処理。基底クラスにて、WriteMainDataSubが呼ばれる前にコールされます。
        ''' 基本的な検証(文字数・書式・区分値)は基底クラスで検証されます。
        ''' </summary>
        '''------------------------------------------------------------------------------
        Protected Overrides Sub EnterValidateSub(ByRef chk As Dbs.Asphalt.Core.Common.Validator)

            '----検証サンプル--------------------------------------------------------------
            ' 担当者マスターに登録済みかどうか
            'chk.FoundMaster(担当者コード, "担当者")
            '----検証サンプル--------------------------------------------------------------

        End Sub

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' 締期間の取得
        ''' </summary>
        '''------------------------------------------------------------------------------
        Public Function GetShimekikan(Shimebi_Clear As Boolean) As Boolean

            Dim dec締日 As Decimal = Text.CVal(締日区分.Value)
            If Shimebi_Clear Then
                dec締日 = 0
            End If

            Using db As New DbCalcTableAdapters.F請求締期間TableAdapter
                Using rs As DbCalc.F請求締期間DataTable = db.GetData(dec締日, 請求年月.Value)
                    If rs.Count > 0 Then
                        締日区分.Value = rs(0).締日区分
                        請求年月.Value = rs(0).請求年月
                        請求日付F.Value = rs(0).請求日付自
                        請求日付T.Value = rs(0).請求日付至
                    End If
                End Using
            End Using

            Return True
        End Function

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' 締期間の計算
        ''' </summary>
        '''------------------------------------------------------------------------------
        Public Function CalcShimekikan() As Boolean

            If 請求年月.Value = "" Then
                Return True
            End If

            Select Case 締日区分.Value
                Case 99
                    請求日付F.Value = 請求年月.Value & "/01"
                    請求日付T.Value = Format(Text.CDateEx(請求日付F.Value).AddMonths(1).AddDays(-1), "yyyy/MM/dd")

                Case Else
                    請求日付T.Value = 請求年月.Value & "/" & Right("00" & 締日区分.Value.ToString, 2)
                    請求日付F.Value = Format(Text.CDateEx(請求日付T.Value).AddMonths(-1).AddDays(1), "yyyy/MM/dd")

            End Select

            Return True
        End Function

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' マネージャの名称(メニューや画面タイトル、権限モジュールリストで共通使用されます)
        ''' </summary>
        '''------------------------------------------------------------------------------
        Public Overrides ReadOnly Property Title As String
            Get
                Return "請求計算"
            End Get
        End Property
    End Class

End Namespace
