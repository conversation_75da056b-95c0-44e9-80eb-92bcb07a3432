﻿Imports System
Imports System.Text
Imports Dbs.Asphalt.Core
Imports Dbs.Asphalt.Core.BaseClass
Imports Dbs.Asphalt.Core.SystemLogic
Imports Dbs.Asphalt.Core.Common
Imports Dbs.Asphalt.Database
Imports Dbs.Application.Logic.Database

Namespace Logic.bill

    ''' <summary>
    ''' 印刷処理サンプルマネージャ
    ''' (UIからの使用方法はweb/extension/ExtensionSample1.ascxを参照)
    ''' </summary>
    Public Class SeikyulistManager
        Inherits PrinterLogicBase

        Public 締日区分 As New Field.ItemData("締日区分", "", Field.ItemType.DataItem, True, "締日区分", False)
        Public 請求年月 As New Field.ItemData("請求年月", "", Field.ItemType.DataItem, True, 7,, Text.FormatContents.tbDateYM)
        Public 得意先コードF As New Field.ItemData("得意先コード自", "", Field.ItemType.DataItem, False, 6,, Text.FormatContents.tbCode)
        Public 得意先コードT As New Field.ItemData("得意先コード至", "", Field.ItemType.DataItem, False, 6,, Text.FormatContents.tbCode)
        Public 担当者コード As New Field.ItemData("担当者コード", "", Field.ItemType.DataItem, False, 10)
        Public 選択リスト As New Field.ItemData("選択リスト", "", Field.ItemType.DataItem, False, 4000,, Text.FormatContents.tbNone)

        Sub New(security As Security)
            MyBase.New(security)
        End Sub

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' フィールドの検証処理をを記述します。
        ''' フィールドの検証処理。基底クラスにて、WriteMainDataSubが呼ばれる前にコールされます。
        ''' 基本的な検証(文字数・書式・区分値)は基底クラスで検証されます。
        ''' </summary>
        '''------------------------------------------------------------------------------
        Protected Overrides Sub EnterValidateSub(ByRef chk As Validator)

            '----検証サンプル--------------------------------------------------------------
            ' コードの開始終了の範囲チェック
            'chk.FromTo(搬入日付F, 搬入日付T)
            '----検証サンプル--------------------------------------------------------------

            If chk.Empty(選択リスト) Then
                chk.AnyCheck(選択リスト, True, "一件も選択されていません")
            End If

        End Sub

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' 印刷データの作成処理を記述します。
        ''' </summary>
        ''' <param name="reportfilename">レポートファイルの名前が渡される場合があります。</param>
        '''------------------------------------------------------------------------------
        Protected Overrides Function PrintMainDataSub(Optional reportfilename As String = "") As Boolean
            ' セクションレポートの場合はインスタンス生成
            Dim rep明細 As New SeikyuList

            Using db As New DbReportTableAdapters.R請求一覧表TableAdapter
                Using rs As DbReport.R請求一覧表DataTable = db.GetData(選択リスト.Value)
                    If rs.Count > 0 Then
                        ' 基底クラスにレポートオブジェクトとデータを渡すだけ
                        If Not MakeReport(rep明細, Me.FileName, rs.DefaultView) Then
                            Return False
                        End If
                    Else
                        ' データがない場合はエラーメッセージをセットして戻る
                        LastError = Message.MessageText("EM_NODATA")
                        Return False
                    End If
                End Using
            End Using

            Return True
        End Function

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' 締期間の取得
        ''' </summary>
        '''------------------------------------------------------------------------------
        Public Function GetShimekikan(Shimebi_Clear As Boolean) As Boolean

            Dim dec締日 As Decimal = Text.CVal(締日区分.Value)
            If Shimebi_Clear Then
                dec締日 = 0
            End If

            Using db As New DbCalcTableAdapters.F請求締期間TableAdapter
                Using rs As DbCalc.F請求締期間DataTable = db.GetData(dec締日, 請求年月.Value)
                    If rs.Count > 0 Then
                        締日区分.Value = rs(0).締日区分
                        請求年月.Value = rs(0).請求年月
                    End If
                End Using
            End Using

            Return True
        End Function

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' マネージャの名称(メニューや画面タイトル、権限モジュールリストで共通使用されます)
        ''' </summary>
        '''------------------------------------------------------------------------------
        Public Overrides ReadOnly Property Title As String
            Get
                Return "請求一覧表"
            End Get
        End Property

        Public ReadOnly Property GetDataView() As DataView
            Get
                Dim db As New DbReportTableAdapters.R請求一覧表TableAdapter
                Dim rs As New DbReport.R請求一覧表DataTable

                rs = db.GetData(選択リスト.Value)
                Return rs.DefaultView
            End Get
        End Property

        Public ReadOnly Property FileName() As String
            Get
                Return "請求一覧表_" & 請求年月.Value.ToString.Replace("/", "") & Right("00" & 締日区分.Value.ToString, 2) & Format(Now, "HHmmss")
            End Get
        End Property

    End Class

End Namespace
