﻿Imports System
Imports System.Text
Imports Dbs.Asphalt.Core
Imports Dbs.Asphalt.Core.BaseClass
Imports Dbs.Asphalt.Core.SystemLogic
Imports Dbs.Asphalt.Core.Common
Imports Dbs.Asphalt.Database
Imports Dbs.Application.Logic.Database

Namespace Logic.Anytime

    ''' <summary>
    ''' 印刷処理サンプルマネージャ
    ''' (UIからの使用方法はweb/extension/ExtensionSample1.ascxを参照)
    ''' </summary>
    Public Class LeaseListManager
        Inherits PrinterLogicBase

        Public 商品コードF As New Field.ItemData("商品コードF", "", Field.ItemType.KeyItem, True, 6,, Text.FormatContents.tbCode)
        Public 商品コードT As New Field.ItemData("商品コードT", "", Field.ItemType.KeyItem, True, 6,, Text.FormatContents.tbCode)
        Public 得意先コード As New Field.ItemData("得意先コード", "", Field.ItemType.DataItem, False, 6,, Text.FormatContents.tbCode)
        Public 工事コード As New Field.ItemData("工事コード", "", Field.ItemType.DataItem, False, 4,, Text.FormatContents.tbCode)
        Public 工事内部コード As New Field.ItemData("工事内部コード", "", Field.ItemType.DataItem, False, 10,, Text.FormatContents.tbCode)

        Sub New(security As Security)
            MyBase.New(security)
        End Sub

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' フィールドの検証処理をを記述します。
        ''' フィールドの検証処理。基底クラスにて、WriteMainDataSubが呼ばれる前にコールされます。
        ''' 基本的な検証(文字数・書式・区分値)は基底クラスで検証されます。
        ''' </summary>
        '''------------------------------------------------------------------------------
        Protected Overrides Sub EnterValidateSub(ByRef chk As Validator)

            '----検証サンプル--------------------------------------------------------------
            ' コードの開始終了の範囲チェック
            'chk.FromTo(搬入日付F, 搬入日付T)
            '----検証サンプル--------------------------------------------------------------

        End Sub

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' 印刷データの作成処理を記述します。
        ''' </summary>
        ''' <param name="reportfilename">レポートファイルの名前が渡される場合があります。</param>
        '''------------------------------------------------------------------------------
        Protected Overrides Function PrintMainDataSub(Optional reportfilename As String = "") As Boolean
            Return True
        End Function

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' マネージャの名称(メニューや画面タイトル、権限モジュールリストで共通使用されます)
        ''' </summary>
        '''------------------------------------------------------------------------------
        Public Overrides ReadOnly Property Title As String
            Get
                Return "リース物件一覧"
            End Get
        End Property

        Public ReadOnly Property GetDataView() As DataView
            Get
                Dim db As New DbCalcTableAdapters.Fリース商品一覧TableAdapter
                Dim rs As New DbCalc.Fリース商品一覧DataTable

                rs = db.GetData(商品コードF.Value, 商品コードT.Value, 得意先コード.Value, 工事内部コード.Value)
                Return rs.DefaultView
            End Get
        End Property

        Public ReadOnly Property FileName() As String
            Get
                Return "リース物件一覧_" & Format(Now, "HHmmss")
            End Get
        End Property
    End Class

End Namespace
