﻿Imports Dbs.Asphalt.Core
Imports Dbs.Asphalt.Core.Common
Imports Dbs.Asphalt.Core.SystemLogic
Imports Dbs.Asphalt.Core.BaseClass
Imports Dbs.Asphalt.Database
Imports Dbs.Application.Logic.Database

Namespace Logic.Anytime

    ''' <summary>
    ''' 登録処理サンプルマネージャ
    ''' </summary>
    Public Class KoujiListManager
        Inherits RegisterLogicBase

        Public 得意先コード As New Field.ItemData("得意先コード", "", Field.ItemType.KeyItem, False, 6,, Text.FormatContents.tbCode)
        Public 工事コードF As New Field.ItemData("工事コードF", "", Field.ItemType.DataItem, False, 4,, Text.FormatContents.tbCode)
        Public 工事コードT As New Field.ItemData("工事コードT", "", Field.ItemType.DataItem, False, 4,, Text.FormatContents.tbCode)
        Public 対象期間F As New Field.ItemData("対象期間F", Format(Now, "yyyy/MM"), Field.ItemType.DataItem, False, 7,, Text.FormatContents.tbDateYM)
        Public 対象期間T As New Field.ItemData("対象期間T", Format(Now, "yyyy/MM"), Field.ItemType.DataItem, False, 7,, Text.FormatContents.tbDateYM)

        Public 日付 As New Field.ItemData("日付", "", Field.ItemType.DataItem, False, 10,, Text.FormatContents.tbDate)
        Public 得意先名 As New Field.ItemData("得意先名", "", Field.ItemType.DataItem, False, 100,, Text.FormatContents.tbNone)
        Public 工事名 As New Field.ItemData("工事名", "", Field.ItemType.DataItem, False, 100,, Text.FormatContents.tbNone)
        Public 商品名 As New Field.ItemData("商品名", "", Field.ItemType.DataItem, False, 100,, Text.FormatContents.tbNone)
        Public 金額 As New Field.ItemData("金額", "", Field.ItemType.DataItem, False, 9,, Text.FormatContents.tbCurrency)
        Public 備考 As New Field.ItemData("備考", "", Field.ItemType.DataItem, False, 100,, Text.FormatContents.tbNone)

        Public 伝票区分 As New Field.ItemData("伝票区分", "", Field.ItemType.DataItem, False, 1,, Text.FormatContents.tbNone)
        Public 伝票番号 As New Field.ItemData("伝票番号", "", Field.ItemType.DataItem, False, 10,, Text.FormatContents.tbCode)
        Public 行番号 As New Field.ItemData("行番号", "", Field.ItemType.DataItem, False, 3,, Text.FormatContents.tbNumeric)
        Public 工事内部コード As New Field.ItemData("工事内部コード", "", Field.ItemType.DataItem, False, 10,, Text.FormatContents.tbCode)
        Public 完了区分 As New Field.ItemData("完了区分", "", Field.ItemType.DataItem, False, "完了区分", True)

        Sub New(Security As Security)
            MyBase.New(Security)
        End Sub

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' DBからフィールドに読み込む処理を記述します。
        ''' エラーが発生した場合はLastErrorにエラーメッセージを格納しFalseを返します。
        ''' </summary>
        ''' <param name="read_after_clear">読み込む前にフィールドをクリアするかどうか</param>
        '''------------------------------------------------------------------------------
        Protected Overrides Function ReadMainDataSub(read_after_clear As Boolean) As Boolean
            If read_after_clear Then

            End If

            得意先コード.Value = ""
            工事コードF.Value = ""
            工事コードT.Value = ""
            対象期間F.Value = Format(Now, "yyyy/MM")
            対象期間T.Value = Format(Now, "yyyy/MM")

            日付.Value = ""
            得意先名.Value = ""
            工事名.Value = ""
            商品名.Value = ""
            金額.Value = 0
            備考.Value = ""
            伝票区分.Value = ""
            伝票番号.Value = ""
            行番号.Value = 0

            Return True
        End Function

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' フィールドからDBに書き込み処理を記述します。
        ''' エラーが発生した場合はLastErrorにエラーメッセージを格納しFalseを返します。
        ''' </summary>
        '''------------------------------------------------------------------------------
        Protected Overrides Function WriteMainDataSub() As Boolean
            Dim strNow As String = Format(Now, "yyyy/MM/dd HH:mm:ss")

            ' 備考の更新
            Try
                Dim int行NO As Integer = Text.CVal(行番号.Value)

                Select Case 伝票区分.Value
                    Case "1"  ' 請求
                        Using db As New DbTranTableAdapters.T請求鑑DTTableAdapter
                            db.UpdateBiko(備考.Value, 伝票番号.Value, int行NO)
                        End Using

                    Case "2"  ' 入金
                        Using db As New DbTranTableAdapters.T入金DTTableAdapter
                            db.UpdateBiko(備考.Value, 伝票番号.Value, int行NO)
                        End Using

                    Case "3"  ' 売上
                        Using db As New DbTranTableAdapters.T売上DTTableAdapter
                            db.UpdateBiko(備考.Value, 伝票番号.Value, int行NO)
                        End Using
                End Select

                ' 工事を完了にする
                'If 完了区分.Value = 1 Then
                Using db As New DbMasterTableAdapters.M工事TableAdapter
                        Using rs As DbMaster.M工事DataTable = db.GetDataPK(工事内部コード.Value)
                            If rs.Count > 0 Then
                                'rs(0).完了区分 = 1
                                rs(0).完了区分 = 完了区分.Value
                                rs(0).更新日時 = strNow
                                rs(0).更新者ID = Security.UserID
                                db.Update(rs)
                            End If
                        End Using
                    End Using
                'End If

            Catch ex As Exception
                Me.LastError = ex.Message
                Return False
            Finally
            End Try

            Return True
        End Function

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' DBから削除する処理を記述します。
        ''' エラーが発生した場合はLastErrorにエラーメッセージを格納しFalseを返します。
        ''' </summary>
        '''------------------------------------------------------------------------------
        Protected Overrides Function DeleteMainDataSub() As Boolean
            'Using db As New DbTranTableAdapters.T請求鑑DTTableAdapter
            '    db.DeleteSeikyuKeisan(締日区分.Value, 請求日付F.Value, 請求日付T.Value, 得意先コードF.Value, 得意先コードT.Value)
            'End Using
            'Using db As New DbTranTableAdapters.T請求鑑HDTableAdapter
            '    db.DeleteSeikyuKeisan(締日区分.Value, 請求日付F.Value, 請求日付T.Value, 得意先コードF.Value, 得意先コードT.Value)
            'End Using

            Return True
        End Function

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' フィールドの検証処理をを記述します。
        ''' フィールドの検証処理。基底クラスにて、WriteMainDataSubが呼ばれる前にコールされます。
        ''' 基本的な検証(文字数・書式・区分値)は基底クラスで検証されます。
        ''' </summary>
        '''------------------------------------------------------------------------------
        Protected Overrides Sub EnterValidateSub(ByRef chk As Dbs.Asphalt.Core.Common.Validator)

            '----検証サンプル--------------------------------------------------------------
            ' 担当者マスターに登録済みかどうか
            'chk.FoundMaster(担当者コード, "担当者")
            '----検証サンプル--------------------------------------------------------------

        End Sub

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' マネージャの名称(メニューや画面タイトル、権限モジュールリストで共通使用されます)
        ''' </summary>
        '''------------------------------------------------------------------------------
        Public Overrides ReadOnly Property Title As String
            Get
                Return "工事台帳"
            End Get
        End Property

        Public ReadOnly Property GetDataView() As DataView
            Get
                Dim db As New DbCalcTableAdapters.F1工事台帳TableAdapter
                Dim rs As New DbCalc.F1工事台帳DataTable

                rs = db.GetData(得意先コード.Value, 工事コードF.Value, 工事コードT.Value, 対象期間F.Value, 対象期間T.Value)
                Return rs.DefaultView
            End Get
        End Property

        Public ReadOnly Property FileName() As String
            Get
                Return "工事台帳_" & Format(Now, "HHmmss")
            End Get
        End Property

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' ﾜｰｸﾃｰﾌﾞﾙの削除
        ''' </summary>
        '''------------------------------------------------------------------------------
        Private Function Delete_Work(ByVal strセッションID As String) As Boolean

            Using db As New DbWorkTableAdapters.W売上選択TableAdapter
                db.DeleteBySessionID(strセッションID)
            End Using

            Return True
        End Function

    End Class
End Namespace
