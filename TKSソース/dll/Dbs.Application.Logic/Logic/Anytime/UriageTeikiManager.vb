﻿Imports Dbs.Asphalt.Core
Imports Dbs.Asphalt.Core.Common
Imports Dbs.Asphalt.Core.SystemLogic
Imports Dbs.Asphalt.Core.BaseClass
Imports Dbs.Asphalt.Database
Imports Dbs.Application.Logic.Database

Namespace Logic.Anytime

    ''' <summary>
    ''' 登録処理サンプルマネージャ
    ''' </summary>
    Public Class UriageTeikiManager
        Inherits RegisterLogicBase

        ' 使用するフィールドを初期化
        Public 売上日付 As New Field.ItemData("売上日付", Format(Now, "yyyy/MM/dd"), Field.ItemType.DataItem, True, 10,, Text.FormatContents.tbDate)
        Public 得意先コード As New Field.ItemData("得意先コード", "", Field.ItemType.DataItem, False, 6,, Text.FormatContents.tbCode)
        Public 売上リスト As New Field.ItemData("売上リスト", "", Field.ItemType.KeyItem, False, 4000,, Text.FormatContents.tbNone)

        Sub New(Security As Security)
            MyBase.New(Security)
        End Sub

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' DBからフィールドに読み込む処理を記述します。
        ''' エラーが発生した場合はLastErrorにエラーメッセージを格納しFalseを返します。
        ''' </summary>
        ''' <param name="read_after_clear">読み込む前にフィールドをクリアするかどうか</param>
        '''------------------------------------------------------------------------------
        Protected Overrides Function ReadMainDataSub(read_after_clear As Boolean) As Boolean
            If read_after_clear Then

            End If

            売上日付.Value = Format(Now, "yyyy/MM/dd")
            得意先コード.Value = ""
            売上リスト.Value = ""

            Return True
        End Function

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' フィールドからDBに書き込み処理を記述します。
        ''' エラーが発生した場合はLastErrorにエラーメッセージを格納しFalseを返します。
        ''' </summary>
        '''------------------------------------------------------------------------------
        Protected Overrides Function WriteMainDataSub() As Boolean
            Dim strNow As String = Format(Now, "yyyy/MM/dd HH:mm:ss")

            Dim str売上番号 As String = ""
            Dim dec行NO As Decimal = 0

            Dim strKeyNew As String = ""
            Dim strKeyOld As String = ""
            Dim strセッションID As String = ""

            Using dbS As New DbSystemTableAdapters.SセッションTableAdapter
                Using rsS As DbSystem.SセッションDataTable = dbS.GetData(Security.UserID)
                    strセッションID = rsS(0).セッションID
                End Using
            End Using

            ' 事前削除
            Me.Delete_Work(strセッションID)

            ' ﾜｰｸｲﾝｻｰﾄ
            Dim str売上行番号 As String = ""
            For i As Integer = 0 To 売上リスト.Value.ToString.Split(",").Count - 1

                str売上行番号 = 売上リスト.Value.ToString.Split(",")(i)

                Using dbW As New DbWorkTableAdapters.W売上選択TableAdapter

                    dbW.Insert(strセッションID _
                             , str売上行番号.Split("-")(0) _
                             , Text.CVal(str売上行番号.Split("-")(1))
                              )
                End Using
            Next

            ' T売上XX作成
            Dim dec数量 As Decimal = 0
            Dim dec単価 As Decimal = 0
            Dim dec基本料金 As Decimal = 0

            Try
                Using tran As New Transactions.TransactionScope(Transactions.TransactionScopeOption.Required, TimeSpan.Zero)

                    ' 売上入力のﾏﾈｰｼﾞｬを使用
                    Dim clsLogic As New Dbs.Application.Logic.Logic.daily.UriageDenpyoManager(Security)

                    Using db定期 As New DbCalcTableAdapters.F売上定期処理TableAdapter
                        Using rs定期 As DbCalc.F売上定期処理DataTable = db定期.GetData(strセッションID, 売上日付.Value)
                            For i As Integer = 0 To rs定期.Count - 1

                                clsLogic.Start()

                                ' HD
                                clsLogic.売上番号.Value = ""
                                clsLogic.搬入日付.Value = rs定期(i).搬入日付
                                clsLogic.伝票区分.Value = rs定期(i).伝票区分
                                clsLogic.得意先コード.Value = rs定期(i).得意先コード
                                clsLogic.工事コード.Value = rs定期(i).工事コード
                                clsLogic.工事内部コード.Value = rs定期(i).工事内部コード
                                clsLogic.摘要1.Value = rs定期(i).摘要1
                                clsLogic.摘要2.Value = rs定期(i).摘要2
                                clsLogic.備考.Value = rs定期(i).備考
                                clsLogic.伝票発行区分.Value = rs定期(i).伝票発行区分
                                clsLogic.請求番号.Value = rs定期(i).請求番号
                                clsLogic.登録日時.Value = strNow
                                clsLogic.登録者ID.Value = Security.UserID
                                clsLogic.更新日時.Value = strNow
                                clsLogic.更新者ID.Value = Security.UserID

                                clsLogic.売上作成区分.Value = "2"

                                ' DT
                                clsLogic.List(0).売上番号.Value = ""
                                clsLogic.List(0).行番号.Value = 0
                                clsLogic.List(0).計算区分.Value = rs定期(i).計算区分
                                clsLogic.List(0).商品コード.Value = rs定期(i).商品コード
                                clsLogic.List(0).商品名.Value = rs定期(i).商品名

                                'clsLogic.List(0).数量.Value = rs定期(i).数量
                                'clsLogic.List(0).単価.Value = rs定期(i).単価
                                'clsLogic.List(0).基本料金.Value = rs定期(i).基本料金

                                dec数量 = Format(rs定期(i).数量.ToString().ToDecimal, "###0")
                                dec単価 = Format(rs定期(i).単価.ToString().ToDecimal, "###0")
                                dec基本料金 = Format(rs定期(i).基本料金.ToString().ToDecimal, "###0")

                                clsLogic.List(0).数量.Value = dec数量
                                clsLogic.List(0).単価.Value = dec単価
                                clsLogic.List(0).基本料金.Value = dec基本料金

                                clsLogic.List(0).金額.Value = 0

                                clsLogic.List(0).単位区分.Value = rs定期(i).単位区分
                                clsLogic.List(0).備考明細.Value = rs定期(i).備考明細
                                clsLogic.List(0).備考社内.Value = rs定期(i).備考社内
                                clsLogic.List(0).搬入番号.Value = rs定期(i).搬入番号
                                clsLogic.List(0).返納日付.Value = rs定期(i).返納日付

                                clsLogic.List(0).開始日付.Value = rs定期(i).開始日付
                                clsLogic.List(0).終了日付.Value = rs定期(i).終了日付

                                ' 金額計算
                                clsLogic.List(0).CalcKingaku()

                                ' 何故がminorが多くなるので
                                clsLogic.List(0).金額.Value = Format(Text.CVal(clsLogic.List(0).金額.Value), "#,##0")

                                ' 保存処理
                                If Not clsLogic.WriteMainData() Then
                                    Me.LastError = clsLogic.LastError
                                    Return False
                                End If

                            Next
                        End Using
                    End Using

                    tran.Complete()
                End Using

            Catch ex As Exception
                Me.LastError = ex.Message
                Return False
            Finally
                ' 事後削除
                Me.Delete_Work(strセッションID)
            End Try

            Return True
        End Function

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' DBから削除する処理を記述します。
        ''' エラーが発生した場合はLastErrorにエラーメッセージを格納しFalseを返します。
        ''' </summary>
        '''------------------------------------------------------------------------------
        Protected Overrides Function DeleteMainDataSub() As Boolean
            'Using db As New DbTranTableAdapters.T請求鑑DTTableAdapter
            '    db.DeleteSeikyuKeisan(締日区分.Value, 請求日付F.Value, 請求日付T.Value, 得意先コードF.Value, 得意先コードT.Value)
            'End Using
            'Using db As New DbTranTableAdapters.T請求鑑HDTableAdapter
            '    db.DeleteSeikyuKeisan(締日区分.Value, 請求日付F.Value, 請求日付T.Value, 得意先コードF.Value, 得意先コードT.Value)
            'End Using

            Return True
        End Function

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' フィールドの検証処理をを記述します。
        ''' フィールドの検証処理。基底クラスにて、WriteMainDataSubが呼ばれる前にコールされます。
        ''' 基本的な検証(文字数・書式・区分値)は基底クラスで検証されます。
        ''' </summary>
        '''------------------------------------------------------------------------------
        Protected Overrides Sub EnterValidateSub(ByRef chk As Dbs.Asphalt.Core.Common.Validator)

            '----検証サンプル--------------------------------------------------------------
            ' 担当者マスターに登録済みかどうか
            'chk.FoundMaster(担当者コード, "担当者")
            '----検証サンプル--------------------------------------------------------------

        End Sub

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' マネージャの名称(メニューや画面タイトル、権限モジュールリストで共通使用されます)
        ''' </summary>
        '''------------------------------------------------------------------------------
        Public Overrides ReadOnly Property Title As String
            Get
                Return "定期売上処理"
            End Get
        End Property

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' ﾜｰｸﾃｰﾌﾞﾙの削除
        ''' </summary>
        '''------------------------------------------------------------------------------
        Private Function Delete_Work(ByVal strセッションID As String) As Boolean

            Using db As New DbWorkTableAdapters.W売上選択TableAdapter
                db.DeleteBySessionID(strセッションID)
            End Using

            Return True
        End Function

    End Class
End Namespace
