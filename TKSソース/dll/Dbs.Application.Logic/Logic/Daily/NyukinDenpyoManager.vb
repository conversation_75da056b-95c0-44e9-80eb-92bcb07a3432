﻿Imports Dbs.Asphalt.Core
Imports Dbs.Asphalt.Core.Common
Imports Dbs.Asphalt.Core.SystemLogic
Imports Dbs.Asphalt.Core.BaseClass
Imports Dbs.Asphalt.Database
Imports Dbs.Application.Logic.Database

Namespace Logic.daily

    ''' <summary>
    ''' 登録処理サンプルマネージャ
    ''' </summary>
    Public Class NyukinDenpyoListManager
        Inherits RegisterLogicBase

        ' 使用するフィールドを初期化
        Public 入金番号 As New Field.ItemData("入金番号", "", Field.ItemType.KeyItem, False, 10)
        Public 行番号 As New Field.ItemData("行番号", "", Field.ItemType.KeyItem, False, 3)
        Public 入金区分 As New Field.ItemData("入金区分", "", Field.ItemType.DataItem, True, "入金区分", False)
        Public 入金金額 As New Field.ItemData("入金金額", "", Field.ItemType.DataItem, True, 9,, Text.FormatContents.tbCurrency)
        Public 備考 As New Field.ItemData("備考", "", Field.ItemType.DataItem, False, 200)

        Sub New(Security As Security)
            MyBase.New(Security)

            Me.AutoBackupRestore = False
        End Sub

        Public Overrides Sub Start()
            MyBase.Start()

            入金番号.Value = ""
            行番号.Value = ""
            入金区分.Value = ""
            入金金額.Value = ""
            備考.Value = ""
        End Sub

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' DBからフィールドに読み込む処理を記述します。
        ''' エラーが発生した場合はLastErrorにエラーメッセージを格納しFalseを返します。
        ''' </summary>
        ''' <param name="read_after_clear">読み込む前にフィールドをクリアするかどうか</param>
        '''------------------------------------------------------------------------------
        Protected Overrides Function ReadMainDataSub(read_after_clear As Boolean) As Boolean
            Return True
        End Function

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' フィールドからDBに書き込み処理を記述します。
        ''' エラーが発生した場合はLastErrorにエラーメッセージを格納しFalseを返します。
        ''' </summary>
        '''------------------------------------------------------------------------------
        Protected Overrides Function WriteMainDataSub() As Boolean
            Return True
        End Function

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' DBから削除する処理を記述します。
        ''' エラーが発生した場合はLastErrorにエラーメッセージを格納しFalseを返します。
        ''' </summary>
        '''------------------------------------------------------------------------------
        Protected Overrides Function DeleteMainDataSub() As Boolean
            Return True
        End Function

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' フィールドの検証処理をを記述します。
        ''' フィールドの検証処理。基底クラスにて、WriteMainDataSubが呼ばれる前にコールされます。
        ''' 基本的な検証(文字数・書式・区分値)は基底クラスで検証されます。
        ''' </summary>
        '''------------------------------------------------------------------------------
        Protected Overrides Sub EnterValidateSub(ByRef chk As Dbs.Asphalt.Core.Common.Validator)

            '----検証サンプル--------------------------------------------------------------
            ' 担当者マスターに登録済みかどうか
            'chk.FoundMaster(担当者コード, "担当者")
            '----検証サンプル--------------------------------------------------------------

        End Sub

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' マネージャの名称(メニューや画面タイトル、権限モジュールリストで共通使用されます)
        ''' </summary>
        '''------------------------------------------------------------------------------
        Public Overrides ReadOnly Property Title As String
            Get
                Return ""
            End Get
        End Property
    End Class

    Public Class NyukinDenpyoManager
        Inherits RegisterLogicBase

        Public 入金番号 As New Field.ItemData("入金番号", "", Field.ItemType.KeyItem, False, 10,, Text.FormatContents.tbCode)
        Public 入金日付 As New Field.ItemData("入金日付", "", Field.ItemType.DataItem, True, 10,, Text.FormatContents.tbDate)
        Public 請求年月 As New Field.ItemData("請求年月", "", Field.ItemType.DataItem, False, 7,, Text.FormatContents.tbDateYM)
        Public 得意先コード As New Field.ItemData("得意先コード", "", Field.ItemType.DataItem, True, 6,, Text.FormatContents.tbCode)
        Public 工事コード As New Field.ItemData("工事コード", "", Field.ItemType.DataItem, True, 4,, Text.FormatContents.tbCode)
        Public 工事内部コード As New Field.ItemData("工事内部コード", "", Field.ItemType.DataItem, False, 10,, Text.FormatContents.tbCode)
        Public 請求番号 As New Field.ItemData("請求番号", "", Field.ItemType.DataItem, False, 10,, Text.FormatContents.tbCode)

        Public 登録日時 As New Field.ItemData("登録日時", "", Field.ItemType.DataItem, False, 20)
        Public 登録者ID As New Field.ItemData("登録者ID", "", Field.ItemType.DataItem, False, 50)
        Public 更新日時 As New Field.ItemData("更新日時", "", Field.ItemType.DataItem, False, 20)
        Public 更新者ID As New Field.ItemData("更新者ID", "", Field.ItemType.DataItem, False, 50)

        Public List(4) As NyukinDenpyoListManager

        Sub New(Security As Security)
            MyBase.New(Security)

            For i As Integer = 0 To List.Length - 1
                List(i) = New NyukinDenpyoListManager(Security)
                List(i).NoLogging = True
                List(i).Start()
            Next

            Me.AutoBackupRestore = False
        End Sub

        Protected Overrides Function DeleteMainDataSub() As Boolean
            Using db As New DbTranTableAdapters.T入金DTTableAdapter
                db.DeleteByCode(入金番号.Value)
            End Using

            Using db As New DbTranTableAdapters.T入金HDTableAdapter
                db.DeleteByCode(入金番号.Value)
            End Using

            Return True
        End Function

        Protected Overrides Sub EnterValidateSub(ByRef chk As Validator)

            ' 得意先の存在
            chk.FoundMaster(得意先コード, "得意先")

            ' 工事の存在
            Dim str工事名 As String = ""
            str工事名 = MasterName.AnyTableName("M工事", "工事コード", "工事名", 工事コード.Value, "得意先コード = '" & 得意先コード.Value.ToString().Replace("'", "''") & "'")
            chk.AnyCheck(工事コード, (str工事名 <> ""), Message.MessageText("EM_NOMASTER", 工事コード.Value))

            ' 請求済はNG
            chk.AnyCheck(請求番号, (請求番号.Value = ""), "請求済の伝票のため保存出来ません。")
        End Sub

        Protected Overrides Function ReadMainDataSub(read_after_clear As Boolean) As Boolean
            ' HD
            If read_after_clear Then

                入金日付.Value = Format(Now, "yyyy/MM/dd")
                請求年月.Value = ""
                得意先コード.Value = ""
                工事コード.Value = ""
                工事内部コード.Value = ""
            End If

            登録日時.Value = ""
            登録者ID.Value = ""
            更新日時.Value = ""
            更新者ID.Value = ""

            Using db As New DbTranTableAdapters.T入金HDTableAdapter
                Using rs As DbTran.T入金HDDataTable = db.GetData(入金番号.Value)
                    If rs.Count > 0 Then
                        入金日付.Value = rs(0).入金日付
                        請求年月.Value = rs(0).請求年月
                        得意先コード.Value = rs(0).得意先コード
                        工事コード.Value = rs(0).工事コード
                        工事内部コード.Value = rs(0).工事内部コード
                        請求番号.Value = rs(0).請求番号

                        登録日時.Value = rs(0).登録日時
                        登録者ID.Value = rs(0).登録者ID
                        更新日時.Value = rs(0).更新日時
                        更新者ID.Value = rs(0).更新者ID

                        EditMode.Value = Field.EditModeContents.emUpdateMode
                    Else
                        EditMode.Value = Field.EditModeContents.emInsertMode
                    End If
                End Using
            End Using

            ' DT
            For i As Integer = 0 To List.Length - 1
                List(i).Start()
            Next

            Using db As New DbTranTableAdapters.T入金DTTableAdapter
                Using rs As DbTran.T入金DTDataTable = db.GetData(入金番号.Value)
                    For i As Integer = 0 To rs.Count - 1
                        List(i).行番号.Value = rs(i).行番号
                        List(i).入金区分.Value = rs(i).入金区分
                        List(i).入金金額.Value = rs(i).入金金額
                        List(i).備考.Value = rs(i).備考
                    Next
                End Using
            End Using

            Return True
        End Function

        Public Overrides Sub Start()
            MyBase.Start()

            For i As Integer = 0 To List.Length - 1
                List(i).Start()
            Next
        End Sub

        Public ReadOnly Property GetDataView() As DataView
            Get
                Dim db As New DbCalcTableAdapters.F工事残高TableAdapter
                Dim rs As New DbCalc.F工事残高DataTable

                rs = db.GetData(得意先コード.Value, 工事内部コード.Value)
                Return rs.DefaultView
            End Get
        End Property

        Public Overrides ReadOnly Property Title As String
            Get
                Return "入金入力"
            End Get
        End Property

        Protected Overrides Function WriteMainDataSub() As Boolean
            Try
                Using tran As New Transactions.TransactionScope(Transactions.TransactionScopeOption.Required, TimeSpan.Zero)
                    Dim strNow As String = Format(Now, "yyyy/MM/dd HH:mm:ss")
                    'Dim aaa As String = Me.Security.se

                    ' HD
                    Using db As New DbTranTableAdapters.T入金HDTableAdapter
                        Using rs As DbTran.T入金HDDataTable = db.GetData(入金番号.Value)
                            If rs.Count > 0 Then

                                rs(0).入金番号 = 入金番号.Value
                                rs(0).入金日付 = 入金日付.Value
                                rs(0).請求年月 = 請求年月.Value
                                rs(0).得意先コード = 得意先コード.Value
                                rs(0).工事コード = 工事コード.Value
                                rs(0).工事内部コード = 工事内部コード.Value
                                rs(0).請求番号 = 請求番号.Value

                                rs(0).更新日時 = strNow
                                rs(0).更新者ID = Security.UserID
                                db.Update(rs)
                            Else
                                ' 商品コードが空白の場合は最新コードを取得
                                If 入金番号.Value = "" Then
                                    入金番号.Value = MasterCode.AnyTableNewNumber("T入金HD", "入金番号")
                                End If

                                db.Insert(
                                入金番号.Value,
                                入金日付.Value,
                                請求年月.Value,
                                得意先コード.Value,
                                工事コード.Value,
                                工事内部コード.Value,
                                請求番号.Value,
                                strNow,
                                Security.UserID,
                                strNow,
                                Security.UserID)
                            End If
                        End Using
                    End Using

                    ' DT
                    Using db As New DbTranTableAdapters.T入金DTTableAdapter
                        ' 事前削除
                        db.DeleteByCode(入金番号.Value)

                        Dim int行NO As Integer = 1

                        For i As Integer = 0 To List.Length - 1
                            If List(i).入金金額.Value.ToString <> "0" Then
                                int行NO = int行NO + i

                                db.Insert(
                                入金番号.Value,
                                int行NO,
                                CShort(Text.CVal(List(i).入金区分.Value)),
                                Text.CVal(List(i).入金金額.Value),
                                List(i).備考.Value)

                            End If
                        Next
                    End Using

                    tran.Complete()
                End Using

            Catch ex As Exception
                Me.LastError = ex.Message
                Return False
            End Try

            Return True
        End Function

    End Class
End Namespace
