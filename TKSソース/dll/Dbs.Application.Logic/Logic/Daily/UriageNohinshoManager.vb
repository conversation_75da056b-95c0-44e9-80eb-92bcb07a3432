﻿Imports System
Imports System.Text
Imports Dbs.Asphalt.Core
Imports Dbs.Asphalt.Core.BaseClass
Imports Dbs.Asphalt.Core.SystemLogic
Imports Dbs.Asphalt.Core.Common
Imports Dbs.Asphalt.Database
Imports Dbs.Application.Logic.Database

Namespace Logic.daily

    ''' <summary>
    ''' 印刷処理サンプルマネージャ
    ''' (UIからの使用方法はweb/extension/ExtensionSample1.ascxを参照)
    ''' </summary>
    Public Class UriageNohinshoManager
        Inherits PrinterLogicBase

        ' 使用するフィールドを初期化
        Public 得意先コード As New Field.ItemData("得意先コード", "", Field.ItemType.DataItem, False, 6,, Text.FormatContents.tbCode)
        Public 工事コード As New Field.ItemData("工事コード", "", Field.ItemType.DataItem, False, 4,, Text.FormatContents.tbCode)
        Public 工事内部コード As New Field.ItemData("工事内部コード", "", Field.ItemType.DataItem, False, 10,, Text.FormatContents.tbCode)
        Public 搬入日付F As New Field.ItemData("搬入日付F", Format(Now, "yyyy/MM/dd"), Field.ItemType.DataItem, False, 10,, Text.FormatContents.tbDate)
        Public 搬入日付T As New Field.ItemData("搬入日付T", Format(Now, "yyyy/MM/dd"), Field.ItemType.DataItem, False, 10,, Text.FormatContents.tbDate)
        Public 売上番号 As New Field.ItemData("売上番号", "", Field.ItemType.KeyItem, False, 10,, Text.FormatContents.tbCode)

        Public 売上リスト As New Field.ItemData("売上リスト", "", Field.ItemType.KeyItem, False, 4000,, Text.FormatContents.tbNone)

        Sub New(security As Security)
            MyBase.New(security)
        End Sub

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' フィールドの検証処理をを記述します。
        ''' フィールドの検証処理。基底クラスにて、WriteMainDataSubが呼ばれる前にコールされます。
        ''' 基本的な検証(文字数・書式・区分値)は基底クラスで検証されます。
        ''' </summary>
        '''------------------------------------------------------------------------------
        Protected Overrides Sub EnterValidateSub(ByRef chk As Validator)

            '----検証サンプル--------------------------------------------------------------
            ' コードの開始終了の範囲チェック
            chk.FromTo(搬入日付F, 搬入日付T)
            '----検証サンプル--------------------------------------------------------------

        End Sub

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' 印刷データの作成処理を記述します。
        ''' </summary>
        ''' <param name="reportfilename">レポートファイルの名前が渡される場合があります。</param>
        '''------------------------------------------------------------------------------
        Protected Overrides Function PrintMainDataSub(Optional reportfilename As String = "") As Boolean

            Dim strファイル名 As String = "納品書_" & Format(Now, "HHmmss")

            ' セクションレポートの場合はインスタンス生成
            Dim rep As New UriageNohinshoBase

            Using db As New DbReportTableAdapters.R納品書TableAdapter
                Using rs As DbReport.R納品書DataTable = db.GetData(売上リスト.Value)
                    If rs.Count > 0 Then
                        rep.Sub_db = rs    ' ｻﾌﾞﾚﾎﾟｰﾄのDataSouceの為に設定する

                        ' 基底クラスにレポートオブジェクトとデータを渡すだけ
                        Return MakeReport(rep, strファイル名, rs.DefaultView)
                    Else
                        ' データがない場合はエラーメッセージをセットして戻る
                        LastError = Message.MessageText("EM_NODATA")
                        Return False
                    End If
                End Using
            End Using

        End Function

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' マネージャの名称(メニューや画面タイトル、権限モジュールリストで共通使用されます)
        ''' </summary>
        '''------------------------------------------------------------------------------
        Public Overrides ReadOnly Property Title As String
            Get
                Return "納品書発行"
            End Get
        End Property
    End Class

End Namespace
