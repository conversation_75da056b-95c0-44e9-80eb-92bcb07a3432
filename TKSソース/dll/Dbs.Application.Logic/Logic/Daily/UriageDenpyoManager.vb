﻿Imports Dbs.Asphalt.Core
Imports Dbs.Asphalt.Core.Common
Imports Dbs.Asphalt.Core.SystemLogic
Imports Dbs.Asphalt.Core.BaseClass
Imports Dbs.Asphalt.Database
Imports Dbs.Application.Logic.Database

Namespace Logic.daily

    ''' <summary>
    ''' 登録処理サンプルマネージャ
    ''' </summary>
    Public Class UriageDenpyoListManager
        Inherits RegisterLogicBase

        ' 使用するフィールドを初期化
        Public 売上番号 As New Field.ItemData("売上番号", "", Field.ItemType.KeyItem, False, 10)
        Public 行番号 As New Field.ItemData("行番号", "", Field.ItemType.KeyItem, False, 3)
        Public 計算区分 As New Field.ItemData("計算区分", "", Field.ItemType.DataItem, False, "計算区分", False)
        Public 商品コード As New Field.ItemData("商品コード", "", Field.ItemType.DataItem, True, 6,, Text.FormatContents.tbCode)
        Public 商品名 As New Field.ItemData("商品名", "", Field.ItemType.DataItem, False, 100)
        Public 数量 As New Field.ItemData("数量", "", Field.ItemType.DataItem, False, 9,, Text.FormatContents.tbCurrency)
        Public 単価 As New Field.ItemData("単価", "", Field.ItemType.DataItem, False, 9,, Text.FormatContents.tbCurrency)
        Public 基本料金 As New Field.ItemData("基本料金", "", Field.ItemType.DataItem, False, 9,, Text.FormatContents.tbCurrency)
        Public 金額 As New Field.ItemData("金額", "", Field.ItemType.DataItem, False, 9,, Text.FormatContents.tbCurrency)
        Public 単位区分 As New Field.ItemData("単位区分", "", Field.ItemType.DataItem, False, "単位区分", False)
        Public 備考明細 As New Field.ItemData("備考明細", "", Field.ItemType.DataItem, False, 100)
        Public 備考社内 As New Field.ItemData("備考社内", "", Field.ItemType.DataItem, False, 100)
        Public 備考台帳 As New Field.ItemData("備考台帳", "", Field.ItemType.DataItem, False, 200)
        Public 搬入番号 As New Field.ItemData("搬入番号", "", Field.ItemType.DataItem, False, 10)
        Public 開始日付 As New Field.ItemData("開始日付", "", Field.ItemType.DataItem, False, 10,, Text.FormatContents.tbDate)
        Public 終了日付 As New Field.ItemData("終了日付", "", Field.ItemType.DataItem, False, 10,, Text.FormatContents.tbDate)
        Public 返納日付 As New Field.ItemData("返納日付", "", Field.ItemType.DataItem, False, 10,, Text.FormatContents.tbDate)

        Public 日数 As New Field.ItemData("日数", "", Field.ItemType.DataItem, False, 10,, Text.FormatContents.tbCurrency)
        Public 伝票区分 As New Field.ItemData("伝票区分", "", Field.ItemType.DataItem, False, "伝票区分", False)

        Sub New(Security As Security)
            MyBase.New(Security)

            Me.AutoBackupRestore = False
        End Sub

        Public Overrides Sub Start()
            MyBase.Start()

            売上番号.Value = ""
            行番号.Value = ""
            計算区分.Value = ""
            商品コード.Value = ""
            商品名.Value = ""
            数量.Value = ""
            単価.Value = ""
            基本料金.Value = ""
            金額.Value = ""
            単位区分.Value = ""
            備考明細.Value = ""
            備考社内.Value = ""
            備考台帳.Value = ""
            搬入番号.Value = ""
            開始日付.Value = ""
            終了日付.Value = ""
            返納日付.Value = ""
            日数.Value = ""
            伝票区分.Value = ""
        End Sub

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' DBからフィールドに読み込む処理を記述します。
        ''' エラーが発生した場合はLastErrorにエラーメッセージを格納しFalseを返します。
        ''' </summary>
        ''' <param name="read_after_clear">読み込む前にフィールドをクリアするかどうか</param>
        '''------------------------------------------------------------------------------
        Protected Overrides Function ReadMainDataSub(read_after_clear As Boolean) As Boolean
            Return True
        End Function

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' フィールドからDBに書き込み処理を記述します。
        ''' エラーが発生した場合はLastErrorにエラーメッセージを格納しFalseを返します。
        ''' </summary>
        '''------------------------------------------------------------------------------
        Protected Overrides Function WriteMainDataSub() As Boolean

            ' DT
            Using db As New DbTranTableAdapters.T売上DTTableAdapter

                db.Insert(
                売上番号.Value,
                Text.CVal(行番号.Value),
                CShort(Text.CVal(計算区分.Value)),
                商品コード.Value,
                商品名.Value,
                Text.CVal(数量.Value),
                Text.CVal(単価.Value),
                Text.CVal(基本料金.Value),
                Text.CVal(金額.Value),
                CShort(Text.CVal(単位区分.Value)),
                備考明細.Value,
                備考社内.Value,
                備考台帳.Value,
                搬入番号.Value,
                開始日付.Value,
                終了日付.Value,
                返納日付.Value)

            End Using

            Return True
        End Function

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' DBから削除する処理を記述します。
        ''' エラーが発生した場合はLastErrorにエラーメッセージを格納しFalseを返します。
        ''' </summary>
        '''------------------------------------------------------------------------------
        Protected Overrides Function DeleteMainDataSub() As Boolean

            Using db As New DbTranTableAdapters.T売上DTTableAdapter
                db.DeleteByRowNo(売上番号.Value, 行番号.Value)
            End Using

            Return True
        End Function

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' フィールドの検証処理をを記述します。
        ''' フィールドの検証処理。基底クラスにて、WriteMainDataSubが呼ばれる前にコールされます。
        ''' 基本的な検証(文字数・書式・区分値)は基底クラスで検証されます。
        ''' </summary>
        '''------------------------------------------------------------------------------
        Protected Overrides Sub EnterValidateSub(ByRef chk As Dbs.Asphalt.Core.Common.Validator)

            '----検証サンプル--------------------------------------------------------------
            ' 担当者マスターに登録済みかどうか
            'chk.FoundMaster(担当者コード, "担当者")
            '----検証サンプル--------------------------------------------------------------
            chk.FoundMaster(商品コード, "商品")

            ' 保証期間以上の日数を指定しているか ※継続ﾃﾞｰﾀは検査しない
            If 伝票区分.Value <> 4 Then

                Dim dec保証日数 As Decimal = 0
                Dim dec売上日数 As Decimal = 0

                Using db As New DbMasterTableAdapters.M商品TableAdapter
                    Using rs As DbMaster.M商品DataTable = db.GetData(商品コード.Value)
                        If rs.Count > 0 Then
                            dec保証日数 = rs(0).保証日数
                        End If
                    End Using
                End Using

                If 開始日付.Value <> "" And 終了日付.Value <> "" Then
                    dec売上日数 = DateAndTime.DateDiff(DateInterval.DayOfYear, Text.CDateEx(開始日付.Value), Text.CDateEx(終了日付.Value)) + 1
                End If

                If dec保証日数 <> 0 And dec売上日数 <> 0 Then
                    If dec売上日数 < dec保証日数 Then
                        chk.AnyCheck(終了日付, False, "保証日数より短い期間が指定されています。(保証日数：" & Format(dec保証日数, "#,##0") & "日)")
                        開始日付.IsError = True
                    End If
                End If

            End If

            ' 返納伝票の場合は明細の返納日付を必須とする
            If 伝票区分.Value = 3 Then
                chk.AnyCheck(返納日付, (返納日付.Value <> ""), "返納のデータは返納日付を入力してください。")
            End If

            ' 搬入数量をを超える返納数はNG
            If 伝票区分.Value = 3 Then
                Using db As New DbCalcTableAdapters.Fリース商品残数TableAdapter
                    Using rs As DbCalc.Fリース商品残数DataTable = db.GetData(搬入番号.Value, 売上番号.Value, 商品コード.Value)

                        Dim dec残数量 As Decimal = rs(0).残数量
                        Dim dec入力数 As Decimal = Text.CVal(数量.Value)

                        chk.AnyCheck(数量, (dec残数量 >= dec入力数), "搬入数を超える返納数は入力出来ません。(搬入数:" & Format(dec残数量, "#,##0") & ")")
                    End Using
                End Using
            End If
        End Sub

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' 商品情報の取得
        ''' </summary>
        '''------------------------------------------------------------------------------
        Public Function GetShohinMaster() As Boolean

            Using db As New DbMasterTableAdapters.M商品TableAdapter
                Using rs As DbMaster.M商品DataTable = db.GetData(商品コード.Value)
                    If rs.Count > 0 Then
                        商品名.Value = rs(0).商品名
                        単価.Value = rs(0).単価
                        基本料金.Value = rs(0).基本料金
                        単位区分.Value = rs(0).単位区分
                    End If
                End Using
            End Using

            ' 金額計算
            CalcKingaku()

            Return True
        End Function

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' 商品名でのｺｰﾄﾞ取得
        ''' </summary>
        '''------------------------------------------------------------------------------
        Public Function GetShohinName() As Boolean

            Using db As New DbMasterTableAdapters.M商品TableAdapter
                Using rs As DbMaster.M商品DataTable = db.GetData(商品名.Value)
                    If rs.Count > 0 Then
                        商品コード.Value = rs(0).商品コード

                        GetShohinMaster()
                    End If
                End Using
            End Using

            Return True
        End Function

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' 金額計算
        ''' </summary>
        '''------------------------------------------------------------------------------
        Public Function CalcKingaku() As Boolean

            ' 金額 = (数量 * 単価 * 日数) + (基本料金 * 数量)
            ' 日数・・・開始日付～終了日付
            Dim dec数量 As Decimal = Text.CVal(数量.Value)
            Dim dec単価 As Decimal = Text.CVal(単価.Value)
            Dim dec料金 As Decimal = Text.CVal(基本料金.Value)

            Dim dec日数 As Decimal = CalcNissu(開始日付.Value, 終了日付.Value)

            ' 日付未入力の場合は0で返って来るので
            If dec日数 = 0 Then : dec日数 = 1 : End If

            Dim dec金額 As Decimal = 0
            Select Case 計算区分.Value
                Case 0    ' 売切
                    dec金額 += dec数量 * dec単価

                Case 1    ' 月額
                    dec金額 += dec数量 * dec単価 * dec日数
                    dec金額 += dec数量 * dec料金

                Case 2    ' 期間
                    dec金額 += dec数量 * dec単価
                    dec金額 += dec数量 * dec料金

                Case 9    ' 返納
                    dec金額 = 0
            End Select

            金額.Value = dec金額

            Return True
        End Function

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' 日数の表示
        ''' </summary>
        '''------------------------------------------------------------------------------
        Public Function GetNisu() As Boolean

            Dim dec日数 As Decimal = CalcNissu(開始日付.Value, 終了日付.Value)
            日数.Value = ""

            If dec日数 <> 0 Then
                日数.Value = dec日数
            End If

            Return True
        End Function

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' 日数計算
        ''' </summary>
        '''------------------------------------------------------------------------------
        Public Function CalcNissu(ByVal 日付F As String, ByVal 日付T As String) As Decimal

            Dim dec日数 As Decimal = 0

            If 日付F <> "" And 日付T <> "" Then
                dec日数 = DateAndTime.DateDiff(DateInterval.DayOfYear, Text.CDateEx(開始日付.Value), Text.CDateEx(終了日付.Value)) + 1
            End If

            Return dec日数
        End Function

        ''''------------------------------------------------------------------------------
        '''' <summary>
        '''' 売上明細の呼び出し(商品名での検索)
        '''' 返納伝票で使用
        '''' </summary>
        ''''------------------------------------------------------------------------------
        'Public Function ReadMeisai(ByVal str搬入日付 As String, ByVal str得意先コード As String) As Boolean

        '    Dim str売上行番号 As String = 商品名.Value
        '    Dim str売上番号 As String = ""
        '    Dim int行NO As Integer = 0

        '    ' 各日付の設定
        '    Dim str終了日付 As String = str搬入日付
        '    Dim str返納日付 As String = str搬入日付

        '    ' 開始日付は締日ごとの月始め
        '    Dim str開始日付 As String = ""
        '    Dim int締日 As Integer = 0
        '    int締日 = MasterName.AnyTableName("M得意先", "得意先コード", "締日区分", str得意先コード)

        '    Select Case int締日
        '        Case 99
        '            str開始日付 = str返納日付.Substring(0, 7) & "/01"
        '        Case Else
        '            str開始日付 = str返納日付.Substring(0, 7) & "/" & Right("00" & (int締日 + 1).ToString, 2)

        '            ' 2024/09/06 > 2024/09/21
        '            If str返納日付 > str開始日付 Then
        '                str開始日付 = Format(Text.CDateEx(str開始日付).AddMonths(-1), "yyyy/MM/dd")
        '            End If
        '    End Select

        '    ' ｷｰ値取得
        '    str売上番号 = str売上行番号.Split("-")(0).ToString
        '    int行NO = Text.CVal(str売上行番号.Split("-")(1))

        '    ' ﾃﾞｰﾀを読み込む前に初期化
        '    Me.Start()

        '    Using db As New DbMasterTableAdapters.オートコンプリートTableAdapter
        '        Using rs As DbMaster.オートコンプリートDataTable = db.LeaseItem("", "", "")
        '            If rs.Count > 0 Then
        '                Dim aaa As String = rs(0).リスト
        '            End If
        '        End Using
        '    End Using

        '    ' T売上DTを読み込み
        '    Using db As New DbTranTableAdapters.T売上DTTableAdapter
        '        Using rs As DbTran.T売上DTDataTable = db.GetDataGyoNO(str売上番号, int行NO)

        '            If rs.Count > 0 Then
        '                計算区分.Value = 9
        '                商品コード.Value = rs(0).商品コード
        '                商品名.Value = rs(0).商品名
        '                数量.Value = 0
        '                単価.Value = rs(0).単価
        '                基本料金.Value = rs(0).基本料金
        '                金額.Value = 0
        '                単位区分.Value = rs(0).単位区分
        '                搬入番号.Value = rs(0).売上番号

        '                開始日付.Value = str開始日付
        '                終了日付.Value = str終了日付
        '                返納日付.Value = str返納日付
        '                GetNisu()
        '            End If
        '        End Using
        '    End Using

        '    Return True
        'End Function

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' マネージャの名称(メニューや画面タイトル、権限モジュールリストで共通使用されます)
        ''' </summary>
        '''------------------------------------------------------------------------------
        Public Overrides ReadOnly Property Title As String
            Get
                Return ""
            End Get
        End Property
    End Class

    Public Class UriageDenpyoManager
        Inherits RegisterLogicBase

        Public 売上番号 As New Field.ItemData("売上番号", "", Field.ItemType.KeyItem, False, 10,, Text.FormatContents.tbCode)
        Public 搬入日付 As New Field.ItemData("搬入日付", "", Field.ItemType.DataItem, True, 10,, Text.FormatContents.tbDate)
        Public 伝票区分 As New Field.ItemData("伝票区分", "", Field.ItemType.DataItem, True, "伝票区分", False)
        Public 得意先コード As New Field.ItemData("得意先コード", "", Field.ItemType.DataItem, True, 6,, Text.FormatContents.tbCode)
        Public 工事コード As New Field.ItemData("工事コード", "", Field.ItemType.DataItem, True, 4,, Text.FormatContents.tbCode)
        Public 工事内部コード As New Field.ItemData("工事内部コード", "", Field.ItemType.DataItem, False, 10,, Text.FormatContents.tbCode)
        Public 摘要1 As New Field.ItemData("摘要1", "", Field.ItemType.DataItem, False, 100)
        Public 摘要2 As New Field.ItemData("摘要2", "", Field.ItemType.DataItem, False, 100)
        Public 備考 As New Field.ItemData("備考", "", Field.ItemType.DataItem, False, 100)
        Public 伝票発行区分 As New Field.ItemData("伝票発行区分", "", Field.ItemType.DataItem, False, "伝票発行区分", False)
        Public 請求番号 As New Field.ItemData("請求番号", "", Field.ItemType.DataItem, False, 10,, Text.FormatContents.tbCode)

        Public 登録日時 As New Field.ItemData("登録日時", "", Field.ItemType.DataItem, False, 20)
        Public 登録者ID As New Field.ItemData("登録者ID", "", Field.ItemType.DataItem, False, 50)
        Public 更新日時 As New Field.ItemData("更新日時", "", Field.ItemType.DataItem, False, 20)
        Public 更新者ID As New Field.ItemData("更新者ID", "", Field.ItemType.DataItem, False, 50)

        ' 売上作成区分→1：売上入力画面、2：定期処理画面での作成
        ' 作成する画面でエラー検査を分けたいので追加
        Public 売上作成区分 As New Field.ItemData("売上作成区分", "", Field.ItemType.DataItem, False, 1)

        Public List(99) As UriageDenpyoListManager

        Sub New(Security As Security)
            MyBase.New(Security)

            For i As Integer = 0 To List.Length - 1
                List(i) = New UriageDenpyoListManager(Security)
                List(i).NoLogging = True
                List(i).Start()
            Next

            Me.AutoBackupRestore = False
        End Sub

        Protected Overrides Function DeleteMainDataSub() As Boolean

            Try
                Using tran As New Transactions.TransactionScope(Transactions.TransactionScopeOption.Required, TimeSpan.Zero)

                    ' HD
                    Using db As New DbTranTableAdapters.T売上HDTableAdapter
                        db.DeleteByCode(売上番号.Value)
                    End Using

                    ' DT
                    Using db As New DbTranTableAdapters.T売上DTTableAdapter
                        Using rs As DbTran.T売上DTDataTable = db.GetData(売上番号.Value)
                            For i As Integer = 0 To rs.Count - 1
                                List(i).売上番号.Value = rs(i).売上番号
                                List(i).行番号.Value = rs(i).行番号

                                If Not List(i).DeleteMainData() Then
                                    LastError = List(i).LastError
                                    Return False
                                End If
                            Next
                        End Using
                    End Using

                    tran.Complete()
                End Using

            Catch ex As Exception
                Me.LastError = ex.Message
                Return False
            End Try

            Return True
        End Function

        Protected Overrides Sub EnterValidateSub(ByRef chk As Validator)

            ' 得意先の存在
            chk.FoundMaster(得意先コード, "得意先")

            ' 工事の存在
            Dim str工事名 As String = ""
            'str工事名 = MasterName.AnyTableName("M工事", "工事コード", "工事名", 工事コード.Value, "得意先コード = '" & 得意先コード.Value.ToString().Replace("'", "''") & "'")
            str工事名 = MasterName.AnyTableName("M工事", "工事内部コード", "工事名", 工事内部コード.Value)
            chk.AnyCheck(工事コード, (str工事名 <> ""), Message.MessageText("EM_NOMASTER", 工事コード.Value))

            ' 請求済はNG
            chk.AnyCheck(請求番号, (請求番号.Value = ""), "請求済の伝票のため保存出来ません。")

            ' 伝票区分：継続は新規作成NG　(定期処理でのは作成時は検査しない)
            If 売上作成区分.Value <> "2" Then
                If EditMode.Value = Field.EditModeContents.emInsertMode Then
                    chk.AnyCheck(伝票区分, (伝票区分.Value <> "4"), "継続のデータは新規での伝票保存は出来ません。")
                End If
            End If

        End Sub

        Protected Overrides Function ReadMainDataSub(read_after_clear As Boolean) As Boolean
            ' HD
            If read_after_clear Then

                搬入日付.Value = Format(Now, "yyyy/MM/dd")
                伝票区分.Value = "1"
                得意先コード.Value = ""
                工事コード.Value = ""
                工事内部コード.Value = ""
                摘要1.Value = ""
                摘要2.Value = ""
                備考.Value = ""
                伝票発行区分.Value = "0"
                請求番号.Value = ""
            End If

            登録日時.Value = ""
            登録者ID.Value = ""
            更新日時.Value = ""
            更新者ID.Value = ""

            売上作成区分.Value = "1"

            Using db As New DbTranTableAdapters.T売上HDTableAdapter
                Using rs As DbTran.T売上HDDataTable = db.GetData(売上番号.Value)
                    If rs.Count > 0 Then
                        売上番号.Value = rs(0).売上番号
                        搬入日付.Value = rs(0).搬入日付
                        伝票区分.Value = rs(0).伝票区分
                        得意先コード.Value = rs(0).得意先コード
                        工事コード.Value = rs(0).工事コード
                        工事内部コード.Value = rs(0).工事内部コード
                        摘要1.Value = rs(0).摘要1
                        摘要2.Value = rs(0).摘要2
                        備考.Value = rs(0).備考
                        伝票発行区分.Value = rs(0).伝票発行区分
                        請求番号.Value = rs(0).請求番号

                        登録日時.Value = rs(0).登録日時
                        登録者ID.Value = rs(0).登録者ID
                        更新日時.Value = rs(0).更新日時
                        更新者ID.Value = rs(0).更新者ID

                        EditMode.Value = Field.EditModeContents.emUpdateMode
                    Else
                        EditMode.Value = Field.EditModeContents.emInsertMode
                    End If
                End Using
            End Using

            ' DT
            For i As Integer = 0 To List.Length - 1
                List(i).Start()
            Next

            Using db As New DbTranTableAdapters.T売上DTTableAdapter
                Using rs As DbTran.T売上DTDataTable = db.GetData(売上番号.Value)
                    For i As Integer = 0 To rs.Count - 1
                        List(i).売上番号.Value = rs(i).売上番号
                        List(i).行番号.Value = rs(i).行番号
                        List(i).計算区分.Value = rs(i).計算区分
                        List(i).商品コード.Value = rs(i).商品コード
                        List(i).商品名.Value = rs(i).商品名
                        List(i).数量.Value = rs(i).数量
                        List(i).単価.Value = rs(i).単価
                        List(i).基本料金.Value = rs(i).基本料金
                        List(i).金額.Value = rs(i).金額
                        List(i).単位区分.Value = rs(i).単位区分
                        List(i).備考明細.Value = rs(i).備考明細
                        List(i).備考社内.Value = rs(i).備考社内
                        List(i).備考台帳.Value = rs(i).備考台帳
                        List(i).搬入番号.Value = rs(i).搬入番号
                        List(i).開始日付.Value = rs(i).開始日付
                        List(i).終了日付.Value = rs(i).終了日付
                        List(i).返納日付.Value = rs(i).返納日付
                        List(i).GetNisu()
                    Next
                End Using
            End Using

            Return True
        End Function

        Public Overrides Sub Start()
            MyBase.Start()

            For i As Integer = 0 To List.Length - 1
                List(i).Start()
            Next
        End Sub

        Public Overrides ReadOnly Property Title As String
            Get
                Return "売上入力"
            End Get
        End Property

        Protected Overrides Function WriteMainDataSub() As Boolean
            Try
                Using tran As New Transactions.TransactionScope(Transactions.TransactionScopeOption.Required, TimeSpan.Zero)
                    Dim strNow As String = Format(Now, "yyyy/MM/dd HH:mm:ss")

                    ' HD
                    Using db As New DbTranTableAdapters.T売上HDTableAdapter
                        Using rs As DbTran.T売上HDDataTable = db.GetData(売上番号.Value)
                            If rs.Count > 0 Then

                                rs(0).売上番号 = 売上番号.Value
                                rs(0).搬入日付 = 搬入日付.Value
                                rs(0).伝票区分 = 伝票区分.Value
                                rs(0).得意先コード = 得意先コード.Value
                                rs(0).工事コード = 工事コード.Value
                                rs(0).工事内部コード = 工事内部コード.Value
                                rs(0).摘要1 = 摘要1.Value
                                rs(0).摘要2 = 摘要2.Value
                                rs(0).備考 = 備考.Value
                                rs(0).伝票発行区分 = 伝票発行区分.Value
                                rs(0).請求番号 = 請求番号.Value

                                rs(0).更新日時 = strNow
                                rs(0).更新者ID = Security.UserID
                                db.Update(rs)
                            Else
                                ' 商品コードが空白の場合は最新コードを取得
                                If 売上番号.Value = "" Then
                                    売上番号.Value = MasterCode.AnyTableNewNumber("T売上HD", "売上番号")
                                End If

                                db.Insert(
                                売上番号.Value,
                                搬入日付.Value,
                                CShort(Text.CVal(伝票区分.Value)),
                                得意先コード.Value,
                                工事コード.Value,
                                工事内部コード.Value,
                                摘要1.Value,
                                摘要2.Value,
                                備考.Value,
                                CShort(Text.CVal(伝票発行区分.Value)),
                                請求番号.Value,
                                strNow,
                                Security.UserID,
                                strNow,
                                Security.UserID)
                            End If
                        End Using
                    End Using

                    ' DT
                    ' 事前削除
                    Using db As New DbTranTableAdapters.T売上DTTableAdapter
                        db.DeleteByCode(売上番号.Value)
                    End Using

                    Dim int行NO As Integer = 1

                    For i As Integer = 0 To List.Length - 1
                        If List(i).商品コード.Value.ToString <> "" Then
                            int行NO = int行NO + i

                            List(i).売上番号.Value = 売上番号.Value
                            List(i).行番号.Value = int行NO
                            List(i).伝票区分.Value = CShort(Text.CVal(伝票区分.Value))

                            If Not List(i).WriteMainData() Then
                                LastError = List(i).LastError
                                Return False
                            End If

                        End If
                    Next

                    tran.Complete()
                End Using

            Catch ex As Exception
                Me.LastError = ex.Message
                Return False
            End Try

            Return True
        End Function

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' 売上明細の呼び出し
        ''' 返納伝票で使用
        ''' </summary>
        '''------------------------------------------------------------------------------
        Public Function ReadMeisai(ByVal str売上明細リスト As String) As Boolean
            Dim str売上番号 As String = ""
            Dim int行NO As Integer = 0

            ' 各日付の設定
            Dim str終了日付 As String = 搬入日付.Value
            Dim str返納日付 As String = 搬入日付.Value

            ' 開始日付は締日ごとの月始め
            Dim str開始日付 As String = ""
            Dim int締日 As Integer = 0
            int締日 = MasterName.AnyTableName("M得意先", "得意先コード", "締日区分", 得意先コード.Value)

            Select Case int締日
                Case 99
                    str開始日付 = str返納日付.Substring(0, 7) & "/01"
                Case Else
                    str開始日付 = str返納日付.Substring(0, 7) & "/" & Right("00" & (int締日 + 1).ToString, 2)

                    ' 2024/09/06 > 2024/09/21
                    If str返納日付 > str開始日付 Then
                        str開始日付 = Format(Text.CDateEx(str開始日付).AddMonths(-1), "yyyy/MM/dd")
                    End If
            End Select

            For Each str売上行番号 As String In str売上明細リスト.Split(",")
                ' ｷｰ値取得
                str売上番号 = str売上行番号.Split("-")(0).ToString
                int行NO = Text.CVal(str売上行番号.Split("-")(1))

                For i As Integer = 0 To List.Length - 1

                    ' 商品コードが入力されている場合
                    If List(i).商品コード.Value.ToString <> "" Then
                        Continue For
                    End If

                    ' ﾃﾞｰﾀを読み込む前に初期化
                    List(i).Start()

                    ' T売上DTを読み込み
                    Using db As New DbTranTableAdapters.T売上DTTableAdapter
                        Using rs As DbTran.T売上DTDataTable = db.GetDataGyoNO(str売上番号, int行NO)

                            If rs.Count > 0 Then
                                'List(i).売上番号.Value = rs(0).売上番号
                                'List(i).行番号.Value = rs(0).行番号
                                List(i).計算区分.Value = 9
                                List(i).商品コード.Value = rs(0).商品コード
                                List(i).商品名.Value = rs(0).商品名
                                List(i).数量.Value = 0
                                List(i).単価.Value = rs(0).単価
                                List(i).基本料金.Value = rs(0).基本料金
                                List(i).金額.Value = 0
                                List(i).単位区分.Value = rs(0).単位区分
                                'List(i).備考明細.Value = rs(0).備考明細
                                'List(i).備考社内.Value = rs(0).備考社内
                                'List(i).備考台帳.Value = rs(0).備考台帳
                                List(i).搬入番号.Value = rs(0).売上番号

                                List(i).開始日付.Value = str開始日付
                                List(i).終了日付.Value = str終了日付
                                List(i).返納日付.Value = str返納日付
                                List(i).GetNisu()
                            End If
                        End Using
                    End Using

                    Exit For
                Next
            Next

            Return True
        End Function

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' 商品の一括選択
        ''' </summary>
        '''------------------------------------------------------------------------------
        Public Function SetShouhinList(ByVal str選択値 As String) As Boolean

            For Each str商品コード As String In str選択値.Split(",")
                For i As Integer = 0 To List.Length - 1

                    ' 商品コードが入力されている場合
                    If List(i).商品コード.Value.ToString <> "" Then
                        Continue For
                    End If

                    ' ﾃﾞｰﾀを読み込む前に初期化
                    List(i).Start()

                    List(i).商品コード.Value = str商品コード
                    List(i).GetShohinMaster()

                    Exit For
                Next
            Next

            Return True
        End Function

    End Class
End Namespace
