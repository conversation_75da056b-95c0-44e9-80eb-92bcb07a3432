﻿Imports Dbs.Asphalt.Core
Imports Dbs.Asphalt.Core.Common
Imports Dbs.Asphalt.Core.SystemLogic
Imports Dbs.Asphalt.Core.BaseClass
Imports Dbs.Asphalt.Database
Imports Dbs.Application.Logic.Database

Namespace Logic.Master

    ''' <summary>
    ''' 登録処理サンプルマネージャ
    ''' </summary>
    Public Class KoujiMasterManager
        Inherits RegisterLogicBase

        ' 使用するフィールドを初期化
        Public 工事内部コード As New Field.ItemData("工事内部コード", "", Field.ItemType.KeyItem, False, 10,, Text.FormatContents.tbCode)
        Public 得意先コード As New Field.ItemData("得意先コード", "", Field.ItemType.KeyItem, True, 6,, Text.FormatContents.tbCode)
        Public 工事コード As New Field.ItemData("工事コード", "", Field.ItemType.KeyItem, False, 4,, Text.FormatContents.tbCode)
        Public 工事名 As New Field.ItemData("工事名", "", Field.ItemType.DataItem, True, 100)
        Public 工事名かな As New Field.ItemData("工事名かな", "", Field.ItemType.DataItem, False, 10)
        Public 完了区分 As New Field.ItemData("完了区分", "", Field.ItemType.DataItem, False, "完了区分", True)
        Public 住所 As New Field.ItemData("住所", "", Field.ItemType.DataItem, False, 100)
        Public 摘要1 As New Field.ItemData("摘要1", "", Field.ItemType.DataItem, False, 100)
        Public 摘要2 As New Field.ItemData("摘要2", "", Field.ItemType.DataItem, False, 100)
        Public 削除区分 As New Field.ItemData("削除区分", "", Field.ItemType.DataItem, False, "削除区分", True)
        Public 登録日時 As New Field.ItemData("登録日時", "", Field.ItemType.DataItem, False, 20)
        Public 登録者ID As New Field.ItemData("登録者ID", "", Field.ItemType.DataItem, False, 50)
        Public 更新日時 As New Field.ItemData("更新日時", "", Field.ItemType.DataItem, False, 20)
        Public 更新者ID As New Field.ItemData("更新者ID", "", Field.ItemType.DataItem, False, 50)

        Sub New(Security As Security)
            MyBase.New(Security)
        End Sub

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' DBからフィールドに読み込む処理を記述します。
        ''' エラーが発生した場合はLastErrorにエラーメッセージを格納しFalseを返します。
        ''' </summary>
        ''' <param name="read_after_clear">読み込む前にフィールドをクリアするかどうか</param>
        '''------------------------------------------------------------------------------
        Protected Overrides Function ReadMainDataSub(read_after_clear As Boolean) As Boolean
            If read_after_clear Then

                工事名.Value = ""
                工事名かな.Value = ""
                完了区分.Value = 0
                住所.Value = ""
                摘要1.Value = ""
                摘要2.Value = ""
                削除区分.Value = 0
            End If

            登録日時.Value = ""
            登録者ID.Value = ""
            更新日時.Value = ""
            更新者ID.Value = ""

            Using db As New DbMasterTableAdapters.M工事TableAdapter
                Using rs As DbMaster.M工事DataTable = db.GetDataUK(得意先コード.Value, 工事コード.Value)
                    If rs.Count > 0 Then
                        工事内部コード.Value = rs(0).工事内部コード
                        得意先コード.Value = rs(0).得意先コード
                        工事コード.Value = rs(0).工事コード
                        工事名.Value = rs(0).工事名
                        工事名かな.Value = rs(0).工事名かな
                        完了区分.Value = rs(0).完了区分
                        住所.Value = rs(0).住所
                        摘要1.Value = rs(0).摘要1
                        摘要2.Value = rs(0).摘要2
                        削除区分.Value = rs(0).削除区分
                        登録日時.Value = rs(0).登録日時
                        登録者ID.Value = rs(0).登録者ID
                        更新日時.Value = rs(0).更新日時
                        更新者ID.Value = rs(0).更新者ID

                        EditMode.Value = Field.EditModeContents.emUpdateMode
                    Else
                        EditMode.Value = Field.EditModeContents.emInsertMode
                    End If
                End Using
            End Using

            Return True
        End Function

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' フィールドからDBに書き込み処理を記述します。
        ''' エラーが発生した場合はLastErrorにエラーメッセージを格納しFalseを返します。
        ''' </summary>
        '''------------------------------------------------------------------------------
        Protected Overrides Function WriteMainDataSub() As Boolean
            Dim strNow As String = Format(Now, "yyyy/MM/dd HH:mm:ss")

            Using db As New DbMasterTableAdapters.M工事TableAdapter
                Using rs As DbMaster.M工事DataTable = db.GetDataPK(工事内部コード.Value)
                    If rs.Count > 0 Then

                        rs(0).得意先コード = 得意先コード.Value
                        rs(0).工事コード = 工事コード.Value
                        rs(0).工事名 = 工事名.Value
                        rs(0).工事名かな = 工事名かな.Value
                        rs(0).完了区分 = CShort(Text.CVal(完了区分.Value))
                        rs(0).住所 = 住所.Value
                        rs(0).摘要1 = 摘要1.Value
                        rs(0).摘要2 = 摘要2.Value

                        rs(0).更新日時 = strNow
                        rs(0).更新者ID = Security.UserID
                        db.Update(rs)
                    Else
                        ' 工事コードが空白の場合は最新コードを取得
                        If 工事コード.Value = "" Then
                            工事コード.Value = MasterCode.AnyTableNewNumber("M工事", "工事コード", "得意先コード = '" & 得意先コード.Value & "'")
                        End If

                        ' 内部ｺｰﾄﾞの~
                        If 工事内部コード.Value = "" Then
                            工事内部コード.Value = MasterCode.AnyTableNewNumber("M工事", "工事内部コード")
                        End If

                        db.Insert(
                            工事内部コード.Value,
                            得意先コード.Value,
                            工事コード.Value,
                            工事名.Value,
                            工事名かな.Value,
                            CShort(Text.CVal(完了区分.Value)),
                            住所.Value,
                            摘要1.Value,
                            摘要2.Value,
                            0,
                            strNow,
                            Security.UserID,
                            strNow,
                            Security.UserID)
                    End If
                End Using
            End Using

            Return True
        End Function

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' DBから削除する処理を記述します。
        ''' エラーが発生した場合はLastErrorにエラーメッセージを格納しFalseを返します。
        ''' </summary>
        '''------------------------------------------------------------------------------
        Protected Overrides Function DeleteMainDataSub() As Boolean

            Dim strNow As String = Format(Now, "yyyy/MM/dd HH:mm:ss")

            Using db As New DbMasterTableAdapters.M工事TableAdapter
                Using rs As DbMaster.M工事DataTable = db.GetDataPK(工事内部コード.Value)
                    If rs.Count > 0 Then

                        rs(0).削除区分 = 1
                        rs(0).更新日時 = strNow
                        rs(0).更新者ID = Security.UserID
                        db.Update(rs)
                    End If
                End Using
            End Using

            Return True
        End Function

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' フィールドの検証処理をを記述します。
        ''' フィールドの検証処理。基底クラスにて、WriteMainDataSubが呼ばれる前にコールされます。
        ''' 基本的な検証(文字数・書式・区分値)は基底クラスで検証されます。
        ''' </summary>
        '''------------------------------------------------------------------------------
        Protected Overrides Sub EnterValidateSub(ByRef chk As Dbs.Asphalt.Core.Common.Validator)

            '----検証サンプル--------------------------------------------------------------
            ' 担当者マスターに登録済みかどうか
            'chk.FoundMaster(担当者コード, "担当者")
            '----検証サンプル--------------------------------------------------------------
            chk.FoundMaster(得意先コード, "得意先")

        End Sub

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' マネージャの名称(メニューや画面タイトル、権限モジュールリストで共通使用されます)
        ''' </summary>
        '''------------------------------------------------------------------------------
        Public Overrides ReadOnly Property Title As String
            Get
                Return "工事マスター"
            End Get
        End Property
    End Class

End Namespace
