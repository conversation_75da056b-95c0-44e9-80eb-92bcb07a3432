﻿Imports Dbs.Asphalt.Core
Imports Dbs.Asphalt.Core.Common
Imports Dbs.Asphalt.Core.SystemLogic
Imports Dbs.Asphalt.Core.BaseClass
Imports Dbs.Asphalt.Database
Imports Dbs.Application.Logic.Database

Namespace Logic.Master

    ''' <summary>
    ''' 税率マスターの登録・変更・削除を行う。
    ''' </summary>
    Public Class ZeirituMasterManager
        Inherits RegisterLogicBase

        Public 税種別 As New Field.ItemData("税種別", "", Field.ItemType.KeyItem, need:=True, "税種別", False)
        Public 適用開始日 As New Field.ItemData("適用開始日", "", Field.ItemType.KeyItem, True, 10,, Text.FormatContents.tbDate)
        Public 税率 As New Field.ItemData("税率", "", Field.ItemType.DataItem, False, 6,, Text.FormatContents.tbNumeric, 2)

        Private _db_Main As New DbCoreMasterTableAdapters.M税TableAdapter

        Sub New(Security As Security)
            MyBase.New(Security)

            Me.AutoBackupRestore = False
        End Sub

        Protected Overrides Sub EnterValidateSub(ByRef chk As Validator)
            Try
                適用開始日.Value = CDate(適用開始日.Value).ToString("yyyy/MM/dd")
            Catch
            End Try

            chk.DateTime(適用開始日)
        End Sub

        Public Overrides Sub Start()
            MyBase.Start()

            適用開始日.Value = ""
            税率.Value = ""
        End Sub

        Public Overrides ReadOnly Property Title As String
            Get
                Return ""
            End Get
        End Property

        Protected Overrides Function DeleteMainDataSub() As Boolean
            LastError = Message.MessageText("EM_CANNOTDEL")
            Return False
        End Function

        Protected Overrides Function ReadMainDataSub(read_after_clear As Boolean) As Boolean
            Using rs As DbCoreMaster.M税DataTable = _db_Main.GetDataByDate(CShort(Text.CVal(税種別.Value)), 適用開始日.Value)
                If read_after_clear Then
                    税率.Value = ""
                End If

                If rs.Count > 0 Then
                    税率.Value = rs(0).税率

                    EditMode.Value = Field.EditModeContents.emUpdateMode
                Else
                    EditMode.Value = Field.EditModeContents.emInsertMode
                End If
            End Using

            Return True
        End Function

        Protected Overrides Function WriteMainDataSub() As Boolean
            Using rs As DbCoreMaster.M税DataTable = _db_Main.GetData(CShort(Text.CVal(税種別.Value)), 適用開始日.Value)
                If rs.Count > 0 Then
                    rs(0).税率 = Text.CVal(税率.Value)
                    _db_Main.Update(rs)
                Else
                    _db_Main.Insert(
                        CShort(Text.CVal(税種別.Value)),
                        適用開始日.Value,
                        Text.CVal(税率.Value))
                End If
            End Using

            Return True
        End Function
    End Class

    Public Class ZeirituMasterListManager
        Inherits RegisterLogicBase

        Public 税種別 As New Field.ItemData("税種別", "", Field.ItemType.DataItem, True)

        Public List(50) As ZeirituMasterManager

        Private _db_Main As New DbCoreMasterTableAdapters.M税TableAdapter

        Sub New(Security As Security)
            MyBase.New(Security)

            For i As Integer = 0 To List.Length - 1
                List(i) = New ZeirituMasterManager(Security)
                List(i).NoLogging = True
                List(i).Start()
            Next

            Me.AutoBackupRestore = False
        End Sub

        Protected Overrides Function DeleteMainDataSub() As Boolean
            LastError = Message.MessageText("EM_CANNOTDEL")
            Return False
        End Function

        Protected Overrides Sub EnterValidateSub(ByRef chk As Validator)
            Dim nodup As Boolean = True

            For i As Integer = 0 To List.Length - 1
                If List(i).適用開始日.Value.ToString <> "" Then
                    chk.AnyCheck("", List(i).EnterValidate(), List(i).LastError)

                    ' 重複コードチェック
                    For j As Integer = 0 To List.Length - 1
                        If i <> j And List(i).適用開始日.Value = List(j).適用開始日.Value Then
                            List(i).適用開始日.IsError = True
                            List(j).適用開始日.IsError = True
                            nodup = False
                        End If
                    Next
                End If
            Next
            chk.AnyCheck("", nodup, Message.MessageText("EM_DUPCODE"))
        End Sub

        Protected Overrides Function ReadMainDataSub(read_after_clear As Boolean) As Boolean
            For i As Integer = 0 To List.Length - 1
                List(i).Start()
            Next

            Using rs As DbCoreMaster.M税DataTable = _db_Main.GetData(CShort(Text.CVal(税種別.Value)), "")
                For i As Integer = 0 To rs.Count - 1
                    List(i).適用開始日.Value = rs(i).適用開始日
                    List(i).税率.Value = rs(i).税率
                Next
            End Using
            Return True
        End Function

        Public Overrides Sub Start()
            MyBase.Start()

            For i As Integer = 0 To List.Length - 1
                List(i).Start()
            Next
        End Sub

        Public Overrides ReadOnly Property Title As String
            Get
                Return "税率設定"
            End Get
        End Property

        Protected Overrides Function WriteMainDataSub() As Boolean
            Using tran As New Transactions.TransactionScope(Transactions.TransactionScopeOption.Required, TimeSpan.Zero)
                _db_Main.ClearData(CShort(Text.CVal(税種別.Value)))
                For i As Integer = 0 To List.Length - 1
                    If List(i).適用開始日.Value.ToString <> "" Then
                        If Not List(i).WriteMainData() Then
                            LastError = List(i).LastError
                            Return False
                        End If
                    End If
                Next
                tran.Complete()
            End Using
            Return True
        End Function

        Public ReadOnly Property GetDataView() As DataView
            Get
                Dim rs As DbCoreMaster.M税DataTable = _db_Main.GetData(CShort(Text.CVal(税種別.Value)), "")
                Return rs.DefaultView
            End Get
        End Property
    End Class
End Namespace

