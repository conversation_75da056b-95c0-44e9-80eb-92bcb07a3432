﻿Imports Dbs.Asphalt.Core
Imports Dbs.Asphalt.Core.Common
Imports Dbs.Asphalt.Core.SystemLogic
Imports Dbs.Asphalt.Core.BaseClass
Imports Dbs.Asphalt.Database
Imports Dbs.Application.Logic.Database

Namespace Logic.Master

    ''' <summary>
    ''' 登録処理サンプルマネージャ
    ''' </summary>
    Public Class ShouhinMasterManager
        Inherits RegisterLogicBase

        ' 使用するフィールドを初期化
        Public 商品コード As New Field.ItemData("商品コード", "", Field.ItemType.KeyItem, False, 6,, Text.FormatContents.tbCode)
        Public 商品名 As New Field.ItemData("商品名", "", Field.ItemType.DataItem, True, 100)
        Public 商品名かな As New Field.ItemData("商品名かな", "", Field.ItemType.DataItem, False, 10)
        Public 単位区分 As New Field.ItemData("単位区分", "", Field.ItemType.DataItem, False, "単位区分", False)
        Public 基本料金 As New Field.ItemData("基本料金", "", Field.ItemType.DataItem, False, 9,, Text.FormatContents.tbCurrency, 0)
        Public 単価 As New Field.ItemData("単価", "", Field.ItemType.DataItem, False, 9,, Text.FormatContents.tbCurrency, 0)
        Public 商品分類区分 As New Field.ItemData("商品分類区分", "", Field.ItemType.DataItem, False, "商品分類区分", False)
        Public 備考 As New Field.ItemData("備考", "", Field.ItemType.DataItem, False, 100)
        Public 保証日数 As New Field.ItemData("保証日数", "", Field.ItemType.DataItem, False, 3,, Text.FormatContents.tbCurrency)
        Public 登録日時 As New Field.ItemData("登録日時", "", Field.ItemType.DataItem, False, 20)
        Public 登録者ID As New Field.ItemData("登録者ID", "", Field.ItemType.DataItem, False, 50)
        Public 更新日時 As New Field.ItemData("更新日時", "", Field.ItemType.DataItem, False, 20)
        Public 更新者ID As New Field.ItemData("更新者ID", "", Field.ItemType.DataItem, False, 50)

        Sub New(Security As Security)
            MyBase.New(Security)
        End Sub

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' DBからフィールドに読み込む処理を記述します。
        ''' エラーが発生した場合はLastErrorにエラーメッセージを格納しFalseを返します。
        ''' </summary>
        ''' <param name="read_after_clear">読み込む前にフィールドをクリアするかどうか</param>
        '''------------------------------------------------------------------------------
        Protected Overrides Function ReadMainDataSub(read_after_clear As Boolean) As Boolean
            If read_after_clear Then

                商品名.Value = ""
                商品名かな.Value = ""
                単位区分.Value = 1
                基本料金.Value = 0
                単価.Value = 0
                商品分類区分.Value = ""
                備考.Value = ""
                保証日数.Value = ""
            End If

            登録日時.Value = ""
            登録者ID.Value = ""
            更新日時.Value = ""
            更新者ID.Value = ""

            Using db As New DbMasterTableAdapters.M商品TableAdapter
                Using rs As DbMaster.M商品DataTable = db.GetData(商品コード.Value)
                    If rs.Count > 0 Then
                        商品コード.Value = rs(0).商品コード
                        商品名.Value = rs(0).商品名
                        商品名かな.Value = rs(0).商品名かな
                        単位区分.Value = rs(0).単位区分
                        基本料金.Value = rs(0).基本料金
                        単価.Value = rs(0).単価
                        商品分類区分.Value = rs(0).商品分類区分
                        備考.Value = rs(0).備考
                        保証日数.Value = rs(0).保証日数

                        登録日時.Value = rs(0).登録日時
                        登録者ID.Value = rs(0).登録者ID
                        更新日時.Value = rs(0).更新日時
                        更新者ID.Value = rs(0).更新者ID

                        EditMode.Value = Field.EditModeContents.emUpdateMode
                    Else
                        EditMode.Value = Field.EditModeContents.emInsertMode
                    End If
                End Using
            End Using

            Return True
        End Function

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' フィールドからDBに書き込み処理を記述します。
        ''' エラーが発生した場合はLastErrorにエラーメッセージを格納しFalseを返します。
        ''' </summary>
        '''------------------------------------------------------------------------------
        Protected Overrides Function WriteMainDataSub() As Boolean
            Dim strNow As String = Format(Now, "yyyy/MM/dd HH:mm:ss")

            Using db As New DbMasterTableAdapters.M商品TableAdapter
                Using rs As DbMaster.M商品DataTable = db.GetData(商品コード.Value)
                    If rs.Count > 0 Then

                        rs(0).商品コード = 商品コード.Value
                        rs(0).商品名 = 商品名.Value
                        rs(0).商品名かな = 商品名かな.Value
                        rs(0).単位区分 = CShort(Text.CVal(単位区分.Value))
                        rs(0).基本料金 = Text.CVal(基本料金.Value)
                        rs(0).単価 = Text.CVal(単価.Value)
                        rs(0).商品分類区分 = CShort(Text.CVal(商品分類区分.Value))
                        rs(0).備考 = 備考.Value
                        rs(0).保証日数 = Text.CVal(保証日数.Value)

                        rs(0).更新日時 = strNow
                        rs(0).更新者ID = Security.UserID
                        db.Update(rs)
                    Else
                        ' 商品コードが空白の場合は最新コードを取得
                        If 商品コード.Value = "" Then
                            商品コード.Value = MasterCode.AnyTableNewNumber("M商品", "商品コード")
                        End If

                        db.Insert(
                            商品コード.Value,
                            商品名.Value,
                            商品名かな.Value,
                            CShort(Text.CVal(単位区分.Value)),
                            Text.CVal(基本料金.Value),
                            Text.CVal(単価.Value),
                            CShort(Text.CVal(商品分類区分.Value)),
                            備考.Value,
                            Text.CVal(保証日数.Value),
                            strNow,
                            Security.UserID,
                            strNow,
                            Security.UserID)
                    End If
                End Using
            End Using

            Return True
        End Function

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' DBから削除する処理を記述します。
        ''' エラーが発生した場合はLastErrorにエラーメッセージを格納しFalseを返します。
        ''' </summary>
        '''------------------------------------------------------------------------------
        Protected Overrides Function DeleteMainDataSub() As Boolean
            Using db As New DbMasterTableAdapters.M商品TableAdapter
                db.DeleteByCode(商品コード.Value)
            End Using

            Return True
        End Function

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' フィールドの検証処理をを記述します。
        ''' フィールドの検証処理。基底クラスにて、WriteMainDataSubが呼ばれる前にコールされます。
        ''' 基本的な検証(文字数・書式・区分値)は基底クラスで検証されます。
        ''' </summary>
        '''------------------------------------------------------------------------------
        Protected Overrides Sub EnterValidateSub(ByRef chk As Dbs.Asphalt.Core.Common.Validator)

            '----検証サンプル--------------------------------------------------------------
            ' 担当者マスターに登録済みかどうか
            'chk.FoundMaster(担当者コード, "担当者")
            '----検証サンプル--------------------------------------------------------------

        End Sub

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' マネージャの名称(メニューや画面タイトル、権限モジュールリストで共通使用されます)
        ''' </summary>
        '''------------------------------------------------------------------------------
        Public Overrides ReadOnly Property Title As String
            Get
                Return "商品マスター"
            End Get
        End Property
    End Class

End Namespace
