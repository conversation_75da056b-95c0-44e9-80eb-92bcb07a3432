﻿Imports Dbs.Asphalt.Core
Imports Dbs.Asphalt.Core.Common
Imports Dbs.Asphalt.Core.SystemLogic
Imports Dbs.Asphalt.Core.BaseClass
Imports Dbs.Asphalt.Database
Imports Dbs.Application.Logic.Database

Namespace Logic.Master

    ''' <summary>
    ''' 登録処理サンプルマネージャ
    ''' </summary>
    Public Class TokuisakiMasterManager
        Inherits RegisterLogicBase

        ' 使用するフィールドを初期化
        Public 得意先コード As New Field.ItemData("得意先コード", "", Field.ItemType.KeyItem, False, 6,, Text.FormatContents.tbCode)
        Public 得意先名 As New Field.ItemData("得意先名", "", Field.ItemType.DataItem, True, 100)
        Public 得意先かな As New Field.ItemData("郵便番号", "", Field.ItemType.DataItem, False, 10)
        Public 郵便番号 As New Field.ItemData("郵便番号", "", Field.ItemType.DataItem, False, 10)
        Public 住所1 As New Field.ItemData("住所1", "", Field.ItemType.DataItem, False, 50)
        Public 住所2 As New Field.ItemData("住所2", "", Field.ItemType.DataItem, False, 50)
        Public 電話番号 As New Field.ItemData("電話番号", "", Field.ItemType.DataItem, False, 20)
        Public FAX番号 As New Field.ItemData("FAX番号", "", Field.ItemType.DataItem, False, 20)
        Public 締日区分 As New Field.ItemData("締日区分", "", Field.ItemType.DataItem, True, "締日区分", False)
        Public 端数区分 As New Field.ItemData("端数区分", "", Field.ItemType.DataItem, True, "端数区分", False)
        Public 請求書区分 As New Field.ItemData("請求書区分", "", Field.ItemType.DataItem, True, "請求書区分", False)
        Public 担当者コード As New Field.ItemData("担当者コード", "", Field.ItemType.DataItem, False, 10)
        Public 諸口区分 As New Field.ItemData("専用帳票区分", "", Field.ItemType.DataItem, True, "専用帳票区分", False)
        Public 消費税区分 As New Field.ItemData("消費税区分", "", Field.ItemType.DataItem, True, "消費税区分", False)
        Public 登録日時 As New Field.ItemData("登録日時", "", Field.ItemType.DataItem, False, 20)
        Public 登録者ID As New Field.ItemData("登録者ID", "", Field.ItemType.DataItem, False, 50)
        Public 更新日時 As New Field.ItemData("更新日時", "", Field.ItemType.DataItem, False, 20)
        Public 更新者ID As New Field.ItemData("更新者ID", "", Field.ItemType.DataItem, False, 50)

        Sub New(Security As Security)
            MyBase.New(Security)
        End Sub

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' DBからフィールドに読み込む処理を記述します。
        ''' エラーが発生した場合はLastErrorにエラーメッセージを格納しFalseを返します。
        ''' </summary>
        ''' <param name="read_after_clear">読み込む前にフィールドをクリアするかどうか</param>
        '''------------------------------------------------------------------------------
        Protected Overrides Function ReadMainDataSub(read_after_clear As Boolean) As Boolean
            If read_after_clear Then

                得意先名.Value = ""
                得意先かな.Value = ""
                郵便番号.Value = ""
                住所1.Value = ""
                住所2.Value = ""
                電話番号.Value = ""
                FAX番号.Value = ""
                締日区分.Value = 99
                端数区分.Value = 2
                請求書区分.Value = 0
                担当者コード.Value = ""
                諸口区分.Value = 0
                消費税区分.Value = 1
            End If

            登録日時.Value = ""
            登録者ID.Value = ""
            更新日時.Value = ""
            更新者ID.Value = ""

            Using db As New DbMasterTableAdapters.M得意先TableAdapter
                Using rs As DbMaster.M得意先DataTable = db.GetData(得意先コード.Value)
                    If rs.Count > 0 Then
                        得意先名.Value = rs(0).得意先名
                        得意先かな.Value = rs(0).得意先かな
                        郵便番号.Value = rs(0).郵便番号
                        住所1.Value = rs(0).住所1
                        住所2.Value = rs(0).住所2
                        電話番号.Value = rs(0).電話番号
                        FAX番号.Value = rs(0).FAX番号
                        締日区分.Value = rs(0).締日区分
                        端数区分.Value = rs(0).端数区分
                        請求書区分.Value = rs(0).請求書区分
                        担当者コード.Value = rs(0).担当者コード
                        諸口区分.Value = rs(0).諸口区分
                        消費税区分.Value = rs(0).消費税区分

                        登録日時.Value = rs(0).登録日時
                        登録者ID.Value = rs(0).登録者ID
                        更新日時.Value = rs(0).更新日時
                        更新者ID.Value = rs(0).更新者ID

                        EditMode.Value = Field.EditModeContents.emUpdateMode
                    Else
                        EditMode.Value = Field.EditModeContents.emInsertMode
                    End If
                End Using
            End Using

            Return True
        End Function

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' フィールドからDBに書き込み処理を記述します。
        ''' エラーが発生した場合はLastErrorにエラーメッセージを格納しFalseを返します。
        ''' </summary>
        '''------------------------------------------------------------------------------
        Protected Overrides Function WriteMainDataSub() As Boolean
            Dim strNow As String = Format(Now, "yyyy/MM/dd HH:mm:ss")

            Using db As New DbMasterTableAdapters.M得意先TableAdapter
                Using rs As DbMaster.M得意先DataTable = db.GetData(得意先コード.Value)
                    If rs.Count > 0 Then

                        rs(0).得意先名 = 得意先名.Value
                        rs(0).得意先かな = 得意先かな.Value
                        rs(0).郵便番号 = 郵便番号.Value
                        rs(0).住所1 = 住所1.Value
                        rs(0).住所2 = 住所2.Value
                        rs(0).電話番号 = 電話番号.Value
                        rs(0).FAX番号 = FAX番号.Value
                        rs(0).締日区分 = CShort(Text.CVal(締日区分.Value))
                        rs(0).端数区分 = CShort(Text.CVal(端数区分.Value))
                        rs(0).請求書区分 = CShort(Text.CVal(請求書区分.Value))
                        rs(0).担当者コード = 担当者コード.Value
                        rs(0).諸口区分 = CShort(Text.CVal(諸口区分.Value))
                        rs(0).消費税区分 = CShort(Text.CVal(消費税区分.Value))

                        rs(0).更新日時 = strNow
                        rs(0).更新者ID = Security.UserID
                        db.Update(rs)
                    Else
                        ' 得意先コードが空白の場合は最新コードを取得
                        If 得意先コード.Value = "" Then
                            得意先コード.Value = MasterCode.AnyTableNewNumber("M得意先", "得意先コード")
                        End If

                        db.Insert(
                            得意先コード.Value,
                            得意先名.Value,
                            得意先かな.Value,
                            郵便番号.Value,
                            住所1.Value,
                            住所2.Value,
                            電話番号.Value,
                            FAX番号.Value,
                            CShort(Text.CVal(締日区分.Value)),
                            CShort(Text.CVal(端数区分.Value)),
                            CShort(Text.CVal(請求書区分.Value)),
                            担当者コード.Value,
                            CShort(Text.CVal(諸口区分.Value)),
                            CShort(Text.CVal(消費税区分.Value)),
                            strNow,
                            Security.UserID,
                            strNow,
                            Security.UserID)
                    End If
                End Using
            End Using

            Return True
        End Function

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' DBから削除する処理を記述します。
        ''' エラーが発生した場合はLastErrorにエラーメッセージを格納しFalseを返します。
        ''' </summary>
        '''------------------------------------------------------------------------------
        Protected Overrides Function DeleteMainDataSub() As Boolean
            Using db As New DbMasterTableAdapters.M得意先TableAdapter
                db.DeleteByCode(得意先コード.Value)
            End Using

            Return True
        End Function

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' フィールドの検証処理をを記述します。
        ''' フィールドの検証処理。基底クラスにて、WriteMainDataSubが呼ばれる前にコールされます。
        ''' 基本的な検証(文字数・書式・区分値)は基底クラスで検証されます。
        ''' </summary>
        '''------------------------------------------------------------------------------
        Protected Overrides Sub EnterValidateSub(ByRef chk As Dbs.Asphalt.Core.Common.Validator)

            '----検証サンプル--------------------------------------------------------------
            ' 担当者マスターに登録済みかどうか
            'chk.FoundMaster(担当者コード, "担当者")
            '----検証サンプル--------------------------------------------------------------

        End Sub

        '''------------------------------------------------------------------------------
        ''' <summary>
        ''' マネージャの名称(メニューや画面タイトル、権限モジュールリストで共通使用されます)
        ''' </summary>
        '''------------------------------------------------------------------------------
        Public Overrides ReadOnly Property Title As String
            Get
                Return "得意先マスター"
            End Get
        End Property
    End Class

End Namespace
