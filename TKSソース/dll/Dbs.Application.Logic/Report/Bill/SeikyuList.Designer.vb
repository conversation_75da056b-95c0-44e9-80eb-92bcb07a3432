﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Public Class SeikyuList
    Inherits Dbs.Asphalt.Core.BaseReportA4L

    'フォームがコンポーネントの一覧をクリーンアップするために dispose をオーバーライドします。
    Protected Overloads Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing Then
        End If
        MyBase.Dispose(disposing)
    End Sub

    'メモ: 以下のプロシージャは ActiveReports デザイナーで必要です。
    'ActiveReports デザイナーを使用して変更できます。  
    'コード エディターを使って変更しないでください。
    Private WithEvents PageHeader As GrapeCity.ActiveReports.SectionReportModel.PageHeader
    Private WithEvents Detail As GrapeCity.ActiveReports.SectionReportModel.Detail
    Private WithEvents PageFooter As GrapeCity.ActiveReports.SectionReportModel.PageFooter
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Dim resources As System.Resources.ResourceManager = New System.Resources.ResourceManager(GetType(SeikyuList))
        Me.Detail = New GrapeCity.ActiveReports.SectionReportModel.Detail()
        Me.txt得意先コード = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt得意先名 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt工事コード = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt工事名 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt前回額 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt入金額 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt調整額 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt繰越額 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt売上額 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt消費税 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt請求額 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.GroupHeader1 = New GrapeCity.ActiveReports.SectionReportModel.GroupHeader()
        Me.lbl得意先コード = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.lbl得意先名 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.lbl工事コード = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.lbl工事名 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.lbl前回額 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.lbl入金額 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.lbl調整額 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.lbl繰越額 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.lbl売上額 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.lbl消費税 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.lbl請求額 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.GroupFooter1 = New GrapeCity.ActiveReports.SectionReportModel.GroupFooter()
        Me.TextBox1 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.TextBox2 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.TextBox3 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.TextBox4 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt前回額小計 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt入金額小計 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt調整額小計 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt繰越額小計 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt売上額小計 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt消費税小計 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt請求額小計 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.GroupHeader2 = New GrapeCity.ActiveReports.SectionReportModel.GroupHeader()
        Me.GroupFooter2 = New GrapeCity.ActiveReports.SectionReportModel.GroupFooter()
        Me.TextBox5 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.TextBox6 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.TextBox7 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.TextBox8 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt前回額合計 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt入金額合計 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt調整額合計 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt繰越額合計 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt売上額合計 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt消費税合計 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt請求額合計 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        CType(Me.lblCompName, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lblDate, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lblTitle, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtJyoken, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt得意先コード, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt得意先名, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt工事コード, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt工事名, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt前回額, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt入金額, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt調整額, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt繰越額, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt売上額, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt消費税, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt請求額, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lbl得意先コード, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lbl得意先名, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lbl工事コード, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lbl工事名, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lbl前回額, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lbl入金額, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lbl調整額, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lbl繰越額, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lbl売上額, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lbl消費税, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lbl請求額, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextBox1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextBox2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextBox3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextBox4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt前回額小計, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt入金額小計, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt調整額小計, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt繰越額小計, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt売上額小計, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt消費税小計, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt請求額小計, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextBox5, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextBox6, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextBox7, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextBox8, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt前回額合計, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt入金額合計, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt調整額合計, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt繰越額合計, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt売上額合計, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt消費税合計, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt請求額合計, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me, System.ComponentModel.ISupportInitialize).BeginInit()
        '
        'Detail
        '
        Me.Detail.Controls.AddRange(New GrapeCity.ActiveReports.SectionReportModel.ARControl() {Me.txt得意先コード, Me.txt得意先名, Me.txt工事コード, Me.txt工事名, Me.txt前回額, Me.txt入金額, Me.txt調整額, Me.txt繰越額, Me.txt売上額, Me.txt消費税, Me.txt請求額})
        Me.Detail.Height = 0.2755906!
        Me.Detail.Name = "Detail"
        '
        'txt得意先コード
        '
        Me.txt得意先コード.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt得意先コード.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt得意先コード.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt得意先コード.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt得意先コード.CanGrow = False
        Me.txt得意先コード.Height = 0.2755905!
        Me.txt得意先コード.Left = 0!
        Me.txt得意先コード.Name = "txt得意先コード"
        Me.txt得意先コード.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.txt得意先コード.ShrinkToFit = True
        Me.txt得意先コード.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: center; vertical-align: middle; d" &
    "do-shrink-to-fit: true"
        Me.txt得意先コード.Text = "012345"
        Me.txt得意先コード.Top = 0!
        Me.txt得意先コード.Width = 0.4330709!
        '
        'txt得意先名
        '
        Me.txt得意先名.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt得意先名.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt得意先名.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt得意先名.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt得意先名.CanGrow = False
        Me.txt得意先名.Height = 0.2755905!
        Me.txt得意先名.Left = 0.4330709!
        Me.txt得意先名.Name = "txt得意先名"
        Me.txt得意先名.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 0, 0, 0)
        Me.txt得意先名.ShrinkToFit = True
        Me.txt得意先名.Style = "font-family: ＭＳ 明朝; font-size: 9pt; vertical-align: middle; ddo-shrink-to-fit: tr" &
    "ue"
        Me.txt得意先名.Text = "XXXX"
        Me.txt得意先名.Top = 0!
        Me.txt得意先名.Width = 2.204724!
        '
        'txt工事コード
        '
        Me.txt工事コード.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt工事コード.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt工事コード.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt工事コード.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt工事コード.CanGrow = False
        Me.txt工事コード.Height = 0.2755905!
        Me.txt工事コード.Left = 2.637795!
        Me.txt工事コード.Name = "txt工事コード"
        Me.txt工事コード.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.txt工事コード.ShrinkToFit = True
        Me.txt工事コード.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: center; vertical-align: middle; d" &
    "do-shrink-to-fit: true"
        Me.txt工事コード.Text = "0123"
        Me.txt工事コード.Top = 0!
        Me.txt工事コード.Width = 0.3149606!
        '
        'txt工事名
        '
        Me.txt工事名.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt工事名.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt工事名.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt工事名.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt工事名.CanGrow = False
        Me.txt工事名.Height = 0.2755905!
        Me.txt工事名.Left = 2.952756!
        Me.txt工事名.Name = "txt工事名"
        Me.txt工事名.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 0, 0, 0)
        Me.txt工事名.ShrinkToFit = True
        Me.txt工事名.Style = "font-family: ＭＳ 明朝; font-size: 9pt; vertical-align: middle; ddo-shrink-to-fit: tr" &
    "ue"
        Me.txt工事名.Text = "XXXX"
        Me.txt工事名.Top = 0!
        Me.txt工事名.Width = 2.204724!
        '
        'txt前回額
        '
        Me.txt前回額.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt前回額.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt前回額.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt前回額.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt前回額.CanGrow = False
        Me.txt前回額.Height = 0.2755905!
        Me.txt前回額.Left = 5.157481!
        Me.txt前回額.Name = "txt前回額"
        Me.txt前回額.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 1, 0)
        Me.txt前回額.ShrinkToFit = True
        Me.txt前回額.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: right; vertical-align: middle; dd" &
    "o-shrink-to-fit: true"
        Me.txt前回額.Text = "99999999"
        Me.txt前回額.Top = 0!
        Me.txt前回額.Width = 0.7480315!
        '
        'txt入金額
        '
        Me.txt入金額.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt入金額.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt入金額.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt入金額.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt入金額.CanGrow = False
        Me.txt入金額.Height = 0.2755906!
        Me.txt入金額.Left = 5.905512!
        Me.txt入金額.Name = "txt入金額"
        Me.txt入金額.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 1, 0)
        Me.txt入金額.ShrinkToFit = True
        Me.txt入金額.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: right; vertical-align: middle; dd" &
    "o-shrink-to-fit: true"
        Me.txt入金額.Text = "99999999"
        Me.txt入金額.Top = 0!
        Me.txt入金額.Width = 0.7480313!
        '
        'txt調整額
        '
        Me.txt調整額.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt調整額.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt調整額.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt調整額.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt調整額.CanGrow = False
        Me.txt調整額.Height = 0.2755906!
        Me.txt調整額.Left = 6.653544!
        Me.txt調整額.Name = "txt調整額"
        Me.txt調整額.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 1, 0)
        Me.txt調整額.ShrinkToFit = True
        Me.txt調整額.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: right; vertical-align: middle; dd" &
    "o-shrink-to-fit: true"
        Me.txt調整額.Text = "99999999"
        Me.txt調整額.Top = 0!
        Me.txt調整額.Width = 0.7480313!
        '
        'txt繰越額
        '
        Me.txt繰越額.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt繰越額.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt繰越額.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt繰越額.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt繰越額.CanGrow = False
        Me.txt繰越額.Height = 0.2755906!
        Me.txt繰越額.Left = 7.401575!
        Me.txt繰越額.Name = "txt繰越額"
        Me.txt繰越額.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 1, 0)
        Me.txt繰越額.ShrinkToFit = True
        Me.txt繰越額.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: right; vertical-align: middle; dd" &
    "o-shrink-to-fit: true"
        Me.txt繰越額.Text = "99999999"
        Me.txt繰越額.Top = 0!
        Me.txt繰越額.Width = 0.7480313!
        '
        'txt売上額
        '
        Me.txt売上額.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt売上額.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt売上額.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt売上額.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt売上額.CanGrow = False
        Me.txt売上額.Height = 0.2755906!
        Me.txt売上額.Left = 8.149609!
        Me.txt売上額.Name = "txt売上額"
        Me.txt売上額.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 1, 0)
        Me.txt売上額.ShrinkToFit = True
        Me.txt売上額.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: right; vertical-align: middle; dd" &
    "o-shrink-to-fit: true"
        Me.txt売上額.Text = "99999999"
        Me.txt売上額.Top = 0!
        Me.txt売上額.Width = 0.7480313!
        '
        'txt消費税
        '
        Me.txt消費税.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt消費税.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt消費税.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt消費税.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt消費税.CanGrow = False
        Me.txt消費税.Height = 0.2755906!
        Me.txt消費税.Left = 8.897641!
        Me.txt消費税.Name = "txt消費税"
        Me.txt消費税.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 1, 0)
        Me.txt消費税.ShrinkToFit = True
        Me.txt消費税.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: right; vertical-align: middle; dd" &
    "o-shrink-to-fit: true"
        Me.txt消費税.Text = "99999999"
        Me.txt消費税.Top = 0!
        Me.txt消費税.Width = 0.7480313!
        '
        'txt請求額
        '
        Me.txt請求額.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt請求額.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt請求額.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt請求額.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt請求額.CanGrow = False
        Me.txt請求額.Height = 0.2755906!
        Me.txt請求額.Left = 9.645667!
        Me.txt請求額.Name = "txt請求額"
        Me.txt請求額.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 1, 0)
        Me.txt請求額.ShrinkToFit = True
        Me.txt請求額.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: right; vertical-align: middle; dd" &
    "o-shrink-to-fit: true"
        Me.txt請求額.Text = "99999999"
        Me.txt請求額.Top = 0!
        Me.txt請求額.Width = 0.7480313!
        '
        'GroupHeader1
        '
        Me.GroupHeader1.Height = 0!
        Me.GroupHeader1.Name = "GroupHeader1"
        '
        'lbl得意先コード
        '
        Me.lbl得意先コード.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl得意先コード.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl得意先コード.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl得意先コード.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl得意先コード.Height = 0.2755905!
        Me.lbl得意先コード.HyperLink = Nothing
        Me.lbl得意先コード.Left = 0!
        Me.lbl得意先コード.Name = "lbl得意先コード"
        Me.lbl得意先コード.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: center; vertical-align: middle"
        Me.lbl得意先コード.Text = "ｺｰﾄﾞ"
        Me.lbl得意先コード.Top = 0!
        Me.lbl得意先コード.Width = 0.4330709!
        '
        'lbl得意先名
        '
        Me.lbl得意先名.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl得意先名.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl得意先名.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl得意先名.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl得意先名.Height = 0.2755905!
        Me.lbl得意先名.HyperLink = Nothing
        Me.lbl得意先名.Left = 0.4330709!
        Me.lbl得意先名.Name = "lbl得意先名"
        Me.lbl得意先名.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: center; vertical-align: middle"
        Me.lbl得意先名.Text = "得意先名"
        Me.lbl得意先名.Top = 0!
        Me.lbl得意先名.Width = 2.204724!
        '
        'lbl工事コード
        '
        Me.lbl工事コード.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl工事コード.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl工事コード.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl工事コード.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl工事コード.Height = 0.2755905!
        Me.lbl工事コード.HyperLink = Nothing
        Me.lbl工事コード.Left = 2.637795!
        Me.lbl工事コード.Name = "lbl工事コード"
        Me.lbl工事コード.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: center; vertical-align: middle"
        Me.lbl工事コード.Text = "ｺｰﾄﾞ"
        Me.lbl工事コード.Top = 0!
        Me.lbl工事コード.Width = 0.3149606!
        '
        'lbl工事名
        '
        Me.lbl工事名.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl工事名.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl工事名.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl工事名.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl工事名.Height = 0.2755905!
        Me.lbl工事名.HyperLink = Nothing
        Me.lbl工事名.Left = 2.952756!
        Me.lbl工事名.Name = "lbl工事名"
        Me.lbl工事名.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: center; vertical-align: middle"
        Me.lbl工事名.Text = "工事名"
        Me.lbl工事名.Top = 0!
        Me.lbl工事名.Width = 2.204724!
        '
        'lbl前回額
        '
        Me.lbl前回額.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl前回額.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl前回額.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl前回額.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl前回額.Height = 0.2755906!
        Me.lbl前回額.HyperLink = Nothing
        Me.lbl前回額.Left = 5.157481!
        Me.lbl前回額.Name = "lbl前回額"
        Me.lbl前回額.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: center; vertical-align: middle"
        Me.lbl前回額.Text = "前回額"
        Me.lbl前回額.Top = 0!
        Me.lbl前回額.Width = 0.7480315!
        '
        'lbl入金額
        '
        Me.lbl入金額.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl入金額.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl入金額.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl入金額.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl入金額.Height = 0.2755906!
        Me.lbl入金額.HyperLink = Nothing
        Me.lbl入金額.Left = 5.905512!
        Me.lbl入金額.Name = "lbl入金額"
        Me.lbl入金額.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: center; vertical-align: middle"
        Me.lbl入金額.Text = "入金額"
        Me.lbl入金額.Top = 0!
        Me.lbl入金額.Width = 0.7480313!
        '
        'lbl調整額
        '
        Me.lbl調整額.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl調整額.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl調整額.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl調整額.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl調整額.Height = 0.2755906!
        Me.lbl調整額.HyperLink = Nothing
        Me.lbl調整額.Left = 6.653544!
        Me.lbl調整額.Name = "lbl調整額"
        Me.lbl調整額.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: center; vertical-align: middle"
        Me.lbl調整額.Text = "調整額"
        Me.lbl調整額.Top = 0!
        Me.lbl調整額.Width = 0.7480313!
        '
        'lbl繰越額
        '
        Me.lbl繰越額.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl繰越額.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl繰越額.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl繰越額.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl繰越額.Height = 0.2755906!
        Me.lbl繰越額.HyperLink = Nothing
        Me.lbl繰越額.Left = 7.401575!
        Me.lbl繰越額.Name = "lbl繰越額"
        Me.lbl繰越額.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: center; vertical-align: middle"
        Me.lbl繰越額.Text = "繰越額"
        Me.lbl繰越額.Top = 0!
        Me.lbl繰越額.Width = 0.7480313!
        '
        'lbl売上額
        '
        Me.lbl売上額.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl売上額.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl売上額.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl売上額.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl売上額.Height = 0.2755906!
        Me.lbl売上額.HyperLink = Nothing
        Me.lbl売上額.Left = 8.149608!
        Me.lbl売上額.Name = "lbl売上額"
        Me.lbl売上額.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: center; vertical-align: middle"
        Me.lbl売上額.Text = "売上額"
        Me.lbl売上額.Top = 0!
        Me.lbl売上額.Width = 0.7480313!
        '
        'lbl消費税
        '
        Me.lbl消費税.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl消費税.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl消費税.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl消費税.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl消費税.Height = 0.2755906!
        Me.lbl消費税.HyperLink = Nothing
        Me.lbl消費税.Left = 8.897635!
        Me.lbl消費税.Name = "lbl消費税"
        Me.lbl消費税.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: center; vertical-align: middle"
        Me.lbl消費税.Text = "消費税"
        Me.lbl消費税.Top = 0!
        Me.lbl消費税.Width = 0.7480313!
        '
        'lbl請求額
        '
        Me.lbl請求額.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl請求額.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl請求額.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl請求額.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl請求額.Height = 0.2755906!
        Me.lbl請求額.HyperLink = Nothing
        Me.lbl請求額.Left = 9.645667!
        Me.lbl請求額.Name = "lbl請求額"
        Me.lbl請求額.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: center; vertical-align: middle"
        Me.lbl請求額.Text = "請求額"
        Me.lbl請求額.Top = 0!
        Me.lbl請求額.Width = 0.7480313!
        '
        'GroupFooter1
        '
        Me.GroupFooter1.Controls.AddRange(New GrapeCity.ActiveReports.SectionReportModel.ARControl() {Me.TextBox1, Me.TextBox2, Me.TextBox3, Me.TextBox4, Me.txt前回額小計, Me.txt入金額小計, Me.txt調整額小計, Me.txt繰越額小計, Me.txt売上額小計, Me.txt消費税小計, Me.txt請求額小計})
        Me.GroupFooter1.Height = 0.2755906!
        Me.GroupFooter1.Name = "GroupFooter1"
        '
        'TextBox1
        '
        Me.TextBox1.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox1.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox1.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox1.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox1.CanGrow = False
        Me.TextBox1.Height = 0.2755906!
        Me.TextBox1.Left = 0!
        Me.TextBox1.Name = "TextBox1"
        Me.TextBox1.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.TextBox1.ShrinkToFit = True
        Me.TextBox1.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: center; vertical-align: middle; d" &
    "do-shrink-to-fit: true"
        Me.TextBox1.Text = Nothing
        Me.TextBox1.Top = 0!
        Me.TextBox1.Width = 0.4330709!
        '
        'TextBox2
        '
        Me.TextBox2.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox2.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox2.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox2.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox2.CanGrow = False
        Me.TextBox2.Height = 0.2755906!
        Me.TextBox2.Left = 0.4330709!
        Me.TextBox2.Name = "TextBox2"
        Me.TextBox2.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 0, 0, 0)
        Me.TextBox2.ShrinkToFit = True
        Me.TextBox2.Style = "font-family: ＭＳ 明朝; font-size: 9pt; vertical-align: middle; ddo-shrink-to-fit: tr" &
    "ue"
        Me.TextBox2.Text = Nothing
        Me.TextBox2.Top = 0!
        Me.TextBox2.Width = 2.204724!
        '
        'TextBox3
        '
        Me.TextBox3.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox3.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox3.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox3.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox3.CanGrow = False
        Me.TextBox3.Height = 0.2755906!
        Me.TextBox3.Left = 2.637796!
        Me.TextBox3.Name = "TextBox3"
        Me.TextBox3.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.TextBox3.ShrinkToFit = True
        Me.TextBox3.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: center; vertical-align: middle; d" &
    "do-shrink-to-fit: true"
        Me.TextBox3.Text = Nothing
        Me.TextBox3.Top = 0!
        Me.TextBox3.Width = 0.3149606!
        '
        'TextBox4
        '
        Me.TextBox4.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox4.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox4.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox4.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox4.CanGrow = False
        Me.TextBox4.Height = 0.2755906!
        Me.TextBox4.Left = 2.952756!
        Me.TextBox4.Name = "TextBox4"
        Me.TextBox4.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 0, 0, 0)
        Me.TextBox4.ShrinkToFit = True
        Me.TextBox4.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: center; vertical-align: middle; d" &
    "do-shrink-to-fit: true"
        Me.TextBox4.Text = "小　　　計"
        Me.TextBox4.Top = 0!
        Me.TextBox4.Width = 2.204724!
        '
        'txt前回額小計
        '
        Me.txt前回額小計.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt前回額小計.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt前回額小計.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt前回額小計.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt前回額小計.CanGrow = False
        Me.txt前回額小計.Height = 0.2755906!
        Me.txt前回額小計.Left = 5.157481!
        Me.txt前回額小計.Name = "txt前回額小計"
        Me.txt前回額小計.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 1, 0)
        Me.txt前回額小計.ShrinkToFit = True
        Me.txt前回額小計.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: right; vertical-align: middle; dd" &
    "o-shrink-to-fit: true"
        Me.txt前回額小計.Text = "99999999"
        Me.txt前回額小計.Top = 0!
        Me.txt前回額小計.Width = 0.7480313!
        '
        'txt入金額小計
        '
        Me.txt入金額小計.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt入金額小計.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt入金額小計.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt入金額小計.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt入金額小計.CanGrow = False
        Me.txt入金額小計.Height = 0.2755906!
        Me.txt入金額小計.Left = 5.905513!
        Me.txt入金額小計.Name = "txt入金額小計"
        Me.txt入金額小計.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 1, 0)
        Me.txt入金額小計.ShrinkToFit = True
        Me.txt入金額小計.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: right; vertical-align: middle; dd" &
    "o-shrink-to-fit: true"
        Me.txt入金額小計.Text = "99999999"
        Me.txt入金額小計.Top = 0!
        Me.txt入金額小計.Width = 0.7480313!
        '
        'txt調整額小計
        '
        Me.txt調整額小計.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt調整額小計.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt調整額小計.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt調整額小計.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt調整額小計.CanGrow = False
        Me.txt調整額小計.Height = 0.2755906!
        Me.txt調整額小計.Left = 6.653544!
        Me.txt調整額小計.Name = "txt調整額小計"
        Me.txt調整額小計.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 1, 0)
        Me.txt調整額小計.ShrinkToFit = True
        Me.txt調整額小計.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: right; vertical-align: middle; dd" &
    "o-shrink-to-fit: true"
        Me.txt調整額小計.Text = "99999999"
        Me.txt調整額小計.Top = 0!
        Me.txt調整額小計.Width = 0.7480313!
        '
        'txt繰越額小計
        '
        Me.txt繰越額小計.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt繰越額小計.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt繰越額小計.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt繰越額小計.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt繰越額小計.CanGrow = False
        Me.txt繰越額小計.Height = 0.2755906!
        Me.txt繰越額小計.Left = 7.401576!
        Me.txt繰越額小計.Name = "txt繰越額小計"
        Me.txt繰越額小計.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 1, 0)
        Me.txt繰越額小計.ShrinkToFit = True
        Me.txt繰越額小計.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: right; vertical-align: middle; dd" &
    "o-shrink-to-fit: true"
        Me.txt繰越額小計.Text = "99999999"
        Me.txt繰越額小計.Top = 0!
        Me.txt繰越額小計.Width = 0.7480313!
        '
        'txt売上額小計
        '
        Me.txt売上額小計.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt売上額小計.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt売上額小計.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt売上額小計.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt売上額小計.CanGrow = False
        Me.txt売上額小計.Height = 0.2755906!
        Me.txt売上額小計.Left = 8.149611!
        Me.txt売上額小計.Name = "txt売上額小計"
        Me.txt売上額小計.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 1, 0)
        Me.txt売上額小計.ShrinkToFit = True
        Me.txt売上額小計.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: right; vertical-align: middle; dd" &
    "o-shrink-to-fit: true"
        Me.txt売上額小計.Text = "99999999"
        Me.txt売上額小計.Top = 0!
        Me.txt売上額小計.Width = 0.7480313!
        '
        'txt消費税小計
        '
        Me.txt消費税小計.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt消費税小計.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt消費税小計.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt消費税小計.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt消費税小計.CanGrow = False
        Me.txt消費税小計.Height = 0.2755906!
        Me.txt消費税小計.Left = 8.897638!
        Me.txt消費税小計.Name = "txt消費税小計"
        Me.txt消費税小計.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 1, 0)
        Me.txt消費税小計.ShrinkToFit = True
        Me.txt消費税小計.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: right; vertical-align: middle; dd" &
    "o-shrink-to-fit: true"
        Me.txt消費税小計.Text = "99999999"
        Me.txt消費税小計.Top = 0!
        Me.txt消費税小計.Width = 0.7480313!
        '
        'txt請求額小計
        '
        Me.txt請求額小計.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt請求額小計.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt請求額小計.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt請求額小計.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt請求額小計.CanGrow = False
        Me.txt請求額小計.Height = 0.2755906!
        Me.txt請求額小計.Left = 9.645666!
        Me.txt請求額小計.Name = "txt請求額小計"
        Me.txt請求額小計.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 1, 0)
        Me.txt請求額小計.ShrinkToFit = True
        Me.txt請求額小計.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: right; vertical-align: middle; dd" &
    "o-shrink-to-fit: true"
        Me.txt請求額小計.Text = "99999999"
        Me.txt請求額小計.Top = 0!
        Me.txt請求額小計.Width = 0.7480313!
        '
        'GroupHeader2
        '
        Me.GroupHeader2.Controls.AddRange(New GrapeCity.ActiveReports.SectionReportModel.ARControl() {Me.lbl得意先コード, Me.lbl得意先名, Me.lbl工事コード, Me.lbl工事名, Me.lbl前回額, Me.lbl入金額, Me.lbl調整額, Me.lbl繰越額, Me.lbl売上額, Me.lbl消費税, Me.lbl請求額})
        Me.GroupHeader2.Height = 0.2755906!
        Me.GroupHeader2.Name = "GroupHeader2"
        '
        'GroupFooter2
        '
        Me.GroupFooter2.Controls.AddRange(New GrapeCity.ActiveReports.SectionReportModel.ARControl() {Me.TextBox5, Me.TextBox6, Me.TextBox7, Me.TextBox8, Me.txt前回額合計, Me.txt入金額合計, Me.txt調整額合計, Me.txt繰越額合計, Me.txt売上額合計, Me.txt消費税合計, Me.txt請求額合計})
        Me.GroupFooter2.Height = 0.2755906!
        Me.GroupFooter2.Name = "GroupFooter2"
        '
        'TextBox5
        '
        Me.TextBox5.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox5.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox5.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox5.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox5.CanGrow = False
        Me.TextBox5.Height = 0.2755906!
        Me.TextBox5.Left = 0!
        Me.TextBox5.Name = "TextBox5"
        Me.TextBox5.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.TextBox5.ShrinkToFit = True
        Me.TextBox5.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: center; vertical-align: middle; d" &
    "do-shrink-to-fit: true"
        Me.TextBox5.Text = Nothing
        Me.TextBox5.Top = 0!
        Me.TextBox5.Width = 0.4330709!
        '
        'TextBox6
        '
        Me.TextBox6.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox6.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox6.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox6.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox6.CanGrow = False
        Me.TextBox6.Height = 0.2755906!
        Me.TextBox6.Left = 0.4330709!
        Me.TextBox6.Name = "TextBox6"
        Me.TextBox6.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 0, 0, 0)
        Me.TextBox6.ShrinkToFit = True
        Me.TextBox6.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: center; vertical-align: middle; d" &
    "do-shrink-to-fit: true"
        Me.TextBox6.Text = "合　　　計"
        Me.TextBox6.Top = 0!
        Me.TextBox6.Width = 2.204724!
        '
        'TextBox7
        '
        Me.TextBox7.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox7.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox7.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox7.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox7.CanGrow = False
        Me.TextBox7.Height = 0.2755906!
        Me.TextBox7.Left = 2.637796!
        Me.TextBox7.Name = "TextBox7"
        Me.TextBox7.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.TextBox7.ShrinkToFit = True
        Me.TextBox7.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: center; vertical-align: middle; d" &
    "do-shrink-to-fit: true"
        Me.TextBox7.Text = Nothing
        Me.TextBox7.Top = 0!
        Me.TextBox7.Width = 0.3149606!
        '
        'TextBox8
        '
        Me.TextBox8.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox8.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox8.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox8.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox8.CanGrow = False
        Me.TextBox8.Height = 0.2755906!
        Me.TextBox8.Left = 2.952756!
        Me.TextBox8.Name = "TextBox8"
        Me.TextBox8.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 0, 0, 0)
        Me.TextBox8.ShrinkToFit = True
        Me.TextBox8.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: center; vertical-align: middle; d" &
    "do-shrink-to-fit: true"
        Me.TextBox8.Text = Nothing
        Me.TextBox8.Top = 0!
        Me.TextBox8.Width = 2.204724!
        '
        'txt前回額合計
        '
        Me.txt前回額合計.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt前回額合計.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt前回額合計.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt前回額合計.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt前回額合計.CanGrow = False
        Me.txt前回額合計.Height = 0.2755906!
        Me.txt前回額合計.Left = 5.157481!
        Me.txt前回額合計.Name = "txt前回額合計"
        Me.txt前回額合計.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 1, 0)
        Me.txt前回額合計.ShrinkToFit = True
        Me.txt前回額合計.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: right; vertical-align: middle; dd" &
    "o-shrink-to-fit: true"
        Me.txt前回額合計.Text = "99999999"
        Me.txt前回額合計.Top = 0!
        Me.txt前回額合計.Width = 0.7480313!
        '
        'txt入金額合計
        '
        Me.txt入金額合計.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt入金額合計.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt入金額合計.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt入金額合計.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt入金額合計.CanGrow = False
        Me.txt入金額合計.Height = 0.2755906!
        Me.txt入金額合計.Left = 5.905513!
        Me.txt入金額合計.Name = "txt入金額合計"
        Me.txt入金額合計.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 1, 0)
        Me.txt入金額合計.ShrinkToFit = True
        Me.txt入金額合計.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: right; vertical-align: middle; dd" &
    "o-shrink-to-fit: true"
        Me.txt入金額合計.Text = "99999999"
        Me.txt入金額合計.Top = 0!
        Me.txt入金額合計.Width = 0.7480313!
        '
        'txt調整額合計
        '
        Me.txt調整額合計.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt調整額合計.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt調整額合計.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt調整額合計.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt調整額合計.CanGrow = False
        Me.txt調整額合計.Height = 0.2755906!
        Me.txt調整額合計.Left = 6.653544!
        Me.txt調整額合計.Name = "txt調整額合計"
        Me.txt調整額合計.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 1, 0)
        Me.txt調整額合計.ShrinkToFit = True
        Me.txt調整額合計.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: right; vertical-align: middle; dd" &
    "o-shrink-to-fit: true"
        Me.txt調整額合計.Text = "99999999"
        Me.txt調整額合計.Top = 0!
        Me.txt調整額合計.Width = 0.7480313!
        '
        'txt繰越額合計
        '
        Me.txt繰越額合計.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt繰越額合計.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt繰越額合計.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt繰越額合計.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt繰越額合計.CanGrow = False
        Me.txt繰越額合計.Height = 0.2755906!
        Me.txt繰越額合計.Left = 7.401576!
        Me.txt繰越額合計.Name = "txt繰越額合計"
        Me.txt繰越額合計.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 1, 0)
        Me.txt繰越額合計.ShrinkToFit = True
        Me.txt繰越額合計.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: right; vertical-align: middle; dd" &
    "o-shrink-to-fit: true"
        Me.txt繰越額合計.Text = "99999999"
        Me.txt繰越額合計.Top = 0!
        Me.txt繰越額合計.Width = 0.7480313!
        '
        'txt売上額合計
        '
        Me.txt売上額合計.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt売上額合計.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt売上額合計.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt売上額合計.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt売上額合計.CanGrow = False
        Me.txt売上額合計.Height = 0.2755906!
        Me.txt売上額合計.Left = 8.149611!
        Me.txt売上額合計.Name = "txt売上額合計"
        Me.txt売上額合計.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 1, 0)
        Me.txt売上額合計.ShrinkToFit = True
        Me.txt売上額合計.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: right; vertical-align: middle; dd" &
    "o-shrink-to-fit: true"
        Me.txt売上額合計.Text = "99999999"
        Me.txt売上額合計.Top = 0!
        Me.txt売上額合計.Width = 0.7480313!
        '
        'txt消費税合計
        '
        Me.txt消費税合計.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt消費税合計.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt消費税合計.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt消費税合計.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt消費税合計.CanGrow = False
        Me.txt消費税合計.Height = 0.2755906!
        Me.txt消費税合計.Left = 8.897638!
        Me.txt消費税合計.Name = "txt消費税合計"
        Me.txt消費税合計.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 1, 0)
        Me.txt消費税合計.ShrinkToFit = True
        Me.txt消費税合計.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: right; vertical-align: middle; dd" &
    "o-shrink-to-fit: true"
        Me.txt消費税合計.Text = "99999999"
        Me.txt消費税合計.Top = 0!
        Me.txt消費税合計.Width = 0.7480313!
        '
        'txt請求額合計
        '
        Me.txt請求額合計.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt請求額合計.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt請求額合計.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt請求額合計.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt請求額合計.CanGrow = False
        Me.txt請求額合計.Height = 0.2755906!
        Me.txt請求額合計.Left = 9.645666!
        Me.txt請求額合計.Name = "txt請求額合計"
        Me.txt請求額合計.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 1, 0)
        Me.txt請求額合計.ShrinkToFit = True
        Me.txt請求額合計.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: right; vertical-align: middle; dd" &
    "o-shrink-to-fit: true"
        Me.txt請求額合計.Text = "99999999"
        Me.txt請求額合計.Top = 0!
        Me.txt請求額合計.Width = 0.7480313!
        '
        'SeikyuList
        '
        Me.MasterReport = False
        Me.PageSettings.DefaultPaperSize = False
        Me.PageSettings.Margins.Bottom = 0.2755905!
        Me.PageSettings.Margins.Left = 0.2755905!
        Me.PageSettings.Margins.Right = 0.2755905!
        Me.PageSettings.Margins.Top = 0.511811!
        Me.PageSettings.Orientation = GrapeCity.ActiveReports.Document.Section.PageOrientation.Landscape
        Me.PageSettings.PaperHeight = 11.69291!
        Me.PageSettings.PaperKind = System.Drawing.Printing.PaperKind.A4
        Me.PageSettings.PaperWidth = 8.267716!
        Me.PrintWidth = 10.88542!
        Me.Sections.Add(Me.GroupHeader2)
        Me.Sections.Add(Me.GroupHeader1)
        Me.Sections.Add(Me.Detail)
        Me.Sections.Add(Me.GroupFooter1)
        Me.Sections.Add(Me.GroupFooter2)
        Me.StyleSheet.Add(New DDCssLib.StyleSheetRule("font-style: normal; text-decoration: none; font-weight: normal; font-size: 10pt; " &
            "color: Black; font-family: ""MS UI Gothic""; ddo-char-set: 128", "Normal"))
        Me.StyleSheet.Add(New DDCssLib.StyleSheetRule("font-size: 16pt; font-weight: bold; font-family: ""MS UI Gothic""; ddo-char-set: 12" &
            "8", "Heading1", "Normal"))
        Me.StyleSheet.Add(New DDCssLib.StyleSheetRule("font-size: 14pt; font-weight: bold; font-style: inherit; font-family: ""MS UI Goth" &
            "ic""; ddo-char-set: 128", "Heading2", "Normal"))
        Me.StyleSheet.Add(New DDCssLib.StyleSheetRule("font-size: 13pt; font-weight: bold; ddo-char-set: 128", "Heading3", "Normal"))
        CType(Me.lblCompName, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lblDate, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lblTitle, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtJyoken, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt得意先コード, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt得意先名, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt工事コード, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt工事名, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt前回額, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt入金額, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt調整額, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt繰越額, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt売上額, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt消費税, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt請求額, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lbl得意先コード, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lbl得意先名, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lbl工事コード, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lbl工事名, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lbl前回額, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lbl入金額, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lbl調整額, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lbl繰越額, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lbl売上額, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lbl消費税, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lbl請求額, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextBox1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextBox2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextBox3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextBox4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt前回額小計, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt入金額小計, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt調整額小計, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt繰越額小計, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt売上額小計, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt消費税小計, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt請求額小計, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextBox5, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextBox6, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextBox7, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextBox8, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt前回額合計, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt入金額合計, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt調整額合計, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt繰越額合計, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt売上額合計, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt消費税合計, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt請求額合計, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me, System.ComponentModel.ISupportInitialize).EndInit()

    End Sub

    Private WithEvents GroupHeader1 As GrapeCity.ActiveReports.SectionReportModel.GroupHeader
    Private WithEvents GroupFooter1 As GrapeCity.ActiveReports.SectionReportModel.GroupFooter
    Private WithEvents lbl得意先コード As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents txt得意先コード As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents lbl得意先名 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents txt得意先名 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents lbl工事コード As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents lbl工事名 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents txt工事コード As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt工事名 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents lbl前回額 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents lbl入金額 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents lbl調整額 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents lbl繰越額 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents lbl売上額 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents lbl消費税 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents lbl請求額 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents txt前回額 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt入金額 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt調整額 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt繰越額 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt売上額 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt消費税 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt請求額 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents TextBox1 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents TextBox2 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents TextBox3 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents TextBox4 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt前回額小計 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt入金額小計 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt調整額小計 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt繰越額小計 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt売上額小計 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt消費税小計 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt請求額小計 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents GroupHeader2 As GrapeCity.ActiveReports.SectionReportModel.GroupHeader
    Private WithEvents GroupFooter2 As GrapeCity.ActiveReports.SectionReportModel.GroupFooter
    Private WithEvents TextBox5 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents TextBox6 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents TextBox7 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents TextBox8 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt前回額合計 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt入金額合計 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt調整額合計 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt繰越額合計 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt売上額合計 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt消費税合計 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt請求額合計 As GrapeCity.ActiveReports.SectionReportModel.TextBox
End Class
