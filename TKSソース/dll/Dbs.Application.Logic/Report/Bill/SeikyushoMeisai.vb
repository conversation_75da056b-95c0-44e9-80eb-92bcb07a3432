﻿Imports GrapeCity.ActiveReports
Imports GrapeCity.ActiveReports.Document
Imports Dbs.Application.Logic.Database
Imports Dbs.Asphalt.Core.Common

Public Class SeikyushoMeisai
    Private int行NO As Integer = 0

    Private strKeyNew As String = ""
    Private strKeyOld As String = ""

    Private Sub UriageNohinsho_ReportStart(sender As Object, e As EventArgs) Handles MyBase.ReportStart

        ' ﾚﾎﾟｰﾄｻｲｽﾞ
        Me.PageSettings.PaperKind = System.Drawing.Printing.PaperKind.A4
        Me.PageSettings.Orientation = GrapeCity.ActiveReports.Document.Section.PageOrientation.Landscape
        Me.PageSettings.Margins.Top = GrapeCity.ActiveReports.SectionReport.CmToInch(0.7)
        Me.PageSettings.Margins.Bottom = GrapeCity.ActiveReports.SectionReport.CmToInch(0.7)
        Me.PageSettings.Margins.Left = GrapeCity.ActiveReports.SectionReport.CmToInch(2.5)
        Me.PageSettings.Margins.Right = GrapeCity.ActiveReports.SectionReport.CmToInch(0.7)

        Dim db As New DbReport.R請求書明細DataTable

        ' ｸﾞﾙｰﾌﾟﾍｯﾀﾞの設定
        Me.GHD工事.DataField = db.工事内部コードColumn.ColumnName
        Me.GHD工事.NewPage = SectionReportModel.NewPage.None
        Me.GHD工事.RepeatStyle = SectionReportModel.RepeatStyle.OnPageIncludeNoDetail  'OnPageIncludeNoDetail･･･改頁した新頁が合計行のみの印字でもﾍｯﾀﾞを印字
        Me.GHD工事.KeepTogether = True     ' ﾍｯﾀﾞの途中で改頁されない様に

        ' ﾌｯﾀﾞ合計
        Me.txt合計金額.SummaryFunc = SectionReportModel.SummaryFunc.Sum
        Me.txt合計金額.SummaryGroup = Me.Detail.Name
        Me.txt合計金額.SummaryRunning = SectionReportModel.SummaryRunning.Group
        Me.txt合計金額.SummaryType = SectionReportModel.SummaryType.SubTotal
        Me.txt合計金額.DataField = db.金額Column.ColumnName

        ' ﾃﾞｰﾀﾌｨｰﾙﾄﾞの設定
        Me.txt基本料金.DataField = db.基本料金Column.ColumnName
        Me.txt金額.DataField = db.金額Column.ColumnName
        Me.txt繰越額.DataField = db.繰越額Column.ColumnName
        Me.txt工事コード.DataField = db.工事コードColumn.ColumnName
        Me.txt工事名.DataField = db.工事名Column.ColumnName
        Me.txt商品名.DataField = db.商品名Column.ColumnName
        Me.txt消費税.DataField = db.消費税Column.ColumnName
        Me.txt振込先.DataField = db.振込先1Column.ColumnName
        Me.txt数量.DataField = db.数量Column.ColumnName
        Me.txt請求額.DataField = db.請求額Column.ColumnName
        'Me.txt請求日付.DataField = db.請求日付Column.ColumnName
        'Me.txt税率.DataField = db.税率Column.ColumnName
        Me.txt前回額.DataField = db.前回額Column.ColumnName
        Me.txt単位区分名.DataField = db.単位区分名Column.ColumnName
        Me.txt単価.DataField = db.単価Column.ColumnName
        Me.txt調整額.DataField = db.調整額Column.ColumnName
        Me.txt登録番号.DataField = db.登録番号Column.ColumnName
        Me.txt得意先コード.DataField = db.得意先コードColumn.ColumnName
        Me.txt得意先名.DataField = db.得意先名Column.ColumnName
        Me.txt日数.DataField = db.日数Column.ColumnName
        Me.txt入金額.DataField = db.入金額Column.ColumnName
        Me.txt税抜額.DataField = db.税抜額Column.ColumnName
        Me.txtグループNO.DataField = db.グループNOColumn.ColumnName

        Me.hid工事内部コード.DataField = db.工事内部コードColumn.ColumnName
        Me.hid請求日付.DataField = db.請求日付至Column.ColumnName
        Me.hid搬入日付.DataField = db.搬入日付Column.ColumnName
        Me.hid開始日付.DataField = db.開始日付Column.ColumnName
        Me.hid終了日付.DataField = db.終了日付Column.ColumnName
        Me.hid返納日付.DataField = db.返納日付Column.ColumnName

        ' 書式
        Me.txt請求額.OutputFormat = "#,##0"
        Me.txt基本料金.OutputFormat = "#,##0"
        Me.txt金額.OutputFormat = "#,##0"
        Me.txt繰越額.OutputFormat = "#,##0"
        Me.txt合計金額.OutputFormat = "#,##0"
        Me.txt消費税.OutputFormat = "#,##0"
        Me.txt数量.OutputFormat = "#,##0"
        Me.txt前回額.OutputFormat = "#,##0"
        Me.txt単価.OutputFormat = "#,##0"
        Me.txt調整額.OutputFormat = "#,##0"
        Me.txt日数.OutputFormat = "#,###"
        Me.txt入金額.OutputFormat = "#,##0"
        Me.txt税抜額.OutputFormat = "#,##0"

        '' 表示/非表示
        'Me.txt行NO.Visible = False    ' ﾃｽﾄ用なので最後は非表示へ
    End Sub

    Private Sub GHD伝票番号_Format(sender As Object, e As EventArgs) Handles GHD工事.Format
        Me.txt請求日付.Text = Format(Text.CDateEx(Me.hid請求日付.Text), "yyyy年MM月dd日")

        strKeyNew = Me.hid工事内部コード.Text
        If strKeyNew = strKeyOld Then
            Me.txt前回額.Text = "***,***,***"
            Me.txt入金額.Text = "***,***,***"
            Me.txt調整額.Text = "***,***,***"
            Me.txt繰越額.Text = "***,***,***"
            Me.txt税抜額.Text = "***,***,***"
            Me.txt消費税.Text = "***,***,***"
            Me.txt請求額.Text = "***,***,***"
        End If
        strKeyOld = strKeyNew
    End Sub

    Private Sub Detail_Format(sender As Object, e As EventArgs) Handles Detail.Format

        int行NO += 1

        Me.txt搬入日付.Text = ""
        Me.txt開始日付.Text = ""
        Me.txt終了日付.Text = ""
        Me.txt返納日付.Text = ""

        If Me.hid搬入日付.Text <> "" Then : Me.txt搬入日付.Text = Format(Text.CDateEx(Me.hid搬入日付.Text), "M/d") : End If
        If Me.hid開始日付.Text <> "" Then : Me.txt開始日付.Text = Format(Text.CDateEx(Me.hid開始日付.Text), "M/d") : End If
        If Me.hid終了日付.Text <> "" Then : Me.txt終了日付.Text = Format(Text.CDateEx(Me.hid終了日付.Text), "M/d") : End If

        Select Case True
            Case Me.hid返納日付.Text = "9999/12/31"
                Me.txt返納日付.Text = "継 続"
            Case Me.hid返納日付.Text <> ""
                Me.txt返納日付.Text = Format(Text.CDateEx(Me.hid返納日付.Text), "M/d")
            Case Else
                Me.txt返納日付.Text = ""
        End Select

    End Sub
End Class
