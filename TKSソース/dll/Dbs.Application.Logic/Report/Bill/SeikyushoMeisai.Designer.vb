﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Public Class SeikyushoMeisai
    Inherits GrapeCity.ActiveReports.SectionReport

    'フォームがコンポーネントの一覧をクリーンアップするために dispose をオーバーライドします。
    Protected Overloads Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing Then
        End If
        MyBase.Dispose(disposing)
    End Sub

    'メモ: 以下のプロシージャは ActiveReports デザイナーで必要です。
    'ActiveReports デザイナーを使用して変更できます。  
    'コード エディターを使って変更しないでください。
    Private WithEvents PageHeader As GrapeCity.ActiveReports.SectionReportModel.PageHeader
    Private WithEvents Detail As GrapeCity.ActiveReports.SectionReportModel.Detail
    Private WithEvents PageFooter As GrapeCity.ActiveReports.SectionReportModel.PageFooter
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Dim resources As System.Resources.ResourceManager = New System.Resources.ResourceManager(GetType(SeikyushoMeisai))
        Me.PageHeader = New GrapeCity.ActiveReports.SectionReportModel.PageHeader()
        Me.Detail = New GrapeCity.ActiveReports.SectionReportModel.Detail()
        Me.txt商品名 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt搬入日付 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt開始日付 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt終了日付 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt返納日付 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt日数 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt数量 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt単位区分名 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt単価 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt基本料金 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt金額 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.hid搬入日付 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.hid開始日付 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.hid終了日付 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.hid返納日付 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.PageFooter = New GrapeCity.ActiveReports.SectionReportModel.PageFooter()
        Me.GHD工事 = New GrapeCity.ActiveReports.SectionReportModel.GroupHeader()
        Me.lbl請求額 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.txt請求額 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.lbl前回額 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.txt前回額 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.lbl入金額 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.lbl調整額 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.lbl繰越額 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.lbl売上額 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.txt入金額 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt調整額 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt繰越額 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt税抜額 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.lbl工事CD = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.lbl工事請求額 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.txtタイトル = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt得意先名 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt請求日付 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt得意先コード = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.lbl得意先コード = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.txt登録番号 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.lbl登録番号 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.lbl振込先 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.txt振込先 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt税率 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.lblmsg = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.lbl消費税 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.txt消費税 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt工事名 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt工事コード = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.lbl工事コード = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.Label8 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.Label9 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.Label10 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.Label11 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.Label12 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.Label13 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.Label14 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.Label15 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.Label16 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.Label17 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.hid請求日付 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txtNo = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txtグループNO = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.hid工事内部コード = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt控 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.GFT工事 = New GrapeCity.ActiveReports.SectionReportModel.GroupFooter()
        Me.TextBox12 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.TextBox13 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.TextBox14 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.TextBox15 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.TextBox16 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.TextBox17 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.TextBox18 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.TextBox19 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.TextBox20 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.TextBox21 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt合計金額 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        CType(Me.txt商品名, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt搬入日付, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt開始日付, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt終了日付, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt返納日付, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt日数, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt数量, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt単位区分名, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt単価, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt基本料金, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt金額, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.hid搬入日付, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.hid開始日付, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.hid終了日付, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.hid返納日付, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lbl請求額, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt請求額, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lbl前回額, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt前回額, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lbl入金額, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lbl調整額, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lbl繰越額, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lbl売上額, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt入金額, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt調整額, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt繰越額, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt税抜額, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lbl工事CD, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lbl工事請求額, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtタイトル, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt得意先名, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt請求日付, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt得意先コード, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lbl得意先コード, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt登録番号, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lbl登録番号, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lbl振込先, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt振込先, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt税率, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lblmsg, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lbl消費税, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt消費税, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt工事名, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt工事コード, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lbl工事コード, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Label8, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Label9, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Label10, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Label11, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Label12, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Label13, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Label14, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Label15, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Label16, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Label17, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.hid請求日付, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtNo, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtグループNO, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.hid工事内部コード, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt控, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextBox12, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextBox13, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextBox14, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextBox15, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextBox16, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextBox17, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextBox18, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextBox19, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextBox20, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextBox21, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt合計金額, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me, System.ComponentModel.ISupportInitialize).BeginInit()
        '
        'PageHeader
        '
        Me.PageHeader.Height = 0!
        Me.PageHeader.Name = "PageHeader"
        '
        'Detail
        '
        Me.Detail.Controls.AddRange(New GrapeCity.ActiveReports.SectionReportModel.ARControl() {Me.txt商品名, Me.txt搬入日付, Me.txt開始日付, Me.txt終了日付, Me.txt返納日付, Me.txt日数, Me.txt数量, Me.txt単位区分名, Me.txt単価, Me.txt基本料金, Me.txt金額, Me.hid搬入日付, Me.hid開始日付, Me.hid終了日付, Me.hid返納日付})
        Me.Detail.Height = 0.1653543!
        Me.Detail.Name = "Detail"
        Me.Detail.RepeatToFill = True
        '
        'txt商品名
        '
        Me.txt商品名.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt商品名.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt商品名.CanGrow = False
        Me.txt商品名.Height = 0.1653543!
        Me.txt商品名.Left = 0.5511813!
        Me.txt商品名.Name = "txt商品名"
        Me.txt商品名.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 0, 0, 0)
        Me.txt商品名.ShrinkToFit = True
        Me.txt商品名.Style = "font-family: ＭＳ 明朝; font-size: 11pt; font-weight: normal; text-align: left; text-" &
    "justify: auto; vertical-align: middle; ddo-char-set: 1; ddo-shrink-to-fit: true"
        Me.txt商品名.Text = "立花建設 株式会社"
        Me.txt商品名.Top = 0!
        Me.txt商品名.Width = 2.913386!
        '
        'txt搬入日付
        '
        Me.txt搬入日付.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt搬入日付.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt搬入日付.CanGrow = False
        Me.txt搬入日付.Height = 0.1653543!
        Me.txt搬入日付.Left = 0!
        Me.txt搬入日付.Name = "txt搬入日付"
        Me.txt搬入日付.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.txt搬入日付.Style = "font-family: ＭＳ 明朝; font-size: 11pt; font-weight: normal; text-align: center; tex" &
    "t-justify: auto; vertical-align: middle; ddo-char-set: 1"
        Me.txt搬入日付.Text = "99/99"
        Me.txt搬入日付.Top = 0!
        Me.txt搬入日付.Width = 0.5511811!
        '
        'txt開始日付
        '
        Me.txt開始日付.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt開始日付.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt開始日付.CanGrow = False
        Me.txt開始日付.Height = 0.1653543!
        Me.txt開始日付.Left = 3.464567!
        Me.txt開始日付.Name = "txt開始日付"
        Me.txt開始日付.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.txt開始日付.Style = "font-family: ＭＳ 明朝; font-size: 11pt; font-weight: normal; text-align: center; tex" &
    "t-justify: auto; vertical-align: middle; ddo-char-set: 1"
        Me.txt開始日付.Text = "99/99"
        Me.txt開始日付.Top = 0!
        Me.txt開始日付.Width = 0.6102362!
        '
        'txt終了日付
        '
        Me.txt終了日付.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt終了日付.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt終了日付.CanGrow = False
        Me.txt終了日付.Height = 0.1653543!
        Me.txt終了日付.Left = 4.074803!
        Me.txt終了日付.Name = "txt終了日付"
        Me.txt終了日付.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.txt終了日付.Style = "font-family: ＭＳ 明朝; font-size: 11pt; font-weight: normal; text-align: center; tex" &
    "t-justify: auto; vertical-align: middle; ddo-char-set: 1"
        Me.txt終了日付.Text = "99/99"
        Me.txt終了日付.Top = 0!
        Me.txt終了日付.Width = 0.6102362!
        '
        'txt返納日付
        '
        Me.txt返納日付.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt返納日付.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt返納日付.CanGrow = False
        Me.txt返納日付.Height = 0.1653543!
        Me.txt返納日付.Left = 4.68504!
        Me.txt返納日付.Name = "txt返納日付"
        Me.txt返納日付.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.txt返納日付.Style = "font-family: ＭＳ 明朝; font-size: 11pt; font-weight: normal; text-align: center; tex" &
    "t-justify: auto; vertical-align: middle; ddo-char-set: 1"
        Me.txt返納日付.Text = "99/99"
        Me.txt返納日付.Top = 0!
        Me.txt返納日付.Width = 0.6102362!
        '
        'txt日数
        '
        Me.txt日数.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt日数.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt日数.CanGrow = False
        Me.txt日数.Height = 0.1653543!
        Me.txt日数.Left = 5.295276!
        Me.txt日数.Name = "txt日数"
        Me.txt日数.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 8, 0)
        Me.txt日数.Style = "font-family: ＭＳ 明朝; font-size: 11.25pt; text-align: right; vertical-align: middle" &
    "; ddo-shrink-to-fit: none"
        Me.txt日数.Text = "999"
        Me.txt日数.Top = 0!
        Me.txt日数.Width = 0.472441!
        '
        'txt数量
        '
        Me.txt数量.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt数量.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt数量.CanGrow = False
        Me.txt数量.Height = 0.1653543!
        Me.txt数量.Left = 5.767717!
        Me.txt数量.Name = "txt数量"
        Me.txt数量.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 8, 0)
        Me.txt数量.Style = "font-family: ＭＳ 明朝; font-size: 11.25pt; text-align: right; vertical-align: middle" &
    "; ddo-shrink-to-fit: none"
        Me.txt数量.Text = "999"
        Me.txt数量.Top = 0!
        Me.txt数量.Width = 0.5511811!
        '
        'txt単位区分名
        '
        Me.txt単位区分名.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt単位区分名.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt単位区分名.CanGrow = False
        Me.txt単位区分名.Height = 0.1653543!
        Me.txt単位区分名.Left = 6.318898!
        Me.txt単位区分名.Name = "txt単位区分名"
        Me.txt単位区分名.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 0, 0, 0)
        Me.txt単位区分名.Style = "font-family: ＭＳ 明朝; font-size: 11pt; font-weight: normal; text-align: left; text-" &
    "justify: auto; vertical-align: middle; ddo-char-set: 1"
        Me.txt単位区分名.Text = "XXXX"
        Me.txt単位区分名.Top = 0!
        Me.txt単位区分名.Width = 0.4527559!
        '
        'txt単価
        '
        Me.txt単価.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt単価.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt単価.CanGrow = False
        Me.txt単価.Height = 0.1653543!
        Me.txt単価.Left = 6.771654!
        Me.txt単価.Name = "txt単価"
        Me.txt単価.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 8, 0)
        Me.txt単価.Style = "font-family: ＭＳ 明朝; font-size: 11.25pt; text-align: right; vertical-align: middle" &
    "; ddo-shrink-to-fit: none"
        Me.txt単価.Text = "9,999"
        Me.txt単価.Top = 0!
        Me.txt単価.Width = 1.003937!
        '
        'txt基本料金
        '
        Me.txt基本料金.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt基本料金.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt基本料金.CanGrow = False
        Me.txt基本料金.Height = 0.1653543!
        Me.txt基本料金.Left = 7.775592!
        Me.txt基本料金.Name = "txt基本料金"
        Me.txt基本料金.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 8, 0)
        Me.txt基本料金.Style = "font-family: ＭＳ 明朝; font-size: 11.25pt; text-align: right; vertical-align: middle" &
    "; ddo-shrink-to-fit: none"
        Me.txt基本料金.Text = "9,999"
        Me.txt基本料金.Top = 0!
        Me.txt基本料金.Width = 1.003937!
        '
        'txt金額
        '
        Me.txt金額.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt金額.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt金額.CanGrow = False
        Me.txt金額.Height = 0.1653543!
        Me.txt金額.Left = 8.779529!
        Me.txt金額.Name = "txt金額"
        Me.txt金額.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 8, 0)
        Me.txt金額.Style = "font-family: ＭＳ 明朝; font-size: 11.25pt; text-align: right; vertical-align: middle" &
    "; ddo-shrink-to-fit: none"
        Me.txt金額.Text = "9,999"
        Me.txt金額.Top = 0!
        Me.txt金額.Width = 1.003937!
        '
        'hid搬入日付
        '
        Me.hid搬入日付.CanGrow = False
        Me.hid搬入日付.Height = 0.1181102!
        Me.hid搬入日付.Left = 0.3732284!
        Me.hid搬入日付.Name = "hid搬入日付"
        Me.hid搬入日付.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 1, 0)
        Me.hid搬入日付.ShrinkToFit = True
        Me.hid搬入日付.Style = "background-color: Red; font-family: ＭＳ 明朝; font-size: 9.75pt; text-align: right; " &
    "vertical-align: middle; ddo-shrink-to-fit: true"
        Me.hid搬入日付.Text = Nothing
        Me.hid搬入日付.Top = 0!
        Me.hid搬入日付.Visible = False
        Me.hid搬入日付.Width = 0.1181102!
        '
        'hid開始日付
        '
        Me.hid開始日付.CanGrow = False
        Me.hid開始日付.Height = 0.1181102!
        Me.hid開始日付.Left = 3.508662!
        Me.hid開始日付.Name = "hid開始日付"
        Me.hid開始日付.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 1, 0)
        Me.hid開始日付.ShrinkToFit = True
        Me.hid開始日付.Style = "background-color: Red; font-family: ＭＳ 明朝; font-size: 9.75pt; text-align: right; " &
    "vertical-align: middle; ddo-shrink-to-fit: true"
        Me.hid開始日付.Text = Nothing
        Me.hid開始日付.Top = 0!
        Me.hid開始日付.Visible = False
        Me.hid開始日付.Width = 0.1181102!
        '
        'hid終了日付
        '
        Me.hid終了日付.CanGrow = False
        Me.hid終了日付.Height = 0.1181102!
        Me.hid終了日付.Left = 4.154331!
        Me.hid終了日付.Name = "hid終了日付"
        Me.hid終了日付.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 1, 0)
        Me.hid終了日付.ShrinkToFit = True
        Me.hid終了日付.Style = "background-color: Red; font-family: ＭＳ 明朝; font-size: 9.75pt; text-align: right; " &
    "vertical-align: middle; ddo-shrink-to-fit: true"
        Me.hid終了日付.Text = Nothing
        Me.hid終了日付.Top = 0!
        Me.hid終了日付.Visible = False
        Me.hid終了日付.Width = 0.1181102!
        '
        'hid返納日付
        '
        Me.hid返納日付.CanGrow = False
        Me.hid返納日付.Height = 0.1181102!
        Me.hid返納日付.Left = 4.81063!
        Me.hid返納日付.Name = "hid返納日付"
        Me.hid返納日付.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 1, 0)
        Me.hid返納日付.ShrinkToFit = True
        Me.hid返納日付.Style = "background-color: Red; font-family: ＭＳ 明朝; font-size: 9.75pt; text-align: right; " &
    "vertical-align: middle; ddo-shrink-to-fit: true"
        Me.hid返納日付.Text = Nothing
        Me.hid返納日付.Top = 0!
        Me.hid返納日付.Visible = False
        Me.hid返納日付.Width = 0.1181102!
        '
        'PageFooter
        '
        Me.PageFooter.Height = 0!
        Me.PageFooter.Name = "PageFooter"
        '
        'GHD工事
        '
        Me.GHD工事.Controls.AddRange(New GrapeCity.ActiveReports.SectionReportModel.ARControl() {Me.lbl請求額, Me.txt請求額, Me.lbl前回額, Me.txt前回額, Me.lbl入金額, Me.lbl調整額, Me.lbl繰越額, Me.lbl売上額, Me.txt入金額, Me.txt調整額, Me.txt繰越額, Me.txt税抜額, Me.lbl工事CD, Me.lbl工事請求額, Me.txtタイトル, Me.txt得意先名, Me.txt請求日付, Me.txt得意先コード, Me.lbl得意先コード, Me.txt登録番号, Me.lbl登録番号, Me.lbl振込先, Me.txt振込先, Me.txt税率, Me.lblmsg, Me.lbl消費税, Me.txt消費税, Me.txt工事名, Me.txt工事コード, Me.lbl工事コード, Me.Label8, Me.Label9, Me.Label10, Me.Label11, Me.Label12, Me.Label13, Me.Label14, Me.Label15, Me.Label16, Me.Label17, Me.hid請求日付, Me.txtNo, Me.txtグループNO, Me.hid工事内部コード, Me.txt控})
        Me.GHD工事.Height = 2.629922!
        Me.GHD工事.Name = "GHD工事"
        '
        'lbl請求額
        '
        Me.lbl請求額.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl請求額.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl請求額.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.ThickSolid
        Me.lbl請求額.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.ThickSolid
        Me.lbl請求額.Height = 0.3267716!
        Me.lbl請求額.HyperLink = Nothing
        Me.lbl請求額.Left = 8.228347!
        Me.lbl請求額.Name = "lbl請求額"
        Me.lbl請求額.Style = "font-family: ＭＳ 明朝; font-size: 11.25pt; text-align: center; vertical-align: middl" &
    "e"
        Me.lbl請求額.Text = "当月御請求額"
        Me.lbl請求額.Top = 1.562992!
        Me.lbl請求額.Width = 1.496063!
        '
        'txt請求額
        '
        Me.txt請求額.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.ThickSolid
        Me.txt請求額.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt請求額.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.ThickSolid
        Me.txt請求額.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt請求額.CanGrow = False
        Me.txt請求額.Height = 0.3267716!
        Me.txt請求額.Left = 8.228347!
        Me.txt請求額.Name = "txt請求額"
        Me.txt請求額.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 8, 0)
        Me.txt請求額.ShrinkToFit = True
        Me.txt請求額.Style = "font-family: ＭＳ 明朝; font-size: 11.25pt; text-align: right; vertical-align: middle" &
    "; ddo-shrink-to-fit: true"
        Me.txt請求額.Text = "9,999"
        Me.txt請求額.Top = 1.889764!
        Me.txt請求額.Width = 1.496063!
        '
        'lbl前回額
        '
        Me.lbl前回額.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl前回額.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.ThickSolid
        Me.lbl前回額.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl前回額.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.ThickSolid
        Me.lbl前回額.Height = 0.3267716!
        Me.lbl前回額.HyperLink = Nothing
        Me.lbl前回額.Left = 0.03937008!
        Me.lbl前回額.Name = "lbl前回額"
        Me.lbl前回額.Style = "font-family: ＭＳ 明朝; font-size: 11.25pt; text-align: center; vertical-align: middl" &
    "e"
        Me.lbl前回額.Text = "前回御請求額"
        Me.lbl前回額.Top = 1.562992!
        Me.lbl前回額.Width = 1.496063!
        '
        'txt前回額
        '
        Me.txt前回額.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.ThickSolid
        Me.txt前回額.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.ThickSolid
        Me.txt前回額.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt前回額.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt前回額.CanGrow = False
        Me.txt前回額.Height = 0.3267716!
        Me.txt前回額.Left = 0.03937008!
        Me.txt前回額.Name = "txt前回額"
        Me.txt前回額.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 8, 0)
        Me.txt前回額.ShrinkToFit = True
        Me.txt前回額.Style = "font-family: ＭＳ 明朝; font-size: 11.25pt; text-align: right; vertical-align: middle" &
    "; ddo-shrink-to-fit: true"
        Me.txt前回額.Text = "9,999"
        Me.txt前回額.Top = 1.889764!
        Me.txt前回額.Width = 1.496063!
        '
        'lbl入金額
        '
        Me.lbl入金額.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl入金額.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl入金額.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl入金額.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.ThickSolid
        Me.lbl入金額.Height = 0.3267716!
        Me.lbl入金額.HyperLink = Nothing
        Me.lbl入金額.Left = 1.535433!
        Me.lbl入金額.Name = "lbl入金額"
        Me.lbl入金額.Style = "font-family: ＭＳ 明朝; font-size: 11.25pt; text-align: center; vertical-align: middl" &
    "e"
        Me.lbl入金額.Text = "当月御入金額"
        Me.lbl入金額.Top = 1.562992!
        Me.lbl入金額.Width = 1.338583!
        '
        'lbl調整額
        '
        Me.lbl調整額.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl調整額.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl調整額.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl調整額.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.ThickSolid
        Me.lbl調整額.Height = 0.3267716!
        Me.lbl調整額.HyperLink = Nothing
        Me.lbl調整額.Left = 2.874016!
        Me.lbl調整額.Name = "lbl調整額"
        Me.lbl調整額.Style = "font-family: ＭＳ 明朝; font-size: 11.25pt; text-align: center; vertical-align: middl" &
    "e"
        Me.lbl調整額.Text = "当月調整額"
        Me.lbl調整額.Top = 1.562992!
        Me.lbl調整額.Width = 1.338583!
        '
        'lbl繰越額
        '
        Me.lbl繰越額.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl繰越額.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl繰越額.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl繰越額.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.ThickSolid
        Me.lbl繰越額.Height = 0.3267716!
        Me.lbl繰越額.HyperLink = Nothing
        Me.lbl繰越額.Left = 4.212599!
        Me.lbl繰越額.Name = "lbl繰越額"
        Me.lbl繰越額.Style = "font-family: ＭＳ 明朝; font-size: 11.25pt; text-align: center; vertical-align: middl" &
    "e"
        Me.lbl繰越額.Text = "繰  越  額"
        Me.lbl繰越額.Top = 1.562992!
        Me.lbl繰越額.Width = 1.338583!
        '
        'lbl売上額
        '
        Me.lbl売上額.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl売上額.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl売上額.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl売上額.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.ThickSolid
        Me.lbl売上額.Height = 0.3267716!
        Me.lbl売上額.HyperLink = Nothing
        Me.lbl売上額.Left = 5.551181!
        Me.lbl売上額.Name = "lbl売上額"
        Me.lbl売上額.Style = "font-family: ＭＳ 明朝; font-size: 11.25pt; text-align: center; vertical-align: middl" &
    "e"
        Me.lbl売上額.Text = "当月御買上額"
        Me.lbl売上額.Top = 1.562992!
        Me.lbl売上額.Width = 1.338583!
        '
        'txt入金額
        '
        Me.txt入金額.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.ThickSolid
        Me.txt入金額.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt入金額.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt入金額.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt入金額.CanGrow = False
        Me.txt入金額.Height = 0.3267716!
        Me.txt入金額.Left = 1.535433!
        Me.txt入金額.Name = "txt入金額"
        Me.txt入金額.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 8, 0)
        Me.txt入金額.ShrinkToFit = True
        Me.txt入金額.Style = "font-family: ＭＳ 明朝; font-size: 11.25pt; text-align: right; vertical-align: middle" &
    "; ddo-shrink-to-fit: true"
        Me.txt入金額.Text = "9,999"
        Me.txt入金額.Top = 1.889764!
        Me.txt入金額.Width = 1.338583!
        '
        'txt調整額
        '
        Me.txt調整額.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.ThickSolid
        Me.txt調整額.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt調整額.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt調整額.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt調整額.CanGrow = False
        Me.txt調整額.Height = 0.3267716!
        Me.txt調整額.Left = 2.874016!
        Me.txt調整額.Name = "txt調整額"
        Me.txt調整額.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 8, 0)
        Me.txt調整額.ShrinkToFit = True
        Me.txt調整額.Style = "font-family: ＭＳ 明朝; font-size: 11.25pt; text-align: right; vertical-align: middle" &
    "; ddo-shrink-to-fit: true"
        Me.txt調整額.Text = "9,999"
        Me.txt調整額.Top = 1.889764!
        Me.txt調整額.Width = 1.338583!
        '
        'txt繰越額
        '
        Me.txt繰越額.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.ThickSolid
        Me.txt繰越額.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt繰越額.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt繰越額.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt繰越額.CanGrow = False
        Me.txt繰越額.Height = 0.3267716!
        Me.txt繰越額.Left = 4.212599!
        Me.txt繰越額.Name = "txt繰越額"
        Me.txt繰越額.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 8, 0)
        Me.txt繰越額.ShrinkToFit = True
        Me.txt繰越額.Style = "font-family: ＭＳ 明朝; font-size: 11.25pt; text-align: right; vertical-align: middle" &
    "; ddo-shrink-to-fit: true"
        Me.txt繰越額.Text = "9,999"
        Me.txt繰越額.Top = 1.889764!
        Me.txt繰越額.Width = 1.338583!
        '
        'txt税抜額
        '
        Me.txt税抜額.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.ThickSolid
        Me.txt税抜額.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt税抜額.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt税抜額.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt税抜額.CanGrow = False
        Me.txt税抜額.Height = 0.3267716!
        Me.txt税抜額.Left = 5.551181!
        Me.txt税抜額.Name = "txt税抜額"
        Me.txt税抜額.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 8, 0)
        Me.txt税抜額.ShrinkToFit = True
        Me.txt税抜額.Style = "font-family: ＭＳ 明朝; font-size: 11.25pt; text-align: right; vertical-align: middle" &
    "; ddo-shrink-to-fit: true"
        Me.txt税抜額.Text = "9,999"
        Me.txt税抜額.Top = 1.889764!
        Me.txt税抜額.Width = 1.338583!
        '
        'lbl工事CD
        '
        Me.lbl工事CD.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl工事CD.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl工事CD.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl工事CD.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl工事CD.Height = 0.3267717!
        Me.lbl工事CD.HyperLink = Nothing
        Me.lbl工事CD.Left = 0!
        Me.lbl工事CD.Name = "lbl工事CD"
        Me.lbl工事CD.Style = "font-family: ＭＳ 明朝; font-size: 11.25pt; text-align: center; vertical-align: middl" &
    "e"
        Me.lbl工事CD.Text = "日付"
        Me.lbl工事CD.Top = 2.30315!
        Me.lbl工事CD.Width = 0.5511811!
        '
        'lbl工事請求額
        '
        Me.lbl工事請求額.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl工事請求額.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl工事請求額.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl工事請求額.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl工事請求額.Height = 0.3267717!
        Me.lbl工事請求額.HyperLink = Nothing
        Me.lbl工事請求額.Left = 8.779529!
        Me.lbl工事請求額.Name = "lbl工事請求額"
        Me.lbl工事請求額.Style = "font-family: ＭＳ 明朝; font-size: 11.25pt; text-align: center; vertical-align: middl" &
    "e"
        Me.lbl工事請求額.Text = "金額"
        Me.lbl工事請求額.Top = 2.30315!
        Me.lbl工事請求額.Width = 1.003937!
        '
        'txtタイトル
        '
        Me.txtタイトル.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtタイトル.CanGrow = False
        Me.txtタイトル.CharacterSpacing = 18.0!
        Me.txtタイトル.Height = 0.2755905!
        Me.txtタイトル.Left = 3.897638!
        Me.txtタイトル.Name = "txtタイトル"
        Me.txtタイトル.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.txtタイトル.Style = "font-family: ＭＳ 明朝; font-size: 14.25pt; font-weight: normal; text-align: center; " &
    "text-justify: auto; vertical-align: middle"
        Me.txtタイトル.Text = "御請求書"
        Me.txtタイトル.Top = 0!
        Me.txtタイトル.Width = 1.968504!
        '
        'txt得意先名
        '
        Me.txt得意先名.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt得意先名.CanGrow = False
        Me.txt得意先名.Height = 0.2755905!
        Me.txt得意先名.Left = 0.3149606!
        Me.txt得意先名.Name = "txt得意先名"
        Me.txt得意先名.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.txt得意先名.Style = "font-family: ＭＳ 明朝; font-size: 11pt; font-weight: normal; text-align: left; text-" &
    "justify: auto; vertical-align: middle; ddo-char-set: 1"
        Me.txt得意先名.Text = "立花建設 株式会社"
        Me.txt得意先名.Top = 0.511811!
        Me.txt得意先名.Width = 2.755905!
        '
        'txt請求日付
        '
        Me.txt請求日付.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt請求日付.CanGrow = False
        Me.txt請求日付.Height = 0.2755905!
        Me.txt請求日付.Left = 8.622046!
        Me.txt請求日付.Name = "txt請求日付"
        Me.txt請求日付.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.txt請求日付.Style = "font-family: ＭＳ 明朝; font-size: 11pt; font-weight: normal; text-align: center; tex" &
    "t-justify: auto; vertical-align: middle; ddo-char-set: 1"
        Me.txt請求日付.Text = "yyyy年MM月dd日"
        Me.txt請求日付.Top = 0.3149606!
        Me.txt請求日付.Width = 1.181102!
        '
        'txt得意先コード
        '
        Me.txt得意先コード.CanGrow = False
        Me.txt得意先コード.Height = 0.1574803!
        Me.txt得意先コード.Left = 1.220473!
        Me.txt得意先コード.Name = "txt得意先コード"
        Me.txt得意先コード.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.txt得意先コード.Style = "font-family: ＭＳ 明朝; font-size: 9.75pt; font-weight: normal; text-align: left; tex" &
    "t-justify: auto; vertical-align: middle; ddo-char-set: 1"
        Me.txt得意先コード.Text = "012345"
        Me.txt得意先コード.Top = 0.3543307!
        Me.txt得意先コード.Width = 0.7185037!
        '
        'lbl得意先コード
        '
        Me.lbl得意先コード.Height = 0.1574803!
        Me.lbl得意先コード.HyperLink = Nothing
        Me.lbl得意先コード.Left = 0.3149607!
        Me.lbl得意先コード.Name = "lbl得意先コード"
        Me.lbl得意先コード.Style = "font-family: ＭＳ 明朝; font-size: 9.75pt; text-align: left; vertical-align: middle; " &
    "ddo-char-set: 128"
        Me.lbl得意先コード.Text = "お客様コード"
        Me.lbl得意先コード.Top = 0.3543307!
        Me.lbl得意先コード.Width = 0.9055118!
        '
        'txt登録番号
        '
        Me.txt登録番号.CanGrow = False
        Me.txt登録番号.Height = 0.1574803!
        Me.txt登録番号.Left = 4.094489!
        Me.txt登録番号.Name = "txt登録番号"
        Me.txt登録番号.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.txt登録番号.Style = "font-family: ＭＳ 明朝; font-size: 9.75pt; font-weight: normal; text-align: left; tex" &
    "t-justify: auto; vertical-align: middle; ddo-char-set: 1"
        Me.txt登録番号.Text = "T3-0118-0101-0290"
        Me.txt登録番号.Top = 1.161417!
        Me.txt登録番号.Width = 1.496063!
        '
        'lbl登録番号
        '
        Me.lbl登録番号.Height = 0.1574803!
        Me.lbl登録番号.HyperLink = Nothing
        Me.lbl登録番号.Left = 3.425198!
        Me.lbl登録番号.Name = "lbl登録番号"
        Me.lbl登録番号.Style = "font-family: ＭＳ 明朝; font-size: 9.75pt; text-align: left; vertical-align: middle; " &
    "ddo-char-set: 128"
        Me.lbl登録番号.Text = "登録番号"
        Me.lbl登録番号.Top = 1.161417!
        Me.lbl登録番号.Width = 0.6692914!
        '
        'lbl振込先
        '
        Me.lbl振込先.Height = 0.1574803!
        Me.lbl振込先.HyperLink = Nothing
        Me.lbl振込先.Left = 3.425198!
        Me.lbl振込先.Name = "lbl振込先"
        Me.lbl振込先.Style = "font-family: ＭＳ 明朝; font-size: 9.75pt; text-align: left; vertical-align: middle; " &
    "ddo-char-set: 128"
        Me.lbl振込先.Text = "お振込先"
        Me.lbl振込先.Top = 1.370079!
        Me.lbl振込先.Width = 0.6692914!
        '
        'txt振込先
        '
        Me.txt振込先.CanGrow = False
        Me.txt振込先.Height = 0.1574803!
        Me.txt振込先.Left = 4.094489!
        Me.txt振込先.Name = "txt振込先"
        Me.txt振込先.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.txt振込先.Style = "font-family: ＭＳ 明朝; font-size: 9.75pt; font-weight: normal; text-align: left; tex" &
    "t-justify: auto; vertical-align: middle; ddo-char-set: 1"
        Me.txt振込先.Text = "三菱UFJ銀行　竹ノ塚支店　当座 450726"
        Me.txt振込先.Top = 1.370079!
        Me.txt振込先.Width = 2.598425!
        '
        'txt税率
        '
        Me.txt税率.CanGrow = False
        Me.txt税率.Height = 0.1574803!
        Me.txt税率.Left = 6.692915!
        Me.txt税率.Name = "txt税率"
        Me.txt税率.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.txt税率.Style = "font-family: ＭＳ 明朝; font-size: 9.75pt; font-weight: normal; text-align: center; t" &
    "ext-justify: auto; vertical-align: middle; ddo-char-set: 1"
        Me.txt税率.Text = "(税率10%)"
        Me.txt税率.Top = 1.370079!
        Me.txt税率.Width = 0.9125977!
        '
        'lblmsg
        '
        Me.lblmsg.Height = 0.1574803!
        Me.lblmsg.HyperLink = Nothing
        Me.lblmsg.Left = 0.04921263!
        Me.lblmsg.Name = "lblmsg"
        Me.lblmsg.Style = "font-family: ＭＳ 明朝; font-size: 9.75pt; text-align: left; vertical-align: middle; " &
    "ddo-char-set: 128"
        Me.lblmsg.Text = "下記のとおり御請求申し上げます。"
        Me.lblmsg.Top = 1.370079!
        Me.lblmsg.Width = 3.149606!
        '
        'lbl消費税
        '
        Me.lbl消費税.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl消費税.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl消費税.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl消費税.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.ThickSolid
        Me.lbl消費税.Height = 0.3267717!
        Me.lbl消費税.HyperLink = Nothing
        Me.lbl消費税.Left = 6.889764!
        Me.lbl消費税.Name = "lbl消費税"
        Me.lbl消費税.Style = "font-family: ＭＳ 明朝; font-size: 11.25pt; text-align: center; vertical-align: middl" &
    "e"
        Me.lbl消費税.Text = "消費税額"
        Me.lbl消費税.Top = 1.562992!
        Me.lbl消費税.Width = 1.338583!
        '
        'txt消費税
        '
        Me.txt消費税.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.ThickSolid
        Me.txt消費税.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt消費税.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt消費税.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt消費税.CanGrow = False
        Me.txt消費税.Height = 0.3267716!
        Me.txt消費税.Left = 6.889764!
        Me.txt消費税.Name = "txt消費税"
        Me.txt消費税.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 8, 0)
        Me.txt消費税.ShrinkToFit = True
        Me.txt消費税.Style = "font-family: ＭＳ 明朝; font-size: 11.25pt; text-align: right; vertical-align: middle" &
    "; ddo-shrink-to-fit: true"
        Me.txt消費税.Text = "9,999"
        Me.txt消費税.Top = 1.889764!
        Me.txt消費税.Width = 1.338583!
        '
        'txt工事名
        '
        Me.txt工事名.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt工事名.CanGrow = False
        Me.txt工事名.Height = 0.2755905!
        Me.txt工事名.Left = 0.3149605!
        Me.txt工事名.Name = "txt工事名"
        Me.txt工事名.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.txt工事名.ShrinkToFit = True
        Me.txt工事名.Style = "font-family: ＭＳ 明朝; font-size: 11pt; font-weight: normal; text-align: left; text-" &
    "justify: auto; vertical-align: middle; ddo-char-set: 1; ddo-shrink-to-fit: true"
        Me.txt工事名.Text = "東京消防庁光が丘消防署北町出張所"
        Me.txt工事名.Top = 1.023622!
        Me.txt工事名.Width = 2.755905!
        '
        'txt工事コード
        '
        Me.txt工事コード.CanGrow = False
        Me.txt工事コード.Height = 0.1574803!
        Me.txt工事コード.Left = 1.220472!
        Me.txt工事コード.Name = "txt工事コード"
        Me.txt工事コード.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.txt工事コード.Style = "font-family: ＭＳ 明朝; font-size: 9.75pt; font-weight: normal; text-align: left; tex" &
    "t-justify: auto; vertical-align: middle; ddo-char-set: 1"
        Me.txt工事コード.Text = "012345"
        Me.txt工事コード.Top = 0.8661418!
        Me.txt工事コード.Width = 0.7185035!
        '
        'lbl工事コード
        '
        Me.lbl工事コード.Height = 0.1574803!
        Me.lbl工事コード.HyperLink = Nothing
        Me.lbl工事コード.Left = 0.3149605!
        Me.lbl工事コード.Name = "lbl工事コード"
        Me.lbl工事コード.Style = "font-family: ＭＳ 明朝; font-size: 9.75pt; text-align: left; vertical-align: middle; " &
    "ddo-char-set: 128"
        Me.lbl工事コード.Text = "工事コード"
        Me.lbl工事コード.Top = 0.8661418!
        Me.lbl工事コード.Width = 0.9055118!
        '
        'Label8
        '
        Me.Label8.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.Label8.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.Label8.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.Label8.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.Label8.Height = 0.3267717!
        Me.Label8.HyperLink = Nothing
        Me.Label8.Left = 7.775592!
        Me.Label8.Name = "Label8"
        Me.Label8.Style = "font-family: ＭＳ 明朝; font-size: 11.25pt; text-align: center; vertical-align: middl" &
    "e"
        Me.Label8.Text = "基本料金"
        Me.Label8.Top = 2.30315!
        Me.Label8.Width = 1.003937!
        '
        'Label9
        '
        Me.Label9.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.Label9.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.Label9.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.Label9.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.Label9.Height = 0.3267717!
        Me.Label9.HyperLink = Nothing
        Me.Label9.Left = 6.771654!
        Me.Label9.Name = "Label9"
        Me.Label9.Style = "font-family: ＭＳ 明朝; font-size: 11.25pt; text-align: center; vertical-align: middl" &
    "e"
        Me.Label9.Text = "単価"
        Me.Label9.Top = 2.30315!
        Me.Label9.Width = 1.003937!
        '
        'Label10
        '
        Me.Label10.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.Label10.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.Label10.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.Label10.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.Label10.Height = 0.3267717!
        Me.Label10.HyperLink = Nothing
        Me.Label10.Left = 0.5511813!
        Me.Label10.Name = "Label10"
        Me.Label10.Style = "font-family: ＭＳ 明朝; font-size: 11.25pt; text-align: center; vertical-align: middl" &
    "e"
        Me.Label10.Text = "品名規格又は作業内容"
        Me.Label10.Top = 2.30315!
        Me.Label10.Width = 2.913386!
        '
        'Label11
        '
        Me.Label11.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.Label11.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.Label11.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.Label11.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.Label11.Height = 0.1633858!
        Me.Label11.HyperLink = Nothing
        Me.Label11.Left = 3.464567!
        Me.Label11.Name = "Label11"
        Me.Label11.Style = "font-family: ＭＳ 明朝; font-size: 11.25pt; text-align: center; vertical-align: middl" &
    "e"
        Me.Label11.Text = "貸　　出　　期　　間"
        Me.Label11.Top = 2.30315!
        Me.Label11.Width = 2.30315!
        '
        'Label12
        '
        Me.Label12.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.Label12.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.Label12.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.Label12.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.Label12.Height = 0.1633858!
        Me.Label12.HyperLink = Nothing
        Me.Label12.Left = 3.464567!
        Me.Label12.Name = "Label12"
        Me.Label12.Style = "font-family: ＭＳ 明朝; font-size: 11.25pt; text-align: center; vertical-align: middl" &
    "e"
        Me.Label12.Text = "開始日"
        Me.Label12.Top = 2.466536!
        Me.Label12.Width = 0.6102362!
        '
        'Label13
        '
        Me.Label13.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.Label13.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.Label13.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.Label13.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.Label13.Height = 0.1633858!
        Me.Label13.HyperLink = Nothing
        Me.Label13.Left = 4.074803!
        Me.Label13.Name = "Label13"
        Me.Label13.Style = "font-family: ＭＳ 明朝; font-size: 11.25pt; text-align: center; vertical-align: middl" &
    "e"
        Me.Label13.Text = "終了日"
        Me.Label13.Top = 2.466536!
        Me.Label13.Width = 0.6102362!
        '
        'Label14
        '
        Me.Label14.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.Label14.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.Label14.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.Label14.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.Label14.Height = 0.1633858!
        Me.Label14.HyperLink = Nothing
        Me.Label14.Left = 4.68504!
        Me.Label14.Name = "Label14"
        Me.Label14.Style = "font-family: ＭＳ 明朝; font-size: 11.25pt; text-align: center; vertical-align: middl" &
    "e"
        Me.Label14.Text = "返納日"
        Me.Label14.Top = 2.466536!
        Me.Label14.Width = 0.6102362!
        '
        'Label15
        '
        Me.Label15.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.Label15.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.Label15.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.Label15.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.Label15.Height = 0.1633858!
        Me.Label15.HyperLink = Nothing
        Me.Label15.Left = 5.295276!
        Me.Label15.Name = "Label15"
        Me.Label15.Style = "font-family: ＭＳ 明朝; font-size: 11.25pt; text-align: center; vertical-align: middl" &
    "e"
        Me.Label15.Text = "日数"
        Me.Label15.Top = 2.466536!
        Me.Label15.Width = 0.4724408!
        '
        'Label16
        '
        Me.Label16.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.Label16.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.Label16.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.Label16.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.Label16.Height = 0.3267717!
        Me.Label16.HyperLink = Nothing
        Me.Label16.Left = 5.767717!
        Me.Label16.Name = "Label16"
        Me.Label16.Style = "font-family: ＭＳ 明朝; font-size: 11.25pt; text-align: center; vertical-align: middl" &
    "e"
        Me.Label16.Text = "数量"
        Me.Label16.Top = 2.30315!
        Me.Label16.Width = 0.5511811!
        '
        'Label17
        '
        Me.Label17.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.Label17.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.Label17.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.Label17.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.Label17.Height = 0.3267717!
        Me.Label17.HyperLink = Nothing
        Me.Label17.Left = 6.318898!
        Me.Label17.Name = "Label17"
        Me.Label17.Style = "font-family: ＭＳ 明朝; font-size: 11.25pt; text-align: center; vertical-align: middl" &
    "e"
        Me.Label17.Text = "単位"
        Me.Label17.Top = 2.30315!
        Me.Label17.Width = 0.4527561!
        '
        'hid請求日付
        '
        Me.hid請求日付.CanGrow = False
        Me.hid請求日付.Height = 0.1181102!
        Me.hid請求日付.Left = 8.110237!
        Me.hid請求日付.Name = "hid請求日付"
        Me.hid請求日付.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 1, 0)
        Me.hid請求日付.ShrinkToFit = True
        Me.hid請求日付.Style = "background-color: Red; font-family: ＭＳ 明朝; font-size: 9.75pt; text-align: right; " &
    "vertical-align: middle; ddo-shrink-to-fit: true"
        Me.hid請求日付.Text = Nothing
        Me.hid請求日付.Top = 0.1574804!
        Me.hid請求日付.Visible = False
        Me.hid請求日付.Width = 0.1181102!
        '
        'txtNo
        '
        Me.txtNo.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtNo.CanGrow = False
        Me.txtNo.Height = 0.2755905!
        Me.txtNo.Left = 9.232285!
        Me.txtNo.Name = "txtNo"
        Me.txtNo.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.txtNo.Style = "font-family: ＭＳ 明朝; font-size: 11pt; font-weight: normal; text-align: left; text-" &
    "justify: auto; vertical-align: middle; ddo-char-set: 1"
        Me.txtNo.Text = "No."
        Me.txtNo.Top = 0.0000001192093!
        Me.txtNo.Width = 0.2755905!
        '
        'txtグループNO
        '
        Me.txtグループNO.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtグループNO.CanGrow = False
        Me.txtグループNO.Height = 0.2755905!
        Me.txtグループNO.Left = 9.507874!
        Me.txtグループNO.Name = "txtグループNO"
        Me.txtグループNO.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.txtグループNO.Style = "font-family: ＭＳ 明朝; font-size: 11pt; font-weight: normal; text-align: center; tex" &
    "t-justify: auto; vertical-align: middle; ddo-char-set: 1"
        Me.txtグループNO.Text = "99"
        Me.txtグループNO.Top = 0!
        Me.txtグループNO.Width = 0.2755906!
        '
        'hid工事内部コード
        '
        Me.hid工事内部コード.CanGrow = False
        Me.hid工事内部コード.Height = 0.1181102!
        Me.hid工事内部コード.Left = 3.346457!
        Me.hid工事内部コード.Name = "hid工事内部コード"
        Me.hid工事内部コード.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 1, 0)
        Me.hid工事内部コード.ShrinkToFit = True
        Me.hid工事内部コード.Style = "background-color: Red; font-family: ＭＳ 明朝; font-size: 9.75pt; text-align: right; " &
    "vertical-align: middle; ddo-shrink-to-fit: true"
        Me.hid工事内部コード.Text = Nothing
        Me.hid工事内部コード.Top = 0.7480316!
        Me.hid工事内部コード.Visible = False
        Me.hid工事内部コード.Width = 0.1181102!
        '
        'txt控
        '
        Me.txt控.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt控.CanGrow = False
        Me.txt控.CharacterSpacing = 3.0!
        Me.txt控.Height = 0.2755905!
        Me.txt控.Left = 5.708662!
        Me.txt控.Name = "txt控"
        Me.txt控.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.txt控.Style = "font-family: ＭＳ 明朝; font-size: 14.25pt; font-weight: normal; text-align: center; " &
    "text-justify: auto; vertical-align: middle"
        Me.txt控.Text = "(控)"
        Me.txt控.Top = 0!
        Me.txt控.Visible = False
        Me.txt控.Width = 0.7086614!
        '
        'GFT工事
        '
        Me.GFT工事.Controls.AddRange(New GrapeCity.ActiveReports.SectionReportModel.ARControl() {Me.TextBox12, Me.TextBox13, Me.TextBox14, Me.TextBox15, Me.TextBox16, Me.TextBox17, Me.TextBox18, Me.TextBox19, Me.TextBox20, Me.TextBox21, Me.txt合計金額})
        Me.GFT工事.Height = 0.1653543!
        Me.GFT工事.Name = "GFT工事"
        '
        'TextBox12
        '
        Me.TextBox12.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox12.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox12.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox12.CanGrow = False
        Me.TextBox12.Height = 0.1653543!
        Me.TextBox12.Left = 0.5511813!
        Me.TextBox12.Name = "TextBox12"
        Me.TextBox12.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 0, 0, 0)
        Me.TextBox12.Style = "font-family: ＭＳ 明朝; font-size: 11pt; font-weight: normal; text-align: center; tex" &
    "t-justify: auto; vertical-align: middle; ddo-char-set: 1; ddo-shrink-to-fit: non" &
    "e"
        Me.TextBox12.Text = "合　　計"
        Me.TextBox12.Top = 0!
        Me.TextBox12.Width = 2.913386!
        '
        'TextBox13
        '
        Me.TextBox13.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox13.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox13.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox13.CanGrow = False
        Me.TextBox13.Height = 0.1653543!
        Me.TextBox13.Left = 0!
        Me.TextBox13.Name = "TextBox13"
        Me.TextBox13.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.TextBox13.Style = "font-family: ＭＳ 明朝; font-size: 11pt; font-weight: normal; text-align: center; tex" &
    "t-justify: auto; vertical-align: middle; ddo-char-set: 1"
        Me.TextBox13.Text = Nothing
        Me.TextBox13.Top = 0!
        Me.TextBox13.Width = 0.5511811!
        '
        'TextBox14
        '
        Me.TextBox14.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox14.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox14.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox14.CanGrow = False
        Me.TextBox14.Height = 0.1653543!
        Me.TextBox14.Left = 3.464567!
        Me.TextBox14.Name = "TextBox14"
        Me.TextBox14.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.TextBox14.Style = "font-family: ＭＳ 明朝; font-size: 11pt; font-weight: normal; text-align: center; tex" &
    "t-justify: auto; vertical-align: middle; ddo-char-set: 1"
        Me.TextBox14.Text = Nothing
        Me.TextBox14.Top = 0!
        Me.TextBox14.Width = 0.6102362!
        '
        'TextBox15
        '
        Me.TextBox15.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox15.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox15.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox15.CanGrow = False
        Me.TextBox15.Height = 0.1653543!
        Me.TextBox15.Left = 4.074803!
        Me.TextBox15.Name = "TextBox15"
        Me.TextBox15.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.TextBox15.Style = "font-family: ＭＳ 明朝; font-size: 11pt; font-weight: normal; text-align: center; tex" &
    "t-justify: auto; vertical-align: middle; ddo-char-set: 1"
        Me.TextBox15.Text = Nothing
        Me.TextBox15.Top = 0!
        Me.TextBox15.Width = 0.6102362!
        '
        'TextBox16
        '
        Me.TextBox16.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox16.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox16.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox16.CanGrow = False
        Me.TextBox16.Height = 0.1653543!
        Me.TextBox16.Left = 4.68504!
        Me.TextBox16.Name = "TextBox16"
        Me.TextBox16.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.TextBox16.Style = "font-family: ＭＳ 明朝; font-size: 11pt; font-weight: normal; text-align: center; tex" &
    "t-justify: auto; vertical-align: middle; ddo-char-set: 1"
        Me.TextBox16.Text = Nothing
        Me.TextBox16.Top = 0!
        Me.TextBox16.Width = 0.6102362!
        '
        'TextBox17
        '
        Me.TextBox17.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox17.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox17.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox17.CanGrow = False
        Me.TextBox17.Height = 0.1653543!
        Me.TextBox17.Left = 5.295277!
        Me.TextBox17.Name = "TextBox17"
        Me.TextBox17.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 8, 0)
        Me.TextBox17.ShrinkToFit = True
        Me.TextBox17.Style = "font-family: ＭＳ 明朝; font-size: 11.25pt; text-align: right; vertical-align: middle" &
    "; ddo-shrink-to-fit: true"
        Me.TextBox17.Text = Nothing
        Me.TextBox17.Top = 0!
        Me.TextBox17.Width = 0.472441!
        '
        'TextBox18
        '
        Me.TextBox18.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox18.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox18.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox18.CanGrow = False
        Me.TextBox18.Height = 0.1653543!
        Me.TextBox18.Left = 5.767717!
        Me.TextBox18.Name = "TextBox18"
        Me.TextBox18.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 8, 0)
        Me.TextBox18.ShrinkToFit = True
        Me.TextBox18.Style = "font-family: ＭＳ 明朝; font-size: 11.25pt; text-align: right; vertical-align: middle" &
    "; ddo-shrink-to-fit: true"
        Me.TextBox18.Text = Nothing
        Me.TextBox18.Top = 0!
        Me.TextBox18.Width = 0.5511811!
        '
        'TextBox19
        '
        Me.TextBox19.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox19.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox19.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox19.CanGrow = False
        Me.TextBox19.Height = 0.1653543!
        Me.TextBox19.Left = 6.318899!
        Me.TextBox19.Name = "TextBox19"
        Me.TextBox19.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 0, 0, 0)
        Me.TextBox19.Style = "font-family: ＭＳ 明朝; font-size: 11pt; font-weight: normal; text-align: left; text-" &
    "justify: auto; vertical-align: middle; ddo-char-set: 1"
        Me.TextBox19.Text = Nothing
        Me.TextBox19.Top = 0!
        Me.TextBox19.Width = 0.4527559!
        '
        'TextBox20
        '
        Me.TextBox20.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox20.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox20.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox20.CanGrow = False
        Me.TextBox20.Height = 0.1653543!
        Me.TextBox20.Left = 6.771654!
        Me.TextBox20.Name = "TextBox20"
        Me.TextBox20.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 8, 0)
        Me.TextBox20.ShrinkToFit = True
        Me.TextBox20.Style = "font-family: ＭＳ 明朝; font-size: 11.25pt; text-align: right; vertical-align: middle" &
    "; ddo-shrink-to-fit: true"
        Me.TextBox20.Text = Nothing
        Me.TextBox20.Top = 0!
        Me.TextBox20.Width = 1.003937!
        '
        'TextBox21
        '
        Me.TextBox21.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox21.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox21.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.TextBox21.CanGrow = False
        Me.TextBox21.Height = 0.1653543!
        Me.TextBox21.Left = 7.77559!
        Me.TextBox21.Name = "TextBox21"
        Me.TextBox21.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 8, 0)
        Me.TextBox21.ShrinkToFit = True
        Me.TextBox21.Style = "font-family: ＭＳ 明朝; font-size: 11.25pt; text-align: right; vertical-align: middle" &
    "; ddo-shrink-to-fit: true"
        Me.TextBox21.Text = Nothing
        Me.TextBox21.Top = 0!
        Me.TextBox21.Width = 1.003937!
        '
        'txt合計金額
        '
        Me.txt合計金額.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt合計金額.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt合計金額.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt合計金額.CanGrow = False
        Me.txt合計金額.Height = 0.1653543!
        Me.txt合計金額.Left = 8.779528!
        Me.txt合計金額.Name = "txt合計金額"
        Me.txt合計金額.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 8, 0)
        Me.txt合計金額.Style = "font-family: ＭＳ 明朝; font-size: 11.25pt; text-align: right; vertical-align: middle" &
    "; ddo-shrink-to-fit: none"
        Me.txt合計金額.Text = "9,999"
        Me.txt合計金額.Top = 0!
        Me.txt合計金額.Width = 1.003937!
        '
        'SeikyushoMeisai
        '
        Me.MasterReport = False
        Me.PageSettings.DefaultPaperSize = False
        Me.PageSettings.Margins.Bottom = 0.2755905!
        Me.PageSettings.Margins.Left = 0.2755905!
        Me.PageSettings.Margins.Right = 0.2755905!
        Me.PageSettings.Margins.Top = 0.511811!
        Me.PageSettings.Orientation = GrapeCity.ActiveReports.Document.Section.PageOrientation.Landscape
        Me.PageSettings.PaperHeight = 11.69291!
        Me.PageSettings.PaperKind = System.Drawing.Printing.PaperKind.A4
        Me.PageSettings.PaperWidth = 8.267716!
        Me.PrintWidth = 9.84252!
        Me.Sections.Add(Me.PageHeader)
        Me.Sections.Add(Me.GHD工事)
        Me.Sections.Add(Me.Detail)
        Me.Sections.Add(Me.GFT工事)
        Me.Sections.Add(Me.PageFooter)
        Me.StyleSheet.Add(New DDCssLib.StyleSheetRule("font-style: normal; text-decoration: none; font-weight: normal; font-size: 10pt; " &
            "color: Black; font-family: ""MS UI Gothic""; ddo-char-set: 128", "Normal"))
        Me.StyleSheet.Add(New DDCssLib.StyleSheetRule("font-size: 16pt; font-weight: bold; font-family: ""MS UI Gothic""; ddo-char-set: 12" &
            "8", "Heading1", "Normal"))
        Me.StyleSheet.Add(New DDCssLib.StyleSheetRule("font-size: 14pt; font-weight: bold; font-style: inherit; font-family: ""MS UI Goth" &
            "ic""; ddo-char-set: 128", "Heading2", "Normal"))
        Me.StyleSheet.Add(New DDCssLib.StyleSheetRule("font-size: 13pt; font-weight: bold; ddo-char-set: 128", "Heading3", "Normal"))
        CType(Me.txt商品名, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt搬入日付, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt開始日付, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt終了日付, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt返納日付, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt日数, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt数量, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt単位区分名, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt単価, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt基本料金, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt金額, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.hid搬入日付, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.hid開始日付, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.hid終了日付, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.hid返納日付, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lbl請求額, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt請求額, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lbl前回額, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt前回額, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lbl入金額, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lbl調整額, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lbl繰越額, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lbl売上額, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt入金額, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt調整額, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt繰越額, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt税抜額, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lbl工事CD, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lbl工事請求額, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtタイトル, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt得意先名, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt請求日付, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt得意先コード, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lbl得意先コード, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt登録番号, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lbl登録番号, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lbl振込先, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt振込先, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt税率, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lblmsg, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lbl消費税, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt消費税, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt工事名, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt工事コード, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lbl工事コード, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Label8, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Label9, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Label10, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Label11, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Label12, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Label13, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Label14, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Label15, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Label16, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Label17, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.hid請求日付, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtNo, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtグループNO, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.hid工事内部コード, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt控, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextBox12, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextBox13, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextBox14, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextBox15, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextBox16, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextBox17, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextBox18, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextBox19, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextBox20, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextBox21, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt合計金額, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me, System.ComponentModel.ISupportInitialize).EndInit()

    End Sub

    Private WithEvents GHD工事 As GrapeCity.ActiveReports.SectionReportModel.GroupHeader
    Private WithEvents GFT工事 As GrapeCity.ActiveReports.SectionReportModel.GroupFooter
    Private WithEvents lbl請求額 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents txt請求額 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents lbl前回額 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents txt前回額 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents lbl入金額 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents lbl調整額 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents lbl繰越額 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents lbl売上額 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents txt入金額 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt調整額 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt繰越額 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt税抜額 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents lbl工事CD As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents lbl工事請求額 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents txt得意先名 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt請求日付 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt得意先コード As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents lbl得意先コード As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents txt登録番号 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents lbl登録番号 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents lbl振込先 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents txt振込先 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt税率 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents lblmsg As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents lbl消費税 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents txt消費税 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt工事名 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt工事コード As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents lbl工事コード As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents Label8 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents Label9 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents Label10 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents Label11 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents Label12 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents Label13 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents Label14 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents Label15 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents Label16 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents Label17 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents txt商品名 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt搬入日付 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt開始日付 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt終了日付 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt返納日付 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt日数 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt数量 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt単位区分名 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt単価 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt基本料金 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt金額 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents TextBox12 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents TextBox13 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents TextBox14 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents TextBox15 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents TextBox16 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents TextBox17 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents TextBox18 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents TextBox19 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents TextBox20 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents TextBox21 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt合計金額 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents hid請求日付 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txtNo As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txtグループNO As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents hid搬入日付 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents hid開始日付 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents hid終了日付 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents hid返納日付 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents hid工事内部コード As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Public WithEvents txtタイトル As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Public WithEvents txt控 As GrapeCity.ActiveReports.SectionReportModel.TextBox
End Class
