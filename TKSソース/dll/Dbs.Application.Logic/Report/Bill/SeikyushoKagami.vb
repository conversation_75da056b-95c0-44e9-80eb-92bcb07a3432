﻿Imports GrapeCity.ActiveReports
Imports GrapeCity.ActiveReports.Document
Imports Dbs.Application.Logic.Database
Imports Dbs.Asphalt.Core.Common

Public Class SeikyushoKagami
    Private int行NO As Integer = 0

    Private strKeyNew As String = ""
    Private strKeyOld As String = ""

    Private Sub UriageNohinsho_ReportStart(sender As Object, e As EventArgs) Handles MyBase.ReportStart

        ' ﾚﾎﾟｰﾄｻｲｽﾞ
        Me.PageSettings.PaperKind = System.Drawing.Printing.PaperKind.A4
        Me.PageSettings.Orientation = GrapeCity.ActiveReports.Document.Section.PageOrientation.Landscape
        Me.PageSettings.Margins.Top = GrapeCity.ActiveReports.SectionReport.CmToInch(0.7)
        Me.PageSettings.Margins.Bottom = GrapeCity.ActiveReports.SectionReport.CmToInch(0.7)
        Me.PageSettings.Margins.Left = GrapeCity.ActiveReports.SectionReport.CmToInch(2.5)
        Me.PageSettings.Margins.Right = GrapeCity.ActiveReports.SectionReport.CmToInch(0.7)

        ' ｸﾞﾙｰﾌﾟﾍｯﾀﾞの設定

        Dim db As New DbReport.R請求書鑑DataTable

        Me.GHD請求番号.DataField = db.請求番号Column.ColumnName
        Me.GHD請求番号.NewPage = SectionReportModel.NewPage.None
        Me.GHD請求番号.RepeatStyle = SectionReportModel.RepeatStyle.OnPageIncludeNoDetail  'OnPageIncludeNoDetail･･･改頁した新頁が合計行のみの印字でもﾍｯﾀﾞを印字
        Me.GHD請求番号.KeepTogether = True     ' ﾍｯﾀﾞの途中で改頁されない様に

        ' ﾃﾞｰﾀﾌｨｰﾙﾄﾞの設定
        Me.txt郵便番号.DataField = db.郵便番号Column.ColumnName
        Me.txt繰越額.DataField = db.繰越額Column.ColumnName
        Me.txt工事CD.DataField = db.工事コードColumn.ColumnName
        Me.txt工事消費税.DataField = db.工事消費税Column.ColumnName
        Me.txt工事税込額.DataField = db.工事税込額Column.ColumnName
        Me.txt工事税抜額.DataField = db.工事税抜額Column.ColumnName
        Me.txt工事名.DataField = db.工事名Column.ColumnName
        Me.txt住所1.DataField = db.住所1Column.ColumnName
        Me.txt住所2.DataField = db.住所2Column.ColumnName
        Me.txt振込先.DataField = db.振込先1Column.ColumnName
        Me.txt請求額.DataField = db.請求額Column.ColumnName
        Me.txt前回額.DataField = db.前回額Column.ColumnName
        Me.txt調整額.DataField = db.調整額Column.ColumnName
        Me.txt登録番号.DataField = db.登録番号Column.ColumnName
        Me.txt得意先コード.DataField = db.得意先コードColumn.ColumnName
        Me.txt得意先名.DataField = db.得意先名Column.ColumnName
        Me.txt入金額.DataField = db.入金額Column.ColumnName
        Me.txt売上額.DataField = db.税込額Column.ColumnName
        Me.txtグループNO.DataField = db.グループNOColumn.ColumnName

        Me.hid請求日付.DataField = db.請求日付至Column.ColumnName

        ' 書式
        Me.txt工事消費税.OutputFormat = "#,##0"
        Me.txt工事税抜額.OutputFormat = "#,##0"
        Me.txt工事税込額.OutputFormat = "#,##0"
        Me.txt繰越額.OutputFormat = "#,##0"
        Me.txt請求額.OutputFormat = "#,##0"
        Me.txt前回額.OutputFormat = "#,##0"
        Me.txt調整額.OutputFormat = "#,##0"
        Me.txt入金額.OutputFormat = "#,##0"
        Me.txt売上額.OutputFormat = "#,##0"

        '' 表示/非表示
        'Me.txt行NO.Visible = False    ' ﾃｽﾄ用なので最後は非表示へ
    End Sub

    Private Sub GHD伝票番号_Format(sender As Object, e As EventArgs) Handles GHD請求番号.Format
        Me.txt請求日付.Text = Format(Text.CDateEx(Me.hid請求日付.Text), "yyyy年MM月dd日")

        strKeyNew = Me.txt得意先コード.Text

        If strKeyNew = strKeyOld Then
            Me.txt繰越額.Text = "***,***,***"
            Me.txt請求額.Text = "***,***,***"
            Me.txt前回額.Text = "***,***,***"
            Me.txt調整額.Text = "***,***,***"
            Me.txt入金額.Text = "***,***,***"
            Me.txt売上額.Text = "***,***,***"
        End If

        strKeyOld = strKeyNew
    End Sub

    Private Sub Detail_Format(sender As Object, e As EventArgs) Handles Detail.Format

        'int行NO += 1
        'Me.txt行NO.Text = int行NO

    End Sub
End Class
