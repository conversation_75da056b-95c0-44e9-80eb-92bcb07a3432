﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Public Class SeikyushoKagami
    Inherits GrapeCity.ActiveReports.SectionReport

    'フォームがコンポーネントの一覧をクリーンアップするために dispose をオーバーライドします。
    Protected Overloads Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing Then
        End If
        MyBase.Dispose(disposing)
    End Sub

    'メモ: 以下のプロシージャは ActiveReports デザイナーで必要です。
    'ActiveReports デザイナーを使用して変更できます。  
    'コード エディターを使って変更しないでください。
    Private WithEvents PageHeader As GrapeCity.ActiveReports.SectionReportModel.PageHeader
    Private WithEvents Detail As GrapeCity.ActiveReports.SectionReportModel.Detail
    Private WithEvents PageFooter As GrapeCity.ActiveReports.SectionReportModel.PageFooter
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Dim resources As System.Resources.ResourceManager = New System.Resources.ResourceManager(GetType(SeikyushoKagami))
        Me.PageHeader = New GrapeCity.ActiveReports.SectionReportModel.PageHeader()
        Me.Detail = New GrapeCity.ActiveReports.SectionReportModel.Detail()
        Me.txt工事税抜額 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt工事消費税 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt工事税込額 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt工事名 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt工事CD = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.PageFooter = New GrapeCity.ActiveReports.SectionReportModel.PageFooter()
        Me.GHD請求番号 = New GrapeCity.ActiveReports.SectionReportModel.GroupHeader()
        Me.lbl請求額 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.txt請求額 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.Label1 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.txt前回額 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.Label2 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.Label3 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.Label4 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.Label5 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.txt入金額 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt調整額 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt繰越額 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt売上額 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.lbl工事CD = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.lbl工事名 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.lbl工事売上額 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.lbl工事消費税 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.lbl工事請求額 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.txtタイトル = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt住所1 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt住所2 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt得意先名 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt郵便番号 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.Label6 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.txt請求日付 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt締切 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt得意先コード = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.lbl得意先コード = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.txt登録番号 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.lbl登録番号 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.lbl振込先 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.txt振込先 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt税率 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.lblmsg = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.txtNo = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txtグループNO = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.hid請求日付 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt控 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.GFT請求番号 = New GrapeCity.ActiveReports.SectionReportModel.GroupFooter()
        CType(Me.txt工事税抜額, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt工事消費税, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt工事税込額, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt工事名, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt工事CD, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lbl請求額, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt請求額, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Label1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt前回額, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Label2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Label3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Label4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Label5, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt入金額, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt調整額, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt繰越額, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt売上額, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lbl工事CD, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lbl工事名, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lbl工事売上額, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lbl工事消費税, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lbl工事請求額, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtタイトル, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt住所1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt住所2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt得意先名, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt郵便番号, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Label6, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt請求日付, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt締切, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt得意先コード, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lbl得意先コード, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt登録番号, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lbl登録番号, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lbl振込先, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt振込先, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt税率, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lblmsg, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtNo, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtグループNO, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.hid請求日付, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt控, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me, System.ComponentModel.ISupportInitialize).BeginInit()
        '
        'PageHeader
        '
        Me.PageHeader.Height = 0!
        Me.PageHeader.Name = "PageHeader"
        '
        'Detail
        '
        Me.Detail.Controls.AddRange(New GrapeCity.ActiveReports.SectionReportModel.ARControl() {Me.txt工事税抜額, Me.txt工事消費税, Me.txt工事税込額, Me.txt工事名, Me.txt工事CD})
        Me.Detail.Height = 0.3149606!
        Me.Detail.Name = "Detail"
        Me.Detail.RepeatToFill = True
        '
        'txt工事税抜額
        '
        Me.txt工事税抜額.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt工事税抜額.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt工事税抜額.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt工事税抜額.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt工事税抜額.CanGrow = False
        Me.txt工事税抜額.Height = 0.3149606!
        Me.txt工事税抜額.Left = 5.07874!
        Me.txt工事税抜額.Name = "txt工事税抜額"
        Me.txt工事税抜額.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 8, 0)
        Me.txt工事税抜額.ShrinkToFit = True
        Me.txt工事税抜額.Style = "font-family: ＭＳ 明朝; font-size: 11.25pt; text-align: right; vertical-align: middle" &
    "; ddo-shrink-to-fit: true"
        Me.txt工事税抜額.Text = "9,999"
        Me.txt工事税抜額.Top = 0!
        Me.txt工事税抜額.Width = 1.377953!
        '
        'txt工事消費税
        '
        Me.txt工事消費税.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt工事消費税.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt工事消費税.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt工事消費税.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt工事消費税.CanGrow = False
        Me.txt工事消費税.Height = 0.3149606!
        Me.txt工事消費税.Left = 6.456692!
        Me.txt工事消費税.Name = "txt工事消費税"
        Me.txt工事消費税.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 8, 0)
        Me.txt工事消費税.ShrinkToFit = True
        Me.txt工事消費税.Style = "font-family: ＭＳ 明朝; font-size: 11.25pt; text-align: right; vertical-align: middle" &
    "; ddo-shrink-to-fit: true"
        Me.txt工事消費税.Text = "9,999"
        Me.txt工事消費税.Top = 0!
        Me.txt工事消費税.Width = 1.377953!
        '
        'txt工事税込額
        '
        Me.txt工事税込額.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt工事税込額.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt工事税込額.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt工事税込額.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt工事税込額.CanGrow = False
        Me.txt工事税込額.Height = 0.3149606!
        Me.txt工事税込額.Left = 7.834644!
        Me.txt工事税込額.Name = "txt工事税込額"
        Me.txt工事税込額.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 8, 0)
        Me.txt工事税込額.ShrinkToFit = True
        Me.txt工事税込額.Style = "font-family: ＭＳ 明朝; font-size: 11.25pt; text-align: right; vertical-align: middle" &
    "; ddo-shrink-to-fit: true"
        Me.txt工事税込額.Text = "9,999"
        Me.txt工事税込額.Top = 0!
        Me.txt工事税込額.Width = 1.377953!
        '
        'txt工事名
        '
        Me.txt工事名.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt工事名.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt工事名.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt工事名.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt工事名.CanGrow = False
        Me.txt工事名.Height = 0.3149606!
        Me.txt工事名.Left = 0.8267717!
        Me.txt工事名.Name = "txt工事名"
        Me.txt工事名.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.txt工事名.Style = "font-family: ＭＳ 明朝; font-size: 11pt; font-weight: normal; text-align: left; text-" &
    "justify: auto; vertical-align: middle; ddo-char-set: 1"
        Me.txt工事名.Text = "立花建設 株式会社"
        Me.txt工事名.Top = 0!
        Me.txt工事名.Width = 4.251967!
        '
        'txt工事CD
        '
        Me.txt工事CD.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt工事CD.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt工事CD.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt工事CD.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt工事CD.CanGrow = False
        Me.txt工事CD.Height = 0.3149606!
        Me.txt工事CD.Left = 0!
        Me.txt工事CD.Name = "txt工事CD"
        Me.txt工事CD.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.txt工事CD.Style = "font-family: ＭＳ 明朝; font-size: 11pt; font-weight: normal; text-align: center; tex" &
    "t-justify: auto; vertical-align: middle; ddo-char-set: 1"
        Me.txt工事CD.Text = "012345"
        Me.txt工事CD.Top = 0!
        Me.txt工事CD.Width = 0.8267716!
        '
        'PageFooter
        '
        Me.PageFooter.Height = 0!
        Me.PageFooter.Name = "PageFooter"
        '
        'GHD請求番号
        '
        Me.GHD請求番号.Controls.AddRange(New GrapeCity.ActiveReports.SectionReportModel.ARControl() {Me.lbl請求額, Me.txt請求額, Me.Label1, Me.txt前回額, Me.Label2, Me.Label3, Me.Label4, Me.Label5, Me.txt入金額, Me.txt調整額, Me.txt繰越額, Me.txt売上額, Me.lbl工事CD, Me.lbl工事名, Me.lbl工事売上額, Me.lbl工事消費税, Me.lbl工事請求額, Me.txtタイトル, Me.txt住所1, Me.txt住所2, Me.txt得意先名, Me.txt郵便番号, Me.Label6, Me.txt請求日付, Me.txt締切, Me.txt得意先コード, Me.lbl得意先コード, Me.txt登録番号, Me.lbl登録番号, Me.lbl振込先, Me.txt振込先, Me.txt税率, Me.lblmsg, Me.txtNo, Me.txtグループNO, Me.hid請求日付, Me.txt控})
        Me.GHD請求番号.Height = 2.964567!
        Me.GHD請求番号.Name = "GHD請求番号"
        '
        'lbl請求額
        '
        Me.lbl請求額.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl請求額.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl請求額.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.ThickSolid
        Me.lbl請求額.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.ThickSolid
        Me.lbl請求額.Height = 0.3267716!
        Me.lbl請求額.HyperLink = Nothing
        Me.lbl請求額.Left = 7.598425!
        Me.lbl請求額.Name = "lbl請求額"
        Me.lbl請求額.Style = "font-family: ＭＳ 明朝; font-size: 11.25pt; text-align: center; vertical-align: middl" &
    "e"
        Me.lbl請求額.Text = "合計御請求額"
        Me.lbl請求額.Top = 1.870079!
        Me.lbl請求額.Width = 1.614173!
        '
        'txt請求額
        '
        Me.txt請求額.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.ThickSolid
        Me.txt請求額.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt請求額.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.ThickSolid
        Me.txt請求額.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt請求額.CanGrow = False
        Me.txt請求額.Height = 0.3267716!
        Me.txt請求額.Left = 7.598425!
        Me.txt請求額.Name = "txt請求額"
        Me.txt請求額.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 8, 0)
        Me.txt請求額.ShrinkToFit = True
        Me.txt請求額.Style = "font-family: ＭＳ 明朝; font-size: 11.25pt; text-align: right; vertical-align: middle" &
    "; ddo-shrink-to-fit: true"
        Me.txt請求額.Text = "9,999"
        Me.txt請求額.Top = 2.19685!
        Me.txt請求額.Width = 1.614173!
        '
        'Label1
        '
        Me.Label1.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.Label1.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.ThickSolid
        Me.Label1.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.Label1.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.ThickSolid
        Me.Label1.Height = 0.3267716!
        Me.Label1.HyperLink = Nothing
        Me.Label1.Left = 0.0000009536743!
        Me.Label1.Name = "Label1"
        Me.Label1.Style = "font-family: ＭＳ 明朝; font-size: 11.25pt; text-align: center; vertical-align: middl" &
    "e"
        Me.Label1.Text = "前回御請求額"
        Me.Label1.Top = 1.870079!
        Me.Label1.Width = 1.496063!
        '
        'txt前回額
        '
        Me.txt前回額.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.ThickSolid
        Me.txt前回額.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.ThickSolid
        Me.txt前回額.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt前回額.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt前回額.CanGrow = False
        Me.txt前回額.Height = 0.3267716!
        Me.txt前回額.Left = 0.0000009536743!
        Me.txt前回額.Name = "txt前回額"
        Me.txt前回額.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 8, 0)
        Me.txt前回額.ShrinkToFit = True
        Me.txt前回額.Style = "font-family: ＭＳ 明朝; font-size: 11.25pt; text-align: right; vertical-align: middle" &
    "; ddo-shrink-to-fit: true"
        Me.txt前回額.Text = "9,999"
        Me.txt前回額.Top = 2.196851!
        Me.txt前回額.Width = 1.496063!
        '
        'Label2
        '
        Me.Label2.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.Label2.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.Label2.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.Label2.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.ThickSolid
        Me.Label2.Height = 0.3267716!
        Me.Label2.HyperLink = Nothing
        Me.Label2.Left = 1.496063!
        Me.Label2.Name = "Label2"
        Me.Label2.Style = "font-family: ＭＳ 明朝; font-size: 11.25pt; text-align: center; vertical-align: middl" &
    "e"
        Me.Label2.Text = "当月御入金額"
        Me.Label2.Top = 1.87008!
        Me.Label2.Width = 1.496063!
        '
        'Label3
        '
        Me.Label3.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.Label3.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.Label3.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.Label3.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.ThickSolid
        Me.Label3.Height = 0.3267716!
        Me.Label3.HyperLink = Nothing
        Me.Label3.Left = 2.992126!
        Me.Label3.Name = "Label3"
        Me.Label3.Style = "font-family: ＭＳ 明朝; font-size: 11.25pt; text-align: center; vertical-align: middl" &
    "e"
        Me.Label3.Text = "当月調整額"
        Me.Label3.Top = 1.870079!
        Me.Label3.Width = 1.496063!
        '
        'Label4
        '
        Me.Label4.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.Label4.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.Label4.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.Label4.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.ThickSolid
        Me.Label4.Height = 0.3267716!
        Me.Label4.HyperLink = Nothing
        Me.Label4.Left = 4.488189!
        Me.Label4.Name = "Label4"
        Me.Label4.Style = "font-family: ＭＳ 明朝; font-size: 11.25pt; text-align: center; vertical-align: middl" &
    "e"
        Me.Label4.Text = "繰  越  額"
        Me.Label4.Top = 1.870079!
        Me.Label4.Width = 1.496063!
        '
        'Label5
        '
        Me.Label5.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.Label5.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.Label5.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.Label5.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.ThickSolid
        Me.Label5.Height = 0.3267716!
        Me.Label5.HyperLink = Nothing
        Me.Label5.Left = 5.984252!
        Me.Label5.Name = "Label5"
        Me.Label5.Style = "font-family: ＭＳ 明朝; font-size: 11.25pt; text-align: center; vertical-align: middl" &
    "e"
        Me.Label5.Text = "当月御請求額"
        Me.Label5.Top = 1.87008!
        Me.Label5.Width = 1.614173!
        '
        'txt入金額
        '
        Me.txt入金額.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.ThickSolid
        Me.txt入金額.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt入金額.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt入金額.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt入金額.CanGrow = False
        Me.txt入金額.Height = 0.3267716!
        Me.txt入金額.Left = 1.496063!
        Me.txt入金額.Name = "txt入金額"
        Me.txt入金額.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 8, 0)
        Me.txt入金額.ShrinkToFit = True
        Me.txt入金額.Style = "font-family: ＭＳ 明朝; font-size: 11.25pt; text-align: right; vertical-align: middle" &
    "; ddo-shrink-to-fit: true"
        Me.txt入金額.Text = "9,999"
        Me.txt入金額.Top = 2.196851!
        Me.txt入金額.Width = 1.496063!
        '
        'txt調整額
        '
        Me.txt調整額.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.ThickSolid
        Me.txt調整額.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt調整額.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt調整額.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt調整額.CanGrow = False
        Me.txt調整額.Height = 0.3267716!
        Me.txt調整額.Left = 2.992126!
        Me.txt調整額.Name = "txt調整額"
        Me.txt調整額.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 8, 0)
        Me.txt調整額.ShrinkToFit = True
        Me.txt調整額.Style = "font-family: ＭＳ 明朝; font-size: 11.25pt; text-align: right; vertical-align: middle" &
    "; ddo-shrink-to-fit: true"
        Me.txt調整額.Text = "9,999"
        Me.txt調整額.Top = 2.196851!
        Me.txt調整額.Width = 1.496063!
        '
        'txt繰越額
        '
        Me.txt繰越額.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.ThickSolid
        Me.txt繰越額.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt繰越額.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt繰越額.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt繰越額.CanGrow = False
        Me.txt繰越額.Height = 0.3267716!
        Me.txt繰越額.Left = 4.488189!
        Me.txt繰越額.Name = "txt繰越額"
        Me.txt繰越額.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 8, 0)
        Me.txt繰越額.ShrinkToFit = True
        Me.txt繰越額.Style = "font-family: ＭＳ 明朝; font-size: 11.25pt; text-align: right; vertical-align: middle" &
    "; ddo-shrink-to-fit: true"
        Me.txt繰越額.Text = "9,999"
        Me.txt繰越額.Top = 2.196851!
        Me.txt繰越額.Width = 1.496063!
        '
        'txt売上額
        '
        Me.txt売上額.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.ThickSolid
        Me.txt売上額.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt売上額.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt売上額.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt売上額.CanGrow = False
        Me.txt売上額.Height = 0.3267716!
        Me.txt売上額.Left = 5.984252!
        Me.txt売上額.Name = "txt売上額"
        Me.txt売上額.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 8, 0)
        Me.txt売上額.ShrinkToFit = True
        Me.txt売上額.Style = "font-family: ＭＳ 明朝; font-size: 11.25pt; text-align: right; vertical-align: middle" &
    "; ddo-shrink-to-fit: true"
        Me.txt売上額.Text = "9,999"
        Me.txt売上額.Top = 2.196851!
        Me.txt売上額.Width = 1.614173!
        '
        'lbl工事CD
        '
        Me.lbl工事CD.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl工事CD.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl工事CD.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl工事CD.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl工事CD.Height = 0.3267716!
        Me.lbl工事CD.HyperLink = Nothing
        Me.lbl工事CD.Left = 0.0000009536743!
        Me.lbl工事CD.Name = "lbl工事CD"
        Me.lbl工事CD.Style = "font-family: ＭＳ 明朝; font-size: 11.25pt; text-align: center; vertical-align: middl" &
    "e"
        Me.lbl工事CD.Text = "工事CD"
        Me.lbl工事CD.Top = 2.637795!
        Me.lbl工事CD.Width = 0.8267716!
        '
        'lbl工事名
        '
        Me.lbl工事名.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl工事名.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl工事名.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl工事名.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl工事名.Height = 0.3267716!
        Me.lbl工事名.HyperLink = Nothing
        Me.lbl工事名.Left = 0.8267717!
        Me.lbl工事名.Name = "lbl工事名"
        Me.lbl工事名.Style = "font-family: ＭＳ 明朝; font-size: 11.25pt; text-align: center; vertical-align: middl" &
    "e"
        Me.lbl工事名.Text = "工　　　事　　　名"
        Me.lbl工事名.Top = 2.637795!
        Me.lbl工事名.Width = 4.251968!
        '
        'lbl工事売上額
        '
        Me.lbl工事売上額.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl工事売上額.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl工事売上額.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl工事売上額.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl工事売上額.Height = 0.3267716!
        Me.lbl工事売上額.HyperLink = Nothing
        Me.lbl工事売上額.Left = 5.078741!
        Me.lbl工事売上額.Name = "lbl工事売上額"
        Me.lbl工事売上額.Style = "font-family: ＭＳ 明朝; font-size: 11.25pt; text-align: center; vertical-align: middl" &
    "e"
        Me.lbl工事売上額.Text = "当月売上高"
        Me.lbl工事売上額.Top = 2.637795!
        Me.lbl工事売上額.Width = 1.377953!
        '
        'lbl工事消費税
        '
        Me.lbl工事消費税.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl工事消費税.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl工事消費税.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl工事消費税.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl工事消費税.Height = 0.3267716!
        Me.lbl工事消費税.HyperLink = Nothing
        Me.lbl工事消費税.Left = 6.456693!
        Me.lbl工事消費税.Name = "lbl工事消費税"
        Me.lbl工事消費税.Style = "font-family: ＭＳ 明朝; font-size: 11.25pt; text-align: center; vertical-align: middl" &
    "e"
        Me.lbl工事消費税.Text = "消費税"
        Me.lbl工事消費税.Top = 2.637795!
        Me.lbl工事消費税.Width = 1.377953!
        '
        'lbl工事請求額
        '
        Me.lbl工事請求額.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl工事請求額.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl工事請求額.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl工事請求額.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl工事請求額.Height = 0.3267717!
        Me.lbl工事請求額.HyperLink = Nothing
        Me.lbl工事請求額.Left = 7.834646!
        Me.lbl工事請求額.Name = "lbl工事請求額"
        Me.lbl工事請求額.Style = "font-family: ＭＳ 明朝; font-size: 11.25pt; text-align: center; vertical-align: middl" &
    "e"
        Me.lbl工事請求額.Text = "当月御請求額"
        Me.lbl工事請求額.Top = 2.637795!
        Me.lbl工事請求額.Width = 1.377953!
        '
        'txtタイトル
        '
        Me.txtタイトル.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtタイトル.CanGrow = False
        Me.txtタイトル.CharacterSpacing = 18.0!
        Me.txtタイトル.Height = 0.2755905!
        Me.txtタイトル.Left = 3.464567!
        Me.txtタイトル.Name = "txtタイトル"
        Me.txtタイトル.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.txtタイトル.Style = "font-family: ＭＳ 明朝; font-size: 14.25pt; font-weight: normal; text-align: center; " &
    "text-justify: auto; vertical-align: middle"
        Me.txtタイトル.Text = "御請求書"
        Me.txtタイトル.Top = 0.0000007450581!
        Me.txtタイトル.Width = 1.968504!
        '
        'txt住所1
        '
        Me.txt住所1.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt住所1.CanGrow = False
        Me.txt住所1.Height = 0.2755905!
        Me.txt住所1.Left = 0.8267726!
        Me.txt住所1.Name = "txt住所1"
        Me.txt住所1.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.txt住所1.Style = "font-family: ＭＳ 明朝; font-size: 11pt; font-weight: normal; text-align: left; text-" &
    "justify: auto; vertical-align: middle; ddo-char-set: 1"
        Me.txt住所1.Text = "東京都練馬区高野台　2-27-25"
        Me.txt住所1.Top = 0.3346464!
        Me.txt住所1.Width = 2.755906!
        '
        'txt住所2
        '
        Me.txt住所2.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt住所2.CanGrow = False
        Me.txt住所2.Height = 0.2755905!
        Me.txt住所2.Left = 0.8267726!
        Me.txt住所2.Name = "txt住所2"
        Me.txt住所2.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.txt住所2.Style = "font-family: ＭＳ 明朝; font-size: 11pt; font-weight: normal; text-align: left; text-" &
    "justify: auto; vertical-align: middle; ddo-char-set: 1"
        Me.txt住所2.Text = "東京都練馬区高野台　2-27-25"
        Me.txt住所2.Top = 0.6496071!
        Me.txt住所2.Width = 2.755905!
        '
        'txt得意先名
        '
        Me.txt得意先名.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt得意先名.CanGrow = False
        Me.txt得意先名.Height = 0.2755905!
        Me.txt得意先名.Left = 0.8267726!
        Me.txt得意先名.Name = "txt得意先名"
        Me.txt得意先名.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.txt得意先名.Style = "font-family: ＭＳ 明朝; font-size: 11pt; font-weight: normal; text-align: left; text-" &
    "justify: auto; vertical-align: middle; ddo-char-set: 1"
        Me.txt得意先名.Text = "立花建設 株式会社"
        Me.txt得意先名.Top = 0.9645677!
        Me.txt得意先名.Width = 2.755905!
        '
        'txt郵便番号
        '
        Me.txt郵便番号.CanGrow = False
        Me.txt郵便番号.Height = 0.2755905!
        Me.txt郵便番号.Left = 0.1574813!
        Me.txt郵便番号.Name = "txt郵便番号"
        Me.txt郵便番号.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.txt郵便番号.Style = "font-family: ＭＳ 明朝; font-size: 11pt; font-weight: normal; text-align: left; text-" &
    "justify: auto; vertical-align: middle; ddo-char-set: 1"
        Me.txt郵便番号.Text = "012-3456"
        Me.txt郵便番号.Top = 0.3346465!
        Me.txt郵便番号.Width = 0.6692914!
        '
        'Label6
        '
        Me.Label6.Height = 0.2755905!
        Me.Label6.HyperLink = Nothing
        Me.Label6.Left = 0!
        Me.Label6.Name = "Label6"
        Me.Label6.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: right; vertical-align: middle; dd" &
    "o-char-set: 1"
        Me.Label6.Text = "〒"
        Me.Label6.Top = 0.3346465!
        Me.Label6.Width = 0.1574813!
        '
        'txt請求日付
        '
        Me.txt請求日付.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt請求日付.CanGrow = False
        Me.txt請求日付.Height = 0.2755905!
        Me.txt請求日付.Left = 6.574803!
        Me.txt請求日付.Name = "txt請求日付"
        Me.txt請求日付.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.txt請求日付.Style = "font-family: ＭＳ 明朝; font-size: 11pt; font-weight: normal; text-align: center; tex" &
    "t-justify: auto; vertical-align: middle; ddo-char-set: 1"
        Me.txt請求日付.Text = "yyyy年MM月dd日"
        Me.txt請求日付.Top = 0.3346457!
        Me.txt請求日付.Width = 1.181102!
        '
        'txt締切
        '
        Me.txt締切.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt締切.CanGrow = False
        Me.txt締切.Height = 0.2755905!
        Me.txt締切.Left = 7.755906!
        Me.txt締切.Name = "txt締切"
        Me.txt締切.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.txt締切.Style = "font-family: ＭＳ 明朝; font-size: 11pt; font-weight: normal; text-align: left; text-" &
    "justify: auto; vertical-align: middle; ddo-char-set: 1"
        Me.txt締切.Text = "締切分"
        Me.txt締切.Top = 0.3346457!
        Me.txt締切.Width = 0.511811!
        '
        'txt得意先コード
        '
        Me.txt得意先コード.CanGrow = False
        Me.txt得意先コード.Height = 0.1574803!
        Me.txt得意先コード.Left = 1.771655!
        Me.txt得意先コード.Name = "txt得意先コード"
        Me.txt得意先コード.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.txt得意先コード.Style = "font-family: ＭＳ 明朝; font-size: 9.75pt; font-weight: normal; text-align: left; tex" &
    "t-justify: auto; vertical-align: middle; ddo-char-set: 1"
        Me.txt得意先コード.Text = "012345"
        Me.txt得意先コード.Top = 1.318898!
        Me.txt得意先コード.Width = 0.7185037!
        '
        'lbl得意先コード
        '
        Me.lbl得意先コード.Height = 0.1574803!
        Me.lbl得意先コード.HyperLink = Nothing
        Me.lbl得意先コード.Left = 0.8267726!
        Me.lbl得意先コード.Name = "lbl得意先コード"
        Me.lbl得意先コード.Style = "font-family: ＭＳ 明朝; font-size: 9.75pt; text-align: left; vertical-align: middle; " &
    "ddo-char-set: 128"
        Me.lbl得意先コード.Text = "お客様コード"
        Me.lbl得意先コード.Top = 1.318898!
        Me.lbl得意先コード.Width = 0.9055118!
        '
        'txt登録番号
        '
        Me.txt登録番号.CanGrow = False
        Me.txt登録番号.Height = 0.1574803!
        Me.txt登録番号.Left = 5.701574!
        Me.txt登録番号.Name = "txt登録番号"
        Me.txt登録番号.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.txt登録番号.Style = "font-family: ＭＳ 明朝; font-size: 9.75pt; font-weight: normal; text-align: left; tex" &
    "t-justify: auto; vertical-align: middle; ddo-char-set: 1"
        Me.txt登録番号.Text = "T3-0118-0101-0290"
        Me.txt登録番号.Top = 1.402361!
        Me.txt登録番号.Width = 1.496063!
        '
        'lbl登録番号
        '
        Me.lbl登録番号.Height = 0.1574803!
        Me.lbl登録番号.HyperLink = Nothing
        Me.lbl登録番号.Left = 5.032283!
        Me.lbl登録番号.Name = "lbl登録番号"
        Me.lbl登録番号.Style = "font-family: ＭＳ 明朝; font-size: 9.75pt; text-align: left; vertical-align: middle; " &
    "ddo-char-set: 128"
        Me.lbl登録番号.Text = "登録番号"
        Me.lbl登録番号.Top = 1.402361!
        Me.lbl登録番号.Width = 0.6692914!
        '
        'lbl振込先
        '
        Me.lbl振込先.Height = 0.1574803!
        Me.lbl振込先.HyperLink = Nothing
        Me.lbl振込先.Left = 5.032283!
        Me.lbl振込先.Name = "lbl振込先"
        Me.lbl振込先.Style = "font-family: ＭＳ 明朝; font-size: 9.75pt; text-align: left; vertical-align: middle; " &
    "ddo-char-set: 128"
        Me.lbl振込先.Text = "お振込先"
        Me.lbl振込先.Top = 1.633858!
        Me.lbl振込先.Width = 0.6692914!
        '
        'txt振込先
        '
        Me.txt振込先.CanGrow = False
        Me.txt振込先.Height = 0.1574803!
        Me.txt振込先.Left = 5.701574!
        Me.txt振込先.Name = "txt振込先"
        Me.txt振込先.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.txt振込先.Style = "font-family: ＭＳ 明朝; font-size: 9.75pt; font-weight: normal; text-align: left; tex" &
    "t-justify: auto; vertical-align: middle; ddo-char-set: 1"
        Me.txt振込先.Text = "三菱UFJ銀行　竹ノ塚支店　当座 450726"
        Me.txt振込先.Top = 1.633858!
        Me.txt振込先.Width = 2.598425!
        '
        'txt税率
        '
        Me.txt税率.CanGrow = False
        Me.txt税率.Height = 0.1574803!
        Me.txt税率.Left = 8.3!
        Me.txt税率.Name = "txt税率"
        Me.txt税率.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.txt税率.Style = "font-family: ＭＳ 明朝; font-size: 9.75pt; font-weight: normal; text-align: center; t" &
    "ext-justify: auto; vertical-align: middle; ddo-char-set: 1"
        Me.txt税率.Text = "(税率10%)"
        Me.txt税率.Top = 1.633858!
        Me.txt税率.Width = 0.9125977!
        '
        'lblmsg
        '
        Me.lblmsg.Height = 0.1574803!
        Me.lblmsg.HyperLink = Nothing
        Me.lblmsg.Left = 0.07874107!
        Me.lblmsg.Name = "lblmsg"
        Me.lblmsg.Style = "font-family: ＭＳ 明朝; font-size: 9.75pt; text-align: left; vertical-align: middle; " &
    "ddo-char-set: 128"
        Me.lblmsg.Text = "下記のとおり御請求申し上げます。"
        Me.lblmsg.Top = 1.633859!
        Me.lblmsg.Width = 3.149606!
        '
        'txtNo
        '
        Me.txtNo.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtNo.CanGrow = False
        Me.txtNo.Height = 0.2755905!
        Me.txtNo.Left = 8.464567!
        Me.txtNo.Name = "txtNo"
        Me.txtNo.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.txtNo.Style = "font-family: ＭＳ 明朝; font-size: 11pt; font-weight: normal; text-align: left; text-" &
    "justify: auto; vertical-align: middle; ddo-char-set: 1"
        Me.txtNo.Text = "No."
        Me.txtNo.Top = 0.3346457!
        Me.txtNo.Width = 0.2755905!
        '
        'txtグループNO
        '
        Me.txtグループNO.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtグループNO.CanGrow = False
        Me.txtグループNO.Height = 0.2755905!
        Me.txtグループNO.Left = 8.740158!
        Me.txtグループNO.Name = "txtグループNO"
        Me.txtグループNO.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.txtグループNO.Style = "font-family: ＭＳ 明朝; font-size: 11pt; font-weight: normal; text-align: center; tex" &
    "t-justify: auto; vertical-align: middle; ddo-char-set: 1"
        Me.txtグループNO.Text = "99"
        Me.txtグループNO.Top = 0.3346456!
        Me.txtグループNO.Width = 0.2755906!
        '
        'hid請求日付
        '
        Me.hid請求日付.CanGrow = False
        Me.hid請求日付.Height = 0.1181102!
        Me.hid請求日付.Left = 7.16063!
        Me.hid請求日付.Name = "hid請求日付"
        Me.hid請求日付.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 1, 0)
        Me.hid請求日付.ShrinkToFit = True
        Me.hid請求日付.Style = "background-color: Red; font-family: ＭＳ 明朝; font-size: 9.75pt; text-align: right; " &
    "vertical-align: middle; ddo-shrink-to-fit: true"
        Me.hid請求日付.Text = Nothing
        Me.hid請求日付.Top = 0.246063!
        Me.hid請求日付.Visible = False
        Me.hid請求日付.Width = 0.1181102!
        '
        'txt控
        '
        Me.txt控.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt控.CanGrow = False
        Me.txt控.CharacterSpacing = 3.0!
        Me.txt控.Height = 0.2755906!
        Me.txt控.Left = 5.275591!
        Me.txt控.Name = "txt控"
        Me.txt控.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.txt控.Style = "font-family: ＭＳ 明朝; font-size: 14.25pt; font-weight: normal; text-align: center; " &
    "text-justify: auto; vertical-align: middle"
        Me.txt控.Text = "(控)"
        Me.txt控.Top = 0!
        Me.txt控.Visible = False
        Me.txt控.Width = 0.7086611!
        '
        'GFT請求番号
        '
        Me.GFT請求番号.Height = 0!
        Me.GFT請求番号.Name = "GFT請求番号"
        '
        'SeikyushoKagami
        '
        Me.MasterReport = False
        Me.PageSettings.DefaultPaperSize = False
        Me.PageSettings.Margins.Bottom = 0.2755905!
        Me.PageSettings.Margins.Left = 0.2755905!
        Me.PageSettings.Margins.Right = 0.2755905!
        Me.PageSettings.Margins.Top = 0.511811!
        Me.PageSettings.Orientation = GrapeCity.ActiveReports.Document.Section.PageOrientation.Landscape
        Me.PageSettings.PaperHeight = 11.69291!
        Me.PageSettings.PaperKind = System.Drawing.Printing.PaperKind.A4
        Me.PageSettings.PaperWidth = 8.267716!
        Me.PrintWidth = 9.251968!
        Me.Sections.Add(Me.PageHeader)
        Me.Sections.Add(Me.GHD請求番号)
        Me.Sections.Add(Me.Detail)
        Me.Sections.Add(Me.GFT請求番号)
        Me.Sections.Add(Me.PageFooter)
        Me.StyleSheet.Add(New DDCssLib.StyleSheetRule("font-style: normal; text-decoration: none; font-weight: normal; font-size: 10pt; " &
            "color: Black; font-family: ""MS UI Gothic""; ddo-char-set: 128", "Normal"))
        Me.StyleSheet.Add(New DDCssLib.StyleSheetRule("font-size: 16pt; font-weight: bold; font-family: ""MS UI Gothic""; ddo-char-set: 12" &
            "8", "Heading1", "Normal"))
        Me.StyleSheet.Add(New DDCssLib.StyleSheetRule("font-size: 14pt; font-weight: bold; font-style: inherit; font-family: ""MS UI Goth" &
            "ic""; ddo-char-set: 128", "Heading2", "Normal"))
        Me.StyleSheet.Add(New DDCssLib.StyleSheetRule("font-size: 13pt; font-weight: bold; ddo-char-set: 128", "Heading3", "Normal"))
        CType(Me.txt工事税抜額, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt工事消費税, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt工事税込額, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt工事名, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt工事CD, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lbl請求額, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt請求額, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Label1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt前回額, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Label2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Label3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Label4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Label5, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt入金額, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt調整額, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt繰越額, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt売上額, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lbl工事CD, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lbl工事名, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lbl工事売上額, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lbl工事消費税, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lbl工事請求額, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtタイトル, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt住所1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt住所2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt得意先名, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt郵便番号, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Label6, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt請求日付, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt締切, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt得意先コード, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lbl得意先コード, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt登録番号, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lbl登録番号, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lbl振込先, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt振込先, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt税率, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lblmsg, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtNo, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtグループNO, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.hid請求日付, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt控, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me, System.ComponentModel.ISupportInitialize).EndInit()

    End Sub

    Private WithEvents GHD請求番号 As GrapeCity.ActiveReports.SectionReportModel.GroupHeader
    Private WithEvents GFT請求番号 As GrapeCity.ActiveReports.SectionReportModel.GroupFooter
    Private WithEvents lbl請求額 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents txt請求額 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents Label1 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents txt前回額 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents Label2 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents Label3 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents Label4 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents Label5 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents txt入金額 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt調整額 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt繰越額 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt売上額 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents lbl工事CD As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents lbl工事名 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents lbl工事売上額 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents lbl工事消費税 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents lbl工事請求額 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents txt住所1 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt住所2 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt得意先名 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt郵便番号 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents Label6 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents txt請求日付 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt締切 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt得意先コード As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents lbl得意先コード As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents txt登録番号 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents lbl登録番号 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents lbl振込先 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents txt振込先 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt税率 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents lblmsg As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents txt工事税抜額 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt工事消費税 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt工事税込額 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt工事名 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt工事CD As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txtNo As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txtグループNO As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents hid請求日付 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Public WithEvents txtタイトル As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Public WithEvents txt控 As GrapeCity.ActiveReports.SectionReportModel.TextBox
End Class
