﻿Imports GrapeCity.ActiveReports
Imports GrapeCity.ActiveReports.Document
Imports Dbs.Application.Logic.Database

Public Class SeikyuList
    Private int行NO As Integer = 0

    Private Sub SeikyuList_ReportStart(sender As Object, e As EventArgs) Handles Me.ReportStart

        ' ﾚﾎﾟｰﾄｻｲｽﾞ
        Me.PageSettings.PaperKind = System.Drawing.Printing.PaperKind.A4
        Me.PageSettings.Orientation = GrapeCity.ActiveReports.Document.Section.PageOrientation.Landscape
        Me.PageSettings.Margins.Top = GrapeCity.ActiveReports.SectionReport.CmToInch(0.7)
        Me.PageSettings.Margins.Bottom = GrapeCity.ActiveReports.SectionReport.CmToInch(0.7)
        Me.PageSettings.Margins.Left = GrapeCity.ActiveReports.SectionReport.CmToInch(1.3)
        Me.PageSettings.Margins.Right = GrapeCity.ActiveReports.SectionReport.CmToInch(0.7)

        ' ｸﾞﾙｰﾌﾟﾍｯﾀﾞの設定
        Dim db As New DbReport.R請求一覧表DataTable

        Me.GroupHeader1.DataField = db.請求番号Column.ColumnName
        Me.GroupHeader1.NewPage = SectionReportModel.NewPage.None
        Me.GroupHeader1.RepeatStyle = SectionReportModel.RepeatStyle.OnPageIncludeNoDetail  'OnPageIncludeNoDetail･･･改頁した新頁が合計行のみの印字でもﾍｯﾀﾞを印字
        Me.GroupHeader1.KeepTogether = True     ' ﾍｯﾀﾞの途中で改頁されない様に

        Me.GroupHeader2.DataField = Nothing
        Me.GroupHeader2.NewPage = SectionReportModel.NewPage.None
        Me.GroupHeader2.RepeatStyle = SectionReportModel.RepeatStyle.All
        Me.GroupHeader2.KeepTogether = True     ' ﾍｯﾀﾞの途中で改頁されない様に

        ' ﾃﾞｰﾀﾌｨｰﾙﾄﾞの設定
        Me.txt繰越額.DataField = db.繰越額Column.ColumnName
        Me.txt工事コード.DataField = db.工事コードColumn.ColumnName
        Me.txt工事名.DataField = db.工事名Column.ColumnName
        Me.txt消費税.DataField = db.消費税Column.ColumnName
        Me.txt請求額.DataField = db.請求額Column.ColumnName
        Me.txt前回額.DataField = db.前回額Column.ColumnName
        Me.txt調整額.DataField = db.調整額Column.ColumnName
        Me.txt得意先コード.DataField = db.得意先コードColumn.ColumnName
        Me.txt得意先名.DataField = db.得意先名Column.ColumnName
        Me.txt入金額.DataField = db.入金額Column.ColumnName
        Me.txt売上額.DataField = db.税抜額Column.ColumnName

        ' 書式
        Me.txt繰越額.OutputFormat = "#,##0"
        Me.txt繰越額合計.OutputFormat = "#,##0"
        Me.txt繰越額小計.OutputFormat = "#,##0"
        Me.txt消費税.OutputFormat = "#,##0"
        Me.txt消費税合計.OutputFormat = "#,##0"
        Me.txt消費税小計.OutputFormat = "#,##0"
        Me.txt請求額.OutputFormat = "#,##0"
        Me.txt請求額合計.OutputFormat = "#,##0"
        Me.txt請求額小計.OutputFormat = "#,##0"
        Me.txt前回額.OutputFormat = "#,##0"
        Me.txt前回額合計.OutputFormat = "#,##0"
        Me.txt前回額小計.OutputFormat = "#,##0"
        Me.txt調整額.OutputFormat = "#,##0"
        Me.txt調整額合計.OutputFormat = "#,##0"
        Me.txt調整額小計.OutputFormat = "#,##0"
        Me.txt入金額.OutputFormat = "#,##0"
        Me.txt入金額合計.OutputFormat = "#,##0"
        Me.txt入金額小計.OutputFormat = "#,##0"
        Me.txt売上額.OutputFormat = "#,##0"
        Me.txt売上額合計.OutputFormat = "#,##0"
        Me.txt売上額小計.OutputFormat = "#,##0"

        ' ﾌｯﾀﾞ小計
        Me.txt請求額小計.SummaryFunc = SectionReportModel.SummaryFunc.Sum
        Me.txt請求額小計.SummaryGroup = Me.GroupHeader1.Name
        Me.txt請求額小計.SummaryRunning = SectionReportModel.SummaryRunning.Group
        Me.txt請求額小計.SummaryType = SectionReportModel.SummaryType.SubTotal
        Me.txt請求額小計.DataField = db.請求額Column.ColumnName

        Me.txt繰越額小計.SummaryFunc = SectionReportModel.SummaryFunc.Sum
        Me.txt繰越額小計.SummaryGroup = Me.GroupHeader1.Name
        Me.txt繰越額小計.SummaryRunning = SectionReportModel.SummaryRunning.Group
        Me.txt繰越額小計.SummaryType = SectionReportModel.SummaryType.SubTotal
        Me.txt繰越額小計.DataField = db.繰越額Column.ColumnName

        Me.txt消費税小計.SummaryFunc = SectionReportModel.SummaryFunc.Sum
        Me.txt消費税小計.SummaryGroup = Me.GroupHeader1.Name
        Me.txt消費税小計.SummaryRunning = SectionReportModel.SummaryRunning.Group
        Me.txt消費税小計.SummaryType = SectionReportModel.SummaryType.SubTotal
        Me.txt消費税小計.DataField = db.消費税Column.ColumnName

        Me.txt前回額小計.SummaryFunc = SectionReportModel.SummaryFunc.Sum
        Me.txt前回額小計.SummaryGroup = Me.GroupHeader1.Name
        Me.txt前回額小計.SummaryRunning = SectionReportModel.SummaryRunning.Group
        Me.txt前回額小計.SummaryType = SectionReportModel.SummaryType.SubTotal
        Me.txt前回額小計.DataField = db.前回額Column.ColumnName

        Me.txt調整額小計.SummaryFunc = SectionReportModel.SummaryFunc.Sum
        Me.txt調整額小計.SummaryGroup = Me.GroupHeader1.Name
        Me.txt調整額小計.SummaryRunning = SectionReportModel.SummaryRunning.Group
        Me.txt調整額小計.SummaryType = SectionReportModel.SummaryType.SubTotal
        Me.txt調整額小計.DataField = db.調整額Column.ColumnName

        Me.txt入金額小計.SummaryFunc = SectionReportModel.SummaryFunc.Sum
        Me.txt入金額小計.SummaryGroup = Me.GroupHeader1.Name
        Me.txt入金額小計.SummaryRunning = SectionReportModel.SummaryRunning.Group
        Me.txt入金額小計.SummaryType = SectionReportModel.SummaryType.SubTotal
        Me.txt入金額小計.DataField = db.入金額Column.ColumnName

        Me.txt売上額小計.SummaryFunc = SectionReportModel.SummaryFunc.Sum
        Me.txt売上額小計.SummaryGroup = Me.GroupHeader1.Name
        Me.txt売上額小計.SummaryRunning = SectionReportModel.SummaryRunning.Group
        Me.txt売上額小計.SummaryType = SectionReportModel.SummaryType.SubTotal
        Me.txt売上額小計.DataField = db.税抜額Column.ColumnName

        ' ﾌｯﾀﾞ合計
        Me.txt請求額合計.SummaryFunc = SectionReportModel.SummaryFunc.Sum
        Me.txt請求額合計.SummaryRunning = SectionReportModel.SummaryRunning.All
        Me.txt請求額合計.SummaryType = SectionReportModel.SummaryType.GrandTotal
        Me.txt請求額合計.DataField = db.請求額Column.ColumnName

        Me.txt繰越額合計.SummaryFunc = SectionReportModel.SummaryFunc.Sum
        Me.txt繰越額合計.SummaryRunning = SectionReportModel.SummaryRunning.All
        Me.txt繰越額合計.SummaryType = SectionReportModel.SummaryType.GrandTotal
        Me.txt繰越額合計.DataField = db.繰越額Column.ColumnName

        Me.txt消費税合計.SummaryFunc = SectionReportModel.SummaryFunc.Sum
        Me.txt消費税合計.SummaryRunning = SectionReportModel.SummaryRunning.All
        Me.txt消費税合計.SummaryType = SectionReportModel.SummaryType.GrandTotal
        Me.txt消費税合計.DataField = db.消費税Column.ColumnName

        Me.txt前回額合計.SummaryFunc = SectionReportModel.SummaryFunc.Sum
        Me.txt前回額合計.SummaryRunning = SectionReportModel.SummaryRunning.All
        Me.txt前回額合計.SummaryType = SectionReportModel.SummaryType.GrandTotal
        Me.txt前回額合計.DataField = db.前回額Column.ColumnName

        Me.txt調整額合計.SummaryFunc = SectionReportModel.SummaryFunc.Sum
        Me.txt調整額合計.SummaryRunning = SectionReportModel.SummaryRunning.All
        Me.txt調整額合計.SummaryType = SectionReportModel.SummaryType.GrandTotal
        Me.txt調整額合計.DataField = db.調整額Column.ColumnName

        Me.txt入金額合計.SummaryFunc = SectionReportModel.SummaryFunc.Sum
        Me.txt入金額合計.SummaryRunning = SectionReportModel.SummaryRunning.All
        Me.txt入金額合計.SummaryType = SectionReportModel.SummaryType.GrandTotal
        Me.txt入金額合計.DataField = db.入金額Column.ColumnName

        Me.txt売上額合計.SummaryFunc = SectionReportModel.SummaryFunc.Sum
        Me.txt売上額合計.SummaryRunning = SectionReportModel.SummaryRunning.All
        Me.txt売上額合計.SummaryType = SectionReportModel.SummaryType.GrandTotal
        Me.txt売上額合計.DataField = db.税抜額Column.ColumnName

    End Sub

    Private Sub Detail_Format(sender As Object, e As EventArgs) Handles Detail.Format

        int行NO += 1

    End Sub
End Class
