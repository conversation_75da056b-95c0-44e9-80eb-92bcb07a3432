﻿Imports GrapeCity.ActiveReports
Imports GrapeCity.ActiveReports.Document
Imports Dbs.Application.Logic.Database

Public Class UriageDenpyoList
    Private int行NO As Integer = 0

    Private Sub UriageDenpyoList_ReportStart(sender As Object, e As EventArgs) Handles Me.ReportStart

        ' ｸﾞﾙｰﾌﾟﾍｯﾀﾞの設定
        Me.GroupHeader1.RepeatStyle = SectionReportModel.RepeatStyle.OnPage

        ' ﾃﾞｰﾀﾌｨｰﾙﾄﾞの設定
        Dim db As New DbReport.R売上伝票一覧DataTable
        Me.txtDT開始日付.DataField = db.開始日付Column.ColumnName
        Me.txtDT終了日付.DataField = db.終了日付Column.ColumnName
        Me.txtDT搬入伝票NO.DataField = db.搬入番号Column.ColumnName
        Me.txtDT返納日付.DataField = db.返納日付Column.ColumnName
        Me.txtHD伝票NO.DataField = db.売上番号Column.ColumnName
        Me.txtHD搬入日付.DataField = db.搬入日付Column.ColumnName
        Me.txtDT計算区分.DataField = db.計算区分Column.ColumnName
        Me.txtDT商品コード.DataField = db.商品コードColumn.ColumnName
        Me.txtDT商品名.DataField = db.商品名Column.ColumnName
        Me.txtDT備考明細.DataField = db.備考明細Column.ColumnName
        Me.txtDT数量.DataField = db.数量Column.ColumnName
        Me.txtDT単位区分.DataField = db.単位区分名Column.ColumnName
        Me.txtDT単価.DataField = db.単価Column.ColumnName
        Me.txtDT基本料金.DataField = db.基本料金Column.ColumnName

        Me.txtHD工事コード.DataField = db.工事コードColumn.ColumnName
        Me.txtHD工事名.DataField = db.工事名Column.ColumnName
        Me.txtHD住所.DataField = db.住所Column.ColumnName
        Me.txtHD摘要1.DataField = db.摘要1Column.ColumnName
        Me.txtHD摘要2.DataField = db.摘要2Column.ColumnName
        Me.txtHD伝票区分名.DataField = db.伝票区分名Column.ColumnName
        Me.txtHD得意先コード.DataField = db.得意先コードColumn.ColumnName
        Me.txtHD得意先名.DataField = db.得意先名Column.ColumnName

        Me.hid行NO.DataField = db.行NOColumn.ColumnName

        ' 書式
        Me.txtDT数量.OutputFormat = "#,##0"
        Me.txtDT単価.OutputFormat = "#,##0"
        Me.txtDT基本料金.OutputFormat = "#,##0"
    End Sub

    Private Sub Detail_Format(sender As Object, e As EventArgs) Handles Detail.Format

        int行NO += 1
        Me.txt行NO.Text = int行NO

        Me.txtDT開始日付.Visible = (Me.hid行NO.Text <> "0")
        Me.txtDT基本料金.Visible = (Me.hid行NO.Text <> "0")
        Me.txtDT計算区分.Visible = (Me.hid行NO.Text <> "0")
        Me.txtDT終了日付.Visible = (Me.hid行NO.Text <> "0")
        Me.txtDT商品コード.Visible = (Me.hid行NO.Text <> "0")
        Me.txtDT商品名.Visible = (Me.hid行NO.Text <> "0")
        Me.txtDT数量.Visible = (Me.hid行NO.Text <> "0")
        Me.txtDT単位区分.Visible = (Me.hid行NO.Text <> "0")
        Me.txtDT単価.Visible = (Me.hid行NO.Text <> "0")
        Me.txtDT搬入伝票NO.Visible = (Me.hid行NO.Text <> "0")
        Me.txtDT備考明細.Visible = (Me.hid行NO.Text <> "0")
        Me.txtDT返納日付.Visible = (Me.hid行NO.Text <> "0")

        Me.txtHD工事コード.Visible = (Me.hid行NO.Text = "0")
        Me.txtHD工事名.Visible = (Me.hid行NO.Text = "0")
        Me.txtHD住所.Visible = (Me.hid行NO.Text = "0")
        Me.txtHD摘要1.Visible = (Me.hid行NO.Text = "0")
        Me.txtHD摘要2.Visible = (Me.hid行NO.Text = "0")
        Me.txtHD伝票区分名.Visible = (Me.hid行NO.Text = "0")
        Me.txtHD得意先コード.Visible = (Me.hid行NO.Text = "0")
        Me.txtHD得意先名.Visible = (Me.hid行NO.Text = "0")

        If (Me.hid行NO.Text <> "0") Then
            Me.txtHD伝票NO.Text = ""
        End If
    End Sub
End Class
