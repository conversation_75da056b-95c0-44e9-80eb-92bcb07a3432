﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Public Class UriageNohinshoSub
    Inherits GrapeCity.ActiveReports.SectionReport

    'フォームがコンポーネントの一覧をクリーンアップするために dispose をオーバーライドします。
    Protected Overloads Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing Then
        End If
        MyBase.Dispose(disposing)
    End Sub

    'メモ: 以下のプロシージャは ActiveReports デザイナーで必要です。
    'ActiveReports デザイナーを使用して変更できます。  
    'コード エディターを使って変更しないでください。
    Private WithEvents PageHeader As GrapeCity.ActiveReports.SectionReportModel.PageHeader
    Private WithEvents Detail As GrapeCity.ActiveReports.SectionReportModel.Detail
    Private WithEvents PageFooter As GrapeCity.ActiveReports.SectionReportModel.PageFooter
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.Resources.ResourceManager = New System.Resources.ResourceManager(GetType(UriageNohinshoSub))
        Me.PageHeader = New GrapeCity.ActiveReports.SectionReportModel.PageHeader()
        Me.Detail = New GrapeCity.ActiveReports.SectionReportModel.Detail()
        Me.txt商品名 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt数量 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt単位区分名 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt備考明細 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt行NO = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.PageFooter = New GrapeCity.ActiveReports.SectionReportModel.PageFooter()
        Me.GHD伝票番号 = New GrapeCity.ActiveReports.SectionReportModel.GroupHeader()
        Me.txt工事住所 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.lbl住所 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.txt工事名 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt工事コード = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.lbl工事コード = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.txtタイトル = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txtタイトル控 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt得意先名 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt得意先コード = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.lbl得意先コード = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.lbl工事名 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.txt会社名 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt会社郵便番号 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.Label1 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.txt会社住所 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt会社電話番号 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.Label2 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.txt会社FAX番号 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.Label3 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.lbl品名 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.lbl数量 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.lbl単位名 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.Label4 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.hid売上番号 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt御中 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt搬入年月日 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.hid搬入日付 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.TextBox1 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.hid伝票区分 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.GFT伝票番号 = New GrapeCity.ActiveReports.SectionReportModel.GroupFooter()
        Me.txt摘要1 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt摘要2 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.lbl摘要 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.lbl受領印 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.txt受領印 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.hid明細件数 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        CType(Me.txt商品名, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt数量, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt単位区分名, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt備考明細, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt行NO, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt工事住所, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lbl住所, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt工事名, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt工事コード, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lbl工事コード, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtタイトル, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtタイトル控, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt得意先名, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt得意先コード, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lbl得意先コード, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lbl工事名, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt会社名, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt会社郵便番号, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Label1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt会社住所, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt会社電話番号, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Label2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt会社FAX番号, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Label3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lbl品名, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lbl数量, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lbl単位名, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Label4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.hid売上番号, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt御中, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt搬入年月日, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.hid搬入日付, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextBox1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.hid伝票区分, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt摘要1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt摘要2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lbl摘要, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lbl受領印, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt受領印, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.hid明細件数, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me, System.ComponentModel.ISupportInitialize).BeginInit()
        '
        'PageHeader
        '
        Me.PageHeader.Height = 0!
        Me.PageHeader.Name = "PageHeader"
        '
        'Detail
        '
        Me.Detail.Controls.AddRange(New GrapeCity.ActiveReports.SectionReportModel.ARControl() {Me.txt商品名, Me.txt数量, Me.txt単位区分名, Me.txt備考明細, Me.txt行NO, Me.hid明細件数})
        Me.Detail.Height = 0.3149606!
        Me.Detail.Name = "Detail"
        '
        'txt商品名
        '
        Me.txt商品名.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt商品名.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.ThickSolid
        Me.txt商品名.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt商品名.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt商品名.CanGrow = False
        Me.txt商品名.Height = 0.3149606!
        Me.txt商品名.Left = 0!
        Me.txt商品名.Name = "txt商品名"
        Me.txt商品名.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.txt商品名.ShrinkToFit = True
        Me.txt商品名.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: justify; vertical-align: middle; " &
    "ddo-shrink-to-fit: true"
        Me.txt商品名.Text = "XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX" &
    "XXXXXXXXXXXXXXXXXXX"
        Me.txt商品名.Top = 0!
        Me.txt商品名.Width = 3.543307!
        '
        'txt数量
        '
        Me.txt数量.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt数量.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt数量.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt数量.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt数量.CanGrow = False
        Me.txt数量.Height = 0.3149606!
        Me.txt数量.Left = 3.543307!
        Me.txt数量.Name = "txt数量"
        Me.txt数量.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 10, 0)
        Me.txt数量.ShrinkToFit = True
        Me.txt数量.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: right; vertical-align: middle; dd" &
    "o-shrink-to-fit: true"
        Me.txt数量.Text = "9,999"
        Me.txt数量.Top = 0!
        Me.txt数量.Width = 0.7086614!
        '
        'txt単位区分名
        '
        Me.txt単位区分名.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt単位区分名.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt単位区分名.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt単位区分名.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt単位区分名.CanGrow = False
        Me.txt単位区分名.Height = 0.3149606!
        Me.txt単位区分名.Left = 4.251968!
        Me.txt単位区分名.Name = "txt単位区分名"
        Me.txt単位区分名.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.txt単位区分名.ShrinkToFit = True
        Me.txt単位区分名.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: center; vertical-align: middle; d" &
    "do-shrink-to-fit: true"
        Me.txt単位区分名.Text = "XXXX"
        Me.txt単位区分名.Top = 0!
        Me.txt単位区分名.Width = 0.5905512!
        '
        'txt備考明細
        '
        Me.txt備考明細.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt備考明細.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt備考明細.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.ThickSolid
        Me.txt備考明細.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt備考明細.CanGrow = False
        Me.txt備考明細.Height = 0.3149606!
        Me.txt備考明細.Left = 4.84252!
        Me.txt備考明細.Name = "txt備考明細"
        Me.txt備考明細.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.txt備考明細.ShrinkToFit = True
        Me.txt備考明細.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: justify; vertical-align: middle; " &
    "ddo-shrink-to-fit: true"
        Me.txt備考明細.Text = "XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
        Me.txt備考明細.Top = 0!
        Me.txt備考明細.Width = 2.637795!
        '
        'txt行NO
        '
        Me.txt行NO.CanGrow = False
        Me.txt行NO.Height = 0.1586614!
        Me.txt行NO.Left = 3.267717!
        Me.txt行NO.Name = "txt行NO"
        Me.txt行NO.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 1, 0)
        Me.txt行NO.ShrinkToFit = True
        Me.txt行NO.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: right; vertical-align: middle; dd" &
    "o-shrink-to-fit: true"
        Me.txt行NO.Text = "99"
        Me.txt行NO.Top = 0.1181102!
        Me.txt行NO.Width = 0.229527!
        '
        'PageFooter
        '
        Me.PageFooter.Height = 0!
        Me.PageFooter.Name = "PageFooter"
        '
        'GHD伝票番号
        '
        Me.GHD伝票番号.Controls.AddRange(New GrapeCity.ActiveReports.SectionReportModel.ARControl() {Me.txt工事住所, Me.lbl住所, Me.txt工事名, Me.txt工事コード, Me.lbl工事コード, Me.txtタイトル, Me.txtタイトル控, Me.txt得意先名, Me.txt得意先コード, Me.lbl得意先コード, Me.lbl工事名, Me.txt会社名, Me.txt会社郵便番号, Me.Label1, Me.txt会社住所, Me.txt会社電話番号, Me.Label2, Me.txt会社FAX番号, Me.Label3, Me.lbl品名, Me.lbl数量, Me.lbl単位名, Me.Label4, Me.hid売上番号, Me.txt御中, Me.txt搬入年月日, Me.hid搬入日付, Me.TextBox1, Me.hid伝票区分})
        Me.GHD伝票番号.Height = 1.771654!
        Me.GHD伝票番号.Name = "GHD伝票番号"
        '
        'txt工事住所
        '
        Me.txt工事住所.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt工事住所.CanGrow = False
        Me.txt工事住所.Height = 0.2755906!
        Me.txt工事住所.Left = 0.7874014!
        Me.txt工事住所.Name = "txt工事住所"
        Me.txt工事住所.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.txt工事住所.Style = "font-family: ＭＳ 明朝; font-size: 11pt; font-weight: normal; text-align: left; text-" &
    "justify: auto; vertical-align: middle; ddo-char-set: 1"
        Me.txt工事住所.Text = "XXXX"
        Me.txt工事住所.Top = 1.062992!
        Me.txt工事住所.Width = 2.362205!
        '
        'lbl住所
        '
        Me.lbl住所.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl住所.Height = 0.2755906!
        Me.lbl住所.HyperLink = Nothing
        Me.lbl住所.Left = 0!
        Me.lbl住所.Name = "lbl住所"
        Me.lbl住所.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: left; vertical-align: middle; ddo" &
    "-char-set: 1"
        Me.lbl住所.Text = "住　　　所"
        Me.lbl住所.Top = 1.062992!
        Me.lbl住所.Width = 0.7874014!
        '
        'txt工事名
        '
        Me.txt工事名.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt工事名.CanGrow = False
        Me.txt工事名.Height = 0.2755905!
        Me.txt工事名.Left = 0.7874016!
        Me.txt工事名.Name = "txt工事名"
        Me.txt工事名.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.txt工事名.Style = "font-family: ＭＳ 明朝; font-size: 11pt; font-weight: normal; text-align: left; text-" &
    "justify: auto; vertical-align: middle; ddo-char-set: 1"
        Me.txt工事名.Text = "XXXX"
        Me.txt工事名.Top = 0.7874016!
        Me.txt工事名.Width = 2.362205!
        '
        'txt工事コード
        '
        Me.txt工事コード.CanGrow = False
        Me.txt工事コード.Height = 0.1574803!
        Me.txt工事コード.Left = 0.7874014!
        Me.txt工事コード.Name = "txt工事コード"
        Me.txt工事コード.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.txt工事コード.Style = "font-family: ＭＳ 明朝; font-size: 9pt; font-weight: normal; text-align: left; text-j" &
    "ustify: auto; vertical-align: middle"
        Me.txt工事コード.Text = "012345"
        Me.txt工事コード.Top = 0.6299213!
        Me.txt工事コード.Width = 1.968504!
        '
        'lbl工事コード
        '
        Me.lbl工事コード.Height = 0.1574803!
        Me.lbl工事コード.HyperLink = Nothing
        Me.lbl工事コード.Left = 0!
        Me.lbl工事コード.Name = "lbl工事コード"
        Me.lbl工事コード.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: left; vertical-align: middle; ddo" &
    "-char-set: 1"
        Me.lbl工事コード.Text = "工事コード"
        Me.lbl工事コード.Top = 0.6299213!
        Me.lbl工事コード.Width = 0.7874014!
        '
        'txtタイトル
        '
        Me.txtタイトル.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtタイトル.CanGrow = False
        Me.txtタイトル.CharacterSpacing = 18.0!
        Me.txtタイトル.Height = 0.2755905!
        Me.txtタイトル.Left = 2.952756!
        Me.txtタイトル.Name = "txtタイトル"
        Me.txtタイトル.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.txtタイトル.Style = "font-family: ＭＳ ゴシック; font-size: 14.25pt; font-weight: bold; text-align: center; " &
    "text-justify: auto; vertical-align: middle"
        Me.txtタイトル.Text = "○×伝票"
        Me.txtタイトル.Top = 0!
        Me.txtタイトル.Width = 1.968504!
        '
        'txtタイトル控
        '
        Me.txtタイトル控.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtタイトル控.CanGrow = False
        Me.txtタイトル控.Height = 0.2755906!
        Me.txtタイトル控.Left = 4.92126!
        Me.txtタイトル控.Name = "txtタイトル控"
        Me.txtタイトル控.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.txtタイトル控.Style = "font-family: ＭＳ ゴシック; font-size: 14.25pt; font-weight: bold; text-align: left; te" &
    "xt-justify: auto; vertical-align: middle"
        Me.txtタイトル控.Text = "(控)"
        Me.txtタイトル控.Top = 0!
        Me.txtタイトル控.Width = 0.472441!
        '
        'txt得意先名
        '
        Me.txt得意先名.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt得意先名.CanGrow = False
        Me.txt得意先名.Height = 0.2755905!
        Me.txt得意先名.Left = 0!
        Me.txt得意先名.Name = "txt得意先名"
        Me.txt得意先名.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.txt得意先名.Style = "font-family: ＭＳ 明朝; font-size: 11pt; font-weight: normal; text-align: left; text-" &
    "justify: auto; vertical-align: middle; ddo-char-set: 1"
        Me.txt得意先名.Text = "XXXX"
        Me.txt得意先名.Top = 0.2755906!
        Me.txt得意先名.Width = 2.440945!
        '
        'txt得意先コード
        '
        Me.txt得意先コード.CanGrow = False
        Me.txt得意先コード.Height = 0.1574803!
        Me.txt得意先コード.Left = 0.7874016!
        Me.txt得意先コード.Name = "txt得意先コード"
        Me.txt得意先コード.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.txt得意先コード.Style = "font-family: ＭＳ 明朝; font-size: 9pt; font-weight: normal; text-align: left; text-j" &
    "ustify: auto; vertical-align: middle"
        Me.txt得意先コード.Text = "012345"
        Me.txt得意先コード.Top = 0.1181102!
        Me.txt得意先コード.Width = 1.968504!
        '
        'lbl得意先コード
        '
        Me.lbl得意先コード.Height = 0.1574803!
        Me.lbl得意先コード.HyperLink = Nothing
        Me.lbl得意先コード.Left = 0!
        Me.lbl得意先コード.Name = "lbl得意先コード"
        Me.lbl得意先コード.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: left; vertical-align: middle; ddo" &
    "-char-set: 1"
        Me.lbl得意先コード.Text = "お客様コード"
        Me.lbl得意先コード.Top = 0.1181104!
        Me.lbl得意先コード.Width = 0.7874016!
        '
        'lbl工事名
        '
        Me.lbl工事名.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl工事名.Height = 0.2755905!
        Me.lbl工事名.HyperLink = Nothing
        Me.lbl工事名.Left = 0!
        Me.lbl工事名.Name = "lbl工事名"
        Me.lbl工事名.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: left; vertical-align: middle; ddo" &
    "-char-set: 1"
        Me.lbl工事名.Text = "工　事　名"
        Me.lbl工事名.Top = 0.7874016!
        Me.lbl工事名.Width = 0.7874016!
        '
        'txt会社名
        '
        Me.txt会社名.CanGrow = False
        Me.txt会社名.CharacterSpacing = 7.0!
        Me.txt会社名.Height = 0.2755905!
        Me.txt会社名.Left = 5.0!
        Me.txt会社名.Name = "txt会社名"
        Me.txt会社名.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.txt会社名.Style = "font-family: ＭＳ ゴシック; font-size: 14.25pt; font-weight: bold; text-align: justify;" &
    " text-justify: auto; vertical-align: middle"
        Me.txt会社名.Text = "○×△□株式会社"
        Me.txt会社名.Top = 0.511811!
        Me.txt会社名.Width = 2.480315!
        '
        'txt会社郵便番号
        '
        Me.txt会社郵便番号.CanGrow = False
        Me.txt会社郵便番号.Height = 0.1574803!
        Me.txt会社郵便番号.Left = 5.157481!
        Me.txt会社郵便番号.Name = "txt会社郵便番号"
        Me.txt会社郵便番号.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.txt会社郵便番号.Style = "font-family: ＭＳ 明朝; font-size: 9pt; font-weight: normal; text-align: left; text-j" &
    "ustify: auto; vertical-align: middle"
        Me.txt会社郵便番号.Text = "012-3456"
        Me.txt会社郵便番号.Top = 0.7874016!
        Me.txt会社郵便番号.Width = 0.5511811!
        '
        'Label1
        '
        Me.Label1.Height = 0.1574803!
        Me.Label1.HyperLink = Nothing
        Me.Label1.Left = 5.0!
        Me.Label1.Name = "Label1"
        Me.Label1.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: right; vertical-align: middle; dd" &
    "o-char-set: 1"
        Me.Label1.Text = "〒"
        Me.Label1.Top = 0.7874016!
        Me.Label1.Width = 0.1574803!
        '
        'txt会社住所
        '
        Me.txt会社住所.CanGrow = False
        Me.txt会社住所.Height = 0.1574803!
        Me.txt会社住所.Left = 5.708662!
        Me.txt会社住所.Name = "txt会社住所"
        Me.txt会社住所.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.txt会社住所.Style = "font-family: ＭＳ 明朝; font-size: 9pt; font-weight: normal; text-align: left; text-j" &
    "ustify: auto; vertical-align: middle"
        Me.txt会社住所.Text = "XXXXXXXXXXXXXXXXXXXXXXXX"
        Me.txt会社住所.Top = 0.7874016!
        Me.txt会社住所.Width = 1.771654!
        '
        'txt会社電話番号
        '
        Me.txt会社電話番号.CanGrow = False
        Me.txt会社電話番号.CharacterSpacing = 2.0!
        Me.txt会社電話番号.Height = 0.1574803!
        Me.txt会社電話番号.Left = 6.141733!
        Me.txt会社電話番号.Name = "txt会社電話番号"
        Me.txt会社電話番号.Padding = New GrapeCity.ActiveReports.PaddingEx(5, 1, 0, 0)
        Me.txt会社電話番号.Style = "font-family: ＭＳ 明朝; font-size: 9pt; font-weight: normal; text-align: left; text-j" &
    "ustify: auto; vertical-align: middle"
        Me.txt会社電話番号.Text = "01-2345-6789"
        Me.txt会社電話番号.Top = 0.944882!
        Me.txt会社電話番号.Width = 1.338583!
        '
        'Label2
        '
        Me.Label2.Height = 0.1574803!
        Me.Label2.HyperLink = Nothing
        Me.Label2.Left = 5.708662!
        Me.Label2.Name = "Label2"
        Me.Label2.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: left; vertical-align: middle; ddo" &
    "-char-set: 1"
        Me.Label2.Text = "ＴＥＬ"
        Me.Label2.Top = 0.944882!
        Me.Label2.Width = 0.4330709!
        '
        'txt会社FAX番号
        '
        Me.txt会社FAX番号.CanGrow = False
        Me.txt会社FAX番号.CharacterSpacing = 2.0!
        Me.txt会社FAX番号.Height = 0.1574803!
        Me.txt会社FAX番号.Left = 6.141733!
        Me.txt会社FAX番号.Name = "txt会社FAX番号"
        Me.txt会社FAX番号.Padding = New GrapeCity.ActiveReports.PaddingEx(5, 1, 0, 0)
        Me.txt会社FAX番号.Style = "font-family: ＭＳ 明朝; font-size: 9pt; font-weight: normal; text-align: left; text-j" &
    "ustify: auto; vertical-align: middle"
        Me.txt会社FAX番号.Text = "01-2345-6789"
        Me.txt会社FAX番号.Top = 1.102362!
        Me.txt会社FAX番号.Width = 1.338583!
        '
        'Label3
        '
        Me.Label3.Height = 0.1574803!
        Me.Label3.HyperLink = Nothing
        Me.Label3.Left = 5.708662!
        Me.Label3.Name = "Label3"
        Me.Label3.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: left; vertical-align: middle; ddo" &
    "-char-set: 1"
        Me.Label3.Text = "ＦＡＸ"
        Me.Label3.Top = 1.102362!
        Me.Label3.Width = 0.4330709!
        '
        'lbl品名
        '
        Me.lbl品名.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl品名.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.ThickSolid
        Me.lbl品名.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl品名.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.ThickSolid
        Me.lbl品名.Height = 0.3149606!
        Me.lbl品名.HyperLink = Nothing
        Me.lbl品名.Left = 0!
        Me.lbl品名.Name = "lbl品名"
        Me.lbl品名.Style = "font-family: ＭＳ 明朝; font-size: 9.75pt; text-align: center; vertical-align: middle" &
    ""
        Me.lbl品名.Text = "品 名 規 格 又 は 作 業 内 容"
        Me.lbl品名.Top = 1.456693!
        Me.lbl品名.Width = 3.543307!
        '
        'lbl数量
        '
        Me.lbl数量.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl数量.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl数量.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl数量.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.ThickSolid
        Me.lbl数量.Height = 0.3149606!
        Me.lbl数量.HyperLink = Nothing
        Me.lbl数量.Left = 3.543307!
        Me.lbl数量.Name = "lbl数量"
        Me.lbl数量.Style = "font-family: ＭＳ 明朝; font-size: 9.75pt; text-align: center; vertical-align: middle" &
    ""
        Me.lbl数量.Text = "数　量"
        Me.lbl数量.Top = 1.456693!
        Me.lbl数量.Width = 0.7086614!
        '
        'lbl単位名
        '
        Me.lbl単位名.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl単位名.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl単位名.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl単位名.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.ThickSolid
        Me.lbl単位名.Height = 0.3149606!
        Me.lbl単位名.HyperLink = Nothing
        Me.lbl単位名.Left = 4.251969!
        Me.lbl単位名.Name = "lbl単位名"
        Me.lbl単位名.Style = "font-family: ＭＳ 明朝; font-size: 9.75pt; text-align: center; vertical-align: middle" &
    ""
        Me.lbl単位名.Text = "単　位"
        Me.lbl単位名.Top = 1.456693!
        Me.lbl単位名.Width = 0.5905514!
        '
        'Label4
        '
        Me.Label4.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.Label4.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.Label4.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.ThickSolid
        Me.Label4.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.ThickSolid
        Me.Label4.Height = 0.3149606!
        Me.Label4.HyperLink = Nothing
        Me.Label4.Left = 4.84252!
        Me.Label4.Name = "Label4"
        Me.Label4.Style = "font-family: ＭＳ 明朝; font-size: 9.75pt; text-align: center; vertical-align: middle" &
    ""
        Me.Label4.Text = "備　　　　　　　　考"
        Me.Label4.Top = 1.456693!
        Me.Label4.Width = 2.637795!
        '
        'hid売上番号
        '
        Me.hid売上番号.CanGrow = False
        Me.hid売上番号.Height = 0.1181102!
        Me.hid売上番号.Left = 3.681103!
        Me.hid売上番号.Name = "hid売上番号"
        Me.hid売上番号.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 1, 0)
        Me.hid売上番号.ShrinkToFit = True
        Me.hid売上番号.Style = "background-color: Red; font-family: ＭＳ 明朝; font-size: 9.75pt; text-align: right; " &
    "vertical-align: middle; ddo-shrink-to-fit: true"
        Me.hid売上番号.Text = Nothing
        Me.hid売上番号.Top = 0.8267719!
        Me.hid売上番号.Visible = False
        Me.hid売上番号.Width = 0.1181102!
        '
        'txt御中
        '
        Me.txt御中.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt御中.CanGrow = False
        Me.txt御中.Height = 0.2755905!
        Me.txt御中.Left = 2.440945!
        Me.txt御中.Name = "txt御中"
        Me.txt御中.Padding = New GrapeCity.ActiveReports.PaddingEx(0, 1, 0, 0)
        Me.txt御中.Style = "font-family: ＭＳ 明朝; font-size: 9pt; font-weight: normal; text-align: left; text-j" &
    "ustify: auto; vertical-align: middle; ddo-char-set: 1"
        Me.txt御中.Text = "御中"
        Me.txt御中.Top = 0.2755906!
        Me.txt御中.Width = 0.3149605!
        '
        'txt搬入年月日
        '
        Me.txt搬入年月日.CanGrow = False
        Me.txt搬入年月日.Height = 0.1574803!
        Me.txt搬入年月日.Left = 6.102362!
        Me.txt搬入年月日.Name = "txt搬入年月日"
        Me.txt搬入年月日.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.txt搬入年月日.Style = "font-family: ＭＳ 明朝; font-size: 9pt; font-weight: normal; text-align: right; text-" &
    "justify: auto; vertical-align: middle"
        Me.txt搬入年月日.Text = "9999年99月99日"
        Me.txt搬入年月日.Top = 0.2362205!
        Me.txt搬入年月日.Width = 1.181102!
        '
        'hid搬入日付
        '
        Me.hid搬入日付.CanGrow = False
        Me.hid搬入日付.Height = 0.1181102!
        Me.hid搬入日付.Left = 5.866142!
        Me.hid搬入日付.Name = "hid搬入日付"
        Me.hid搬入日付.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 1, 0)
        Me.hid搬入日付.ShrinkToFit = True
        Me.hid搬入日付.Style = "background-color: Red; font-family: ＭＳ 明朝; font-size: 9.75pt; text-align: right; " &
    "vertical-align: middle; ddo-shrink-to-fit: true"
        Me.hid搬入日付.Text = Nothing
        Me.hid搬入日付.Top = 0.2362205!
        Me.hid搬入日付.Visible = False
        Me.hid搬入日付.Width = 0.1181102!
        '
        'TextBox1
        '
        Me.TextBox1.CanGrow = False
        Me.TextBox1.Height = 0.1181102!
        Me.TextBox1.Left = 3.681103!
        Me.TextBox1.Name = "TextBox1"
        Me.TextBox1.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 1, 0)
        Me.TextBox1.ShrinkToFit = True
        Me.TextBox1.Style = "background-color: Red; font-family: ＭＳ 明朝; font-size: 9.75pt; text-align: right; " &
    "vertical-align: middle; ddo-shrink-to-fit: true"
        Me.TextBox1.Text = Nothing
        Me.TextBox1.Top = 0.8267719!
        Me.TextBox1.Visible = False
        Me.TextBox1.Width = 0.1181102!
        '
        'hid伝票区分
        '
        Me.hid伝票区分.CanGrow = False
        Me.hid伝票区分.Height = 0.1181102!
        Me.hid伝票区分.Left = 3.931103!
        Me.hid伝票区分.Name = "hid伝票区分"
        Me.hid伝票区分.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 1, 0)
        Me.hid伝票区分.ShrinkToFit = True
        Me.hid伝票区分.Style = "background-color: Red; font-family: ＭＳ 明朝; font-size: 9.75pt; text-align: right; " &
    "vertical-align: middle; ddo-shrink-to-fit: true"
        Me.hid伝票区分.Text = Nothing
        Me.hid伝票区分.Top = 0.8267717!
        Me.hid伝票区分.Visible = False
        Me.hid伝票区分.Width = 0.1181102!
        '
        'GFT伝票番号
        '
        Me.GFT伝票番号.Controls.AddRange(New GrapeCity.ActiveReports.SectionReportModel.ARControl() {Me.txt摘要1, Me.txt摘要2, Me.lbl摘要, Me.lbl受領印, Me.txt受領印})
        Me.GFT伝票番号.Height = 0.472441!
        Me.GFT伝票番号.Name = "GFT伝票番号"
        '
        'txt摘要1
        '
        Me.txt摘要1.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt摘要1.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt摘要1.CanGrow = False
        Me.txt摘要1.Height = 0.2362205!
        Me.txt摘要1.Left = 0.393701!
        Me.txt摘要1.Name = "txt摘要1"
        Me.txt摘要1.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.txt摘要1.ShrinkToFit = True
        Me.txt摘要1.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: left; vertical-align: middle; ddo" &
    "-shrink-to-fit: true"
        Me.txt摘要1.Text = "摘要1"
        Me.txt摘要1.Top = 0!
        Me.txt摘要1.Width = 3.858268!
        '
        'txt摘要2
        '
        Me.txt摘要2.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.ThickSolid
        Me.txt摘要2.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt摘要2.CanGrow = False
        Me.txt摘要2.Height = 0.2362205!
        Me.txt摘要2.Left = 0.393701!
        Me.txt摘要2.Name = "txt摘要2"
        Me.txt摘要2.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.txt摘要2.ShrinkToFit = True
        Me.txt摘要2.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: left; vertical-align: middle; ddo" &
    "-shrink-to-fit: true"
        Me.txt摘要2.Text = "摘要2"
        Me.txt摘要2.Top = 0.2362205!
        Me.txt摘要2.Width = 3.858268!
        '
        'lbl摘要
        '
        Me.lbl摘要.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.ThickSolid
        Me.lbl摘要.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.ThickSolid
        Me.lbl摘要.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl摘要.Height = 0.472441!
        Me.lbl摘要.HyperLink = Nothing
        Me.lbl摘要.Left = 0!
        Me.lbl摘要.Name = "lbl摘要"
        Me.lbl摘要.Padding = New GrapeCity.ActiveReports.PaddingEx(2, 2, 0, 0)
        Me.lbl摘要.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: center; vertical-align: top; ddo-" &
    "char-set: 1"
        Me.lbl摘要.Text = "摘要"
        Me.lbl摘要.Top = 0!
        Me.lbl摘要.Width = 0.3937008!
        '
        'lbl受領印
        '
        Me.lbl受領印.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.ThickSolid
        Me.lbl受領印.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl受領印.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl受領印.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl受領印.Height = 0.472441!
        Me.lbl受領印.HyperLink = Nothing
        Me.lbl受領印.Left = 4.251969!
        Me.lbl受領印.Name = "lbl受領印"
        Me.lbl受領印.Style = "font-family: ＭＳ 明朝; font-size: 9.75pt; text-align: center; vertical-align: middle" &
    ""
        Me.lbl受領印.Text = "受領印"
        Me.lbl受領印.Top = 0!
        Me.lbl受領印.Width = 0.5905512!
        '
        'txt受領印
        '
        Me.txt受領印.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.ThickSolid
        Me.txt受領印.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt受領印.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.ThickSolid
        Me.txt受領印.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt受領印.CanGrow = False
        Me.txt受領印.Height = 0.472441!
        Me.txt受領印.Left = 4.84252!
        Me.txt受領印.Name = "txt受領印"
        Me.txt受領印.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.txt受領印.ShrinkToFit = True
        Me.txt受領印.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: justify; vertical-align: middle; " &
    "ddo-shrink-to-fit: true"
        Me.txt受領印.Text = Nothing
        Me.txt受領印.Top = 0!
        Me.txt受領印.Width = 2.637795!
        '
        'hid明細件数
        '
        Me.hid明細件数.CanGrow = False
        Me.hid明細件数.Height = 0.1181102!
        Me.hid明細件数.Left = 3.326772!
        Me.hid明細件数.Name = "hid明細件数"
        Me.hid明細件数.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 1, 0)
        Me.hid明細件数.ShrinkToFit = True
        Me.hid明細件数.Style = "background-color: Red; font-family: ＭＳ 明朝; font-size: 9.75pt; text-align: right; " &
    "vertical-align: middle; ddo-shrink-to-fit: true"
        Me.hid明細件数.Text = Nothing
        Me.hid明細件数.Top = 0!
        Me.hid明細件数.Visible = False
        Me.hid明細件数.Width = 0.1181102!
        '
        'UriageNohinshoSub
        '
        Me.MasterReport = False
        Me.PageSettings.DefaultPaperSize = False
        Me.PageSettings.Margins.Bottom = 0!
        Me.PageSettings.Margins.Left = 0!
        Me.PageSettings.Margins.Right = 0!
        Me.PageSettings.Margins.Top = 0!
        Me.PageSettings.PaperHeight = 5.41!
        Me.PageSettings.PaperKind = System.Drawing.Printing.PaperKind.Custom
        Me.PageSettings.PaperName = "ユーザー定義のサイズ"
        Me.PageSettings.PaperWidth = 7.480315!
        Me.PrintWidth = 7.480315!
        Me.Sections.Add(Me.PageHeader)
        Me.Sections.Add(Me.GHD伝票番号)
        Me.Sections.Add(Me.Detail)
        Me.Sections.Add(Me.GFT伝票番号)
        Me.Sections.Add(Me.PageFooter)
        Me.StyleSheet.Add(New DDCssLib.StyleSheetRule("font-style: normal; text-decoration: none; font-weight: normal; font-size: 10pt; " &
            "color: Black; font-family: ""MS UI Gothic""; ddo-char-set: 128", "Normal"))
        Me.StyleSheet.Add(New DDCssLib.StyleSheetRule("font-size: 16pt; font-weight: bold; font-family: ""MS UI Gothic""; ddo-char-set: 12" &
            "8", "Heading1", "Normal"))
        Me.StyleSheet.Add(New DDCssLib.StyleSheetRule("font-size: 14pt; font-weight: bold; font-style: inherit; font-family: ""MS UI Goth" &
            "ic""; ddo-char-set: 128", "Heading2", "Normal"))
        Me.StyleSheet.Add(New DDCssLib.StyleSheetRule("font-size: 13pt; font-weight: bold; ddo-char-set: 128", "Heading3", "Normal"))
        CType(Me.txt商品名, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt数量, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt単位区分名, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt備考明細, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt行NO, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt工事住所, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lbl住所, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt工事名, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt工事コード, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lbl工事コード, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtタイトル, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtタイトル控, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt得意先名, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt得意先コード, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lbl得意先コード, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lbl工事名, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt会社名, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt会社郵便番号, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Label1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt会社住所, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt会社電話番号, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Label2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt会社FAX番号, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Label3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lbl品名, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lbl数量, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lbl単位名, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Label4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.hid売上番号, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt御中, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt搬入年月日, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.hid搬入日付, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextBox1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.hid伝票区分, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt摘要1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt摘要2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lbl摘要, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lbl受領印, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt受領印, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.hid明細件数, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me, System.ComponentModel.ISupportInitialize).EndInit()

    End Sub

    Private WithEvents GHD伝票番号 As GrapeCity.ActiveReports.SectionReportModel.GroupHeader
    Private WithEvents GFT伝票番号 As GrapeCity.ActiveReports.SectionReportModel.GroupFooter
    Private WithEvents txtタイトル As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt得意先名 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt得意先コード As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents lbl得意先コード As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents txt工事名 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt工事コード As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents lbl工事コード As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents lbl工事名 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents txt工事住所 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents lbl住所 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents txt会社名 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt会社郵便番号 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents Label1 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents txt会社住所 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt会社電話番号 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents Label2 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents txt会社FAX番号 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents Label3 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents txt商品名 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt数量 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt摘要1 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt摘要2 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents lbl摘要 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents lbl品名 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents txt単位区分名 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt備考明細 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents lbl数量 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents lbl単位名 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents Label4 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents lbl受領印 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents txt受領印 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents hid売上番号 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt行NO As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt御中 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt搬入年月日 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents hid搬入日付 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Public WithEvents txtタイトル控 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents TextBox1 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents hid伝票区分 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents hid明細件数 As GrapeCity.ActiveReports.SectionReportModel.TextBox
End Class
