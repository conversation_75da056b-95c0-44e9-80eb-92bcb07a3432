﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Public Class UriageDenpyoList
    Inherits Dbs.Asphalt.Core.BaseReportA4L

    'フォームがコンポーネントの一覧をクリーンアップするために dispose をオーバーライドします。
    Protected Overloads Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing Then
        End If
        MyBase.Dispose(disposing)
    End Sub

    'メモ: 以下のプロシージャは ActiveReports デザイナーで必要です。
    'ActiveReports デザイナーを使用して変更できます。  
    'コード エディターを使って変更しないでください。
    Private WithEvents PageHeader As GrapeCity.ActiveReports.SectionReportModel.PageHeader
    Private WithEvents Detail As GrapeCity.ActiveReports.SectionReportModel.Detail
    Private WithEvents PageFooter As GrapeCity.ActiveReports.SectionReportModel.PageFooter
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.Resources.ResourceManager = New System.Resources.ResourceManager(GetType(UriageDenpyoList))
        Me.Detail = New GrapeCity.ActiveReports.SectionReportModel.Detail()
        Me.txtDT開始日付 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txtDT終了日付 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txtDT搬入伝票NO = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txtDT返納日付 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txt行NO = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txtHD伝票NO = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txtHD搬入日付 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txtDT計算区分 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txtDT商品名 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txtDT備考明細 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txtDT数量 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txtDT単位区分 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txtDT単価 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txtDT基本料金 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txtDT商品コード = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txtHD伝票区分名 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txtHD得意先コード = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txtHD工事コード = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txtHD得意先名 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txtHD工事名 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txtHD住所 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txtHD摘要1 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.txtHD摘要2 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.hid行NO = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.GroupHeader1 = New GrapeCity.ActiveReports.SectionReportModel.GroupHeader()
        Me.lbl伝票NO = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.lbl行NO = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.lbl搬入日付 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.lbl区分 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.lbl名称 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.lbl数量 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.lbl単位区分 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.lbl単価 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.lbl基本料金 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.lbl開始終了日付 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.lbl返納日付 = New GrapeCity.ActiveReports.SectionReportModel.Label()
        Me.GroupFooter1 = New GrapeCity.ActiveReports.SectionReportModel.GroupFooter()
        CType(Me.lblCompName, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lblDate, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lblTitle, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtJyoken, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtDT開始日付, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtDT終了日付, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtDT搬入伝票NO, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtDT返納日付, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt行NO, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtHD伝票NO, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtHD搬入日付, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtDT計算区分, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtDT商品名, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtDT備考明細, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtDT数量, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtDT単位区分, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtDT単価, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtDT基本料金, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtDT商品コード, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtHD伝票区分名, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtHD得意先コード, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtHD工事コード, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtHD得意先名, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtHD工事名, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtHD住所, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtHD摘要1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtHD摘要2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.hid行NO, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lbl伝票NO, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lbl行NO, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lbl搬入日付, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lbl区分, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lbl名称, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lbl数量, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lbl単位区分, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lbl単価, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lbl基本料金, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lbl開始終了日付, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.lbl返納日付, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me, System.ComponentModel.ISupportInitialize).BeginInit()
        '
        'Detail
        '
        Me.Detail.Controls.AddRange(New GrapeCity.ActiveReports.SectionReportModel.ARControl() {Me.txtDT開始日付, Me.txtDT終了日付, Me.txtDT搬入伝票NO, Me.txtDT返納日付, Me.txt行NO, Me.txtHD伝票NO, Me.txtHD搬入日付, Me.txtDT計算区分, Me.txtDT商品名, Me.txtDT備考明細, Me.txtDT数量, Me.txtDT単位区分, Me.txtDT単価, Me.txtDT基本料金, Me.txtDT商品コード, Me.txtHD伝票区分名, Me.txtHD得意先コード, Me.txtHD工事コード, Me.txtHD得意先名, Me.txtHD工事名, Me.txtHD住所, Me.txtHD摘要1, Me.txtHD摘要2, Me.hid行NO})
        Me.Detail.Height = 0.3543308!
        Me.Detail.Name = "Detail"
        Me.Detail.RepeatToFill = True
        '
        'txtDT開始日付
        '
        Me.txtDT開始日付.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtDT開始日付.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtDT開始日付.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtDT開始日付.CanGrow = False
        Me.txtDT開始日付.Height = 0.1771654!
        Me.txtDT開始日付.Left = 8.149607!
        Me.txtDT開始日付.Name = "txtDT開始日付"
        Me.txtDT開始日付.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 0, 1, 0)
        Me.txtDT開始日付.ShrinkToFit = True
        Me.txtDT開始日付.Style = "font-family: ＭＳ 明朝; font-size: 9pt; ddo-shrink-to-fit: true"
        Me.txtDT開始日付.Text = "yyyy/mm/dd"
        Me.txtDT開始日付.Top = 0!
        Me.txtDT開始日付.Width = 1.181102!
        '
        'txtDT終了日付
        '
        Me.txtDT終了日付.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtDT終了日付.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtDT終了日付.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtDT終了日付.CanGrow = False
        Me.txtDT終了日付.Height = 0.1771653!
        Me.txtDT終了日付.Left = 8.149607!
        Me.txtDT終了日付.Name = "txtDT終了日付"
        Me.txtDT終了日付.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 0, 1, 0)
        Me.txtDT終了日付.ShrinkToFit = True
        Me.txtDT終了日付.Style = "font-family: ＭＳ 明朝; font-size: 9pt; ddo-shrink-to-fit: true"
        Me.txtDT終了日付.Text = "yyyy/mm/dd"
        Me.txtDT終了日付.Top = 0.1771654!
        Me.txtDT終了日付.Width = 1.181102!
        '
        'txtDT搬入伝票NO
        '
        Me.txtDT搬入伝票NO.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtDT搬入伝票NO.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtDT搬入伝票NO.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtDT搬入伝票NO.CanGrow = False
        Me.txtDT搬入伝票NO.Height = 0.1771653!
        Me.txtDT搬入伝票NO.Left = 9.330709!
        Me.txtDT搬入伝票NO.Name = "txtDT搬入伝票NO"
        Me.txtDT搬入伝票NO.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 0, 1, 0)
        Me.txtDT搬入伝票NO.ShrinkToFit = True
        Me.txtDT搬入伝票NO.Style = "font-family: ＭＳ 明朝; font-size: 9pt; ddo-shrink-to-fit: true"
        Me.txtDT搬入伝票NO.Text = "0123456789"
        Me.txtDT搬入伝票NO.Top = 0!
        Me.txtDT搬入伝票NO.Width = 1.181102!
        '
        'txtDT返納日付
        '
        Me.txtDT返納日付.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtDT返納日付.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtDT返納日付.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtDT返納日付.CanGrow = False
        Me.txtDT返納日付.Height = 0.1771653!
        Me.txtDT返納日付.Left = 9.330709!
        Me.txtDT返納日付.Name = "txtDT返納日付"
        Me.txtDT返納日付.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 0, 1, 0)
        Me.txtDT返納日付.ShrinkToFit = True
        Me.txtDT返納日付.Style = "font-family: ＭＳ 明朝; font-size: 9pt; ddo-shrink-to-fit: true"
        Me.txtDT返納日付.Text = "yyyy/mm/dd"
        Me.txtDT返納日付.Top = 0.1771653!
        Me.txtDT返納日付.Width = 1.181102!
        '
        'txt行NO
        '
        Me.txt行NO.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt行NO.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt行NO.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt行NO.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txt行NO.CanGrow = False
        Me.txt行NO.Height = 0.3543307!
        Me.txt行NO.Left = 0!
        Me.txt行NO.Name = "txt行NO"
        Me.txt行NO.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 1, 0)
        Me.txt行NO.ShrinkToFit = True
        Me.txt行NO.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: right; vertical-align: middle; dd" &
    "o-shrink-to-fit: true"
        Me.txt行NO.Text = "99"
        Me.txt行NO.Top = 0!
        Me.txt行NO.Width = 0.3543307!
        '
        'txtHD伝票NO
        '
        Me.txtHD伝票NO.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtHD伝票NO.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtHD伝票NO.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtHD伝票NO.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtHD伝票NO.CanGrow = False
        Me.txtHD伝票NO.Height = 0.3543307!
        Me.txtHD伝票NO.Left = 0.3543307!
        Me.txtHD伝票NO.Name = "txtHD伝票NO"
        Me.txtHD伝票NO.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.txtHD伝票NO.ShrinkToFit = True
        Me.txtHD伝票NO.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: center; vertical-align: middle; d" &
    "do-shrink-to-fit: true"
        Me.txtHD伝票NO.Text = "0123456789"
        Me.txtHD伝票NO.Top = 0.00000001490116!
        Me.txtHD伝票NO.Width = 0.7480315!
        '
        'txtHD搬入日付
        '
        Me.txtHD搬入日付.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtHD搬入日付.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtHD搬入日付.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtHD搬入日付.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtHD搬入日付.CanGrow = False
        Me.txtHD搬入日付.Height = 0.3543307!
        Me.txtHD搬入日付.Left = 1.102362!
        Me.txtHD搬入日付.Name = "txtHD搬入日付"
        Me.txtHD搬入日付.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.txtHD搬入日付.ShrinkToFit = True
        Me.txtHD搬入日付.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: center; vertical-align: middle; d" &
    "do-shrink-to-fit: true"
        Me.txtHD搬入日付.Text = "yyyy/mm/dd"
        Me.txtHD搬入日付.Top = 0!
        Me.txtHD搬入日付.Width = 0.7874016!
        '
        'txtDT計算区分
        '
        Me.txtDT計算区分.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtDT計算区分.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtDT計算区分.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtDT計算区分.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtDT計算区分.CanGrow = False
        Me.txtDT計算区分.Height = 0.3543307!
        Me.txtDT計算区分.Left = 1.889764!
        Me.txtDT計算区分.Name = "txtDT計算区分"
        Me.txtDT計算区分.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.txtDT計算区分.ShrinkToFit = True
        Me.txtDT計算区分.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: center; vertical-align: middle; d" &
    "do-shrink-to-fit: true"
        Me.txtDT計算区分.Text = "XXXX"
        Me.txtDT計算区分.Top = 0.00000001490116!
        Me.txtDT計算区分.Width = 0.3937008!
        '
        'txtDT商品名
        '
        Me.txtDT商品名.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtDT商品名.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtDT商品名.CanGrow = False
        Me.txtDT商品名.Height = 0.1771654!
        Me.txtDT商品名.Left = 2.775591!
        Me.txtDT商品名.Name = "txtDT商品名"
        Me.txtDT商品名.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 0, 0, 0)
        Me.txtDT商品名.ShrinkToFit = True
        Me.txtDT商品名.Style = "font-family: ＭＳ 明朝; font-size: 9pt; vertical-align: middle; ddo-shrink-to-fit: tr" &
    "ue"
        Me.txtDT商品名.Text = "XXXX"
        Me.txtDT商品名.Top = 0!
        Me.txtDT商品名.Width = 2.46063!
        '
        'txtDT備考明細
        '
        Me.txtDT備考明細.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtDT備考明細.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtDT備考明細.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtDT備考明細.CanGrow = False
        Me.txtDT備考明細.Height = 0.1771653!
        Me.txtDT備考明細.Left = 2.283465!
        Me.txtDT備考明細.Name = "txtDT備考明細"
        Me.txtDT備考明細.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 0, 0, 0)
        Me.txtDT備考明細.ShrinkToFit = True
        Me.txtDT備考明細.Style = "font-family: ＭＳ 明朝; font-size: 9pt; vertical-align: middle; ddo-shrink-to-fit: tr" &
    "ue"
        Me.txtDT備考明細.Text = "XXXX"
        Me.txtDT備考明細.Top = 0.1771654!
        Me.txtDT備考明細.Width = 2.952756!
        '
        'txtDT数量
        '
        Me.txtDT数量.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtDT数量.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtDT数量.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtDT数量.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtDT数量.CanGrow = False
        Me.txtDT数量.Height = 0.3543307!
        Me.txtDT数量.Left = 5.23622!
        Me.txtDT数量.Name = "txtDT数量"
        Me.txtDT数量.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 1, 0)
        Me.txtDT数量.ShrinkToFit = True
        Me.txtDT数量.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: right; vertical-align: middle; dd" &
    "o-shrink-to-fit: true"
        Me.txtDT数量.Text = "99999999"
        Me.txtDT数量.Top = 0!
        Me.txtDT数量.Width = 0.6692914!
        '
        'txtDT単位区分
        '
        Me.txtDT単位区分.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtDT単位区分.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtDT単位区分.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtDT単位区分.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtDT単位区分.CanGrow = False
        Me.txtDT単位区分.Height = 0.3543307!
        Me.txtDT単位区分.Left = 5.905512!
        Me.txtDT単位区分.Name = "txtDT単位区分"
        Me.txtDT単位区分.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.txtDT単位区分.ShrinkToFit = True
        Me.txtDT単位区分.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: center; vertical-align: middle; d" &
    "do-shrink-to-fit: true"
        Me.txtDT単位区分.Text = "XXXX"
        Me.txtDT単位区分.Top = 0!
        Me.txtDT単位区分.Width = 0.3937007!
        '
        'txtDT単価
        '
        Me.txtDT単価.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtDT単価.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtDT単価.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtDT単価.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtDT単価.CanGrow = False
        Me.txtDT単価.Height = 0.3543307!
        Me.txtDT単価.Left = 6.299213!
        Me.txtDT単価.Name = "txtDT単価"
        Me.txtDT単価.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 1, 0)
        Me.txtDT単価.ShrinkToFit = True
        Me.txtDT単価.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: right; vertical-align: middle; dd" &
    "o-shrink-to-fit: true"
        Me.txtDT単価.Text = "99999999"
        Me.txtDT単価.Top = 0!
        Me.txtDT単価.Width = 0.8661417!
        '
        'txtDT基本料金
        '
        Me.txtDT基本料金.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtDT基本料金.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtDT基本料金.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtDT基本料金.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtDT基本料金.CanGrow = False
        Me.txtDT基本料金.Height = 0.3543307!
        Me.txtDT基本料金.Left = 7.165354!
        Me.txtDT基本料金.Name = "txtDT基本料金"
        Me.txtDT基本料金.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 1, 0)
        Me.txtDT基本料金.ShrinkToFit = True
        Me.txtDT基本料金.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: right; vertical-align: middle; dd" &
    "o-shrink-to-fit: true"
        Me.txtDT基本料金.Text = "99999999"
        Me.txtDT基本料金.Top = 0!
        Me.txtDT基本料金.Width = 0.9842523!
        '
        'txtDT商品コード
        '
        Me.txtDT商品コード.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtDT商品コード.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtDT商品コード.CanGrow = False
        Me.txtDT商品コード.Height = 0.1771654!
        Me.txtDT商品コード.Left = 2.283465!
        Me.txtDT商品コード.Name = "txtDT商品コード"
        Me.txtDT商品コード.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 0, 0, 0)
        Me.txtDT商品コード.ShrinkToFit = True
        Me.txtDT商品コード.Style = "font-family: ＭＳ 明朝; font-size: 9pt; vertical-align: middle; ddo-shrink-to-fit: tr" &
    "ue"
        Me.txtDT商品コード.Text = "XXXXXX"
        Me.txtDT商品コード.Top = 0!
        Me.txtDT商品コード.Width = 0.492126!
        '
        'txtHD伝票区分名
        '
        Me.txtHD伝票区分名.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtHD伝票区分名.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtHD伝票区分名.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtHD伝票区分名.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtHD伝票区分名.CanGrow = False
        Me.txtHD伝票区分名.Height = 0.3543307!
        Me.txtHD伝票区分名.Left = 1.889764!
        Me.txtHD伝票区分名.Name = "txtHD伝票区分名"
        Me.txtHD伝票区分名.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 0, 0)
        Me.txtHD伝票区分名.ShrinkToFit = True
        Me.txtHD伝票区分名.Style = "font-family: ＭＳ 明朝; font-size: 9pt; text-align: center; vertical-align: middle; d" &
    "do-shrink-to-fit: true"
        Me.txtHD伝票区分名.Text = "XXXX"
        Me.txtHD伝票区分名.Top = 0!
        Me.txtHD伝票区分名.Width = 0.3937007!
        '
        'txtHD得意先コード
        '
        Me.txtHD得意先コード.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtHD得意先コード.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtHD得意先コード.CanGrow = False
        Me.txtHD得意先コード.Height = 0.1771654!
        Me.txtHD得意先コード.Left = 2.283465!
        Me.txtHD得意先コード.Name = "txtHD得意先コード"
        Me.txtHD得意先コード.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 0, 0, 0)
        Me.txtHD得意先コード.ShrinkToFit = True
        Me.txtHD得意先コード.Style = "font-family: ＭＳ 明朝; font-size: 9pt; vertical-align: middle; ddo-shrink-to-fit: tr" &
    "ue"
        Me.txtHD得意先コード.Text = "XXXXXX"
        Me.txtHD得意先コード.Top = 0!
        Me.txtHD得意先コード.Width = 0.492126!
        '
        'txtHD工事コード
        '
        Me.txtHD工事コード.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtHD工事コード.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtHD工事コード.CanGrow = False
        Me.txtHD工事コード.Height = 0.1771654!
        Me.txtHD工事コード.Left = 2.283465!
        Me.txtHD工事コード.Name = "txtHD工事コード"
        Me.txtHD工事コード.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 0, 0, 0)
        Me.txtHD工事コード.ShrinkToFit = True
        Me.txtHD工事コード.Style = "font-family: ＭＳ 明朝; font-size: 9pt; vertical-align: middle; ddo-shrink-to-fit: tr" &
    "ue"
        Me.txtHD工事コード.Text = "XXXXXX"
        Me.txtHD工事コード.Top = 0.1771654!
        Me.txtHD工事コード.Width = 0.492126!
        '
        'txtHD得意先名
        '
        Me.txtHD得意先名.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtHD得意先名.CanGrow = False
        Me.txtHD得意先名.Height = 0.1771654!
        Me.txtHD得意先名.Left = 2.775591!
        Me.txtHD得意先名.Name = "txtHD得意先名"
        Me.txtHD得意先名.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 0, 0, 0)
        Me.txtHD得意先名.ShrinkToFit = True
        Me.txtHD得意先名.Style = "font-family: ＭＳ 明朝; font-size: 9pt; vertical-align: middle; ddo-shrink-to-fit: tr" &
    "ue"
        Me.txtHD得意先名.Text = "XXXX"
        Me.txtHD得意先名.Top = 0!
        Me.txtHD得意先名.Width = 5.374016!
        '
        'txtHD工事名
        '
        Me.txtHD工事名.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtHD工事名.CanGrow = False
        Me.txtHD工事名.Height = 0.1771654!
        Me.txtHD工事名.Left = 2.775591!
        Me.txtHD工事名.Name = "txtHD工事名"
        Me.txtHD工事名.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 0, 0, 0)
        Me.txtHD工事名.ShrinkToFit = True
        Me.txtHD工事名.Style = "font-family: ＭＳ 明朝; font-size: 9pt; vertical-align: middle; ddo-shrink-to-fit: tr" &
    "ue"
        Me.txtHD工事名.Text = "XXXX"
        Me.txtHD工事名.Top = 0.1771654!
        Me.txtHD工事名.Width = 2.46063!
        '
        'txtHD住所
        '
        Me.txtHD住所.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtHD住所.CanGrow = False
        Me.txtHD住所.Height = 0.1771653!
        Me.txtHD住所.Left = 5.236221!
        Me.txtHD住所.Name = "txtHD住所"
        Me.txtHD住所.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 0, 0, 0)
        Me.txtHD住所.ShrinkToFit = True
        Me.txtHD住所.Style = "font-family: ＭＳ 明朝; font-size: 9pt; vertical-align: middle; ddo-shrink-to-fit: tr" &
    "ue"
        Me.txtHD住所.Text = "XXXX"
        Me.txtHD住所.Top = 0.1771654!
        Me.txtHD住所.Width = 2.913386!
        '
        'txtHD摘要1
        '
        Me.txtHD摘要1.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtHD摘要1.CanGrow = False
        Me.txtHD摘要1.Height = 0.1771653!
        Me.txtHD摘要1.Left = 8.149607!
        Me.txtHD摘要1.Name = "txtHD摘要1"
        Me.txtHD摘要1.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 0, 0, 0)
        Me.txtHD摘要1.ShrinkToFit = True
        Me.txtHD摘要1.Style = "font-family: ＭＳ 明朝; font-size: 9pt; vertical-align: middle; ddo-shrink-to-fit: tr" &
    "ue"
        Me.txtHD摘要1.Text = "XXXX"
        Me.txtHD摘要1.Top = 0!
        Me.txtHD摘要1.Width = 2.362205!
        '
        'txtHD摘要2
        '
        Me.txtHD摘要2.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.txtHD摘要2.CanGrow = False
        Me.txtHD摘要2.Height = 0.1771653!
        Me.txtHD摘要2.Left = 8.149604!
        Me.txtHD摘要2.Name = "txtHD摘要2"
        Me.txtHD摘要2.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 0, 0, 0)
        Me.txtHD摘要2.ShrinkToFit = True
        Me.txtHD摘要2.Style = "font-family: ＭＳ 明朝; font-size: 9pt; vertical-align: middle; ddo-shrink-to-fit: tr" &
    "ue"
        Me.txtHD摘要2.Text = "XXXX"
        Me.txtHD摘要2.Top = 0.1771654!
        Me.txtHD摘要2.Width = 2.362204!
        '
        'hid行NO
        '
        Me.hid行NO.CanGrow = False
        Me.hid行NO.Height = 0.1181102!
        Me.hid行NO.Left = 0!
        Me.hid行NO.Name = "hid行NO"
        Me.hid行NO.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 1, 0)
        Me.hid行NO.ShrinkToFit = True
        Me.hid行NO.Style = "background-color: Red; font-family: ＭＳ 明朝; font-size: 9.75pt; text-align: right; " &
    "vertical-align: middle; ddo-shrink-to-fit: true"
        Me.hid行NO.Text = Nothing
        Me.hid行NO.Top = 0!
        Me.hid行NO.Visible = False
        Me.hid行NO.Width = 0.1181102!
        '
        'GroupHeader1
        '
        Me.GroupHeader1.Controls.AddRange(New GrapeCity.ActiveReports.SectionReportModel.ARControl() {Me.lbl伝票NO, Me.lbl行NO, Me.lbl搬入日付, Me.lbl区分, Me.lbl名称, Me.lbl数量, Me.lbl単位区分, Me.lbl単価, Me.lbl基本料金, Me.lbl開始終了日付, Me.lbl返納日付})
        Me.GroupHeader1.Height = 0.3543307!
        Me.GroupHeader1.Name = "GroupHeader1"
        '
        'lbl伝票NO
        '
        Me.lbl伝票NO.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl伝票NO.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl伝票NO.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl伝票NO.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl伝票NO.Height = 0.3543307!
        Me.lbl伝票NO.HyperLink = Nothing
        Me.lbl伝票NO.Left = 0.3543307!
        Me.lbl伝票NO.Name = "lbl伝票NO"
        Me.lbl伝票NO.Style = "font-family: ＭＳ 明朝; font-size: 9.75pt; text-align: center; vertical-align: middle" &
    ""
        Me.lbl伝票NO.Text = "伝票NO"
        Me.lbl伝票NO.Top = 0!
        Me.lbl伝票NO.Width = 0.7480315!
        '
        'lbl行NO
        '
        Me.lbl行NO.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl行NO.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl行NO.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl行NO.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl行NO.Height = 0.3543307!
        Me.lbl行NO.HyperLink = Nothing
        Me.lbl行NO.Left = 0!
        Me.lbl行NO.Name = "lbl行NO"
        Me.lbl行NO.Style = "font-family: ＭＳ 明朝; font-size: 9.75pt; text-align: center; vertical-align: middle" &
    ""
        Me.lbl行NO.Text = ""
        Me.lbl行NO.Top = 0.00000001490116!
        Me.lbl行NO.Width = 0.3543307!
        '
        'lbl搬入日付
        '
        Me.lbl搬入日付.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl搬入日付.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl搬入日付.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl搬入日付.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl搬入日付.Height = 0.3543307!
        Me.lbl搬入日付.HyperLink = Nothing
        Me.lbl搬入日付.Left = 1.102362!
        Me.lbl搬入日付.Name = "lbl搬入日付"
        Me.lbl搬入日付.Style = "font-family: ＭＳ 明朝; font-size: 9.75pt; text-align: center; vertical-align: middle" &
    ""
        Me.lbl搬入日付.Text = "搬入日"
        Me.lbl搬入日付.Top = 0.00000001490116!
        Me.lbl搬入日付.Width = 0.7874016!
        '
        'lbl区分
        '
        Me.lbl区分.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl区分.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl区分.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl区分.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl区分.Height = 0.3543307!
        Me.lbl区分.HyperLink = Nothing
        Me.lbl区分.Left = 1.889764!
        Me.lbl区分.Name = "lbl区分"
        Me.lbl区分.Style = "font-family: ＭＳ 明朝; font-size: 9.75pt; text-align: center; vertical-align: middle" &
    ""
        Me.lbl区分.Text = "区分"
        Me.lbl区分.Top = 0.00000001490116!
        Me.lbl区分.Width = 0.3937008!
        '
        'lbl名称
        '
        Me.lbl名称.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl名称.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl名称.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl名称.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl名称.Height = 0.3543307!
        Me.lbl名称.HyperLink = Nothing
        Me.lbl名称.Left = 2.283465!
        Me.lbl名称.Name = "lbl名称"
        Me.lbl名称.Style = "font-family: ＭＳ 明朝; font-size: 9.75pt; text-align: center; vertical-align: middle" &
    ""
        Me.lbl名称.Text = "得　意　先　／　工　事　名" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "品　　　名　／　備　　　考"
        Me.lbl名称.Top = 0.00000001490116!
        Me.lbl名称.Width = 2.952756!
        '
        'lbl数量
        '
        Me.lbl数量.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl数量.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl数量.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl数量.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl数量.Height = 0.3543307!
        Me.lbl数量.HyperLink = Nothing
        Me.lbl数量.Left = 5.23622!
        Me.lbl数量.Name = "lbl数量"
        Me.lbl数量.Style = "font-family: ＭＳ 明朝; font-size: 9.75pt; text-align: center; vertical-align: middle" &
    ""
        Me.lbl数量.Text = "数量"
        Me.lbl数量.Top = 0.00000001490116!
        Me.lbl数量.Width = 0.6692914!
        '
        'lbl単位区分
        '
        Me.lbl単位区分.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl単位区分.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl単位区分.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl単位区分.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl単位区分.Height = 0.3543307!
        Me.lbl単位区分.HyperLink = Nothing
        Me.lbl単位区分.Left = 5.905512!
        Me.lbl単位区分.Name = "lbl単位区分"
        Me.lbl単位区分.Style = "font-family: ＭＳ 明朝; font-size: 9.75pt; text-align: center; vertical-align: middle" &
    ""
        Me.lbl単位区分.Text = "単位"
        Me.lbl単位区分.Top = -8.881784E-16!
        Me.lbl単位区分.Width = 0.3937007!
        '
        'lbl単価
        '
        Me.lbl単価.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl単価.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl単価.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl単価.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl単価.Height = 0.3543307!
        Me.lbl単価.HyperLink = Nothing
        Me.lbl単価.Left = 6.299213!
        Me.lbl単価.Name = "lbl単価"
        Me.lbl単価.Style = "font-family: ＭＳ 明朝; font-size: 9.75pt; text-align: center; vertical-align: middle" &
    ""
        Me.lbl単価.Text = "単価"
        Me.lbl単価.Top = -8.881784E-16!
        Me.lbl単価.Width = 0.8661417!
        '
        'lbl基本料金
        '
        Me.lbl基本料金.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl基本料金.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl基本料金.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl基本料金.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl基本料金.Height = 0.3543307!
        Me.lbl基本料金.HyperLink = Nothing
        Me.lbl基本料金.Left = 7.165355!
        Me.lbl基本料金.Name = "lbl基本料金"
        Me.lbl基本料金.Style = "font-family: ＭＳ 明朝; font-size: 9.75pt; text-align: center; vertical-align: middle" &
    ""
        Me.lbl基本料金.Text = "基本料金"
        Me.lbl基本料金.Top = -1.058791E-22!
        Me.lbl基本料金.Width = 0.984252!
        '
        'lbl開始終了日付
        '
        Me.lbl開始終了日付.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl開始終了日付.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl開始終了日付.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl開始終了日付.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl開始終了日付.Height = 0.3543307!
        Me.lbl開始終了日付.HyperLink = Nothing
        Me.lbl開始終了日付.Left = 8.149607!
        Me.lbl開始終了日付.Name = "lbl開始終了日付"
        Me.lbl開始終了日付.Style = "font-family: ＭＳ 明朝; font-size: 9.75pt; text-align: center; vertical-align: middle" &
    ""
        Me.lbl開始終了日付.Text = "開始日" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "終了日"
        Me.lbl開始終了日付.Top = 1.893266E-29!
        Me.lbl開始終了日付.Width = 1.181102!
        '
        'lbl返納日付
        '
        Me.lbl返納日付.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl返納日付.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl返納日付.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl返納日付.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid
        Me.lbl返納日付.Height = 0.3543307!
        Me.lbl返納日付.HyperLink = Nothing
        Me.lbl返納日付.Left = 9.330712!
        Me.lbl返納日付.Name = "lbl返納日付"
        Me.lbl返納日付.Style = "font-family: ＭＳ 明朝; font-size: 9.75pt; text-align: center; vertical-align: middle" &
    ""
        Me.lbl返納日付.Text = "搬入伝票NO" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "返　納　日"
        Me.lbl返納日付.Top = 1.893266E-29!
        Me.lbl返納日付.Width = 1.181102!
        '
        'GroupFooter1
        '
        Me.GroupFooter1.Height = 0!
        Me.GroupFooter1.Name = "GroupFooter1"
        '
        'UriageDenpyoList
        '
        Me.MasterReport = False
        Me.PageSettings.DefaultPaperSize = False
        Me.PageSettings.Margins.Bottom = 0.2755905!
        Me.PageSettings.Margins.Left = 0.2755905!
        Me.PageSettings.Margins.Right = 0.2755905!
        Me.PageSettings.Margins.Top = 0.511811!
        Me.PageSettings.Orientation = GrapeCity.ActiveReports.Document.Section.PageOrientation.Landscape
        Me.PageSettings.PaperHeight = 11.69291!
        Me.PageSettings.PaperKind = System.Drawing.Printing.PaperKind.A4
        Me.PageSettings.PaperWidth = 8.267716!
        Me.PrintWidth = 10.88542!
        Me.Sections.Add(Me.GroupHeader1)
        Me.Sections.Add(Me.Detail)
        Me.Sections.Add(Me.GroupFooter1)
        Me.StyleSheet.Add(New DDCssLib.StyleSheetRule("font-style: normal; text-decoration: none; font-weight: normal; font-size: 10pt; " &
            "color: Black; font-family: ""MS UI Gothic""; ddo-char-set: 128", "Normal"))
        Me.StyleSheet.Add(New DDCssLib.StyleSheetRule("font-size: 16pt; font-weight: bold; font-family: ""MS UI Gothic""; ddo-char-set: 12" &
            "8", "Heading1", "Normal"))
        Me.StyleSheet.Add(New DDCssLib.StyleSheetRule("font-size: 14pt; font-weight: bold; font-style: inherit; font-family: ""MS UI Goth" &
            "ic""; ddo-char-set: 128", "Heading2", "Normal"))
        Me.StyleSheet.Add(New DDCssLib.StyleSheetRule("font-size: 13pt; font-weight: bold; ddo-char-set: 128", "Heading3", "Normal"))
        CType(Me.lblCompName, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lblDate, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lblTitle, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtJyoken, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtDT開始日付, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtDT終了日付, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtDT搬入伝票NO, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtDT返納日付, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt行NO, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtHD伝票NO, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtHD搬入日付, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtDT計算区分, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtDT商品名, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtDT備考明細, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtDT数量, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtDT単位区分, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtDT単価, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtDT基本料金, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtDT商品コード, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtHD伝票区分名, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtHD得意先コード, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtHD工事コード, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtHD得意先名, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtHD工事名, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtHD住所, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtHD摘要1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtHD摘要2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.hid行NO, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lbl伝票NO, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lbl行NO, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lbl搬入日付, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lbl区分, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lbl名称, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lbl数量, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lbl単位区分, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lbl単価, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lbl基本料金, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lbl開始終了日付, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.lbl返納日付, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me, System.ComponentModel.ISupportInitialize).EndInit()

    End Sub

    Private WithEvents GroupHeader1 As GrapeCity.ActiveReports.SectionReportModel.GroupHeader
    Private WithEvents GroupFooter1 As GrapeCity.ActiveReports.SectionReportModel.GroupFooter
    Private WithEvents lbl伝票NO As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents lbl行NO As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents lbl搬入日付 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents lbl区分 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents lbl名称 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents lbl数量 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents lbl単位区分 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents lbl単価 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents lbl基本料金 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents lbl開始終了日付 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents lbl返納日付 As GrapeCity.ActiveReports.SectionReportModel.Label
    Private WithEvents txtDT開始日付 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txtDT終了日付 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txtDT搬入伝票NO As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txtDT返納日付 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txt行NO As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txtHD伝票NO As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txtHD搬入日付 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txtDT計算区分 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txtDT商品名 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txtDT備考明細 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txtDT数量 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txtDT単位区分 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txtDT単価 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txtDT基本料金 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txtDT商品コード As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txtHD伝票区分名 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txtHD得意先コード As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txtHD工事コード As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txtHD得意先名 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txtHD工事名 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txtHD住所 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txtHD摘要1 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents txtHD摘要2 As GrapeCity.ActiveReports.SectionReportModel.TextBox
    Private WithEvents hid行NO As GrapeCity.ActiveReports.SectionReportModel.TextBox
End Class
