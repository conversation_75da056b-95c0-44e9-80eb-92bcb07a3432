﻿Imports GrapeCity.ActiveReports
Imports GrapeCity.ActiveReports.Document
Imports Dbs.Application.Logic.Database
Imports Dbs.Asphalt.Core.Common

Public Class UriageNohinshoSub
    Private int行NO As Integer = 0

    Private Sub UriageNohinsho_ReportStart(sender As Object, e As EventArgs) Handles MyBase.ReportStart

        Dim db As New DbReport.R納品書DataTable

        ' ｸﾞﾙｰﾌﾟﾍｯﾀﾞの設定
        Me.GHD伝票番号.DataField = db.売上番号Column.ColumnName
        Me.GHD伝票番号.NewPage = SectionReportModel.NewPage.None
        'Me.GHD伝票番号.NewPage = SectionReportModel.NewPage.None
        Me.GHD伝票番号.RepeatStyle = SectionReportModel.RepeatStyle.OnPageIncludeNoDetail  'OnPageIncludeNoDetail･･･改頁した新頁が合計行のみの印字でもﾍｯﾀﾞを印字
        Me.GHD伝票番号.KeepTogether = True     ' ﾍｯﾀﾞの途中で改頁されない様に

        ' ﾃﾞｰﾀﾌｨｰﾙﾄﾞの設定
        Me.txt商品名.DataField = db.商品名Column.ColumnName
        Me.txt会社FAX番号.DataField = db.会社FAX番号Column.ColumnName
        Me.txt会社住所.DataField = db.会社住所1Column.ColumnName
        Me.txt会社電話番号.DataField = db.会社電話番号Column.ColumnName
        Me.txt会社名.DataField = db.会社名Column.ColumnName
        Me.txt会社郵便番号.DataField = db.会社郵便番号Column.ColumnName
        Me.txt工事コード.DataField = db.工事コードColumn.ColumnName
        Me.txt工事名.DataField = db.工事名Column.ColumnName
        Me.txt工事住所.DataField = db.工事住所Column.ColumnName
        Me.txt数量.DataField = db.数量Column.ColumnName
        Me.txt単位区分名.DataField = db.単位区分名Column.ColumnName
        Me.txt摘要1.DataField = db.摘要1Column.ColumnName
        Me.txt摘要2.DataField = db.摘要2Column.ColumnName
        Me.txt得意先コード.DataField = db.得意先コードColumn.ColumnName
        Me.txt得意先名.DataField = db.得意先名Column.ColumnName
        Me.txt備考明細.DataField = db.備考明細Column.ColumnName

        Me.hid搬入日付.DataField = db.搬入日付Column.ColumnName
        Me.hid売上番号.DataField = db.売上番号Column.ColumnName
        Me.hid伝票区分.DataField = db.伝票区分Column.ColumnName
        Me.hid明細件数.DataField = db.明細件数Column.ColumnName

        ' 書式
        Me.txt数量.OutputFormat = "#,##0"

        ' 表示/非表示
        Me.txt行NO.Visible = False    ' ﾃｽﾄ用なので最後は非表示へ
    End Sub

    Private Sub GHD伝票番号_Format(sender As Object, e As EventArgs) Handles GHD伝票番号.Format
        Me.txt搬入年月日.Text = Format(Text.CDateEx(Me.hid搬入日付.Text), "yyyy年MM月dd日")

        Select Case True
            Case Me.hid伝票区分.Text = "1" : Me.txtタイトル.Text = "納品伝票"    ' 搬入
            Case Me.hid伝票区分.Text = "2" : Me.txtタイトル.Text = "納品伝票"    ' 売切
            Case Me.hid伝票区分.Text = "3" : Me.txtタイトル.Text = "返納伝票"    ' 返納
        End Select

        int行NO = 0
    End Sub

    Private Sub Detail_Format(sender As Object, e As EventArgs) Handles Detail.Format

        int行NO += 1
        Me.txt行NO.Text = int行NO

        If int行NO > Text.CVal(Me.hid明細件数.Text) Then
            Me.txt商品名.Text = ""
            Me.txt行NO.Text = ""
            Me.txt数量.Text = ""
            Me.txt単位区分名.Text = ""
            Me.txt備考明細.Text = ""
        End If
    End Sub
    Private Sub UriageNohinshoSub_FetchData(sender As Object, eArgs As FetchEventArgs) Handles Me.FetchData

        ' 何故かRepeatToFillでは上手くいかないのでFetchDataで行数を埋めてます
        If eArgs.EOF = True Then
            If int行NO <= 9 Then
                eArgs.EOF = False
            Else
                eArgs.EOF = True
            End If
        End If
    End Sub
End Class
