﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Public Class UriageNohinshoBase
    Inherits GrapeCity.ActiveReports.SectionReport

    'フォームがコンポーネントの一覧をクリーンアップするために dispose をオーバーライドします。
    Protected Overloads Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing Then
        End If
        MyBase.Dispose(disposing)
    End Sub
    Private WithEvents Detail As GrapeCity.ActiveReports.SectionReportModel.Detail
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Dim resources As System.Resources.ResourceManager = New System.Resources.ResourceManager(GetType(UriageNohinshoBase))
        Me.Detail = New GrapeCity.ActiveReports.SectionReportModel.Detail()
        Me.GHD売上番号 = New GrapeCity.ActiveReports.SectionReportModel.GroupHeader()
        Me.SubReport2 = New GrapeCity.ActiveReports.SectionReportModel.SubReport()
        Me.SubReport1 = New GrapeCity.ActiveReports.SectionReportModel.SubReport()
        Me.hid売上番号 = New GrapeCity.ActiveReports.SectionReportModel.TextBox()
        Me.GFT売上番号 = New GrapeCity.ActiveReports.SectionReportModel.GroupFooter()
        Me.PageHeader1 = New GrapeCity.ActiveReports.SectionReportModel.PageHeader()
        Me.PageFooter1 = New GrapeCity.ActiveReports.SectionReportModel.PageFooter()
        CType(Me.hid売上番号, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me, System.ComponentModel.ISupportInitialize).BeginInit()
        '
        'Detail
        '
        Me.Detail.Height = 0!
        Me.Detail.Name = "Detail"
        '
        'GHD売上番号
        '
        Me.GHD売上番号.Controls.AddRange(New GrapeCity.ActiveReports.SectionReportModel.ARControl() {Me.SubReport2, Me.SubReport1, Me.hid売上番号})
        Me.GHD売上番号.Height = 11.10236!
        Me.GHD売上番号.Name = "GHD売上番号"
        '
        'SubReport2
        '
        Me.SubReport2.CloseBorder = False
        Me.SubReport2.Height = 5.413386!
        Me.SubReport2.Left = 0!
        Me.SubReport2.Name = "SubReport2"
        Me.SubReport2.Report = Nothing
        Me.SubReport2.ReportName = "SubReport1"
        Me.SubReport2.Top = 5.688976!
        Me.SubReport2.Width = 7.480315!
        '
        'SubReport1
        '
        Me.SubReport1.CanGrow = False
        Me.SubReport1.CanShrink = False
        Me.SubReport1.CloseBorder = False
        Me.SubReport1.Height = 5.413386!
        Me.SubReport1.Left = 0.000001192093!
        Me.SubReport1.Name = "SubReport1"
        Me.SubReport1.Report = Nothing
        Me.SubReport1.ReportName = "SubReport1"
        Me.SubReport1.Top = 0!
        Me.SubReport1.Width = 7.480313!
        '
        'hid売上番号
        '
        Me.hid売上番号.CanGrow = False
        Me.hid売上番号.Height = 0.1181102!
        Me.hid売上番号.Left = 3.764567!
        Me.hid売上番号.Name = "hid売上番号"
        Me.hid売上番号.Padding = New GrapeCity.ActiveReports.PaddingEx(1, 1, 1, 0)
        Me.hid売上番号.ShrinkToFit = True
        Me.hid売上番号.Style = "background-color: Red; font-family: ＭＳ 明朝; font-size: 9.75pt; text-align: right; " &
    "vertical-align: middle; ddo-shrink-to-fit: true"
        Me.hid売上番号.Text = Nothing
        Me.hid売上番号.Top = 0.09645653!
        Me.hid売上番号.Visible = False
        Me.hid売上番号.Width = 0.1181102!
        '
        'GFT売上番号
        '
        Me.GFT売上番号.Height = 0.003937008!
        Me.GFT売上番号.Name = "GFT売上番号"
        '
        'PageHeader1
        '
        Me.PageHeader1.Height = 0!
        Me.PageHeader1.Name = "PageHeader1"
        '
        'PageFooter1
        '
        Me.PageFooter1.Height = 0!
        Me.PageFooter1.Name = "PageFooter1"
        '
        'UriageNohinshoBase
        '
        Me.MasterReport = False
        Me.PageSettings.DefaultPaperSize = False
        Me.PageSettings.Margins.Bottom = 0.2755905!
        Me.PageSettings.Margins.Left = 0.511811!
        Me.PageSettings.Margins.Right = 0.2755905!
        Me.PageSettings.Margins.Top = 0.2755905!
        Me.PageSettings.PaperHeight = 11.69291!
        Me.PageSettings.PaperKind = System.Drawing.Printing.PaperKind.A4
        Me.PageSettings.PaperWidth = 8.267716!
        Me.PrintWidth = 7.480315!
        Me.Sections.Add(Me.PageHeader1)
        Me.Sections.Add(Me.GHD売上番号)
        Me.Sections.Add(Me.Detail)
        Me.Sections.Add(Me.GFT売上番号)
        Me.Sections.Add(Me.PageFooter1)
        Me.StyleSheet.Add(New DDCssLib.StyleSheetRule("font-style: normal; text-decoration: none; font-weight: normal; font-size: 10pt; " &
            "color: Black; font-family: ""MS UI Gothic""; ddo-char-set: 128", "Normal"))
        Me.StyleSheet.Add(New DDCssLib.StyleSheetRule("font-size: 16pt; font-weight: bold; font-family: ""MS UI Gothic""; ddo-char-set: 12" &
            "8", "Heading1", "Normal"))
        Me.StyleSheet.Add(New DDCssLib.StyleSheetRule("font-size: 14pt; font-weight: bold; font-style: inherit; font-family: ""MS UI Goth" &
            "ic""; ddo-char-set: 128", "Heading2", "Normal"))
        Me.StyleSheet.Add(New DDCssLib.StyleSheetRule("font-size: 13pt; font-weight: bold; ddo-char-set: 128", "Heading3", "Normal"))
        CType(Me.hid売上番号, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me, System.ComponentModel.ISupportInitialize).EndInit()

    End Sub

    Private WithEvents GHD売上番号 As GrapeCity.ActiveReports.SectionReportModel.GroupHeader
    Private WithEvents GFT売上番号 As GrapeCity.ActiveReports.SectionReportModel.GroupFooter
    Private WithEvents PageHeader1 As GrapeCity.ActiveReports.SectionReportModel.PageHeader
    Private WithEvents PageFooter1 As GrapeCity.ActiveReports.SectionReportModel.PageFooter
    Private WithEvents SubReport2 As GrapeCity.ActiveReports.SectionReportModel.SubReport
    Private WithEvents SubReport1 As GrapeCity.ActiveReports.SectionReportModel.SubReport
    Private WithEvents hid売上番号 As GrapeCity.ActiveReports.SectionReportModel.TextBox
End Class
