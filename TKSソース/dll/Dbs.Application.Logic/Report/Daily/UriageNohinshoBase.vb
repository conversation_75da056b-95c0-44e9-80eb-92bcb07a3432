﻿Imports GrapeCity.ActiveReports
Imports GrapeCity.ActiveReports.Document
Imports Dbs.Application.Logic.Database

Public Class UriageNohinshoBase
    Public Sub_db As New DbReport.R納品書DataTable    ' ｻﾌﾞﾚﾎﾟｰﾄのDataSouceの為に設定する

    Private Sub UriageNohinshoBase_ReportStart(sender As Object, e As EventArgs) Handles Me.ReportStart

        Dim db As New DbReport.R納品書DataTable

        ' ｸﾞﾙｰﾌﾟﾍｯﾀﾞの設定
        Me.GHD売上番号.DataField = db.売上番号Column.ColumnName
        Me.GHD売上番号.NewPage = SectionReportModel.NewPage.None
        Me.GHD売上番号.RepeatStyle = SectionReportModel.RepeatStyle.OnPageIncludeNoDetail  'OnPageIncludeNoDetail･･･改頁した新頁が合計行のみの印字でもﾍｯﾀﾞを印字
        Me.GHD売上番号.KeepTogether = True     ' ﾍｯﾀﾞの途中で改頁されない様に

        ' ﾃﾞｰﾀﾌｨｰﾙﾄﾞの設定
        Me.hid売上番号.DataField = db.売上番号Column.ColumnName
    End Sub

    Private Sub GHD伝票番号_Format(sender As Object, e As EventArgs) Handles GHD売上番号.Format

        Dim ReportSub1 As New UriageNohinshoSub
        Dim ReportSub2 As New UriageNohinshoSub

        Dim db As New DbReport.R納品書DataTable

        Dim dr() As DataRow
        dr = Sub_db.Select(db.売上番号Column.ColumnName & " = '" & Me.hid売上番号.Text & "'")

        ReportSub1.DataSource = dr
        ReportSub1.txtタイトル控.Visible = False

        ReportSub2.DataSource = dr
        ReportSub2.txtタイトル控.Visible = True

        Me.SubReport1.Report = ReportSub1
        Me.SubReport1.CanGrow = False
        Me.SubReport1.ReportName = "UriageNohinshoSub1"

        Me.SubReport2.Report = ReportSub2
        Me.SubReport2.CanGrow = False
        Me.SubReport2.ReportName = "UriageNohinshoSub2"
    End Sub
End Class
