﻿body
{
    background-color: White;
}

.titlebarmenu
{
    display: none;
}

.mainframe
{
    margin-left:0;
    margin-right:0;
}

.main_place_outer
{
    margin-left:0;
    margin-right:0;
    margin-top: 15px;
}

h1
{
    font-size: 10pt;
    padding-top: 2px;
    padding-bottom: 4px;
    height: 16px;
}

.itemframe .inner
{
    width: auto;
}

.errorframe
{
  width: auto;
  text-align: left;
  padding: 2px;
}

.error_message_body
{
}

.messageframe
{
    width: auto;
    text-align: left;
    padding: 2px;
}

.message_body
{
}

.toolbar_body
{
    position: fixed;
    width: 100%;
    height: 35px;
    background-color: white;
    border-width: 0;
    border-bottom-width: 1px;
    text-align: left;
    margin-left: 0;
    margin-top: -10px;
    margin-right: 0px;
    padding: 5px;
    border-radius: 0px;
    -webkit-border-radius: 0px;
    -moz-border-radius: 0px;
}

.toolbar_body .toolbar_button
{
    width: 30px;
    height: 30px;
    float: left;
}

.toolbar_body .toolbar_button input
{
    width: 30px;
    height: 30px;
}

.toolbar_body .toolbar_buttonname
{
    display: none;
}

.main_place,
.main_place_side0
{
    position: relative;
    margin-left: 0px;
    margin-top: 26px;
}

.main_place
{
    width: auto;
}

.main_place_side0
{
    margin-top: -16px;
}

.itemframe
{
    border-radius: 0px;
    -webkit-border-radius: 0px;
    -moz-border-radius: 0px;
    border-width: 0;
    border-bottom-width: 2px;
    /*width: auto;*/
    width: auto;
    margin-bottom: 10px;
}

.itemframe .framename
{
    background-color: white;
    font-size: 11pt;
    font-weight: bold;
    color: #000000;
    padding: 0px;
    padding-bottom: 5px;
}

.itemframe .inner
{
    background-color: white;
    margin: 0;
    padding: 10px;
}

.itemframe_small
{
    border-radius: 3px;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border: #cccccc 1px solid;
    background-color: #f9f9f9;
    color: black;
    padding: 5px;
}

div.dbsitempanel div.dbspanelitem,
div.dbsitempanel div.dbspanelcontrol
{
    display: block;
    padding: 0px;
    width: 100%;
    color: #000000;
}

div.dbsitempanel div.dbspanelitem 
{
    border: 0;
    background-color: transparent;
    text-align: left;
    padding-left: 0px;
    padding-bottom: 0px;
    padding-top: 5px;
}

.uploader_file,
.uploader_camera
{
    display: block;
    float: left;
    background-color: #333333;
    border-radius: 100px;
    -webkit-border-radius: 100px;
    -moz-border-radius: 100px;
    border: 0;
    background-position: center;
    background-repeat: no-repeat;
    background-size: 20px;
    width: 0px;
    margin-right: 5px;
    padding: 20px;
}

.upload_area:after
{
    content: '';
}

.uploader_file
{
    background-image: url(images/uploader_file.png);
}

.uploader_camera
{
    background-image: url(images/uploader_camera.png);
}
