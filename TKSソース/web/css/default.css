body
{
    margin: 0px;
    padding: 0px;
    color: #000000;
    background-color: #f5f5f5;
    font-family: "Arial","BIZ UDPGothic","Meiryo","Hiragino Kaku Gothic ProN","Hiragino Sans",sans-serif;
    font-size: 10pt;
    line-height: 150%;
}

a
{
    color: #333333;
    text-decoration: underline;
}

a:hover
{
    color: #aa0000;
    text-decoration: none;
}

a:focus
{
	outline:none;					/* Chromeのフォーカス枠を消す */
}

.cursor_button
{
    width: 0px;
    position: absolute;
    height: 0px;
    display: none;
}

img
{
    border: 0px;
}

.userimageicon,
.userimageicon_thumnail
{
  border-radius: 100%;
  object-fit: cover;
}

.userimageicon_thumnail
{
    width: 30px;
}

input[type=image],
input[type=image]:focus
{
    background: 0;
    border: 0;
    box-shadow: none;
}

/* CheckBoxのスタイル変更 */
input[type="checkbox"]
{
    position: relative;
    width: 16px;
    height: 16px;
    border: 1px solid #aaaaaa;
    border-radius: 3px;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    vertical-align: middle;
}

input[type="checkbox"]:checked:before {
    position: absolute;
    top: 0px;
    left: 3px;
    transform: rotate(50deg);
    width: 4px;
    height: 8px;
    border-right: 3px solid #333333 !important;
    border-bottom: 4px solid #333333 !important;
    content: '';
}

/* Radioのスタイル変更 */
input[type="radio"]
{
    position: relative;
    width: 16px;
    height: 16px;
    border: 1px solid #aaaaaa;
    border-radius: 10px;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    vertical-align: middle;
}

input[type="radio"]:checked:before {
    position: absolute;
    top: 2px;
    left: 2px;
    width: 10px;
    height: 10px;
    border-radius: 10px;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    background-color: #333333;
    content: '';
}

span,
div
{
    /*font-family: 'ヒラギノ角ゴ Pro W3','Hiragino Kaku Gothic Pro','メイリオ',Meiryo,'ＭＳ Ｐゴシック',sans-serif;*/
}

.common_bar
{
    padding: 0;
    margin: 0;
    position: fixed;
    width: 100%;
    top: 0px;
    left: 0px;
    z-index: 5000;
    background-color: #f5f5f5;
}

div.toptoolbar
{
    width: 100%;
    text-align: right;
    margin-bottom: 5px;
    margin-left: auto;
    margin-right: auto;
    padding: 0px;
}

div.bottomtoolbar
{
    width: 100%;
    text-align: center;
    margin-left: auto;
    margin-right: auto;
}

div.inframetoptoolbar
{
    width: auto;
    text-align: right;
    margin-bottom: 5px;
}

div.inframetoolbar
{
    width: auto;
    text-align: center;
}

.guide,
.guider,
.guidebox
{
    font-size: 11px;
    font-weight: normal;
    color: #666666;
}

.guider
{
    color: #ff0000;
}

.guidebox
{
    background-color: steelblue;
    color: white;
    margin-left: 5px;
    margin-right: 5px;
    padding-left: 5px;
    padding-right: 5px;
}

input,
textarea,
select
{
    font-family: "ＭＳ ゴシック";
    font-size: 14px;
    border-radius: 2px;
    background-color: #ffffff;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    resize: vertical;
    /* box-shadow: 0 1px 0 #FFFFFF, 0 1px 1px rgba(0, 0, 0, 0.17) inset; */
    /*box-sizing: border-box;*/
}

input:focus,
textarea:focus,
select:focus
{
	outline:none;					/* Chromeのフォーカス枠を消す */
    color: #000000;
    background-color: #E0F0FF;
    box-shadow: 0 0 0 1px #0088DD;
}

input:disabled,
textarea:disabled,
select:disabled
{
    background-color: #e0e0e0 !important;
}

input[type="submit"]:disabled,
input[type="submit"]:disabled:hover
{
    background: #cccccc;
    cursor: not-allowed;
    border-color: #cccccc;
    color: #ffffff;
}

input[type="submit"],
input.button,
input.buttonw,
input.buttonb,
input.buttonr,
input.buttong,
input.button_small,
input.button_smallw,
input.button_smallb,
input.button_smallr,
input.button_smallg
{
    font-family: "Arial","BIZ UDPGothic","Meiryo","Hiragino Kaku Gothic ProN","Hiragino Sans",sans-serif;
    color: #ffffff;
    padding: 0px;
	border: 1px solid #333333;
    height: 30px;
    background: #333333;
    padding-left: 5px;
    padding-right: 5px;
    font-size: 13px;
    cursor: pointer;
    box-shadow: none;
    border-radius: 2px;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
}

input.buttonw
{
    color: #000000;
    background: #dddddd;
    border-color: #dddddd;
}

input.buttonb
{
    background: #4488dd;
    border-color: #4488dd;
}

input.buttonr
{
    background: #aa0000;
    border-color: #aa0000;
}

input.buttong
{
    background: #66bb22;
    border-color: #66bb22;
}

input.button_small,
input.button_smallw,
input.button_smallb,
input.button_smallr,
input.button_smallg
{
    height: 22px;
    padding: 0;
    padding-left: 4px;
    padding-right: 4px;
}

input.button_smallw
{
    color: #000000;
    background: #dddddd;
    border-color: #dddddd;
}

input.button_smallb
{
    background: #4488dd;
    border-color: #4488dd;
}

input.button_smallr
{
    background: #aa0000;
    border-color: #aa0000;
}

input.button_smallg
{
    background: #66bb22;
    border-color: #66bb22;
}

input.button_record
{
	font-size: 10px;
    padding-left: 3px;
    padding-right: 3px;
    height: 22px;
}

input[type="submit"]:hover,
input[type="submit"]:focus,
input.button:hover,
input.buttonw:hover,
input.buttonb:hover,
input.buttonr:hover,
input.buttong:hover,
input.button_small:hover,
input.button_smallw:hover,
input.button_smallb:hover,
input.button_smallr:hover,
input.button_smallg:hover,
input.button:focus,
input.buttonw:focus,
input.buttonb:focus,
input.buttonr:focus,
input.buttong:focus,
input.button_small:focus,
input.button_smallw:focus,
input.button_smallb:focus,
input.button_smallr:focus,
input.button_smallg:focus
{
    background-color: #E0F0FF;
    border-color: #0088DD;
	color: #000000;
}

input.header_button
{
    height: 20px;
    padding-left: 3px;
    padding-right: 3px;
    margin: 0;
    cursor: pointer;
    background: none;
    border: 0;
    color: #FFFFFF;
}

input.header_button:hover
{
    background: #FFFFFF;
    color: #4B711A;
}


input.line_button
{
    background-color: none;
    border-color: none;
	color: #000000;
    height: 22px;
    padding-left: 1px;
    padding-right: 1px;
    margin-top: 3px;
    cursor: pointer;
    background: none;
    border: 0;
    color: #333333;
}

input.line_button:hover
{
    background: #333333;
    color: #ffffff;
}

.delete_button
{
    background-color: none !important;
    border-color: none !important;
    cursor: pointer !important;
    font-size: 11px !important;
    font-weight: bold !important;
    text-decoration: none !important;
    color: #aa0000 !important;
    float: right !important;
    text-align: center !important;
    line-height: 100% !important;
}

.delete_button:hover
{
    background-color: none;
    border-color: none;
    text-decoration: underline;
}

.mainframe
{
    position: relative;
    padding-top: 100px;
    padding-left: 5px;
    margin-top: 5px;
}

.footerframe
{
    margin-top: 30px;
    padding: 10px;
    font-size: 11px;
    border-top: solid 5px #707070;
    color: #707070;
    text-align: center;
}

.loginform
{
    width: 400px;
    color: #ffffff;
    margin-top: -50px;
    margin-left: auto;
    margin-right: auto;
    background-image: url(images/logintitle.png);
    background-position: top center;
    background-repeat: no-repeat;
    background-color: #333333;
    padding-bottom: 20px;
    border: 1px solid #333333;
    box-shadow: 7px 7px 30px 0px #888888;
    -webkit-box-shadow: 7px 7px 30px 0px #888888;
    -moz-box-shadow: 7px 7px 30px 0px #888888;
}

.loginframe
{
    max-width: 300px;
    margin-left: auto;
    margin-right: auto;
    padding-top: 130px;
}

.logintextbox
{
    font-size: 18px;
    font-weight: bold;
    color: #333333;
    border: solid 2px #888888;
    margin-top: 3px;
    margin-bottom: 10px;
    padding: 3px;
    font-family: "Arial","BIZ UDPGothic","Meiryo","Hiragino Kaku Gothic ProN","Hiragino Sans",sans-serif;
    background-color: #ffffff;
    width: 100%;
    box-sizing: border-box;
}

.loginform a
{
    color: #ffffff;
}

.titlebar
{
  background-color: #333333;
  background-repeat: repeat-x;
  height: 45px;
  color: white;
  text-align: center;
  margin: 0px;
  padding: 0px;
}

.titlebarinner
{
  color: #cccccc;
  margin: 0px;
  padding: 0px;
  width: 100%;
  height: 45px;
}

.titlebarinner a.toplink
{
    background-image: url(images/commontitle.png);
    background-repeat: no-repeat;
    float: left;
    width: 300px;
    height: 45px;
}

.titlebarinner
{
  color: #cccccc;
  margin: 0px;
  padding: 0px;
  line-height: 110%;
}

.titlebarinner a
{
    color: #ffffff;
    font-size: 12px;
    font-weight: bold;
}

.titlebarmenu
{
    float: right;
    margin-top: 4px;
    text-align: right;
    color: White;
    font-size: 12px;
    padding-top: 3px;
    padding-right: 10px;
}

.username
{
    color: #FFCC00;
    font-weight: bold;
    font-size: 12px;
}

.itemframe
{
    border-radius: 5px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    box-sizing: border-box;
    width: 100%;
    border: #333333 1px solid;
    background-color: #f8f8f8;
    margin-bottom: 8px;
    /*box-shadow: 1px 1px 5px #888888;*/
}

.itemframe .framename
{
	background-color: #333333;
	font-size: 16px;
	font-weight: normal;
	color: White;
	padding: 5px;
	padding-left: 10px;
}

.itemframe .inner
{
    width: 930px;
    color: black;
    padding: 15px;
}

.itemframe_small
{
    border-radius: 3px;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border: #888888 1px solid;
    background-color: #eaeaea;
    color: black;
    padding: 15px;
}

.leftpane
{
    float: left;
}

.rightpane
{
    float: right;
}

.clear
{
    clear: both;
}

.margin
{
    margin: 5px;
}

.menubar
{
    color: #000000;
    background-color: #ebf4ed;
    border-bottom: 1px solid black;
    text-align: left;
}

.view_normal,
.view_error,
.view_warn,
.view_normal_numeric,
.view_error_numeric,
.view_warn_numeric
{
    color: Black;
    padding: 2px;
    margin-top: 0px;
    text-align: left;
    font-family: "Arial","BIZ UDPGothic","Meiryo","Hiragino Kaku Gothic ProN","Hiragino Sans",sans-serif;
}

.view_error
{
    color: white;
    background-color: tomato;
}

.view_warn
{
    color: white;
    background-color: gold;
}

.view_normal_numeric
{
    text-align: right;
}

.view_error_numeric
{
    color: white;
    background-color: tomato;
    text-align: right;
}

.view_warn_numeric
{
    color: white;
    background-color: gold;
    text-align: right;
}

.textbox
{
    border-color: #aaaaaa;
}

.dropdownbox
{
    border-color: #aaaaaa;
    white-space: nowrap;
}

.readonly
{
    background-color: #EEEEEE !important;
    border-color: #aaaaaa;
}

.need
{
    /* background-color: #FFCCDD; */
    border-color: #FF6666;
}

.input_normal,
.input_error,
.input_warn,
.input_normal_numeric,
.input_normal_imeon,
.input_warn_numeric,
.input_error_numeric,
.input_error_imeon,
.input_warn_imeon
{
  color: Black;
  /*background-color: #FFFFFF;*/
  ime-mode: inactive;
  border-width: 1px;
  border-style: solid;
  padding: 4px;
  margin: 0px;
  margin-bottom: 2px;
  margin-top: 0px;
}

.input_error
{
  color: white;
  background-color: tomato;
}

.input_warn
{
  background-color: gold;
}

.input_normal_numeric
{
  text-align: right;
}

.input_normal_imeon
{
  ime-mode: active;
}

.input_warn_numeric
{
  background-color: gold;
  text-align: right;
}

.input_error_numeric
{
  color: white;
  background-color: tomato;
  text-align: right;
}

.input_error_imeon
{
  color: white;
  background-color: tomato;
  ime-mode: active;
}

.input_warn_imeon
{
  background-color: gold;
  ime-mode: active;
}

select.input_normal,
select.input_error,
select.input_warn
{
    padding: 3px;
}

h1
{
    font-size: 17px;
    padding-top: 5px;
    padding-bottom: 5px;
    padding-left: 10px;
    font-weight: bold;
    margin: 0px;
    color: #ffffff;
    background-color: #886655;
    height: 18px;
}

h2
{
    font-size: 17px;
    font-weight: bold;
    margin: 0px;
    margin-top: 0px;
    margin-bottom: 15px;
    color: #888888;
}

h3
{
    font-size: 13px;
    font-weight: bold;
    margin: 0px;
    margin-bottom: 20px;
    margin-top: 0px;
    color: #000000;
    padding: 5px;
}

h4
{
    font-size: 12px;
    font-weight: bold;
    margin: 0px;
    margin-bottom: 10px;
    border-bottom: #000000 1px dotted;
    color: #666666;
    padding: 5px;
}

.boxframe
{
  padding-bottom: 10px;
}

.errorframe
{
    width: 100%;
    margin-left: 0px;
	padding-bottom: 5px;
	text-align: left;
}

.error_message_body
{
    font-weight: bold;
    font-size: 15px;
    color: #d35e5e;
    background-color: #feeeee;
    border: #d35e5e 1px solid;
    padding: 5px;
    padding-left: 10px;
    border-radius: 5px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
}

.error_message_body::before
{
    background-color: #d35e5e;
    color: #feeeee;
    content: '!';
    border-radius: 100%;
    -webkit-border-radius: 100%;
    -moz-border-radius: 100%;
    width: 22px;
    height: 22px;
    display: block;
    float: left;
    margin-right: 10px;
    text-align: center;
}

.error_message_body a
{
    color: #d35e5e;
    font-weight: bold;
    text-decoration: none;
}

.error_message_body a:hover
{
    color: #ffe0e0;
    background-color: #d35e5e;
    text-decoration: none;
}

.error_message_body span
{
    display: block;
    width: 900px;
}

.messageframe
{
    width: 100%;
    margin-left: 0px;
	padding-bottom: 5px;
	text-align: left;
}

.message_body
{
    font-weight: bold;
    font-size: 15px;
    color: #3b9ac4;
    background-color: #eaf9ff;
    border: #3b9ac4 1px solid;
    padding: 5px;
    padding-left: 10px;
    border-radius: 5px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
}

.message_body::before
{
    background-color: #3b9ac4;
    color: #eaf9ff;
    content: 'i';
    border-radius: 100%;
    -webkit-border-radius: 100%;
    -moz-border-radius: 100%;
    width: 22px;
    height: 22px;
    display: block;
    float: left;
    margin-right: 10px;
    text-align: center;
}

.message_body a
{
    color: #3b9ac4;
    font-weight: bold;
    text-decoration: none;
}

.message_body a:hover
{
    color: #E0F0FF;
    background-color: #3b9ac4;
    text-decoration: none;
}

.message_body span
{
    display: block;
    width: 900px;
}

.progress_body
{
    padding: 10px;
    border: 2px #333333 solid;
    background-color: #f2f2f2;
    margin: 10px;
    border-radius: 5px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
}

.progress_bar_frame
{
    width: 100%;
    border: 1px solid #333333;
    padding: 0px;
    margin-top: 10px;
    box-sizing: border-box;
}

.progress_bar
{
    background-color: #333333;
    height: 12px;
}

hr
{
    border: 0px;
    border-top: #cccccc 1px dotted;
    height: 1px;
}

table 
{
    border: 0;
    border-collapse: collapse;
    empty-cells: show;
}

td
{
    word-break: break-all;
    padding-top: 1px;
    padding-bottom: 1px;
}

.grid,
.grid2,
.commonfiner
{
	border: solid 2px #333333;
}

.grid td,
.grid th,
.commonfiner td,
.commonfiner th
{
  border: solid 1px #cccccc;
  padding: 2px;
  padding-left: 3px;
  padding-right: 3px;
}

.grid table td
{
  border: none;
}

th {
  /* 縦スクロール時に固定する */
  position: -webkit-sticky;
  position: sticky;
  top: 98px;
  /* tbody内のセルより手前に表示する */
  z-index: 1;
  background-color: #333333;
}

.commonfiner td,
.commonfiner th,
.commonfiner td
{
  padding-left: 4px;
  padding-right: 4px;
}

.dbstextbox td
{
  border: 0px;
  margin: 0px;
  padding: 0px;
}

.grid_header,
.grid_headerg
{
    background-color: #333333;
    text-align: center;
    border: solid 1px #cccccc;
    color: White;
    padding: 2px;
    font-weight: bold;
    font-size: 13px;
}

.grid_headerg
{
    background-color: #339900;
    font-size: 13px;
}

.grid_header a
{
    color: White;
}

.grid_row,
.grid_alterrow,
.grid_selectedrow,
.grid_group,
.grid_light
{
  background-color: #ffffff;
  border: #cccccc 1px solid;
  padding: 2px;
}

.grid_row:hover,
.grid_alterrow:hover,
.grid_selectedrow:hover,
.grid_group:hover
{
    background-color: #E0F0FF;
}

.grid_subtotal0
{
}

.grid_subtotal1
{
    background-color: #cef2ff;
}

.grid_subtotal2
{
    background-color: #baebfc;
}

.grid_subtotal3
{
    background-color: #aee6fa;
}

.grid_subtotal4
{
    background-color: #a5e7ff;
}

.grid_subtotal5
{
    background-color: #97e3ff;
}

.grid_alterrow
{
    /*background-color: #f5f5f5;*/
}

.grid_selectedrow
{
    background-color: #b6f396;
}

.grid_group
{
  border-top: solid 2px #333333;
}

.grid_light
{
    color: #bbbbbb;
}

.grid_item,
.grid_item_nowidth,
.grid_item_right
{
    font-weight: bold;
}

.grid_item
{
    width: 120px;
    padding-left: 10px;
    /*text-align: right;*/
}

.grid_item_right
{
    width: 115px;
    text-align: right;
    padding-right: 5px;
}

.strong_error
{
    background-color: tomato;
    color: #ffffff;
    font-size: 18px;
    font-weight: bold;
    padding: 8px;
    border: solid 2px red;
}

.updateframe
{
    margin-left: 5px;
    float: right;
    text-align: center;
    background-color: floralwhite;
    padding: 3px;
    font-size: 11px;
    font-weight: bold;
    border: #cc6633 2px solid;
}

.editmode
{
    border-radius: 3px;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    background-color: #dd2200;
    color: #ffffff;
    font-weight: bold;
    padding: 2px;
    padding-left: 8px;
    padding-right: 8px;
}

.pager
{
	max-width: 960px;
    padding-top: 3px;
    padding-bottom: 0px;
}

.datatitle
{
    font-size: 17px;
    font-weight: bold;
    border-bottom: #000000 1px dotted;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.box100per
{
    border: 1px gray solid;
    padding: 5px;
    width: 100%;
}

.schedule_table
{
	border: solid 2px #333333;
    vertical-align: top;
    width: 100%;
    background-color: #ffffff;
}

.schedule_table tr:hover
{
    background-color: #E0F0FF;
}

.schedule_cell_day,
.schedule_cell_normal,
.schedule_cell_data0,
.schedule_cell_data1,
.schedule_cell_data2,
.schedule_cell_data3,
.schedule_cell_data4,
.schedule_cell_data5,
.schedule_cell_data6,
.schedule_cell_data7,
.schedule_cell_data8,
.schedule_cell_holiday1,
.schedule_cell_holiday2,
.schedule_cell_today,
.schedule_cell_today_data,
.schedule_cell_data_return
{
    border: 1px solid #cccccc;
    vertical-align: top;
    padding: 1px;
    overflow:hidden;
    text-align: left;
}

.schedule_cell_day,
.schedule_cell_day a,
.schedule_cell_holiday1,
.schedule_cell_holiday1 a,
.schedule_cell_holiday2,
.schedule_cell_holiday2 a,
.schedule_cell_today,
.schedule_cell_today a
{
    background-color: #333333;
    font-size: 14px;
    font-weight: bold;
    text-align: center;
    color: #ffffff;
}

.schedule_cell_holiday1,
.schedule_cell_holiday1 a
{
    background-color: #009933;
}

.schedule_cell_holiday2,
.schedule_cell_holiday2 a
{
    background-color: #ff0066;
}

.schedule_cell_today,
.schedule_cell_today a
{
    background-color: #0066ff;
}

.schedule_cell_today_data,
.schedule_cell_today_data a
{
    background-color: #eefeff;
}

.schedule_cell_normal
{
}

.schedule_cell_data0,
.schedule_cell_data0 a
{
    background-color: Transparent !important;
    color: #000000;
}

.schedule_cell_data1,
.schedule_cell_data1 a
{
    background-color: #ccccff !important;
    color: #000000;
}

.schedule_cell_data2,
.schedule_cell_data2 a
{
    background-color: #ffcccc !important;
    color: #000000;
}

.schedule_cell_data3,
.schedule_cell_data3 a
{
    background-color: #ffccff !important;
    color: #000000;
}

.schedule_cell_data4,
.schedule_cell_data4 a
{
    background-color: #ccff00 !important;
    color: #000000;
}

.schedule_cell_data5,
.schedule_cell_data5 a
{
    background-color: #ccffff !important;
    color: #000000;
}

.schedule_cell_data6,
.schedule_cell_data6 a
{
    background-color: #ffffcc !important;
    color: #000000;
}

.schedule_cell_data7,
.schedule_cell_data7 a
{
    background-color: #aaaaaa !important;
    color: #000000;
}

.schedule_cell_data_return,
.schedule_cell_data_return a
{
    background-color: #ff0066;
    color: #ffffff;
}

.schedule_subject
{
    display: block;
    float: left;
    white-space: pre-wrap;
    word-wrap: break-all;
/*
    white-space:nowrap;
    overflow:hidden;
    width: 50px;
*/
}

.schedule_subject_daily
{
    display: block;
    position: absolute;
    white-space:nowrap;
/*
    overflow:hidden;
    width: 50px;
*/
}

.schedule_subject a,
.schedule_subject_daily a
{
    background-color: Transparent;
    font-weight: bold;
}

.schedule_subject_daily a
{
    white-space:nowrap;
}

.schedule_time
{
    display: block;
    float: right;
    text-align: right;
    white-space: nowrap;
}

.schedule_cell_name
{
    float: left;
    display: block;
    font-size: 11px;
    color: #aaaaaa;
}

.schedule_editbutton
{
    text-align: right;
    display: block;
    font-size: 16px;
    font-weight: bold;
}

div
{
    padding: 0px;
    margin: 0px;
}

.notice_message,
.notice_message_mini,
.message_mini
{
    display: block;
    color: #d35e5e;
    padding: 10px;
    text-align: left;
    font-weight: bold;
    margin-top: 5px;
    margin-bottom: 5px;
    border: #d35e5e 1px solid;
    background-color: #feeeee;
    word-break: break-all;
}

.notice_message_mini
{
    padding: 1px;
}

.message_mini
{
    color: #66aadd;
    border-color: #66aadd;
    background-color: #eeeefe;
}

.toolbar_button,
.toolbar_button:hover,
.toolbar_button:focus
{
    background: #fafafa !important; /* Old browsers */
    background: -moz-linear-gradient(top,  #fafafa 0%, #e0e0e0 100%) !important; /* FF3.6-15 */
    background: -webkit-linear-gradient(top,  #fafafa 0%,#e0e0e0 100%) !important; /* Chrome10-25,Safari5.1-6 */
    background: linear-gradient(to bottom,  #fafafa 0%,#e0e0e0 100%) !important; /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#fafafa', endColorstr='#e0e0e0',GradientType=0 ); /* IE6-9 */
    border-radius: 3px;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    box-shadow: 1px 1px 3px #888888 !important;
    margin: 5px;
    padding: 0px;
    width: 60px;
    height: 50px;
}

.toolbar_button:hover
{
    background: #E0F0FF !important;
    box-shadow: 0 0 0 1px #0088DD !important;
}

.toolbar_buttonname
{
}

.toolbar_body
{
    float: left;
    position: fixed;
    border-radius: 3px;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border: #333333 1px solid;
    background-color: #ffffff;
    width: 70px;
    text-align: center;
    font-size: 11px;
    padding: 5px;
    line-height: 100%;
    margin-right: 10px;
    z-index: 4999;
}

.toolbar_button input
{
    width: 60px;
    height: 50px;
}

.toolbar_button .btn_add,
.toolbar_button .btn_add:focus
{
    background-image: url(images/toolbutton/btn_add.png);
    background-position: center center;
    background-repeat: no-repeat;
}

.toolbar_button .btn_back,
.toolbar_button .btn_back:focus
{
    background-image: url(images/toolbutton/btn_back.png);
    background-position: center center;
    background-repeat: no-repeat;
}

.toolbar_button .btn_del,
.toolbar_button .btn_del:focus
{
    background-image: url(images/toolbutton/btn_del.png);
    background-position: center center;
    background-repeat: no-repeat;
}

.toolbar_button .btn_excel,
.toolbar_button .btn_excel:focus
{
    background-image: url(images/toolbutton/btn_excel.png);
    background-position: center center;
    background-repeat: no-repeat;
}

.toolbar_button .btn_csv,
.toolbar_button .btn_csv:focus
{
    background-image: url(images/toolbutton/btn_csv.png);
    background-position: center center;
    background-repeat: no-repeat;
}

.toolbar_button .btn_finder,
.toolbar_button .btn_finder:focus
{
    background-image: url(images/toolbutton/btn_finder.png);
    background-position: center center;
    background-repeat: no-repeat;
}

.toolbar_button .btn_new,
.toolbar_button .btn_new:focus
{
    background-image: url(images/toolbutton/btn_new.png);
    background-position: center center;
    background-repeat: no-repeat;
}

.toolbar_button .btn_ok,
.toolbar_button .btn_ok:focus
{
    background-image: url(images/toolbutton/btn_ok.png);
    background-position: center center;
    background-repeat: no-repeat;
}

.toolbar_button .btn_print,
.toolbar_button .btn_print:focus
{
    background-image: url(images/toolbutton/btn_print.png);
    background-position: center center;
    background-repeat: no-repeat;
}

.toolbar_button .btn_rem,
.toolbar_button .btn_rem:focus
{
    background-image: url(images/toolbutton/btn_rem.png);
    background-position: center center;
    background-repeat: no-repeat;
}

.toolbar_button .btn_save,
.toolbar_button .btn_save:focus
{
    background-image: url(images/toolbutton/btn_save.png);
    background-position: center center;
    background-repeat: no-repeat;
}

.toolbar_button .btn_mail,
.toolbar_button .btn_mail:focus
{
    background-image: url(images/toolbutton/btn_mail.png);
    background-position: center center;
    background-repeat: no-repeat;
}

.toolbar_button .btn_config,
.toolbar_button .btn_config:focus
{
    background-image: url(images/toolbutton/btn_config.png);
    background-position: center center;
    background-repeat: no-repeat;
}

.toolbar_button .btn_paste,
.toolbar_button .btn_paste:focus
{
    background-image: url(images/toolbutton/btn_paste.png);
    background-position: center center;
    background-repeat: no-repeat;
}

.toolbar_button .btn_close,
.toolbar_button .btn_close:focus
{
    background-image: url(images/toolbutton/btn_close.png);
    background-position: center center;
    background-repeat: no-repeat;
}

.toolbar_button .btn_upload,
.toolbar_button .btn_upload:focus
{
    background-image: url(images/toolbutton/btn_upload.png);
    background-position: center center;
    background-repeat: no-repeat;
}

.toolbar_button .btn_check,
.toolbar_button .btn_check:focus
{
    background-image: url(images/toolbutton/btn_check.png);
    background-position: center center;
    background-repeat: no-repeat;
}

.main_place_outer,
.main_place_outer_side0
{
    float: left;
    position: relative;
    margin-left: 87px;
}

.main_place_outer_side0
{
    margin-left: 0px;
}

.main_place 
{
    text-align: left;
    /*width: 970px;*/
}

.messagebox_background
{
    background: #000000;
    position: fixed;
    left: 0px;
    top: 0px;
    width: 100%;
    height: 100%;
    z-index: 9998;
    filter: alpha(opacity=40);
    -moz-opacity:0.40;
    opacity:0.40;
}

.messagebox_frame
{
    position: fixed;
    top: 200px;
    width: 100%;
    z-index: 9999;
}

.messagebox_frame_inner
{
    margin-left: auto;
    margin-right: auto;
    max-width: 500px;
    padding: 20px;
    background-color: #ffffff;
    z-index: 9999;
    color: #000000;
    border-radius: 5px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    box-shadow: 7px 7px 30px 0px #444444;
    -webkit-box-shadow: 7px 7px 30px 0px #444444;
    -moz-box-shadow: 7px 7px 30px 0px #444444;
}

.dialogbox_background
{
    background: #000000;
    position: fixed;
    left: 0px;
    top: 0px;
    width: 100%;
    height: 100%;
    z-index: 9998;
    filter: alpha(opacity=40);
    -moz-opacity:0.40;
    opacity:0.40;
}

.dialogbox_frame
{
    position: fixed;
    top: 120px;
    width: 100%;
    z-index: 9999;
}

.dialogbox_frame_inner
{
    margin-left: 50px;
    max-width: 600px;
    padding: 20px;
    /*border: 1px solid #333333;*/
    background-color: #ffffff;
    z-index: 9999;
    /*text-align: center;*/
    border-radius: 5px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    box-shadow: 7px 7px 30px 0px #444444;
    -webkit-box-shadow: 7px 7px 30px 0px #444444;
    -moz-box-shadow: 7px 7px 30px 0px #444444;
}

.popup_frame
{
    padding: 5px;
    border: 1px solid #000000;
    background-color: #ffffff;
    text-align: center;
}

.newpage_cell
{
    border-bottom: 2px solid red;
}

.scrollframe
{
    padding: 5px;
    overflow: auto;
}

.progress_message
{
    top: 200px;
    position: fixed;
    width: 100%;
    z-index: 15000;
    display: none;
}

.progress_background,
.progress_background_wait
{
    background: #000000;
    position: fixed;
    left: 0px;
    top: 0px;
    width: 100%;
    height: 100%;
    z-index: 15000;
    filter: alpha(opacity=0);
    -moz-opacity:0.00;
    opacity:0.00;
    cursor: wait;
}

.progress_background_wait
{
    filter: alpha(opacity=30);
    -moz-opacity:0.30;
    opacity:0.30;
}

.progress_message div.progress_message_innner
{
    position: relative;
    margin-left: auto;
    margin-right: auto;
    max-width: 200px;
    /*border: 1px solid #333333;*/
    background-color: #ffffff;
    background-image: url(images/loading.gif);
    background-position: left 10px center;
    background-repeat: no-repeat;
    text-align: center;
    font-size: 16px;
    padding: 20px;
    z-index: 15001;
    border-radius: 5px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    box-shadow: 7px 7px 30px 0px #444444;
    -webkit-box-shadow: 7px 7px 30px 0px #444444;
    -moz-box-shadow: 7px 7px 30px 0px #444444;
}

.progress_message_innner::after
{
    content: '処理中...';
}

div.dbsitempanel
{
    margin-bottom: 0px;
}

div.dbsitempanel div.dbspanelitem,
div.dbsitempanel div.dbspanelitem_disabled,
div.dbsitempanel div.dbspanelcontrol
{
    display: table-cell;
    vertical-align: middle;
    padding: 0px;
    border-left: 0;
}

div.dbsitempanel div.dbspanelitem,
div.dbsitempanel div.dbspanelitem_disabled
{
    border: 0;
}

div.dbsitempanel input,
div.dbsitempanel select,
div.dbsitempanel textarea,
div.dbsitempanel img
{
    margin-top: 0px;
}

div.dbsitempanel div.dbspanelitem,
div.dbsitempanel div.dbspanelitem_disabled
{
    /*width: 100px;*/
    color: #000000;
    text-align: right;
    font-size: 13px;
    font-weight: bold;
    vertical-align: top;
    padding-top: 2px;
    padding-right: 10px;
    white-space: nowrap;
    /*background-color: #333333;*/
}

div.dbsitempanel div.dbspanelitem_disabled
{
    color: #aaaaaa;
}

/* IE 6 */
* html div.dbsitempanel div.dbspanelitem,
* html div.dbsitempanel div.dbspanelcontrol
{
    display: inline;
    zoom: 1;
}
 
/* IE 7 */
*:first-child+html div.dbsitempanel div.dbspanelitem,
*:first-child+html div.dbsitempanel div.dbspanelcontrol
{
    display: inline;
    zoom: 1;
}
 
* html div.dbsitempanel div.dbspanelitem,
* html div.dbsitempanel div.dbspanelcontrol
{
    display: inline-block;
}

.completion_list
{
	z-index: 15002 !important;
    text-align: left;
    position: relative;
    margin-top: 0;
    padding: 0;
	display: inline-block;
	list-style: none;
	background-color: #f5fff5;
	border: 2px solid #555555;
	max-height: 200px;
	overflow: auto;
    font-family: "ＭＳ ゴシック";
    font-size: 14px;
}

.completion_list_item
{
	background-color: #fffff5;
    cursor: pointer;
    padding-left: 4px;
    padding-right: 4px;
}

.completion_list_select
{
	background-color: #333333;
	color: #ffffff;
    cursor: pointer;
    padding-left: 4px;
    padding-right: 4px;
}

.sort_up
{
    background-image: url(images/sort_up.png);
    background-position: right center;
    background-repeat: no-repeat;
}

.sort_down
{
    background-image: url(images/sort_down.png);
    background-position: right center;
    background-repeat: no-repeat;
}

table.excel
{
    /*
    font-family: 'ＭＳ Ｐゴシック';
    font-size: 9pt;
    */
}

.excel td
{
    background-color: #ffffff;
    color: #000000;
    border-color: #000000;
    border: 0;
    padding: 0;
}

.forum_open_button
{
	border: 1px solid #cccccc;
    background-color: #eaeaea;
    padding: 10px;
}

.forum_open_button a
{
    font-size: 16px;
    color: #000000;
}

.togglebutton_on,
.togglebutton_off,
.togglebutton_disable
{
	border: 0 !important;
	padding: 0px !important;
	margin-left: 1px !important;
	margin-right: 1px !important;
	color: #000000 !important;
    cursor: pointer !important;
    width: 20px !important;
    height: 20px !important;
    border-radius: 2px !important;
    -webkit-border-radius: 2px !important;
    -moz-border-radius: 2px !important;
}

.togglebutton_on
{
	background-color: #66cc00 !important;
    box-shadow: 0px 0px 7px #33cc00 !important;
}

.togglebutton_off
{
	background-color: #aaaaaa !important;
	color: white;
}

.togglebutton_disable
{
	background-color: #dddddd !important;
	border: 0;
	color: #dddddd !important;
    cursor: default !important;
}

.fixed_frame
{
	position: fixed;
	background-color: #f5f5f5;
}

.relative_frame
{
	position: static;
	background-color: #f5f5f5;
	padding-top: 160px;
}

table.auth_module_table
{
    border-collapse: collapse;
    border: solid 1px #aaaaaa;
    table-layout: fixed;
    padding-top: 100px;
}

table.auth_module_table td
{
	/*width: 80px;*/
    border: solid 1px #aaaaaa;
}

table.auth_module_table tr:hover
{
    background-color: #FEDEA0;
}

.uploadfile_frame
{
    width: 179px;
    height: 180px;
    text-align: center;
    float: left;
    margin-right: 5px;
    margin-bottom: 5px;
    border: 1px solid #cccccc;
}

.uploadfile_frame .button_smallr
{
    display: none;
    position: relative;
    top: -183px;
    left: 80px;
    border-radius: 0px;
    -webkit-border-radius: 0px;
    -moz-border-radius: 0px;
}

.uploadfile
{
    width: 170px;
    height: 170px;
    text-align: center;
    cursor: pointer;
    overflow: hidden;
    padding: 5px;
    margin-bottom: 2px;
}

.uploadfile .filename
{
    position: absolute;
    width: 170px;
    height: 170px;
    display: flex;
    flex-direction: row;
    align-items: flex-end;
    overflow: hidden;
    white-space: nowrap;
    font-size: 11px;
}

.uploadfile_frame:hover
{
    background: #E0F0FF;
    border-color: #0088DD;
}

.uploadfile_frame:hover > .button_smallr
{
    display: inline;
}

.jumptopbutton,
.jumptopbutton:focus
{
    outline: none !important;
    margin: 0 !important;
    padding: 0 !important;
    border: 0 !important;
    width: 0px !important;
    height: 0px !important;
    box-shadow: none !important;
    background-color: none !important;
}

.checkboxframe
{
    overflow-y: scroll;
    border-left: 0;
    border-right: 0;
    border-bottom: 0;
    resize: vertical;
}

.textboxbutton
{
    vertical-align: middle;
    width: 24px;
    height: 24px;
}

.textboxbutton input
{
    width: 24px;
    height: 24px;
}

.textboxbutton.editor,
.textboxbutton.editor:focus
{
    background-image: url(images/buttoninfo.png);
    background-position: center center;
    background-repeat: no-repeat;
}

.textboxbutton.finder,
.textboxbutton.finder:focus
{
    background-image: url(images/buttonfind.png);
    background-position: center center;
    background-repeat: no-repeat;
}

.textboxbutton.calendar,
.textboxbutton.calendar:focus
{
    background-image: url(images/buttoncal.png);
    background-position: center center;
    background-repeat: no-repeat;
}

.password_reset_form
{
    margin-left: auto;
    margin-right: auto;
    width: 800px;
}

.module_color_Master
{
    background-color: #E0F0FF;
}

.module_color_System
{
    background-color: #dddddd;
}

.upload_area,
.upload_area_dragover
{
    padding:10px;
    text-align : center;
    font-size: 12pt;
    border: 2px dashed #888888;
    color: #555555;
    cursor: pointer;
    display: block;
}

.upload_area:after
{
    content: "ここにファイルをドロップするかクリックしてファイル選択";
}

.upload_area_dragover
{
    background-color: #E0F0FF;
    border-color: #0088DD;
    color: #0088DD;
    font-weight: bold;
}

.upload_area_dragover:after
{
    content: "ファイルをドロップしてアップロード";
}

.uploader_camera
{
    display: none;
}

.approval_type0,            /* 未承認 */
.approval_type1,            /* 承認済 */
.approval_type2             /* 非承認 */
{
    font-size: 11pt;
    font-weight: bold;
    padding: 4px;
    border-radius: 3px;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
}

.approval_type0
{
    background-color: none;
    border: 1px solid #888888;
    color: #888888;
}

.approval_type1
{
    background-color: none;
    border: 1px solid #009900;
    color: #009900;
}

.approval_type2
{
    background-color: none;
    border: 1px solid #FF3300;
    color: #FF3300;
}
