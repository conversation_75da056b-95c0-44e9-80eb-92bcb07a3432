/*
ThemeName:グリーン
*/

body
{
    background-color: #fcfffa;
}

a
{
    color: #85ac5f;
}

.titlebar
{
  background-color: #85ac5f;
}

.itemframe
{
    border: #85ac5f 2px solid;
    background-color: #e9f6e5;
}

.itemframe .framename
{
	background-color: #85ac5f;
}

.itemframe_small
{
    background-color: #d7ebd1;
}

h1
{
	background-color: #85ac5f;
}

.toolbar_body
{
    border-color: #85ac5f;
}

.grid_header,
.grid_headerg
{
    background-color: #85ac5f;
}

.grid,
.grid2,
.commonfiner
{
	border: solid 2px #85ac5f;
}

.grid_group
{
  border-top: solid 2px #85ac5f;
}

th {
  background-color: #85ac5f;
}

input.button,
input.button_small,
input.button_record
{
    background: #85ac5f;
	border: 1px solid #85ac5f;
}

div.dbsitempanel div.dbspanelitem,
div.dbsitempanel div.dbspanelitem_disabled
{
    color: #85ac5f;
}

h4
{
    color: #85ac5f;
    border-bottom: #85ac5f 1px solid;
}

.toolbar_button_image,
.textboxbutton
{
    filter: invert(8%) sepia(99%) saturate(7044%) hue-rotate(150deg) brightness(100%) contrast(50%);
}

input[type="checkbox"]:checked:before {
    border-color: #85ac5f;
}

input[type="radio"]:checked:before {
    background-color: #85ac5f;
}
