/*
ThemeName:ダーク
*/

body
{
    background-color: #000000;
}

a
{
    color: #aaaaaa;
}

.itemframe
{
    border: #777777 2px solid;
    background-color: #222222;
}

.itemframe .inner
{
	background-color: #222222;
  color: #ffffff;
}

.itemframe .framename
{
	background-color: #222222;
  color: #ffffff;
}

.itemframe_small
{
  background-color: #333333;
  color: #ffffff;
}

h1
{
	background-color: #777777;
}

.grid_header,
.grid_headerg
{
    background-color: #cccccc;
}

.grid,
.grid2,
.commonfiner
{
	border: solid 2px #777777;
}

.grid_group
{
  border-top: solid 2px #777777;
}

.grid td,
.grid2 td,
.commonfiner td
{
  color: #ffffff;
  background-color: #222222;
}

th {
  background-color: #777777;
}

.toolbar_body
{
  background-color: #333333;
  border-color: #cccccc;
  color: #ffffff;
}

input.button,
input.button_small,
input.button_record
{
    background: #777777;
	border: 1px solid #777777;
}

div.dbsitempanel div.dbspanelitem,
div.dbsitempanel div.dbspanelitem_disabled
{
    color: #cccccc;
}

h4
{
    color: #cccccc;
    border-bottom: #cccccc 1px solid;
}

.view_normal, .view_error, .view_warn, .view_normal_numeric, .view_error_numeric, .view_warn_numeric
{
    color: #ffffff;
}

.guide
{
    color: #eeeeee;
}

.pager
{
    color: #eeeeee;
}

div.dbsdropdownmenu
{
    background-color: #333333;
}

.forum_open_button
{
    background-color: #333333;
}

.input_normal, .input_error, .input_warn, .input_normal_numeric, .input_normal_imeon, .input_warn_numeric, .input_error_numeric, .input_error_imeon, .input_warn_imeon
{
    background-color: #222222;
    color: #ffffff;
}

input:focus,
textarea:focus,
select:focus {
    color: #ffffff;
    background-color: #222222;
    outline: solid 1px #DDDDDD;
}

.readonly
{
    background-color: #444444 !important;
}

.toolbar_button_image,
.textboxbutton
{
    filter: invert(180%) sepia(120%) saturate(144%) hue-rotate(500deg) brightness(1000%) contrast(50%);
}

.toolbar_button
{
    background: #333333 !important; /* Old browsers */
    box-shadow: none !important;
    border: 1px solid #cccccc;
}

input[type="checkbox"]:checked:before {
    border-color: #333333;
}

input[type="radio"]:checked:before {
    background-color: #333333;
}

.completion_list,
.completion_list_item
{
  background-color: #000000;
}

.MyCalendar .ajax__calendar_container
{
  background-color: #000000;
  color: #ffffff;
}

input:disabled,
textarea:disabled,
select:disabled
{
  background-color: #888888 !important;
}

.dialogbox_frame_inner,
.progress_message div.progress_message_innner,
.messagebox_frame_inner
{
  background-color: #111111;
  border: 1px solid #666666;
  color: #ffffff;
}
