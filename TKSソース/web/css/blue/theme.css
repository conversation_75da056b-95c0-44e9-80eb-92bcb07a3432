/*
ThemeName:ブルー
*/

body
{
    background-color: #fcfffa;
}

.titlebar
{
  background-color: #5f7bac;
}

.itemframe
{
    border: #5f7bac 2px solid;
    background-color: #e5eef6;
}

.itemframe .framename
{
	background-color: #5f7bac;
}

.itemframe_small
{
    background-color: #cfddea;
}

h1
{
	background-color: #5f7bac;
}

.grid_header,
.grid_headerg
{
    background-color: #5f7bac;
}

.grid,
.grid2,
.commonfiner
{
	border: solid 2px #5f7bac;
}

.grid_group
{
  border-top: solid 2px #5f7bac;
}

th {
  background-color: #5f7bac;
}

input.button,
input.button_small,
input.button_record
{
    background: #5f7bac;
	border: 1px solid #5f7bac;
}

div.dbsitempanel div.dbspanelitem,
div.dbsitempanel div.dbspanelitem_disabled
{
    color: #5f7bac;
}

h4
{
    color: #5f7bac;
    border-bottom: #5f7bac 1px solid;
}

.toolbar_button_image,
.textboxbutton
{
    filter: invert(8%) sepia(99%) saturate(7044%) hue-rotate(200deg) brightness(100%) contrast(145%);
}

input[type="checkbox"]:checked:before {
    border-color: #5f7bac;
}

input[type="radio"]:checked:before {
    background-color: #5f7bac;
}
