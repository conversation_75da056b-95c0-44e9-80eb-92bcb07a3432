﻿/* ドロップダウンメニューストリップスタイル */

div.dbsdropdownmenu
{
	background: #f0f0d0;
	padding: 1px;
	margin: 0px;
}

ul.dbsdropdownmenu {
	position: relative;
	display: inline-block;
	list-style: none;
	padding: 0px;
	margin: 0px;
	z-index: 6997;
}

ul.dbsdropdownmenu li
{
	position: relative;
	display: inline-block;
	white-space: nowrap;
	float: left;
	padding: 0px;
	margin: 0px;
    text-decoration: none;
	z-index: 6998;
}

ul.dbsdropdownmenu a.nochild,
ul.dbsdropdownmenu a.havechild,
ul.dbsdropdownmenu a.disabled
{
	position: relative;
	display: inline-block;
	text-decoration: none;
	margin: 0px;
	padding: 2px;
	padding-left: 10px;
	padding-right: 10px;
	/*color: #333333;*/
    cursor: pointer;
	z-index: 6999;
}

ul.dbsdropdownmenu a.havechild
{
    background-image: url("images/menu_child.gif");
    background-repeat: no-repeat;
    background-position: right;
	z-index: 6998;
}

ul.dbsdropdownmenu a.disabled
{
    color: #aaaaaa;
}

ul.dbsdropdownmenu div.separator
{
	background-color: #8694A4;
	height: 1px;
}

ul.dbsdropdownmenu ul {
	background: #333333;
	border: 1px solid #333333;
	position: relative;
	display: none;
	list-style: none;
	margin: 0px;
	padding: 0px;
	z-index: 7000;
}

ul.dbsdropdownmenu > li {
	text-align: left;
	color: #ffffff;
}

ul.dbsdropdownmenu > li li {
	clear: left;
	color: #ffffff;
}

ul.dbsdropdownmenu > li li a {
	text-align: left;
	color: #ffffff;
}

ul.dbsdropdownmenu > li li a:hover {
	text-align: left;
    color: #000000;
    background: #f0f0d0;
}

ul.dbsdropdownmenu li:hover > a
{
    color: #ffffff;
    background: #333333;
    text-align: left;
}

ul.dbsdropdownmenu li:hover > ul {
	display: inline-block;
	position: absolute;
	top: 100%;
	left: 0px;
}

ul.dbsdropdownmenu li a:hover {
	color: #ffffff;
	background: #333333;
}

ul.dbsdropdownmenu li li:hover ul {
	top: 0px;
	left: 100%;
}
