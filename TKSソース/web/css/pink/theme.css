/*
ThemeName:ピンク
*/

body
{
    background-color: #FFFAFA;
}

a
{
    color: #ff006e;
}

.titlebar
{
  background-color: #ff006e;
}

.itemframe
{
    border: #ff006e 2px solid;
    background-color: #fee8e8;
}

.itemframe .framename
{
	background-color: #ff006e;
}

.itemframe_small
{
    border: #ff006e 1px solid;
    background-color: #FFD2D2;
}

h1
{
    border-bottom: 1px solid #ff006e;
	background-color: transparent;
    color: #ff006e;
}

.toolbar_body
{
    border-color: #ff006e;
}

.grid_header,
.grid_headerg
{
    background-color: #ff006e;
}

.grid,
.grid2,
.commonfiner
{
	border: solid 2px #ff006e;
}

.grid_group
{
  border-top: solid 2px #ff006e;
}

th {
  background-color: #ff006e;
}

input.button,
input.button_small,
input.button_record
{
    background: #ff006e;
	border: 1px solid #ff006e;
}

div.dbsitempanel div.dbspanelitem,
div.dbsitempanel div.dbspanelitem_disabled
{
    color: #ff006e;
}

h4
{
    color: #ff006e;
    border-bottom: #ff006e 1px solid;
}

.toolbar_button_image,
.textboxbutton
{
    filter: invert(8%) sepia(99%) saturate(7044%) hue-rotate(320deg) brightness(100%) contrast(100%);
}

div.dbsdropdownmenu
{
	background: #fee8e8;
}

ul.dbsdropdownmenu ul {
	background: #FFD2D2;
	border: 1px solid #FFD2D2;
}

ul.dbsdropdownmenu > li,
ul.dbsdropdownmenu > li li,
ul.dbsdropdownmenu > li li a
{
	color: #ff006e;
}

ul.dbsdropdownmenu div.separator
{
	background-color: #ff006e;
}

ul.dbsdropdownmenu > li li a:hover {
    color: #FFD2D2;
    background: #ff006e;
}

ul.dbsdropdownmenu li:hover > a
{
    background: #FFD2D2;
    color: #ff006e;
}

ul.dbsdropdownmenu li a:hover {
    background: #FFD2D2;
    color: #ff006e;
}

input[type="checkbox"]:checked:before {
    border-color: #ba1a69;
}

input[type="radio"]:checked:before {
    background-color: #ba1a69;
}
