/*
ThemeName:カラフル
*/

body
{
    background-color: #efdfa8;
}

.titlebar
{
  background-color: #c1740a;
}

.itemframe
{
    border: #ff6a00 2px solid;
    background-color: #caffd8;
}

.itemframe .framename
{
	background-color: #ff6a00;
}

.itemframe_small
{
    background-color: #b6ff00;
}

h1
{
	background-color: #ffd800;
    color: #000000;
}

.grid_header,
.grid_headerg
{
    background-color: #2d88b0;
}

.grid,
.grid2,
.commonfiner
{
	border: solid 2px #2d88b0;
}

.grid_group
{
  border-top: solid 2px #2d88b0;
}

th {
  background-color: #2d88b0;
}

input.button,
input.button_small,
input.button_record
{
    background: #c1740a;
	border: 1px solid #c1740a;
}

div.dbsitempanel div.dbspanelitem,
div.dbsitempanel div.dbspanelitem_disabled
{
    color: #2d5db0;
}

h4
{
    color: #2d5db0;
    border-bottom: #2d5db0 1px solid;
}

.toolbar_button_image,
.textboxbutton
{
    filter: invert(8%) sepia(99%) saturate(7044%) hue-rotate(20deg) brightness(100%) contrast(145%);
}

input[type="checkbox"]:checked:before {
    border-color: #ff6a00;
}

input[type="radio"]:checked:before {
    background-color: #ff6a00;
}
