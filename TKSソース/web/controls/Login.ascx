﻿<%@ Control Language="VB" AutoEventWireup="false" CodeFile="Login.ascx.vb" Inherits="Login" %>
<%@ Register Assembly="Dbs.Asphalt.UI" Namespace="Dbs.Asphalt.UI" TagPrefix="cc1" %>

<asp:Panel runat="server" ID="PanelLoginForm" CssClass="loginform">
  <div class="loginframe">
    <p><asp:Label ID="LabelMessage" runat="server" Text=""></asp:Label></p>
    <asp:Panel runat="server" ID="PanelLoginFormInner">
      ログインID：<br />
      <asp:TextBox runat="server" ID="TextUserID" CssClass="logintextbox" ></asp:TextBox><br />
      パスワード：<br />
      <asp:TextBox runat="server" ID="TextPasswd" CssClass="logintextbox" TextMode="Password"></asp:TextBox>
      <asp:Panel runat="server" ID="PanelPasswordReset" CssClass="bottomtoolbar" Visible="false">
        <asp:LinkButton runat="server" ID="LinkPasswordReset" Text="パスワードを忘れた場合"></asp:LinkButton><p></p>
      </asp:Panel>

      <div style="float: left;">
        <asp:CheckBox runat="server" ID="CheckSaveID" Text="ログインIDを記憶" />
      </div>
      <div style="float: right;">
        <asp:Button ID="ButtonLogin" runat="server" Text="　　ログイン　　" CssClass="buttonw" UseSubmitBehavior="true" />
      </div>
      <div class="clear"></div>
    </asp:Panel>

    <asp:Panel runat="server" ID="PanelSecondAuth" Visible="false">
      2段階目の認証パスワード：<br />
      <asp:TextBox runat="server" ID="TextSecondPassword" CssClass="logintextbox" ></asp:TextBox><br />
      <asp:CheckBox runat="server" ID="CheckTrusted" Text="このブラウザを信頼する" /><br />
      <div style="float: right;">
        <asp:Button ID="ButtonSecondAuth" runat="server" Text="　　認証　　" CssClass="buttonw" UseSubmitBehavior="true" />
        <asp:Button ID="ButtonBackLoginForm" runat="server" Text="キャンセル" CssClass="buttonw" UseSubmitBehavior="true" />
      </div>
      <div class="clear"></div>
      <asp:Label runat="server" ID="LabelAuthKey" Visible="false"></asp:Label>
    </asp:Panel>
  </div>
</asp:Panel>

<asp:Panel runat="server" ID="PanelGoPasswordResetForm" Visible="false" CssClass="password_reset_form">
  <h2>パスワードリセット手続き</h2>
  <p>新しいパスワードを指定してください。</p>
  <hr />
  パスワード：<br />
  <asp:TextBox runat="server" ID="TextResetPassword" CssClass="logintextbox" TextMode="Password"></asp:TextBox><br />
  パスワード(確認)：<br />
  <asp:TextBox runat="server" ID="TextResetPassword2" CssClass="logintextbox" TextMode="Password"></asp:TextBox>
  <asp:Label runat="server" ID="LabelPasswordResetMessage2" Visible="false" CssClass="notice_message_mini"></asp:Label>
  <div class="bottomtoolbar">
    <asp:Button ID="ButtonGoReset" runat="server" Text="　パスワード変更　" />
  </div>
</asp:Panel>

<asp:Panel runat="server" ID="PanelPasswordResetForm" Visible="false" CssClass="password_reset_form">
  <h2>パスワードリセット手続き</h2>
  <p>登録済みのメールアドレスを入力して、送信ボタンをクリックしてください。パスワードリセット用のURLを送信します。</p>
  <hr />
  メールアドレス：<br />
  <asp:TextBox runat="server" ID="TextMailAddress" CssClass="logintextbox" ></asp:TextBox><br />
  <asp:Label runat="server" ID="LabelResetFormMessage" Visible="false" CssClass="notice_message_mini"></asp:Label>
  <div class="bottomtoolbar">
    <asp:Button ID="ButtonSendMail" runat="server" Text="　送信　"/>
    <asp:Button ID="ButtonBack" runat="server" Text="戻る" />
  </div>
</asp:Panel>

<asp:Panel runat="server" ID="PanelPasswordResetSent" Visible="false" CssClass="password_reset_form">
  <h2>パスワードリセット手続き</h2>
  <p>指定メールアドレス宛てにパスワードリセット用のURLを送信しました。24時間以内に受信して、パスワードをリセットしてください。</p>
  <hr />
  <div class="bottomtoolbar">
    <asp:Button ID="ButtonBack2" runat="server" Text="戻る" />
  </div>
</asp:Panel>

<asp:Panel runat="server" ID="PanelPasswordResetComplete" Visible="false" CssClass="password_reset_form">
  <h2>パスワードリセット手続き</h2>
  <p>パスワード変更が完了しました。新しいパスワードでログインしてください。</p>
  <hr />
  <div class="bottomtoolbar">
    <asp:Button ID="ButtonBack3" runat="server" Text="戻る" />
  </div>
</asp:Panel>
