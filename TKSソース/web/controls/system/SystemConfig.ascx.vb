﻿Imports Dbs.Asphalt.UI
Imports Dbs.Asphalt.Core.CoreLogic.System
Imports Dbs.Asphalt.Core.SystemLogic
Imports Dbs.Asphalt.Core.Common

Partial Class SystemConfig
    Inherits ModuleBase

    Private logic As SystemConfigManager
    Private logicUserMaster As AccountEditorManager

    Protected Overrides Sub OnInit(e As System.EventArgs)
        logic = New SystemConfigManager(Base.Security)
        logicUserMaster = New AccountEditorManager(Base.Security)

        logic.SMTPServer.Control(Field.ControlTypeContents.ctTextBox) = TextSMTPServer
        logic.SMTPPort.Control(Field.ControlTypeContents.ctTextBox) = TextSMTPPort
        logic.SMTPAuthUserID.Control(Field.ControlTypeContents.ctTextBox) = TextSMTPUserID
        logic.SMTPAuthPassword.Control(Field.ControlTypeContents.ctTextBox) = TextSMTPPassword
        logic.SMTPSendFrom.Control(Field.ControlTypeContents.ctTextBox) = TextSMTPSendFrom
        logic.SMTPEnableSSL.Control(Field.ControlTypeContents.ctCheckBox) = CheckSMTPSSL
        logic.LogSaveDays.Control(Field.ControlTypeContents.ctTextBox) = TextLogSavedDays
        logic.LockoutRetryCounter.Control(Field.ControlTypeContents.ctTextBox) = TextRetryCount
        logic.LockoutInt.Control(Field.ControlTypeContents.ctTextBox) = TextLockArea
        logic.AvailablePasswordReset.Control(Field.ControlTypeContents.ctCheckBox) = CheckAvailablePasswordReset
        logic.AvailableTrustFunction.Control(Field.ControlTypeContents.ctCheckBox) = CheckAvailableTrustFunction
        logic.ScheTitle1Name.Control(Field.ControlTypeContents.ctTextBox) = TextSubject1Title
        logic.ScheTitle2Name.Control(Field.ControlTypeContents.ctTextBox) = TextSubject2Title
        logic.ScheTitle3Name.Control(Field.ControlTypeContents.ctTextBox) = TextSubject3Title
        logic.ScheTitle4Name.Control(Field.ControlTypeContents.ctTextBox) = TextSubject4Title
        logic.ScheTitle5Name.Control(Field.ControlTypeContents.ctTextBox) = TextSubject5Title
        logic.ScheStartHH.Control(Field.ControlTypeContents.ctTextBox) = TextStartHH
        logic.ScheEndHH.Control(Field.ControlTypeContents.ctTextBox) = TextEndHH
        logic.ScheEditImpossibleData.Control(Field.ControlTypeContents.ctRadioButtonList) = RadioAuthEditData
        logic.ScheCopyUserType.Control(Field.ControlTypeContents.ctRadioButtonList) = RadioCopyUserSelectType
        logic.Start()

        logicUserMaster.Start()

        ' ユーザーの権限区分によってコントロールの表示制御
        PanelTotalAdmin.Visible = Base.Security.IsAdmin

        If Not IsPostBack Then
            If Not logic.ReadMainData(True) Then
                Base.ErrorMessage = logic.LastError
            End If
        End If

        MyBase.OnInit(e)
    End Sub

    Public Overrides Sub FinderClose(findername As String, result As System.Collections.Hashtable, resulttargetid As String)

    End Sub

    Public Overrides Sub FinderOpen()

    End Sub

    Public Overrides ReadOnly Property PageTitle() As String
        Get
            Return logic.Title
        End Get
    End Property

    Protected Sub GridResult_RowCommand(sender As Object, e As System.Web.UI.WebControls.GridViewCommandEventArgs) Handles GridResult.RowCommand
        Select Case e.CommandName
            Case "Up"
                If Not logicUserMaster.ChangeOrder(e.CommandArgument, AccountEditorManager.ChangeOrderType.Up) Then
                    Base.ErrorMessage = logicUserMaster.LastError
                    Return
                End If
            Case "Down"
                If Not logicUserMaster.ChangeOrder(e.CommandArgument, AccountEditorManager.ChangeOrderType.Down) Then
                    Base.ErrorMessage = logicUserMaster.LastError
                    Return
                End If
        End Select
        GridResult.DataBind()
        sender.Focus()
    End Sub

    Protected Sub GridResult_RowDataBound(sender As Object, e As Dbs.Asphalt.UI.GridViewExRowEventArgs) Handles GridResult.RowDataBound
        If e.Row.RowType = DataControlRowType.DataRow Then
            CType(e.RowCell("並び替え").FindControl("ButtonUp"), Button).CommandArgument = e.RowCell("ID").Text
            CType(e.RowCell("並び替え").FindControl("ButtonDown"), Button).CommandArgument = e.RowCell("ID").Text

            CType(e.RowCell("写真").FindControl("ImagePhoto"), UserImage).UserID = e.RowCell("ID").Text
        End If

        e.RowCell("ID").Visible = False
    End Sub

    Protected Sub ButtonRegist_Click(sender As Object, e As System.EventArgs) Handles ButtonRegist.Click
        If Not logic.WriteMainData Then
            Base.ErrorMessage = logic.LastError
            Return
        End If

        Base.Message = logic.LastMessage
    End Sub

    Public Overrides ReadOnly Property RunAuth() As Dbs.Asphalt.Core.SystemLogic.Security.emUserAuth
        Get
            Return Security.emUserAuth.一般
        End Get
    End Property

    Public Overrides Sub MessageBoxClose(messageboxname As String, result As Dbs.Asphalt.UI.ModuleBase.MessageBoxResult)

    End Sub

End Class
