﻿Imports Dbs.Asphalt.UI
Imports Dbs.Asphalt.Core
Imports Dbs.Asphalt.Core.SystemLogic
Imports Dbs.Asphalt.Core.Common

Partial Class AuthModuleConfig
    Inherits ModuleBase

    Private CheckRead() As Button
    Private CheckWrite() As Button
    Private CheckDelete() As Button
    Private CheckPrint() As Button

    Protected Overrides Sub OnInit(e As System.EventArgs)
        ' Finderの読み込み
        Base.LoadFinder("UserMasterFinder", "UserMasterFinder")

        If Session("ModuleAuthUserList") Is Nothing Then
            Session("ModuleAuthUserList") = ""
        End If

        make_modulelist()

        ' クライアントメッセージの設定
        ButtonRegist.ConfirmMessage = Message.MessageText("MS_QUESTREGIST")

        MyBase.OnInit(e)
    End Sub

    Protected Overrides Sub OnLoad(e As System.EventArgs)
        MyBase.OnLoad(e)
    End Sub

    Public Overrides Sub FinderClose(findername As String, result As System.Collections.Hashtable, resulttargetid As String)
        Select Case findername
            Case "UserMasterFinder"
                Dim param As New Hashtable
                Session("ModuleAuthUserList") = result("ID")
                Base.RunCommand("system/AuthModuleConfig", param)
        End Select
    End Sub

    Public Overrides Sub FinderOpen()

    End Sub

    Public Overrides ReadOnly Property PageTitle() As String
        Get
            Return "実行権限設定"
        End Get
    End Property

    Public Overrides ReadOnly Property RunAuth() As Dbs.Asphalt.Core.SystemLogic.Security.emUserAuth
        Get
            Return SystemLogic.Security.emUserAuth.ユーザー管理者
        End Get
    End Property

    Public Overrides Sub MessageBoxClose(messageboxname As String, result As Dbs.Asphalt.UI.ModuleBase.MessageBoxResult)

    End Sub

    Protected Sub ButtonSelUser_Click(sender As Object, e As System.Web.UI.ImageClickEventArgs) Handles ButtonSelUser.Click
        Dim param As New Hashtable
        param("複数選択") = "可能"
        param("種別") = -1
        Base.OpenFinder("UserMasterFinder", param)
    End Sub

    Private Sub make_modulelist()
        Dim modlist As ModuleManager.ModuleListItem() = ModuleManager.GetLogicModuleNameList(, True)
        Dim idx As Integer = 1
        Dim bidx As Integer = 0
        Dim userlist As String() = Session("ModuleAuthUserList").ToString.Split(",")
        Dim row As String

        TableModuleList.Rows.Clear()

        row = "モジュール"
        For i = 0 To userlist.Length - 1
            If userlist(i) <> "" Then
                row &= vbTab & New MasterName(Base.Security).UserMaster(userlist(i))
            End If
        Next
        Base.UICommon.AddTableRow(TableModuleList, row)
        TableModuleList.Rows(0).CssClass = "grid_header"
        TableModuleList.BackColor = Drawing.Color.White

        For i As Integer = 0 To modlist.Length - 1
            row = modlist(i).Title
            For j = 0 To userlist.Length - 1
                If userlist(j) <> "" Then
                    row &= vbTab & ""
                End If
            Next
            Base.UICommon.AddTableRow(TableModuleList, row)
            TableModuleList.Rows(idx).Cells(0).CssClass = "module_color_" & modlist(i).Category

            For j = 1 To TableModuleList.Rows(idx).Cells.Count - 1
                TableModuleList.Rows(idx).Cells(j).HorizontalAlign = HorizontalAlign.Center
            Next

            For j = 1 To TableModuleList.Rows(idx).Cells.Count - 1
                If userlist(j - 1) <> "" Then
                    Array.Resize(CheckRead, bidx + 1)
                    Array.Resize(CheckWrite, bidx + 1)
                    Array.Resize(CheckDelete, bidx + 1)
                    Array.Resize(CheckPrint, bidx + 1)

                    CheckRead(bidx) = New Button
                    CheckWrite(bidx) = New Button
                    CheckDelete(bidx) = New Button
                    CheckPrint(bidx) = New Button

                    CheckRead(bidx).ID = "CheckRead_" & bidx
                    CheckWrite(bidx).ID = "CheckWrite_" & bidx
                    CheckDelete(bidx).ID = "CheckDelete_" & bidx
                    CheckPrint(bidx).ID = "CheckPrint_" & bidx

                    CheckRead(bidx).Text = "R"
                    CheckWrite(bidx).Text = "W"
                    CheckDelete(bidx).Text = "D"
                    CheckPrint(bidx).Text = "P"

                    CheckRead(bidx).ToolTip = "読込権限"
                    CheckWrite(bidx).ToolTip = "書込権限"
                    CheckDelete(bidx).ToolTip = "削除権限"
                    CheckPrint(bidx).ToolTip = "印刷権限"

                    CheckRead(bidx).CommandArgument = modlist(i).Name & vbTab & userlist(j - 1)
                    CheckWrite(bidx).CommandArgument = modlist(i).Name & vbTab & userlist(j - 1)
                    CheckDelete(bidx).CommandArgument = modlist(i).Name & vbTab & userlist(j - 1)
                    CheckPrint(bidx).CommandArgument = modlist(i).Name & vbTab & userlist(j - 1)

                    TableModuleList.Rows(idx).Cells(j).Controls.Add(CheckRead(bidx))
                    TableModuleList.Rows(idx).Cells(j).Controls.Add(CheckWrite(bidx))
                    TableModuleList.Rows(idx).Cells(j).Controls.Add(CheckDelete(bidx))
                    TableModuleList.Rows(idx).Cells(j).Controls.Add(CheckPrint(bidx))

                    AddHandler CheckRead(bidx).Click, AddressOf Check_Clicked
                    AddHandler CheckWrite(bidx).Click, AddressOf Check_Clicked
                    AddHandler CheckDelete(bidx).Click, AddressOf Check_Clicked
                    AddHandler CheckPrint(bidx).Click, AddressOf Check_Clicked

                    CheckRead(bidx).CommandName = IIf(Base.Security.ModuleAuth(modlist(i).Name, SystemLogic.Security.ModuleAuthTypeList.Read, userlist(j - 1)), "1", "0")
                    CheckWrite(bidx).CommandName = IIf(Base.Security.ModuleAuth(modlist(i).Name, SystemLogic.Security.ModuleAuthTypeList.Write, userlist(j - 1)), "1", "0")
                    CheckDelete(bidx).CommandName = IIf(Base.Security.ModuleAuth(modlist(i).Name, SystemLogic.Security.ModuleAuthTypeList.Delete, userlist(j - 1)), "1", "0")
                    CheckPrint(bidx).CommandName = IIf(Base.Security.ModuleAuth(modlist(i).Name, SystemLogic.Security.ModuleAuthTypeList.Print, userlist(j - 1)), "1", "0")

                    TableModuleList.Rows(idx).Style("white-space") = "nowrap"

                    CheckRead(bidx).Enabled = (modlist(i).ModuleType = "RegisterLogicBase")
                    CheckWrite(bidx).Enabled = (modlist(i).ModuleType = "RegisterLogicBase")
                    CheckDelete(bidx).Enabled = (modlist(i).ModuleType = "RegisterLogicBase")
                    CheckPrint(bidx).Enabled = (modlist(i).ModuleType = "PrinterLogicBase")

                    change_style(CheckRead(bidx))
                    change_style(CheckWrite(bidx))
                    change_style(CheckDelete(bidx))
                    change_style(CheckPrint(bidx))

                    bidx += 1
                End If
            Next

            idx += 1
        Next
    End Sub

    Protected Sub Check_Clicked(sender As Object, e As System.EventArgs)
        Dim modid As String = sender.CommandArgument.ToString.Split(vbTab)(0)
        Dim userid As String = sender.CommandArgument.ToString.Split(vbTab)(1)
        Dim wmodid As String
        Dim wuserid As String
        Dim cbox As Button = Nothing

        sender.CommandName = IIf(sender.CommandName = "1", "0", "1")
        change_style(sender)

        For i As Integer = 0 To CheckRead.Length - 1
            Select Case sender.ID.ToString.Substring(0, sender.ID.ToString.IndexOf("_"))
                Case "CheckRead"
                    cbox = CheckRead(i)
                Case "CheckWrite"
                    cbox = CheckWrite(i)
                Case "CheckDelete"
                    cbox = CheckDelete(i)
                Case "CheckPrint"
                    cbox = CheckPrint(i)
            End Select

            If Not cbox Is Nothing Then
                wmodid = cbox.CommandArgument.ToString.Split(vbTab)(0)
                wuserid = cbox.CommandArgument.ToString.Split(vbTab)(1)

                If CheckAllModules.Checked And cbox.Enabled Then
                    If userid = wuserid Then
                        cbox.CommandName = sender.CommandName
                    End If
                End If
                If CheckAllUsers.Checked And cbox.Enabled Then
                    If modid = wmodid Then
                        cbox.CommandName = sender.CommandName
                    End If
                End If
                If CheckAllModules.Checked And CheckAllUsers.Checked And cbox.Enabled Then
                    cbox.CommandName = sender.CommandName
                End If

                change_style(cbox)
            End If
        Next
    End Sub

    Protected Sub ButtonRegist_Click(sender As Object, e As System.Web.UI.ImageClickEventArgs) Handles ButtonRegist.Click
        If Session("ModuleAuthUserList") = "" Then
            Base.ErrorMessage = "ユーザーが選択されていません。"
            Return
        End If

        For i = 0 To CheckRead.Length - 1
            Dim modid As String = CheckRead(i).CommandArgument.ToString.Split(vbTab)(0)
            Dim userid As String = CheckRead(i).CommandArgument.ToString.Split(vbTab)(1)
            If Not Base.Security.SetModuleAuth(modid, CheckRead(i).CommandName, CheckWrite(i).CommandName, CheckDelete(i).CommandName, CheckPrint(i).CommandName, userid) Then
                Base.ErrorMessage = Base.Security.LastError
                Return
            End If
        Next

        make_modulelist()

        Base.Message = Message.MessageText("MS_SAVECOMPLETE")
    End Sub

    Private Sub change_style(btn As Button)
        If btn.Enabled Then
            If btn.CommandName = "1" Then
                btn.CssClass = "togglebutton_on"
            Else
                btn.CssClass = "togglebutton_off"
            End If
        Else
            btn.CssClass = "togglebutton_disable"
        End If
    End Sub

End Class
