﻿Imports Dbs.Asphalt.UI
Imports Dbs.Asphalt.Core
Imports Dbs.Asphalt.Core.CoreLogic.System
Imports Dbs.Asphalt.Core.Common

Partial Class GroupEditor
    Inherits ModuleBase

    Private logic As AccountEditorManager
    Private logicPos As PositionEditorManager

    Protected Overrides Sub OnInit(e As System.EventArgs)
        logic = New AccountEditorManager(Base.Security)

        logic.EditMode.Control(Field.ControlTypeContents.ctEditMode) = EditMode
        logic.ユーザーID.Control(Field.ControlTypeContents.ctTextBox) = TextGroupID
        logic.ユーザー名.Control(Field.ControlTypeContents.ctTextBox) = TextGroupName
        logic.ユーザー名カナ.Control(Field.ControlTypeContents.ctTextBox) = TextGroupKana
        logic.メールアドレス.Control(Field.ControlTypeContents.ctTextBox) = TextEMail
        logic.メモ.Control(Field.ControlTypeContents.ctTextBox) = TextMemo
        logic.登録日時.Control(Field.ControlTypeContents.ctTextBox) = TextRegDate
        logic.登録者名.Control(Field.ControlTypeContents.ctTextBox) = TextRegUserID
        logic.更新日時.Control(Field.ControlTypeContents.ctTextBox) = TextUpdDate
        logic.更新者名.Control(Field.ControlTypeContents.ctTextBox) = TextUpdUserID

        logic.種別.Value = 1            ' 編集対象の種別はグループのみ

        logicPos = New PositionEditorManager(Base.Security)
        logicPos.グループID.Control(Field.ControlTypeContents.ctTextBox) = TextGroupID
        'logicPos.所属ユーザーIDリスト.Control(Field.ControlTypeContents.ctCheckBoxList) = ListPositionUserIDList

        ' ユーザーの権限区分によってコントロールの表示制御
        PanelEditToolButton.Visible = Base.Security.IsUserAdmin
        ButtonDelete.Visible = Base.Security.IsUserAdmin

        If Not IsPostBack Then
            TextGroupID.Text = ""
            read_data(True)
        End If

        ' Finderの読み込み
        Base.LoadFinder("UserMasterFinder", "UserMasterFinder")
        Base.LoadFinder("UserMasterFinder2", "UserMasterFinder")

        MyBase.OnInit(e)
    End Sub

    Private Sub GroupEditor_PreRender(sender As Object, e As EventArgs) Handles Me.PreRender
        If logic.EditMode.Value = Field.EditModeContents.emInsertMode Then
            TextGroupID.Focus()
            TextGroupID.Enabled = True
            ButtonDelete.Visible = False
        Else
            TextGroupName.Focus()
            TextGroupID.Enabled = False
            ButtonDelete.Visible = True
        End If
    End Sub

    Public Overrides Sub FinderClose(findername As String, result As System.Collections.Hashtable, resulttargetid As String)
        Select Case findername
            Case "UserMasterFinder"
                TextGroupID.Text = result("ID")
                read_data(True)
            Case "UserMasterFinder2"
                Dim fld As String() = result("ID").ToString.Split(",")
                For i As Integer = 0 To fld.Length - 1
                    add_position_list_item(fld(i))
                Next
        End Select
    End Sub

    Public Overrides Sub FinderOpen()

    End Sub

    Public Overrides ReadOnly Property PageTitle() As String
        Get
            Return logicPos.Title
        End Get
    End Property

    Protected Sub ButtonRegist_Click(sender As Object, e As System.EventArgs) Handles ButtonRegist.Click
        If Not logic.WriteMainData Then
            Base.ErrorMessage = logic.LastError
            Return
        End If

        ' 所属ユーザーIDリストの作成
        logicPos.所属ユーザーIDリスト.Value = ""
        For i As Integer = 0 To ListPositionUserIDList.Items.Count - 1
            If ListPositionUserIDList.Items(i).Selected Then
                If logicPos.所属ユーザーIDリスト.Value <> "" Then
                    logicPos.所属ユーザーIDリスト.Value &= vbTab
                End If
                logicPos.所属ユーザーIDリスト.Value &= ListPositionUserIDList.Items(i).Value
            End If
        Next
        If Not logicPos.WriteMainData Then
            Base.ErrorMessage = logicPos.LastError
            Return
        End If

        Base.Message = logic.LastMessage

        read_data(True)
    End Sub

    Protected Sub ButtonNew_Click(sender As Object, e As System.EventArgs) Handles ButtonNew.Click
        TextGroupID.Text = ""
        read_data(True)
    End Sub

    Protected Sub ButtonFind_Click(sender As Object, e As System.EventArgs) Handles ButtonFind.Click
        Dim param As New Hashtable
        param("種別") = 1
        Base.OpenFinder("UserMasterFinder", param)
    End Sub

    Protected Sub ButtonDelete_Click(sender As Object, e As System.EventArgs) Handles ButtonDelete.Click
        If Not logic.DeleteMainData Then
            Base.ErrorMessage = logic.LastError
            Return
        End If
        If Not logicPos.DeleteMainData Then
            Base.ErrorMessage = logicPos.LastError
            Return
        End If

        Base.Message = logic.LastMessage

        ButtonNew_Click(ButtonNew, Nothing)
    End Sub

    Public Overrides ReadOnly Property RunAuth() As Dbs.Asphalt.Core.SystemLogic.Security.emUserAuth
        Get
            Return SystemLogic.Security.emUserAuth.一般
        End Get
    End Property

    Protected Sub ButtonAddUser_Click(sender As Object, e As System.EventArgs) Handles ButtonAddUser.Click
        Dim param As New Hashtable
        param("複数選択") = "可能"
        param("種別") = 0
        Base.OpenFinder("UserMasterFinder2", param)
    End Sub

    Protected Sub ButtonMailTest_Click(sender As Object, e As System.EventArgs) Handles ButtonMailTest.Click
        Dim mail As New SendMail
        mail.Subject = "メールテスト"
        mail.SendTo = TextEMail.Text
        mail.Body = "このメールは、システムからのテストメールです。" & vbNewLine &
                    "覚えがない場合は、大変お手数ですがこのまま破棄して下さい。"
        If Not mail.SendMail() Then
            Base.ErrorMessage = mail.LastError
            Return
        End If
        Base.Message = Message.MessageText("MS_SENDCOMPLETE", TextEMail.Text)
    End Sub

    Public Overrides Sub MessageBoxClose(messageboxname As String, result As Dbs.Asphalt.UI.ModuleBase.MessageBoxResult)

    End Sub

    Private Sub read_data(read_after_clear As Boolean)
        If Not logic.ReadMainData(read_after_clear) Then
            Base.ErrorMessage = logic.LastError
        End If
        If Not logicPos.ReadMainData(read_after_clear) Then
            Base.ErrorMessage = logicPos.LastError
        Else
            ListPositionUserIDList.Items.Clear()
            If logicPos.所属ユーザーIDリスト.Value <> "" Then
                Dim fld As String() = logicPos.所属ユーザーIDリスト.Value.ToString.Split(vbTab)
                For i As Integer = 0 To fld.Length - 1
                    add_position_list_item(fld(i))
                Next
            End If
        End If
    End Sub

    Private Sub add_position_list_item(userid As String)
        If ListPositionUserIDList.Items.FindByValue(userid) Is Nothing Then
            Dim itm As New ListItem
            itm.Text = New MasterName(Base.Security).UserMaster(userid)
            itm.Value = userid
            itm.Selected = True
            ListPositionUserIDList.Items.Add(itm)
        End If
    End Sub

End Class
