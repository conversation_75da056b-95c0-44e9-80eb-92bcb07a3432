﻿Imports Dbs.Asphalt.UI
Imports Dbs.Asphalt.Core
Imports Dbs.Asphalt.Core.Logic
Imports Dbs.Asphalt.Core.Logic.System
Imports Dbs.Asphalt.Core.Common
Imports Dbs.Asphalt.Database

Partial Class DatabaseBackup
    Inherits ModuleBase

    Protected Overrides Sub OnInit(e As System.EventArgs)

        Base.AddPostbackTrrigerControl(ButtonBackup)

        MyBase.OnInit(e)
    End Sub

    Public Overrides Sub FinderClose(findername As String, result As System.Collections.Hashtable, resulttargetid As String)

    End Sub

    Public Overrides Sub FinderOpen()

    End Sub

    Public Overrides Sub MessageBoxClose(messageboxname As String, result As Dbs.Asphalt.UI.ModuleBase.MessageBoxResult)

    End Sub

    Public Overrides ReadOnly Property PageTitle As String
        Get
            Return "データバックアップ"
        End Get
    End Property

    Public Overrides ReadOnly Property RunAuth As Dbs.Asphalt.Core.SystemLogic.Security.emUserAuth
        Get
            Return SystemLogic.Security.emUserAuth.総合管理者
        End Get
    End Property

    Protected Sub ButtonBackup_Click(sender As Object, e As System.EventArgs) Handles ButtonBackup.Click
        Dim md As String = Base.MakeClientExclusiveFolder()

        Try
            Using db As New DbSystemTableAdapters.DatabaseBackupTableAdapter
                db.Backup(md & "\AsphaltDB.bak")
            End Using
        Catch ex As Exception
            Base.ErrorMessage = ex.Message
            Return
        End Try

        Base.DownloadBinaryFile(md & "\AsphaltDB.bak")
    End Sub

End Class
