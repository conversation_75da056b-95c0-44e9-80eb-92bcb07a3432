﻿Imports Dbs.Asphalt.UI
Imports Dbs.Asphalt.Core
Imports Dbs.Asphalt.Core.CoreLogic
Imports Dbs.Asphalt.Core.CoreLogic.System
Imports Dbs.Asphalt.Core.Common
Imports Dbs.Asphalt.Core.SystemLogic
Imports System.Data
Imports System.Web

Partial Class Scheduler
    Inherits ModuleBase

    Private logic As ScheduleManager
    Private cfglogic As SystemConfigManager
    Private printer As SchedulePrinter

    Protected Sub DropStartWeek_SelectedIndexChanged(sender As Object, e As System.EventArgs) Handles DropStartWeek.SelectedIndexChanged
        Dim cfg As New Configurator(Base.Security)
        cfg.SetUserConfigData(Base.Security.UserID, "スケジュール週表示開始曜日", DropStartWeek.SelectedValue)
        Base.RunCommand("system/Scheduler")
    End Sub

    Protected Sub ButtonPrint_Click(sender As Object, e As EventArgs) Handles ButtonPrint.Click
        printer = New SchedulePrinter(Base.Security)
        printer.Start()

        printer.ユーザーID.Value = DropFilter.SelectedValue.Split(vbTab)(0)
        Select Case Session("スケジューラ表示形式")
            Case 1          ' 日表示
                printer.開始日付.Value = CDate(Session("スケジューラ表示開始日")).ToString("yyyy/MM/dd")
                printer.終了日付.Value = CDate(Session("スケジューラ表示開始日")).ToString("yyyy/MM/dd")
            Case 2          ' 月表示
                printer.開始日付.Value = CDate(Session("スケジューラ表示開始日")).ToString("yyyy/MM/01")
                printer.終了日付.Value = CDate(printer.開始日付.Value).AddMonths(1).AddDays(-1).ToString("yyyy/MM/dd")
            Case Else       ' 週表示
                printer.開始日付.Value = CDate(Session("スケジューラ表示開始日")).ToString("yyyy/MM/dd")
                printer.終了日付.Value = CDate(Session("スケジューラ表示開始日")).AddDays(6).ToString("yyyy/MM/dd")
        End Select

        If Not printer.PrintMainData() Then
            Base.ErrorMessage = printer.LastError
            Return
        End If

        Base.DownloadPDF(printer.Report, "schedule.pdf")

    End Sub

    Protected Sub ButtonAddUser_Click(sender As Object, e As System.EventArgs) Handles ButtonAddUser.Click
        Dim param As New Hashtable
        param("複数選択") = "可能"
        param("種別") = 0
        Base.OpenFinder("UserMasterFinder2", param)
    End Sub

    Protected Sub Drop開始_SelectedIndexChanged(sender As Object, e As System.EventArgs) Handles DropStartHH.SelectedIndexChanged, _
                                                                                                             DropStartMM.SelectedIndexChanged, _
                                                                                                             DropEndHH.SelectedIndexChanged, _
                                                                                                             DropEndMM.SelectedIndexChanged
        Select Case CType(sender, DropDownList).ID
            Case "DropStartHH"
                If DropStartHH.SelectedValue <> "" And DropStartMM.SelectedValue = "" Then
                    DropStartMM.SelectedIndex = 1
                End If
                If DropStartHH.SelectedValue = "" And DropStartMM.SelectedValue <> "" Then
                    DropStartMM.SelectedIndex = 0
                End If
            Case "DropStartMM"
                If DropStartMM.SelectedValue <> "" And DropStartHH.SelectedValue = "" Then
                    DropStartHH.SelectedIndex = 1
                End If
                If DropStartMM.SelectedValue = "" And DropStartHH.SelectedValue <> "" Then
                    DropStartHH.SelectedIndex = 0
                End If
            Case "DropEndHH"
                If DropEndHH.SelectedValue <> "" And DropEndMM.SelectedValue = "" Then
                    DropEndMM.SelectedIndex = 1
                End If
                If DropEndHH.SelectedValue = "" And DropEndMM.SelectedValue <> "" Then
                    DropEndMM.SelectedIndex = 0
                End If
            Case "DropEndMM"
                If DropEndMM.SelectedValue <> "" And DropEndHH.SelectedValue = "" Then
                    DropEndHH.SelectedIndex = 1
                End If
                If DropEndMM.SelectedValue = "" And DropEndHH.SelectedValue <> "" Then
                    DropEndHH.SelectedIndex = 0
                End If
        End Select
    End Sub

    Protected Sub ButtonDelete_Click(sender As Object, e As System.EventArgs) Handles ButtonDelete.Click
        If Not logic.DeleteMainData() Then
            Base.ErrorMessage = logic.LastError
            Return
        End If
        Base.RunCommand("system/Scheduler")
    End Sub

    Protected Sub ButtonRegist_Click(sender As Object, e As System.EventArgs) Handles ButtonRegist.Click
        logic.スケジュール種別.Value = "0"
        logic.色.Value = ColorSelector.SelectedColor

        If Not logic.WriteMainData() Then
            Base.ErrorMessage = logic.LastError
            Return
        End If

        If ListPositionUserIDList.Items.Count > 0 Then
            Dim nowuserid As String = TextUserID.Text
            For i As Integer = 0 To ListPositionUserIDList.Items.Count - 1
                If ListPositionUserIDList.Items(i).Selected And ListPositionUserIDList.Items(i).Value <> nowuserid Then
                    TextUserID.Text = ListPositionUserIDList.Items(i).Value
                    DropAuthEditUser.SelectedValue = 2
                    If Not logic.WriteMainData() Then
                        Base.ErrorMessage = logic.LastError
                        Return
                    End If
                End If
            Next
        End If

        Base.RunCommand("system/Scheduler")
    End Sub

    Protected Sub ButtonFinder_Click(sender As Object, e As System.EventArgs) Handles ButtonFinder.Click
        Dim param As New Hashtable
        param("複数選択") = "不可"
        param("種別") = -1
        Base.OpenFinder("UserMasterFinder", param)
    End Sub

    Protected Sub DropFilter_SelectedIndexChanged(sender As Object, e As System.EventArgs) Handles DropFilter.SelectedIndexChanged
        Dim cfg As New Configurator(Base.Security)
        cfg.SetUserConfigData(Base.Security.UserID, "スケジューラ表示フィルタ", DropFilter.SelectedValue)
        Base.RunCommand("system/Scheduler")
    End Sub

    Protected Sub ButtonView_Click(sender As Object, e As System.EventArgs) Handles ButtonViewWeekly.Click,
                                                                                    ButtonViewDaily.Click,
                                                                                    ButtonViewMonthly.Click
        Dim cfg As New Configurator(Base.Security)
        cfg.SetUserConfigData(Base.Security.UserID, "スケジューラ表示形式", CType(sender, Button).CommandArgument)
        Session("スケジューラ表示形式") = CType(sender, Button).CommandArgument
        Base.RunCommand("system/Scheduler")
    End Sub

    Protected Sub ButtonMoveDay_Click(sender As Object, e As System.EventArgs) Handles ButtonNextDay.Click,
                                                                                       ButtonNextWeek.Click,
                                                                                       ButtonNextMonth.Click,
                                                                                       ButtonPrevDay.Click,
                                                                                       ButtonPrevWeek.Click,
                                                                                       ButtonPrevMonth.Click,
                                                                                       ButtonToday.Click
        Dim nowdt As Date = CDate(Session("スケジューラ表示開始日"))

        Select Case CType(sender, Button).CommandArgument
            Case "-3"
                nowdt = nowdt.AddMonths(-1)
            Case "-2"
                nowdt = nowdt.AddDays(-7)
            Case "-1"
                nowdt = nowdt.AddDays(-1)
            Case "0"
                nowdt = Now
            Case "1"
                nowdt = nowdt.AddDays(1)
            Case "2"
                nowdt = nowdt.AddDays(7)
            Case "3"
                nowdt = nowdt.AddMonths(1)
        End Select
        Session("スケジューラ表示開始日") = nowdt
        view_schedule_frame()

    End Sub

    Protected Sub ButtonBack_Click(sender As Object, e As System.EventArgs) Handles ButtonBack.Click
        Base.RunCommand("system/Scheduler")
    End Sub

    Public Overrides Sub FinderClose(findername As String, result As System.Collections.Hashtable, resulttargetid As String)
        Select Case findername
            Case "UserMasterFinder"
                DropFilter.SelectedValue = result("ID") & vbTab & "0"
                If DropFilter.SelectedValue = "" Then
                    DropFilter.SelectedValue = result("ID") & vbTab & "1"
                End If
                DropFilter_SelectedIndexChanged(DropFilter, Nothing)
            Case "UserMasterFinder2"
                Dim fld As String() = result("ID").ToString.Split(",")
                For i As Integer = 0 To fld.Length - 1
                    add_position_list_item(fld(i))
                Next
        End Select
    End Sub

    Public Overrides Sub FinderOpen()

    End Sub

    Public Overrides ReadOnly Property PageTitle() As String
        Get
            Return logic.Title
        End Get
    End Property

    Public Overrides ReadOnly Property RunAuth() As Dbs.Asphalt.Core.SystemLogic.Security.emUserAuth
        Get
            Return Security.emUserAuth.一般
        End Get
    End Property

    Protected Overrides Sub OnInit(e As System.EventArgs)
        Dim cfg As New Configurator(Base.Security)
        Dim dl As New DataList(Base.Security)

        Base.UICommon.ListControlDataBind(DropFilter, dl.UserAndGroupList(True), UICommon.AllSelectionTextType.指定なし, "")
        DropFilter.Items(0).Text = "( 全員表示 )"
        DropFilter.SelectedValue = cfg.GetUserConfigData(Base.Security.UserID, "スケジューラ表示フィルタ", "")

        Base.UICommon.ListControlDataBind(DropStartWeek, dl.AnyList("スケジュール週表示開始曜日"))
        DropStartWeek.SelectedValue = cfg.GetUserConfigData(Base.Security.UserID, "スケジュール週表示開始曜日", "0")

        logic = New ScheduleManager(Base.Security)

        logic.EditMode.Control(Field.ControlTypeContents.ctEditMode) = EditMode
        logic.ID.Control(Field.ControlTypeContents.ctTextBox) = TextID
        logic.スケジュール区分.Control(Field.ControlTypeContents.ctTextBox) = TextScheduleType
        logic.ユーザーID.Control(Field.ControlTypeContents.ctTextBox) = TextUserID
        logic.開始日付.Control(Field.ControlTypeContents.ctTextBox) = TextStartDate
        logic.開始時.Control(Field.ControlTypeContents.ctDropDownList) = DropStartHH
        logic.開始分.Control(Field.ControlTypeContents.ctDropDownList) = DropStartMM
        logic.終了日付.Control(Field.ControlTypeContents.ctTextBox) = TextEndDate
        logic.終了時.Control(Field.ControlTypeContents.ctDropDownList) = DropEndHH
        logic.終了分.Control(Field.ControlTypeContents.ctDropDownList) = DropEndMM
        logic.件名1.Control(Field.ControlTypeContents.ctTextBox) = TextSubject1
        logic.件名2.Control(Field.ControlTypeContents.ctTextBox) = TextSubject2
        logic.件名3.Control(Field.ControlTypeContents.ctTextBox) = TextSubject3
        logic.件名4.Control(Field.ControlTypeContents.ctTextBox) = TextSubject4
        logic.件名5.Control(Field.ControlTypeContents.ctTextBox) = TextSubject5
        logic.詳細.Control(Field.ControlTypeContents.ctTextBox) = TextBody
        logic.編集可能ユーザー.Control(Field.ControlTypeContents.ctDropDownList) = DropAuthEditUser
        logic.登録日時.Control(Field.ControlTypeContents.ctTextBox) = TextRegDate
        logic.登録者名.Control(Field.ControlTypeContents.ctTextBox) = TextRegUserName
        logic.更新日時.Control(Field.ControlTypeContents.ctTextBox) = TextModDate
        logic.更新者名.Control(Field.ControlTypeContents.ctTextBox) = TextModUserName

        logic.Start()

        Base.AddPostbackTrrigerControl(ButtonPrint)

        cfglogic = New SystemConfigReader(Base.Security)
        cfglogic.Start()
        cfglogic.ReadMainData(True)

        ' Finderの読み込み
        Base.LoadFinder("UserMasterFinder", "UserMasterFinder")
        Base.LoadFinder("UserMasterFinder2", "UserMasterFinder")

        ' 表示形式の初期値設定(Sessionで保持)
        If Session("スケジューラ表示形式") Is Nothing Then
            Session("スケジューラ表示形式") = cfg.GetUserConfigData(Base.Security.UserID, "スケジューラ表示形式", 0)
        End If
        If Not IsDate(Session("スケジューラ表示開始日")) Then
            Session("スケジューラ表示開始日") = Now
        End If

        ' 表示フィルターの初期値設定
        If Not Request("filteruserid") Is Nothing Then
            DropFilter.SelectedValue = Request("filteruserid") & vbTab & "0"
            DropFilter_SelectedIndexChanged(DropFilter, Nothing)
        End If
        If Not Request("filtergroupid") Is Nothing Then
            DropFilter.SelectedValue = Request("filtergroupid") & vbTab & "1"
            DropFilter_SelectedIndexChanged(DropFilter, Nothing)
        End If

        ' actionパラメータが指定されている場合は、指示に従って処理を変更
        If Not Request("action") Is Nothing Then
            Select Case Request("action")
                Case "dailyview"                    ' ***** 週または月表示で日付をクリックされた(日表示切り替え)
                    Session("スケジューラ表示開始日") = Request("date")
                    Session("スケジューラ表示形式") = 1
                    If Request("userid") <> "" Then
                        DropFilter.SelectedValue = Request("userid") & vbTab & "0"
                    End If
                    DropFilter_SelectedIndexChanged(DropFilter, Nothing)
                Case "view", "return"               ' ***** スケジュール編集フレームの表示
                    If Request("action") = "view" Then
                        TextScheduleType.Text = 0       ' 通常スケジュール
                        PanelReturnTimeGuide.Visible = False
                    Else
                        TextScheduleType.Text = 1       ' 帰社時間
                        PanelReturnTimeGuide.Visible = True
                    End If

                    ' 時間ドロップダウンのリスト作成
                    DropStartHH.AddItem("--", "")
                    DropEndHH.AddItem("--", "")
                    For i As Integer = cfglogic.ScheStartHH.Value To cfglogic.ScheEndHH.Value
                        DropStartHH.AddItem(i.ToString("00"), i.ToString("00"))
                        DropEndHH.AddItem(i.ToString("00"), i.ToString("00"))
                    Next
                    DropStartMM.AddItem("--", "")
                    DropEndMM.AddItem("--", "")
                    For i As Integer = 0 To 59 Step 15
                        DropStartMM.AddItem(i.ToString("00"), i.ToString("00"))
                        DropEndMM.AddItem(i.ToString("00"), i.ToString("00"))
                    Next

                    ' 件名1～5の項目名が定義されているもののみ表示
                    If cfglogic.ScheTitle1Name.Value <> "" Then
                        LabelSubject1.Text = cfglogic.ScheTitle1Name.Value
                        PanelSubject1.Visible = True
                    End If
                    If cfglogic.ScheTitle2Name.Value <> "" Then
                        LabelSubject2.Text = cfglogic.ScheTitle2Name.Value
                        PanelSubject2.Visible = True
                    End If
                    If cfglogic.ScheTitle3Name.Value <> "" Then
                        LabelSubject3.Text = cfglogic.ScheTitle3Name.Value
                        PanelSubject3.Visible = True
                    End If
                    If cfglogic.ScheTitle4Name.Value <> "" Then
                        LabelSubject4.Text = cfglogic.ScheTitle4Name.Value
                        PanelSubject4.Visible = True
                    End If
                    If cfglogic.ScheTitle5Name.Value <> "" Then
                        LabelSubject5.Text = cfglogic.ScheTitle5Name.Value
                        PanelSubject5.Visible = True
                    End If

                    ' データのIDが指定されている場合はIDで読み込み、それ以外の場合は新規作成
                    If Not Request("id") Is Nothing Then
                        TextID.Text = Request("id")
                        read_data()
                    Else
                        TextID.Text = ""
                        TextStartDate.Text = Request("date")
                        TextUserID.Text = Request("userid")

                        read_data()

                        TextStartDate.Text = Request("date")
                        TextEndDate.Text = Request("date")
                        TextUserID.Text = Request("userid")

                        ' テーブルピッカーから渡されたパラメータがある場合は、開始～終了時間を初期セット
                        If Not Request("from") Is Nothing And Not Request("to") Is Nothing Then
                            Dim pfrom As Integer
                            Dim pto As Integer
                            If Text.CVal(Request("from")) < Text.CVal(Request("to")) Then
                                pfrom = Text.CVal(Request("from"))
                                pto = Text.CVal(Request("to"))
                            Else
                                pfrom = Text.CVal(Request("to"))
                                pto = Text.CVal(Request("from"))
                            End If

                            DropStartHH.SelectedIndex = 1
                            DropStartMM.SelectedIndex = 1
                            For i As Integer = 0 To pfrom - 1
                                If DropStartMM.SelectedIndex = DropStartMM.Items.Count - 1 Then
                                    If DropStartHH.SelectedIndex = DropStartHH.Items.Count - 1 Then
                                        DropStartHH.SelectedIndex = 0
                                        DropStartMM.SelectedIndex = 0
                                        Exit For
                                    Else
                                        DropStartHH.SelectedIndex += 1
                                        DropStartMM.SelectedIndex = 1
                                    End If
                                Else
                                    DropStartMM.SelectedIndex += 1
                                End If
                            Next

                            DropEndHH.SelectedIndex = 1
                            DropEndMM.SelectedIndex = 1
                            For i As Integer = 0 To pto
                                If DropEndMM.SelectedIndex = DropEndMM.Items.Count - 1 Then
                                    If DropEndHH.SelectedIndex = DropEndHH.Items.Count - 1 Then
                                        DropEndHH.SelectedIndex = 0
                                        DropEndMM.SelectedIndex = 0
                                        Exit For
                                    Else
                                        DropEndHH.SelectedIndex += 1
                                        DropEndMM.SelectedIndex = 1
                                    End If
                                Else
                                    DropEndMM.SelectedIndex += 1
                                End If
                            Next
                        End If
                    End If

                    ' 編集対象ユーザーのその日の日スケジュール表示を行う
                    read_daily_table(TextStartDate.Text, , TextUserID.Text)

                    ' ユーザーイメージのコントロールへIDセット
                    ImagePhoto.UserID = TextUserID.Text

                    ' 権限に応じて編集・閲覧モードを切り替え
                    If logic.CheckEditAuthByUserID(Text.CVal(cfglogic.ScheEditImpossibleData.Value), TextUserID.Text) Or logic.CheckEditAuthByScheduleData(Text.CVal(TextID.Text)) Then
                        LabelDate.Text = CDate(TextStartDate.Text).ToString("yyyy/MM/dd") & "のスケジュール編集"
                        'Base.UICommon.ContainerReadOnly(PanelEdit, False)
                        ButtonRegist.Visible = True
                        ButtonDelete.Visible = True
                        EditMode.Visible = True
                        ColorSelector.Enabled = True
                        LabelNotice.Visible = False
                    Else
                        LabelDate.Text = CDate(TextStartDate.Text).ToString("yyyy/MM/dd") & "のスケジュール詳細"
                        'Base.UICommon.ContainerReadOnly(PanelEdit, True)
                        ButtonRegist.Visible = False
                        ButtonDelete.Visible = False
                        EditMode.Visible = False
                        ColorSelector.Enabled = False
                        LabelNotice.Visible = True
                    End If

                    ' 帰社時間登録の場合は、一般予定編集用の項目を非表示
                    If TextScheduleType.Text = 1 Then
                        LabelDate.Text = CDate(TextStartDate.Text).ToString("yyyy/MM/dd") & "の帰社予定時間"
                        TextStartDate.Visible = False
                        PanelScheduleEditorItem1.Visible = False
                        PanelScheduleEditorItem2.Visible = False
                    End If

                    PanelEdit.Visible = True

                    If Not TextStartDate.ReadOnly Then
                        'TextStartDate.Focus()
                    End If
            End Select

            PanelViewButtons.Visible = False
        Else
            view_schedule_frame()

            PanelViewButtons.Visible = True
        End If

        ' ボタンへの確認メッセージを定義(javascript)
        ButtonDelete.OnClientClick = "return confirm('" & Message.MessageText("MS_QUESTDELETE") & "');"

        MyBase.OnInit(e)
    End Sub

    Protected Overrides Sub OnLoad(e As System.EventArgs)
        MyBase.OnLoad(e)
    End Sub

    Private Sub view_schedule_frame()
        ' 表示フィルターの読込(ID + TAB + n(0:ユーザー,1:グループ))
        Dim groupid As String = ""
        Dim userid As String = ""
        If DropFilter.SelectedValue <> "" Then
            Dim fld As String() = DropFilter.SelectedValue.Split(vbTab)
            Select Case fld(1)
                Case "0"      ' ユーザー
                    userid = fld(0)
                Case "1"      ' グループ
                    groupid = fld(0)
            End Select
        Else
            userid = ""
            groupid = ""
        End If

        ButtonViewMonthly.CssClass = "button"
        ButtonViewWeekly.CssClass = "button"
        ButtonViewDaily.CssClass = "button"
        ButtonPrevMonth.Visible = True
        ButtonNextMonth.Visible = True
        ButtonPrevWeek.Visible = True
        ButtonNextWeek.Visible = True
        ButtonPrevDay.Visible = True
        ButtonNextDay.Visible = True

        ' 表示形式に従ってHTML出力
        Select Case Session("スケジューラ表示形式")
            Case 1          ' 日表示
                ButtonViewDaily.CssClass = "buttonb"
                ButtonPrevMonth.Visible = False
                ButtonPrevWeek.Visible = False
                ButtonNextMonth.Visible = False
                ButtonNextWeek.Visible = False
                PanelStartWeek.Visible = False
                ButtonToday.Text = "今日"
                read_daily_table(Session("スケジューラ表示開始日"), groupid, userid)
            Case 2          ' 月表示
                ButtonViewMonthly.CssClass = "buttonb"
                ButtonPrevDay.Visible = False
                ButtonPrevWeek.Visible = False
                ButtonNextDay.Visible = False
                ButtonNextWeek.Visible = False
                PanelStartWeek.Visible = False
                ButtonToday.Text = "今月"
                read_monthly_table(Session("スケジューラ表示開始日"), groupid, userid)
            Case Else       ' 週表示
                ButtonViewWeekly.CssClass = "buttonb"
                ButtonPrevMonth.Visible = False
                ButtonNextMonth.Visible = False
                If Text.CVal(DropStartWeek.SelectedValue) <> 0 Then
                    ButtonPrevDay.Visible = False
                    ButtonNextDay.Visible = False
                End If
                ButtonToday.Text = "今週"

                ' 表示開始曜日が指定されている場合、日付を含む開始曜日にあたる日付を計算
                Session("スケジューラ表示開始日") = get_startweek_day(Text.CVal(DropStartWeek.SelectedValue), CDate(Session("スケジューラ表示開始日")))

                read_weekly_table(Session("スケジューラ表示開始日"), groupid, userid)
        End Select
    End Sub

    Public Overrides Sub MessageBoxClose(messageboxname As String, result As Dbs.Asphalt.UI.ModuleBase.MessageBoxResult)

    End Sub

#Region "週表示"

    '''=============================================================================================
    ''' <summary>
    ''' 週表示
    ''' </summary>
    ''' <param name="startday">表示開始日</param>
    ''' <param name="groupid">グループID(省略時は全グループ)</param>
    ''' <param name="userid">ユーザーID(省略時は全ユーザー)</param>
    '''=============================================================================================
    Private Sub read_weekly_table(startday As Date, Optional groupid As String = "", Optional userid As String = "")
        Dim cal As New Calendar
        Dim headerrow As New TableRow
        Dim headercell As New TableHeaderCell
        Dim action As String
        Dim color As String
        Dim holidayname As String = ""
        Dim edit As Boolean

        ' 日本の祝日を取得する
        Dim holidays As Calendar.NationalHoliday() = cal.GetNationalHolidays(startday, startday.AddDays(7))

        TableSchedule.Rows.Clear()

        If Not logic.ReadScheduleViewData(ScheduleManager.emViewType.Weekly, startday, "", groupid, userid) Then
            Base.ErrorMessage = logic.LastError
            Return
        End If

        headercell.Text = ""
        'headercell.Width = 200
        headercell.CssClass = "schedule_cell_day"
        headerrow.Cells.Add(headercell)
        For j As Integer = 0 To logic.ScheduleViewData(0).DataCount - 1
            Dim cell As New TableHeaderCell
            Dim wkno As Integer = Weekday(logic.ScheduleViewData(0).DayData(j).Day)

            cell.Text = "<a href=""?cmd=system/Scheduler&action=dailyview&date=" & logic.ScheduleViewData(0).DayData(j).Day.ToString("yyyy/MM/dd") & "&userid="">" & logic.ScheduleViewData(0).DayData(j).Day.ToString("M月d日") & "(" & WeekdayName(wkno).Substring(0, 1) & ")" & "</a>"
            If logic.ScheduleViewData(0).DayData(j).Day.ToString("yyyy/MM/dd") = Now.ToString("yyyy/MM/dd") Then
                cell.CssClass = "schedule_cell_today"
            Else
                If cal.IsHoliday(logic.ScheduleViewData(0).DayData(j).Day, holidays, holidayname) Then
                    cell.CssClass = "schedule_cell_holiday2"
                    cell.ToolTip = holidayname
                Else
                    Select Case wkno
                        Case 7
                            cell.CssClass = "schedule_cell_holiday1"
                        Case 1
                            cell.CssClass = "schedule_cell_holiday2"
                        Case Else
                            cell.CssClass = "schedule_cell_day"
                    End Select
                End If
            End If
            cell.Width = Unit.Percentage(12)
            headerrow.Cells.Add(cell)
        Next
        TableSchedule.Rows.Add(headerrow)

        For i As Integer = 0 To logic.DataCount - 1
            Dim row As New TableRow
            Dim usernamecell As New TableCell
            Dim lbl As New Label

            usernamecell.CssClass = "schedule_cell_normal"
            usernamecell.Text = make_user_imagebutton(logic.ScheduleViewData(i).UserID, logic.ScheduleViewData(i).UserName)
            row.Cells.Add(usernamecell)

            edit = logic.CheckEditAuthByUserID(Text.CVal(cfglogic.ScheEditImpossibleData.Value), logic.ScheduleViewData(i).UserID)

            For j As Integer = 0 To logic.ScheduleViewData(i).DataCount - 1
                Dim cell As New TableCell
                If logic.ScheduleViewData(i).DayData(j).Day.ToString("yyyy/MM/dd") = Now.ToString("yyyy/MM/dd") Then
                    cell.CssClass = "schedule_cell_today_data"
                Else
                    cell.CssClass = "schedule_cell_normal"
                End If
                'cell.Text = "<div class=""schedule_cell_name"">&nbsp;" & _
                '            logic.ScheduleViewData(i).UserName & _
                '            "</div>"
                If edit Then
                    cell.Text &= "<div class=""schedule_editbutton"">" &
                                   "<a href=""?cmd=system/Scheduler&action=view&userid=" & logic.ScheduleViewData(i).UserID & "&date=" & logic.ScheduleViewData(i).DayData(j).Day.ToString("yyyy/MM/dd") & """><img src=""images/ico_edit.png"" title=""" & logic.ScheduleViewData(i).DayData(j).Day.ToString("yyyy/MM/dd") & "の予定を追加"" /></a>&nbsp;" &
                                   "<a href=""?cmd=system/Scheduler&action=return&userid=" & logic.ScheduleViewData(i).UserID & "&date=" & logic.ScheduleViewData(i).DayData(j).Day.ToString("yyyy/MM/dd") & """><img src=""images/ico_return.png"" title=""" & logic.ScheduleViewData(i).DayData(j).Day.ToString("yyyy/MM/dd") & "の帰社予定時間を登録"" /></a>" &
                                 "</div>" &
                                 "<div class=""clear""></div>"
                    'ElseIf logic.ScheduleViewData(i).DayData(j).DataCount > 0 Then
                    '    cell.Text = "<div class=""schedule_editbutton"">" & _
                    '                "<a href=""?cmd=system/Scheduler&action=view&userid=" & logic.ScheduleViewData(i).UserID & "&date=" & logic.ScheduleViewData(i).DayData(j).Day.ToString("yyyy/MM/dd") & """><img src=""images/ico_view.png"" title=""この日の予定を見る"" /></a>" & _
                    '                "</div>"
                End If
                For k As Integer = 0 To logic.ScheduleViewData(i).DayData(j).DataCount - 1
                    With logic.ScheduleViewData(i).DayData(j).TimeData(k)
                        If .ScheduleDataType = ScheduleManager.emScheduleDataType.Schedule Then
                            action = "view"
                            color = "schedule_cell_data" & .Color
                        Else
                            action = "return"
                            If .EndTime = "" Then
                                color = "schedule_cell_data_dreturn"
                            Else
                                color = "schedule_cell_data_return"
                            End If
                        End If

                        cell.Text &= "<div class=""" & color & """>" &
                                         "<div class=""schedule_subject"">" &
                                             "<a href=""?cmd=system/Scheduler&action=" & action & "&id=" & .ID & """>" & .Subject1 & "</a>" & IIf(.Subject2 <> "", "<br />" & .Subject2, "") &
                                         "</div>" &
                                         "<div class=""schedule_time"">" &
                                             .StartTime &
                                             IIf(.StartTime = "" Or .EndTime = "", "→", "～") &
                                             .EndTime &
                                         "</div>" &
                                     "</div>"
                    End With
                Next
                row.Cells.Add(cell)
            Next
            TableSchedule.Rows.Add(row)
        Next
    End Sub

#End Region

#Region "日表示"

    '''=============================================================================================
    ''' <summary>
    ''' 日表示
    ''' </summary>
    ''' <param name="startday">表示開始日</param>
    ''' <param name="groupid">グループID(省略時は全グループ)</param>
    ''' <param name="userid">ユーザーID(省略時は全ユーザー)</param>
    '''=============================================================================================
    Private Sub read_daily_table(startday As Date, Optional groupid As String = "", Optional userid As String = "")
        Dim cal As New Calendar
        Dim headerrow As New TableRow
        Dim headercell As New TableHeaderCell
        Dim sth As Integer = cfglogic.ScheStartHH.Value
        Dim eth As Integer = cfglogic.ScheEndHH.Value
        Dim holidayname As String = ""
        Dim edit As Boolean

        ' 日本の祝日を取得する
        Dim holidays As Calendar.NationalHoliday() = cal.GetNationalHolidays(startday, startday)

        ' セル選択用スクリプトの定義
        TableSchedule.Attributes.Add("onmousedown", "mouseDown(this, event); return false;")
        TableSchedule.Attributes.Add("onmouseup", "mouseUp(this, event);")
        TableSchedule.Attributes.Add("onmousemove", "mouseMove(this, event); return false;")

        TableSchedule.Rows.Clear()

        If Not logic.ReadScheduleViewData(ScheduleManager.emViewType.Daily, startday, "", groupid, userid) Then
            Base.ErrorMessage = logic.LastError
            Return
        End If

        headerrow = New TableRow
        headercell = New TableHeaderCell
        headercell.Text = ""
        headercell.Width = 200
        headercell.CssClass = "schedule_cell_day"
        headerrow.Cells.Add(headercell)
        For j As Integer = 0 To 0
            Dim cell As New TableCell
            cell.Text = startday.ToString("yyyy年M月d日") & "(" & WeekdayName(Weekday(startday)).Substring(0, 1) & ")"
            cell.CssClass = "schedule_cell_day"
            cell.ColumnSpan = (eth - sth) * 4 + 4
            If startday.ToString("yyyy/MM/dd") = Now.ToString("yyyy/MM/dd") Then
                cell.CssClass = "schedule_cell_today"
            Else
                If cal.IsHoliday(startday, holidays, holidayname) Then
                    cell.CssClass = "schedule_cell_holiday2"
                    cell.ToolTip = holidayname
                Else
                    Select Case Weekday(startday)
                        Case 7
                            cell.CssClass = "schedule_cell_holiday1"
                        Case 1
                            cell.CssClass = "schedule_cell_holiday2"
                        Case Else
                            cell.CssClass = "schedule_cell_day"
                    End Select
                End If
            End If
            headerrow.Cells.Add(cell)
        Next
        'headercell.RowSpan = 2
        TableSchedule.Rows.Add(headerrow)

        headerrow = New TableRow
        headercell = New TableHeaderCell
        headercell.Text = ""
        headercell.Width = 200
        headercell.CssClass = "schedule_cell_day"
        headerrow.Cells.Add(headercell)
        For j As Integer = sth To eth
            Dim cell As New TableHeaderCell
            cell.Text = j.ToString("00")
            cell.CssClass = "schedule_cell_day"
            cell.ColumnSpan = 4
            headerrow.Cells.Add(cell)
        Next
        TableSchedule.Rows.Add(headerrow)

        For i As Integer = 0 To logic.DataCount - 1

            edit = logic.CheckEditAuthByUserID(Text.CVal(cfglogic.ScheEditImpossibleData.Value), logic.ScheduleViewData(i).UserID)

            For j As Integer = 0 To logic.ScheduleViewData(i).DataCount - 1
                If logic.ScheduleViewData(i).DayData(j).DataCount = 0 Then
                    Dim cell As New TableCell
                    Dim row As New TableRow
                    Dim usernamecell As New TableCell

                    usernamecell.CssClass = "schedule_cell_normal"
                    If edit Then
                        usernamecell.Text = "<div class=""schedule_editbutton"" style=""float:right;"">" &
                                                "<a href=""?cmd=system/Scheduler&action=view&userid=" & logic.ScheduleViewData(i).UserID & "&date=" & startday.ToString("yyyy/MM/dd") & """><img src=""images/ico_edit.png"" title=""" & startday.ToString("yyyy/MM/dd") & "の予定を追加"" /></a>&nbsp;" &
                                                "<a href=""?cmd=system/Scheduler&action=return&userid=" & logic.ScheduleViewData(i).UserID & "&date=" & startday.ToString("yyyy/MM/dd") & """><img src=""images/ico_return.png"" title=""" & startday.ToString("yyyy/MM/dd") & "の帰社予定時間を登録"" /></a>" &
                                            "</div>"
                    End If
                    usernamecell.Text &= make_user_imagebutton(logic.ScheduleViewData(i).UserID, logic.ScheduleViewData(i).UserName)
                    usernamecell.Text &= "<div class=""clear""></div>"
                    usernamecell.RowSpan = logic.ScheduleViewData(i).DataCount
                    row.Cells.Add(usernamecell)
                    For l As Integer = 0 To (eth - sth) * 4 + 3
                        cell = New TableCell
                        cell.CssClass = "schedule_cell_normal"
                        If edit Then
                            cell.Attributes.Add("id", "pick:" & logic.ScheduleViewData(i).UserID & ":" & startday.ToString("yyyy/MM/dd") & ":" & l)
                            cell.Style.Add("cursor", "pointer")
                        End If
                        row.Cells.Add(cell)
                    Next
                    TableSchedule.Rows.Add(row)
                Else
                    For k As Integer = 0 To logic.ScheduleViewData(i).DayData(j).DataCount - 1
                        Dim row As New TableRow
                        Dim usernamecell As New TableCell

                        If k = 0 Then
                            usernamecell.CssClass = "schedule_cell_normal"
                            If edit Then
                                usernamecell.Text = "<div class=""schedule_editbutton"" style=""float:right;"">" &
                                                        "<a href=""?cmd=system/Scheduler&action=view&userid=" & logic.ScheduleViewData(i).UserID & "&date=" & startday.ToString("yyyy/MM/dd") & """><img src=""images/ico_edit.png"" title=""" & startday.ToString("yyyy/MM/dd") & "の予定を追加"" /></a>&nbsp;" &
                                                        "<a href=""?cmd=system/Scheduler&action=return&userid=" & logic.ScheduleViewData(i).UserID & "&date=" & startday.ToString("yyyy/MM/dd") & """><img src=""images/ico_return.png"" title=""" & startday.ToString("yyyy/MM/dd") & "の帰社予定時間を登録"" /></a>" &
                                                    "</div>"
                                'Else
                                '    usernamecell.Text = "<div class=""schedule_editbutton"" style=""float:right;"">" & _
                                '                         "<a href=""?cmd=system/Scheduler&action=view&userid=" & logic.ScheduleViewData(i).UserID & "&date=" & startday.ToString("yyyy/MM/dd") & """><img src=""images/ico_view.png"" title=""この日の予定を見る"" /></a>" & _
                                '                         "</div>"
                            End If
                            usernamecell.Text &= make_user_imagebutton(logic.ScheduleViewData(i).UserID, logic.ScheduleViewData(i).UserName)
                            usernamecell.Text &= "<div class=""clear""></div>"
                            usernamecell.RowSpan = logic.ScheduleViewData(i).DayData(j).DataCount
                            row.Cells.Add(usernamecell)
                        End If

                        With logic.ScheduleViewData(i).DayData(j).TimeData(k)
                            Dim cell As New TableCell
                            Dim stpos As Integer
                            Dim edpos As Integer
                            Dim action As String
                            Dim color As String
                            Dim flg As Boolean
                            Dim height As Integer

                            If .StartTime.IndexOf(":") = -1 Then
                                stpos = 0
                            Else
                                stpos = (Text.CVal(.StartTime.Split(":")(0)) - sth) * 4 + (Text.CVal(.StartTime.Split(":")(1)) / 60 * 4)
                            End If
                            If .EndTime.IndexOf(":") = -1 Then
                                edpos = (eth - sth) * 4 + 4
                            Else
                                edpos = (Text.CVal(.EndTime.Split(":")(0)) - sth) * 4 + (Text.CVal(.EndTime.Split(":")(1)) / 60 * 4)
                            End If
                            If .ScheduleDataType = ScheduleManager.emScheduleDataType.Schedule Then
                                action = "view"
                                color = "schedule_cell_data" & .Color
                            Else
                                action = "return"
                                If .EndTime = "" Then
                                    color = "schedule_cell_data_dreturn"
                                Else
                                    color = "schedule_cell_data_return"
                                End If
                            End If

                            cell.Text &= "<div class=""schedule_subject_daily"">" &
                                             "<a href=""?cmd=system/Scheduler&action=" & action & "&id=" & .ID & """>" & .Subject1 & "</a>&nbsp;" &
                                             .StartTime &
                                             IIf(.StartTime = "" Or .EndTime = "", "→", "～") &
                                             .EndTime &
                                             IIf(.Subject2 <> "", "<br />" & .Subject2, "") &
                                         "</div>"

                            If .Subject2 <> "" Then
                                height = 40
                            Else
                                height = 22
                            End If

                            flg = False
                            Dim cnt As Integer = 0
                            For l As Integer = 0 To (eth - sth) * 4 + 3
                                If l >= stpos And l < edpos Then
                                    If Not flg Then
                                        cell.ColumnSpan = edpos - stpos
                                        cell.Width = cell.ColumnSpan * 10
                                        cell.CssClass = color
                                        cell.Height = height
                                        row.Cells.Add(cell)
                                        flg = True
                                    End If
                                Else
                                    Dim hcell As New TableCell
                                    hcell.CssClass = "schedule_cell_normal"
                                    If edit Then
                                        hcell.Attributes.Add("id", "pick:" & logic.ScheduleViewData(i).UserID & ":" & startday.ToString("yyyy/MM/dd") & ":" & cnt)
                                        hcell.Style.Add("cursor", "pointer")
                                    End If
                                    row.Cells.Add(hcell)
                                End If
                                cnt += 1
                            Next
                        End With
                        TableSchedule.Rows.Add(row)
                    Next
                End If
            Next
        Next
    End Sub

#End Region

#Region "月表示"

    '''=============================================================================================
    ''' <summary>
    ''' 月表示
    ''' </summary>
    ''' <param name="startday">表示開始日</param>
    ''' <param name="groupid">グループID(省略時は全グループ)</param>
    ''' <param name="userid">ユーザーID(省略時は全ユーザー)</param>
    '''=============================================================================================
    Private Sub read_monthly_table(startday As Date, Optional groupid As String = "", Optional userid As String = "")
        Dim cal As New Calendar
        Dim flg As Boolean
        Dim stp As Integer
        Dim edp As Integer
        Dim loopmax As Integer
        Dim day As Integer
        Dim action As String
        Dim color As String
        Dim holidayname As String = ""
        Dim edit As Boolean

        ' 日本の祝日を取得する
        Dim holidays As Calendar.NationalHoliday() = cal.GetNationalHolidays(startday.ToString("yyyy/MM/01"), startday.AddDays(31))

        TableSchedule.Rows.Clear()

        If Not logic.ReadScheduleViewData(ScheduleManager.emViewType.Monthly, startday, "", groupid, userid) Then
            Base.ErrorMessage = logic.LastError
            Return
        End If

        Dim headerrow As New TableRow
        Dim headercell As New TableCell
        headercell.Text = ""
        'headercell.Width = 200
        headercell.CssClass = "schedule_cell_day"
        headercell.RowSpan = "2"
        headerrow.Cells.Add(headercell)
        For j As Integer = 0 To 0
            Dim cell As New TableCell
            cell.Text = startday.ToString("yyyy年M月")
            cell.CssClass = "schedule_cell_day"
            cell.ColumnSpan = 7
            cell.CssClass = "schedule_cell_day"
            headerrow.Cells.Add(cell)
        Next
        TableSchedule.Rows.Add(headerrow)

        For i As Integer = 0 To logic.DataCount - 1
            headerrow = New TableRow
            If i > 0 Then
                headercell = New TableCell
                headercell.Text = ""
                headercell.CssClass = "schedule_cell_day"
                headerrow.Cells.Add(headercell)
            End If
            For j As Integer = 1 To 7
                Dim cell As New TableCell

                Select Case j
                    Case 7
                        cell.CssClass = "schedule_cell_holiday1"
                    Case 1
                        cell.CssClass = "schedule_cell_holiday2"
                    Case Else
                        cell.CssClass = "schedule_cell_day"
                End Select
                cell.Text = WeekdayName(j).Substring(0, 1)
                cell.Width = Unit.Percentage(12)
                headerrow.Cells.Add(cell)
            Next
            TableSchedule.Rows.Add(headerrow)

            Dim row As New TableRow

            flg = False
            stp = Weekday(CDate(startday.ToString("yyyy/MM/01")))
            edp = stp + Text.GetDays(startday.Year, startday.Month) - 1
            loopmax = edp + IIf(edp Mod 7 <> 0, (7 - (edp Mod 7)), 0)
            day = 1

            row = New TableRow

            Dim usernamecell As New TableCell
            usernamecell.CssClass = "schedule_cell_normal"
            usernamecell.Text = make_user_imagebutton(logic.ScheduleViewData(i).UserID, logic.ScheduleViewData(i).UserName)
            usernamecell.RowSpan = Math.Ceiling(edp / 7)
            row.Cells.Add(usernamecell)

            edit = logic.CheckEditAuthByUserID(Text.CVal(cfglogic.ScheEditImpossibleData.Value), logic.ScheduleViewData(i).UserID)

            For j As Integer = 1 To loopmax
                Dim cell As New TableCell
                cell.CssClass = "schedule_cell_normal"
                If j >= stp And j <= edp Then
                    cell.CssClass = "schedule_cell_normal"
                    If cal.IsHoliday(CDate(startday.ToString("yyyy/MM/" & day)), holidays, holidayname) Then
                        cell.Text = "<div class=""schedule_cell_holiday2"" style=""float:left;"">"
                        cell.ToolTip = holidayname
                    Else
                        If CDate(startday.ToString("yyyy/MM/" & day)).ToString("yyyy/MM/dd") = Now.ToString("yyyy/MM/dd") Then
                            cell.Text = "<div class=""schedule_cell_today"" style=""float:left;"">"
                        Else
                            cell.Text = "<div class=""schedule_editbutton"" style=""float:left;"">"
                        End If
                    End If
                    cell.Text &= "<a href=""?cmd=system/Scheduler&action=dailyview&date=" & logic.ScheduleViewData(i).DayData(day - 1).Day.ToString("yyyy/MM/dd") & "&userid="">" & day & "</a></div>"
                    '"<div class=""schedule_cell_name"">&nbsp;" & _
                    'logic.ScheduleViewData(i).UserName & _
                    '"</div>"

                    If edit Then
                        cell.Text &= "<div class=""schedule_editbutton"" style=""float:right;"">" &
                                         "<a href=""?cmd=system/Scheduler&action=view&userid=" & logic.ScheduleViewData(i).UserID & "&date=" & logic.ScheduleViewData(i).DayData(day - 1).Day.ToString("yyyy/MM/dd") & """><img src=""images/ico_edit.png"" title=""" & logic.ScheduleViewData(i).DayData(day - 1).Day.ToString("yyyy/MM/dd") & "の予定を追加"" /></a>&nbsp;" &
                                         "<a href=""?cmd=system/Scheduler&action=return&userid=" & logic.ScheduleViewData(i).UserID & "&date=" & logic.ScheduleViewData(i).DayData(day - 1).Day.ToString("yyyy/MM/dd") & """><img src=""images/ico_return.png"" title=""" & logic.ScheduleViewData(i).DayData(day - 1).Day.ToString("yyyy/MM/dd") & "の帰社予定時間を登録"" /></a>" &
                                     "</div>"
                        'ElseIf logic.ScheduleViewData(i).DayData(day - 1).DataCount > 0 Then
                        '    cell.Text &= "<div class=""schedule_editbutton"" style=""float:right;"">" & _
                        '                 "<a href=""?cmd=system/Scheduler&action=view&userid=" & logic.ScheduleViewData(i).UserID & "&date=" & logic.ScheduleViewData(i).DayData(day - 1).Day.ToString("yyyy/MM/dd") & """><img src=""images/ico_view.png"" title=""この日の予定を見る"" /></a>" & _
                        '                 "</div>"
                    End If
                    cell.Text &= "<div class=""clear"">"
                    For k As Integer = 0 To logic.ScheduleViewData(i).DataCount - 1
                        If logic.ScheduleViewData(i).DayData(k).Day = CDate(startday.ToString("yyyy/MM/" & day)) Then
                            For l As Integer = 0 To logic.ScheduleViewData(i).DayData(k).DataCount - 1
                                With logic.ScheduleViewData(i).DayData(k).TimeData(l)
                                    If .ScheduleDataType = ScheduleManager.emScheduleDataType.Schedule Then
                                        action = "view"
                                        color = "schedule_cell_data" & .Color
                                    Else
                                        action = "return"
                                        If .EndTime = "" Then
                                            color = "schedule_cell_data_dreturn"
                                        Else
                                            color = "schedule_cell_data_return"
                                        End If
                                    End If

                                    cell.Text &= "<div class=""" & color & """>" &
                                                     "<div class=""schedule_subject"">" &
                                                         "<a href=""?cmd=system/Scheduler&action=" & action & "&id=" & .ID & """>" & .Subject1 & "</a>" & IIf(.Subject2 <> "", "<br />" & .Subject2, "") &
                                                     "</div>" &
                                                     "<div class=""schedule_time"">" &
                                                         .StartTime &
                                                         IIf(.StartTime = "" Or .EndTime = "", "→", "～") &
                                                         .EndTime &
                                                     "</div>" &
                                                     "<div class=""clear""></div>" &
                                                 "</div>"
                                End With
                            Next
                        End If
                    Next

                    day += 1
                End If
                row.Cells.Add(cell)

                If j Mod 7 = 0 Or j = loopmax Then
                    TableSchedule.Rows.Add(row)
                    row = New TableRow
                End If
            Next
        Next
    End Sub

#End Region

#Region "その他共通処理"

    '''=============================================================================================
    ''' <summary>
    ''' 開始曜日を基準に、指定された日付を含む週の開始日を計算する。
    ''' </summary>
    ''' <param name="startweek">開始曜日(1:日曜～7:土曜)</param>
    ''' <param name="startday">日付</param>
    '''=============================================================================================
    Private Function get_startweek_day(startweek As Integer, startday As Date) As Date
        If startweek <> 0 Then
            Dim week As Integer = Weekday(startday)
            If week >= startweek Then
                startday = startday.AddDays((week - startweek) * -1)
            Else
                startday = startday.AddDays((7 - (startweek - week)) * -1)
            End If
        End If
        Return startday
    End Function

    '''=============================================================================================
    ''' <summary>
    ''' スケジュールデータの読込処理
    ''' </summary>
    '''=============================================================================================
    Private Sub read_data()
        If Not logic.ReadMainData(True) Then
            Base.ErrorMessage = logic.LastError
            Return
        End If

        ColorSelector.SelectedColor = CInt(Text.CVal(logic.色.Value))

        Dim cfg As New SystemConfigReader(Base.Security)
        cfg.ReadMainData(True)
        PanelCopy.Visible = (logic.EditMode.Value = Field.EditModeContents.emInsertMode)
        'If Text.CVal(cfg.ScheEditImpossibleData.Value) = 2 Then
        '    ' 編集可能データが「全て」の場合は、追加モード時のみ全ユーザーがコピー機能使用可能
        '    PanelCopy.Visible = (logic.EditMode.Value = Field.EditModeContents.emInsertMode)
        'Else
        '    ' 編集可能データが「全て」以外の場合は、追加モード時およびユーザー管理者のみがコピー機能使用可能
        '    PanelCopy.Visible = (logic.EditMode.Value = Field.EditModeContents.emInsertMode And Base.Security.IsUserAdmin)
        'End If
        ButtonDelete.Enabled = (logic.EditMode.Value = Field.EditModeContents.emUpdateMode)

        If cfg.ScheCopyUserType.Value = 1 Then
            Dim dl As New DataList(Base.Security)
            Base.UICommon.ListControlDataBind(ListPositionUserIDList, dl.UserList(True))
            ButtonAddUser.Visible = False
        Else
            ListPositionUserIDList.Items.Clear()
            ButtonAddUser.Visible = True
        End If
    End Sub

    '''=============================================================================================
    ''' <summary>
    ''' ユーザーイメージのHTML作成
    ''' </summary>
    '''=============================================================================================
    Private Function make_user_imagebutton(userid As String, username As String) As String
        Dim rc As String = ""
        Dim grouplist As String() = logic.MasterName.GroupNameToArray(userid)
        Dim am As New AccountEditorManager(Nothing)
        Dim ymd As String = ""

        am.NoModuleAuth = True
        am.ユーザーID.Value = userid
        If am.ReadMainData(True) Then
            ymd = Text.CDateEx(am.更新日時.Value).ToString("yyyyMMddHHmmss")
        End If

        rc &= "<table><tr>"
        rc &= "<td><img src=""ajax.aspx?cmd=userimage&userid=" & userid & "&imageid=" & ymd & """ class=""userimageicon_thumnail"" /></td>"
        rc &= "<td><a href=""?cmd=system/Scheduler&filteruserid=" & HttpUtility.UrlEncode(userid) & """ style=""font-size:11pt;""><strong>" & username & "</strong></a></td>"
        rc &= "</tr></table>"
        rc &= "<span style=""font-size: 8pt;"">"
        If Not grouplist Is Nothing Then
            For i As Integer = 0 To grouplist.Length - 1
                rc &= IIf(i = 0, "", " / ") & "<a href=""?cmd=system/Scheduler&filtergroupid=" & HttpUtility.UrlEncode(grouplist(i).Split(vbTab)(0)) & """>" & grouplist(i).Split(vbTab)(1) & "</a>"
            Next
        End If
        rc &= "</span>"

        Return rc
    End Function

    '''=============================================================================================
    ''' <summary>
    ''' ユーザーが所属するグループのリストHTML作成
    ''' </summary>
    '''=============================================================================================
    Private Sub add_position_list_item(userid As String)
        If ListPositionUserIDList.Items.FindByValue(userid) Is Nothing Then
            Dim itm As New ListItem
            itm.Text = logic.MasterName.UserMaster(userid)
            itm.Value = userid
            itm.Selected = True
            ListPositionUserIDList.Items.Add(itm)
        End If
    End Sub

#End Region

End Class
