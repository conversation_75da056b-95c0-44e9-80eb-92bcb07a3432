﻿<%@ Control Language="VB" AutoEventWireup="false" CodeFile="Scheduler.ascx.vb" Inherits="Scheduler" %>
<%@ Register Src="../common/ColorSelector.ascx" TagName="ColorSelector" TagPrefix="uc1" %>
<%@ Register Assembly="Dbs.Asphalt.UI" Namespace="Dbs.Asphalt.UI" TagPrefix="cc1" %>

<div style="padding-bottom: 5px;">
  <div style="float: left;">
    <asp:Button runat="server" Text="週表示" ID="ButtonViewWeekly" CommandArgument="0" />
    <asp:Button runat="server" Text="日表示" ID="ButtonViewDaily" CommandArgument="1" />
    <asp:Button runat="server" Text="月表示" ID="ButtonViewMonthly" CommandArgument="2" />
    <asp:Button runat="server" Text="印刷" ID="ButtonPrint" />
  </div>
  <div style="float: left; padding-left: 10px;">
    <cc1:DropDownList runat="server" ID="DropFilter" AutoPostBack="true"></cc1:DropDownList>
    <asp:Button runat="server" ID="ButtonFinder" Text="検索" CssClass="button_small" />
  </div>
  <asp:Panel runat="server" ID="PanelViewButtons">
    <div style="float: right; padding-left: 10px;">
      <asp:Panel runat="server" ID="PanelStartWeek">
        表示開始曜日:
        <cc1:DropDownList runat="server" ID="DropStartWeek" AutoPostBack="true"></cc1:DropDownList>
      </asp:Panel>
    </div>
    <div style="float: right;">
      <asp:Button runat="server" Text="◀" ID="ButtonPrevMonth" ToolTip="前月" CommandArgument="-3" />
      <asp:Button runat="server" Text="◀" ID="ButtonPrevWeek" ToolTip="前週" CommandArgument="-2" />
      <asp:Button runat="server" Text="◀" ID="ButtonPrevDay" ToolTip="前日" CommandArgument="-1" />
      <asp:Button runat="server" ID="ButtonToday" CommandArgument="0" />
      <asp:Button runat="server" Text="▶" ID="ButtonNextDay" ToolTip="翌日" CommandArgument="1" />
      <asp:Button runat="server" Text="▶" ID="ButtonNextWeek" ToolTip="翌週" CommandArgument="2" />
      <asp:Button runat="server" Text="▶" ID="ButtonNextMonth" ToolTip="翌月" CommandArgument="3" />
    </div>
  </asp:Panel>
  <div class="clear"></div>
</div>

<div style="padding-bottom: 10px;">
  <asp:Table runat="server" ID="TableSchedule" CssClass="schedule_table"></asp:Table>
</div>

<asp:Panel runat="server" ID="PanelEdit" Visible="false">
  <cc1:ItemFrame runat="server">
    <asp:Label runat="server" ID="LabelNotice" CssClass="notice_message">このデータは閲覧のみ許可されています</asp:Label>
  
    <cc1:EditMode runat="server" ID="EditMode"></cc1:EditMode>
    
    <h2><asp:Label runat="server" ID="LabelDate"></asp:Label></h2>

    <div class="leftpane" style="margin-right: 20px; width: 100px;">
      <cc1:UserImage runat="server" ID="ImagePhoto" ViewUserName="Bottom" />
    </div>

    <div class="leftpane" style="padding-left: 20px; border-left: 2px solid #cccccc;">
      <cc1:TextBox runat="server" ID="TextID" Visible="false"></cc1:TextBox>
      <cc1:TextBox runat="server" ID="TextUserID" Visible="false"></cc1:TextBox>
      <cc1:TextBox runat="server" ID="TextScheduleType" Visible="false"></cc1:TextBox>

      <asp:UpdatePanel runat="server" ID="UpdateScheduleDateTime">
        <ContentTemplate>
          <table>
            <tr>
              <td class="grid_item">予定日時</td>
              <asp:Panel runat="server" ID="PanelScheduleEditorItem1">
                <td><cc1:TextBox runat="server" ID="TextStartDate" Format="tbDate" Columns="10" MaxLength="10" Font-Size="13" Width="100px"></cc1:TextBox></td>
                <td><cc1:DropDownList runat="server" ID="DropStartHH" Font-Size="13" AutoPostBack="true"></cc1:DropDownList>&nbsp;時&nbsp;<cc1:DropDownList runat="server" ID="DropStartMM" Font-Size="13" AutoPostBack="true"></cc1:DropDownList>&nbsp;分&nbsp;</td>
                <td>&nbsp;&nbsp;～&nbsp;&nbsp;</td>
                <td><cc1:TextBox runat="server" ID="TextEndDate" Format="tbDate" Columns="10" MaxLength="10" Font-Size="13" Width="100px"></cc1:TextBox></td>
              </asp:Panel>
              <td><cc1:DropDownList runat="server" ID="DropEndHH" Font-Size="13" AutoPostBack="true"></cc1:DropDownList>&nbsp;時&nbsp;<cc1:DropDownList runat="server" ID="DropEndMM" Font-Size="13" AutoPostBack="true"></cc1:DropDownList>&nbsp;分&nbsp;</td>
            </tr>
          </table>
          <asp:Panel runat="server" ID="PanelReturnTimeGuide">
            <table>
              <tr>
                <td class="grid_item"></td>
                <td class="guide" style="width:640px;">帰社せずに直帰するには、予定時間に時間を指定しないで下さい。</td>
              </tr>
            </table>
          </asp:Panel>

          <asp:Panel runat="server" ID="PanelScheduleEditorItem2">
            <asp:Panel runat="server" ID="PanelSubject1" Visible="false">
              <table>
                <tr>
                  <td class="grid_item"><asp:Label runat="server" ID="LabelSubject1" Text="件名1"></asp:Label></td>
                  <td><cc1:TextBox runat="server" ID="TextSubject1" width="640" IMEOn="true" MaxLength="1000"></cc1:TextBox></td>
                </tr>
              </table>
            </asp:Panel>
            <asp:Panel runat="server" ID="PanelSubject2" Visible="false">
              <table>
                <tr>
                  <td class="grid_item"><asp:Label runat="server" ID="LabelSubject2" Text="件名2"></asp:Label></td>
                  <td><cc1:TextBox runat="server" ID="TextSubject2" width="640" IMEOn="true" MaxLength="1000"></cc1:TextBox></td>
                </tr>
              </table>
            </asp:Panel>
            <asp:Panel runat="server" ID="PanelSubject3" Visible="false">
              <table>
                <tr>
                  <td class="grid_item"><asp:Label runat="server" ID="LabelSubject3" Text="件名3"></asp:Label></td>
                  <td><cc1:TextBox runat="server" ID="TextSubject3" width="640" IMEOn="true" MaxLength="1000"></cc1:TextBox></td>
                </tr>
              </table>
            </asp:Panel>
            <asp:Panel runat="server" ID="PanelSubject4" Visible="false">
              <table>
                <tr>
                  <td class="grid_item"><asp:Label runat="server" ID="LabelSubject4" Text="件名4"></asp:Label></td>
                  <td><cc1:TextBox runat="server" ID="TextSubject4" width="640" IMEOn="true" MaxLength="1000"></cc1:TextBox></td>
                </tr>
              </table>
            </asp:Panel>
            <asp:Panel runat="server" ID="PanelSubject5" Visible="false">
              <table>
                <tr>
                  <td class="grid_item"><asp:Label runat="server" ID="LabelSubject5" Text="件名5"></asp:Label></td>
                  <td><cc1:TextBox runat="server" ID="TextSubject5" width="640" IMEOn="true" MaxLength="1000"></cc1:TextBox></td>
                </tr>
              </table>
            </asp:Panel>
            <table>
              <tr>
                <td class="grid_item">詳細</td>
                <td><cc1:TextBox runat="server" ID="TextBody" TextMode="MultiLine" width="640" Height="100" IMEOn="true"></cc1:TextBox></td>
              </tr>
            </table>
            <table>
              <tr>
                <td class="grid_item">色</td>
                <td><uc1:ColorSelector ID="ColorSelector" runat="server" /></td>
              </tr>
            </table>
          </asp:Panel>
          <table>
            <tr>
              <td class="grid_item">編集可能ユーザー</td>
              <td><cc1:DropDownList runat="server" ID="DropAuthEditUser"></cc1:DropDownList></td>
            </tr>
          </table>
        </ContentTemplate>
      </asp:UpdatePanel>

      <hr />
      <asp:Panel runat="server" ID="PanelCopy">
        <table>
          <tr>
            <td class="grid_item">予定コピーユーザー</td>
            <td>
              <div class="input_normal" style="float: left; padding-left: 2px; width: 500px;">
                <cc1:CheckBoxList runat="server" ID="ListPositionUserIDList" RepeatDirection="Vertical" RepeatLayout="Flow" ListFrame="false"></cc1:CheckBoxList>
              </div>
              <div style="float: left; padding-left: 2px;">
                <asp:Button runat="server" ID="ButtonAddUser" Text="追加" CssClass="button_small" />
              </div>
              <div class="clear"></div>
            </td>
          </tr>
          <tr>
            <td class="grid_item"></td>
            <td class="guide" style="width:640px;">
              上記に選択されているユーザーに、この予定を自動的に複写します。同行者への予定割り当てが必要な場合は、コピーユーザーにそのユーザーを選択して下さい。<br />
              予定の新規追加時のコピー先として使用されるのみで、選択したユーザーリストが保存されるわけではありません。
            </td>
          </tr>
        </table>
        <hr />
      </asp:Panel>
      <table>
        <tr>
          <td class="grid_item">登録日時</td>
          <td><cc1:TextBox runat="server" ID="TextRegDate" ViewOnly="true" Width="120"></cc1:TextBox></td>
          <td><cc1:TextBox runat="server" ID="TextRegUserName" ViewOnly="true" Width="400"></cc1:TextBox></td>
        </tr>
      </table>
      <table>
        <tr>
          <td class="grid_item">更新日時</td>
          <td><cc1:TextBox runat="server" ID="TextModDate" ViewOnly="true" Width="120"></cc1:TextBox></td>
          <td><cc1:TextBox runat="server" ID="TextModUserName" ViewOnly="true" Width="400"></cc1:TextBox></td>
        </tr>
      </table>

      <hr />

      <div class="inframetoolbar">
        <asp:Button runat="server" ID="ButtonRegist" Text="　　　保存　　　" CssClass="buttonb" />
        <asp:Button runat="server" ID="ButtonDelete" Text="　削除　" CssClass="buttonr" />
        <asp:Button runat="server" ID="ButtonBack" Text="戻る" CssClass="button" />
      </div>
    </div>

    <div class="clear"></div>
  </cc1:ItemFrame>

</asp:Panel>

