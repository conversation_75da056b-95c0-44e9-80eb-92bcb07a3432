﻿Imports Dbs.Asphalt.UI
Imports Dbs.Asphalt.Core
Imports Dbs.Asphalt.Core.CoreLogic.System
Imports Dbs.Asphalt.Core.Common
Imports System.IO

Partial Class AccountEditor
    Inherits ModuleBase

    Private logic As AccountEditorManager

    Protected Overrides Sub OnInit(e As System.EventArgs)
        logic = New AccountEditorManager(Base.Security)

        logic.EditMode.Control(Field.ControlTypeContents.ctEditMode) = EditMode
        logic.ユーザーID.Control(Field.ControlTypeContents.ctTextBox) = TextUserID
        logic.ユーザー名.Control(Field.ControlTypeContents.ctTextBox) = TextUserName
        logic.公開メッセージ.Control(Field.ControlTypeContents.ctTextBox) = TextMessage
        logic.テーマ.Control(Field.ControlTypeContents.ctDropDownList) = DropTheme
        logic.文字サイズ.Control(Field.ControlTypeContents.ctDropDownList) = DropFontSize
        logic.パスワード.Control(Field.ControlTypeContents.ctTextBox) = TextPassword
        logic.パスワード確認.Control(Field.ControlTypeContents.ctTextBox) = TextPassword2
        logic.ユーザー名カナ.Control(Field.ControlTypeContents.ctTextBox) = TextUserKana
        logic.メールアドレス.Control(Field.ControlTypeContents.ctTextBox) = TextEMail
        logic.メールアドレス変更.Control(Field.ControlTypeContents.ctTextBox) = TextEMailChange
        logic.メールアドレス変更キー.Control(Field.ControlTypeContents.ctTextBox) = TextEMailChangeKey
        logic.利用開始日付.Control(Field.ControlTypeContents.ctTextBox) = TextStartDate
        logic.利用終了日付.Control(Field.ControlTypeContents.ctTextBox) = TextEndDate
        logic.権限区分.Control(Field.ControlTypeContents.ctRadioButtonList) = RadioAuthType
        logic.使用停止.Control(Field.ControlTypeContents.ctCheckBox) = CheckStoped
        logic.リトライ回数.Control(Field.ControlTypeContents.ctTextBox) = TextRetryCounter
        logic.ロックFLG.Control(Field.ControlTypeContents.ctCheckBox) = CheckLocked
        logic.ロック日時.Control(Field.ControlTypeContents.ctTextBox) = TextLockedDate
        logic.メモ.Control(Field.ControlTypeContents.ctTextBox) = TextMemo
        logic.登録日時.Control(Field.ControlTypeContents.ctTextBox) = TextRegDate
        logic.登録者名.Control(Field.ControlTypeContents.ctTextBox) = TextRegUserID
        logic.更新日時.Control(Field.ControlTypeContents.ctTextBox) = TextUpdDate
        logic.更新者名.Control(Field.ControlTypeContents.ctTextBox) = TextUpdUserID

        logic.種別.Value = 0            ' 編集対象の種別はユーザーのみ

        ' テーマのリストを取得
        DropTheme.Items.Clear()
        DropTheme.AddItem("標準テーマ", "")
        Dim buf As String = ""
        Dim path As String = Server.MapPath("./css")
        buf = Dir(path & "\*.*", FileAttribute.Directory)
        Do Until (buf = "")
            Try
                Dim datfile As String = path & "\" & buf & "\theme.css"
                Dim fc As New IO.StreamReader(datfile)
                Dim rec As String
                While (Not fc.EndOfStream)
                    rec = fc.ReadLine()
                    If rec.Trim.Length > 10 Then
                        If rec.Trim.Substring(0, 10) = "ThemeName:" Then
                            DropTheme.AddItem(rec.Trim.Substring(10), buf)
                            Exit While
                        End If
                    End If
                End While
                fc.Close()
            Catch ex As Exception
            End Try

            buf = Dir()
        Loop
        DropTheme.SelectedValue = Base.Security.UserTheme

        ' ユーザーの権限区分によってコントロールの表示制御
        PanelEditToolButton.Visible = Base.Security.IsUserAdmin
        PanelAccount.Visible = Base.Security.IsUserAdmin
        PanelAdmin.Visible = Base.Security.IsAdmin
        ButtonMailTest.Visible = Base.Security.IsAdmin
        ButtonDelete.Visible = Base.Security.IsUserAdmin

        ' Finderの読み込み
        Base.LoadFinder("UserMasterFinder", "UserMasterFinder")

        Base.AddPostbackTrrigerControl(ButtonRemoveImage, True, Message.MessageText("MS_QUESTDELETE"))
        Base.SetClientYesNoBox(ButtonMailTest, Message.MessageText("MS_QUESTSEND"))

        MyBase.OnInit(e)
    End Sub

    Protected Overrides Sub OnLoad(e As System.EventArgs)

        If Not IsPostBack Then
            TextUserID.Text = Base.Security.UserID
            read_data(True)
        End If

        MyBase.OnLoad(e)
    End Sub

    Private Sub AccountEditor_PreRender(sender As Object, e As EventArgs) Handles Me.PreRender
        If logic.EditMode.Value = Field.EditModeContents.emInsertMode Then
            TextUserID.Focus()
            TextUserID.Enabled = True
            ButtonDelete.Visible = False
        Else
            TextUserName.Focus()
            TextUserID.Enabled = False
            ButtonDelete.Visible = Base.Security.IsUserAdmin
        End If
    End Sub

    Public Overrides Sub FinderClose(findername As String, result As System.Collections.Hashtable, resulttargetid As String)
        Select Case findername
            Case "UserMasterFinder"
                TextUserID.Text = result("ID")
                read_data(True)
        End Select
    End Sub

    Public Overrides Sub FinderOpen()

    End Sub

    Public Overrides ReadOnly Property PageTitle() As String
        Get
            Return logic.Title
        End Get
    End Property

    Protected Sub ButtonRegist_Click(sender As Object, e As System.EventArgs) Handles ButtonRegist.Click
        If FilePhoto.FileName <> "" Then
            logic.写真.Value = FilePhoto.FileBytes
        End If

        If Not logic.WriteMainData Then
            Base.ErrorMessage = logic.LastError
            Return
        End If

        Base.Message = logic.LastMessage

        read_data(True)
    End Sub

    Protected Sub ButtonNew_Click(sender As Object, e As System.EventArgs) Handles ButtonNew.Click
        TextUserID.Text = ""
        read_data(True)
    End Sub

    Protected Sub ButtonFind_Click(sender As Object, e As System.EventArgs) Handles ButtonFind.Click
        Dim param As New Hashtable
        param("種別") = 0
        Base.OpenFinder("UserMasterFinder", param)
    End Sub

    Protected Sub ButtonDelete_Click(sender As Object, e As System.EventArgs) Handles ButtonDelete.Click
        If Not logic.DeleteMainData Then
            Base.ErrorMessage = logic.LastError
            Return
        End If

        Base.Message = logic.LastMessage

        ButtonNew_Click(ButtonNew, Nothing)
    End Sub

    Public Overrides ReadOnly Property RunAuth() As Dbs.Asphalt.Core.SystemLogic.Security.emUserAuth
        Get
            Return SystemLogic.Security.emUserAuth.一般
        End Get
    End Property

    Protected Sub ButtonMailTest_Click(sender As Object, e As System.EventArgs) Handles ButtonMailTest.Click
        Dim mail As New SendMail
        mail.Subject = "メールテスト"
        mail.SendTo = TextEMail.Text
        mail.Body = "このメールは、システムからのテストメールです。" & vbNewLine &
                    "覚えがない場合は、大変お手数ですがこのまま破棄して下さい。"
        If Not mail.SendMail() Then
            Base.ErrorMessage = mail.LastError
            TextEMail.IsError = True
            Return
        End If
        Base.Message = Message.MessageText("MS_SENDCOMPLETE", TextEMail.Text)
    End Sub

    Protected Sub ButtonRemoveImage_Click(sender As Object, e As System.EventArgs) Handles ButtonRemoveImage.Click
        If Not logic.DeleteImageData Then
            Base.ErrorMessage = logic.LastError
            Return
        End If

        Base.Message = logic.LastMessage

        read_data(True)
    End Sub

    Public Overrides Sub MessageBoxClose(messageboxname As String, result As Dbs.Asphalt.UI.ModuleBase.MessageBoxResult)

    End Sub

    Private Sub ButtonEMailChange_Click(sender As Object, e As EventArgs) Handles ButtonEMailChange.Click
        If Not logic.EmailChange Then
            Base.ErrorMessage = logic.LastError
            Return
        End If

        PanelEmailChange.Visible = False

        Base.Message = logic.LastMessage
    End Sub

    Private Sub ButtonEMailChangeCancel_Click(sender As Object, e As EventArgs) Handles ButtonEMailChangeCancel.Click
        If Not logic.EmailChangeCancel Then
            Base.ErrorMessage = logic.LastError
            Return
        End If

        PanelEmailChange.Visible = False
    End Sub

    Private Sub read_data(read_after_clear As Boolean)
        If Not logic.ReadMainData(read_after_clear) Then
            Base.ErrorMessage = logic.LastError
        End If

        ImagePhoto.UserID = TextUserID.Text

        PanelAdmin.Visible = (Base.Security.UserID <> TextUserID.Text And Base.Security.IsAdmin)
        PanelAccount.Visible = (Base.Security.UserID <> TextUserID.Text)
        RadioAuthType.Enabled = (Base.Security.UserID <> TextUserID.Text)
        PanelEmailChange.Visible = (logic.メールアドレス変更.Value <> "")
    End Sub

End Class
