﻿Imports System.IO
Imports System.Data
Imports Dbs.Asphalt.UI
Imports Dbs.Asphalt.Core
Imports Dbs.Asphalt.Core.CoreLogic.System
Imports Dbs.Asphalt.Core.Common

Partial Class TemplateUploader
    Inherits ModuleBase

    Private logic As TemplateEditorManager

    Protected Overrides Sub OnInit(e As System.EventArgs)

        logic = New TemplateEditorManager(Base.Security)
        logic.カテゴリ.Control(Field.ControlTypeContents.ctDropDownList) = DropCategory
        logic.上書きバックアップ.Control(Field.ControlTypeContents.ctCheckBox) = CheckBackup
        logic.Start()

        read_template_files()

        Base.AddPostbackTrrigerControl(ButtonUpload, True)

        MyBase.OnInit(e)
    End Sub

    Protected Overrides Sub OnLoad(e As EventArgs)
        read_template_files()

        MyBase.OnLoad(e)
    End Sub

    Public Overrides Sub FinderClose(findername As String, result As System.Collections.Hashtable, resulttargetid As String)

    End Sub

    Public Overrides Sub FinderOpen()

    End Sub

    Public Overrides ReadOnly Property PageTitle() As String
        Get
            Return logic.Title
        End Get
    End Property

    Public Overrides ReadOnly Property RunAuth() As Dbs.Asphalt.Core.SystemLogic.Security.emUserAuth
        Get
            Return SystemLogic.Security.emUserAuth.総合管理者
        End Get
    End Property

    Public Overrides Sub MessageBoxClose(messageboxname As String, result As Dbs.Asphalt.UI.ModuleBase.MessageBoxResult)

    End Sub

    Private Sub read_template_files()
        Dim cnt As Integer = 1
        Dim dv As DataView = logic.ReadTemplateList

        TableFileList.Rows.Clear()

        If dv Is Nothing Then
            LabelNoFile.Visible = True
            Return
        Else
            LabelNoFile.Visible = False
        End If

        Base.UICommon.AddTableRow(TableFileList, "テンプレートファイル名" & vbTab & "更新日時" & vbTab & "サイズ" & vbTab & "ダウンロード" & vbTab & "削除", "grid_header")
        For i As Integer = 0 To dv.Count - 1
            Base.UICommon.AddTableRow(TableFileList, dv(i)("テンプレート名") & vbTab &
                                                     IIf(dv(i)("更新日時") = "", dv(i)("登録日時"), dv(i)("更新日時")) & vbTab &
                                                     Text.TextFormat(dv(i)("データ").Length, 13, Text.FormatContents.tbCurrency).Trim & vbTab & "" & vbTab & "",
                                                     IIf(Path.GetExtension(dv(i)("テンプレート名")).ToLower = ".bak", "grid_alterrow", "grid_row"))

            Dim btn As New Button
            btn.Text = "ダウンロード"
            btn.CssClass = "button_smallb"
            btn.CommandArgument = dv(i)("テンプレート名")
            btn.ID = "ButtonDownload" & cnt
            Base.AddPostbackTrrigerControl(btn)
            AddHandler btn.Click, AddressOf Download_Click
            TableFileList.Rows(TableFileList.Rows.Count - 1).Cells(3).Controls.Add(btn)

            Dim btn2 As New Button
            btn2.Text = "×"
            btn2.CssClass = "button_smallr"
            btn2.CommandArgument = dv(i)("テンプレート名")
            btn2.ID = "ButtonDelete" & cnt
            Base.SetClientYesNoBox(btn2, Message.MessageText("MS_QUESTDELETE"))
            AddHandler btn2.Click, AddressOf Delete_Click
            TableFileList.Rows(TableFileList.Rows.Count - 1).Cells(4).Controls.Add(btn2)
            If Path.GetExtension(dv(i)("テンプレート名")).ToLower = ".bak" Then
                TableFileList.Rows(TableFileList.Rows.Count - 1).CssClass = "grid_light"
            End If

            cnt += 1
        Next

        For i As Integer = 1 To TableFileList.Rows.Count - 1
            TableFileList.Rows(i).Cells(1).Style.Add("text-align", "center")
            TableFileList.Rows(i).Cells(1).Style.Add("width", "150px")

            TableFileList.Rows(i).Cells(2).Style.Add("text-align", "right")
            TableFileList.Rows(i).Cells(2).Style.Add("width", "100px")

            TableFileList.Rows(i).Cells(3).Style.Add("text-align", "center")
            TableFileList.Rows(i).Cells(3).Style.Add("width", "100px")

            TableFileList.Rows(i).Cells(4).Style.Add("text-align", "center")
            TableFileList.Rows(i).Cells(4).Style.Add("width", "50px")
        Next
    End Sub

    Protected Sub Download_Click(sender As Object, e As System.EventArgs)
        logic.テンプレート名.Value = sender.CommandArgument
        If Not logic.ReadMainData(True) Then
            Base.ErrorMessage = logic.LastError
            Return
        End If
        Base.DownloadBinaryData(sender.CommandArgument, logic.データ.Value)
    End Sub

    Protected Sub Delete_Click(sender As Object, e As System.EventArgs)
        logic.テンプレート名.Value = sender.CommandArgument
        If Not logic.DeleteMainData() Then
            Base.ErrorMessage = logic.LastError
            Return
        End If
        read_template_files()
    End Sub

    Protected Sub ButtonUpload_Click(sender As Object, e As System.EventArgs) Handles ButtonUpload.Click
        Try
            If FileUpload.FileBytes.Length = 0 Then
                Base.ErrorMessage = Message.MessageText("EM_UPLOADDATAFAILURE")
                Return
            End If

            logic.テンプレート名.Value = FileUpload.FileName
            logic.データ.Value = FileUpload.FileBytes
            If Not logic.WriteMainData() Then
                Base.ErrorMessage = logic.LastError
            Else
                Base.Message = Message.MessageText("MS_UPLOADCOMPLETE", FileUpload.FileName)
            End If
        Catch ex As Exception
            Base.ErrorMessage = ex.Message
        End Try

        read_template_files()
    End Sub

    Private Sub DropCategory_SelectedIndexChanged(sender As Object, e As EventArgs) Handles DropCategory.SelectedIndexChanged
        read_template_files()
    End Sub

End Class
