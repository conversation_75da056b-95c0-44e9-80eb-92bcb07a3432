﻿<%@ Control Language="VB" AutoEventWireup="false" CodeFile="AuthModuleConfig.ascx.vb" Inherits="AuthModuleConfig" %>
<%@ Register Assembly="Dbs.Asphalt.UI" Namespace="Dbs.Asphalt.UI" TagPrefix="cc1" %>

<cc1:ItemFrame runat="server">
  ユーザーを選択(複数可)し、モジュールごとの権限をクリックして設定を行ってください。
  <hr />
  <cc1:CheckBox runat="server" ID="CheckAllUsers" Text="一覧全員に適用" />
  <cc1:CheckBox runat="server" ID="CheckAllModules" Text="全モジュールに適用" />
</cc1:ItemFrame>
R:読込権限 W:書込権限 D:削除権限 P:印刷権限<br />
<span class="guider">※読込権限または印刷権限がOFFの場合、メニューアイテム自体が非表示となります。</span>
<asp:table runat="server" ID="TableModuleList" CssClass="grid"></asp:table>

<asp:panel runat="server" ID="PanelToolbar">
    <cc1:ToolButton runat="server" ButtonImage="biSave" Text="保存" ID="ButtonRegist" PostbackTrriger="true" PostbackTrrigerFreeze="true" />
    <cc1:ToolButton runat="server" ButtonImage="biCheck" Text="ユーザー選択" ID="ButtonSelUser" />
</asp:panel>
