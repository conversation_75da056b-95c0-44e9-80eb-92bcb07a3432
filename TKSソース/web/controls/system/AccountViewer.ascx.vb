﻿Imports Dbs.Asphalt.UI
Imports Dbs.Asphalt.Core
Imports Dbs.Asphalt.Core.CoreLogic.System
Imports Dbs.Asphalt.Core.Common

Partial Class AccountViewer
    Inherits ModuleBase

    Private logic As AccountEditorManager

    Protected Overrides Sub OnInit(e As System.EventArgs)
        logic = New AccountEditorManager(Base.Security)
        logic.NoModuleAuth = True
        logic.ユーザーID.Control(Field.ControlTypeContents.ctTextBox) = TextUserID
        logic.ユーザー名.Control(Field.ControlTypeContents.ctTextBox) = TextUserName
        logic.ユーザー名カナ.Control(Field.ControlTypeContents.ctTextBox) = TextUserKana
        logic.メールアドレス.Control(Field.ControlTypeContents.ctTextBox) = TextEMail
        logic.公開メッセージ.Control(Field.ControlTypeContents.ctTextBox) = TextMessage

        If Not IsPostBack Then
            TextUserID.Text = Request("userid")
            read_data(True)
        End If

        MyBase.OnInit(e)
    End Sub

    Public Overrides Sub FinderClose(findername As String, result As System.Collections.Hashtable, resulttargetid As String)

    End Sub

    Public Overrides Sub FinderOpen()

    End Sub

    Public Overrides ReadOnly Property PageTitle() As String
        Get
            Return logic.Title
        End Get
    End Property

    Private Sub read_data(read_after_clear As Boolean)
        If Not logic.ReadMainData(read_after_clear) Then
            Base.ErrorMessage = logic.LastError
        End If

        TextEMail.NavigateUrl = "mailto:" & TextEMail.Text
        ImagePhoto.UserID = TextUserID.Text
        TextMessage.Text = (New UICommon(Nothing)).AutoLink(TextMessage.Text).Replace(vbNewLine, "<br />")
        TextPositionGroup.Text = New MasterName(Base.Security).GroupName(TextUserID.Text)
    End Sub

    Public Overrides ReadOnly Property RunAuth() As Dbs.Asphalt.Core.SystemLogic.Security.emUserAuth
        Get
            Return SystemLogic.Security.emUserAuth.一般
        End Get
    End Property

    Public Overrides Sub MessageBoxClose(messageboxname As String, result As Dbs.Asphalt.UI.ModuleBase.MessageBoxResult)

    End Sub

End Class
