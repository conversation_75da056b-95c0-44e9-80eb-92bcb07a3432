﻿<%@ Control Language="VB" AutoEventWireup="false" CodeFile="SystemKubunEditor.ascx.vb" Inherits="SystemKubunEditor" %>
<%@ Register Assembly="Dbs.Asphalt.UI" Namespace="Dbs.Asphalt.UI" TagPrefix="cc1" %>

<cc1:ItemFrame runat="server" ID="PanelMain">
  <cc1:ItemPanel runat="server" ItemWidth="0" Text="データ区分">
    <cc1:DropDownList runat="server" ID="DropDataKubun" AutoPostBack="true"></cc1:DropDownList>
  </cc1:ItemPanel>
  <span class="guider">システム区分の変更は、システムの動作に影響を与える可能性がありますので、十分ご注意ください。</span>
  <cc1:GridViewEx runat="server" ID="GridDetail" AllowPaging="false" AllowSorting="false" RowStyle-Wrap="false" RowStyle-VerticalAlign="Top" Width="" AllowInsertButton="true" AllowDeleteButton="true" AllowUpDownButton="true" AllowDeleteCheckBox="true">
    <Columns>
      <asp:TemplateField HeaderText="区分コード">
        <ItemTemplate><cc1:TextBox runat="server" ID="TextCondCode"></cc1:TextBox></ItemTemplate>
      </asp:TemplateField>
      <asp:TemplateField HeaderText="区分名">
        <ItemTemplate><cc1:TextBox runat="server" ID="TextCondName" IMEOn="true"></cc1:TextBox></ItemTemplate>
      </asp:TemplateField>
    </Columns>
  </cc1:GridViewEx>
</cc1:ItemFrame>

<asp:panel runat="server" ID="PanelToolbar">
  <cc1:ToolButton runat="server" ButtonImage="biSave" Text="保存" ID="ButtonRegist" PostbackTrriger="true" PostbackTrrigerFreeze="true" />
  <cc1:ToolButton runat="server" ButtonImage="biExcel" Text="ダウンロード" ID="ButtonDownload" PostbackTrriger="true" />
</asp:panel>
