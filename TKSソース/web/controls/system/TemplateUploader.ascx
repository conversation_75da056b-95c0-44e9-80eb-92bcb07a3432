﻿<%@ Control Language="VB" AutoEventWireup="false" CodeFile="TemplateUploader.ascx.vb" Inherits="TemplateUploader" %>
<%@ Register Assembly="Dbs.Asphalt.UI" Namespace="Dbs.Asphalt.UI" TagPrefix="cc1" %>

<cc1:ItemFrame runat="server" ID="PanelBase" Text="テンプレートファイルリスト">
  カテゴリ：<cc1:DropDownList runat="server" ID="DropCategory" KubunNoListDataName="テンプレートカテゴリ" AutoPostBack="true"></cc1:DropDownList><br />
  <span class="guide">以下のテンプレートファイルがサーバーにインストールされています。ダウンロードをクリックすると、セキュアにファイルをダウンロードできます。</span>
  <hr />
  <asp:Table runat="server" ID="TableFileList" CssClass="grid" Width="100%"></asp:Table>
  <asp:Label runat="server" ID="LabelNoFile" Text="テンプレートファイルはありません。" Font-Bold="true" Visible="false"></asp:Label>
</cc1:ItemFrame>

<cc1:ItemFrame runat="server" ID="PanelUpload" Text="テンプレートファイルアップロード">
  <span class="guide">以下のボックスから、テンプレートファイルをサーバーにアップロードできます。テンプレートファイルの名前および内容は、システムの既定に従った状態である必要があります。</span><br />
  <span class="guider" style="font-weight: bold;">【注意!!】システムの仕様外のテンプレートがアップロードされた場合、テンプレートを使用する処理が正常に動作しなくなる可能性があります。</span><br />
  <span class="guider" style="font-weight: bold;">【注意!!】アップロードするファイルは、必ず事前にウィルスチェックを行ってください。</span>
  <hr />
  <cc1:FileUpload runat="server" ID="FileUpload" Width="300" />
  <asp:Button runat="server" ID="ButtonUpload" Text="アップロード" CssClass="buttonr" />
  <cc1:CheckBox runat="server" ID="CheckBackup" Text="上書きバックアップ"></cc1:CheckBox>
</cc1:ItemFrame>
