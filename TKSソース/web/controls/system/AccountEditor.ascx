﻿<%@ Control Language="VB" AutoEventWireup="false" CodeFile="AccountEditor.ascx.vb" Inherits="AccountEditor" %>
<%@ Register Assembly="Dbs.Asphalt.UI" Namespace="Dbs.Asphalt.UI" TagPrefix="cc1" %>

<cc1:ItemFrame runat="server" Text="基本情報">
  <cc1:EditMode runat="server" ID="EditMode"></cc1:EditMode>

  <cc1:ItemPanel runat="server" Text="ユーザーID">
    <cc1:TextBox runat="server" ID="TextUserID"></cc1:TextBox>
  </cc1:ItemPanel>

  <hr />

  <cc1:ItemPanel runat="server" Text="ユーザー名">
    <cc1:TextBox runat="server" ID="TextUserName" IMEOn="true"></cc1:TextBox>
  </cc1:ItemPanel>

  <cc1:ItemPanel runat="server" Text="カナ">
    <cc1:TextBox runat="server" ID="TextUserKana" IMEOn="true" KanaParentTextBoxID="TextUserName"></cc1:TextBox>
  </cc1:ItemPanel>

  <hr />

  <cc1:ItemPanel runat="server" Text="写真">
    <cc1:UserImage runat="server" ID="ImagePhoto" /><br />
    <cc1:FileUpload runat="server" ID="FilePhoto" Accept="image/*" />
    <asp:Button runat="server" ID="ButtonRemoveImage" Text="写真を削除" CssClass="button_small" /><br />
    <span class="guide">※写真とする画像は、100×100ピクセル以下の正方形に近い画像を指定してください。</span>
  </cc1:ItemPanel>

  <hr />

  <cc1:ItemPanel runat="server" Text="メールアドレス">
    <cc1:TextBox runat="server" ID="TextEMail"></cc1:TextBox>
    <asp:Button runat="server" Text="送信テスト" ID="ButtonMailTest" CssClass="button_small" /><br />
    <span class="guider">
      ※メールアドレス変更時には、メールアドレス変更キーが変更後のメールアドレス宛てに送信されます。承認するまで変更されませんのでご注意ください。
    </span>
    <asp:Panel runat="server" ID="PanelEmailChange" Visible="false">
      <hr />
      <span class="guider">メールアドレスを変更するには、変更後のメールを受信し、メールに記載された変更キーを入力する必要があります。</span><br />
      <cc1:TextBox runat="server" ID="TextEMailChange" ReadOnly="true"></cc1:TextBox><br />
      <cc1:TextBox runat="server" ID="TextEMailChangeKey" ItemName="変更キー"></cc1:TextBox>
      <asp:Button runat="server" Text="メールアドレス変更" ID="ButtonEMailChange" CssClass="button_small" />
      <asp:Button runat="server" Text="変更しない" ID="ButtonEMailChangeCancel" CssClass="button_small" />
    </asp:Panel>
  </cc1:ItemPanel>

  <hr />

  <cc1:ItemPanel runat="server" Text="公開メッセージ">
    <cc1:TextBox runat="server" ID="TextMessage" TextMode="MultiLine" IMEOn="true" Width="800" Height="50"></cc1:TextBox><br />
    <span class="guide">※公開メッセージは、名前や連絡先と一緒に、自分のユーザーページに表示されます。</span>
  </cc1:ItemPanel>

  <hr />

  <cc1:ItemPanel runat="server" Text="テーマ">
    <cc1:DropDownList runat="server" ID="DropTheme"></cc1:DropDownList><br />
    <span class="guide">※画面の配色やデザインを変更します。</span>
  </cc1:ItemPanel>

  <cc1:ItemPanel runat="server" Text="文字サイズ" Visible="false">
    <cc1:DropDownList runat="server" ID="DropFontSize" KubunNoListDataName="文字サイズ"></cc1:DropDownList>
  </cc1:ItemPanel>

  <hr />

  <cc1:ItemPanel runat="server" Text="パスワード">
    <cc1:TextBox runat="server" ID="TextPassword" TextMode="Password"></cc1:TextBox>
  </cc1:ItemPanel>

  <cc1:ItemPanel runat="server" Text="(確認)">
    <cc1:TextBox runat="server" ID="TextPassword2" TextMode="Password"></cc1:TextBox>
  </cc1:ItemPanel>

</cc1:ItemFrame>

<cc1:ItemFrame runat="server" ID="PanelAccount" Text="アカウント情報">
  <cc1:ItemPanel runat="server" Text="利用期間">
    <cc1:TextBox runat="server" ID="TextStartDate"></cc1:TextBox>
    ～
    <cc1:TextBox runat="server" ID="TextEndDate"></cc1:TextBox>
  </cc1:ItemPanel>

  <hr />

  <cc1:ItemPanel runat="server" Text="アカウント状態">
    <cc1:CheckBox runat="server" id="CheckStoped" Text="使用停止" />
  </cc1:ItemPanel>

  <hr />

  <cc1:ItemPanel runat="server" Text="リトライ回数">
    <cc1:TextBox runat="server" ID="TextRetryCounter"></cc1:TextBox>
  </cc1:ItemPanel>

  <hr />

  <cc1:ItemPanel runat="server" Text="ロック状態">
    <cc1:CheckBox runat="server" id="CheckLocked" Text="アカウントロック中" />
  </cc1:ItemPanel>

  <cc1:ItemPanel runat="server" Text="ロック日時">
    <cc1:TextBox runat="server" ID="TextLockedDate" ReadOnly="true"></cc1:TextBox>
  </cc1:ItemPanel>
</cc1:ItemFrame>

<cc1:ItemFrame runat="server" ID="PanelAdmin" Text="管理情報">
  <cc1:ItemPanel runat="server" Text="ユーザー権限">
    <cc1:RadioButtonList runat="server" ID="RadioAuthType" RepeatDirection="Horizontal" RepeatLayout="Flow"></cc1:RadioButtonList>
  </cc1:ItemPanel>

  <hr />

  <cc1:ItemPanel runat="server" Text="メモ">
    <cc1:TextBox runat="server" ID="TextMemo" TextMode="MultiLine" IMEOn="true" Width="750" Height="50"></cc1:TextBox>
  </cc1:ItemPanel>

  <hr />
  <cc1:ItemPanel runat="server" Text="登録">
    <cc1:TextBox runat="server" ID="TextRegDate" ViewOnly="true"></cc1:TextBox>&nbsp;
    <cc1:TextBox runat="server" ID="TextRegUserID" ViewOnly="true"></cc1:TextBox>
  </cc1:ItemPanel>
  <cc1:ItemPanel runat="server" Text="更新">
    <cc1:TextBox runat="server" ID="TextUpdDate" ViewOnly="true"></cc1:TextBox>&nbsp;
    <cc1:TextBox runat="server" ID="TextUpdUserID" ViewOnly="true"></cc1:TextBox>
  </cc1:ItemPanel>
</cc1:ItemFrame>

<asp:panel runat="server" ID="PanelToolbar">
    <cc1:ToolButton runat="server" ButtonImage="biSave" Text="保存" ID="ButtonRegist" PostbackTrriger="true" PostbackTrrigerFreeze="true" />
    <cc1:ToolButton runat="server" ButtonImage="biDelete" Text="削除" ID="ButtonDelete" PostbackTrriger="true" PostbackTrrigerFreeze="true" />
    <asp:PlaceHolder runat="server" ID="PanelEditToolButton">
        <cc1:ToolButton runat="server" ButtonImage="biNew" Text="新規追加" ID="ButtonNew" />
        <cc1:ToolButton runat="server" ButtonImage="biFinder" Text="検索" ID="ButtonFind" />
    </asp:PlaceHolder>
</asp:panel>
