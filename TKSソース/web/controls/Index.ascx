﻿<%@ Control Language="VB" AutoEventWireup="false" CodeFile="Index.ascx.vb" Inherits="Index" %>
<%@ Register Src="common/GridPager.ascx" TagName="GridPager" TagPrefix="uc1" %>
<%@ Register Assembly="Dbs.Asphalt.UI" Namespace="Dbs.Asphalt.UI" TagPrefix="cc1" %>

<cc1:ItemFrame runat="server" ID="FrameFindItem">
  <div class="leftpane">
    <h2>記事リスト</h2>
  </div>
  <div class="rightpane">
    <cc1:TextBox runat="server" ID="TextKeyword" Columns="30"></cc1:TextBox>
    <asp:Button runat="server" ID="ButtonStart" Text="検索" CssClass="button_small" />
  </div>
  <div class="clear"></div>

  <asp:Panel runat="server" ID="FrameResult">
    <uc1:GridPager ID="GridPager1" runat="server" TargetGridViewControlID="GridResult" />
    <cc1:GridViewEx runat="server" ID="GridResult" DataSourceID="DataResult">
      <Columns>
        <asp:CommandField ButtonType="Button" ShowSelectButton="True" SelectText="見る" HeaderStyle-Wrap="false">
          <ItemStyle Width="1px" HorizontalAlign="Center" />
          <ControlStyle CssClass="button_small" />
        </asp:CommandField>
        <asp:BoundField DataField="投稿ID" HeaderText="投稿ID" ReadOnly="True" SortExpression="投稿ID" HeaderStyle-Wrap="false" />
        <asp:BoundField DataField="添付ファイルあり" HeaderText="添付ファイルあり" SortExpression="添付ファイルあり" HeaderStyle-Wrap="false" />
        <asp:TemplateField HeaderText="添付" SortExpression="添付ファイルあり" HeaderStyle-Wrap="false">
          <ItemStyle HorizontalAlign="Center" Width="30px" />
          <ItemTemplate>
            <asp:Image runat="server" ID="ImageClip" ImageUrl="../images/clip.gif" ToolTip="添付ファイルあり"></asp:Image>
          </ItemTemplate>
        </asp:TemplateField>
        <asp:BoundField DataField="件名" HeaderText="件名" ReadOnly="True" SortExpression="件名" HeaderStyle-Wrap="false" />
        <asp:BoundField DataField="投稿者名" HeaderText="投稿者名" SortExpression="投稿者名カナ" HeaderStyle-Wrap="false">
          <ItemStyle Width="200px" />
        </asp:BoundField>
        <asp:BoundField DataField="投稿日時" HeaderText="投稿日時" SortExpression="投稿日時" HeaderStyle-Wrap="false">
          <ItemStyle Width="160px" HorizontalAlign="center" />
        </asp:BoundField>
        <asp:BoundField DataField="返信数" HeaderText="返信数" SortExpression="返信数" HeaderStyle-Wrap="false">
          <ItemStyle Width="40px" HorizontalAlign="center" />
        </asp:BoundField>
      </Columns>
    </cc1:GridViewEx>
    <uc1:GridPager ID="GridPager2" runat="server" TargetGridViewControlID="GridResult" />
    <asp:ObjectDataSource runat="server" ID="DataResult" SelectMethod="GetFindData" TypeName="Dbs.Asphalt.Database.DbFinderTableAdapters.S掲示板検索TableAdapter" EnablePaging="True" SortParameterName="sortName" SelectCountMethod="GetFindDataCount">
      <SelectParameters>
        <asp:ControlParameter ControlID="TextKeyword" Name="キーワード" PropertyName="Text" Type="String" ConvertEmptyStringToNull="False" />
      </SelectParameters>
    </asp:ObjectDataSource>

    <br />
    <asp:Panel runat="server" ID="PanelForumEditorForm" CssClass="forum_open_button">
      <table>
        <tr>
          <td class="grid_item">件名:</td>
          <td><cc1:TextBox runat="server" ID="TextSubject" IMEOn="true" Width="770"></cc1:TextBox></td>
        </tr>
      </table>
      <hr />
      <table>
        <tr>
          <td class="grid_item">本文:</td>
          <td><cc1:TextBox runat="server" ID="TextBody" IMEOn="true" TextMode="MultiLine" Width="770" Height="100"></cc1:TextBox></td>
        </tr>
      </table>
      <hr />
      <table>
        <tr>
          <td class="grid_item">添付ファイル:</td>
          <td><cc1:FileUpload runat="server" ID="FileFile1" Width="400" /></td>
        </tr>
        <tr>
          <td class="grid_item">(3個まで)</td>
          <td><cc1:FileUpload runat="server" ID="FileFile2" Width="400" /></td>
        </tr>
        <tr>
          <td class="grid_item"></td>
          <td><cc1:FileUpload runat="server" ID="FileFile3" Width="400" /></td>
        </tr>
      </table>
      <hr />
      <div class="inframetoolbar">
        <asp:Button runat="server" ID="ButtonRegist" Text="　　投稿　　" CssClass="buttonb" />
      </div>
    </asp:Panel>
  </asp:Panel>
</cc1:ItemFrame>

<cc1:ItemFrame runat="server" ID="FrameItemViewer" Visible="false">
  <asp:Label runat="server" ID="LabelDataID" Visible="false"></asp:Label>
  <div class="datatitle">
    <asp:Button runat="server" ID="ButtonBack" Text="　◀　" CssClass="buttonr" ToolTip="一覧に戻る" />
    <asp:Label runat="server" ID="LabelSubject"></asp:Label>
    <asp:LinkButton runat="server" ID="ButtonDeleteData" Text="この投稿を削除" CssClass="delete_button" />
  </div>
  <div class="leftpane" style="margin-right: 20px; margin-bottom: 20px;">
    <cc1:UserImage runat="server" ID="ImageAuthor" AjaxUri="../ajax.aspx" />
  </div>
  <div class="leftpane" style="width:800px;">
    <asp:Label runat="server" ID="LabelAuthorName"></asp:Label>【<asp:Label runat="server" ID="LabelDataDateTime"></asp:Label>】<br /><br />
    <asp:Label runat="server" ID="LabelBody"></asp:Label>
  </div>
  <div class="clear"></div>
  <div style="text-align:center; width: 200px; float:left; padding-right: 20px; padding-bottom: 10px;">
    <asp:HyperLink runat="server" ID="LinkFile1" Target="_blank"><asp:Image runat="server" ID="ImageFile1" Width="80" /></asp:HyperLink><br />
    <asp:Label runat="server" id="LabelFile1"></asp:Label>
  </div>
  <div style="text-align:center; width: 200px; float:left; padding-right: 20px; padding-bottom: 10px;">
    <asp:HyperLink runat="server" ID="LinkFile2" Target="_blank"><asp:Image runat="server" ID="ImageFile2" Width="80" /></asp:HyperLink><br />
    <asp:Label runat="server" id="LabelFile2"></asp:Label>
  </div>
  <div style="text-align:center; width: 200px; float:left; padding-right: 20px; padding-bottom: 10px;">
    <asp:HyperLink runat="server" ID="LinkFile3" Target="_blank"><asp:Image runat="server" ID="ImageFile3" Width="80" /></asp:HyperLink><br />
    <asp:Label runat="server" id="LabelFile3"></asp:Label>
  </div>
  <div class="clear"></div>
  
  <div class="itemframe_small">
    <asp:Repeater runat="server" ID="RepeatComment" DataSourceID="DataComment">
      <ItemTemplate>
        <asp:Label runat="server" ID="LabelCommentDataID" Text='<%#DataBinder.Eval(Container.DataItem, "投稿ID")%>' Visible="false"></asp:Label>
        <asp:Label runat="server" ID="LabelCommentAuthorID" Text='<%#DataBinder.Eval(Container.DataItem, "投稿者ID")%>' Visible="false"></asp:Label>
        <asp:LinkButton runat="server" ID="ButtonDeleteComment" Text="コメントを削除" CssClass="delete_button" CommandName="DeleteComment" CommandArgument='<%#DataBinder.Eval(Container.DataItem, "投稿ID")%>' />
        <div class="leftpane" style="margin-right: 20px;">
          <cc1:UserImage runat="server" ID="ImageCommentAuthor" AjaxUri="../ajax.aspx" />
        </div>
        <div class="leftpane" style="width:750px;">
          <%#Replace(Base.UICommon.MakeUserDisplayHTML(DataBinder.Eval(Container.DataItem, "投稿者ID", "")), vbLf, "<br />")%>【<%#Replace(DataBinder.Eval(Container.DataItem, "投稿日時", ""), vbLf, "<br />")%>】<br /><br />
          <%#Replace((New UICommon(Nothing)).AutoLink(DataBinder.Eval(Container.DataItem, "本文", "")), vbLf, "<br />")%>
        </div>
        <div class="clear"></div>
        <hr />
      </ItemTemplate>
    </asp:Repeater>
    <asp:ObjectDataSource runat="server" ID="DataComment" SelectMethod="GetChildData" TypeName="Dbs.Asphalt.Database.DbSystemTableAdapters.S掲示板TableAdapter" EnablePaging="False">
      <SelectParameters>
        <asp:ControlParameter ControlID="LabelDataID" Name="親投稿ID" PropertyName="Text" Type="Decimal" ConvertEmptyStringToNull="False" />
      </SelectParameters>
    </asp:ObjectDataSource>
  
    <asp:Panel runat="server" ID="FrameComment">

      <div class="leftpane" style="margin-right: 20px;">
        <cc1:UserImage runat="server" ID="ImageMyComment" AjaxUri="../ajax.aspx" />
      </div>
      <div class="leftpane" style="width:750px; padding-bottom: 10px;">
        <cc1:TextBox runat="server" ID="TextComment" IMEOn="true" TextMode="MultiLine" Width="770" Height="100"></cc1:TextBox>
      </div>
      <div class="clear"></div>
      <div class="inframetoolbar">
        <asp:Button runat="server" ID="ButtonRegistComment" Text="コメント投稿" CssClass="buttonb" />
      </div>
    </asp:Panel>
  </div>
</cc1:ItemFrame>
