﻿Imports Dbs.Asphalt.Core
Imports Dbs.Asphalt.Core.Common
Imports Dbs.Asphalt.Core.SystemLogic
Imports Dbs.Asphalt.UI

Partial Class Login
    Inherits ModuleBase

    Private preset As PasswordResetManager

    Public Overrides ReadOnly Property PageTitle() As String
        Get
            Return "ユーザー認証"
        End Get
    End Property

    Protected Overrides Sub OnInit(e As System.EventArgs)
        If IsNothing(Session("LOGIN_ID")) Or Session("LOGIN_ID") = "" Then
            Try
                TextUserID.Text = Request.Cookies("Asphalt.NET")("userid").ToString
                CheckSaveID.Checked = Request.Cookies("Asphalt.NET")("saveid").ToString
            Catch ex As Exception
            End Try
        Else
            Try
                TextUserID.Text = Session("LOGIN_ID")
            Catch ex As Exception
            End Try
        End If

        preset = New PasswordResetManager(New Security(""))

        MyBase.OnInit(e)
    End Sub

    Protected Overrides Sub OnLoad(e As System.EventArgs)
        Dim cfg As New Configurator(Base.Security)

        ' メンテナンス中かどうか
        Dim mainte As String = cfg.GetConfigData("全般設定", "メンテナンス終了時間", "")
        If mainte <> "" And cfg.GetConfigData("全般設定", "メンテナンス端末", "") <> Base.Security.ClientID Then
            LabelMessage.Text = "<p style=""text-align: center; font-size: 120%; font-weight: bold;"">【 システムメンテナンス中 】</p>" &
                                "<p style=""text-align: center; font-size: 150%; font-weight: bold;"">" & mainte & "</p>" &
                                "現在、システムメンテナンス中のため、システムをご利用いただけません。<br />" &
                                "ご迷惑おかけ致しますが、ご了承いただけますようお願い申し上げます。"
            PanelLoginFormInner.Visible = False
        Else
            If Parameters("信頼登録") = True Then
                PanelLoginFormInner.Visible = False
                PanelSecondAuth.Visible = True
                LabelAuthKey.Text = Parameters("端末信頼キー")
            End If

            If Session("LOGIN_ID") <> "" Then
                If Parameters("メッセージ") <> "" Then
                    LabelMessage.Text = Parameters("メッセージ").ToString.Replace(vbNewLine, "<br />")
                    LabelMessage.CssClass = "notice_message_mini"
                Else
                    LabelMessage.Text = Base.Security.LastError.Replace(vbNewLine, "<br />")
                    LabelMessage.CssClass = "notice_message_mini"
                End If

                'Session("LOGIN_ID") = ""
                'Session("LOGIN_PASSWD") = ""
            Else
                If Parameters("メッセージ") <> "" Then
                    LabelMessage.Text = Parameters("メッセージ").ToString.Replace(vbNewLine, "<br />")
                    LabelMessage.CssClass = "notice_message_mini"
                Else
                    LabelMessage.Text = Message.MessageText("MS_LOGIN")
                    LabelMessage.CssClass = ""
                End If
            End If

            ' パスワードリセット機能
            If cfg.GetConfigData("全般設定", "パスワードリセット機能", "0").ToString.ToDecimal = 1 Then
                ' パスワードリセット用識別子が指定されている場合
                If Request("passwordresetid") IsNot Nothing Then
                    Dim userid As String = preset.GetUserIDbyPasswordResetID(Request("passwordresetid"))
                    If userid <> "" Then
                        TextResetPassword.Focus()
                        PanelPasswordResetSent.Visible = False
                        PanelGoPasswordResetForm.Visible = True
                    Else
                        LabelPasswordResetMessage2.Text = preset.LastError
                        LabelPasswordResetMessage2.Visible = True
                        PanelPasswordResetSent.Visible = True
                        PanelGoPasswordResetForm.Visible = False
                    End If

                    PanelLoginForm.Visible = False
                    PanelPasswordResetForm.Visible = False
                Else
                    PanelPasswordReset.Visible = True
                End If
            End If
        End If

        If PanelLoginForm.Visible Then
            If TextUserID.Text <> "" Then
                TextPasswd.Focus()
            Else
                TextUserID.Focus()
            End If
        End If

        MyBase.OnLoad(e)
    End Sub

    Protected Sub ButtonLogin_Click(sender As Object, e As System.EventArgs) Handles ButtonLogin.Click
        Session("LOGIN_RECLOG") = True              ' このアクションからのみログを記録する

        Session("LOGIN_ID") = TextUserID.Text
        Session("LOGIN_PASSWD") = TextPasswd.Text
        Session("LOGIN_SAVEID") = CheckSaveID.Checked
        If Request.Cookies("Asphalt.TrustKey." & TextUserID.Text) IsNot Nothing Then
            Session("LOGIN_TRUSTKEY") = Request.Cookies("Asphalt.TrustKey." & TextUserID.Text).Value
        Else
            Session("LOGIN_TRUSTKEY") = ""
        End If
        Base.RunCommand(Session("Asphalt.NET_urlparameters"))
    End Sub

    Public Overrides Sub FinderClose(findername As String, result As System.Collections.Hashtable, resulttargetid As String)

    End Sub

    Public Overrides Sub FinderOpen()

    End Sub

    Public Overrides ReadOnly Property RunAuth() As Dbs.Asphalt.Core.SystemLogic.Security.emUserAuth
        Get
            Return Security.emUserAuth.一般
        End Get
    End Property

    Public Overrides Sub MessageBoxClose(messageboxname As String, result As Dbs.Asphalt.UI.ModuleBase.MessageBoxResult)

    End Sub

#Region "パスワード再発行"

    Private Sub LinkPasswordReset_Click(sender As Object, e As EventArgs) Handles LinkPasswordReset.Click
        PanelLoginForm.Visible = False
        PanelPasswordResetForm.Visible = True
        PanelPasswordResetSent.Visible = False
    End Sub

    ''' <summary>指定メールアドレスにパスワードリセット用のURLを送信する。</summary>
    Private Sub ButtonSendMail_Click(sender As Object, e As EventArgs) Handles ButtonSendMail.Click
        PanelLoginForm.Visible = False
        PanelPasswordResetForm.Visible = False
        PanelPasswordResetSent.Visible = True

        If TextMailAddress.Text <> "" Then
            Dim body As String = "以下のURLにアクセスして、パスワードをリセットしてください。" & vbNewLine &
                                 "このURLは、24時間経過すると無効となります。" & vbNewLine &
                                 Base.GetBaseURL() & "?passwordresetid={id}" & vbNewLine & vbNewLine &
                                 "※このメールに心当たりがない場合は、今すぐ削除をお願いいたします。" & vbNewLine &
                                 "※このメールは送信用アドレスから送信されました。絶対に返信しないようにお願いいたします。"
            If Not preset.SendPasswordResetURL(TextMailAddress.Text, body) Then
                PanelLoginForm.Visible = False
                PanelPasswordResetForm.Visible = True
                PanelPasswordResetSent.Visible = False
                LabelResetFormMessage.Text = preset.LastError.Replace(vbNewLine, "<br />")
                LabelResetFormMessage.Visible = True
                Return
            End If
        Else
            PanelLoginForm.Visible = False
            PanelPasswordResetForm.Visible = True
            PanelPasswordResetSent.Visible = False
            LabelResetFormMessage.Text = "登録済みのメールアドレスを指定してください。"
            LabelResetFormMessage.Visible = True
            Return
        End If
    End Sub

    Private Sub ButtonBack_Click(sender As Object, e As EventArgs) Handles ButtonBack.Click, ButtonBack2.Click, ButtonBack3.Click
        PanelLoginForm.Visible = True
        PanelPasswordResetForm.Visible = False
        PanelPasswordResetSent.Visible = False

        If sender.ID = "ButtonBack3" Then
            Base.RunCommand("")
        End If
    End Sub

    ''' <summary>パスワードリセットを実行する。</summary>
    Private Sub ButtonGoReset_Click(sender As Object, e As EventArgs) Handles ButtonGoReset.Click
        If preset.GoReset(Request("passwordresetid"), TextResetPassword.Text, TextResetPassword2.Text) Then
            PanelGoPasswordResetForm.Visible = False
            PanelPasswordResetComplete.Visible = True
        Else
            LabelPasswordResetMessage2.Text = preset.LastError.Replace(vbNewLine, "<br />")
            LabelPasswordResetMessage2.Visible = True
        End If
    End Sub

    Private Sub ButtonBackLoginForm_Click(sender As Object, e As EventArgs) Handles ButtonBackLoginForm.Click
        LabelMessage.Text = ""
        LabelMessage.CssClass = ""
        PanelLoginFormInner.Visible = True
        PanelSecondAuth.Visible = False
    End Sub

    Private Sub ButtonSecondAuth_Click(sender As Object, e As EventArgs) Handles ButtonSecondAuth.Click
        Dim trustuserid As String

        trustuserid = Base.Security.SecondAuth(LabelAuthKey.Text, TextSecondPassword.Text)
        If trustuserid = "" Then
            Parameters("メッセージ") = Base.Security.LastError
        End If

        ' 端末に信頼登録
        Response.Cookies("Asphalt.TrustKey." & trustuserid).Secure = False
        Response.Cookies("Asphalt.TrustKey." & trustuserid).Value = LabelAuthKey.Text
        LabelMessage.Text = ""
        LabelMessage.CssClass = ""
        If CheckTrusted.Checked Then
            Response.Cookies("Asphalt.TrustKey." & trustuserid).Expires = Now.AddYears(10)
            LabelMessage.Text = "このブラウザは信頼する端末として登録されました。"
            LabelMessage.CssClass = "message_mini"
        Else
            LabelMessage.Text = "ログインが可能になりました。"
            LabelMessage.CssClass = "message_mini"
        End If

        PanelLoginFormInner.Visible = True
        PanelSecondAuth.Visible = False
    End Sub

#End Region

End Class
