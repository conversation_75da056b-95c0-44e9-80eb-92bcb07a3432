﻿Imports System.Data
Imports Dbs.Asphalt.UI
Imports Dbs.Asphalt.Core
Imports Dbs.Asphalt.Core.Common

Partial Class SystemLogFinder
    Inherits ModuleBase

    Protected Overrides Sub OnInit(e As System.EventArgs)
        MyBase.OnInit(e)
    End Sub

    Public Overrides Sub FinderClose(findername As String, result As System.Collections.Hashtable, resulttargetid As String)

    End Sub

    Public Overrides Sub FinderOpen()
        FrameResult.Visible = False
    End Sub

    Public Overrides ReadOnly Property PageTitle() As String
        Get
            Return "システムログ検索"
        End Get
    End Property

    Protected Sub ButtonStart_Click(sender As Object, e As System.EventArgs) Handles ButtonStart.Click
        GridResult.PageIndex = 0
        GridResult.DataBind()
        FrameResult.Visible = True
    End Sub

    Protected Sub GridResult_RowDataBound(sender As Object, e As GridViewExRowEventArgs) Handles GridResult.RowDataBound
        If e.Row.RowType = DataControlRowType.DataRow Then
            Dim dv As DataRowView = CType(e.Row.DataItem, DataRowView)

            e.RowCellFormat("日時", 10, Text.FormatContents.tbLongDateTime)

            ' ログの区分によって色分け
            Select Case dv("区分")
                Case Message.MessageText("LT_FAILURE"), Message.MessageText("LT_FATAL")
                    e.Row.ForeColor = Drawing.Color.Red
                Case Message.MessageText("LT_WARNNING")
                    e.Row.ForeColor = Drawing.Color.DarkGoldenrod
                Case Else
            End Select
        End If
    End Sub

    Protected Sub ButtonDownload_Click(sender As Object, e As System.EventArgs) Handles ButtonDownload.Click
        Base.DownloadExcel(DataResult.Select, GridResult, "systemlog.xls")
    End Sub

    Public Overrides ReadOnly Property RunAuth() As Dbs.Asphalt.Core.SystemLogic.Security.emUserAuth
        Get
            Return SystemLogic.Security.emUserAuth.総合管理者
        End Get
    End Property

    Public Overrides Sub MessageBoxClose(messageboxname As String, result As Dbs.Asphalt.UI.ModuleBase.MessageBoxResult)

    End Sub

End Class
