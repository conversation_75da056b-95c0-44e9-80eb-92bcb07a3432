﻿<%@ Control Language="VB" AutoEventWireup="false" CodeFile="UriageMeisaiFinder.ascx.vb" Inherits="UriageMeisaiFinder" %>
<%@ Register Assembly="Dbs.Asphalt.UI" Namespace="Dbs.Asphalt.UI" TagPrefix="cc1" %>
<%@ Register Src="../common/GridPager.ascx" TagName="GridPager" TagPrefix="uc1" %>

<cc1:ItemFrame runat="server" ID="FrameFindItem" Text="納品データ検索">
  <div class="itemframe_small">
    <div style="float: left;">
      <cc1:ItemPanel runat="server" Text="得意先">
        <cc1:TextBox runat="server" ID="Text得意先コード" ReadOnly="true"></cc1:TextBox>
      </cc1:ItemPanel>
    </div>

    <div class="leftpane">
      <cc1:ItemPanel runat="server" Text="工事">
        <cc1:TextBox runat="server" ID="Text工事コードF" ></cc1:TextBox>～<cc1:TextBox runat="server" ID="Text工事コードT" ></cc1:TextBox>
      </cc1:ItemPanel>
      <cc1:TextBox runat="server" ID="Text画面工事コード" Visible="false" ></cc1:TextBox>
    </div>

    <div class="clear"></div>

    <hr />

    <div class="inframetoolbar">
      <asp:Button runat="server" ID="ButtonStart" Text="　検索　" CssClass="button" />
    </div>
  </div>

  <asp:Panel runat="server" ID="FrameResult" Visible="false">
    <uc1:GridPager ID="GridPager1" runat="server" TargetGridViewControlID="GridResult" CheckBoxHeaderText="選択" CheckBoxPrimaryKeyHeaderText="売上行番号" />
    <cc1:GridViewEx runat="server" ID="GridResult" DataSourceID="DataResult">
      <Columns>
        <asp:TemplateField HeaderText="選択">
          <ItemStyle HorizontalAlign="Center" Width="30" />
          <ItemTemplate>
            <cc1:CheckBox runat="server" ID="CheckBox" />
          </ItemTemplate>
        </asp:TemplateField>
        <asp:CommandField ButtonType="Button" ShowSelectButton="True">
          <ItemStyle Width="1px" HorizontalAlign="Center" />
          <ControlStyle CssClass="button_small" />
        </asp:CommandField>
<%--        <asp:BoundField DataField="ID" HeaderText="ID" ReadOnly="True" SortExpression="ID">
          <ItemStyle Width="120px" />
        </asp:BoundField>--%>
        <asp:BoundField DataField="売上行番号" HeaderText="売上行番号" ReadOnly="True" SortExpression="売上行番号" HeaderStyle-Wrap="false" />
        <asp:BoundField DataField="売上番号" HeaderText="売上番号" ReadOnly="True" SortExpression="売上番号" HeaderStyle-Wrap="false" />
        <asp:BoundField DataField="工事コード" HeaderText="工事コード" ReadOnly="True" SortExpression="工事コード" HeaderStyle-Wrap="false" />
        <asp:BoundField DataField="工事名" HeaderText="工事名" ReadOnly="True" SortExpression="工事名" HeaderStyle-Wrap="false" />
        <asp:BoundField DataField="商品コード" HeaderText="商品コード" ReadOnly="True" SortExpression="商品コード" HeaderStyle-Wrap="false" />
        <asp:BoundField DataField="商品名" HeaderText="商品名" ReadOnly="True" SortExpression="商品名" HeaderStyle-Wrap="false" />
        <asp:BoundField DataField="数量" HeaderText="数量" ReadOnly="True" SortExpression="数量" HeaderStyle-Wrap="false" />
        <asp:BoundField DataField="単価" HeaderText="単価" ReadOnly="True" SortExpression="単価" HeaderStyle-Wrap="false" />
        <asp:BoundField DataField="開始日付" HeaderText="開始日付" ReadOnly="True" SortExpression="開始日付" HeaderStyle-Wrap="false" />
        <asp:BoundField DataField="終了日付" HeaderText="終了日付" ReadOnly="True" SortExpression="終了日付" HeaderStyle-Wrap="false" />
      </Columns>
    </cc1:GridViewEx>
      <uc1:GridPager ID="GridPager2" runat="server" TargetGridViewControlID="GridResult" />
      <asp:ObjectDataSource runat="server" ID="DataResult" SelectMethod="GetData" TypeName="Dbs.Application.Logic.Database.DbCalcTableAdapters.Fリース商品検索TableAdapter">
      <SelectParameters>
        <asp:ControlParameter ControlID="Text得意先コード" Name="得意先コード" PropertyName="Text" Type="String" ConvertEmptyStringToNull="False" />
        <asp:ControlParameter ControlID="Text工事コードF" Name="工事コードF" PropertyName="Text" Type="String" ConvertEmptyStringToNull="False" />
        <asp:ControlParameter ControlID="Text工事コードT" Name="工事コードT" PropertyName="Text" Type="String" ConvertEmptyStringToNull="False" />
      </SelectParameters>
    </asp:ObjectDataSource>
  </asp:Panel>
</cc1:ItemFrame>

<asp:panel runat="server" ID="PanelToolbar">
    <cc1:ToolButton runat="server" ButtonImage="biExcel" Text="ダウンロード" ID="ButtonDownload" PostbackTrriger="true" />
    <cc1:ToolButton runat="server" ButtonImage="biBack" Text="戻る" ID="ButtonBack" />
</asp:panel>
