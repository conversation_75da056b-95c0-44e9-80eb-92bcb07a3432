﻿Imports System.Data
Imports Dbs.Asphalt.UI
Imports Dbs.Asphalt.Core
Imports Dbs.Asphalt.Core.Common

Partial Class UriageMeisaiFinder
    Inherits ModuleBase

    Protected Overrides Sub OnInit(e As System.EventArgs)
        MyBase.OnInit(e)

        Text得意先コード.MaxLength = 6
        Text工事コードF.MaxLength = 4
        Text工事コードT.MaxLength = 4

        Text工事コードF.Format = TextBox.FormatContents.tbCode
        Text工事コードT.Format = TextBox.FormatContents.tbCode
    End Sub

    Public Overrides Sub FinderClose(findername As String, result As System.Collections.Hashtable, resulttargetid As String)

    End Sub

    Public Overrides Sub FinderOpen()
        GridResult.ColumnByHeaderText("選択").Visible = (Parameters("複数選択") = "可能")

        If Parameters("得意先コード") Is Nothing Then
            Parameters("得意先コード") = ""
        End If

        Text得意先コード.Text = Parameters("得意先コード").ToString
        Text工事コードF.Text = Parameters("工事コード").ToString
        Text工事コードT.Text = Parameters("工事コード").ToString
        Text画面工事コード.Text = Parameters("工事コード").ToString

        Text得意先コード.AddText = Parameters("得意先名").ToString

        FrameResult.Visible = False
    End Sub

    Public Overrides ReadOnly Property PageTitle() As String
        Get
            Return "リース商品検索"
        End Get
    End Property

    Protected Sub ButtonBack_Click(sender As Object, e As System.EventArgs) Handles ButtonBack.Click
        Base.CloseFinder(False, Nothing)
    End Sub

    Protected Sub ButtonStart_Click(sender As Object, e As System.EventArgs) Handles ButtonStart.Click
        GridResult.PageIndex = 0
        GridResult.DataBind()
        FrameResult.Visible = True
    End Sub

    Protected Sub GridResult_RowDataBound(sender As Object, e As Dbs.Asphalt.UI.GridViewExRowEventArgs) Handles GridResult.RowDataBound

        If e.Row.RowType = DataControlRowType.DataRow Then
            e.RowCell("数量").HorizontalAlign = HorizontalAlign.Right
            e.RowCellFormat("数量", 15, Text.FormatContents.tbCurrency, 2)

            e.RowCell("単価").HorizontalAlign = HorizontalAlign.Right
            e.RowCellFormat("単価", 15, Text.FormatContents.tbCurrency, 2)

            If e.RowCell("工事コード").Text <> Text画面工事コード.Text Then
                e.Row.BackColor = Drawing.Color.LightGray
                e.RowCell("選択").Enabled = False
                e.RowCell("").Enabled = False
            End If
        End If

        e.RowCell("売上行番号").Visible = False
    End Sub

    Protected Sub GridResult_SelectedIndexChanged(sender As Object, e As System.EventArgs) Handles GridResult.SelectedIndexChanged
        Dim result As New Hashtable
        Dim idlist As String = ""

        If GridResult.ColumnByHeaderText("選択").Visible Then
            GridPager1.SaveCheckCondition()
            If Not GridPager1.CheckCondition Is Nothing Then
                For Each v As Object In GridPager1.CheckCondition
                    idlist &= IIf(idlist = "", "", ",") & v.ToString
                Next
            End If
            If idlist <> "" Then
                result("選択値") = idlist
            Else
                result("選択値") = GridResult.SelectedRow("売上行番号").Text
            End If
        Else
            result("選択値") = GridResult.SelectedRow("売上行番号").Text
        End If
        Base.CloseFinder(True, result)
    End Sub

    Protected Sub ButtonDownload_Click(sender As Object, e As System.EventArgs) Handles ButtonDownload.Click
        Dim dv As DataView = CType(DataResult.Select, DataView)
        Base.DownloadExcel(dv, GridResult, "納品一覧.xlsx")
    End Sub

    Public Overrides ReadOnly Property RunAuth() As Dbs.Asphalt.Core.SystemLogic.Security.emUserAuth
        Get
            Return SystemLogic.Security.emUserAuth.一般
        End Get
    End Property

    Public Overrides Sub MessageBoxClose(messageboxname As String, result As Dbs.Asphalt.UI.ModuleBase.MessageBoxResult)

    End Sub
End Class
