﻿Imports System.Data
Imports Dbs.Asphalt.UI
Imports Dbs.Asphalt.Core
Imports Dbs.Asphalt.Core.Common

Partial Class UserMasterFinder
    Inherits ModuleBase

    Protected Overrides Sub OnInit(e As System.EventArgs)
        MyBase.OnInit(e)
    End Sub

    Public Overrides Sub FinderClose(findername As String, result As System.Collections.Hashtable, resulttargetid As String)

    End Sub

    Public Overrides Sub FinderOpen()
        GridResult.ColumnByHeaderText("選択").Visible = (Parameters("複数選択") = "可能")

        If Parameters("種別") Is Nothing Then
            Parameters("種別") = 0
        End If
        DataResult.SelectParameters("種別").DefaultValue = Parameters("種別")

        FrameResult.Visible = False
    End Sub

    Public Overrides ReadOnly Property PageTitle() As String
        Get
            Return "ユーザー・グループ検索"
        End Get
    End Property

    Protected Sub ButtonBack_Click(sender As Object, e As System.EventArgs) Handles ButtonBack.Click
        Base.CloseFinder(False, Nothing)
    End Sub

    Protected Sub ButtonStart_Click(sender As Object, e As System.EventArgs) Handles ButtonStart.Click
        GridResult.PageIndex = 0
        GridResult.DataBind()
        FrameResult.Visible = True
    End Sub

    Protected Sub GridResult_RowDataBound(sender As Object, e As Dbs.Asphalt.UI.GridViewExRowEventArgs) Handles GridResult.RowDataBound
        If e.Row.RowType = DataControlRowType.DataRow Then
            CType(e.RowCell("写真").FindControl("ImagePhoto"), UserImage).UserID = e.RowCell("ID").Text
        End If

        e.RowCell("ID").Visible = False
    End Sub

    Protected Sub GridResult_SelectedIndexChanged(sender As Object, e As System.EventArgs) Handles GridResult.SelectedIndexChanged
        Dim result As New Hashtable
        Dim idlist As String = ""

        If GridResult.ColumnByHeaderText("選択").Visible Then
            GridPager1.SaveCheckCondition()
            If Not GridPager1.CheckCondition Is Nothing Then
                For Each v As Object In GridPager1.CheckCondition
                    idlist &= IIf(idlist = "", "", ",") & v.ToString
                Next
            End If
            If idlist <> "" Then
                result("ID") = idlist
            Else
                result("ID") = GridResult.SelectedRow("ID").Text
            End If
        Else
            result("ID") = GridResult.SelectedRow("ID").Text
        End If
        Base.CloseFinder(True, result)
    End Sub

    Protected Sub ButtonDownload_Click(sender As Object, e As System.EventArgs) Handles ButtonDownload.Click
        Dim dv As DataView = CType(DataResult.Select, DataView)
        Base.DownloadExcel(dv, GridResult, "usermaster.xlsx")
    End Sub

    Public Overrides ReadOnly Property RunAuth() As Dbs.Asphalt.Core.SystemLogic.Security.emUserAuth
        Get
            Return SystemLogic.Security.emUserAuth.ユーザー管理者
        End Get
    End Property

    Public Overrides Sub MessageBoxClose(messageboxname As String, result As Dbs.Asphalt.UI.ModuleBase.MessageBoxResult)

    End Sub

End Class
