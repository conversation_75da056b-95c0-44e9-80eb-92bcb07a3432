﻿<%@ Control Language="VB" AutoEventWireup="false" CodeFile="CommonFinder.ascx.vb" Inherits="CommonFinder" %>
<%@ Register Assembly="Dbs.Asphalt.UI" Namespace="Dbs.Asphalt.UI" TagPrefix="cc1" %>
<%@ Register Src="../common/GridPager.ascx" TagName="GridPager" TagPrefix="uc1" %>

<cc1:ItemFrame runat="server" ID="FrameFindItem">
  <div class="leftpane">
    <cc1:DropDownList runat="server" ID="DropItem1" Width="200" AutoPostBack="true"></cc1:DropDownList>
  </div>
  <div class="leftpane" style="margin-left: 5px;">
    <asp:Panel runat="server" ID="PanelString1">
      <cc1:TextBox runat="server" ID="TextKeyword1" Width="600" IMEOn="true"></cc1:TextBox>
      <cc1:CheckBox runat="server" ID="CheckEmpty1" Text="空白検索" />
    </asp:Panel>
    <asp:Panel runat="server" ID="PanelDropdown1">
      <cc1:DropDownList runat="server" ID="DropKeyword1"></cc1:DropDownList>
    </asp:Panel>
    <asp:Panel runat="server" ID="PanelDate1">
      <cc1:TextBox runat="server" ID="TextFDate1" Columns="10" Format="tbDate"></cc1:TextBox>
      ～</td>
      <cc1:TextBox runat="server" ID="TextTDate1" Columns="10" Format="tbDate"></cc1:TextBox>
    </asp:Panel>
    <asp:Panel runat="server" ID="PanelNumeric1">
      <cc1:TextBox runat="server" ID="TextFNum1" Columns="10"></cc1:TextBox>
      ～</td>
      <cc1:TextBox runat="server" ID="TextTNum1" Columns="10"></cc1:TextBox>
    </asp:Panel>
  </div>
  <div class="clear"></div>

  <div class="leftpane">
    <cc1:DropDownList runat="server" ID="DropItem2" Width="200" AutoPostBack="true"></cc1:DropDownList>
  </div>
  <div class="leftpane" style="margin-left: 5px;">
    <asp:Panel runat="server" ID="PanelString2">
      <cc1:TextBox runat="server" ID="TextKeyword2" Width="600" IMEOn="true"></cc1:TextBox>
      <cc1:CheckBox runat="server" ID="CheckEmpty2" Text="空白検索" />
    </asp:Panel>
    <asp:Panel runat="server" ID="PanelDropdown2">
      <cc1:DropDownList runat="server" ID="DropKeyword2"></cc1:DropDownList>
    </asp:Panel>
    <asp:Panel runat="server" ID="PanelDate2">
      <cc1:TextBox runat="server" ID="TextFDate2" Columns="10" Format="tbDate"></cc1:TextBox>
      ～</td>
      <cc1:TextBox runat="server" ID="TextTDate2" Columns="10" Format="tbDate"></cc1:TextBox>
    </asp:Panel>
    <asp:Panel runat="server" ID="PanelNumeric2">
      <cc1:TextBox runat="server" ID="TextFNum2" Columns="10"></cc1:TextBox>
      ～</td>
      <cc1:TextBox runat="server" ID="TextTNum2" Columns="10"></cc1:TextBox>
    </asp:Panel>
  </div>
  <div class="clear"></div>

  <div class="leftpane">
    <cc1:DropDownList runat="server" ID="DropItem3" Width="200" AutoPostBack="true"></cc1:DropDownList>
  </div>
  <div class="leftpane" style="margin-left: 5px;">
    <asp:Panel runat="server" ID="PanelString3">
      <cc1:TextBox runat="server" ID="TextKeyword3" Width="600" IMEOn="true"></cc1:TextBox>
      <cc1:CheckBox runat="server" ID="CheckEmpty3" Text="空白検索" />
    </asp:Panel>
    <asp:Panel runat="server" ID="PanelDropdown3">
      <cc1:DropDownList runat="server" ID="DropKeyword3"></cc1:DropDownList>
    </asp:Panel>
    <asp:Panel runat="server" ID="PanelDate3">
      <cc1:TextBox runat="server" ID="TextFDate3" Columns="10" Format="tbDate"></cc1:TextBox>
      ～</td>
      <cc1:TextBox runat="server" ID="TextTDate3" Columns="10" Format="tbDate"></cc1:TextBox>
    </asp:Panel>
    <asp:Panel runat="server" ID="PanelNumeric3">
      <cc1:TextBox runat="server" ID="TextFNum3" Columns="10"></cc1:TextBox>
      ～</td>
      <cc1:TextBox runat="server" ID="TextTNum3" Columns="10"></cc1:TextBox>
    </asp:Panel>
  </div>
  <div class="clear"></div>

  <hr />
  <div class="inframetoolbar">
    <asp:Button runat="server" ID="ButtonStart" Text="　検索　" CssClass="button" />
  </div>
</cc1:ItemFrame>

<asp:Panel runat="server" ID="FrameResult" Visible="false">
  <uc1:GridPager ID="GridPager1" runat="server" TargetGridViewControlID="GridResult" />
  <cc1:GridViewEx runat="server" ID="GridResult" DataSourceID="DataResult" Width="" CssClass="commonfiner" HeaderStyle-Wrap="false">
    <Columns>
      <asp:TemplateField HeaderText="選択">
        <ItemStyle HorizontalAlign="Center" Width="30" />
        <ItemTemplate>
          <cc1:CheckBox runat="server" ID="CheckBox" />
        </ItemTemplate>
      </asp:TemplateField>
      <asp:CommandField ButtonType="Button" ShowSelectButton="True">
        <ItemStyle Width="1px" HorizontalAlign="Center" />
        <ControlStyle CssClass="button_small" />
      </asp:CommandField>
      <asp:TemplateField HeaderText="開く">
        <ItemStyle HorizontalAlign="Center" Width="30" />
      </asp:TemplateField>
    </Columns>
  </cc1:GridViewEx>
  <uc1:GridPager ID="GridPager2" runat="server" TargetGridViewControlID="GridResult" />
  <asp:ObjectDataSource runat="server" ID="DataResult" EnablePaging="True" SortParameterName="sortName" SelectMethod="GetData" SelectCountMethod="GetDataCount">
      <SelectParameters>
          <asp:Parameter Name="sqlname" Type="String" ConvertEmptyStringToNull="False" />
          <asp:Parameter Name="returnname" Type="String" ConvertEmptyStringToNull="False" />
          <asp:ControlParameter ControlID="DropItem1" Name="検索キー1" PropertyName="SelectedValue" Type="String" ConvertEmptyStringToNull="False" />
          <asp:ControlParameter ControlID="DropItem2" Name="検索キー2" PropertyName="SelectedValue" Type="String" ConvertEmptyStringToNull="False" />
          <asp:ControlParameter ControlID="DropItem3" Name="検索キー3" PropertyName="SelectedValue" Type="String" ConvertEmptyStringToNull="False" />
          <asp:Parameter Name="検索値1" Type="String" ConvertEmptyStringToNull="False" />
          <asp:Parameter Name="検索値2" Type="String" ConvertEmptyStringToNull="False" />
          <asp:Parameter Name="検索値3" Type="String" ConvertEmptyStringToNull="False" />
          <asp:Parameter Name="固定条件" Type="String" ConvertEmptyStringToNull="False" />
      </SelectParameters>
  </asp:ObjectDataSource>
</asp:Panel>

<cc1:ModalPanel runat="server" ID="PanelConfig" Visible="false">
  <div class="leftpane margin">
    非表示項目<br />
    <cc1:ListBox runat="server" ID="ListItemList" Width="240" Height="300" SelectionMode="Multiple" CssClass="input_normal"></cc1:ListBox>
  </div>
  <div class="leftpane margin">
    <br /><br /><br /><br />
    <asp:Button ID="ButtonAdd" runat="server" CssClass="button_small" text="追加 ▶" /><br /><br />
    <asp:Button ID="ButtonRem" runat="server" CssClass="button_small" text="◀ 削除" />
  </div>
  <div class="leftpane margin">
    表示項目<br />
    <cc1:ListBox runat="server" ID="ListViewList" Width="240" Height="300" SelectionMode="Multiple" CssClass="input_normal"></cc1:ListBox>
  </div>
  <div class="leftpane margin">
    <br /><br /><br /><br />
    <asp:Button ID="ButtonUp" runat="server" CssClass="button_small" text="▲" /><br /><br />
    <asp:Button ID="ButtonDown" runat="server" CssClass="button_small" text="▼" />
  </div>
  <div class="clear"></div>

  <hr />
  <div class="bottomtoolbar">
    <asp:button runat="server" text="　　　OK　　　" ID="ButtonOK" CssClass="button" />
    <asp:button runat="server" text="キャンセル" ID="ButtonCancel" CssClass="button" />
  </div>
</cc1:ModalPanel>

<asp:panel runat="server" ID="PanelToolbar">
    <cc1:ToolButton runat="server" ButtonImage="biExcel" Text="ダウンロード" ID="ButtonDownload" PostbackTrriger="true" />
    <cc1:ToolButton runat="server" ButtonImage="biConfig" Text="設定" ID="ButtonConfig" />
    <cc1:ToolButton runat="server" ButtonImage="biBack" Text="戻る" ID="ButtonBack" />
</asp:panel>
