﻿<%@ Control Language="VB" AutoEventWireup="false" CodeFile="UserMasterFinder.ascx.vb" Inherits="UserMasterFinder" %>
<%@ Register Assembly="Dbs.Asphalt.UI" Namespace="Dbs.Asphalt.UI" TagPrefix="cc1" %>
<%@ Register Src="../common/GridPager.ascx" TagName="GridPager" TagPrefix="uc1" %>

<cc1:ItemFrame runat="server" ID="FrameFindItem" Text="ユーザー検索">
  <div class="itemframe_small">
    <div style="float: left;">
      <cc1:ItemPanel runat="server" Text="ユーザーID">
        <cc1:TextBox runat="server" ID="TextUserID" width="100"></cc1:TextBox>
      </cc1:ItemPanel>
    </div>
    <div style="float: left;">
      <cc1:ItemPanel runat="server" Text="名前">
        <cc1:TextBox runat="server" ID="TextUserName" width="200" IMEOn="true"></cc1:TextBox>
      </cc1:ItemPanel>
    </div>
    <div class="clear"></div>
    <hr />
    <div class="inframetoolbar">
      <asp:Button runat="server" ID="ButtonStart" Text="　検索　" CssClass="button" />
    </div>
  </div>

  <asp:Panel runat="server" ID="FrameResult" Visible="false">
    <uc1:GridPager ID="GridPager1" runat="server" TargetGridViewControlID="GridResult" CheckBoxHeaderText="選択" CheckBoxPrimaryKeyHeaderText="ID" />
    <cc1:GridViewEx runat="server" ID="GridResult" DataKeyNames="ID" DataSourceID="DataResult">
      <Columns>
        <asp:TemplateField HeaderText="選択">
          <ItemStyle HorizontalAlign="Center" Width="30" />
          <ItemTemplate>
            <cc1:CheckBox runat="server" ID="CheckBox" />
          </ItemTemplate>
        </asp:TemplateField>
        <asp:CommandField ButtonType="Button" ShowSelectButton="True">
          <ItemStyle Width="1px" HorizontalAlign="Center" />
          <ControlStyle CssClass="button_small" />
        </asp:CommandField>
        <asp:TemplateField HeaderText="写真">
          <ItemStyle HorizontalAlign="Center" Width="10" />
          <ItemTemplate>
            <cc1:UserImage runat="server" ID="ImagePhoto" IsThumbnail="true" ajaxuri="../../ajax.aspx"></cc1:UserImage>
          </ItemTemplate>
        </asp:TemplateField>
        <asp:BoundField DataField="ID" HeaderText="ID" ReadOnly="True" SortExpression="ID">
          <ItemStyle Width="120px" />
        </asp:BoundField>
        <asp:BoundField DataField="名前" HeaderText="名前" SortExpression="カナ" />
        <asp:BoundField DataField="利用" HeaderText="利用" ReadOnly="True" SortExpression="利用" ItemStyle-Width="80" ItemStyle-HorizontalAlign="Center" />
        <asp:BoundField DataField="ロック" HeaderText="ロック" ReadOnly="True" SortExpression="ロック" ItemStyle-Width="80" ItemStyle-HorizontalAlign="Center" />
        <asp:BoundField DataField="権限" HeaderText="権限" ReadOnly="True" SortExpression="権限" ItemStyle-Width="120" ItemStyle-HorizontalAlign="Center" />
      </Columns>
    </cc1:GridViewEx>
    <uc1:GridPager ID="GridPager2" runat="server" TargetGridViewControlID="GridResult" CheckBoxHeaderText="選択" CheckBoxPrimaryKeyHeaderText="ID" />
    <asp:ObjectDataSource runat="server" ID="DataResult" SelectMethod="GetFindData" TypeName="Dbs.Asphalt.Database.DbFinderTableAdapters.Mユーザー検索TableAdapter" EnablePaging="True" SortParameterName="sortName" SelectCountMethod="GetFindDataCount">
      <SelectParameters>
        <asp:ControlParameter ControlID="TextUserID" Name="ユーザーID" PropertyName="Text" Type="String" ConvertEmptyStringToNull="False" />
        <asp:ControlParameter ControlID="TextUserName" Name="ユーザー名" PropertyName="Text" Type="String" ConvertEmptyStringToNull="False" />
        <asp:Parameter Name="種別" Type="Int16" ConvertEmptyStringToNull="false" />
      </SelectParameters>
    </asp:ObjectDataSource>
  </asp:Panel>
</cc1:ItemFrame>

<asp:panel runat="server" ID="PanelToolbar">
    <cc1:ToolButton runat="server" ButtonImage="biExcel" Text="ダウンロード" ID="ButtonDownload" PostbackTrriger="true" />
    <cc1:ToolButton runat="server" ButtonImage="biBack" Text="戻る" ID="ButtonBack" />
</asp:panel>
