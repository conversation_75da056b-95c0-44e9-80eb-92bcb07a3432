﻿Imports System.Data
Imports System.IO
Imports Dbs.Asphalt.UI
Imports Dbs.Asphalt.Core
Imports Dbs.Asphalt.Core.Common
Imports Dbs.Asphalt.Core.SystemLogic

''' <summary>
''' ****************
''' 汎用検索フォーム
''' ****************
''' 検索で使用したい検索SQLを、Dbs.Application.Logic.Database.CommonFinderSqlフォルダ内に定義しておく。(SQLファイル)
''' 名前は「XXXXXX.sql」とし、LoadFinder()の第3パラメータに「XXXXXX」の部分を渡す。
''' 
''' =================================================
''' OpenFinderのパラメータに渡すことができる要素
''' =================================================
''' 起動     : "直接"と指定すると、戻るボタンが非表示となる。
''' 固定条件 : SQLのWHERE文に準拠した固定条件をセットする。
''' 開くURI  : 特定のWEBフォームを開くcmd=XXXXXXXの部分を指定すると、code=ZZZZをURLパラメータに追加したURLで、「開く」ハイパーリンクが検索結果の各行に表示される。
'''            (例) Dim param As New Hashtable
'''                 param("開くURI") = "master/SyohinMasterEditor"
'''                 <c>http://server/?cmd=master/SyohinMasterEditor&code=XXXXというURLの「開く」ハイパーリンクが表示される</c>
''' 複数選択 : "可能"とセットすると、検索結果にチェックボックスが表示され、複数選択可能となる。
''' 識別名   : 一意の識別名を指定すると、戻り値セットに"識別名"という要素でその値が返される。
''' 
''' -----------------------------------------------
''' 戻り値(FinderCloseメソッドのresultに渡される値)
''' -----------------------------------------------
''' 選択値   : 選択された一意の値が格納されます。複数選択可の場合は、選択された値がカンマ区切りで格納されます。
''' 識別名   : OpenFinderのパラメータに渡された場合はその値がそのまま返される。
''' </summary>
Partial Class CommonFinder
    Inherits ModuleBase

    Private master_name As MasterName

    Protected Overrides Sub OnInit(e As System.EventArgs)
        Dim dl As New DataList(Base.Security)

        master_name = New MasterName(Base.Security)

        DataResult.TypeName = "Dbs.Asphalt.Database.Common.CommonFinderManager"

        For i As Integer = GridResult.Columns.Count - 1 To 3 Step -1
            GridResult.Columns.Remove(GridResult.Columns(i))
        Next

        FrameFindItem.Text = IIf("MSTW".IndexOf(Me.CommonFinderTable.Substring(0, 1)) >= 0, Me.CommonFinderTable.Substring(1), Me.CommonFinderTable)
        DataResult.SelectParameters("sqlname").DefaultValue = Me.CommonFinderTable
        DataResult.SelectParameters("returnname").DefaultValue = ReturnFieldName

        Dim fld As DataView = dl.FinderViewColumnsList(Base.Security.UserID, Me.CommonFinderTable)
        Dim df As New BoundField

        If fld Is Nothing Then
            fld = dl.FinderColumnsList(Me.CommonFinderTable, ReturnFieldName)
        End If

        If Not fld Is Nothing Then
            For i As Integer = 0 To fld.Count - 1
                df = New BoundField
                df.HeaderText = fld(i)("名前")
                df.SortExpression = fld(i)("名前")
                df.DataField = fld(i)("名前")
                df.HeaderStyle.Wrap = False
                df.ItemStyle.Wrap = False
                GridResult.Columns.Add(df)
            Next
        End If

        df = New BoundField
        df.HeaderText = "_返却項目"
        df.SortExpression = ReturnFieldName
        df.DataField = ReturnFieldName
        GridResult.Columns.Add(df)

        MyBase.OnInit(e)
    End Sub

    Protected Overrides Sub OnLoad(e As System.EventArgs)
        GridResult.Columns(1).ControlStyle.CssClass = "button_small"

        If Not IsPostBack Then
            If Parameters("起動") = "直接" Then
                ButtonBack.Visible = False
                FinderOpen()
            End If
        End If

        MyBase.OnLoad(e)
    End Sub

    Public Overrides Sub FinderClose(findername As String, result As System.Collections.Hashtable, resulttargetid As String)

    End Sub

    Public Overrides Sub FinderOpen()
        Dim cfg As New Configurator(Nothing)
        Dim dl As New DataList(Base.Security)
        Dim dv As DataView = dl.FinderColumnsList(Me.CommonFinderTable, ReturnFieldName)

        GridResult.ColumnByHeaderText("選択").Visible = (Parameters("複数選択") = "可能")
        GridResult.ColumnByHeaderText("開く").Visible = (Parameters("起動") = "直接")

        Base.UICommon.ListControlDataBind(DropItem1, dv)
        Base.UICommon.ListControlDataBind(DropItem2, dv)
        Base.UICommon.ListControlDataBind(DropItem3, dv)

        ' 検索項目を復帰
        DropItem1.SelectedValue = cfg.GetUserConfigData(Base.Security.UserID, Me.CommonFinderTable & "-検索項目1", "")
        DropItem2.SelectedValue = cfg.GetUserConfigData(Base.Security.UserID, Me.CommonFinderTable & "-検索項目2", "")
        DropItem3.SelectedValue = cfg.GetUserConfigData(Base.Security.UserID, Me.CommonFinderTable & "-検索項目3", "")

        ' ソート項目を設定
        Dim sortitem As String = cfg.GetUserConfigData(Base.Security.UserID, Me.CommonFinderTable & "-ソート項目", "")
        If sortitem <> "" Then
            Dim sortasc As SortDirection = SortDirection.Ascending
            If Text.CVal(cfg.GetUserConfigData(Base.Security.UserID, Me.CommonFinderTable & "-ソート順", 0)) = 1 Then
                sortasc = SortDirection.Descending
            End If
            Try
                GridResult.Sort(sortitem, sortasc)
            Catch ex As Exception
            End Try
        End If

        If Not FrameResult.Visible Then
            ButtonStart_Click(ButtonStart, Nothing)
            FrameResult.Visible = False
        End If

        DropItem_SelectedIndexChanged(DropItem3, Nothing)
        DropItem_SelectedIndexChanged(DropItem2, Nothing)
        DropItem_SelectedIndexChanged(DropItem1, Nothing)

        ' ページャーコントロールの項目値を設定
        If Parameters("複数選択") = "可能" Then
            GridPager1.CheckBoxHeaderText = "選択"
            GridPager2.CheckBoxHeaderText = "選択"
            GridPager1.CheckBoxPrimaryKeyHeaderText = ReturnFieldName
            GridPager2.CheckBoxPrimaryKeyHeaderText = ReturnFieldName
        End If
        FrameFindItem.Text = IIf("MSTW".IndexOf(Me.CommonFinderTable.Substring(0, 1)) >= 0, Me.CommonFinderTable.Substring(1), Me.CommonFinderTable)

        PanelToolbar.Visible = True
    End Sub

    Public Overrides ReadOnly Property PageTitle() As String
        Get
            Return "汎用検索"
        End Get
    End Property

    Protected Sub ButtonBack_Click(sender As Object, e As System.EventArgs) Handles ButtonBack.Click
        Base.CloseFinder(False, Nothing)
    End Sub

    Protected Sub ButtonStart_Click(sender As Object, e As System.EventArgs) Handles ButtonStart.Click
        save_finditems()

        DataResult.TypeName = "Dbs.Asphalt.Database.Common.CommonFinderManager"

        Select Case True
            Case PanelString1.Visible
                If CheckEmpty1.Checked Then
                    DataResult.SelectParameters("検索値1").DefaultValue = Chr(&HFF)
                Else
                    DataResult.SelectParameters("検索値1").DefaultValue = TextKeyword1.Text
                End If
            Case PanelDropdown1.Visible
                If DropKeyword1.SelectedIndex = 0 Then
                    DataResult.SelectParameters("検索値1").DefaultValue = ""
                Else
                    DataResult.SelectParameters("検索値1").DefaultValue = DropKeyword1.SelectedValue & vbTab & DropKeyword1.SelectedValue
                End If
            Case PanelDate1.Visible
                DataResult.SelectParameters("検索値1").DefaultValue = TextFDate1.Text & vbTab & TextTDate1.Text
            Case PanelNumeric1.Visible
                DataResult.SelectParameters("検索値1").DefaultValue = TextFNum1.Text & vbTab & TextTNum1.Text
        End Select
        Select Case True
            Case PanelString2.Visible
                If CheckEmpty2.Checked Then
                    DataResult.SelectParameters("検索値2").DefaultValue = Chr(&HFF)
                Else
                    DataResult.SelectParameters("検索値2").DefaultValue = TextKeyword2.Text
                End If
            Case PanelDropdown2.Visible
                If DropKeyword2.SelectedIndex = 0 Then
                    DataResult.SelectParameters("検索値2").DefaultValue = ""
                Else
                    DataResult.SelectParameters("検索値2").DefaultValue = DropKeyword2.SelectedValue & vbTab & DropKeyword2.SelectedValue
                End If
            Case PanelDate2.Visible
                DataResult.SelectParameters("検索値2").DefaultValue = TextFDate2.Text & vbTab & TextTDate2.Text
            Case PanelNumeric2.Visible
                DataResult.SelectParameters("検索値2").DefaultValue = TextFNum2.Text & vbTab & TextTNum2.Text
        End Select
        Select Case True
            Case PanelString3.Visible
                If CheckEmpty3.Checked Then
                    DataResult.SelectParameters("検索値3").DefaultValue = Chr(&HFF)
                Else
                    DataResult.SelectParameters("検索値3").DefaultValue = TextKeyword3.Text
                End If
            Case PanelDropdown3.Visible
                If DropKeyword3.SelectedIndex = 0 Then
                    DataResult.SelectParameters("検索値3").DefaultValue = ""
                Else
                    DataResult.SelectParameters("検索値3").DefaultValue = DropKeyword3.SelectedValue & vbTab & DropKeyword3.SelectedValue
                End If
            Case PanelDate3.Visible
                DataResult.SelectParameters("検索値3").DefaultValue = TextFDate3.Text & vbTab & TextTDate3.Text
            Case PanelNumeric3.Visible
                DataResult.SelectParameters("検索値3").DefaultValue = TextFNum3.Text & vbTab & TextTNum3.Text
        End Select

        DataResult.SelectParameters("固定条件").DefaultValue = Parameters("固定条件")

        GridPager1.ClearCheckCondition()

        GridResult.PageIndex = 0
        Try
            GridResult.DataBind()
        Catch ex As Exception
            Dim cfg As New Configurator(Nothing)
            cfg.SetUserConfigData(Base.Security.UserID, Me.CommonFinderTable & "-表示項目", "")
            Base.ErrorMessage = "検索項目が変更されたため、表示項目をリセットします。" & vbNewLine & ex.Message
            If ex.InnerException IsNot Nothing Then
                Base.ErrorMessage &= vbNewLine & ex.InnerException.Message
            End If
            OnInit(Nothing)
        End Try
        FrameResult.Visible = True
    End Sub

    Protected Sub GridResult_RowDataBound(sender As Object, e As Dbs.Asphalt.UI.GridViewExRowEventArgs) Handles GridResult.RowDataBound
        If e.Row.RowType = DataControlRowType.DataRow Then
            If Parameters("開くURI") <> "" Then
                Dim dr As DataRow = e.Row.DataItem.Row
                e.RowCell("開く").Text = "<a target=""brank"" href=""?cmd=" & Parameters("開くURI") & "&code=" & dr(Parameters("返却項目")) & """>開く</a>"
            End If

            For i As Integer = 0 To GridResult.Columns.Count - 1
                Dim hn As String = GridResult.Columns(i).HeaderText
                If hn <> "" Then
                    ' 数値型の列の判断
                    Dim num As Boolean = False
                    If hn = "数量" Then
                        num = True
                    End If
                    If hn.Substring(hn.Length - 1, 1) = "数" Then
                        num = True
                    End If

                    If num Then
                        e.RowCell(hn).HorizontalAlign = HorizontalAlign.Right
                        e.RowCellFormat(hn, 15, Text.FormatContents.tbCurrency, 2)
                    End If
                    If hn.Substring(hn.Length - 2, 2) = "単価" Or hn.Substring(hn.Length - 1, 1) = "額" Or hn.IndexOf("定価") >= 0 Or hn.IndexOf("価格") >= 0 Then
                        e.RowCell(hn).HorizontalAlign = HorizontalAlign.Right
                        e.RowCellFormat(hn, 15, Text.FormatContents.tbCurrency)
                    End If
                    If hn.Substring(hn.Length - 1, 1) = "率" Then
                        e.RowCell(hn).HorizontalAlign = HorizontalAlign.Right
                        e.RowCellFormat(hn, 15, Text.FormatContents.tbCurrency, 2)
                    End If
                    If hn.Substring(hn.Length - 1, 1) = "色" Then
                        Dim img As New Image
                        img.ImageUrl = "../../images/brank.gif"
                        img.CssClass = "schedule_cell_data" & e.RowCell(hn).Text
                        img.BorderWidth = 2
                        e.RowCell(hn).HorizontalAlign = HorizontalAlign.Center
                        e.RowCell(hn).Controls.Add(img)
                    End If
                End If
            Next
        End If

        Try
            e.RowCell("_返却項目").Visible = False
        Catch ex As Exception
        End Try

        If Parameters("起動") = "直接" Then
            e.Row.Cells(1).Visible = False
        End If
    End Sub

    Protected Sub GridResult_SelectedIndexChanged(sender As Object, e As System.EventArgs) Handles GridResult.SelectedIndexChanged
        Dim result As Hashtable = Parameters
        Dim idlist As String = ""

        result("識別名") = Parameters("識別名")
        If GridResult.ColumnByHeaderText("選択").Visible Then
            GridPager1.SaveCheckCondition()
            If Not GridPager1.CheckCondition Is Nothing Then
                For Each v As Object In GridPager1.CheckCondition
                    idlist &= IIf(idlist = "", "", ",") & v.ToString
                Next
            End If
            If idlist <> "" Then
                result("選択値") = idlist
            Else
                result("選択値") = GridResult.SelectedRow("_返却項目").Text
            End If
        Else
            result("選択値") = GridResult.SelectedRow("_返却項目").Text
        End If
        Base.CloseFinder(True, result)
    End Sub

    Protected Sub ButtonDownload_Click(sender As Object, e As System.EventArgs) Handles ButtonDownload.Click
        Dim dv As DataView = CType(DataResult.Select, DataView)
        Base.DownloadExcel(dv, GridResult, "finder_" & Me.CommonFinderTable.Substring(1) & ".xlsx")
    End Sub

    Public Overrides ReadOnly Property RunAuth() As Dbs.Asphalt.Core.SystemLogic.Security.emUserAuth
        Get
            Return SystemLogic.Security.emUserAuth.一般
        End Get
    End Property

    Protected Sub ButtonConfig_Click(sender As Object, e As System.EventArgs) Handles ButtonConfig.Click
        Dim dl As New DataList(Base.Security)

        Base.UICommon.ListControlDataBind(ListItemList, dl.FinderColumnsList(Me.CommonFinderTable, ReturnFieldName))
        Base.UICommon.ListControlDataBind(ListViewList, dl.FinderViewColumnsList(Base.Security.UserID, Me.CommonFinderTable))

        If ListViewList.Items.Count = 0 Then
            Base.UICommon.ListControlDataBind(ListViewList, dl.FinderColumnsList(Me.CommonFinderTable, ReturnFieldName))
        End If

        delete_viewlist()

        PanelConfig.Visible = True
    End Sub

    Protected Sub ButtonOK_Click(sender As Object, e As System.EventArgs) Handles ButtonOK.Click
        Dim buf As String = ""
        Dim cfg As New Configurator(Base.Security)

        For i As Integer = 0 To ListViewList.Items.Count - 1
            buf &= IIf(buf = "", "", ",") & ListViewList.Items(i).Text
        Next

        cfg.SetUserConfigData(Base.Security.UserID, Me.CommonFinderTable & "-表示項目", buf)

        PanelConfig.Visible = False

        OnInit(Nothing)
    End Sub

    Protected Sub ButtonCancel_Click(sender As Object, e As System.EventArgs) Handles ButtonCancel.Click
        PanelConfig.Visible = False
    End Sub

    Protected Sub ButtonAdd_Click(sender As Object, e As System.EventArgs) Handles ButtonAdd.Click
        For i As Integer = 0 To ListItemList.Items.Count - 1
            If ListItemList.Items(i).Selected Then
                ListViewList.Items.Add(ListItemList.Items(i))
            End If
        Next
        delete_viewlist()
    End Sub

    Protected Sub ButtonRem_Click(sender As Object, e As System.EventArgs) Handles ButtonRem.Click
        For i As Integer = ListViewList.Items.Count - 1 To 0 Step -1
            If ListViewList.Items(i).Selected Then
                ListViewList.Items.Remove(ListViewList.Items(i))
            End If
        Next
        delete_viewlist()
    End Sub

    Protected Sub DropItem_SelectedIndexChanged(sender As Object, e As System.EventArgs) Handles DropItem1.SelectedIndexChanged,
                                                                                                 DropItem2.SelectedIndexChanged,
                                                                                                 DropItem3.SelectedIndexChanged
        FrameResult.Visible = False

        DataResult.SelectParameters("検索値1").DefaultValue = Chr(&HAA)
        Dim dv As DataView = CType(DataResult.Select, DataView)
        Dim s_target As Panel
        Dim l_target As Panel
        Dim d_target As Panel
        Dim n_target As Panel
        Dim columntype As String = dv.Table.Columns(CType(sender, Dbs.Asphalt.UI.DropDownList).SelectedValue).DataType.Name
        Dim columnname As String = dv.Table.Columns(CType(sender, Dbs.Asphalt.UI.DropDownList).SelectedValue).ColumnName
        Dim ftbox As TextBox
        Dim ttbox As TextBox
        Dim dl As New DataList(Base.Security)
        Dim ddlist As DropDownList
        Dim kubun As String

        Select Case CType(sender, DropDownList).ID
            Case "DropItem1"
                s_target = PanelString1
                l_target = PanelDropdown1
                d_target = PanelDate1
                n_target = PanelNumeric1

                ftbox = TextFNum1
                ttbox = TextTNum1
                ddlist = DropKeyword1
            Case "DropItem2"
                s_target = PanelString2
                l_target = PanelDropdown2
                d_target = PanelDate2
                n_target = PanelNumeric2

                ftbox = TextFNum2
                ttbox = TextTNum2
                ddlist = DropKeyword2
            Case Else ' "DropItem3"
                s_target = PanelString3
                l_target = PanelDropdown3
                d_target = PanelDate3
                n_target = PanelNumeric3

                ftbox = TextFNum3
                ttbox = TextTNum3
                ddlist = DropKeyword3
        End Select

        s_target.Visible = False
        l_target.Visible = False
        d_target.Visible = False
        n_target.Visible = False

        If e IsNot Nothing Then
            ftbox.Text = ""
            ttbox.Text = ""
            save_finditems()
        End If

        kubun = master_name.DataKubunName(columnname)

        If kubun <> "" Then
            ' 区分に存在する場合は区分のドロップダウン
            l_target.Visible = True
            Base.UICommon.ListControlDataBind(ddlist, dl.AnyList(kubun), UICommon.AllSelectionTextType.空白, -1)
        Else
            If columnname.Substring(columnname.Length - 2) = "日時" Or
               columnname.Substring(columnname.Length - 1) = "日" Or
               columnname.Substring(columnname.Length - 2) = "日付" Then
                d_target.Visible = True
            ElseIf columnname = "????コード" Then
                n_target.Visible = True
                ftbox.Format = TextBox.FormatContents.tbCode
                ttbox.Format = TextBox.FormatContents.tbCode
                ftbox.MaxLength = 4
                ttbox.MaxLength = 4
            Else
                Select Case columntype
                    Case "String"
                        s_target.Visible = True
                    Case "DateTime"
                        d_target.Visible = True
                    Case Else
                        ftbox.Format = TextBox.FormatContents.tbText
                        ttbox.Format = TextBox.FormatContents.tbText
                        n_target.Visible = True
                End Select
            End If
        End If

        sender.Focus()
    End Sub

    Public Overrides Sub MessageBoxClose(messageboxname As String, result As Dbs.Asphalt.UI.ModuleBase.MessageBoxResult)

    End Sub

    Protected Sub ButtonUp_Click(sender As Object, e As System.EventArgs) Handles ButtonUp.Click
        Dim i As Integer = 0
        Dim swap_s As Boolean
        Dim swap_v As String
        Dim swap_t As String

        If Not ListViewList.Items(i).Selected Then
            Do
                If ListViewList.Items(i).Selected Then
                    swap_v = ListViewList.Items(i).Value
                    swap_t = ListViewList.Items(i).Text
                    ListViewList.Items(i).Value = ListViewList.Items(i - 1).Value
                    ListViewList.Items(i).Text = ListViewList.Items(i - 1).Text
                    ListViewList.Items(i - 1).Value = swap_v
                    ListViewList.Items(i - 1).Text = swap_t

                    swap_s = ListViewList.Items(i).Selected
                    ListViewList.Items(i).Selected = ListViewList.Items(i - 1).Selected
                    ListViewList.Items(i - 1).Selected = swap_s
                End If
                i = i + 1
            Loop Until (i > ListViewList.Items.Count - 1)
        End If
        delete_viewlist()
    End Sub

    Protected Sub ButtonDown_Click(sender As Object, e As System.EventArgs) Handles ButtonDown.Click
        Dim i As Integer = ListViewList.Items.Count - 1
        Dim swap_s As Boolean
        Dim swap_v As String
        Dim swap_t As String

        If Not ListViewList.Items(i).Selected Then
            Do
                If ListViewList.Items(i).Selected Then
                    swap_v = ListViewList.Items(i).Value
                    swap_t = ListViewList.Items(i).Text
                    ListViewList.Items(i).Value = ListViewList.Items(i + 1).Value
                    ListViewList.Items(i).Text = ListViewList.Items(i + 1).Text
                    ListViewList.Items(i + 1).Value = swap_v
                    ListViewList.Items(i + 1).Text = swap_t

                    swap_s = ListViewList.Items(i).Selected
                    ListViewList.Items(i).Selected = ListViewList.Items(i + 1).Selected
                    ListViewList.Items(i + 1).Selected = swap_s
                End If
                i = i - 1
            Loop Until (i < 0)
        End If
        delete_viewlist()
    End Sub

    ''' <summary>
    ''' 検索表示項目に設定されている項目を、選択項目リストから削除する
    ''' </summary>
    Private Sub delete_viewlist()
        Dim dl As New DataList(Base.Security)

        Base.UICommon.ListControlDataBind(ListItemList, dl.FinderColumnsList(Me.CommonFinderTable, ReturnFieldName))

        For i As Integer = 0 To ListViewList.Items.Count - 1
            For j As Integer = 0 To ListItemList.Items.Count - 1
                If ListItemList.Items(j).Equals(ListViewList.Items(i)) Then
                    ListItemList.Items.Remove(ListItemList.Items(j))
                    Exit For
                End If
            Next
        Next

    End Sub

    Private Sub save_finditems()
        ' 検索項目を記憶
        Dim cfg As New Configurator(Base.Security)
        cfg.SetUserConfigData(Base.Security.UserID, Me.CommonFinderTable & "-検索項目1", DropItem1.SelectedValue)
        cfg.SetUserConfigData(Base.Security.UserID, Me.CommonFinderTable & "-検索項目2", DropItem2.SelectedValue)
        cfg.SetUserConfigData(Base.Security.UserID, Me.CommonFinderTable & "-検索項目3", DropItem3.SelectedValue)
        cfg.SetUserConfigData(Base.Security.UserID, Me.CommonFinderTable & "-検索値1", TextKeyword1.Text)
        cfg.SetUserConfigData(Base.Security.UserID, Me.CommonFinderTable & "-検索値2", TextKeyword2.Text)
        cfg.SetUserConfigData(Base.Security.UserID, Me.CommonFinderTable & "-検索値3", TextKeyword3.Text)
    End Sub

    Protected Sub GridResult_Sorted(sender As Object, e As System.EventArgs) Handles GridResult.Sorted
        Dim cfg As New Configurator(Base.Security)
        cfg.SetUserConfigData(Base.Security.UserID, Me.CommonFinderTable & "-ソート項目", GridResult.SortExpression)
        cfg.SetUserConfigData(Base.Security.UserID, Me.CommonFinderTable & "-ソート順", GridResult.SortDirection)
    End Sub

End Class
