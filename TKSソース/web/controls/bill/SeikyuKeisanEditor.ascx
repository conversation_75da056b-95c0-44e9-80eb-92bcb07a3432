﻿<%@ Control Language="VB" AutoEventWireup="false" CodeFile="SeikyuKeisanEditor.ascx.vb" Inherits="SeikyuKeisanEditor" %>
<%@ Register Assembly="Dbs.Asphalt.UI" Namespace="Dbs.Asphalt.UI" TagPrefix="cc1" %>
<%@ Register Src="~/controls/common/RecordSelector.ascx" TagPrefix="uc1" TagName="RecordSelector" %>
<%@ Register Src="~/controls/common/ReportDataViewer.ascx" TagPrefix="uc1" TagName="ReportDataViewer" %>
<%@ Register Src="~/controls/common/GridPager.ascx" TagPrefix="uc1" TagName="GridPager" %>

<cc1:ItemFrame runat="server" ID="PanelBase" Text="実行条件">
  <div class="leftpane">
      <cc1:ItemPanel runat="server" Text="締日区分">
        <cc1:DropDownList runat="server" ID="Drop締日区分" AutoPostBack="true"></cc1:DropDownList>
      </cc1:ItemPanel>

      <cc1:ItemPanel runat="server" Text="請求年月">
        <cc1:TextBox runat="server" ID="Text請求年月" AutoPostBack="true"></cc1:TextBox>
      </cc1:ItemPanel>

      <cc1:ItemPanel runat="server" Text="請求日付">
        <cc1:TextBox runat="server" ID="Text請求日付F"></cc1:TextBox>～<cc1:TextBox runat="server" ID="Text請求日付T"></cc1:TextBox>
      </cc1:ItemPanel>

      <cc1:ItemPanel runat="server" Text="得意先コード">
        <cc1:TextBox runat="server" ID="Text得意先コードF" FinderButton="true" ></cc1:TextBox>～<cc1:TextBox runat="server" ID="Text得意先コードT" FinderButton="true" ></cc1:TextBox>
      </cc1:ItemPanel>

      <cc1:TextBox runat="server" ID="Text締日区分" Visible="false" ></cc1:TextBox>
  </div>
  <div class="clear"></div>
</cc1:ItemFrame>

  <asp:UpdatePanel runat="server" ID="FrameResult" Visible="false">
    <ContentTemplate>
      <uc1:GridPager ID="GridPager1" runat="server" TargetGridViewControlID="GridResult" CheckBoxHeaderText="選択" CheckBoxPrimaryKeyHeaderText="得意先コード" />
      <cc1:GridViewEx runat="server" ID="GridResult" DataSourceID="DataResult">
        <Columns>
          <asp:TemplateField HeaderText="選択" ItemStyle-HorizontalAlign="Center" ItemStyle-Width="30">
            <ItemTemplate><cc1:CheckBox runat="server" ID="CheckBox" /></ItemTemplate>
          </asp:TemplateField>
          <asp:BoundField DataField="得意先コード" HeaderText="得意先コード" ReadOnly="True" SortExpression="得意先コード" HeaderStyle-Wrap="false" />
          <asp:BoundField DataField="得意先名" HeaderText="得意先名" ReadOnly="True" SortExpression="得意先名" HeaderStyle-Wrap="false" />
          <asp:BoundField DataField="請求番号" HeaderText="請求番号" ReadOnly="True" SortExpression="請求番号" HeaderStyle-Wrap="false" />
          <asp:BoundField DataField="請求日付自" HeaderText="請求日付自" ReadOnly="True" SortExpression="請求日付自" HeaderStyle-Wrap="false" />
          <asp:BoundField DataField="請求日付至" HeaderText="請求日付至" ReadOnly="True" SortExpression="請求日付至" HeaderStyle-Wrap="false" />
          <asp:BoundField DataField="前回額" HeaderText="前回額" ReadOnly="True" SortExpression="前回額" HeaderStyle-Wrap="false" />
          <asp:BoundField DataField="入金額" HeaderText="入金額" ReadOnly="True" SortExpression="入金額" HeaderStyle-Wrap="false" />
          <asp:BoundField DataField="調整額" HeaderText="調整額" ReadOnly="True" SortExpression="調整額" HeaderStyle-Wrap="false" />
          <asp:BoundField DataField="繰越額" HeaderText="繰越額" ReadOnly="True" SortExpression="繰越額" HeaderStyle-Wrap="false" />
          <asp:BoundField DataField="税抜額" HeaderText="税抜額" ReadOnly="True" SortExpression="税抜額" HeaderStyle-Wrap="false" />
          <asp:BoundField DataField="税込額" HeaderText="税込額" ReadOnly="True" SortExpression="税込額" HeaderStyle-Wrap="false" />
          <asp:BoundField DataField="消費税" HeaderText="消費税" ReadOnly="True" SortExpression="消費税" HeaderStyle-Wrap="false" />
          <asp:BoundField DataField="請求額" HeaderText="請求額" ReadOnly="True" SortExpression="請求額" HeaderStyle-Wrap="false" />
        </Columns>
      </cc1:GridViewEx>
      <uc1:GridPager ID="GridPager2" runat="server" TargetGridViewControlID="GridResult" />
      <asp:ObjectDataSource runat="server" ID="DataResult" SelectMethod="GetData" TypeName="Dbs.Application.Logic.Database.DbCalcTableAdapters.F請求計算検索TableAdapter">
        <SelectParameters>
            <asp:ControlParameter ControlID="Text締日区分" Name="締日区分" PropertyName="Text" Type="Int32" ConvertEmptyStringToNull="False" />
            <asp:ControlParameter ControlID="Text請求年月" Name="請求年月" PropertyName="Text" Type="String" ConvertEmptyStringToNull="False" />
            <asp:ControlParameter ControlID="Text請求日付F" Name="請求日付F" PropertyName="Text" Type="String" ConvertEmptyStringToNull="False" />
            <asp:ControlParameter ControlID="Text請求日付T" Name="請求日付T" PropertyName="Text" Type="String" ConvertEmptyStringToNull="False" />
            <asp:ControlParameter ControlID="Text得意先コードF" Name="得意先コードF" PropertyName="Text" Type="String" ConvertEmptyStringToNull="False" />
            <asp:ControlParameter ControlID="Text得意先コードT" Name="得意先コードT" PropertyName="Text" Type="String" ConvertEmptyStringToNull="False" />
        </SelectParameters>
      </asp:ObjectDataSource>
    </ContentTemplate>
  </asp:UpdatePanel>

<asp:panel runat="server" ID="PanelToolbar">
  <cc1:ToolButton runat="server" ButtonImage="biFinder" Text="検索" ID="ButtonFind" />
  <cc1:ToolButton runat="server" ButtonImage="biSave" Text="保存" ID="ButtonRegist" PostbackTrriger="true" PostbackTrrigerFreeze="true" />
  <cc1:ToolButton runat="server" ButtonImage="biDelete" Text="削除" ID="ButtonDelete" PostbackTrriger="true" PostbackTrrigerFreeze="true" />
</asp:panel>
