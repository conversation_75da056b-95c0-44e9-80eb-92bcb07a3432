﻿Imports System.Data
Imports Dbs.Asphalt.UI
Imports Dbs.Asphalt.Core
Imports Dbs.Application.Logic.Logic.bill
Imports Dbs.Asphalt.Core.Common

Partial Class SeikyuPrintEditor
    Inherits ModuleBase

    Private logic As SeikyuPrintManager

    Protected Overrides Sub OnInit(e As System.EventArgs)
        logic = New SeikyuPrintManager(Base.Security)

        'logic.EditMode.Control(Field.ControlTypeContents.ctEditMode) = EditMode
        logic.締日区分.Control(Field.ControlTypeContents.ctDropDownList) = Drop締日区分
        logic.請求年月.Control(Field.ControlTypeContents.ctTextBox) = Text請求年月
        logic.得意先コードF.Control(Field.ControlTypeContents.ctTextBox) = Text得意先コードF
        logic.得意先コードT.Control(Field.ControlTypeContents.ctTextBox) = Text得意先コードT

        logic.請求書帳票区分.Control(Field.ControlTypeContents.ctRadioButtonList) = Radio請求書帳票区分
        logic.控フラグ.Control(Field.ControlTypeContents.ctCheckBox) = Check控
        logic.Start()

        Base.AddPostbackTrrigerControl(ButtonPrint)

        ' Finderの読み込み
        Base.LoadFinder("TokuisakiMasterFinder", "CommonFinder", "M得意先検索", "得意先コード")

        ' ﾗｼﾞｵﾎﾞﾀﾝの設定
        SetRadioList()

        MyBase.OnInit(e)
    End Sub

    Protected Overrides Sub OnLoad(e As System.EventArgs)

        If Not IsPostBack Then
            logic.GetShimekikan(True)
        End If

        MyBase.OnLoad(e)
    End Sub

    Private Sub AccountEditor_PreRender(sender As Object, e As EventArgs) Handles Me.PreRender

    End Sub

    Public Overrides Sub FinderClose(findername As String, result As System.Collections.Hashtable, resulttargetid As String)

    End Sub

    Public Overrides Sub FinderOpen()

    End Sub

    Private Sub GridResult_RowDataBound(sender As Object, e As GridViewExRowEventArgs) Handles GridResult.RowDataBound

        ' 書式
        If e.Row.RowType = DataControlRowType.DataRow Then
            e.RowCell("前回額").HorizontalAlign = HorizontalAlign.Right
            e.RowCell("入金額").HorizontalAlign = HorizontalAlign.Right
            e.RowCell("調整額").HorizontalAlign = HorizontalAlign.Right
            e.RowCell("繰越額").HorizontalAlign = HorizontalAlign.Right
            e.RowCell("税抜額").HorizontalAlign = HorizontalAlign.Right
            e.RowCell("税込額").HorizontalAlign = HorizontalAlign.Right
            e.RowCell("消費税").HorizontalAlign = HorizontalAlign.Right
            e.RowCell("請求額").HorizontalAlign = HorizontalAlign.Right

            e.RowCellFormat("前回額", 15, Text.FormatContents.tbCurrency)
            e.RowCellFormat("入金額", 15, Text.FormatContents.tbCurrency)
            e.RowCellFormat("調整額", 15, Text.FormatContents.tbCurrency)
            e.RowCellFormat("繰越額", 15, Text.FormatContents.tbCurrency)
            e.RowCellFormat("税抜額", 15, Text.FormatContents.tbCurrency)
            e.RowCellFormat("税込額", 15, Text.FormatContents.tbCurrency)
            e.RowCellFormat("消費税", 15, Text.FormatContents.tbCurrency)
            e.RowCellFormat("請求額", 15, Text.FormatContents.tbCurrency)
        End If
    End Sub

    Public Overrides Sub MessageBoxClose(messageboxname As String, result As Dbs.Asphalt.UI.ModuleBase.MessageBoxResult)

    End Sub

    Public Overrides ReadOnly Property PageTitle As String
        Get
            Return logic.Title
        End Get
    End Property

    Public Overrides ReadOnly Property RunAuth As Dbs.Asphalt.Core.SystemLogic.Security.emUserAuth
        Get
            Return SystemLogic.Security.emUserAuth.一般
        End Get
    End Property

    Private Sub TextTokuisakiCode_FinderButtonClicked(sender As Object, e As ImageClickEventArgs) Handles Text得意先コードF.FinderButtonClicked, Text得意先コードT.FinderButtonClicked
        ' 得意先検索を開く
        Base.OpenFinder("TokuisakiMasterFinder", , sender.ID)
    End Sub

    Private Sub Text請求年月_TextChanged(sender As Object, e As EventArgs) Handles Text請求年月.TextChanged, Drop締日区分.TextChanged

    End Sub

    Private Sub SetRadioList()

        Radio諸口区分.Items.Clear()

        Radio諸口区分.AddItem("全て", "9")

        Dim dv As DataView = logic.GetDataKubun
        For i As Integer = 0 To logic.GetDataKubun.Count - 1
            Radio諸口区分.AddItem(dv(i)("区分名"), dv(i)("区分コード"))
        Next

        Radio諸口区分.SelectedValue = "9"
    End Sub

    Protected Sub ButtonPrint_Click(sender As Object, e As ImageClickEventArgs) Handles ButtonPrint.Click

        ' 以下のコードで、idlistにチェックされているｷｰがカンマ区切りで格納される。
        Dim idlist As String = ""
        GridPager1.SaveCheckCondition()
        If Not GridPager1.CheckCondition Is Nothing Then
            For Each v As Object In GridPager1.CheckCondition
                idlist &= IIf(idlist = "", "", ",") & v.ToString
            Next
        End If

        logic.選択リスト.Value = idlist

        ' チェックをクリア
        GridPager1.ClearCheckCondition()

        If logic.PrintMainData() Then
            Base.DownloadPDF(logic.Report)
            'Base.DownloadPDF(logic.ConnectedReport)
        Else
            Base.ErrorMessage = logic.LastError
        End If
    End Sub

    Protected Sub ButtonFind_Click(sender As Object, e As ImageClickEventArgs) Handles ButtonFind.Click
        Dim param As New Hashtable

        Text締日区分.Value = Drop締日区分.SelectedValue
        Text諸口区分.Value = Radio諸口区分.SelectedValue

        ' チェックをクリア
        GridPager1.ClearCheckCondition()
        GridPager1.AllCheckCondition(True)

        GridResult.PageIndex = 0
        GridResult.DataBind()
        FrameResult.Visible = True
    End Sub

End Class
