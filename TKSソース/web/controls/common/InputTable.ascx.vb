﻿Imports System.Data
Imports Dbs.Asphalt.Core
Imports Dbs.Asphalt.Core.Common
Imports Dbs.Asphalt.Core.SystemLogic
Imports Dbs.Asphalt.UI

Public Class ControlEventArgs
    Inherits EventArgs

    Private _rowIndex As Integer = 1
    Private _controlID As String = ""
    Private _cancel As Boolean = False

    Sub New(rowindex As Integer, controlid As String)
        _rowIndex = rowindex
        _controlID = controlid
    End Sub

    ''' <summary>
    ''' イベントが発生した行番号(1～)を取得します。
    ''' </summary>
    Public ReadOnly Property RowIndex As Integer
        Get
            Return _rowIndex
        End Get
    End Property

    ''' <summary>
    ''' 初期化されたコントロールのIDを取得します。
    ''' </summary>
    Public ReadOnly Property ControlID As String
        Get
            Return _controlID
        End Get
    End Property

    ''' <summary>
    ''' 行操作をキャンセルする場合はTrueをセットします。ButtonClickingイベントのみ有効です。
    ''' </summary>
    Public Property Cancel As Boolean
        Get
            Return _cancel
        End Get
        Set(value As Boolean)
            _cancel = value
        End Set
    End Property
End Class

Public Class RowEventArgs
    Inherits EventArgs

    Private _rowIndex As Integer = 1
    Private _headerRow As TableRow
    Private _tableRow As TableRow

    Sub New(rowindex As Integer, header As TableRow, row As TableRow)
        _rowIndex = rowindex
        _headerRow = header
        _tableRow = row
    End Sub

    ''' <summary>
    ''' イベントが発生した行番号(1～)を取得します。
    ''' </summary>
    Public ReadOnly Property RowIndex As Integer
        Get
            Return _rowIndex
        End Get
    End Property

    ''' <summary>
    ''' イベントが発生した行のセルをヘッダテキストから取得します。
    ''' </summary>
    Public Function GetCellByHeaderText(headertext As String) As TableCell
        For col As Integer = 0 To _headerRow.Cells.Count - 1
            If _headerRow.Cells(col).Text = headertext Then
                Return _tableRow.Cells(col)
            End If
        Next
        Return Nothing
    End Function

    ''' <summary>
    ''' イベントが発生した行内のコントロールをIDで取得します。
    ''' </summary>
    Public Function FindControl(id As String) As Object
        Return FindControlSub(_tableRow, id)
    End Function

    Private Function FindControlSub(container As Object, id As String) As Object
        For Each ctr As Object In container.Controls
            If ctr.HasControls Then
                Dim rc As Object = FindControlSub(ctr, id)
                If rc IsNot Nothing Then
                    Return rc
                End If
            Else
                Try
                    If ctr.ID.ToString.Split("_")(0) = id Then
                        Return ctr
                    End If
                Catch
                End Try
            End If
        Next
        Return Nothing
    End Function
End Class

Partial Class InputTable
    Inherits CommonControlBase

    ''' <summary>行が描画されるときに発生します。</summary>
    Public Event RowRender(sender As Object, e As RowEventArgs)
    ''' <summary>セル内のコントロールが描画されるときに発生します。</summary>
    Public Event ControlRender(sender As Object, e As ControlEventArgs)
    ''' <summary>行操作ボタンがクリックされた直後に発生します。</summary>
    Public Event ButtonClicking(sender As Object, e As ControlEventArgs)
    ''' <summary>行操作ボタンがクリックされ、実際の行操作が行われた後に発生します。</summary>
    Public Event ButtonClicked(sender As Object, e As ControlEventArgs)
    ''' <summary>行追加ボタンがクリックされ、実際の行追加が行われた後に発生します。</summary>
    Public Event AddLineButtonClicked(sender As Object, e As EventArgs)

    Public Enum ControlType
        CheckBox = 1
        CheckBoxList = 2
        DropDownList = 3
        RadioButton = 4
        RadioButtonList = 5
        TextBox = 6
        Button = 7
        Literal = 8
        LineFeed = 9
    End Enum

    Public Enum AddLineButtonType
        ''' <summary>使用しない</summary>
        None = 0
        ''' <summary>行追加ボタンのみ使用</summary>
        ButtonOnly = 1
        ''' <summary>追加行数テキストボックスとともに使用</summary>
        WithLineCountTextBox = 2
    End Enum

    Public ColumnControl As New Dictionary(Of String, Dictionary(Of String, ControlType))

    Private _headers As String()
    Private _addLineButton As AddLineButtonType = AddLineButtonType.WithLineCountTextBox
    Private _allowLineNumber As Boolean = True
    Private _allowInsertButton As Boolean = False
    Private _allowDeleteButton As Boolean = False
    Private _allowUpDownButton As Boolean = False
    Private _rowButtonPosition As TextAlign = TextAlign.Right

    Public Property MaxRowsCount As Integer
        Get
            If ViewState("MaxRowsCount") Is Nothing Then
                ViewState("MaxRowsCount") = 0
            End If
            Return ViewState("MaxRowsCount")
        End Get
        Set(value As Integer)
            ViewState("MaxRowsCount") = value
        End Set
    End Property

    ''' <summary>
    ''' 行追加ボタンを使用するかどうかを設定します。
    ''' </summary>
    Public Property AddLineButton As AddLineButtonType
        Get
            Return _addLineButton
        End Get
        Set(value As AddLineButtonType)
            TextAddLineCount.Visible = (value = AddLineButtonType.WithLineCountTextBox)
            ButtonLineAdd.Visible = (value <> AddLineButtonType.None)
            _addLineButton = value
        End Set
    End Property

    ''' <summary>
    ''' 行番号を表示するかどうかを設定します。
    ''' </summary>
    Public Property AllowLineNumber As Boolean
        Get
            Return _allowLineNumber
        End Get
        Set(value As Boolean)
            _allowLineNumber = value
        End Set
    End Property

    ''' <summary>
    ''' 行挿入ボタンを使用するかどうかを設定します。
    ''' </summary>
    Public Property AllowInsertButton As Boolean
        Get
            Return _allowInsertButton
        End Get
        Set(value As Boolean)
            _allowInsertButton = value
        End Set
    End Property

    ''' <summary>
    ''' 行削除ボタンを使用するかどうかを設定します。
    ''' </summary>
    Public Property AllowDeleteButton As Boolean
        Get
            Return _allowDeleteButton
        End Get
        Set(value As Boolean)
            _allowDeleteButton = value
        End Set
    End Property

    ''' <summary>
    ''' 行移動ボタンを使用するかどうかを設定します。
    ''' </summary>
    Public Property AllowUpDownButton As Boolean
        Get
            Return _allowUpDownButton
        End Get
        Set(value As Boolean)
            _allowUpDownButton = value
        End Set
    End Property

    ''' <summary>
    ''' 行操作ボタンの位置を設定します。
    ''' </summary>
    Public Property RowButtonPosition As TextAlign
        Get
            Return _rowButtonPosition
        End Get
        Set(value As TextAlign)
            _rowButtonPosition = value
        End Set
    End Property

    ''' <summary>
    ''' 行数を取得・設定します。設定後には必ずSetupControls()メソッドをコールしてください。
    ''' </summary>
    Public Property RowsCount As Integer
        Get
            If Session(Parent.ToString & "_Rows") Is Nothing Then
                Session(Parent.ToString & "_Rows") = 2
            End If
            Return Session(Parent.ToString & "_Rows")
        End Get
        Set(value As Integer)
            Session(Parent.ToString & "_Rows") = value
        End Set
    End Property

    ''' <summary>
    ''' テーブルコントロールのオブジェクトを返します。
    ''' </summary>
    Public ReadOnly Property TableControl As Table
        Get
            Return TableInput
        End Get
    End Property

    ''' <summary>
    ''' 列のヘッダテキストを配列で指定します。
    ''' </summary>
    Public Property Headers As String()
        Get
            Return _headers
        End Get
        Set(value As String())
            _headers = value
        End Set
    End Property

    ''' <summary>
    ''' 任意の行のコントロールをIDから検索します。
    ''' </summary>
    ''' <param name="rowindex">テーブルの行番号(1～)</param>
    ''' <param name="id">コントロールID</param>
    ''' <returns></returns>
    Public Overloads Function FindControl(rowindex As Integer, id As String) As Object
        Return FindControlSub(TableInput.Rows(rowindex), id)
    End Function

    Private Overloads Function FindControlSub(container As Object, id As String) As Object
        For Each ctr As Object In container.Controls
            If ctr.HasControls Then
                Dim rc As Object = FindControlSub(ctr, id)
                If rc IsNot Nothing Then
                    Return rc
                End If
            Else
                Try
                    If ctr.ID.ToString.Split("_")(0) = id Then
                        Return ctr
                    End If
                Catch
                End Try
            End If
        Next
        Return Nothing
    End Function

    ''' <summary>
    ''' 列にコントロールを追加します。
    ''' </summary>
    ''' <param name="headertext">追加対象の列のヘッダテキスト</param>
    ''' <param name="id">コントロールの一意のID</param>
    ''' <param name="ct">コントロールの種類</param>
    Public Sub AddColumnControl(headertext As String, id As String, ct As ControlType)
        If ColumnControl.ContainsKey(headertext) = False Then
            ColumnControl.Add(headertext, New Dictionary(Of String, ControlType))
        End If
        If ColumnControl(headertext).ContainsKey(id) = False Then
            ColumnControl(headertext).Add(id, ct)
        End If
    End Sub

    ''' <summary>
    ''' 行を追加します。
    ''' </summary>
    ''' <param name="addcount">追加する行数</param>
    Public Function AddRow(Optional addcount As Integer = 1) As Boolean
        For i As Integer = 1 To addcount
            If MaxRowsCount > 0 And RowsCount >= MaxRowsCount Then
                LabelError.Text = "これ以上、明細を追加できません。"
                LabelError.Visible = True
                Return False
            End If

            Dim newrow As New TableRow
            newrow.CssClass = "grid_row"
            For col As Integer = 0 To TableInput.Rows(0).Cells.Count - 1
                Dim cell As New TableCell
                cell.Wrap = False
                cell.VerticalAlign = VerticalAlign.Top
                newrow.Cells.Add(cell)
            Next
            TableInput.Rows.Add(newrow)
            RowsCount = TableInput.Rows.Count

            Dim row As Integer = TableInput.Rows.Count - 1
            For Each ctrs As Object In ColumnControl
                For col As Integer = 0 To TableInput.Rows(0).Cells.Count - 1
                    If TableInput.Rows(0).Cells(col).Text = ctrs.Key Then
                        For Each ct As Object In ctrs.Value
                            Dim ctr As Object
                            If ct.key.ToString.IndexOf("_") >= 0 Then
                                ctr = New Literal
                                ctr.Text = "IDにアンダースコア(_)を使用することはできません。"
                            Else
                                Select Case ct.Value
                                    Case ControlType.CheckBox
                                        ctr = New CheckBox
                                    Case ControlType.CheckBoxList
                                        ctr = New CheckBoxList
                                    Case ControlType.DropDownList
                                        ctr = New DropDownList
                                    Case ControlType.RadioButton
                                        ctr = New RadioButton
                                    Case ControlType.RadioButtonList
                                        ctr = New RadioButtonList
                                    Case ControlType.Button
                                        ctr = New Button
                                    Case ControlType.TextBox
                                        ctr = New TextBox
                                    Case ControlType.LineFeed
                                        ctr = New Literal
                                        ctr.Text = "<br />"
                                    Case Else
                                        ctr = New Literal
                                End Select

                                ctr.ID = ct.Key & "_" & row

                                ' コントロール描画イベント
                                RaiseEvent ControlRender(ctr, New ControlEventArgs(row, ct.key))
                            End If

                            TableInput.Rows(row).Cells(col).Controls.Add(ctr)
                        Next
                    End If
                Next
            Next

            If AllowLineNumber Then
                TableInput.Rows(row).Cells(0).HorizontalAlign = HorizontalAlign.Center
                TableInput.Rows(row).Cells(0).Wrap = False
                TableInput.Rows(row).Cells(0).VerticalAlign = VerticalAlign.Top
                TableInput.Rows(row).Cells(0).Text = row
            End If

            ' 行操作ボタンのセル作成
            Dim celpos As Integer
            If RowButtonPosition = TextAlign.Left Then
                celpos = IIf(AllowLineNumber, 1, 0)
            Else
                celpos = TableInput.Rows(row).Cells.Count - 1
            End If
            TableInput.Rows(row).Cells(celpos).Wrap = False
            TableInput.Rows(row).Cells(celpos).HorizontalAlign = HorizontalAlign.Center

            Dim btn1 As New Button
            Dim btn2 As New Button
            Dim btn3 As New Button
            Dim btn4 As New Button
            If AllowInsertButton Then
                ' 行挿入ボタン
                btn1.CssClass = "line_button" : btn1.ID = "ButtonLineIns" & "_" & row : btn1.Text = "☲" : btn1.ToolTip = "行挿入"
                btn1.TabIndex = -1
                TableInput.Rows(row).Cells(celpos).Controls.Add(btn1)
                RaiseEvent ControlRender(btn1, New ControlEventArgs(row, btn1.ID.Split("_")(0)))
                AddHandler btn1.Click, AddressOf LineButton_Clicked
            End If
            If AllowDeleteButton Then
                ' 行削除ボタン
                btn2.CssClass = "line_button" : btn2.ID = "ButtonLineDel" & "_" & row : btn2.Text = "✕" : btn2.ToolTip = "行削除"
                btn2.TabIndex = -1
                TableInput.Rows(row).Cells(celpos).Controls.Add(btn2)
                RaiseEvent ControlRender(btn2, New ControlEventArgs(row, btn2.ID.Split("_")(0)))
                AddHandler btn2.Click, AddressOf LineButton_Clicked
            End If
            If AllowUpDownButton Then
                ' 上移動ボタン
                btn3.CssClass = "line_button" : btn3.ID = "ButtonLineUp" & "_" & row : btn3.Text = "▲" : btn3.ToolTip = "上移動"
                btn3.TabIndex = -1
                TableInput.Rows(row).Cells(celpos).Controls.Add(btn3)
                RaiseEvent ControlRender(btn3, New ControlEventArgs(row, btn3.ID.Split("_")(0)))
                AddHandler btn3.Click, AddressOf LineButton_Clicked
                ' 下移動ボタン
                btn4.CssClass = "line_button" : btn4.ID = "ButtonLineDown" & "_" & row : btn4.Text = "▼" : btn4.ToolTip = "下移動"
                btn4.TabIndex = -1
                TableInput.Rows(row).Cells(celpos).Controls.Add(btn4)
                RaiseEvent ControlRender(btn4, New ControlEventArgs(row, btn4.ID.Split("_")(0)))
                AddHandler btn4.Click, AddressOf LineButton_Clicked
            End If

            ' 行描画イベント
            RaiseEvent RowRender(TableInput.Rows(row), New RowEventArgs(row, TableInput.Rows(0), TableInput.Rows(row)))
        Next

        Return True
    End Function

    ''' <summary>
    ''' 列とコントロールの定義に従って、テーブルを作成します。ページのInitイベントやRowsCount変更後に必ずコールしてください。
    ''' </summary>
    Public Sub SetupControls()
        TableInput.Rows.Clear()

        Dim rwheader As New TableRow
        rwheader.CssClass = "grid_header"

        ' 行番号表示
        If AllowLineNumber Then
            Dim btncell As New TableHeaderCell
            btncell.Text = "No"
            btncell.Wrap = False
            btncell.VerticalAlign = VerticalAlign.Top
            rwheader.Cells.Add(btncell)
        End If

        ' 行操作ボタンがどれか一つ有効
        If AllowDeleteButton Or AllowInsertButton Or AllowUpDownButton Then
            ' 行操作ボタンを左端に表示
            If RowButtonPosition = TextAlign.Left Then
                Dim btncell As New TableHeaderCell
                btncell.Text = "行操作"
                btncell.VerticalAlign = VerticalAlign.Top
                rwheader.Cells.Add(btncell)
            End If
        End If

        ' ヘッダ行を作成
        For Each header As String In _headers
            Dim cell As New TableHeaderCell
            cell.Text = header
            cell.Wrap = False
            cell.VerticalAlign = VerticalAlign.Top
            rwheader.Cells.Add(cell)
        Next

        ' 行操作ボタンがどれか一つ有効
        If AllowDeleteButton Or AllowInsertButton Or AllowUpDownButton Then
            ' 行操作ボタンを右端に表示
            If RowButtonPosition = TextAlign.Right Then
                Dim btncell As New TableHeaderCell
                btncell.Text = "行操作"
                btncell.Wrap = False
                btncell.VerticalAlign = VerticalAlign.Top
                rwheader.Cells.Add(btncell)
            End If
        End If

        TableInput.Rows.Add(rwheader)

        For row As Integer = 1 To RowsCount - 1
            AddRow()
        Next
    End Sub

    Private Sub LineButton_Clicked(sender As Object, e As EventArgs)
        Dim idx As Integer = sender.ID.ToString.Split("_")(1).ToDecimal
        Dim i As Integer
        Dim ev As New ControlEventArgs(idx, sender.ID.ToString.Split("_")(0))

        RaiseEvent ButtonClicking(sender, ev)
        If ev.Cancel Then
            Return
        End If

        Select Case sender.ID.ToString.Split("_")(0)
            Case "ButtonLineIns"
                If AddRow() Then
                    For i = TableInput.Rows.Count - 1 To idx + 1 Step -1
                        If TableInput.Rows(i).Visible Then
                            row_controls_value_copy(TableInput.Rows(i - 1), TableInput.Rows(i))
                        End If
                    Next
                    row_controls_value_copy(Nothing, TableInput.Rows(i))
                End If
            Case "ButtonLineDel"
                For i = idx To TableInput.Rows.Count - 2
                    row_controls_value_copy(TableInput.Rows(i + 1), TableInput.Rows(i))
                    If Not TableInput.Rows(i).Visible Then
                        Exit For
                    End If
                Next
                row_controls_value_copy(Nothing, TableInput.Rows(i))
                If RowsCount > 2 Then
                    TableInput.Rows.Remove(TableInput.Rows(TableInput.Rows.Count - 1))
                    RowsCount -= 1
                End If
            Case "ButtonLineUp"
                If idx = 0 Then
                    Return
                End If
                row_controls_value_swap(TableInput.Rows(idx - 1), TableInput.Rows(idx))
            Case "ButtonLineDown"
                If idx = TableInput.Rows.Count - 1 Then
                    Return
                End If
                row_controls_value_swap(TableInput.Rows(idx + 1), TableInput.Rows(idx))
        End Select

        RaiseEvent ButtonClicked(sender, New ControlEventArgs(idx, sender.ID.ToString.Split("_")(0)))
    End Sub

    Private Sub row_controls_value_copy(frow As Object, trow As Object)
        On Error Resume Next

        For i As Integer = 0 To trow.Controls.Count - 1
            If Not TypeOf (trow.Controls(i)) Is Button Then
                If TypeOf (trow.Controls(i)) Is DropDownListEx Or
                   TypeOf (trow.Controls(i)) Is CheckBoxList Or
                   TypeOf (trow.Controls(i)) Is RadioButtonList Or
                   Not trow.Controls(i).HasControls Then
                    If frow Is Nothing Then
                        trow.Controls(i).Value = ""
                        trow.Controls(i).RaiseOnSelectedIndexChangedEvent
                        trow.Controls(i).Enabled = True
                    Else
                        trow.Controls(i).Format = frow.Controls(i).Format
                        trow.Controls(i).Minor = frow.Controls(i).Minor
                        trow.Controls(i).Value = frow.Controls(i).Value
                        trow.Controls(i).RaiseOnSelectedIndexChangedEvent
                        trow.Controls(i).Enabled = frow.Controls(i).Enabled
                    End If
                Else
                    If frow Is Nothing Then
                        row_controls_value_copy(Nothing, trow.Controls(i))
                    Else
                        row_controls_value_copy(frow.Controls(i), trow.Controls(i))
                    End If
                End If
            End If
        Next
    End Sub

    Private Sub row_controls_value_swap(frow As Object, trow As Object)
        Dim swap(3) As Object

        On Error Resume Next

        For i As Integer = 0 To trow.Controls.Count - 1
            If Not TypeOf (trow.Controls(i)) Is Button Then
                If TypeOf (trow.Controls(i)) Is DropDownListEx Or
                   TypeOf (trow.Controls(i)) Is CheckBoxList Or
                   TypeOf (trow.Controls(i)) Is RadioButtonList Or
                   Not trow.Controls(i).HasControls Then
                    swap(0) = trow.Controls(i).Format
                    swap(1) = trow.Controls(i).Minor
                    swap(2) = trow.Controls(i).Value
                    swap(3) = trow.Controls(i).Enabled
                    trow.Controls(i).Format = frow.Controls(i).Format
                    trow.Controls(i).Minor = frow.Controls(i).Minor
                    trow.Controls(i).Value = frow.Controls(i).Value
                    trow.Controls(i).Enabled = frow.Controls(i).Enabled
                    frow.Controls(i).Format = swap(0)
                    frow.Controls(i).Minor = swap(1)
                    frow.Controls(i).Value = swap(2)
                    frow.Controls(i).Enabled = swap(3)
                Else
                    row_controls_value_swap(frow.Controls(i), trow.Controls(i))
                End If
            End If
        Next
    End Sub

    Private Sub ButtonLineAdd_Click(sender As Object, e As EventArgs) Handles ButtonLineAdd.Click
        If TextAddLineCount.Text.ToDecimal = 0 Then
            AddRow()
        Else
            AddRow(TextAddLineCount.Text.ToDecimal)
        End If

        RaiseEvent AddLineButtonClicked(sender, e)
    End Sub

    Private Sub InputTable_Load(sender As Object, e As EventArgs) Handles Me.Load
        LabelError.Visible = False
    End Sub
End Class
