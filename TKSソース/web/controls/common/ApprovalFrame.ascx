﻿<%@ Control Language="VB" AutoEventWireup="false" CodeFile="ApprovalFrame.ascx.vb" Inherits="ApprovalFrame" %>
<%@ Register Assembly="Dbs.Asphalt.UI" Namespace="Dbs.Asphalt.UI" TagPrefix="cc1" %>

<asp:Panel runat="server" ID="PanelApproval">
  <cc1:GridViewEx runat="server" ID="GridResult" DataSourceID="DataResult" Width="100%" HeaderStyle-Wrap="false" AllowPaging="false">
    <Columns>
      <asp:TemplateField HeaderText="承認者" ItemStyle-Wrap="false" ItemStyle-Width="250px">
        <ItemTemplate>
          <cc1:UserImage runat="server" ID="ImageUser" IsThumbnail="true" ViewUserName="Right" ajaxuri="../../ajax.aspx"></cc1:UserImage>
        </ItemTemplate>
      </asp:TemplateField>
      <asp:TemplateField HeaderText="承認" ItemStyle-Wrap="false" ItemStyle-Width="10px">
        <ItemTemplate>
          <cc1:DropDownList runat="server" ID="DropApproval" KubunNoListDataName="承認区分"></cc1:DropDownList>
          <asp:Button runat="server" ID="ButtonApproval" Text="更新" CssClass="button_small" OnClick="ButtonApproval_Click" />
        </ItemTemplate>
      </asp:TemplateField>
      <asp:BoundField DataField="承認日時" HeaderText="承認日時" ItemStyle-Width="120px" ItemStyle-HorizontalAlign="Center" />
      <asp:TemplateField HeaderText="コメント" ItemStyle-Wrap="false"  ItemStyle-Width="250px">
        <ItemTemplate>
          <cc1:TextBox runat="server" ID="TextComment" IMEOn="true" Width="250"></cc1:TextBox>
        </ItemTemplate>
      </asp:TemplateField>
      <asp:TemplateField HeaderText="削除" ItemStyle-Wrap="false" ItemStyle-Width="10px" ItemStyle-HorizontalAlign="Center">
        <ItemTemplate>
          <asp:Button runat="server" ID="ButtonDelUser" Text="✖" CssClass="button_smallr" OnClick="ButtonApproval_Click" />
        </ItemTemplate>
      </asp:TemplateField>
      <asp:BoundField DataField="連番" HeaderText="連番" />
      <asp:BoundField DataField="承認者ID" HeaderText="承認者ID" />
      <asp:BoundField DataField="承認区分" HeaderText="承認区分" />
    </Columns>
  </cc1:GridViewEx>
  <asp:ObjectDataSource runat="server" ID="DataResult" SelectMethod="GetData" TypeName="Dbs.Asphalt.Database.DbSystemTableAdapters.T承認TableAdapter" EnablePaging="false">
    <SelectParameters>
      <asp:Parameter Name="データ区分" Type="String" ConvertEmptyStringToNull="False" DefaultValue="" />
      <asp:Parameter Name="キー" Type="String" ConvertEmptyStringToNull="False" DefaultValue="" />
      <asp:Parameter Name="連番" Type="Int16" ConvertEmptyStringToNull="False" DefaultValue="0" />
    </SelectParameters>
  </asp:ObjectDataSource>
  <hr />
  <asp:Button runat="server" ID="ButtonAddUser" Text="✚承認者追加" CssClass="button_small"/>
  <asp:Button runat="server" ID="ButtonSendMail" Text="✉全承認者へ承認依頼メール送信" CssClass="button_small" />
</asp:Panel>
