﻿Imports Dbs.Asphalt.Core
Imports Dbs.Asphalt.Core.Common
Imports Dbs.Asphalt.Core.BaseClass
Imports Dbs.Asphalt.UI

Partial Class PdfViewer
    Inherits CommonControlBase

    Public Width As Unit = Unit.Percentage(100)
    Public Height As Unit = Unit.Pixel(300)
    Public CssClass As String = ""
    Public Style As String = ""
    Public PdfURL As String = ""

    Protected Overrides Sub OnInit(e As System.EventArgs)
        If PdfURL.Substring(0, 4).ToLower <> "http" Then
            PdfURL = "../../../" & PdfURL
        End If
        IframeHTML.Text = "<iframe src=""../js/pdfjs/web/viewer.html?file=" & PdfURL & """ frameborder=""0"" width=""" & Width.ToString & """ height=""" & Height.ToString & """ class=""" & CssClass & """ style=""" & Style & """></iframe>"
        MyBase.OnInit(e)
    End Sub

End Class
