﻿Imports System.Data
Imports Dbs.Asphalt.UI
Imports Dbs.Asphalt.Database
Imports Dbs.Asphalt.Core
Imports Dbs.Asphalt.Core.Common

Partial Class RecordSelector
    Inherits CommonControlBase

    Public Event DataKeyChanged(sender As Object, e As System.EventArgs)

    Private _dataKeyControlID As String
    Private _dataName As String
    Private _keyName As String
    Private _keyIsNumeric As Boolean = True
    Private _findItemList As String

    Private cfg As New Configurator(Nothing)

    Protected Overrides Sub OnLoad(e As System.EventArgs)
        LabelError.Visible = False
        MyBase.OnLoad(e)

        If _findItemList <> "" Then
            Dim fld As String() = _findItemList.Split(",")
            DropFindItem.Items.Clear()
            For i As Integer = 0 To fld.Length - 1
                DropFindItem.AddItem(fld(i), fld(i))
            Next
            DropFindItem.SelectedValue = cfg.GetUserConfigData(Base.Security.UserID, _dataName & "_レコード検索項目", fld(0))
            PanelFind.Visible = True
        Else
            PanelFind.Visible = False
        End If

        Dim finditem As String = cfg.GetUserConfigData(Base.Security.UserID, ParentControl.GetType.ToString & "_検索項目", "")
        If finditem <> "" Then
            DropFindItem.SelectedValue = finditem
        End If
        CheckClearHeader.Checked = (Text.CVal(cfg.GetUserConfigData(Base.Security.UserID, ParentControl.GetType.ToString & "_クリアチェックボックス", "0")) = 1)
    End Sub

    Public Property FindItemList As String
        Get
            Return _findItemList
        End Get
        Set(value As String)
            _findItemList = value
        End Set
    End Property

    Public Property DataKeyControlID As String
        Get
            Return _dataKeyControlID
        End Get
        Set(value As String)
            _dataKeyControlID = value
        End Set
    End Property

    Public Property DataName As String
        Get
            Return _dataName
        End Get
        Set(value As String)
            _dataName = value
        End Set
    End Property

    Public Property KeyName As String
        Get
            Return _keyName
        End Get
        Set(value As String)
            _keyName = value
        End Set
    End Property

    Public Property KeyIsNumeric As Boolean
        Get
            Return _keyIsNumeric
        End Get
        Set(value As Boolean)
            _keyIsNumeric = value
        End Set
    End Property

    Public Property FixedWhere As String
        Get
            Return ViewState("FixedWhere")
        End Get
        Set(value As String)
            ViewState("FixedWhere") = value
        End Set
    End Property

    Public Property AllowClearCheckBox As Boolean
        Get
            Return CheckClearHeader.Visible
        End Get
        Set(value As Boolean)
            CheckClearHeader.Visible = value
        End Set
    End Property

    Public Property ClearItems As Boolean
        Get
            Return CheckClearHeader.Checked
        End Get
        Set(value As Boolean)
            CheckClearHeader.Checked = value
        End Set
    End Property

    Protected Sub Button_Click(sender As Object, e As EventArgs) Handles ButtonLast.Click, ButtonNext.Click, ButtonPrev.Click, ButtonTop.Click
        Dim db As New DbSystemTableAdapters.Sレコード選択TableAdapter(_dataName, _keyName, ViewState("FixedWhere"))
        Dim rs As DbSystem.Sレコード選択DataTable
        Dim ctr As Dbs.Asphalt.UI.TextBox
        Dim keyval As Object
        Dim finditem As String = ""
        Dim finddata As String = ""

        Try
            ctr = ParentControl.FindControl(_dataKeyControlID)

            If _keyIsNumeric Then
                If IsNumeric(ctr.Text) Then
                    keyval = ctr.Text
                Else
                    keyval = 0
                End If
            Else
                keyval = ctr.Text
            End If

            If PanelFind.Visible And TextFindData.Text <> "" Then
                finditem = DropFindItem.SelectedValue
                finddata = TextFindData.Text

                cfg.SetUserConfigData(Base.Security.UserID, _dataName & "_レコード検索項目", finditem)
            End If

            Select Case CType(sender, Button).ID
                Case "ButtonLast"
                    rs = db.GetLast(finditem, finddata)
                Case "ButtonNext"
                    rs = db.GetNext(keyval, finditem, finddata)
                Case "ButtonPrev"
                    rs = db.GetPrev(keyval, finditem, finddata)
                Case Else ' "ButtonTop"
                    rs = db.GetTop(finditem, finddata)
            End Select

            If rs.Count > 0 Then
                ctr.Text = rs(0).キー
                ctr.RaiseTextChanged()
                RaiseEvent DataKeyChanged(Me, e)
                LabelError.Visible = False
            Else
                LabelError.Visible = True
            End If
        Catch ex As Exception
            Base.ErrorMessage = ex.Message
        End Try
    End Sub

    Protected Sub CheckClearHeader_CheckedChanged(sender As Object, e As System.EventArgs) Handles CheckClearHeader.CheckedChanged
        cfg.SetUserConfigData(Base.Security.UserID, ParentControl.GetType.ToString & "_クリアチェックボックス", IIf(CheckClearHeader.Checked, 1, 0))
    End Sub

    Private Sub DropFindItem_SelectedIndexChanged(sender As Object, e As EventArgs) Handles DropFindItem.SelectedIndexChanged
        cfg.SetUserConfigData(Base.Security.UserID, ParentControl.GetType.ToString & "_検索項目", DropFindItem.SelectedValue)
    End Sub
End Class
