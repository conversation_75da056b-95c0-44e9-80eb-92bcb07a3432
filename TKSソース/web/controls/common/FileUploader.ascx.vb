﻿Imports Dbs.Asphalt.UI
Imports Dbs.Asphalt.Core
Imports Dbs.Asphalt.Core.Common
Imports Dbs.Asphalt.Core.Common.Text
Imports Dbs.Asphalt.Core.CoreLogic.System

Partial Class FileUploader
    Inherits CommonControlBase

    Private _dataType As String = ""

    Private logic As FileUploadManager

    Public Property DataType As String
        Get
            Return _dataType
        End Get
        Set(value As String)
            _dataType = value
        End Set
    End Property

    Public Property OverWrite As Boolean
        Get
            Return IsNull(ViewState("OverWrite"), False)
        End Get
        Set(value As Boolean)
            ViewState("OverWrite") = value
        End Set
    End Property

    Public Property DataKey As String
        Get
            Return IsNull(ViewState("DataKey"), "")
        End Get
        Set(value As String)
            ViewState("DataKey") = value
            DataFileList.SelectParameters("データ区分").DefaultValue = DataType
            DataFileList.SelectParameters("キー").DefaultValue = DataKey
            DataFileList.DataBind()
        End Set
    End Property

    Public Property Enabled As Boolean
        Get
            Return IsNull(ViewState("Enabled"), True)
        End Get
        Set(value As Boolean)
            ViewState("Enabled") = value
            Base.UICommon.ContainerEnabled(PanelFileUploader, value)
        End Set
    End Property

    Public Property ArrowEdit As Boolean
        Get
            Return IsNull(ViewState("ArrowEdit"), True)
        End Get
        Set(value As Boolean)
            ViewState("ArrowEdit") = value
            Me.Enabled = value
            PanelFileList.DataBind()
        End Set
    End Property

    Public Property FileSaveStorageType As FileUploadManager.StorageType
        Get
            Return IsNull(ViewState("FileSaveStorageType"), FileUploadManager.StorageType.Database)
        End Get
        Set(value As FileUploadManager.StorageType)
            ViewState("FileSaveStorageType") = value
        End Set
    End Property

    Public Sub AllFileDelete()
        logic.DataType = DataType
        logic.DataKey = DataKey
        logic.Number = 0
        If Not logic.DeleteMainData() Then
            Base.ErrorMessage = logic.LastError
            Return
        End If

        PanelFileList.DataBind()
    End Sub

    Private Sub FileUploader_Init(sender As Object, e As EventArgs) Handles Me.Init
        Base.AddPostbackTrrigerControl(ButtonUpload1, True)
        Base.AddPostbackTrrigerControl(ButtonUpload2, True)

        logic = New FileUploadManager(Base.Security)
        logic.FileSaveStorageType = FileSaveStorageType
        logic.Start()
    End Sub

    Private Sub ButtonUpload_Click(sender As Object, e As EventArgs) Handles ButtonUpload1.Click, ButtonUpload2.Click
        Dim upcnt As Integer = 0
        Dim target As FileUpload

        Select Case sender.ID
            Case "ButtonUpload2"
                target = FileUpload2
            Case Else
                target = FileUpload1
        End Select

        logic.DataType = DataType
        logic.DataKey = DataKey
        logic.OverWrite = OverWrite
        For Each pf As HttpPostedFile In target.PostedFiles
            If pf.ContentLength > 0 Then
                Dim data(pf.ContentLength - 1) As Byte
                pf.InputStream.Read(data, 0, pf.ContentLength)
                logic.Number = 0
                logic.FileName = pf.FileName
                logic.FileType = pf.ContentType
                logic.Data = data
                If Not logic.WriteMainData() Then
                    Base.ErrorMessage = logic.LastError
                    Return
                End If
                upcnt += 1
            End If
        Next
        If upcnt = 0 Then
            Base.ErrorMessage = "まだアップロードできません。"
            Return
        End If

        PanelFileList.DataBind()
    End Sub

    Private Sub PanelFileList_ItemDataBound(sender As Object, e As RepeaterItemEventArgs) Handles PanelFileList.ItemDataBound
        Dim no As Integer = Text.CVal(CType(e.Item.FindControl("LabelNo"), Label).Text)
        Dim update As String = Text.CDateEx(CType(e.Item.FindControl("LabelUpdate"), Label).Text).ToString("yyyyMMddHHmmss")
        Dim delbtn As Button = CType(e.Item.FindControl("ButtonDelete"), Button)
        Dim tn As Image = CType(e.Item.FindControl("ImageThumbnail"), Image)
        tn.ImageUrl = "../../ajax.aspx?cmd=uploadfile&datatype=" & DataType & "&datakey=" & DataKey & "&no=" & no & "&thumbnail=1&imageid=" & update
        delbtn.CommandArgument = DataType & vbTab & DataKey & vbTab & no

        delbtn.Enabled = Enabled And ArrowEdit
    End Sub

    Public Sub ButtonDelete_Click(sender As Object, e As EventArgs)
        Dim ca As String = sender.CommandArgument

        logic.DataType = ca.Split(vbTab)(0)
        logic.DataKey = ca.Split(vbTab)(1)
        logic.Number = ca.Split(vbTab)(2).ToDecimal
        If Not logic.DeleteMainData() Then
            Base.ErrorMessage = logic.LastError
            Return
        End If

        PanelFileList.DataBind()
    End Sub
End Class
