﻿Imports System.IO
Imports Dbs.Asphalt.Core
Imports Dbs.Asphalt.Core.Common
Imports Dbs.Asphalt.UI

Partial Class TitleBar
    Inherits ModuleBase

    Public Overrides ReadOnly Property PageTitle() As String
        Get
            Return ""
        End Get
    End Property

    Protected Overrides Sub OnInit(e As System.EventArgs)
        MyBase.OnInit(e)
    End Sub

    Protected Overrides Sub OnLoad(e As System.EventArgs)
        LabelUserName.Text = Base.Security.UserName

        MenuMain.Security = Base.Security
        MenuMain.VisibleByEnabled = True
        'MenuMain.FindMenuItem("Scheduler").Enabled = Not Base.Security.IsAdmin
        'MenuMain.FindMenuItem("GroupEditor").Enabled = Base.Security.IsUserAdmin
        'MenuMain.FindMenuItem("DatabaseBackup").Enabled = Base.Security.IsAdmin
        'MenuMain.FindMenuItem("TemplateUploader").Enabled = Base.Security.IsAdmin
        MenuMain.FindMenuItem("finder/SystemLogFinder").Enabled = Base.Security.IsAdmin
        MenuMain.FindMenuItem("system/AuthModuleConfig").Enabled = Base.Security.IsUserAdmin
        MenuMain.FindMenuItem("system/SystemConfig").Enabled = Base.Security.IsAdmin

        ' 拡張メニューにインストールされているモジュールを追加
        Try
            Dim exps As String() = Directory.GetFiles(Server.MapPath("./controls/extension"), "*.ascx")
            For Each exp As String In exps
                Dim eo As Object = LoadControl("../extension/" & Path.GetFileName(exp))
                Dim mi As New MenuItem(eo.PageTitle, "extension/" & Path.GetFileNameWithoutExtension(exp))
                MenuMain.FindMenuItem("拡張").ChildItems.Add(mi)
            Next
        Catch
        End Try
        If MenuMain.FindMenuItem("拡張").ChildItems.Count = 0 Then
            MenuMain.FindMenuItem("拡張").Enabled = False
        End If

        UserImage.UserID = Base.Security.UserID

        MyBase.OnLoad(e)
    End Sub

    Public Overrides Sub FinderClose(findername As String, result As System.Collections.Hashtable, resulttargetid As String)

    End Sub

    Public Overrides Sub FinderOpen()

    End Sub

    Public Overrides ReadOnly Property RunAuth() As Dbs.Asphalt.Core.SystemLogic.Security.emUserAuth
        Get
            Return SystemLogic.Security.emUserAuth.一般
        End Get
    End Property

    Protected Sub MenuMain_MenuItemClick(sender As Object, e As System.Web.UI.WebControls.MenuEventArgs) Handles MenuMain.MenuItemClick
        Base.RunCommand(e.Item.Value)
    End Sub

    Public Overrides Sub MessageBoxClose(messageboxname As String, result As Dbs.Asphalt.UI.ModuleBase.MessageBoxResult)

    End Sub

End Class
