﻿<%@ Control Language="VB" AutoEventWireup="false" CodeFile="RecordSelector.ascx.vb" Inherits="RecordSelector" %>
<%@ Register Assembly="Dbs.Asphalt.UI" Namespace="Dbs.Asphalt.UI" TagPrefix="cc1" %>

<div style="float: right; padding-top: 4px; text-align: right;">
  <asp:Panel runat="server" ID="PanelFind" CssClass="leftpane">
    <cc1:DropDownList runat="server" ID="DropFindItem" AutoPostBack="true" TabIndex="-1"></cc1:DropDownList>
    <cc1:TextBox runat="server" ID="TextFindData" Width="100" TabIndex="-1"></cc1:TextBox>
  </asp:Panel>
  <div class="rightpane" style="margin-left: 5px;">
    <asp:Button runat="server" ID="ButtonTop" Text="&nbsp;◀◀&nbsp;" TabIndex="-1" CssClass="button_record" ToolTip="先頭" />
    <asp:Button runat="server" ID="ButtonPrev" Text="&nbsp;◀&nbsp;" TabIndex="-1" CssClass="button_record" ToolTip="前へ" />
    <asp:Button runat="server" ID="ButtonNext" Text="&nbsp;▶&nbsp;" TabIndex="-1" CssClass="button_record" ToolTip="次へ" />
    <asp:Button runat="server" ID="ButtonLast" Text="&nbsp;▶▶&nbsp;" TabIndex="-1" CssClass="button_record" ToolTip="最終" />
  </div>
  <div class="clear"></div>
  <cc1:CheckBox runat="server" ID="CheckClearHeader" Text="新規／登録後に項目をクリアする" AutoPostBack="true" Visible="false" TabIndex="-1" /><br />
  <asp:label runat="server" ID="LabelError" text="これ以上表示できるデータはありません" CssClass="notice_message_mini" Visible="false"></asp:label>
</div>
<div class="clear"></div>
