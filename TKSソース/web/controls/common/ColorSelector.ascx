﻿<%@ Control Language="VB" AutoEventWireup="false" CodeFile="ColorSelector.ascx.vb" Inherits="ColorSelector" %>

<asp:ImageButton runat="server" ID="Color0" CssClass="schedule_cell_data0" Width="10" Height="10" BorderWidth="3" BorderColor="#cccccc" ImageUrl="../../images/brank.gif" />
<asp:ImageButton runat="server" ID="Color1" CssClass="schedule_cell_data1" Width="10" Height="10" BorderWidth="3" BorderColor="#cccccc" ImageUrl="../../images/brank.gif" />
<asp:ImageButton runat="server" ID="Color2" CssClass="schedule_cell_data2" Width="10" Height="10" BorderWidth="3" BorderColor="#cccccc" ImageUrl="../../images/brank.gif" />
<asp:ImageButton runat="server" ID="Color3" CssClass="schedule_cell_data3" Width="10" Height="10" BorderWidth="3" BorderColor="#cccccc" ImageUrl="../../images/brank.gif" />
<asp:ImageButton runat="server" ID="Color4" CssClass="schedule_cell_data4" Width="10" Height="10" BorderWidth="3" BorderColor="#cccccc" ImageUrl="../../images/brank.gif" />
<asp:ImageButton runat="server" ID="Color5" CssClass="schedule_cell_data5" Width="10" Height="10" BorderWidth="3" BorderColor="#cccccc" ImageUrl="../../images/brank.gif" />
<asp:ImageButton runat="server" ID="Color6" CssClass="schedule_cell_data6" Width="10" Height="10" BorderWidth="3" BorderColor="#cccccc" ImageUrl="../../images/brank.gif" />
<asp:ImageButton runat="server" ID="Color7" CssClass="schedule_cell_data7" Width="10" Height="10" BorderWidth="3" BorderColor="#cccccc" ImageUrl="../../images/brank.gif" />
