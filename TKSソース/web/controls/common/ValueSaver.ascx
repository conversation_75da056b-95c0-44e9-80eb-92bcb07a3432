﻿<%@ Control Language="VB" AutoEventWireup="false" CodeFile="ValueSaver.ascx.vb" Inherits="ValueSaver" %>
<%@ Register assembly="Dbs.Asphalt.UI" namespace="Dbs.Asphalt.UI" tagprefix="cc1" %>

<cc1:ItemFrame runat="server" ID="PanelValueSaver">
  <cc1:ItemPanel runat="server" Text="条件名" ItemWidth="60">
    <asp:Panel runat="server" ID="PanelNormal">
      <cc1:DropDownList ID="DropValueName" runat="server" Width="300" AutoPostBack="true"></cc1:DropDownList>
      <asp:Button runat="server" ID="ButtonSave" Text="保存" CssClass="button_small" />
      <asp:Button runat="server" ID="ButtonRemove" Text="削除" CssClass="button_small" />
    </asp:Panel>
    <asp:Panel runat="server" ID="PanelEdit">
      <cc1:TextBox runat="server" ID="TextValueName" Width="300" MaxLength="50"></cc1:TextBox>
      <asp:Button runat="server" ID="ButtonEditSave" Text="保存" CssClass="button_small" />
      <asp:Button runat="server" ID="ButtonEditCancel" Text="キャンセル" CssClass="button_small" /><br />
      <span class="guide">条件名を入力して保存ボタンをクリックしてください。</span><br />
      <asp:Label runat="server" ID="LabelError" Text="条件名が未入力です。" Visible="false" CssClass="notice_message_mini"></asp:Label>
    </asp:Panel>
  </cc1:ItemPanel>
</cc1:ItemFrame>
