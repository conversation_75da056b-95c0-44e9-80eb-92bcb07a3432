﻿Imports System.Data
Imports Dbs.Asphalt.Core
Imports Dbs.Asphalt.Core.Common
Imports Dbs.Asphalt.Core.SystemLogic
Imports Dbs.Asphalt.UI

Partial Class ColorSelector
    Inherits CommonControlBase

    Public Property Enabled() As Boolean
        Get
            Return Me.ViewState("Enabled")
        End Get
        Set(value As Boolean)
            Me.ViewState("Enabled") = value

            Color0.Visible = (Me.ViewState("Enabled") Or Me.ViewState("Color") = 0)
            Color1.Visible = (Me.ViewState("Enabled") Or Me.ViewState("Color") = 1)
            Color2.Visible = (Me.ViewState("Enabled") Or Me.ViewState("Color") = 2)
            Color3.Visible = (Me.ViewState("Enabled") Or Me.ViewState("Color") = 3)
            Color4.Visible = (Me.ViewState("Enabled") Or Me.ViewState("Color") = 4)
            Color5.Visible = (Me.ViewState("Enabled") Or Me.ViewState("Color") = 5)
            Color6.Visible = (Me.ViewState("Enabled") Or Me.ViewState("Color") = 6)
            Color7.Visible = (Me.ViewState("Enabled") Or Me.ViewState("Color") = 7)
        End Set
    End Property

    Public Property SelectedColor() As Integer
        Get
            Return Me.ViewState("Color")
        End Get
        Set(value As Integer)
            If value < 0 Or value > 7 Then
                value = 0
            End If
            Me.ViewState("Color") = value
            Color_Click(FindControl("Color" & value), Nothing)
        End Set
    End Property


    Protected Sub Color_Click(sender As Object, e As System.Web.UI.ImageClickEventArgs) Handles Color0.Click, Color1.Click, Color2.Click, Color3.Click, Color4.Click, Color5.Click, Color6.Click, Color7.Click
        Color0.BorderColor = Drawing.Color.FromArgb(&HCC, &HCC, &HCC)
        Color1.BorderColor = Drawing.Color.FromArgb(&HCC, &HCC, &HCC)
        Color2.BorderColor = Drawing.Color.FromArgb(&HCC, &HCC, &HCC)
        Color3.BorderColor = Drawing.Color.FromArgb(&HCC, &HCC, &HCC)
        Color4.BorderColor = Drawing.Color.FromArgb(&HCC, &HCC, &HCC)
        Color5.BorderColor = Drawing.Color.FromArgb(&HCC, &HCC, &HCC)
        Color6.BorderColor = Drawing.Color.FromArgb(&HCC, &HCC, &HCC)
        Color7.BorderColor = Drawing.Color.FromArgb(&HCC, &HCC, &HCC)

        CType(sender, ImageButton).BorderColor = Drawing.Color.Black

        Me.ViewState("Color") = Right(CType(sender, ImageButton).ID, 1)
    End Sub
End Class
