﻿<%@ Control Language="VB" AutoEventWireup="false" CodeFile="GridPager.ascx.vb" Inherits="GridPager" %>
<%@ Register Assembly="Dbs.Asphalt.UI" Namespace="Dbs.Asphalt.UI" TagPrefix="cc1" %>

<div class="pager">
  <div class="leftpane">
    <cc1:DropDownList runat="server" ID="DropPageLine" AutoPostBack="True"></cc1:DropDownList>
    <asp:Label runat="server" ID="LabelFoundCount" Text=""></asp:Label>
  </div>
  <asp:Panel runat="server" ID="PanelPager" CssClass="rightpane">
    <asp:Button runat="server" ID="LinkTopPage" Text="◀◀" CssClass="button_small" ToolTip="先頭ページへ" />
    <asp:Button runat="server" ID="LinkPrevPage" Text="◀" CssClass="button_small" ToolTip="前ページ" />
    <cc1:DropDownList runat="server" ID="DropPage" AutoPostBack="True"></cc1:DropDownList>
    <asp:Label runat="server" ID="LabelPager" Text=""></asp:Label>
    <asp:Button runat="server" ID="LinkNextPage" Text="▶" CssClass="button_small" ToolTip="次ページ" />
    <asp:Button runat="server" ID="LinkLastPage" Text="▶▶" CssClass="button_small" ToolTip="最終ページへ" />
  </asp:Panel>
  <div class="clear"></div>

  <asp:Panel runat="server" ID="PanelSelect">
    <asp:LinkButton runat="server" ID="ButtonAllCheck" Text="全選択" CssClass="button_small" />
    |
    <asp:LinkButton runat="server" ID="ButtonAllUnCheck" Text="全解除" CssClass="button_small" />
  </asp:Panel>
</div>
