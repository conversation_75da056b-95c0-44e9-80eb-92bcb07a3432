﻿Imports System.Data
Imports Dbs.Asphalt.Core
Imports Dbs.Asphalt.Core.Common
Imports Dbs.Asphalt.Core.SystemLogic
Imports Dbs.Asphalt.UI

Partial Class ApprovalFrame
    Inherits CommonControlBase

    Public Event ButtonAddUserClick(ByVal sender As Object, ByVal e As System.EventArgs)

    Private appman As ApprovalManager

    Protected Overrides Sub OnInit(e As EventArgs)
        MyBase.OnInit(e)

        appman = New ApprovalManager(Base.Security)

    End Sub

    Public Property DataKubun As String
        Get
            Return DataResult.SelectParameters("データ区分").DefaultValue
        End Get
        Set(value As String)
            DataResult.SelectParameters("データ区分").DefaultValue = value
        End Set
    End Property

    Public Property DataKey As String
        Get
            Return DataResult.SelectParameters("キー").DefaultValue
        End Get
        Set(value As String)
            DataResult.SelectParameters("キー").DefaultValue = value
            Me.Visible = (value <> "")
            If value <> "" Then
                appman.InitApprovalUserList(DataKubun, DataKey)
            End If
        End Set
    End Property

    Public ReadOnly Property ApprovalCondition As ApprovalManager.承認状態
        Get
            Return appman.ApprovalCondition(DataKubun, DataKey)
        End Get
    End Property

    Public Property Enabled As Boolean
        Get
            Return PanelApproval.Enabled
        End Get
        Set(value As Boolean)
            PanelApproval.Enabled = value
        End Set
    End Property

    Public Property UriOnMail() As String
        Get
            Return IsNull(ViewState("UriOnMail"), "")
        End Get
        Set(value As String)
            ViewState("UriOnMail") = value
        End Set
    End Property

    Public Function AddUser(userid As String) As Boolean
        If Not appman.AddApprovalUser(DataKubun, DataKey, userid) Then
            Base.ErrorMessage = appman.LastError
            Return False
        End If
        Return True
    End Function

    Private Sub ApprovalFrame_Load(sender As Object, e As EventArgs) Handles Me.Load
        Base.SetClientYesNoBox(ButtonSendMail, "全ての未承認者に承認依頼のメールを送信します。")
    End Sub

    Private Sub ApprovalFrame_PreRender(sender As Object, e As EventArgs) Handles Me.PreRender
        GridResult.DataBind()
    End Sub

    Private Sub GridResult_RowDataBound(sender As Object, e As GridViewExRowEventArgs) Handles GridResult.RowDataBound
        If e.Row.RowType = DataControlRowType.DataRow Then
            Dim dr As DataRowView = e.Row.DataItem
            Dim mn As New MasterName(Base.Security)

            Dim img As UserImage = CType(e.RowCell("承認者").FindControl("ImageUser"), UserImage)
            img.UserID = dr("承認者ID")

            Dim ak As DropDownList = e.RowCellFirstControl("承認").FindControl("DropApproval")
            Dim ab As Button = e.RowCellFirstControl("承認").FindControl("ButtonApproval")
            ak.SelectedValue = dr("承認区分")
            ak.CssClass = "approval_type" & dr("承認区分")
            ak.ViewOnly = Not (dr("承認者ID") = Base.Security.UserID)
            ab.Visible = Not ak.ViewOnly
            ab.CommandArgument = e.Row.RowIndex

            e.RowCellFirstControl("承認").FindControl("ButtonDelUser").CommandArgument = dr("承認者ID")

            Dim cb As TextBox = e.RowCellFirstControl("コメント").FindControl("TextComment")
            cb.Text = dr("コメント")
            cb.ViewOnly = Not (dr("承認者ID") = Base.Security.UserID)

            Base.SetClientYesNoBox(e.RowCellFirstControl("削除").FindControl("ButtonDelUser"), Message.MessageText("MS_QUESTDELETE"))
        End If

        e.RowCell("連番").Visible = False
        e.RowCell("承認者ID").Visible = False
        e.RowCell("承認区分").Visible = False
    End Sub

    Protected Sub ButtonApproval_Click(sender As Object, e As EventArgs)
        Dim idx As Short = sender.CommandArgument.ToString.ToDecimal()
        Select Case sender.ID
            Case "ButtonApproval"
                Dim au As DropDownList = CType(GridResult.RowsCell(idx, "連番").FindControl("DropApproval"), DropDownList)
                Dim cb As TextBox = GridResult.RowsCell(idx, "コメント").FindControl("TextComment")
                If Not appman.SetApproval(DataKubun, DataKey, CType(GridResult.RowsCell(idx, "承認者").FindControl("ImageUser"), UserImage).UserID, au.SelectedValue, cb.Text) Then
                    Base.ErrorMessage = appman.LastError
                End If
            Case "ButtonDelUser"
                If Not appman.RemoveApprovalUser(DataKubun, DataKey, sender.CommandArgument) Then
                    Base.ErrorMessage = appman.LastError
                End If
        End Select
    End Sub

    Private Sub ButtonAddUser_Click(sender As Object, e As EventArgs) Handles ButtonAddUser.Click
        RaiseEvent ButtonAddUserClick(Me, e)
    End Sub

    Private Sub ButtonSendMail_Click(sender As Object, e As EventArgs) Handles ButtonSendMail.Click
        If Not appman.SendMailAllApprovalUsers(DataKubun, DataKey, Base.GetBaseURL & UriOnMail) Then
            Base.ErrorMessage = appman.LastError
        End If

        Base.Message = Message.MessageText("MS_SENDCOMPLETE", "承認者")
    End Sub

End Class
