﻿<%@ Control Language="VB" AutoEventWireup="false" CodeFile="FileUploader.ascx.vb" Inherits="FileUploader" %>
<%@ Register Assembly="Dbs.Asphalt.UI" Namespace="Dbs.Asphalt.UI" TagPrefix="cc1" %>

<cc1:ItemFrame runat="server" ID="PanelFileUploader" Text="添付ファイル" CssClass="uploader_frame">
  <asp:Repeater runat="server" ID="PanelFileList" DataSourceID="DataFileList">
    <ItemTemplate>
      <div class="uploadfile_frame">
        <a target="_blank" title="<%#DataBinder.Eval(Container.DataItem, "ファイル名") & vbNewLine%><%#DataBinder.Eval(Container.DataItem, "登録日時")%>" href="ajax.aspx?cmd=uploadfile&datatype=<%#DataBinder.Eval(Container.DataItem, "データ区分")%>&datakey=<%#DataBinder.Eval(Container.DataItem, "キー")%>&no=<%#DataBinder.Eval(Container.DataItem, "連番")%>&imageid=<%#CDate(DataBinder.Eval(Container.DataItem, "登録日時")).ToString("yyyyMMddHHmmss")%>&storage=<%#Me.FileSaveStorageType%>">
          <div class="uploadfile">
            <div class="filename">
              <asp:Label runat="server" ID="LabelFileName" Text='<%#DataBinder.Eval(Container.DataItem, "ファイル名")%>'></asp:Label><br />
            </div>
            <asp:Image runat="server" ID="ImageThumbnail" />

            <asp:Label runat="server" ID="LabelNo" Text='<%#DataBinder.Eval(Container.DataItem, "連番")%>' Visible="false"></asp:Label>
            <asp:Label runat="server" ID="LabelUpdate" Text='<%#DataBinder.Eval(Container.DataItem, "登録日時")%>' Visible="false"></asp:Label>
          </div>
        </a>
        <asp:Button runat="server" ID="ButtonDelete" Text="×" CssClass="button_smallr" OnClick="ButtonDelete_Click" OnClientClick="return confirm('添付ファイルを削除しますか？');" />
      </div>
    </ItemTemplate>
  </asp:Repeater>
  <asp:ObjectDataSource runat="server" ID="DataFileList" SelectMethod="GetData" TypeName="Dbs.Asphalt.Database.DbSystemTableAdapters.T添付ファイルTableAdapter">
    <SelectParameters>
      <asp:Parameter Name="データ区分" Type="String" ConvertEmptyStringToNull="false" />
      <asp:Parameter Name="キー" Type="String" ConvertEmptyStringToNull="false" />
      <asp:Parameter Name="連番" Type="Int16" DefaultValue="0" ConvertEmptyStringToNull="False" />
    </SelectParameters>
  </asp:ObjectDataSource>

  <div class="clear"></div>
  <br />
  <cc1:FileUpload runat="server" ID="FileUpload1" AllowMultiple="true" UploadButtonControlID="ButtonUpload1" CssClass="uploader_file" Accept="*/*" CaptureCamera="false" />
  <cc1:FileUpload runat="server" ID="FileUpload2" AllowMultiple="true" UploadButtonControlID="ButtonUpload2" CssClass="uploader_camera" Accept="image/*" CaptureCamera="true" />
  <asp:Button runat="server" ID="ButtonUpload1" Text="アップロード" />
  <asp:Button runat="server" ID="ButtonUpload2" Text="アップロード" />
  <div class="clear"></div>
</cc1:ItemFrame>
