﻿Imports System.Data
Imports Dbs.Asphalt.Core
Imports Dbs.Asphalt.Core.Common
Imports Dbs.Asphalt.Core.SystemLogic
Imports Dbs.Asphalt.UI

Partial Class GridPager
    Inherits CommonControlBase

    Public Event LinkPrevPageClick(sender As Object, e As System.EventArgs)
    Public Event LinkNextPageClick(sender As Object, e As System.EventArgs)
    Public Event LinkTopPageClick(sender As Object, e As System.EventArgs)
    Public Event LinkLastPageClick(sender As Object, e As System.EventArgs)
    Public Event DropPageSelectedIndexChanged(sender As Object, e As System.EventArgs)
    Public Event DropPageLineSelectedIndexChanged(sender As Object, e As System.EventArgs)

    Private _targetGridViewControlID As String

    Private WithEvents _dataSource As ObjectDataSource
    Private WithEvents _gridView As GridViewEx

    Private _dataList As DataList
    Private _uiCommon As New Dbs.Asphalt.UI.UICommon(Nothing)

    Private _checkbuf As Hashtable

    Private cfg As Configurator = New Configurator(Nothing)

    Protected Overrides Sub OnInit(e As System.EventArgs)
        MyBase.OnInit(e)

        _dataList = New DataList(Base.Security)

        _gridView = find_object(Parent, _targetGridViewControlID)
        _dataSource = find_object(Parent, _gridView.DataSourceID)

        _uiCommon.ListControlDataBind(DropPageLine, _dataList.PageLineList)
        If Not ParentControl Is Nothing Then
            Dim defline As String = cfg.GetUserConfigData(Base.Security.UserID, TypeName(ParentControl) & "_ページ行数", "50")
            DropPageLine.SelectedValue = defline
            _gridView.PageSize = Text.CVal(defline)
        Else
            DropPageLine.SelectedValue = "50"
            _gridView.PageSize = 50
        End If

        If Not IsPostBack Then
            ClearCheckCondition()
        End If
    End Sub

    Protected Overrides Sub OnLoad(e As System.EventArgs)
        ' チェック状態の復元
        If Session("チェック状態_" & TypeName(ParentControl)) Is Nothing Then
            _checkbuf = New Hashtable
        Else
            _checkbuf = Session("チェック状態_" & TypeName(ParentControl))
        End If

        MyBase.OnLoad(e)
    End Sub

    ''' <summary>チェックボックスとなっている列のヘッダテキストを指定します。</summary>
    Public Property CheckBoxHeaderText() As String
        Get
            Return ViewState("CheckBoxHeaderText")
        End Get
        Set(value As String)
            ViewState("CheckBoxHeaderText") = value
        End Set
    End Property

    ''' <summary>チェックボックスの制御を行う一意となるキーの列のヘッダテキストを指定します。</summary>
    Public Property CheckBoxPrimaryKeyHeaderText() As String
        Get
            Return ViewState("CheckBoxPrimaryKeyHeaderText")
        End Get
        Set(value As String)
            ViewState("CheckBoxPrimaryKeyHeaderText") = value
        End Set
    End Property

    ''' <summary>ページング処理するGridViewコントロールのIDを指定します。</summary>
    Public Property TargetGridViewControlID() As String
        Get
            Return _targetGridViewControlID
        End Get
        Set(value As String)
            _targetGridViewControlID = value
        End Set
    End Property

    Protected Sub _dataSource_Selected(sender As Object, e As System.Web.UI.WebControls.ObjectDataSourceStatusEventArgs) Handles _dataSource.Selected
        If IsNumeric(e.ReturnValue) Then
            If e.ReturnValue = 0 Then
                LabelFoundCount.Text = Message.MessageText("EM_NODATA")
                PanelPager.Visible = False
                PanelSelect.Visible = False
            Else
                LabelFoundCount.Text = Message.MessageText("MS_DATAFOUND", Text.TextFormat(e.ReturnValue, 13, Text.FormatContents.tbCurrency, 0, True).Trim)
                PanelPager.Visible = True
                PanelSelect.Visible = True
            End If
        End If
    End Sub

    Protected Sub _gridView_DataBound(sender As Object, e As System.EventArgs) Handles _gridView.DataBound

        DropPageLine.SelectedValue = _gridView.PageSize

        ' ページリストの作成
        DropPage.Items.Clear()
        For i As Integer = (_gridView.PageIndex + 1) - 100 To (_gridView.PageIndex + 1) + 100
            If i >= 1 And i <= _gridView.PageCount Then
                DropPage.AddItem(i, i)
            End If
        Next
        DropPage.SelectedValue = _gridView.PageIndex + 1

        ' 前へリンク
        LinkTopPage.Enabled = (_gridView.PageIndex > 0)
        LinkPrevPage.Enabled = (_gridView.PageIndex > 0)

        ' 次へリンク
        LinkNextPage.Enabled = (_gridView.PageIndex < _gridView.PageCount - 1)
        LinkLastPage.Enabled = (_gridView.PageIndex < _gridView.PageCount - 1)

        ' メッセージ
        'LabelPager.Text = Message.MessageText("MS_PAGERSTATUS", _
        '                                      Text.TextFormat(_gridView.PageIndex + 1, 13, Text.FormatContents.tbCurrency, 0, True).Trim, _
        '                                      Text.TextFormat(_gridView.PageCount, 13, Text.FormatContents.tbCurrency, 0, True).Trim)
        LabelPager.Text = Text.TextFormat(_gridView.PageCount, 13, Text.FormatContents.tbCurrency, 0, True).Trim

        LoadCheckCondition()
    End Sub

    Protected Sub _gridView_Sorting(sender As Object, e As System.Web.UI.WebControls.GridViewSortEventArgs) Handles _gridView.Sorting
        SaveCheckCondition()
    End Sub

    Protected Sub LinkTopPage_Click(sender As Object, e As System.EventArgs) Handles LinkTopPage.Click
        Try
            _gridView.PageIndex = 0
        Catch ex As Exception
        End Try
        RaiseEvent LinkTopPageClick(sender, e)

        SaveCheckCondition()
    End Sub

    Protected Sub LinkLastPage_Click(sender As Object, e As System.EventArgs) Handles LinkLastPage.Click
        Try
            _gridView.PageIndex = _gridView.PageCount - 1
        Catch ex As Exception
        End Try
        RaiseEvent LinkLastPageClick(sender, e)

        SaveCheckCondition()
    End Sub

    Protected Sub LinkPrevPage_Click(sender As Object, e As System.EventArgs) Handles LinkPrevPage.Click
        Try
            _gridView.PageIndex = DropPage.SelectedValue - 2
        Catch ex As Exception
        End Try
        RaiseEvent LinkPrevPageClick(sender, e)

        SaveCheckCondition()
    End Sub

    Protected Sub LinkNextPage_Click(sender As Object, e As System.EventArgs) Handles LinkNextPage.Click
        Try
            _gridView.PageIndex = DropPage.SelectedValue
        Catch ex As Exception
        End Try
        RaiseEvent LinkNextPageClick(sender, e)

        SaveCheckCondition()
    End Sub

    Protected Sub DropPage_SelectedIndexChanged(sender As Object, e As System.EventArgs) Handles DropPage.SelectedIndexChanged
        _gridView.PageIndex = DropPage.SelectedValue - 1
        RaiseEvent DropPageSelectedIndexChanged(sender, e)

        SaveCheckCondition()
    End Sub

    Protected Sub DropPageLine_SelectedIndexChanged(sender As Object, e As System.EventArgs) Handles DropPageLine.SelectedIndexChanged
        _gridView.PageSize = DropPageLine.SelectedValue
        _gridView.PageIndex = 0
        RaiseEvent DropPageLineSelectedIndexChanged(sender, e)

        SaveCheckCondition()

        If Not ParentControl Is Nothing Then
            cfg.SetUserConfigData(Base.Security.UserID, TypeName(ParentControl) & "_ページ行数", DropPageLine.SelectedValue)
        End If
    End Sub

    Protected Sub ButtonAllCheck_Click(sender As Object, e As System.EventArgs) Handles ButtonAllCheck.Click
        AllCheckCondition(True)
    End Sub

    Protected Sub ButtonAllUnCheck_Click(sender As Object, e As System.EventArgs) Handles ButtonAllUnCheck.Click
        AllCheckCondition(False)
    End Sub

    ''' <summary>チェック状態を復元して、UIに反映します。</summary>
    Public Sub LoadCheckCondition()
        Dim cell_checkbox As TableCell
        Dim cell_primarykey As TableCell

        Try
            If ViewState("CheckBoxHeaderText") = "" Or ViewState("CheckBoxPrimaryKeyHeaderText") = "" Then
                PanelSelect.Visible = False
                Return
            ElseIf Not _gridView.ColumnByHeaderText(ViewState("CheckBoxHeaderText")).Visible Then
                PanelSelect.Visible = False
                Return
            End If
        Catch ex As Exception
        End Try

        If Session("チェック状態_" & TypeName(ParentControl)) Is Nothing Then
            _checkbuf = New Hashtable
        Else
            _checkbuf = Session("チェック状態_" & TypeName(ParentControl))
        End If

        For i As Integer = 0 To _gridView.Rows.Count - 1
            cell_checkbox = _gridView.RowsCell(i, ViewState("CheckBoxHeaderText"))
            cell_primarykey = _gridView.RowsCell(i, ViewState("CheckBoxPrimaryKeyHeaderText"))
            If Not cell_checkbox Is Nothing And Not cell_primarykey Is Nothing Then
                Try
                    If Not _checkbuf(cell_primarykey.Text) Is Nothing Then
                        CType(cell_checkbox.FindControl("CheckBox"), CheckBox).Checked = True
                    Else
                        CType(cell_checkbox.FindControl("CheckBox"), CheckBox).Checked = False
                    End If
                Catch ex As Exception
                End Try
            End If
        Next
    End Sub

    ''' <summary>UIの全ての行のチェックコントロールのチェック状態を保存します。</summary>
    Public Sub SaveCheckCondition()
        Dim cell_checkbox As TableCell
        Dim cell_primarykey As TableCell

        If ViewState("CheckBoxHeaderText") = "" Or ViewState("CheckBoxPrimaryKeyHeaderText") = "" Then
            PanelSelect.Visible = False
            Return
        End If

        For i As Integer = 0 To _gridView.Rows.Count - 1
            cell_checkbox = _gridView.RowsCell(i, ViewState("CheckBoxHeaderText"))
            cell_primarykey = _gridView.RowsCell(i, ViewState("CheckBoxPrimaryKeyHeaderText"))
            If Not cell_checkbox Is Nothing And Not cell_primarykey Is Nothing Then
                Try
                    If CType(cell_checkbox.FindControl("CheckBox"), CheckBox).Checked Then
                        _checkbuf(cell_primarykey.Text) = True
                    Else
                        _checkbuf.Remove(cell_primarykey.Text)
                    End If
                Catch ex As Exception
                End Try
            End If
        Next

        Session("チェック状態_" & TypeName(ParentControl)) = _checkbuf
    End Sub

    Public Sub ClearCheckCondition()
        Session("チェック状態_" & TypeName(ParentControl)) = Nothing
    End Sub

    ''' <summary>全ての行のチェックコントロールのチェック状態を変更します。</summary>
    Public Sub AllCheckCondition(cond As Boolean)
        Dim uicmn As New Dbs.Asphalt.UI.UICommon(Nothing)
        Dim dv As DataView = CType(_dataSource.Select, DataView)

        If ViewState("CheckBoxHeaderText") = "" Or ViewState("CheckBoxPrimaryKeyHeaderText") = "" Then
            Return
        End If

        If cond Then
            For i As Integer = 0 To dv.Count - 1
                _checkbuf(dv(i)(ViewState("CheckBoxPrimaryKeyHeaderText")).ToString) = True
            Next
        Else
            _checkbuf = New Hashtable
        End If

        Session("チェック状態_" & TypeName(ParentControl)) = _checkbuf
        LoadCheckCondition()
    End Sub

    ''' <summary>チェック状態を格納したハッシュテーブルを返します。</summary>
    Public ReadOnly Property CheckCondition() As Object()
        Get
            Dim work As Object() = Nothing
            Dim i As Integer = 0
            For Each buf As Object In _checkbuf.Keys
                Array.Resize(work, i + 1)
                work(i) = buf
                i += 1
            Next
            If Not work Is Nothing Then
                Array.Sort(work)
            End If
            Return work
        End Get
    End Property

    ''' <summary>複数選択されたキー項目をカンマ区切りにした文字列を返します。</summary>
    Public Property SelectedValues As String
        Get
            Dim idlist As String = ""
            If Not CheckCondition Is Nothing Then
                For Each v As Object In CheckCondition
                    idlist &= IIf(idlist = "", "", ",") & v.ToString
                Next
            End If
            Return idlist
        End Get
        Set(value As String)
            Dim fld As String() = value.Split(",")
            _checkbuf = New Hashtable
            For Each itm As String In fld
                _checkbuf(itm) = True
            Next
            Session("チェック状態_" & TypeName(ParentControl)) = _checkbuf
            LoadCheckCondition()
        End Set
    End Property

    Private Function find_object(page As Object, id As String) As Object
        For i As Integer = 0 To page.Controls.Count - 1
            If page.Controls(i).ID = id Then
                Return page.Controls(i)
            End If
            If page.Controls(i).HasControls Then
                Dim obj As Object = find_object(page.Controls(i), id)
                If Not obj Is Nothing Then
                    Return obj
                End If
            End If
        Next
        Return Nothing
    End Function

End Class
