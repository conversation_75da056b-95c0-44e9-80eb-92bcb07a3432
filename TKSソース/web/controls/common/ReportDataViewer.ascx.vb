﻿Imports System.Data
Imports Dbs.Asphalt.UI
Imports Dbs.Asphalt.Core.Common

Partial Class ReportDataViewer
    Inherits CommonControlBase

    Public Event ItemDataBound(sender As Object, e As DataGridItemEventArgs)
    Public Event ItemCreated(sender As Object, e As DataGridItemEventArgs)

    Public ReadOnly Property GridControl As DataGrid
        Get
            Return GridResult
        End Get
    End Property

    Public Property DataSource As Object
        Get
            Return GridResult.DataSource
        End Get
        Set(value As Object)
            'If value.Table.Rows.Count > 5000 Then
            '    CType(Page, PageBase).ErrorMessage = "検索されたデータが多すぎます。条件範囲を絞るか、データをダウンロードして照会してください。"
            '    Return
            'End If

            GridResult.DataSource = value
            GridResult.PageSize = 5000
            GridResult.CurrentPageIndex = 0
            GridResult.DataBind()
        End Set
    End Property

    Private Sub GridResult_ItemDataBound(sender As Object, e As DataGridItemEventArgs) Handles GridResult.ItemDataBound
        Dim dv As DataView = CType(GridResult.DataSource, DataView)

        For col As Integer = 0 To dv.Table.Columns.Count - 1
            If dv.Table.Columns(col).ColumnName.Substring(0, 1) = "_" Then
                e.Item.Cells(col).Visible = False

                If e.Item.ItemIndex >= 0 And dv.Table.Columns(col).ColumnName = "_行区分" Then
                    If dv(e.Item.ItemIndex)("_行区分") > 0 Then
                        e.Item.CssClass &= " grid_subtotal" & dv(e.Item.ItemIndex)("_行区分")
                    End If
                End If
            End If
        Next

        RaiseEvent ItemDataBound(sender, e)
    End Sub

    Private Sub GridResult_ItemCreated(sender As Object, e As DataGridItemEventArgs) Handles GridResult.ItemCreated
        ' ヘッダテキストの置換
        If e.Item.ItemType = ListItemType.Header Then
            For Each cell As TableCell In e.Item.Cells
                MasterName.ReplaceItemName(cell.Text)
            Next
        End If

        RaiseEvent ItemCreated(sender, e)
    End Sub
End Class
