﻿Imports Dbs.Asphalt.Core
Imports Dbs.Asphalt.Core.SystemLogic
Imports Dbs.Asphalt.Core.Common
Imports Dbs.Asphalt.Core.BaseClass
Imports Dbs.Asphalt.UI

Partial Class ValueSaver
    Inherits CommonControlBase

    Public Event ValueSaveItemChanged(sender As Object, e As System.EventArgs)

    Private _unSaveControlID As String = ""

    ''' <summary>
    ''' 値を保存しないコントロールIDをカンマ区切りで指定します。
    ''' </summary>
    Public Property UnSaveControlID As String
        Get
            Return _unSaveControlID
        End Get
        Set(value As String)
            _unSaveControlID = value
        End Set
    End Property

    Protected Overrides Sub OnInit(e As System.EventArgs)
        MyBase.OnInit(e)

        PanelNormal.Visible = True
        PanelEdit.Visible = False
        LabelError.Visible = False

        Base.SetClientYesNoBox(ButtonRemove, Message.MessageText("MS_QUESTDELETE"))

        refresh_valuelist()
    End Sub

    Protected Sub ButtonSave_Click(sender As Object, e As System.EventArgs) Handles ButtonSave.Click
        PanelNormal.Visible = False
        PanelEdit.Visible = True

        TextValueName.Text = DropValueName.SelectedValue
        TextValueName.Focus()
    End Sub

    Protected Sub ButtonEditCancel_Click(sender As Object, e As System.EventArgs) Handles ButtonEditCancel.Click
        PanelNormal.Visible = True
        PanelEdit.Visible = False
    End Sub

    Protected Sub ButtonEditSave_Click(sender As Object, e As System.EventArgs) Handles ButtonEditSave.Click
        If TextValueName.Text = "" Then
            TextValueName.IsError = True
            TextValueName.Focus()
            LabelError.Visible = True
            Exit Sub
        End If

        save_values(Me.Parent)
        PanelNormal.Visible = True
        PanelEdit.Visible = False

        refresh_valuelist()

        DropValueName.SelectedValue = TextValueName.Text
        TextValueName.Text = ""
    End Sub

    Protected Sub ButtonRemove_Click(sender As Object, e As System.EventArgs) Handles ButtonRemove.Click
        Dim cfg As New Configurator(Base.Security)
        cfg.RemoveValuesName(Base.Security.UserID, CType(Parent, ModuleBase).ToString, DropValueName.SelectedValue)

        DropValueName.SelectedIndex = -1
        refresh_valuelist()
    End Sub

    Protected Sub DropValueName_SelectedIndexChanged(sender As Object, e As System.EventArgs) Handles DropValueName.SelectedIndexChanged
        If DropValueName.SelectedValue <> "" Then
            load_values(Me.Parent)
            RaiseEvent ValueSaveItemChanged(Me, e)
        End If
    End Sub

    Private Sub refresh_valuelist()
        Dim dl As New DataList(Base.Security)
        Base.UICommon.ListControlDataBind(DropValueName, dl.ScreenValueNameList(Base.Security.UserID, CType(Parent, ModuleBase).ToString), True, "")
    End Sub

    Private Sub save_values(container As Object)
        Dim cfg As New Configurator(Base.Security)
        For Each ctr As Object In container.Controls
            Try
                ' UnSaveControlIDで指定されているIDと一致した場合はスキップ
                If ctr.ID = Me.ID Or Array.IndexOf(_unSaveControlID.Split(","), ctr.ID) >= 0 Then
                    Exit Try
                End If
                Dim hc As Boolean
                Try
                    hc = ctr.HasControls
                Catch
                    hc = False
                End Try
                If hc Then
                    save_values(ctr)
                Else
                    cfg.SetScreenValue(Base.Security.UserID, CType(Parent, ModuleBase).ToString, TextValueName.Text, ctr.ID, ctr.Value)
                End If
            Catch
            End Try
        Next
    End Sub

    Private Sub load_values(container As Object)
        Dim cfg As New Configurator(Base.Security)
        For Each ctr As Object In container.Controls
            Try
                ' UnSaveControlIDで指定されているIDと一致した場合はスキップ
                If ctr.ID = Me.ID Or Array.IndexOf(_unSaveControlID.Split(","), ctr.ID) >= 0 Then
                    Exit Try
                End If
                Dim hc As Boolean
                Try
                    hc = ctr.HasControls
                Catch
                    hc = False
                End Try
                If hc Then
                    load_values(ctr)
                Else
                    ctr.Value = cfg.GetScreenValue(Base.Security.UserID, CType(Parent, ModuleBase).ToString, DropValueName.SelectedValue, ctr.ID, "")
                End If
            Catch
            End Try
        Next
    End Sub

End Class
