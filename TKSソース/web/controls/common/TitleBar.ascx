﻿<%@ Control Language="VB" AutoEventWireup="false" CodeFile="TitleBar.ascx.vb" Inherits="TitleBar" %>
<%@ Register Assembly="Dbs.Asphalt.UI" Namespace="Dbs.Asphalt.UI" TagPrefix="cc1" %>

<div class="titlebar">
  <div class="titlebarinner">
    <a class="toplink" href="./?cmd=index" title="トップページへ"></a>
    <div class="titlebarmenu">
      <div class="rightpane" style="margin-left: 10px;">
        <a href="?cmd=system/AccountEditor"><cc1:UserImage runat="server" ID="UserImage" AjaxUri="../../ajax.aspx" IsThumbnail="true" ViewerLink="false" /></a>
      </div>
      <div class="rightpane" style="margin-left: 10px;">
        <asp:Label runat="server" ID="LabelUserName" TabIndex="-1"></asp:Label><br />
        <a href="?cmd=Logout">ログアウト</a>
      </div>
    </div>
  </div>
</div>

<!------- SeparatorImageUrlにはその処理画面のマネージャクラス名を指定する(ユーザー権限に応じてメニューアイテムの非表示を制御するため) -------->
<cc1:Menu runat="server" ID="MenuMain" ItemWidth="150px">
  <Items>
    <asp:MenuItem Text="グループウェア" Value="グループウェア">
      <asp:MenuItem Value="Index" SeparatorImageUrl="ForumEditorManager"></asp:MenuItem>
      <asp:MenuItem Value="system/Scheduler" SeparatorImageUrl="ScheduleManager"></asp:MenuItem>
    </asp:MenuItem>
    <asp:MenuItem Text="日常業務" Value="日常業務">
      <asp:MenuItem Value="daily/UriageDenpyoEditor2" SeparatorImageUrl="UriageDenpyoManager"></asp:MenuItem>
      <asp:MenuItem Value="daily/UriageDenpyoPrintEditor" SeparatorImageUrl="UriageDenpyoPrintManager"></asp:MenuItem>
      <%--<asp:MenuItem Value="daily/UriageDenpyoPrintEditor3" SeparatorImageUrl="UriageDenpyoPrintManager"></asp:MenuItem>--%>
      <asp:MenuItem Value="daily/UriageNohinshoEditor" SeparatorImageUrl="UriageNohinshoManager"></asp:MenuItem>
      <asp:MenuItem Text="-" Value="-"></asp:MenuItem>
      <asp:MenuItem Value="daily/NyukinDenpyoEditor" SeparatorImageUrl="NyukinDenpyoManager"></asp:MenuItem>
      <asp:MenuItem Value="daily/NyukinDenpyoPrintEditor" SeparatorImageUrl="NyukinDenpyoPrintManager"></asp:MenuItem>
    </asp:MenuItem>
    <asp:MenuItem Text="請求業務" Value="請求業務">
      <asp:MenuItem Value="bill/SeikyuKeisanEditor" SeparatorImageUrl="SeikyuKeisanManager"></asp:MenuItem>
      <asp:MenuItem Value="bill/SeikyuPrintEditor" SeparatorImageUrl="SeikyuPrintManager"></asp:MenuItem>
      <asp:MenuItem Value="bill/SeikyulistEditor" SeparatorImageUrl="SeikyulistManager"></asp:MenuItem>
    </asp:MenuItem>
    <asp:MenuItem Text="随時業務" Value="随時業務">
      <asp:MenuItem Value="anytime/LeaseListEditor" SeparatorImageUrl="LeaseListManager"></asp:MenuItem>
      <asp:MenuItem Value="anytime/KoujiListEditor" SeparatorImageUrl="KoujiListManager"></asp:MenuItem>
      <asp:MenuItem Text="-" Value="-"></asp:MenuItem>
      <asp:MenuItem Value="anytime/UriageTeikiEditor" SeparatorImageUrl="UriageTeikiManager"></asp:MenuItem>
      <%--<asp:MenuItem Value="anytime/ReportGridViewSample" ></asp:MenuItem>--%>
    </asp:MenuItem>
    <asp:MenuItem Text="マスター" Value="マスター">
      <asp:MenuItem Value="master/CompanyEditor" SeparatorImageUrl="CompanyEditorManager"></asp:MenuItem>
      <asp:MenuItem Value="master/TokuisakiMasterEditor2" SeparatorImageUrl="TokuisakiMasterManager"></asp:MenuItem>
      <asp:MenuItem Value="master/KoujiMasterEditor" SeparatorImageUrl="KoujiMasterManager"></asp:MenuItem>
      <asp:MenuItem Value="master/ShouhinMasterEditor" SeparatorImageUrl="ShouhinMasterManager"></asp:MenuItem>
      <asp:MenuItem Text="-" Value="-"></asp:MenuItem>
      <asp:MenuItem Value="master/KubunEditor" SeparatorImageUrl="KubunListManager"></asp:MenuItem>
      <asp:MenuItem Value="master/ZeirituMasterEditor" SeparatorImageUrl="ZeirituMasterListManager"></asp:MenuItem>
    </asp:MenuItem>
    <asp:MenuItem Text="拡張" Value="拡張">
    </asp:MenuItem>
    <asp:MenuItem Text="システム" Value="システム">
      <asp:MenuItem Value="system/AccountEditor" SeparatorImageUrl="AccountEditorManager"></asp:MenuItem>
      <asp:MenuItem Value="system/GroupEditor" SeparatorImageUrl="PositionEditorManager"></asp:MenuItem>
      <asp:MenuItem Value="system/SystemKubunEditor" SeparatorImageUrl="SystemKubunListManager"></asp:MenuItem>
      <asp:MenuItem Text="-" Value="-"></asp:MenuItem>
      <asp:MenuItem Value="system/TemplateUploader" SeparatorImageUrl="TemplateEditorManager"></asp:MenuItem>
      <%--<asp:MenuItem Text="データバックアップ" Value="DatabaseBackup" SeparatorImageUrl=""></asp:MenuItem>--%>
      <asp:MenuItem Text="-" Value="-"></asp:MenuItem>
      <asp:MenuItem Text="システムログ" Value="finder/SystemLogFinder" SeparatorImageUrl=""></asp:MenuItem>
      <asp:MenuItem Text="実行権限設定" Value="system/AuthModuleConfig" SeparatorImageUrl=""></asp:MenuItem>
      <asp:MenuItem Value="system/SystemConfig" SeparatorImageUrl="SystemConfigManager"></asp:MenuItem>
      <asp:MenuItem Text="-" Value="-"></asp:MenuItem>
      <asp:MenuItem Text="ログアウト" Value="Logout" SeparatorImageUrl=""></asp:MenuItem>
    </asp:MenuItem>
  </Items>
</cc1:Menu>
