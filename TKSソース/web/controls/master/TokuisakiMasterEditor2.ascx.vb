﻿Imports Dbs.Asphalt.UI
Imports Dbs.Asphalt.Core
Imports Dbs.Asphalt.Core.CoreLogic.System
Imports Dbs.Asphalt.Core.Common
Imports System.IO
Imports Dbs.Application.Logic.Logic.Master

Partial Class TokuisakiMasterEditor2
    Inherits ModuleBase

    Private logic As TokuisakiMasterManager

    Protected Overrides Sub OnInit(e As System.EventArgs)
        logic = New TokuisakiMasterManager(Base.Security)

        logic.EditMode.Control(Field.ControlTypeContents.ctEditMode) = EditMode
        logic.得意先コード.Control(Field.ControlTypeContents.ctTextBox) = TextCode
        logic.得意先名.Control(Field.ControlTypeContents.ctTextBox) = TextName
        logic.得意先かな.Control(Field.ControlTypeContents.ctTextBox) = TextKana
        logic.郵便番号.Control(Field.ControlTypeContents.ctTextBox) = TextPostNo
        logic.住所1.Control(Field.ControlTypeContents.ctTextBox) = TextAddress1
        logic.住所2.Control(Field.ControlTypeContents.ctTextBox) = TextAddress2
        logic.電話番号.Control(Field.ControlTypeContents.ctTextBox) = TextTel
        logic.FAX番号.Control(Field.ControlTypeContents.ctTextBox) = TextFax
        logic.締日区分.Control(Field.ControlTypeContents.ctRadioButtonList) = DropShimebiKubun
        logic.端数区分.Control(Field.ControlTypeContents.ctRadioButtonList) = DropHasuKubun
        logic.請求書区分.Control(Field.ControlTypeContents.ctRadioButtonList) = DropSeikyuKubun
        logic.担当者コード.Control(Field.ControlTypeContents.ctTextBox) = TextTantoCode
        logic.諸口区分.Control(Field.ControlTypeContents.ctRadioButtonList) = DropShoguchiKubun
        logic.消費税区分.Control(Field.ControlTypeContents.ctRadioButtonList) = DropZeiKubun

        logic.登録日時.Control(Field.ControlTypeContents.ctTextBox) = TextRegDate
        logic.登録者ID.Control(Field.ControlTypeContents.ctTextBox) = TextRegUserID
        logic.更新日時.Control(Field.ControlTypeContents.ctTextBox) = TextUpdDate
        logic.更新者ID.Control(Field.ControlTypeContents.ctTextBox) = TextUpdUserID

        logic.Start()

        ' Finderの読み込み
        Base.LoadFinder("TokuisakiMasterFinder", "CommonFinder", "M得意先検索", "得意先コード")
        Base.LoadFinder("UserMasterFinder", "UserMasterFinder")

        MyBase.OnInit(e)
    End Sub

    Protected Overrides Sub OnLoad(e As System.EventArgs)

        If Not IsPostBack Then
            'TextUserID.Text = Base.Security.UserID
            read_data(True)
        End If

        MyBase.OnLoad(e)
    End Sub

    Private Sub AccountEditor_PreRender(sender As Object, e As EventArgs) Handles Me.PreRender

        ButtonDelete.Visible = (logic.EditMode.Value = Field.EditModeContents.emUpdateMode)
    End Sub

    Public Overrides Sub FinderClose(findername As String, result As System.Collections.Hashtable, resulttargetid As String)
        Select Case findername
            Case "UserMasterFinder"
                TextTantoCode.Text = result("ID")
        End Select
    End Sub

    Public Overrides Sub FinderOpen()

    End Sub

    Public Overrides ReadOnly Property PageTitle() As String
        Get
            Return logic.Title
        End Get
    End Property

    ' 保存ﾎﾞﾀﾝ
    Protected Sub ButtonRegist_Click(sender As Object, e As System.EventArgs) Handles ButtonRegist.Click
        If Not logic.WriteMainData Then
            Base.ErrorMessage = logic.LastError
            Return
        End If

        Base.Message = logic.LastMessage

        read_data(RecordSelector.ClearItems)
    End Sub

    Protected Sub ButtonNew_Click(sender As Object, e As System.EventArgs) Handles ButtonNew.Click
        TextCode.Text = ""
        read_data(RecordSelector.ClearItems)
    End Sub

    Protected Sub ButtonFind_Click(sender As Object, e As System.EventArgs) Handles ButtonFind.Click
        Dim param As New Hashtable
        Base.OpenFinder("TokuisakiMasterFinder", param, "TextCode")
    End Sub

    Protected Sub ButtonDelete_Click(sender As Object, e As System.EventArgs) Handles ButtonDelete.Click
        If Not logic.DeleteMainData Then
            Base.ErrorMessage = logic.LastError
            Return
        End If

        Base.Message = logic.LastMessage

        TextCode.Text = ""
        read_data(True)
    End Sub

    Public Overrides ReadOnly Property RunAuth() As Dbs.Asphalt.Core.SystemLogic.Security.emUserAuth
        Get
            Return SystemLogic.Security.emUserAuth.一般
        End Get
    End Property

    Public Overrides Sub MessageBoxClose(messageboxname As String, result As Dbs.Asphalt.UI.ModuleBase.MessageBoxResult)

    End Sub

    Private Sub read_data(read_after_clear As Boolean)
        If Not logic.ReadMainData(read_after_clear) Then
            Base.ErrorMessage = logic.LastError
        End If
    End Sub

    Protected Sub TextCode_TextChanged(sender As Object, e As EventArgs) Handles TextCode.TextChanged
        read_data(True)
    End Sub

    Protected Sub TextPostNo_TextChanged(sender As Object, e As EventArgs) Handles TextPostNo.TextChanged
        Dim post As New PostDic
        Dim addr As PostDic.AddressData
        addr = post.GetAddress(TextPostNo.Text)
        If (addr.Pref & addr.City & addr.Area).Length > 50 Then
            TextAddress1.Text = addr.Pref & addr.City
            TextAddress2.Text = addr.Area
        Else
            TextAddress1.Text = addr.Pref & addr.City & addr.Area
            TextAddress2.Text = ""
        End If
    End Sub

    Private Sub TextTantoCode_FinderButtonClicked(sender As Object, e As ImageClickEventArgs) Handles TextTantoCode.FinderButtonClicked
        Dim param As New Hashtable
        param("種別") = 0
        Base.OpenFinder("UserMasterFinder", param)
    End Sub
End Class
