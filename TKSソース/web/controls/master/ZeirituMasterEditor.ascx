﻿<%@ Control Language="VB" AutoEventWireup="false" CodeFile="ZeirituMasterEditor.ascx.vb" Inherits="ZeirituMasterEditor" %>
<%@ Register Assembly="Dbs.Asphalt.UI" Namespace="Dbs.Asphalt.UI" TagPrefix="cc1" %>

<cc1:ItemFrame runat="server" ID="PanelMain">
  <cc1:ItemPanel runat="server" ItemWidth="0" Text="税種別">
    <cc1:DropDownList runat="server" ID="DropDataKubun" AutoPostBack="true"></cc1:DropDownList>
  </cc1:ItemPanel>
  <cc1:GridViewEx runat="server" ID="GridDetail" AllowPaging="false" AllowSorting="false" RowStyle-Wrap="false" RowStyle-VerticalAlign="Top" Width="" AllowDeleteButton="true">
    <Columns>
      <asp:TemplateField HeaderText="適用開始日">
        <ItemTemplate><cc1:TextBox runat="server" ID="TextDate"></cc1:TextBox></ItemTemplate>
      </asp:TemplateField>
      <asp:TemplateField HeaderText="税率">
        <ItemTemplate><cc1:TextBox runat="server" ID="TextTax"></cc1:TextBox></ItemTemplate>
      </asp:TemplateField>
    </Columns>
  </cc1:GridViewEx>
</cc1:ItemFrame>

<asp:panel runat="server" ID="PanelToolbar">
  <cc1:ToolButton runat="server" ButtonImage="biSave" Text="保存" ID="ButtonRegist" PostbackTrriger="true" PostbackTrrigerFreeze="true" />
  <cc1:ToolButton runat="server" ButtonImage="biExcel" Text="ダウンロード" ID="ButtonDownload" PostbackTrriger="true" />
</asp:panel>
