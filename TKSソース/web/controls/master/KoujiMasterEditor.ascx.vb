﻿Imports Dbs.Asphalt.UI
Imports Dbs.Asphalt.Core
Imports Dbs.Asphalt.Core.CoreLogic.System
Imports Dbs.Asphalt.Core.Common
Imports System.IO
Imports Dbs.Application.Logic.Logic.Master

Partial Class KoujiMasterEditor
    Inherits ModuleBase

    Private logic As KoujiMasterManager

    Protected Overrides Sub OnInit(e As System.EventArgs)
        logic = New KoujiMasterManager(Base.Security)

        logic.EditMode.Control(Field.ControlTypeContents.ctEditMode) = EditMode
        logic.工事内部コード.Control(Field.ControlTypeContents.ctTextBox) = TextKoujiNaibuCode
        logic.得意先コード.Control(Field.ControlTypeContents.ctTextBox) = TextTokuisakiCode
        logic.工事コード.Control(Field.ControlTypeContents.ctTextBox) = TextKoujiCode
        logic.工事名.Control(Field.ControlTypeContents.ctTextBox) = TextKoujiName
        logic.工事名かな.Control(Field.ControlTypeContents.ctTextBox) = TextKoujiKana
        logic.完了区分.Control(Field.ControlTypeContents.ctDropDownList) = DropKanryoKubun
        logic.住所.Control(Field.ControlTypeContents.ctTextBox) = TextAddress
        logic.摘要1.Control(Field.ControlTypeContents.ctTextBox) = TextTekiyou1
        logic.摘要2.Control(Field.ControlTypeContents.ctTextBox) = TextTekiyou2

        logic.登録日時.Control(Field.ControlTypeContents.ctTextBox) = TextRegDate
        logic.登録者ID.Control(Field.ControlTypeContents.ctTextBox) = TextRegUserID
        logic.更新日時.Control(Field.ControlTypeContents.ctTextBox) = TextUpdDate
        logic.更新者ID.Control(Field.ControlTypeContents.ctTextBox) = TextUpdUserID

        logic.Start()

        ' Finderの読み込み
        Base.LoadFinder("KoujiMasterFinder", "CommonFinder", "M工事検索", "_取得値")
        Base.LoadFinder("TokuisakiMasterFinder", "CommonFinder", "M得意先検索", "得意先コード")

        MyBase.OnInit(e)
    End Sub

    Protected Overrides Sub OnLoad(e As System.EventArgs)

        If Not IsPostBack Then
            read_data(True)
        End If

        MyBase.OnLoad(e)
    End Sub

    Private Sub AccountEditor_PreRender(sender As Object, e As EventArgs) Handles Me.PreRender

        ButtonDelete.Visible = (logic.EditMode.Value = Field.EditModeContents.emUpdateMode)

    End Sub

    Public Overrides Sub FinderClose(findername As String, result As System.Collections.Hashtable, resulttargetid As String)
        Select Case findername
            Case "KoujiMasterFinder"
                Dim codes As String() = result("選択値").ToString.Split(",")      ' 取得結果を分解
                TextTokuisakiCode.Text = codes(0)                                 ' 得意先コード
                TextKoujiCode.Text = codes(1)                                     ' 工事コード
        End Select

        read_data(True)
    End Sub

    Public Overrides Sub FinderOpen()

    End Sub

    Public Overrides ReadOnly Property PageTitle() As String
        Get
            Return logic.Title
        End Get
    End Property

    ' 保存ﾎﾞﾀﾝ
    Protected Sub ButtonRegist_Click(sender As Object, e As System.EventArgs) Handles ButtonRegist.Click
        If Not logic.WriteMainData Then
            Base.ErrorMessage = logic.LastError
            Return
        End If

        Base.Message = logic.LastMessage

        read_data(RecordSelector.ClearItems)
    End Sub

    Protected Sub ButtonNew_Click(sender As Object, e As System.EventArgs) Handles ButtonNew.Click
        TextKoujiNaibuCode.Text = ""
        TextKoujiCode.Text = ""
        read_data(RecordSelector.ClearItems)
    End Sub

    Protected Sub ButtonFind_Click(sender As Object, e As System.EventArgs) Handles ButtonFind.Click
        Dim param As New Hashtable
        param("固定条件") = "得意先コード = '" & TextTokuisakiCode.Text & "'"
        'param("固定条件") = "得意先コード = '001017'"
        Base.OpenFinder("KoujiMasterFinder", param)
    End Sub

    Protected Sub ButtonDelete_Click(sender As Object, e As System.EventArgs) Handles ButtonDelete.Click
        If Not logic.DeleteMainData Then
            Base.ErrorMessage = logic.LastError
            Return
        End If

        Base.Message = logic.LastMessage

        TextTokuisakiCode.Text = ""
        TextKoujiCode.Text = ""
        read_data(True)
    End Sub

    Public Overrides ReadOnly Property RunAuth() As Dbs.Asphalt.Core.SystemLogic.Security.emUserAuth
        Get
            Return SystemLogic.Security.emUserAuth.一般
        End Get
    End Property

    Public Overrides Sub MessageBoxClose(messageboxname As String, result As Dbs.Asphalt.UI.ModuleBase.MessageBoxResult)

    End Sub

    Private Sub read_data(read_after_clear As Boolean)
        If Not logic.ReadMainData(read_after_clear) Then
            Base.ErrorMessage = logic.LastError
        End If
    End Sub

    Protected Sub TextTokuisakiCode_TextChanged(sender As Object, e As EventArgs) Handles TextTokuisakiCode.TextChanged
        'read_data(True)
    End Sub

    Private Sub TextTokuisakiCode_FinderButtonClicked(sender As Object, e As ImageClickEventArgs) Handles TextTokuisakiCode.FinderButtonClicked
        ' 得意先検索を開く
        Base.OpenFinder("TokuisakiMasterFinder", , sender.ID)
    End Sub

    Protected Sub TextKoujiCode_TextChanged(sender As Object, e As EventArgs) Handles TextKoujiCode.TextChanged
        read_data(True)
    End Sub
End Class
