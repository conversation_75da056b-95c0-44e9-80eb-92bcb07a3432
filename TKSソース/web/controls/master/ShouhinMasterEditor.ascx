﻿<%@ Control Language="VB" AutoEventWireup="false" CodeFile="ShouhinMasterEditor.ascx.vb" Inherits="ShouhinMasterEditor" %>
<%@ Register Assembly="Dbs.Asphalt.UI" Namespace="Dbs.Asphalt.UI" TagPrefix="cc1" %>
<%@ Register Src="~/controls/common/RecordSelector.ascx" TagPrefix="uc1" TagName="RecordSelector" %>
<%@ Register Src="~/controls/common/ReportDataViewer.ascx" TagPrefix="uc1" TagName="ReportDataViewer" %>

<cc1:ItemFrame runat="server" ID="PanelBase" Text="基本情報">
  <div class="leftpane">
    <cc1:ItemPanel runat="server" Text="商品コード">
      <cc1:TextBox runat="server" ID="TextCode" AutoPostBack="true" ></cc1:TextBox><br />
      <span class="guide">商品コードを空白で登録すると、自動的にコードを割り当てて追加登録されます。</span>
    </cc1:ItemPanel>

    <cc1:ItemPanel runat="server" Text="登録">
      <cc1:TextBox runat="server" ID="TextRegDate" ViewOnly="true"></cc1:TextBox>&nbsp;
      <cc1:TextBox runat="server" ID="TextRegUserID" ViewOnly="true"></cc1:TextBox>
    </cc1:ItemPanel>
    <cc1:ItemPanel runat="server" Text="更新">
      <cc1:TextBox runat="server" ID="TextUpdDate" ViewOnly="true"></cc1:TextBox>&nbsp;
      <cc1:TextBox runat="server" ID="TextUpdUserID" ViewOnly="true"></cc1:TextBox>
    </cc1:ItemPanel>
  </div>
  <div class="rightpane">
    <cc1:EditMode runat="server" ID="EditMode"></cc1:EditMode><br />
    <uc1:RecordSelector runat="server" ID="RecordSelector" DataKeyControlID="TextCode" DataName="M商品" FindItemList="商品名,商品名かな" KeyIsNumeric="false" KeyName="商品コード" AllowClearCheckBox="true" />
  </div>
  <div class="clear"></div>

  <hr />

  <cc1:ItemPanel runat="server" Text="商品名">
    <cc1:TextBox runat="server" ID="TextName"></cc1:TextBox>
  </cc1:ItemPanel>

  <cc1:ItemPanel runat="server" Text="カナ">
    <cc1:TextBox runat="server" ID="TextKana"></cc1:TextBox>
  </cc1:ItemPanel>

  <hr />

  <cc1:ItemPanel runat="server" Text="単位区分">
    <cc1:DropDownList runat="server" ID="DropUnitKubun"></cc1:DropDownList>
  </cc1:ItemPanel>

  <cc1:ItemPanel runat="server" Text="基本料金">
    <cc1:TextBox runat="server" ID="TextTeika"></cc1:TextBox>
  </cc1:ItemPanel>

  <cc1:ItemPanel runat="server" Text="単価">
    <cc1:TextBox runat="server" ID="TextTanka"></cc1:TextBox>
  </cc1:ItemPanel>

  <hr />

  <cc1:ItemPanel runat="server" Text="商品分類">
    <cc1:DropDownList runat="server" ID="DropBunruiKubun"></cc1:DropDownList>
  </cc1:ItemPanel>

  <cc1:ItemPanel runat="server" Text="保証日数">
    <cc1:TextBox runat="server" ID="TextHosho"></cc1:TextBox>
  </cc1:ItemPanel>

  <cc1:ItemPanel runat="server" Text="備考">
    <cc1:TextBox runat="server" ID="TextBikou"></cc1:TextBox>
  </cc1:ItemPanel>

</cc1:ItemFrame>

<asp:panel runat="server" ID="PanelToolbar">
  <cc1:ToolButton runat="server" ButtonImage="biSave" Text="保存" ID="ButtonRegist" PostbackTrriger="true" PostbackTrrigerFreeze="true" OnClick="ButtonRegist_Click" />
  <cc1:ToolButton runat="server" ButtonImage="biDelete" Text="削除" ID="ButtonDelete" PostbackTrriger="true" PostbackTrrigerFreeze="true" OnClick="ButtonDelete_Click" />
  <cc1:ToolButton runat="server" ButtonImage="biNew" Text="新規追加" ID="ButtonNew" OnClick="ButtonNew_Click" />
  <cc1:ToolButton runat="server" ButtonImage="biFinder" Text="検索" ID="ButtonFind" OnClick="ButtonFind_Click" />
</asp:panel>
