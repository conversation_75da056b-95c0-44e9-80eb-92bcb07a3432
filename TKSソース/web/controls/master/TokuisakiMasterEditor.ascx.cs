﻿using System;
using System.Web.UI;
using System.Collections;
using System.Web.UI.WebControls;
using Dbs.Asphalt.Core;
using Dbs.Asphalt.Core.Common;
using Dbs.Application.Logic.Logic.Master;
using Dbs.Asphalt.UI;
using Dbs.Asphalt.Core.SystemLogic;
using Dbs.Asphalt.Database;
using Dbs.Asphalt.Database.DbFinderTableAdapters;

/// <summary>
/// 得意先マスターの実装例
/// このモジュールは、C#でのコーディング例です。
/// </summary>
partial class TokuisakiMasterEditor : ModuleBase
{
    private TokuisakiMasterManager logic;

    protected void Page_Init(object sender, EventArgs e)
    {
        logic = new TokuisakiMasterManager(Base.Security);
        logic.EditMode.set_Control(Field.ControlTypeContents.ctEditMode, EditMode);
        logic.得意先コード.set_Control(Field.ControlTypeContents.ctTextBox, TextCode);
        logic.得意先名.set_Control(Field.ControlTypeContents.ctTextBox, TextName);
        logic.郵便番号.set_Control(Field.ControlTypeContents.ctTextBox, TextPostNo);
        logic.住所1.set_Control(Field.ControlTypeContents.ctTextBox, TextAddress1);
        logic.住所2.set_Control(Field.ControlTypeContents.ctTextBox, TextAddress2);
        logic.電話番号.set_Control(Field.ControlTypeContents.ctTextBox, TextTel);
        logic.FAX番号.set_Control(Field.ControlTypeContents.ctTextBox, TextFax);
        logic.消費税区分.set_Control(Field.ControlTypeContents.ctDropDownList, DropZeiKubun);
        logic.Start();

        // 検索画面を読み込む
        Base.LoadFinder("TokuisakiMasterFinder", "CommonFinder", "M得意先検索", "得意先コード");

        if (IsPostBack == false)
        {
            read_data();
        }

        // ReportDataViewerへのデータセットの表示サンプル(掲示板投稿リスト)
        using (S掲示板検索TableAdapter db = new S掲示板検索TableAdapter())
        {
            using (DbFinder.S掲示板検索DataTable rs = db.GetData(""))
            {
                ReportDataViewer.DataSource = rs.DefaultView;
            }
        }
    }

    protected void Page_PreRender(object sender, EventArgs e)
    {
        // 修正モードの場合のみボタン表示
        ButtonDelete.Visible = (EditMode.EditMode == Field.EditModeContents.emUpdateMode);
    }

    protected void TextCode_TextChanged(object sender, EventArgs e)
    {
        read_data();
    }

    protected void ButtonRegist_Click(object sender, ImageClickEventArgs e)
    {
        if (logic.WriteMainData() == false)
        {
            Base.ErrorMessage = logic.LastError;
            return;
        }

        Base.Message = logic.LastMessage;

        if (RecordSelector.ClearItems)
        {
            TextCode.Text = "";
        }
        read_data();
    }

    protected void ButtonDelete_Click(object sender, ImageClickEventArgs e)
    {
        if (logic.DeleteMainData() == false)
        {
            Base.ErrorMessage = logic.LastError;
            return;
        }

        Base.Message = logic.LastMessage;
        TextCode.Text = "";
        read_data();
    }

    protected void ButtonNew_Click(object sender, ImageClickEventArgs e)
    {
        TextCode.Text = "";
        read_data();
    }

    protected void ButtonFind_Click(object sender, ImageClickEventArgs e)
    {
        Hashtable param = new Hashtable();
        param["固定条件"] = "消費税区分 = 1";
        Base.OpenFinder("TokuisakiMasterFinder", param, "TextCode");
    }

    protected void TextPostNo_TextChanged(object sender, EventArgs e)
    {
        PostDic post = new PostDic();
        PostDic.AddressData addr;
        addr = post.GetAddress(TextPostNo.Text);

        if ((addr.Pref + addr.City + addr.Area).LenB() > 50)
        {
            TextAddress1.Text = addr.Pref + addr.City;
            TextAddress2.Text = addr.Area;
        }
        else
        {
            TextAddress1.Text = addr.Pref + addr.City + addr.Area;
            TextAddress2.Text = "";
        }
    }

    protected void ReportDataViewer_ItemDataBound(object sender, DataGridItemEventArgs e)
    {
        if (e.Item.ItemType == ListItemType.AlternatingItem || e.Item.ItemType == ListItemType.Item)
        {
            e.Item.Cells[1].Text = "セル内容の書き換え";
            e.Item.Cells[1].ForeColor = System.Drawing.Color.Red;
        }
    }

    public override void FinderOpen()
    {
        
    }

    public override void FinderClose(string findername, Hashtable result, string resulttargetid)
    {
        
    }

    public override void MessageBoxClose(string messageboxname, MessageBoxResult result)
    {
        
    }

    public override Security.emUserAuth RunAuth
    {
        get
        {
            return Security.emUserAuth.一般;
        }
    }

    public override string PageTitle
    {
        get
        {
            return logic.Title;
        }
    }

    private void  read_data()
    {
        if (logic.ReadMainData(RecordSelector.ClearItems) == false)
        {
            Base.ErrorMessage = logic.LastError;
            return;
        }

        TextName.Focus();
    }
}
