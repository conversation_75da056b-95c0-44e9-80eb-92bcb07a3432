﻿Imports Dbs.Asphalt.UI
Imports Dbs.Asphalt.Core
Imports Dbs.Asphalt.Core.CoreLogic
Imports Dbs.Asphalt.Core.CoreLogic.System
Imports Dbs.Asphalt.Core.Common

Partial Class CompanyEditor
    Inherits ModuleBase

    Private logic As CompanyEditorManager

    Protected Overrides Sub OnInit(e As System.EventArgs)
        logic = New CompanyEditorManager(Base.Security)

        Base.UICommon.ListControlDataBind(DropSihaType, logic.DataList.AnyList("サイト区分"))

        logic.EditMode.Control(Field.ControlTypeContents.ctEditMode) = EditMode
        logic.会社ID.Value = 1
        logic.会社名.Control(Field.ControlTypeContents.ctTextBox) = TextCompanyName
        logic.会社名カナ.Control(Field.ControlTypeContents.ctTextBox) = TextCompanyKana
        logic.郵便番号.Control(Field.ControlTypeContents.ctTextBox) = TextPostNo
        logic.住所1.Control(Field.ControlTypeContents.ctTextBox) = TextAddress1
        logic.住所2.Control(Field.ControlTypeContents.ctTextBox) = TextAddress2
        logic.電話番号.Control(Field.ControlTypeContents.ctTextBox) = TextTel
        logic.FAX番号.Control(Field.ControlTypeContents.ctTextBox) = TextFax
        logic.代表者名.Control(Field.ControlTypeContents.ctTextBox) = TextTopName
        logic.締日.Control(Field.ControlTypeContents.ctTextBox) = TextSimeDD
        logic.支払区分.Control(Field.ControlTypeContents.ctDropDownList) = DropSihaType
        logic.支払日.Control(Field.ControlTypeContents.ctTextBox) = TextSihaDD
        logic.期首月.Control(Field.ControlTypeContents.ctTextBox) = TextFirstMM
        logic.登録番号.Control(Field.ControlTypeContents.ctTextBox) = TextTourokuBango
        logic.振込先1.Control(Field.ControlTypeContents.ctTextBox) = TextBankInfo1
        logic.振込先2.Control(Field.ControlTypeContents.ctTextBox) = TextBankInfo2
        logic.振込先3.Control(Field.ControlTypeContents.ctTextBox) = TextBankInfo3
        logic.振込先4.Control(Field.ControlTypeContents.ctTextBox) = TextBankInfo4
        logic.振込先5.Control(Field.ControlTypeContents.ctTextBox) = TextBankInfo5
        logic.メモ.Control(Field.ControlTypeContents.ctTextBox) = TextMemo
        logic.登録日時.Control(Field.ControlTypeContents.ctTextBox) = TextRegDate
        logic.登録者名.Control(Field.ControlTypeContents.ctTextBox) = TextRegUserID
        logic.更新日時.Control(Field.ControlTypeContents.ctTextBox) = TextUpdDate
        logic.更新者名.Control(Field.ControlTypeContents.ctTextBox) = TextUpdUserID

        If Not IsPostBack Then
            read_data(True)
        End If

        Base.AddPostbackTrrigerControl(ButtonLogoUpload, True)
        Base.AddPostbackTrrigerControl(ButtonLogoDelete, True, Message.MessageText("MS_QUESTDELETE"))
        Base.AddPostbackTrrigerControl(ButtonSignUpload, True)
        Base.AddPostbackTrrigerControl(ButtonSignDelete, True, Message.MessageText("MS_QUESTDELETE"))

        MyBase.OnInit(e)
    End Sub

    Private Sub CompanyEditor_PreRender(sender As Object, e As EventArgs) Handles Me.PreRender
        If logic.EditMode.Value = Field.EditModeContents.emInsertMode Then
            PanelImage.Enabled = False
        Else
            PanelImage.Enabled = True
        End If
    End Sub

    Public Overrides Sub FinderClose(findername As String, result As System.Collections.Hashtable, resulttargetid As String)

    End Sub

    Public Overrides Sub FinderOpen()

    End Sub

    Public Overrides ReadOnly Property PageTitle() As String
        Get
            Return logic.Title
        End Get
    End Property

    Protected Sub ButtonRegist_Click(sender As Object, e As System.EventArgs) Handles ButtonRegist.Click
        If Not logic.WriteMainData Then
            Base.ErrorMessage = logic.LastError
            Return
        End If

        Base.Message = logic.LastMessage

        read_data(True)
    End Sub

    Public Overrides ReadOnly Property RunAuth() As Dbs.Asphalt.Core.SystemLogic.Security.emUserAuth
        Get
            Return SystemLogic.Security.emUserAuth.一般
        End Get
    End Property

    Public Overrides Sub MessageBoxClose(messageboxname As String, result As Dbs.Asphalt.UI.ModuleBase.MessageBoxResult)

    End Sub

    Protected Sub TextPostNo_TextChanged(sender As Object, e As System.EventArgs) Handles TextPostNo.TextChanged
        Dim post As New PostDic
        Dim addr As PostDic.AddressData
        addr = post.GetAddress(TextPostNo.Text)
        If (addr.Pref & addr.City & addr.Area).Length > 50 Then
            TextAddress1.Text = addr.Pref & addr.City
            TextAddress2.Text = addr.Area
        Else
            TextAddress1.Text = addr.Pref & addr.City & addr.Area
            TextAddress2.Text = ""
        End If
    End Sub

    Private Sub ButtonImageUpload_Click(sender As Object, e As EventArgs) Handles ButtonLogoUpload.Click, ButtonSignUpload.Click
        Select Case sender.ID
            Case "ButtonLogoUpload"
                If FileLogo.PostedFile.ContentType.Split("/")(0) = "image" Then
                    logic.ロゴ画像.Value = FileLogo.FileBytes
                Else
                    Base.ErrorMessage = Message.MessageText("EM_NOTIMAGE")
                    Return
                End If
            Case "ButtonSignUpload"
                If FileSign.PostedFile.ContentType.Split("/")(0) = "image" Then
                    logic.社判画像.Value = FileSign.FileBytes
                Else
                    Base.ErrorMessage = Message.MessageText("EM_NOTIMAGE")
                    Return
                End If
        End Select
        If Not logic.WriteMainData Then
            Base.ErrorMessage = logic.LastError
            Return
        End If

        Base.Message = logic.LastMessage

        read_data(True)
    End Sub

    Private Sub ButtonImageDelete_Click(sender As Object, e As EventArgs) Handles ButtonLogoDelete.Click, ButtonSignDelete.Click
        Select Case sender.ID
            Case "ButtonLogoDelete"
                If Not logic.DeleteLogoImage Then
                    Base.ErrorMessage = logic.LastError
                    Return
                End If
            Case "ButtonSignDelete"
                If Not logic.DeleteSignImage Then
                    Base.ErrorMessage = logic.LastError
                    Return
                End If
        End Select

        Base.Message = logic.LastMessage

        read_data(True)
    End Sub

    Private Sub read_data(read_after_clear As Boolean)
        If Not logic.ReadMainData(read_after_clear) Then
            Base.ErrorMessage = logic.LastError
        End If

        ImageLogo.ImageUrl = "../../ajax.aspx?cmd=logoimage&comid=1&imageid=" & Text.CDateEx(IIf(logic.更新日時.Value <> "", logic.更新日時.Value, logic.登録日時.Value)).ToString("yyyyMMddHHmmss")
        ImageSign.ImageUrl = "../../ajax.aspx?cmd=signimage&comid=1&imageid=" & Text.CDateEx(IIf(logic.更新日時.Value <> "", logic.更新日時.Value, logic.登録日時.Value)).ToString("yyyyMMddHHmmss")
        ImageLogo.Visible = (logic.ロゴ画像.Value IsNot Nothing)
        ImageSign.Visible = (logic.社判画像.Value IsNot Nothing)

        TextCompanyName.Focus()
    End Sub

End Class
