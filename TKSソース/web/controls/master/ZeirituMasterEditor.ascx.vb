﻿Imports System.Data
Imports Dbs.Asphalt.UI
Imports Dbs.Asphalt.Core
Imports Dbs.Application.Logic.Logic.Master
Imports Dbs.Asphalt.Core.Common

Partial Class ZeirituMasterEditor
    Inherits ModuleBase

    Private logic As ZeirituMasterListManager

    Protected Overrides Sub OnInit(e As System.EventArgs)
        logic = New ZeirituMasterListManager(Base.Security)

        logic.税種別.Control(Field.ControlTypeContents.ctDropDownList) = DropDataKubun
        logic.Start()

        GridDetail.DefaultRowsCount = logic.List.Length
        For i As Integer = 0 To logic.List.Length - 1
            logic.List(i).税種別.Control(Field.ControlTypeContents.ctDropDownList) = DropDataKubun
            logic.List(i).適用開始日.Control(Field.ControlTypeContents.ctTextBox) = GridDetail.RowsCellFirstControl(i, "適用開始日")
            logic.List(i).税率.Control(Field.ControlTypeContents.ctTextBox) = GridDetail.RowsCellFirstControl(i, "税率")

            GridDetail.RowsCellFirstControl(i, "適用開始日").ID &= "_" & (i + 1)
        Next

        If Not IsPostBack Then
            read_data(True)
        End If

        MyBase.OnInit(e)
    End Sub

    Public Overrides Sub FinderClose(findername As String, result As System.Collections.Hashtable, resulttargetid As String)

    End Sub

    Public Overrides Sub FinderOpen()

    End Sub

    Public Overrides Sub MessageBoxClose(messageboxname As String, result As Dbs.Asphalt.UI.ModuleBase.MessageBoxResult)

    End Sub

    Public Overrides ReadOnly Property PageTitle As String
        Get
            Return logic.Title
        End Get
    End Property

    Public Overrides ReadOnly Property RunAuth As Dbs.Asphalt.Core.SystemLogic.Security.emUserAuth
        Get
            Return SystemLogic.Security.emUserAuth.一般
        End Get
    End Property

    Private Sub read_data(read_after_clear As Boolean)
        If Not logic.ReadMainData(True) Then
            Base.ErrorMessage = logic.LastError
        End If

        codebox_enable()
    End Sub

    Private Sub codebox_enable()
        For i = 0 To GridDetail.Rows.Count - 1
            Dim code As TextBox
            code = CType(GridDetail.RowsCellFirstControl(i, "適用開始日"), TextBox)
            If code.Text <> "" Then
                code.Enabled = False
            Else
                code.Enabled = True
            End If
        Next
    End Sub

    Protected Sub ButtonRegist_Click(sender As Object, e As System.Web.UI.ImageClickEventArgs) Handles ButtonRegist.Click
        If Not logic.WriteMainData() Then
            Base.ErrorMessage = logic.LastError
            Return
        End If

        Base.Message = logic.LastMessage

        read_data(True)
    End Sub

    Protected Sub DropDataKubun_SelectedIndexChanged(sender As Object, e As System.EventArgs) Handles DropDataKubun.SelectedIndexChanged
        read_data(True)
    End Sub

    Protected Sub ButtonDownload_Click(sender As Object, e As System.Web.UI.ImageClickEventArgs) Handles ButtonDownload.Click
        Base.DownloadExcel(logic.GetDataView, GridDetail, "zeiritsu_" & DropDataKubun.SelectedText & ".xlsx")
    End Sub
End Class
