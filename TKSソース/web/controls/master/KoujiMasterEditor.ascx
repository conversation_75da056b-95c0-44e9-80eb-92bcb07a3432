﻿<%@ Control Language="VB" AutoEventWireup="false" CodeFile="KoujiMasterEditor.ascx.vb" Inherits="KoujiMasterEditor" %>
<%@ Register Assembly="Dbs.Asphalt.UI" Namespace="Dbs.Asphalt.UI" TagPrefix="cc1" %>
<%@ Register Src="~/controls/common/RecordSelector.ascx" TagPrefix="uc1" TagName="RecordSelector" %>
<%@ Register Src="~/controls/common/ReportDataViewer.ascx" TagPrefix="uc1" TagName="ReportDataViewer" %>

<cc1:ItemFrame runat="server" ID="PanelBase" Text="基本情報">
  <div class="leftpane">
    <cc1:ItemPanel runat="server" Text="得意先コード">
      <cc1:TextBox runat="server" ID="TextTokuisakiCode" AutoPostBack="true" FinderButton="true" MasterTableName="M得意先" MasterGetName="得意先名" MasterKeyName="得意先コード"></cc1:TextBox><br />
    </cc1:ItemPanel>
    <cc1:ItemPanel runat="server" Text="工事コード">
      <cc1:TextBox runat="server" ID="TextKoujiCode" AutoPostBack="true" ></cc1:TextBox><cc1:TextBox runat="server" ID="TextKoujiNaibuCode" Visible="false" ></cc1:TextBox><br />
      <span class="guide">工事コードを空白で登録すると、自動的にコードを割り当てて追加登録されます。</span>
    </cc1:ItemPanel>

    <cc1:ItemPanel runat="server" Text="登録">
      <cc1:TextBox runat="server" ID="TextRegDate" ViewOnly="true"></cc1:TextBox>&nbsp;
      <cc1:TextBox runat="server" ID="TextRegUserID" ViewOnly="true"></cc1:TextBox>
    </cc1:ItemPanel>
    <cc1:ItemPanel runat="server" Text="更新">
      <cc1:TextBox runat="server" ID="TextUpdDate" ViewOnly="true"></cc1:TextBox>&nbsp;
      <cc1:TextBox runat="server" ID="TextUpdUserID" ViewOnly="true"></cc1:TextBox>
    </cc1:ItemPanel>
  </div>
  <div class="rightpane">
    <cc1:EditMode runat="server" ID="EditMode"></cc1:EditMode><br />
    <uc1:RecordSelector runat="server" ID="RecordSelector" DataKeyControlID="TextKoujiCode" DataName="M工事" FindItemList="工事名,工事名かな" KeyIsNumeric="false" KeyName="工事コード" AllowClearCheckBox="true" />
  </div>
  <div class="clear"></div>

  <hr />

  <cc1:ItemPanel runat="server" Text="工事名">
    <cc1:TextBox runat="server" ID="TextKoujiName"></cc1:TextBox>
  </cc1:ItemPanel>

  <cc1:ItemPanel runat="server" Text="カナ">
    <cc1:TextBox runat="server" ID="TextKoujiKana"></cc1:TextBox>
  </cc1:ItemPanel>

  <hr />

  <cc1:ItemPanel runat="server" Text="住所">
    <cc1:TextBox runat="server" ID="TextAddress" IMEOn="true"></cc1:TextBox><br />
  </cc1:ItemPanel>

  <hr />

  <cc1:ItemPanel runat="server" Text="完了区分">
    <cc1:DropDownList runat="server" ID="DropKanryoKubun"></cc1:DropDownList><br />
    <span class="guide">※完了にすると売上入力と工事台帳に表示されなくなります。</span>
  </cc1:ItemPanel>

  <hr />

  <cc1:ItemPanel runat="server" Text="摘要1">
    <cc1:TextBox runat="server" ID="TextTekiyou1" ></cc1:TextBox><br />
  </cc1:ItemPanel>

  <cc1:ItemPanel runat="server" Text="摘要2">
    <cc1:TextBox runat="server" ID="TextTekiyou2" ></cc1:TextBox><br />
  </cc1:ItemPanel>

</cc1:ItemFrame>

<asp:panel runat="server" ID="PanelToolbar">
  <cc1:ToolButton runat="server" ButtonImage="biSave" Text="保存" ID="ButtonRegist" PostbackTrriger="true" PostbackTrrigerFreeze="true" OnClick="ButtonRegist_Click" />
  <cc1:ToolButton runat="server" ButtonImage="biDelete" Text="削除" ID="ButtonDelete" PostbackTrriger="true" PostbackTrrigerFreeze="true" OnClick="ButtonDelete_Click" />
  <cc1:ToolButton runat="server" ButtonImage="biNew" Text="新規追加" ID="ButtonNew" OnClick="ButtonNew_Click" />
  <cc1:ToolButton runat="server" ButtonImage="biFinder" Text="検索" ID="ButtonFind" OnClick="ButtonFind_Click" />
</asp:panel>
