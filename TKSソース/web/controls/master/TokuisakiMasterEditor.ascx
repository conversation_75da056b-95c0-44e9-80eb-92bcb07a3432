﻿<%@ Control Language="C#" AutoEventWireup="true" CodeFile="TokuisakiMasterEditor.ascx.cs" Inherits="TokuisakiMasterEditor" %>
<%@ Register Assembly="Dbs.Asphalt.UI" Namespace="Dbs.Asphalt.UI" TagPrefix="cc1" %>
<%@ Register Src="~/controls/common/RecordSelector.ascx" TagPrefix="uc1" TagName="RecordSelector" %>
<%@ Register Src="~/controls/common/ReportDataViewer.ascx" TagPrefix="uc1" TagName="ReportDataViewer" %>


<span>このモジュールはC#でのコーディング例です。</span>

<cc1:ItemFrame runat="server" ID="PanelBase" Text="基本情報">
  <div class="leftpane">
    <cc1:ItemPanel runat="server" Text="得意先コード">
      <cc1:TextBox runat="server" ID="TextCode" AutoPostBack="true" OnTextChanged="TextCode_TextChanged"></cc1:TextBox><br />
      <span class="guide">得意先コードを空白で登録すると、自動的にコードを割り当てて追加登録されます。</span>
    </cc1:ItemPanel>
  </div>
  <div class="rightpane">
    <cc1:EditMode runat="server" ID="EditMode"></cc1:EditMode><br />
    <uc1:RecordSelector runat="server" ID="RecordSelector" DataKeyControlID="TextCode" DataName="M得意先" FindItemList="得意先名,電話番号" KeyIsNumeric="false" KeyName="得意先コード" AllowClearCheckBox="true" />
  </div>
  <div class="clear"></div>

  <hr />

  <cc1:ItemPanel runat="server" Text="得意先名">
    <cc1:TextBox runat="server" ID="TextName"></cc1:TextBox>
  </cc1:ItemPanel>

  <hr />

  <cc1:ItemPanel runat="server" Text="住所">
    <cc1:TextBox runat="server" ID="TextPostNo" ItemName="〒" AutoPostBack="true" AutoComplete="true" AutoCompleteListMethod="PostNo" OnTextChanged="TextPostNo_TextChanged"></cc1:TextBox><br />
    <cc1:TextBox runat="server" ID="TextAddress1" IMEOn="true"></cc1:TextBox><br />
    <cc1:TextBox runat="server" ID="TextAddress2" IMEOn="true"></cc1:TextBox>
  </cc1:ItemPanel>

  <hr />

  <cc1:ItemPanel runat="server" Text="電話番号">
    <cc1:TextBox runat="server" ID="TextTel"></cc1:TextBox>
  </cc1:ItemPanel>

  <cc1:ItemPanel runat="server" Text="FAX番号">
    <cc1:TextBox runat="server" ID="TextFax"></cc1:TextBox>
  </cc1:ItemPanel>

  <hr />

  <cc1:ItemPanel runat="server" Text="消費税区分">
    <cc1:DropDownList runat="server" ID="DropZeiKubun"></cc1:DropDownList>
  </cc1:ItemPanel>

</cc1:ItemFrame>

<hr />
<h2>データ照会サンプル</h2>
<uc1:ReportDataViewer runat="server" ID="ReportDataViewer" OnItemDataBound="ReportDataViewer_ItemDataBound" />

<asp:panel runat="server" ID="PanelToolbar">
  <cc1:ToolButton runat="server" ButtonImage="biSave" Text="保存" ID="ButtonRegist" PostbackTrriger="true" PostbackTrrigerFreeze="true" OnClick="ButtonRegist_Click" />
  <cc1:ToolButton runat="server" ButtonImage="biDelete" Text="削除" ID="ButtonDelete" PostbackTrriger="true" PostbackTrrigerFreeze="true" OnClick="ButtonDelete_Click" />
  <cc1:ToolButton runat="server" ButtonImage="biNew" Text="新規追加" ID="ButtonNew" OnClick="ButtonNew_Click" />
  <cc1:ToolButton runat="server" ButtonImage="biFinder" Text="検索" ID="ButtonFind" OnClick="ButtonFind_Click" />
</asp:panel>
