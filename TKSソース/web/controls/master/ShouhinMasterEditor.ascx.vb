﻿Imports Dbs.Asphalt.UI
Imports Dbs.Asphalt.Core
Imports Dbs.Asphalt.Core.CoreLogic.System
Imports Dbs.Asphalt.Core.Common
Imports System.IO
Imports Dbs.Application.Logic.Logic.Master

Partial Class ShouhinMasterEditor
    Inherits ModuleBase

    Private logic As ShouhinMasterManager

    Protected Overrides Sub OnInit(e As System.EventArgs)
        logic = New ShouhinMasterManager(Base.Security)

        logic.EditMode.Control(Field.ControlTypeContents.ctEditMode) = EditMode
        logic.商品コード.Control(Field.ControlTypeContents.ctTextBox) = TextCode
        logic.商品名.Control(Field.ControlTypeContents.ctTextBox) = TextName
        logic.商品名かな.Control(Field.ControlTypeContents.ctTextBox) = TextKana
        logic.単位区分.Control(Field.ControlTypeContents.ctRadioButtonList) = DropUnitKubun
        logic.基本料金.Control(Field.ControlTypeContents.ctTextBox) = TextTeika
        logic.単価.Control(Field.ControlTypeContents.ctTextBox) = TextTanka
        logic.商品分類区分.Control(Field.ControlTypeContents.ctRadioButtonList) = DropBunruiKubun
        logic.備考.Control(Field.ControlTypeContents.ctTextBox) = TextBikou
        logic.保証日数.Control(Field.ControlTypeContents.ctTextBox) = TextHosho

        logic.登録日時.Control(Field.ControlTypeContents.ctTextBox) = TextRegDate
        logic.登録者ID.Control(Field.ControlTypeContents.ctTextBox) = TextRegUserID
        logic.更新日時.Control(Field.ControlTypeContents.ctTextBox) = TextUpdDate
        logic.更新者ID.Control(Field.ControlTypeContents.ctTextBox) = TextUpdUserID

        logic.Start()

        ' Finderの読み込み
        Base.LoadFinder("ShouhinMasterFinder", "CommonFinder", "M商品検索", "商品コード")

        MyBase.OnInit(e)
    End Sub

    Protected Overrides Sub OnLoad(e As System.EventArgs)

        If Not IsPostBack Then
            'TextUserID.Text = Base.Security.UserID
            read_data(True)
        End If

        MyBase.OnLoad(e)
    End Sub

    Private Sub AccountEditor_PreRender(sender As Object, e As EventArgs) Handles Me.PreRender

        ButtonDelete.Visible = (logic.EditMode.Value = Field.EditModeContents.emUpdateMode)
    End Sub

    Public Overrides Sub FinderClose(findername As String, result As System.Collections.Hashtable, resulttargetid As String)
    End Sub

    Public Overrides Sub FinderOpen()

    End Sub

    Public Overrides ReadOnly Property PageTitle() As String
        Get
            Return logic.Title
        End Get
    End Property

    ' 保存ﾎﾞﾀﾝ
    Protected Sub ButtonRegist_Click(sender As Object, e As System.EventArgs) Handles ButtonRegist.Click
        If Not logic.WriteMainData Then
            Base.ErrorMessage = logic.LastError
            Return
        End If

        Base.Message = logic.LastMessage

        read_data(RecordSelector.ClearItems)
    End Sub

    Protected Sub ButtonNew_Click(sender As Object, e As System.EventArgs) Handles ButtonNew.Click
        TextCode.Text = ""
        read_data(RecordSelector.ClearItems)
    End Sub

    Protected Sub ButtonFind_Click(sender As Object, e As System.EventArgs) Handles ButtonFind.Click
        Dim param As New Hashtable
        Base.OpenFinder("ShouhinMasterFinder", param, "TextCode")
    End Sub

    Protected Sub ButtonDelete_Click(sender As Object, e As System.EventArgs) Handles ButtonDelete.Click
        If Not logic.DeleteMainData Then
            Base.ErrorMessage = logic.LastError
            Return
        End If

        Base.Message = logic.LastMessage

        TextCode.Text = ""
        read_data(True)
    End Sub

    Public Overrides ReadOnly Property RunAuth() As Dbs.Asphalt.Core.SystemLogic.Security.emUserAuth
        Get
            Return SystemLogic.Security.emUserAuth.一般
        End Get
    End Property

    Public Overrides Sub MessageBoxClose(messageboxname As String, result As Dbs.Asphalt.UI.ModuleBase.MessageBoxResult)

    End Sub

    Private Sub read_data(read_after_clear As Boolean)
        If Not logic.ReadMainData(read_after_clear) Then
            Base.ErrorMessage = logic.LastError
        End If
    End Sub

    Protected Sub TextCode_TextChanged(sender As Object, e As EventArgs) Handles TextCode.TextChanged
        read_data(True)
    End Sub
End Class
