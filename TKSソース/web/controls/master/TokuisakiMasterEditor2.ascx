﻿<%@ Control Language="VB" AutoEventWireup="false" CodeFile="TokuisakiMasterEditor2.ascx.vb" Inherits="TokuisakiMasterEditor2" %>
<%@ Register Assembly="Dbs.Asphalt.UI" Namespace="Dbs.Asphalt.UI" TagPrefix="cc1" %>
<%@ Register Src="~/controls/common/RecordSelector.ascx" TagPrefix="uc1" TagName="RecordSelector" %>
<%@ Register Src="~/controls/common/ReportDataViewer.ascx" TagPrefix="uc1" TagName="ReportDataViewer" %>

<cc1:ItemFrame runat="server" ID="PanelBase" Text="基本情報">
  <div class="leftpane">
    <cc1:ItemPanel runat="server" Text="得意先コード">
      <cc1:TextBox runat="server" ID="TextCode" AutoPostBack="true" ></cc1:TextBox><br />
      <span class="guide">得意先コードを空白で登録すると、自動的にコードを割り当てて追加登録されます。</span>
    </cc1:ItemPanel>

    <cc1:ItemPanel runat="server" Text="登録">
      <cc1:TextBox runat="server" ID="TextRegDate" ViewOnly="true"></cc1:TextBox>&nbsp;
      <cc1:TextBox runat="server" ID="TextRegUserID" ViewOnly="true"></cc1:TextBox>
    </cc1:ItemPanel>
    <cc1:ItemPanel runat="server" Text="更新">
      <cc1:TextBox runat="server" ID="TextUpdDate" ViewOnly="true"></cc1:TextBox>&nbsp;
      <cc1:TextBox runat="server" ID="TextUpdUserID" ViewOnly="true"></cc1:TextBox>
    </cc1:ItemPanel>
  </div>
  <div class="rightpane">
    <cc1:EditMode runat="server" ID="EditMode"></cc1:EditMode><br />
    <uc1:RecordSelector runat="server" ID="RecordSelector" DataKeyControlID="TextCode" DataName="M得意先" FindItemList="得意先名,得意先かな,電話番号" KeyIsNumeric="false" KeyName="得意先コード" AllowClearCheckBox="true" />
  </div>
  <div class="clear"></div>

  <hr />

  <cc1:ItemPanel runat="server" Text="得意先名">
    <cc1:TextBox runat="server" ID="TextName"></cc1:TextBox>
  </cc1:ItemPanel>

  <cc1:ItemPanel runat="server" Text="カナ">
    <cc1:TextBox runat="server" ID="TextKana"></cc1:TextBox>
  </cc1:ItemPanel>

  <hr />

  <cc1:ItemPanel runat="server" Text="住所">
    <cc1:TextBox runat="server" ID="TextPostNo" ItemName="〒" AutoPostBack="true" AutoComplete="true" AutoCompleteListMethod="PostNo" ></cc1:TextBox><br />
    <cc1:TextBox runat="server" ID="TextAddress1" IMEOn="true"></cc1:TextBox><br />
    <cc1:TextBox runat="server" ID="TextAddress2" IMEOn="true"></cc1:TextBox>
  </cc1:ItemPanel>

  <hr />

  <cc1:ItemPanel runat="server" Text="電話番号">
    <cc1:TextBox runat="server" ID="TextTel"></cc1:TextBox>
  </cc1:ItemPanel>

  <cc1:ItemPanel runat="server" Text="FAX番号">
    <cc1:TextBox runat="server" ID="TextFax"></cc1:TextBox>
  </cc1:ItemPanel>

  <hr />

  <cc1:ItemPanel runat="server" Text="担当者">
    <cc1:TextBox runat="server" ID="TextTantoCode" FinderButton="true" MasterTableName="Mユーザー" MasterGetName="ユーザー名" MasterKeyName="ユーザーID" AutoPostBack="true"></cc1:TextBox>
  </cc1:ItemPanel>

  <cc1:ItemPanel runat="server" Text="締日区分">
    <cc1:DropDownList runat="server" ID="DropShimebiKubun"></cc1:DropDownList>
  </cc1:ItemPanel>

  <cc1:ItemPanel runat="server" Text="端数区分">
    <cc1:DropDownList runat="server" ID="DropHasuKubun"></cc1:DropDownList>
  </cc1:ItemPanel>

  <cc1:ItemPanel runat="server" Text="請求書区分">
    <cc1:DropDownList runat="server" ID="DropSeikyuKubun"></cc1:DropDownList>
  </cc1:ItemPanel>

  <cc1:ItemPanel runat="server" Text="専用帳票区分">
    <cc1:DropDownList runat="server" ID="DropShoguchiKubun"></cc1:DropDownList>
  </cc1:ItemPanel>

  <cc1:ItemPanel runat="server" Text="消費税区分">
    <cc1:DropDownList runat="server" ID="DropZeiKubun"></cc1:DropDownList>
  </cc1:ItemPanel>
</cc1:ItemFrame>

<hr />
<asp:panel runat="server" ID="PanelToolbar">
  <cc1:ToolButton runat="server" ButtonImage="biSave" Text="保存" ID="ButtonRegist" PostbackTrriger="true" PostbackTrrigerFreeze="true" OnClick="ButtonRegist_Click" />
  <cc1:ToolButton runat="server" ButtonImage="biDelete" Text="削除" ID="ButtonDelete" PostbackTrriger="true" PostbackTrrigerFreeze="true" OnClick="ButtonDelete_Click" />
  <cc1:ToolButton runat="server" ButtonImage="biNew" Text="新規追加" ID="ButtonNew" OnClick="ButtonNew_Click" />
  <cc1:ToolButton runat="server" ButtonImage="biFinder" Text="検索" ID="ButtonFind" OnClick="ButtonFind_Click" />
</asp:panel>
