﻿Imports Dbs.Asphalt.UI
Imports Dbs.Asphalt.Core
Imports Dbs.Asphalt.Core.CoreLogic.System
Imports Dbs.Asphalt.Core.Common
Imports Dbs.Asphalt.Core.SystemLogic
Imports System.Data
Imports System.Net

Partial Class Index
    Inherits ModuleBase

    Private logic As ForumEditorManager
    Private logicComment As ForumEditorManager
    Private logicView As ForumEditorManager

    Protected Overrides Sub OnInit(e As System.EventArgs)
        logic = New ForumEditorManager(Base.Security)
        logic.投稿ID.Control(Field.ControlTypeContents.ctTextBox) = Nothing
        logic.件名.Control(Field.ControlTypeContents.ctTextBox) = TextSubject
        logic.本文.Control(Field.ControlTypeContents.ctTextBox) = TextBody
        logic.Start()

        logicComment = New ForumEditorManager(Base.Security)
        logicComment.親投稿ID.Control(Field.ControlTypeContents.ctTextBox) = LabelDataID
        logicComment.件名.Control(Field.ControlTypeContents.ctTextBox) = LabelSubject
        logicComment.本文.Control(Field.ControlTypeContents.ctTextBox) = TextComment
        logicComment.Start()

        Base.AddPostbackTrrigerControl(ButtonRegist, True)

        MyBase.OnInit(e)
    End Sub

    Protected Overrides Sub OnLoad(e As System.EventArgs)
        If Not IsPostBack Then
            If Not Request("id") Is Nothing Then
                readdetail(Request("id"))
            End If
        End If

        MyBase.OnLoad(e)
    End Sub

    Public Overrides Sub FinderClose(findername As String, result As System.Collections.Hashtable, resulttargetid As String)

    End Sub

    Public Overrides Sub FinderOpen()

    End Sub

    Public Overrides ReadOnly Property PageTitle() As String
        Get
            Return logic.Title
        End Get
    End Property

    Public Overrides ReadOnly Property RunAuth() As Dbs.Asphalt.Core.SystemLogic.Security.emUserAuth
        Get
            Return Security.emUserAuth.一般
        End Get
    End Property

    Protected Sub GridResult_RowDataBound(sender As Object, e As Dbs.Asphalt.UI.GridViewExRowEventArgs) Handles GridResult.RowDataBound
        If e.Row.RowType = DataControlRowType.DataRow Then
            e.RowCellFormat("投稿日時", 10, Text.FormatContents.tbLongDateTime)

            CType(e.RowCell("添付").FindControl("ImageClip"), Image).Visible = (e.RowCell("添付ファイルあり").Text = "1")
        End If

        e.RowCell("投稿ID").Visible = False
        e.RowCell("添付ファイルあり").Visible = False
    End Sub

    Protected Sub ButtonRegist_Click(sender As Object, e As System.EventArgs) Handles ButtonRegist.Click
        If FileFile1.FileName <> "" Then
            logic.ファイル1.Value = FileFile1.FileBytes
            logic.ファイル1名前.Value = FileFile1.FileName
            logic.ファイル1形式.Value = FileFile1.PostedFile.ContentType
        End If
        If FileFile2.FileName <> "" Then
            logic.ファイル2.Value = FileFile2.FileBytes
            logic.ファイル2名前.Value = FileFile2.FileName
            logic.ファイル2形式.Value = FileFile2.PostedFile.ContentType
        End If
        If FileFile3.FileName <> "" Then
            logic.ファイル3.Value = FileFile3.FileBytes
            logic.ファイル3名前.Value = FileFile3.FileName
            logic.ファイル3形式.Value = FileFile3.PostedFile.ContentType
        End If

        If Not logic.WriteMainData() Then
            Base.ErrorMessage = logic.LastError
            Return
        End If

        TextSubject.Text = ""
        TextBody.Text = ""

        DataResult.DataBind()
        GridResult.DataBind()
    End Sub

    Protected Sub GridResult_SelectedIndexChanged(sender As Object, e As System.EventArgs) Handles GridResult.SelectedIndexChanged
        readdetail(GridResult.RowsCell(GridResult.SelectedIndex, "投稿ID").Text)
    End Sub

    Protected Sub ButtonRegistComment_Click(sender As Object, e As System.EventArgs) Handles ButtonRegistComment.Click
        If Not logicComment.WriteMainData() Then
            Base.ErrorMessage = logicComment.LastError
            Return
        End If

        TextComment.Text = ""

        DataComment.DataBind()
        RepeatComment.DataBind()

        DataResult.DataBind()
        GridResult.DataBind()
    End Sub

    Protected Sub RepeatComment_ItemCommand(source As Object, e As System.Web.UI.WebControls.RepeaterCommandEventArgs) Handles RepeatComment.ItemCommand
        If e.CommandName = "DeleteComment" Then
            logicComment.投稿ID.Value = e.CommandArgument
            If Not logicComment.DeleteMainData() Then
                Base.ErrorMessage = logicComment.LastError
                Return
            End If

            DataComment.DataBind()
            RepeatComment.DataBind()
        End If
    End Sub

    Protected Sub ButtonDeleteData_Click(sender As Object, e As System.EventArgs) Handles ButtonDeleteData.Click
        logic.投稿ID.Control(Field.ControlTypeContents.ctTextBox) = LabelDataID
        If Not logic.DeleteMainData() Then
            Base.ErrorMessage = logic.LastError
            Return
        End If

        FrameFindItem.Visible = True
        FrameItemViewer.Visible = False

        DataResult.DataBind()
        GridResult.DataBind()
    End Sub

    Protected Sub RepeatComment_ItemDataBound(sender As Object, e As System.Web.UI.WebControls.RepeaterItemEventArgs) Handles RepeatComment.ItemDataBound
        CType(e.Item.FindControl("ButtonDeleteComment"), LinkButton).OnClientClick = "return confirm('" & Message.MessageText("MS_QUESTDELETE") & "');"
        CType(e.Item.FindControl("ImageCommentAuthor"), UserImage).UserID = CType(e.Item.FindControl("LabelCommentAuthorID"), Label).Text

        If CType(e.Item.FindControl("LabelCommentAuthorID"), Label).Text <> Base.Security.UserID Then
            logicComment.投稿ID.Value = CType(e.Item.FindControl("LabelCommentDataID"), Label).Text
            e.Item.FindControl("ButtonDeleteComment").Visible = logicComment.CheckDeleteAuth()
        End If
    End Sub

    Public Overrides Sub MessageBoxClose(messageboxname As String, result As Dbs.Asphalt.UI.ModuleBase.MessageBoxResult)

    End Sub

    Private Sub readdetail(id As Decimal)
        FrameFindItem.Visible = False
        FrameItemViewer.Visible = True

        LabelDataID.Text = id

        logic.投稿ID.Control(Field.ControlTypeContents.ctTextBox) = LabelDataID
        logic.投稿者名.Control(Field.ControlTypeContents.ctTextBox) = LabelAuthorName
        logic.投稿日時.Control(Field.ControlTypeContents.ctTextBox) = LabelDataDateTime
        logic.件名.Control(Field.ControlTypeContents.ctTextBox) = LabelSubject
        logic.本文.Control(Field.ControlTypeContents.ctTextBox) = LabelBody

        If Not logic.ReadMainData(True) Then
            Base.ErrorMessage = logic.LastError
            Return
        End If

        LabelAuthorName.Text = Base.UICommon.MakeUserDisplayHTML(logic.投稿者ID.Value)
        ImageAuthor.UserID = logic.投稿者ID.Value
        LabelBody.Text = (New UICommon(Nothing)).AutoLink(LabelBody.Text)

        ImageFile1.ImageUrl = "../ajax.aspx?cmd=forumfile&id=" & logic.投稿ID.Value & "&no=1&thumbnail=1"
        LinkFile1.NavigateUrl = "../ajax.aspx?cmd=forumfile&id=" & logic.投稿ID.Value & "&no=1"
        LabelFile1.Text = logic.ファイル1名前.Value
        ImageFile1.Visible = Not IsNothing(logic.ファイル1.Value)
        ImageFile2.ImageUrl = "../ajax.aspx?cmd=forumfile&id=" & logic.投稿ID.Value & "&no=2&thumbnail=1"
        LinkFile2.NavigateUrl = "../ajax.aspx?cmd=forumfile&id=" & logic.投稿ID.Value & "&no=2"
        LabelFile2.Text = logic.ファイル2名前.Value
        ImageFile2.Visible = Not IsNothing(logic.ファイル2.Value)
        ImageFile3.ImageUrl = "../ajax.aspx?cmd=forumfile&id=" & logic.投稿ID.Value & "&no=3&thumbnail=1"
        LinkFile3.NavigateUrl = "../ajax.aspx?cmd=forumfile&id=" & logic.投稿ID.Value & "&no=3"
        LabelFile3.Text = logic.ファイル3名前.Value
        ImageFile3.Visible = Not IsNothing(logic.ファイル3.Value)

        LabelBody.Text = Replace(LabelBody.Text, vbLf, "<br />")

        ButtonDeleteData.OnClientClick = "return confirm('" & Message.MessageText("MS_QUESTDELETE") & "');"
        ButtonDeleteData.Visible = logic.CheckDeleteAuth()

        ImageMyComment.UserID = Base.Security.UserID
    End Sub

    Protected Sub ButtonBack_Click(sender As Object, e As System.EventArgs) Handles ButtonBack.Click
        FrameFindItem.Visible = True
        FrameItemViewer.Visible = False

        DataResult.DataBind()
        GridResult.DataBind()
    End Sub

End Class
