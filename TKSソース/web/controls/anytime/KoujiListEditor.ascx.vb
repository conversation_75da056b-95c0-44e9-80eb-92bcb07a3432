﻿Imports System.Data
Imports Dbs.Asphalt.UI
Imports Dbs.Asphalt.Core
Imports Dbs.Asphalt.Core.CoreLogic.System
Imports Dbs.Asphalt.Core.Common
Imports System.IO
Imports Dbs.Application.Logic.Logic.Anytime

Partial Class KoujiListEditor
    Inherits ModuleBase

    Private logic As KoujiListManager

    Protected Overrides Sub OnInit(e As System.EventArgs)
        logic = New KoujiListManager(Base.Security)

        logic.得意先コード.Control(Field.ControlTypeContents.ctTextBox) = Text得意先コード
        logic.工事コードF.Control(Field.ControlTypeContents.ctTextBox) = Text工事コードF
        logic.工事コードT.Control(Field.ControlTypeContents.ctTextBox) = Text工事コードT
        logic.対象期間F.Control(Field.ControlTypeContents.ctTextBox) = Text対象期間F
        logic.対象期間T.Control(Field.ControlTypeContents.ctTextBox) = Text対象期間T

        logic.日付.Control(Field.ControlTypeContents.ctTextBox) = Text日付
        logic.得意先名.Control(Field.ControlTypeContents.ctTextBox) = Text得意先名
        logic.工事名.Control(Field.ControlTypeContents.ctTextBox) = Text工事名
        logic.商品名.Control(Field.ControlTypeContents.ctTextBox) = Text商品名
        logic.金額.Control(Field.ControlTypeContents.ctTextBox) = Text金額

        logic.備考.Control(Field.ControlTypeContents.ctTextBox) = Text備考
        logic.伝票区分.Control(Field.ControlTypeContents.ctTextBox) = Text伝票区分
        logic.伝票番号.Control(Field.ControlTypeContents.ctTextBox) = Text伝票番号
        logic.行番号.Control(Field.ControlTypeContents.ctTextBox) = Text行番号
        logic.工事内部コード.Control(Field.ControlTypeContents.ctTextBox) = Text工事内部コード
        logic.完了区分.Control(Field.ControlTypeContents.ctDropDownList) = Drop完了区分

        logic.Start()

        'Base.AddPostbackTrrigerControl(ButtonPrint)

        ' Finderの読み込み
        Base.LoadFinder("TokuisakiMasterFinder", "CommonFinder", "M得意先検索", "得意先コード")
        Base.LoadFinder("KoujiMasterFinder", "CommonFinder", "M工事検索", "_取得値")

        MyBase.OnInit(e)
    End Sub

    Protected Overrides Sub OnLoad(e As System.EventArgs)

        If Not IsPostBack Then
            'TextUserID.Text = Base.Security.UserID
            'read_data(True)
        End If

        MyBase.OnLoad(e)
    End Sub

    Private Sub AccountEditor_PreRender(sender As Object, e As EventArgs) Handles Me.PreRender

        'ButtonDelete.Visible = (logic.EditMode.Value = Field.EditModeContents.emUpdateMode)
    End Sub

    Public Overrides Sub FinderClose(findername As String, result As System.Collections.Hashtable, resulttargetid As String)
    End Sub

    Public Overrides Sub FinderOpen()

    End Sub
    Private Sub Text得意先コード_FinderButtonClicked(sender As Object, e As ImageClickEventArgs) Handles Text得意先コード.FinderButtonClicked
        ' 得意先検索を開く
        Base.OpenFinder("TokuisakiMasterFinder", , sender.ID)
    End Sub

    Private Sub Text工事コード_FinderButtonClicked(sender As Object, e As ImageClickEventArgs) Handles Text工事コードF.FinderButtonClicked, Text工事コードT.FinderButtonClicked
        ' 工事検索を開く
        Dim param As New Hashtable
        param("固定条件") = "得意先コード = '" & Text得意先コード.Text & "'"

        Base.OpenFinder("KoujiMasterFinder", param, sender.ID)
    End Sub

    Public Overrides ReadOnly Property PageTitle() As String
        Get
            Return logic.Title
        End Get
    End Property

    Protected Sub GridResult_RowDataBound(sender As Object, e As Dbs.Asphalt.UI.GridViewExRowEventArgs) Handles GridResult.RowDataBound

        ' 表示非表示
        e.RowCell("備考").Visible = False
        e.RowCell("伝票区分").Visible = False
        e.RowCell("伝票番号").Visible = False
        e.RowCell("行番号").Visible = False
        e.RowCell("工事内部コード").Visible = False

        ' 書式
        If e.Row.RowType = DataControlRowType.DataRow Then
            e.RowCell("数量").HorizontalAlign = HorizontalAlign.Right
            e.RowCellFormat("数量", 15, Text.FormatContents.tbCurrency, 2)

            e.RowCell("単価").HorizontalAlign = HorizontalAlign.Right
            e.RowCellFormat("単価", 15, Text.FormatContents.tbCurrency, 2)

            e.RowCell("基本料金").HorizontalAlign = HorizontalAlign.Right
            e.RowCellFormat("基本料金", 15, Text.FormatContents.tbCurrency)

            e.RowCell("売上金額").HorizontalAlign = HorizontalAlign.Right
            e.RowCellFormat("売上金額", 15, Text.FormatContents.tbCurrency)

            e.RowCell("入金金額").HorizontalAlign = HorizontalAlign.Right
            e.RowCellFormat("入金金額", 15, Text.FormatContents.tbCurrency)

            e.RowCell("請求残高").HorizontalAlign = HorizontalAlign.Right
            e.RowCellFormat("請求残高", 15, Text.FormatContents.tbCurrency)

            If e.RowCell("伝票区分").Text >= "4" Then  ' 小計、消費税、合計
                e.RowCell("").Visible = False
                'CType(e.RowCellFirstControl(""), DataControlButton).ShowSelectButton = False
                'e.RowCell("").Enabled = False
            End If

            If e.RowCell("商品名").Text = "小計" Then
                e.RowCell("商品名").HorizontalAlign = HorizontalAlign.Right
                e.RowCell("日付").Text = ""
            End If
            If e.RowCell("商品名").Text = "消費税" Then
                e.RowCell("商品名").HorizontalAlign = HorizontalAlign.Right
                e.RowCell("日付").Text = ""
            End If
            If e.RowCell("商品名").Text = "合計" Then
                e.Row.BackColor = Drawing.Color.LightGoldenrodYellow
                e.RowCell("商品名").Font.Bold = True
                e.RowCell("商品名").HorizontalAlign = HorizontalAlign.Right
                e.RowCell("日付").Text = ""
            End If

        End If

    End Sub

    Protected Sub GridResult_SelectedIndexChanged(sender As Object, e As System.EventArgs) Handles GridResult.SelectedIndexChanged

        If GridResult.RowsCell(GridResult.SelectedIndex, "伝票区分").Text >= "4" Then
            Base.ErrorMessage = "選択した行はメモの登録はできません。"
            Exit Sub
        End If

        Text日付.Text = GridResult.RowsCell(GridResult.SelectedIndex, "日付").Text
        Text得意先名.Text = GridResult.RowsCell(GridResult.SelectedIndex, "得意先名").Text
        Text工事名.Text = GridResult.RowsCell(GridResult.SelectedIndex, "工事名").Text
        Text商品名.Text = GridResult.RowsCell(GridResult.SelectedIndex, "商品名").Text
        Text備考.Text = GridResult.RowsCell(GridResult.SelectedIndex, "備考").Text.Replace("&nbsp;", "")
        Text伝票区分.Text = GridResult.RowsCell(GridResult.SelectedIndex, "伝票区分").Text
        Text伝票番号.Text = GridResult.RowsCell(GridResult.SelectedIndex, "伝票番号").Text
        Text行番号.Text = GridResult.RowsCell(GridResult.SelectedIndex, "行番号").Text
        Text工事内部コード.Text = GridResult.RowsCell(GridResult.SelectedIndex, "工事内部コード").Text

        Drop完了区分.SelectedValue = logic.MasterName.AnyTableName("M工事", "工事内部コード", "完了区分", Text工事内部コード.Text)

        Dim str金額名 As String = ""
        Select Case GridResult.RowsCell(GridResult.SelectedIndex, "伝票区分").Text
            Case "1" : str金額名 = "請求残高"
            Case "2" : str金額名 = "入金金額"
            Case "3" : str金額名 = "売上金額"
        End Select
        Text金額.Text = GridResult.RowsCell(GridResult.SelectedIndex, str金額名).Text

        PanelBase.Visible = False
        FrameResult.Visible = False

        FrameItemViewer.Visible = True

    End Sub

    Protected Sub ButtonFind_Click(sender As Object, e As System.EventArgs) Handles ButtonFind.Click

        GridResult.PageIndex = 0
        GridResult.DataBind()
        PanelBase.Visible = True
        FrameResult.Visible = True
        FrameItemViewer.Visible = False
    End Sub

    Protected Sub ButtonBack_Click(sender As Object, e As System.EventArgs) Handles ButtonBack.Click
        ButtonFind_Click(sender, e)
    End Sub

    Protected Sub ButtonDownload_Click(sender As Object, e As System.Web.UI.ImageClickEventArgs) Handles ButtonDownload.Click
        Base.DownloadExcel(logic.GetDataView,, logic.FileName & ".xlsx")
    End Sub

    Private Sub ButtonRegist_Click(sender As Object, e As EventArgs) Handles ButtonRegist.Click
        If Not logic.WriteMainData() Then
            Base.ErrorMessage = logic.LastError
            Return
        End If

        Base.Message = "正常に終了しました。"
    End Sub

    Public Overrides ReadOnly Property RunAuth() As Dbs.Asphalt.Core.SystemLogic.Security.emUserAuth
        Get
            Return SystemLogic.Security.emUserAuth.一般
        End Get
    End Property

    Public Overrides Sub MessageBoxClose(messageboxname As String, result As Dbs.Asphalt.UI.ModuleBase.MessageBoxResult)

    End Sub
End Class
