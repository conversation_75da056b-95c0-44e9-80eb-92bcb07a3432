﻿Imports System.Data
Imports Dbs.Asphalt.UI
Imports Dbs.Asphalt.Core
Imports Dbs.Asphalt.Core.CoreLogic.System
Imports Dbs.Asphalt.Core.Common
Imports System.IO
Imports Dbs.Application.Logic.Logic.Anytime

Partial Class UriageTeikiEditor
    Inherits ModuleBase

    Private logic As UriageTeikiManager

    Protected Overrides Sub OnInit(e As System.EventArgs)
        logic = New UriageTeikiManager(Base.Security)

        logic.売上日付.Control(Field.ControlTypeContents.ctTextBox) = Text売上日付
        logic.得意先コード.Control(Field.ControlTypeContents.ctTextBox) = Text得意先コード
        logic.Start()

        ' Finderの読み込み
        Base.LoadFinder("TokuisakiMasterFinder", "CommonFinder", "M得意先検索", "得意先コード")
        'Base.LoadFinder("UriageDenpyoFinder", "CommonFinder", "T売上検索", "売上番号")
        'Base.LoadFinder("KoujiMasterFinder", "CommonFinder", "M工事検索", "_取得値")

        MyBase.OnInit(e)
    End Sub

    Protected Overrides Sub OnLoad(e As System.EventArgs)

        If Not IsPostBack Then
            'TextUserID.Text = Base.Security.UserID
            'read_data(True)
        End If

        MyBase.OnLoad(e)
    End Sub

    Private Sub AccountEditor_PreRender(sender As Object, e As EventArgs) Handles Me.PreRender

        'ButtonDelete.Visible = (logic.EditMode.Value = Field.EditModeContents.emUpdateMode)
    End Sub

    Public Overrides Sub FinderClose(findername As String, result As System.Collections.Hashtable, resulttargetid As String)
    End Sub

    Public Overrides Sub FinderOpen()

    End Sub

    Public Overrides ReadOnly Property PageTitle() As String
        Get
            Return logic.Title
        End Get
    End Property

    Private Sub Text得意先コード_FinderButtonClicked(sender As Object, e As ImageClickEventArgs) Handles Text得意先コード.FinderButtonClicked
        ' 得意先検索を開く
        Base.OpenFinder("TokuisakiMasterFinder", , sender.ID)
    End Sub

    Protected Sub ButtonFind_Click(sender As Object, e As System.EventArgs) Handles ButtonFind.Click
        Dim param As New Hashtable

        ' チェックをクリア
        GridPager1.ClearCheckCondition()
        GridPager1.AllCheckCondition(True)

        GridResult.PageIndex = 0
        GridResult.DataBind()
        FrameResult.Visible = True
    End Sub

    Protected Sub ButtonRegist_Click(sender As Object, e As ImageClickEventArgs) Handles ButtonRegist.Click

        ' 以下のコードで、idlistにチェックされているｷｰがカンマ区切りで格納される。
        Dim idlist As String = ""
        GridPager1.SaveCheckCondition()
        If Not GridPager1.CheckCondition Is Nothing Then
            For Each v As Object In GridPager1.CheckCondition
                idlist &= IIf(idlist = "", "", ",") & v.ToString
            Next
        End If

        logic.売上リスト.Value = idlist

        ' チェックをクリア
        GridPager1.ClearCheckCondition()

        If Not logic.WriteMainData() Then
            Base.ErrorMessage = logic.LastError
            Return
        End If

        Base.Message = "正常に終了しました。"

        ButtonFind_Click(sender, e)
    End Sub

    Public Overrides ReadOnly Property RunAuth() As Dbs.Asphalt.Core.SystemLogic.Security.emUserAuth
        Get
            Return SystemLogic.Security.emUserAuth.一般
        End Get
    End Property

    Public Overrides Sub MessageBoxClose(messageboxname As String, result As Dbs.Asphalt.UI.ModuleBase.MessageBoxResult)

    End Sub
End Class
