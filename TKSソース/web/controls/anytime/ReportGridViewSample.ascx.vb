﻿Imports System.Data
Imports Dbs.Asphalt.UI
Imports Dbs.Asphalt.Core
Imports Dbs.Asphalt.Core.Common

Partial Class ReportGridViewSample
    Inherits ModuleBase

    Protected Overrides Sub OnInit(e As System.EventArgs)
        MyBase.OnInit(e)
    End Sub

    Public Overrides Sub FinderClose(findername As String, result As System.Collections.Hashtable, resulttargetid As String)

    End Sub

    Public Overrides Sub FinderOpen()

    End Sub

    Public Overrides ReadOnly Property PageTitle() As String
        Get
            Return "レポートページング表示サンプル"
        End Get
    End Property

    Protected Sub ButtonStart_Click(sender As Object, e As System.EventArgs) Handles ButtonStart.Click
        ' チェックをクリア
        GridPager1.ClearCheckCondition()

        GridResult.PageIndex = 0
        GridResult.DataBind()
        FrameResult.Visible = True
    End Sub

    Protected Sub GridResult_RowDataBound(sender As Object, e As GridViewExRowEventArgs) Handles GridResult.RowDataBound
        If e.Row.RowType = DataControlRowType.DataRow Then
            Dim dv As DataRowView = CType(e.Row.DataItem, DataRowView)

            e.RowCellFormat("日時", 10, Text.FormatContents.tbLongDateTime)

            ' ログの区分によって色分け
            Select Case dv("区分")
                Case Message.MessageText("LT_FAILURE"), Message.MessageText("LT_FATAL")
                    e.Row.ForeColor = Drawing.Color.Red
                Case Message.MessageText("LT_WARNNING")
                    e.Row.ForeColor = Drawing.Color.DarkGoldenrod
                Case Else
            End Select
        End If
    End Sub

    Protected Sub ButtonDownload_Click(sender As Object, e As System.EventArgs) Handles ButtonDownload.Click
        ' チェックされているIDの列挙
        ' 以下のコードで、idlistにチェックされているIDがカンマ区切りで格納される。
        Dim idlist As String = ""
        GridPager1.SaveCheckCondition()
        If Not GridPager1.CheckCondition Is Nothing Then
            For Each v As Object In GridPager1.CheckCondition
                idlist &= IIf(idlist = "", "", ",") & v.ToString
            Next
        End If

        ' Gridの内容をExcelファイルでダウンロード
        Base.DownloadExcel(DataResult.Select, GridResult, "systemlog.xls")
    End Sub

    Public Overrides ReadOnly Property RunAuth() As Dbs.Asphalt.Core.SystemLogic.Security.emUserAuth
        Get
            Return SystemLogic.Security.emUserAuth.一般
        End Get
    End Property

    Public Overrides Sub MessageBoxClose(messageboxname As String, result As Dbs.Asphalt.UI.ModuleBase.MessageBoxResult)

    End Sub

End Class
