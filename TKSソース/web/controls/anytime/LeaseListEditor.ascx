﻿<%@ Control Language="VB" AutoEventWireup="false" CodeFile="LeaseListEditor.ascx.vb" Inherits="LeaseListEditor" %>
<%@ Register Assembly="Dbs.Asphalt.UI" Namespace="Dbs.Asphalt.UI" TagPrefix="cc1" %>
<%@ Register Src="~/controls/common/RecordSelector.ascx" TagPrefix="uc1" TagName="RecordSelector" %>
<%@ Register Src="~/controls/common/ReportDataViewer.ascx" TagPrefix="uc1" TagName="ReportDataViewer" %>

<cc1:ItemFrame runat="server" ID="PanelBase" Text="検索条件">

  <div class="leftpane">
      <cc1:ItemPanel runat="server" Text="商品コード">
          <cc1:TextBox runat="server" ID="Text商品コードF" FinderButton="true" AutoPostBack="true"></cc1:TextBox>～<cc1:TextBox runat="server" ID="Text商品コードT" FinderButton="true" AutoPostBack="true"></cc1:TextBox>
      </cc1:ItemPanel>

      <cc1:ItemPanel runat="server" Text="得意先コード">
          <cc1:TextBox runat="server" ID="Text得意先コード" FinderButton="true" MasterTableName="M得意先" MasterGetName="得意先名" MasterKeyName="得意先コード" AutoPostBack="true"></cc1:TextBox><br />
      </cc1:ItemPanel>
      <cc1:ItemPanel runat="server" Text="工事コード">
          <cc1:TextBox runat="server" ID="Text工事コード" FinderButton="true" AutoPostBack="true"></cc1:TextBox><br />
      </cc1:ItemPanel>

      <cc1:TextBox runat="server" ID="Text工事内部コード" Visible="false" ></cc1:TextBox>
  </div>
  <div class="clear"></div>
</cc1:ItemFrame>

<uc1:ReportDataViewer runat="server" ID="ReportDataViewer1" >
</uc1:ReportDataViewer>

<asp:panel runat="server" ID="PanelToolbar">
  <cc1:ToolButton runat="server" ButtonImage="biFinder" Text="検索" ID="ButtonFind" />
  <cc1:ToolButton runat="server" ButtonImage="biExcel" Text="ダウンロード" ID="ButtonDownload" PostbackTrriger="true" />
</asp:panel>
