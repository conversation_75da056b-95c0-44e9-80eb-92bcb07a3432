﻿Imports System.Data
Imports Dbs.Asphalt.UI
Imports Dbs.Asphalt.Core
Imports Dbs.Asphalt.Core.CoreLogic.System
Imports Dbs.Asphalt.Core.Common
Imports System.IO
Imports Dbs.Application.Logic.Logic.Anytime

Partial Class LeaseListEditor
    Inherits ModuleBase

    Private logic As LeaseListManager

    Protected Overrides Sub OnInit(e As System.EventArgs)
        logic = New LeaseListManager(Base.Security)

        logic.商品コードF.Control(Field.ControlTypeContents.ctTextBox) = Text商品コードF
        logic.商品コードT.Control(Field.ControlTypeContents.ctTextBox) = Text商品コードT
        logic.得意先コード.Control(Field.ControlTypeContents.ctTextBox) = Text得意先コード
        logic.工事コード.Control(Field.ControlTypeContents.ctTextBox) = Text工事コード
        logic.工事内部コード.Control(Field.ControlTypeContents.ctTextBox) = Text工事内部コード
        logic.Start()

        'Base.AddPostbackTrrigerControl(ButtonPrint)

        ' Finderの読み込み
        Base.LoadFinder("ShouhinMasterFinder", "CommonFinder", "M商品検索", "商品コード")
        Base.LoadFinder("TokuisakiMasterFinder", "CommonFinder", "M得意先検索", "得意先コード")
        Base.LoadFinder("KoujiMasterFinder", "CommonFinder", "M工事検索", "_取得値")

        MyBase.OnInit(e)
    End Sub

    Protected Overrides Sub OnLoad(e As System.EventArgs)

        If Not IsPostBack Then
            'TextUserID.Text = Base.Security.UserID
            'read_data(True)
        End If

        MyBase.OnLoad(e)
    End Sub

    Private Sub AccountEditor_PreRender(sender As Object, e As EventArgs) Handles Me.PreRender

        'ButtonDelete.Visible = (logic.EditMode.Value = Field.EditModeContents.emUpdateMode)
    End Sub

    Public Overrides Sub FinderClose(findername As String, result As System.Collections.Hashtable, resulttargetid As String)
    End Sub

    Public Overrides Sub FinderOpen()

    End Sub

    Private Sub ReportDataViewer1_ItemDataBound(sender As Object, e As DataGridItemEventArgs) Handles ReportDataViewer1.ItemDataBound

        ' 書式の設定は以下でよいのか？
        If e.Item.ItemType <> ListItemType.Header Then
            e.Item.Cells(6).HorizontalAlign = HorizontalAlign.Right
            e.Item.Cells(6).Text = e.Item.Cells(6).Text.TextFormat(15, Text.FormatContents.tbCurrency)
        End If
    End Sub

    Private Sub Text商品コード_FinderButtonClicked(sender As Object, e As ImageClickEventArgs) Handles Text商品コードF.FinderButtonClicked, Text商品コードT.FinderButtonClicked
        ' 商品検索を開く
        Base.OpenFinder("ShouhinMasterFinder", , sender.ID)
    End Sub

    Private Sub Text得意先コード_FinderButtonClicked(sender As Object, e As ImageClickEventArgs) Handles Text得意先コード.FinderButtonClicked
        ' 得意先検索を開く
        Base.OpenFinder("TokuisakiMasterFinder", , sender.ID)
    End Sub

    Private Sub Text工事コード_FinderButtonClicked(sender As Object, e As ImageClickEventArgs) Handles Text工事コード.FinderButtonClicked
        ' 工事検索を開く
        Dim param As New Hashtable
        param("固定条件") = "得意先コード = '" & Text得意先コード.Text & "'"

        Base.OpenFinder("KoujiMasterFinder", param, sender.ID)
    End Sub

    Private Sub TextKoujiCode_TextChanged(sender As Object, e As EventArgs) Handles Text工事コード.TextChanged, Text得意先コード.TextChanged
        Dim str工事名 As String = ""
        Dim str内部CD As String = ""

        Dim strWHERE As String = ""
        Dim strAND As String = ""
        strWHERE += strAND & "削除区分     = '0'" : strAND = " AND "
        strWHERE += strAND & "得意先コード = '" & Text得意先コード.DbText & "'" : strAND = " AND "

        str工事名 = logic.MasterName.AnyTableName("M工事", "工事コード", "工事名", Text工事コード.Text, strWHERE)
        str内部CD = logic.MasterName.AnyTableName("M工事", "工事コード", "工事内部コード", Text工事コード.Text, strWHERE)

        Text工事コード.AddText = str工事名
        Text工事内部コード.Text = str内部CD
    End Sub

    Private Sub Text商品コードF_TextChanged(sender As Object, e As EventArgs) Handles Text商品コードF.TextChanged
        Text商品コードT.Text = Text商品コードF.Text
    End Sub

    Public Overrides ReadOnly Property PageTitle() As String
        Get
            Return logic.Title
        End Get
    End Property

    Protected Sub ButtonFind_Click(sender As Object, e As System.EventArgs) Handles ButtonFind.Click

        Me.ReportDataViewer1.DataSource = logic.GetDataView
    End Sub

    Protected Sub ButtonDownload_Click(sender As Object, e As System.Web.UI.ImageClickEventArgs) Handles ButtonDownload.Click
        Base.DownloadExcel(logic.GetDataView,, logic.FileName & ".xlsx")
    End Sub

    Public Overrides ReadOnly Property RunAuth() As Dbs.Asphalt.Core.SystemLogic.Security.emUserAuth
        Get
            Return SystemLogic.Security.emUserAuth.一般
        End Get
    End Property

    Public Overrides Sub MessageBoxClose(messageboxname As String, result As Dbs.Asphalt.UI.ModuleBase.MessageBoxResult)

    End Sub

End Class
