﻿<%@ Control Language="VB" AutoEventWireup="false" CodeFile="KoujiListEditor.ascx.vb" Inherits="KoujiListEditor" %>
<%@ Register Assembly="Dbs.Asphalt.UI" Namespace="Dbs.Asphalt.UI" TagPrefix="cc1" %>
<%@ Register Src="~/controls/common/RecordSelector.ascx" TagPrefix="uc1" TagName="RecordSelector" %>
<%@ Register Src="~/controls/common/ReportDataViewer.ascx" TagPrefix="uc1" TagName="ReportDataViewer" %>
<%@ Register Src="~/controls/common/GridPager.ascx" TagPrefix="uc1" TagName="GridPager" %>

<cc1:ItemFrame runat="server" ID="PanelBase" Text="検索条件">
  <div class="leftpane">
      <cc1:ItemPanel runat="server" Text="得意先コード">
        <cc1:TextBox runat="server" ID="Text得意先コード" FinderButton="true" MasterTableName="M得意先" MasterGetName="得意先名" MasterKeyName="得意先コード" AutoPostBack="true"></cc1:TextBox>
      </cc1:ItemPanel>
      <cc1:ItemPanel runat="server" Text="工事コード">
        <cc1:TextBox runat="server" ID="Text工事コードF" FinderButton="true" ></cc1:TextBox>～<cc1:TextBox runat="server" ID="Text工事コードT" FinderButton="true" ></cc1:TextBox>
      </cc1:ItemPanel>

      <cc1:ItemPanel runat="server" Text="範囲">
        <cc1:TextBox runat="server" ID="Text対象期間F"></cc1:TextBox>～<cc1:TextBox runat="server" ID="Text対象期間T"></cc1:TextBox>
      </cc1:ItemPanel>
  </div>
  <div class="clear"></div>
</cc1:ItemFrame>

  <asp:Panel runat="server" ID="FrameResult" Visible="false">
    <ContentTemplate>
      <uc1:GridPager ID="GridPager1" runat="server" TargetGridViewControlID="GridResult" />
      <cc1:GridViewEx runat="server" ID="GridResult" DataSourceID="DataResult">
        <Columns>
          <asp:BoundField DataField="得意先名" HeaderText="得意先名" ReadOnly="True" SortExpression="得意先名" HeaderStyle-Wrap="false" />
          <asp:BoundField DataField="工事名" HeaderText="工事名" ReadOnly="True" SortExpression="工事名" HeaderStyle-Wrap="false" />
          <asp:BoundField DataField="日付" HeaderText="日付" ReadOnly="True" SortExpression="日付" HeaderStyle-Wrap="false" />
          <asp:BoundField DataField="商品名" HeaderText="商品名" ReadOnly="True" SortExpression="商品名" HeaderStyle-Wrap="false" />
          <asp:BoundField DataField="数量" HeaderText="数量" ReadOnly="True" SortExpression="数量" HeaderStyle-Wrap="false" />
          <asp:BoundField DataField="単位区分名" HeaderText="単位" ReadOnly="True" SortExpression="単位区分名" HeaderStyle-Wrap="false" />
          <asp:BoundField DataField="単価" HeaderText="単価" ReadOnly="True" SortExpression="単価" HeaderStyle-Wrap="false" />
          <asp:BoundField DataField="基本料金" HeaderText="基本料金" ReadOnly="True" SortExpression="基本料金" HeaderStyle-Wrap="false" />
          <asp:BoundField DataField="売上金額" HeaderText="売上金額" ReadOnly="True" SortExpression="売上金額" HeaderStyle-Wrap="false" />
          <asp:BoundField DataField="入金金額" HeaderText="入金金額" ReadOnly="True" SortExpression="入金金額" HeaderStyle-Wrap="false" />
          <asp:BoundField DataField="請求残高" HeaderText="請求残高" ReadOnly="True" SortExpression="請求残高" HeaderStyle-Wrap="false" />
          <asp:BoundField DataField="開始日付" HeaderText="開始日付" ReadOnly="True" SortExpression="開始日付" HeaderStyle-Wrap="false" />
          <asp:BoundField DataField="終了日付" HeaderText="終了日付" ReadOnly="True" SortExpression="終了日付" HeaderStyle-Wrap="false" />
          <asp:BoundField DataField="備考" HeaderText="メモ" ReadOnly="True" SortExpression="備考" HeaderStyle-Wrap="false" />
          <asp:CommandField ButtonType="Button" ShowSelectButton="True" SelectText="メモ＆完了" HeaderStyle-Wrap="false">
            <ItemStyle Width="1px" HorizontalAlign="Center" />
            <ControlStyle CssClass="button_small" />
          </asp:CommandField>

          <asp:BoundField DataField="備考" HeaderText="備考" ReadOnly="True" SortExpression="備考" HeaderStyle-Wrap="false" />
          <asp:BoundField DataField="伝票区分" HeaderText="伝票区分" ReadOnly="True" SortExpression="伝票区分" HeaderStyle-Wrap="false" />
          <asp:BoundField DataField="伝票番号" HeaderText="伝票番号" ReadOnly="True" SortExpression="伝票番号" HeaderStyle-Wrap="false" />
          <asp:BoundField DataField="行番号" HeaderText="行番号" ReadOnly="True" SortExpression="行番号" HeaderStyle-Wrap="false" />
          <asp:BoundField DataField="工事内部コード" HeaderText="工事内部コード" ReadOnly="True" SortExpression="工事内部コード" HeaderStyle-Wrap="false" />
        </Columns>
      </cc1:GridViewEx>
      <uc1:GridPager ID="GridPager2" runat="server" TargetGridViewControlID="GridResult" />
      <asp:ObjectDataSource runat="server" ID="DataResult" SelectMethod="GetData" TypeName="Dbs.Application.Logic.Database.DbCalcTableAdapters.F1工事台帳TableAdapter">
        <SelectParameters>
            <asp:ControlParameter ControlID="Text得意先コード" Name="得意先コード" PropertyName="Text" Type="String" ConvertEmptyStringToNull="False" />
            <asp:ControlParameter ControlID="Text工事コードF" Name="工事コードF" PropertyName="Text" Type="String" ConvertEmptyStringToNull="False" />
            <asp:ControlParameter ControlID="Text工事コードT" Name="工事コードT" PropertyName="Text" Type="String" ConvertEmptyStringToNull="False" />
            <asp:ControlParameter ControlID="Text対象期間F" Name="対象期間F" PropertyName="Text" Type="String" ConvertEmptyStringToNull="False" />
            <asp:ControlParameter ControlID="Text対象期間T" Name="対象期間T" PropertyName="Text" Type="String" ConvertEmptyStringToNull="False" />
        </SelectParameters>
      </asp:ObjectDataSource>
    </ContentTemplate>
  </asp:Panel>

<cc1:ItemFrame runat="server" ID="FrameItemViewer" Visible="false">
  <div class="datatitle">
    <asp:Button runat="server" ID="ButtonBack" Text="　◀戻る　" CssClass="buttonr" ToolTip="一覧に戻る" />
  </div>
  <div class="leftpane">
      <cc1:ItemPanel runat="server" Text="日付">
          <cc1:TextBox runat="server" ID="Text日付" ViewOnly="true"></cc1:TextBox><br />
      </cc1:ItemPanel>
      <cc1:ItemPanel runat="server" Text="得意先">
          <cc1:TextBox runat="server" ID="Text得意先名" ViewOnly="true"></cc1:TextBox><br />
      </cc1:ItemPanel>
      <cc1:ItemPanel runat="server" Text="工事">
          <cc1:TextBox runat="server" ID="Text工事名" ViewOnly="true"></cc1:TextBox><br />
      </cc1:ItemPanel>

      <hr />

      <cc1:ItemPanel runat="server" Text="商品">
          <cc1:TextBox runat="server" ID="Text商品名" ViewOnly="true"></cc1:TextBox><br />
      </cc1:ItemPanel>
      <cc1:ItemPanel runat="server" Text="金額">
          <cc1:TextBox runat="server" ID="Text金額" ViewOnly="true"></cc1:TextBox><br />
      </cc1:ItemPanel>

      <hr />

      <cc1:ItemPanel runat="server" Text="メモ">
          <cc1:TextBox runat="server" ID="Text備考" ></cc1:TextBox><br />
      </cc1:ItemPanel>

      <hr />
    
      <cc1:ItemPanel runat="server" Text="完了区分">
        <cc1:DropDownList runat="server" ID="Drop完了区分"></cc1:DropDownList><br />
        <span class="guide">※完了にすると売上入力と工事台帳に表示されなくなります。</span>
      </cc1:ItemPanel>

      <cc1:TextBox runat="server" ID="Text伝票区分" Visible="false" ></cc1:TextBox>
      <cc1:TextBox runat="server" ID="Text伝票番号" Visible="false" ></cc1:TextBox>
      <cc1:TextBox runat="server" ID="Text行番号" Visible="false" ></cc1:TextBox>
      <cc1:TextBox runat="server" ID="Text工事内部コード" Visible="false" ></cc1:TextBox>

      <hr />
      <div class="inframetoolbar">
        <asp:Button runat="server" ID="ButtonRegist" Text="　　登録　　" CssClass="buttonb" />
      </div>

  </div>
  <div class="clear"></div>
</cc1:ItemFrame>

<asp:panel runat="server" ID="PanelToolbar">
  <cc1:ToolButton runat="server" ButtonImage="biFinder" Text="検索" ID="ButtonFind" />
  <cc1:ToolButton runat="server" ButtonImage="biExcel" Text="ダウンロード" ID="ButtonDownload" PostbackTrriger="true" />
</asp:panel>
