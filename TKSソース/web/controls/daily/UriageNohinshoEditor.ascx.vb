﻿Imports System.Data
Imports Dbs.Asphalt.UI
Imports Dbs.Asphalt.Core
Imports Dbs.Asphalt.Core.CoreLogic.System
Imports Dbs.Asphalt.Core.Common
Imports System.IO
Imports Dbs.Application.Logic.Logic.daily

Partial Class UriageNohinshoEditor
    Inherits ModuleBase

    Private logic As UriageNohinshoManager

    Protected Overrides Sub OnInit(e As System.EventArgs)
        logic = New UriageNohinshoManager(Base.Security)

        logic.得意先コード.Control(Field.ControlTypeContents.ctTextBox) = Text得意先コード
        logic.工事コード.Control(Field.ControlTypeContents.ctTextBox) = Text工事コード
        logic.工事内部コード.Control(Field.ControlTypeContents.ctTextBox) = Text工事内部コード
        logic.搬入日付F.Control(Field.ControlTypeContents.ctTextBox) = Text搬入日付F
        logic.搬入日付T.Control(Field.ControlTypeContents.ctTextBox) = Text搬入日付T
        logic.売上番号.Control(Field.ControlTypeContents.ctTextBox) = Text売上番号
        logic.Start()

        Base.AddPostbackTrrigerControl(ButtonPrint)

        ' Finderの読み込み
        Base.LoadFinder("TokuisakiMasterFinder", "CommonFinder", "M得意先検索", "得意先コード")
        Base.LoadFinder("UriageDenpyoFinder", "CommonFinder", "T売上検索", "売上番号")
        Base.LoadFinder("KoujiMasterFinder", "CommonFinder", "M工事検索", "_取得値")

        MyBase.OnInit(e)
    End Sub

    Protected Overrides Sub OnLoad(e As System.EventArgs)

        If Not IsPostBack Then
            'TextUserID.Text = Base.Security.UserID
            'read_data(True)
        End If

        MyBase.OnLoad(e)
    End Sub

    Private Sub AccountEditor_PreRender(sender As Object, e As EventArgs) Handles Me.PreRender

        'ButtonDelete.Visible = (logic.EditMode.Value = Field.EditModeContents.emUpdateMode)
    End Sub

    Public Overrides Sub FinderClose(findername As String, result As System.Collections.Hashtable, resulttargetid As String)
    End Sub

    Public Overrides Sub FinderOpen()

    End Sub

    Private Sub Text得意先コード_FinderButtonClicked(sender As Object, e As ImageClickEventArgs) Handles Text得意先コード.FinderButtonClicked
        ' 得意先検索を開く
        Base.OpenFinder("TokuisakiMasterFinder", , sender.ID)
    End Sub

    Private Sub Text工事コード_FinderButtonClicked(sender As Object, e As ImageClickEventArgs) Handles Text工事コード.FinderButtonClicked
        ' 工事検索を開く
        Dim param As New Hashtable
        param("固定条件") = "得意先コード = '" & Text得意先コード.Text & "'"

        Base.OpenFinder("KoujiMasterFinder", param, sender.ID)
    End Sub

    Private Sub Text売上番号_FinderButtonClicked(sender As Object, e As ImageClickEventArgs) Handles Text売上番号.FinderButtonClicked
        Dim param As New Hashtable
        Base.OpenFinder("UriageDenpyoFinder", param, sender.ID)
    End Sub

    Private Sub TextKoujiCode_TextChanged(sender As Object, e As EventArgs) Handles Text工事コード.TextChanged, Text得意先コード.TextChanged
        Dim str工事名 As String = ""
        Dim str内部CD As String = ""

        Dim strWHERE As String = ""
        Dim strAND As String = ""
        strWHERE += strAND & "削除区分     = '0'" : strAND = " AND "
        strWHERE += strAND & "得意先コード = '" & Text得意先コード.DbText & "'" : strAND = " AND "

        str工事名 = logic.MasterName.AnyTableName("M工事", "工事コード", "工事名", Text工事コード.Text, strWHERE)
        str内部CD = logic.MasterName.AnyTableName("M工事", "工事コード", "工事内部コード", Text工事コード.Text, strWHERE)

        Text工事コード.AddText = str工事名
        Text工事内部コード.Text = str内部CD
    End Sub

    Public Overrides ReadOnly Property PageTitle() As String
        Get
            Return logic.Title
        End Get
    End Property

    Protected Sub ButtonFind_Click(sender As Object, e As System.EventArgs) Handles ButtonFind.Click
        Dim param As New Hashtable

        ' チェックをクリア
        GridPager1.ClearCheckCondition()
        GridPager1.AllCheckCondition(True)

        GridResult.PageIndex = 0
        GridResult.DataBind()
        FrameResult.Visible = True
    End Sub

    Protected Sub ButtonPrint_Click(sender As Object, e As ImageClickEventArgs) Handles ButtonPrint.Click

        ' 以下のコードで、idlistにチェックされているｷｰがカンマ区切りで格納される。
        Dim idlist As String = ""
        GridPager1.SaveCheckCondition()
        If Not GridPager1.CheckCondition Is Nothing Then
            For Each v As Object In GridPager1.CheckCondition
                idlist &= IIf(idlist = "", "", ",") & v.ToString
            Next
        End If

        logic.売上リスト.Value = idlist

        ' チェックをクリア
        GridPager1.ClearCheckCondition()

        If logic.PrintMainData() Then
            Base.DownloadPDF(logic.Report)
        Else
            Base.ErrorMessage = logic.LastError
        End If
    End Sub

    Public Overrides ReadOnly Property RunAuth() As Dbs.Asphalt.Core.SystemLogic.Security.emUserAuth
        Get
            Return SystemLogic.Security.emUserAuth.一般
        End Get
    End Property

    Public Overrides Sub MessageBoxClose(messageboxname As String, result As Dbs.Asphalt.UI.ModuleBase.MessageBoxResult)

    End Sub
End Class
