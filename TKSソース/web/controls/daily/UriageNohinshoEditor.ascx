﻿<%@ Control Language="VB" AutoEventWireup="false" CodeFile="UriageNohinshoEditor.ascx.vb" Inherits="UriageNohinshoEditor" %>
<%@ Register Assembly="Dbs.Asphalt.UI" Namespace="Dbs.Asphalt.UI" TagPrefix="cc1" %>
<%@ Register Src="~/controls/common/RecordSelector.ascx" TagPrefix="uc1" TagName="RecordSelector" %>
<%@ Register Src="~/controls/common/ReportDataViewer.ascx" TagPrefix="uc1" TagName="ReportDataViewer" %>
<%@ Register Src="~/controls/common/GridPager.ascx" TagPrefix="uc1" TagName="GridPager" %>

<cc1:ItemFrame runat="server" ID="PanelBase" Text="検索条件">

  <div class="leftpane">
      <cc1:ItemPanel runat="server" Text="得意先コード">
          <cc1:TextBox runat="server" ID="Text得意先コード" FinderButton="true" MasterTableName="M得意先" MasterGetName="得意先名" MasterKeyName="得意先コード" AutoPostBack="true"></cc1:TextBox><br />
      </cc1:ItemPanel>
      <cc1:ItemPanel runat="server" Text="工事コード">
          <cc1:TextBox runat="server" ID="Text工事コード" FinderButton="true" AutoPostBack="true"></cc1:TextBox><br />
      </cc1:ItemPanel>
  </div>

  <div class="leftpane">
      <cc1:ItemPanel runat="server" Text="搬入日付">
        <cc1:TextBox runat="server" ID="Text搬入日付F"></cc1:TextBox>～<cc1:TextBox runat="server" ID="Text搬入日付T"></cc1:TextBox>
      </cc1:ItemPanel>

        <cc1:ItemPanel runat="server" Text="売上番号">
          <cc1:TextBox runat="server" ID="Text売上番号" FinderButton="true" ></cc1:TextBox><br />
        </cc1:ItemPanel>

      <cc1:TextBox runat="server" ID="Text工事内部コード" Visible="false" ></cc1:TextBox>
  </div>

  <div class="clear"></div>
</cc1:ItemFrame>

  <asp:UpdatePanel runat="server" ID="FrameResult" Visible="false">
    <ContentTemplate>
      <uc1:GridPager ID="GridPager1" runat="server" TargetGridViewControlID="GridResult" CheckBoxHeaderText="選択" CheckBoxPrimaryKeyHeaderText="売上番号" />
      <cc1:GridViewEx runat="server" ID="GridResult" DataSourceID="DataResult">
        <Columns>
          <asp:TemplateField HeaderText="選択" ItemStyle-HorizontalAlign="Center" ItemStyle-Width="30">
            <ItemTemplate><cc1:CheckBox runat="server" ID="CheckBox" /></ItemTemplate>
          </asp:TemplateField>
          <asp:BoundField DataField="売上番号" HeaderText="売上番号" ReadOnly="True" SortExpression="売上番号" HeaderStyle-Wrap="false" />
          <asp:BoundField DataField="搬入日付" HeaderText="搬入日付" ReadOnly="True" SortExpression="搬入日付" HeaderStyle-Wrap="false" />
          <asp:BoundField DataField="伝票区分名" HeaderText="伝票区分名" ReadOnly="True" SortExpression="伝票区分名" HeaderStyle-Wrap="false" />
          <asp:BoundField DataField="得意先コード" HeaderText="得意先コード" ReadOnly="True" SortExpression="得意先コード" HeaderStyle-Wrap="false" />
          <asp:BoundField DataField="工事コード" HeaderText="工事コード" ReadOnly="True" SortExpression="工事コード" HeaderStyle-Wrap="false" />
          <asp:BoundField DataField="得意先名" HeaderText="得意先名" ReadOnly="True" SortExpression="得意先名" HeaderStyle-Wrap="false" />
          <asp:BoundField DataField="工事名" HeaderText="工事名" ReadOnly="True" SortExpression="工事名" HeaderStyle-Wrap="false" />
          <asp:BoundField DataField="摘要1" HeaderText="摘要1" ReadOnly="True" SortExpression="摘要1" HeaderStyle-Wrap="false" />
          <asp:BoundField DataField="摘要2" HeaderText="摘要2" ReadOnly="True" SortExpression="摘要2" HeaderStyle-Wrap="false" />
          <%--<asp:BoundField DataField="登録日時" HeaderText="登録日時" ReadOnly="True" SortExpression="登録日時" HeaderStyle-Wrap="false" visible="true" ItemStyle-Width="0"/>--%>
        </Columns>
      </cc1:GridViewEx>
      <uc1:GridPager ID="GridPager2" runat="server" TargetGridViewControlID="GridResult" />
      <asp:ObjectDataSource runat="server" ID="DataResult" SelectMethod="GetData" TypeName="Dbs.Application.Logic.Database.DbCalcTableAdapters.F売上伝票検索TableAdapter">
        <SelectParameters>
            <asp:ControlParameter ControlID="Text得意先コード" Name="得意先コード" PropertyName="Text" Type="String" ConvertEmptyStringToNull="False" />
            <asp:ControlParameter ControlID="Text工事内部コード" Name="工事内部コード" PropertyName="Text" Type="String" ConvertEmptyStringToNull="False" />
            <asp:ControlParameter ControlID="Text搬入日付F" Name="搬入日付F" PropertyName="Text" Type="String" ConvertEmptyStringToNull="False" />
            <asp:ControlParameter ControlID="Text搬入日付T" Name="搬入日付T" PropertyName="Text" Type="String" ConvertEmptyStringToNull="False" />
            <asp:ControlParameter ControlID="Text売上番号" Name="売上番号" PropertyName="Text" Type="String" ConvertEmptyStringToNull="False" />
        </SelectParameters>
      </asp:ObjectDataSource>
    </ContentTemplate>
  </asp:UpdatePanel>

<asp:panel runat="server" ID="PanelToolbar">
  <cc1:ToolButton runat="server" ButtonImage="biFinder" Text="検索" ID="ButtonFind" />
  <cc1:ToolButton runat="server" ButtonImage="biPrint" Text="印刷" ID="ButtonPrint" />
</asp:panel>
