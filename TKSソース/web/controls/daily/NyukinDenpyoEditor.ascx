﻿<%@ Control Language="VB" AutoEventWireup="false" CodeFile="NyukinDenpyoEditor.ascx.vb" Inherits="NyukinDenpyoEditor" %>
<%@ Register Assembly="Dbs.Asphalt.UI" Namespace="Dbs.Asphalt.UI" TagPrefix="cc1" %>
<%@ Register Src="~/controls/common/RecordSelector.ascx" TagPrefix="uc1" TagName="RecordSelector" %>
<%@ Register Src="~/controls/common/ReportDataViewer.ascx" TagPrefix="uc1" TagName="ReportDataViewer" %>

<cc1:ItemFrame runat="server" ID="PanelBase" Text="基本情報">
  <div class="leftpane">
    <cc1:ItemPanel runat="server" Text="入金番号">
      <cc1:TextBox runat="server" ID="TextNo" AutoPostBack="true" ></cc1:TextBox><br />
      <span class="guide">入金番号を空白で登録すると、自動的にコードを割り当てて追加登録されます。</span>
    </cc1:ItemPanel>

    <cc1:ItemPanel runat="server" Text="登録">
      <cc1:TextBox runat="server" ID="TextRegDate" ViewOnly="true"></cc1:TextBox>&nbsp;
      <cc1:TextBox runat="server" ID="TextRegUserID" ViewOnly="true"></cc1:TextBox>
    </cc1:ItemPanel>
    <cc1:ItemPanel runat="server" Text="更新">
      <cc1:TextBox runat="server" ID="TextUpdDate" ViewOnly="true"></cc1:TextBox>&nbsp;
      <cc1:TextBox runat="server" ID="TextUpdUserID" ViewOnly="true"></cc1:TextBox>
    </cc1:ItemPanel>
  </div>
  <div class="rightpane">
    <cc1:EditMode runat="server" ID="EditMode"></cc1:EditMode><br />
    <uc1:RecordSelector runat="server" ID="RecordSelector" DataKeyControlID="TextNo" DataName="T入金HD" KeyIsNumeric="false" KeyName="入金番号" AllowClearCheckBox="true" />
  </div>
  <div class="clear"></div>

  <hr />

  <cc1:ItemPanel runat="server" Text="入金日付">
    <cc1:TextBox runat="server" ID="TextDate"></cc1:TextBox>
  </cc1:ItemPanel>

  <cc1:ItemPanel runat="server" Text="得意先コード">
      <cc1:TextBox runat="server" ID="TextTokuisakiCode" FinderButton="true" MasterTableName="M得意先" MasterGetName="得意先名" MasterKeyName="得意先コード" AutoPostBack="true"></cc1:TextBox><br />
  </cc1:ItemPanel>
  <cc1:ItemPanel runat="server" Text="工事コード">
      <cc1:TextBox runat="server" ID="TextKoujiCode" FinderButton="true" AutoPostBack="true"></cc1:TextBox><cc1:TextBox runat="server" ID="TextKoujiNaibuCode" Visible="false" ></cc1:TextBox><br />
  </cc1:ItemPanel>

  <cc1:ItemPanel runat="server" Text="請求年月">
    <cc1:TextBox runat="server" ID="TextBill"></cc1:TextBox>
  </cc1:ItemPanel>

  <cc1:TextBox runat="server" ID="TextSeikyuNo" Visible="false" ></cc1:TextBox>

<%--  <div class="inframetoolbar">
      <asp:Button runat="server" ID="ButtonStart" Text="　検索　" CssClass="button" />
  </div>--%>
</cc1:ItemFrame>


<div class="leftpane">
    <cc1:GridViewEx runat="server" ID="GridDetail" AllowPaging="false" AllowSorting="false" RowStyle-Wrap="false" RowStyle-VerticalAlign="Top" Width="" AllowDeleteButton="true">
    <Columns>
        <asp:TemplateField HeaderText="入金区分">
        <ItemTemplate><cc1:DropDownList runat="server" ID="DropNyukinKubun"></cc1:DropDownList></ItemTemplate>
        </asp:TemplateField>
        <asp:TemplateField HeaderText="金額">
        <ItemTemplate><cc1:TextBox runat="server" ID="TextKingaku"></cc1:TextBox></ItemTemplate>
        </asp:TemplateField>
        <asp:TemplateField HeaderText="備考" Visible="false">
        <ItemTemplate><cc1:TextBox runat="server" ID="TextBikou" Visible="false"></cc1:TextBox></ItemTemplate>
        </asp:TemplateField>
    </Columns>
    </cc1:GridViewEx>
</div>

<div class="leftmargin">
    <h2>残高参照</h2>
    <uc1:ReportDataViewer runat="server" ID="ReportDataViewer1" >
    </uc1:ReportDataViewer>
</div>


<asp:panel runat="server" ID="PanelToolbar">
  <cc1:ToolButton runat="server" ButtonImage="biSave" Text="保存" ID="ButtonRegist" PostbackTrriger="true" PostbackTrrigerFreeze="true" />
  <cc1:ToolButton runat="server" ButtonImage="biDelete" Text="削除" ID="ButtonDelete" PostbackTrriger="true" PostbackTrrigerFreeze="true" />
  <cc1:ToolButton runat="server" ButtonImage="biNew" Text="新規追加" ID="ButtonNew" />
  <cc1:ToolButton runat="server" ButtonImage="biFinder" Text="検索" ID="ButtonFind" />
</asp:panel>
