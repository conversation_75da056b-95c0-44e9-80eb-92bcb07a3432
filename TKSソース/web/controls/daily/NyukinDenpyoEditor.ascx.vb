﻿Imports System.Data
Imports Dbs.Asphalt.UI
Imports Dbs.Asphalt.Core
Imports Dbs.Application.Logic.Logic.daily
Imports Dbs.Asphalt.Core.Common

Partial Class NyukinDenpyoEditor
    Inherits ModuleBase

    Private logic As NyukinDenpyoManager

    Protected Overrides Sub OnInit(e As System.EventArgs)
        logic = New NyukinDenpyoManager(Base.Security)

        logic.EditMode.Control(Field.ControlTypeContents.ctEditMode) = EditMode
        logic.入金番号.Control(Field.ControlTypeContents.ctTextBox) = TextNo
        logic.入金日付.Control(Field.ControlTypeContents.ctTextBox) = TextDate
        logic.請求年月.Control(Field.ControlTypeContents.ctTextBox) = TextBill
        logic.得意先コード.Control(Field.ControlTypeContents.ctTextBox) = TextTokuisakiCode
        logic.工事コード.Control(Field.ControlTypeContents.ctTextBox) = TextKoujiCode
        logic.工事内部コード.Control(Field.ControlTypeContents.ctTextBox) = TextKoujiNaibuCode
        logic.請求番号.Control(Field.ControlTypeContents.ctTextBox) = TextSeikyuNo

        logic.登録日時.Control(Field.ControlTypeContents.ctTextBox) = TextRegDate
        logic.登録者ID.Control(Field.ControlTypeContents.ctTextBox) = TextRegUserID
        logic.更新日時.Control(Field.ControlTypeContents.ctTextBox) = TextUpdDate
        logic.更新者ID.Control(Field.ControlTypeContents.ctTextBox) = TextUpdUserID

        logic.Start()

        GridDetail.DefaultRowsCount = logic.List.Length
        For i As Integer = 0 To logic.List.Length - 1
            logic.List(i).入金区分.Control(Field.ControlTypeContents.ctDropDownList) = GridDetail.RowsCellFirstControl(i, "入金区分")
            logic.List(i).入金金額.Control(Field.ControlTypeContents.ctTextBox) = GridDetail.RowsCellFirstControl(i, "金額")
            logic.List(i).備考.Control(Field.ControlTypeContents.ctTextBox) = GridDetail.RowsCellFirstControl(i, "備考")

            GridDetail.RowsCellFirstControl(i, "入金区分").ID &= "_" & (i + 1)
            GridDetail.RowsCellFirstControl(i, "金額").ID &= "_" & (i + 1)
            GridDetail.RowsCellFirstControl(i, "備考").ID &= "_" & (i + 1)
        Next

        ' Finderの読み込み
        Base.LoadFinder("TokuisakiMasterFinder", "CommonFinder", "M得意先検索", "得意先コード")
        Base.LoadFinder("NyukinDenpyoFinder", "CommonFinder", "T入金検索", "入金番号")
        Base.LoadFinder("KoujiMasterFinder", "CommonFinder", "M工事検索", "_取得値")

        MyBase.OnInit(e)
    End Sub

    Protected Overrides Sub OnLoad(e As System.EventArgs)

        If Not IsPostBack Then
            read_data(True)
        End If

        MyBase.OnLoad(e)
    End Sub

    Private Sub AccountEditor_PreRender(sender As Object, e As EventArgs) Handles Me.PreRender

        ButtonDelete.Visible = (logic.EditMode.Value = Field.EditModeContents.emUpdateMode)

    End Sub

    Public Overrides Sub FinderClose(findername As String, result As System.Collections.Hashtable, resulttargetid As String)

    End Sub

    Public Overrides Sub FinderOpen()

    End Sub

    Private Sub ReportDataViewer1_ItemDataBound(sender As Object, e As DataGridItemEventArgs) Handles ReportDataViewer1.ItemDataBound

        ' 書式の設定は以下でよいのか？
        If e.Item.ItemType <> ListItemType.Header Then
            e.Item.Cells(3).HorizontalAlign = HorizontalAlign.Right
            e.Item.Cells(3).Text = e.Item.Cells(3).Text.TextFormat(15, Text.FormatContents.tbCurrency)
        End If
    End Sub

    Public Overrides Sub MessageBoxClose(messageboxname As String, result As Dbs.Asphalt.UI.ModuleBase.MessageBoxResult)

    End Sub

    Public Overrides ReadOnly Property PageTitle As String
        Get
            Return logic.Title
        End Get
    End Property

    Public Overrides ReadOnly Property RunAuth As Dbs.Asphalt.Core.SystemLogic.Security.emUserAuth
        Get
            Return SystemLogic.Security.emUserAuth.一般
        End Get
    End Property

    Private Sub read_data(read_after_clear As Boolean)
        If Not logic.ReadMainData(True) Then
            Base.ErrorMessage = logic.LastError
        End If

        ' 残高明細
        Me.ReportDataViewer1.DataSource = logic.GetDataView
    End Sub

    Protected Sub ButtonRegist_Click(sender As Object, e As System.Web.UI.ImageClickEventArgs) Handles ButtonRegist.Click
        If Not logic.WriteMainData() Then
            Base.ErrorMessage = logic.LastError
            Return
        End If

        Base.Message = logic.LastMessage

        read_data(RecordSelector.ClearItems)
    End Sub

    Protected Sub TextNo_TextChanged(sender As Object, e As System.EventArgs) Handles TextNo.TextChanged
        read_data(True)
    End Sub

    Private Sub TextKoujiCode_TextChanged(sender As Object, e As EventArgs) Handles TextKoujiCode.TextChanged, TextTokuisakiCode.TextChanged
        Dim str工事名 As String = ""
        str工事名 = logic.MasterName.AnyTableName("M工事", "工事コード", "工事名", TextKoujiCode.Text, "得意先コード = '" & TextTokuisakiCode.DbText & "'")
        TextKoujiCode.AddText = str工事名

        Dim str工事内部コード As String = ""
        str工事内部コード = logic.MasterName.AnyTableName("M工事", "工事コード", "工事内部コード", TextKoujiCode.Text, "得意先コード = '" & TextTokuisakiCode.DbText & "'")
        TextKoujiNaibuCode.Text = str工事内部コード

        ' 残高明細
        Me.ReportDataViewer1.DataSource = logic.GetDataView
    End Sub

    Private Sub TextTokuisakiCode_FinderButtonClicked(sender As Object, e As ImageClickEventArgs) Handles TextTokuisakiCode.FinderButtonClicked
        ' 得意先検索を開く
        Base.OpenFinder("TokuisakiMasterFinder", , sender.ID)
    End Sub
    Private Sub TextKoujiCode_FinderButtonClicked(sender As Object, e As ImageClickEventArgs) Handles TextKoujiCode.FinderButtonClicked
        ' 工事検索を開く
        Base.OpenFinder("KoujiMasterFinder", , sender.ID)
    End Sub

    Protected Sub ButtonDelete_Click(sender As Object, e As ImageClickEventArgs) Handles ButtonDelete.Click
        If Not logic.DeleteMainData Then
            Base.ErrorMessage = logic.LastError
            Return
        End If

        Base.Message = logic.LastMessage

        TextNo.Text = ""
        read_data(True)
    End Sub

    Protected Sub ButtonNew_Click(sender As Object, e As ImageClickEventArgs) Handles ButtonNew.Click
        TextNo.Text = ""
        read_data(RecordSelector.ClearItems)
    End Sub

    Protected Sub ButtonFind_Click(sender As Object, e As ImageClickEventArgs) Handles ButtonFind.Click
        Dim param As New Hashtable
        'param("起動") = "直接"

        Base.OpenFinder("NyukinDenpyoFinder", param, "TextNo")
    End Sub
End Class
