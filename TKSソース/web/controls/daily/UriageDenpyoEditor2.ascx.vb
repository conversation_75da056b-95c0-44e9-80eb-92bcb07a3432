﻿Imports System.Data
Imports Dbs.Asphalt.UI
Imports Dbs.Asphalt.Core
Imports Dbs.Application.Logic.Logic.daily
Imports Dbs.Asphalt.Core.Common

Partial Class UriageDenpyoEditor2
    Inherits ModuleBase

    Private logic As UriageDenpyoManager

    Protected Overrides Sub OnInit(e As System.EventArgs)
        logic = New UriageDenpyoManager(Base.Security)

        logic.EditMode.Control(Field.ControlTypeContents.ctEditMode) = EditMode
        logic.売上番号.Control(Field.ControlTypeContents.ctTextBox) = TextNo
        logic.搬入日付.Control(Field.ControlTypeContents.ctTextBox) = TextDate
        logic.伝票区分.Control(Field.ControlTypeContents.ctRadioButtonList) = RadioDenpyoKubun
        logic.得意先コード.Control(Field.ControlTypeContents.ctTextBox) = TextTokuisakiCode
        logic.工事コード.Control(Field.ControlTypeContents.ctTextBox) = TextKoujiCode
        logic.工事内部コード.Control(Field.ControlTypeContents.ctTextBox) = TextKoujiNaibuCode
        logic.摘要1.Control(Field.ControlTypeContents.ctTextBox) = TextTekiyou1
        logic.摘要2.Control(Field.ControlTypeContents.ctTextBox) = TextTekiyou2
        logic.備考.Control(Field.ControlTypeContents.ctTextBox) = TextBiko
        logic.伝票発行区分.Control(Field.ControlTypeContents.ctTextBox) = TextHakkoKubun
        logic.請求番号.Control(Field.ControlTypeContents.ctTextBox) = TextSeikyuNo

        logic.登録日時.Control(Field.ControlTypeContents.ctTextBox) = TextRegDate
        logic.登録者ID.Control(Field.ControlTypeContents.ctTextBox) = TextRegUserID
        logic.更新日時.Control(Field.ControlTypeContents.ctTextBox) = TextUpdDate
        logic.更新者ID.Control(Field.ControlTypeContents.ctTextBox) = TextUpdUserID

        logic.売上作成区分.Control(Field.ControlTypeContents.ctTextBox) = TextSakuseiKubun
        TextTokuisakiName.MaxLength = 80
        TextKoujiName.MaxLength = 80

        logic.Start()

        'RadioDenpyoKubun.KubunNoListDataName

        ' 明細の設定
        InputTable1.Headers = {"計算区分" _
                             , "商品コード" _
                             , "商品名／備考／備考(社内)" _
                             , "数量" _
                             , "単位" _
                             , "単価" _
                             , "基本料金" _
                             , "金額" _
                             , "開始／終了日付" _
                             , "搬入NO／返納日付"
                              }                 ' 明細のﾍｯﾀﾞﾃｷｽﾄ

        InputTable1.AddColumnControl("計算区分", "DropKeisanKubun", InputTable.ControlType.DropDownList)
        InputTable1.AddColumnControl("商品コード", "TextShouhinCD", InputTable.ControlType.TextBox)
        InputTable1.AddColumnControl("商品名／備考／備考(社内)", "TextShouhinName", InputTable.ControlType.TextBox)
        InputTable1.AddColumnControl("商品名／備考／備考(社内)", "LineFeed1", InputTable.ControlType.LineFeed)          ' ***** コントロールを改行
        InputTable1.AddColumnControl("商品名／備考／備考(社内)", "TextBikoMeisai", InputTable.ControlType.TextBox)
        InputTable1.AddColumnControl("商品名／備考／備考(社内)", "LineFeed4", InputTable.ControlType.LineFeed)          ' ***** コントロールを改行
        InputTable1.AddColumnControl("商品名／備考／備考(社内)", "TextBikoShanai", InputTable.ControlType.TextBox)
        InputTable1.AddColumnControl("数量", "TextSuryo", InputTable.ControlType.TextBox)
        InputTable1.AddColumnControl("単位", "TextTaniKubun", InputTable.ControlType.DropDownList)
        InputTable1.AddColumnControl("単価", "TextTanka", InputTable.ControlType.TextBox)
        InputTable1.AddColumnControl("基本料金", "TextKihonRyoukin", InputTable.ControlType.TextBox)
        InputTable1.AddColumnControl("金額", "TextKingaku", InputTable.ControlType.TextBox)
        InputTable1.AddColumnControl("開始／終了日付", "TextKaishiDate", InputTable.ControlType.TextBox)
        InputTable1.AddColumnControl("開始／終了日付", "LineFeed2", InputTable.ControlType.LineFeed)        ' ***** コントロールを改行
        InputTable1.AddColumnControl("開始／終了日付", "TextShuryoDate", InputTable.ControlType.TextBox)
        InputTable1.AddColumnControl("開始／終了日付", "LineFeed5", InputTable.ControlType.LineFeed)        ' ***** コントロールを改行
        InputTable1.AddColumnControl("開始／終了日付", "TextNisu", InputTable.ControlType.TextBox)
        InputTable1.AddColumnControl("搬入NO／返納日付", "TextHannyuNO", InputTable.ControlType.TextBox)
        InputTable1.AddColumnControl("搬入NO／返納日付", "LineFeed3", InputTable.ControlType.LineFeed)      ' ***** コントロールを改行
        InputTable1.AddColumnControl("搬入NO／返納日付", "TextHennoDate", InputTable.ControlType.TextBox)
        InputTable1.AddColumnControl("搬入NO／返納日付", "TextBikoDaicho", InputTable.ControlType.TextBox)

        If IsPostBack = False Then
            'InputTable1.RowsCount = logic.List.Length + 1
            InputTable1.RowsCount = 11          ' 使用したい行数+1の数値
        End If

        InputTable1.AddLineButton = InputTable.AddLineButtonType.WithLineCountTextBox           ' 行追加ﾎﾞﾀﾝ
        InputTable1.AllowDeleteButton = True                                                    ' 行削除ﾎﾞﾀﾝ
        InputTable1.AllowInsertButton = True                                                    ' 行挿入ﾎﾞﾀﾝ
        InputTable1.AllowUpDownButton = True                                                    ' 行移動ﾎﾞﾀﾝ
        InputTable1.AllowLineNumber = True                                                      ' 行番号の表示
        InputTable1.MaxRowsCount = 0                                                            ' 明細の最大行数(0を指定すると制限しない)
        InputTable1.RowButtonPosition = TextAlign.Right                                         ' 行操作ﾎﾞﾀﾝのｾﾙの位置
        InputTable1.SetupControls()                                                             ' 明細テーブル生成

        ' Finderの読み込み
        Base.LoadFinder("TokuisakiMasterFinder", "CommonFinder", "M得意先検索", "得意先コード")
        Base.LoadFinder("UriageDenpyoFinder", "CommonFinder", "T売上検索", "売上番号")
        Base.LoadFinder("KoujiMasterFinder", "CommonFinder", "M工事検索", "_取得値")
        Base.LoadFinder("ShouhinMasterFinder", "CommonFinder", "M商品検索", "商品コード")
        Base.LoadFinder("UriageMeisaiFinder", "UriageMeisaiFinder")

        MyBase.OnInit(e)
    End Sub

    Protected Overrides Sub OnLoad(e As System.EventArgs)

        If Not IsPostBack Then
            read_data(True)
        End If

        MyBase.OnLoad(e)
    End Sub

    Private Sub AccountEditor_PreRender(sender As Object, e As EventArgs) Handles Me.PreRender

        ButtonDelete.Visible = (logic.EditMode.Value = Field.EditModeContents.emUpdateMode)

    End Sub

    Public Overrides Sub FinderClose(findername As String, result As System.Collections.Hashtable, resulttargetid As String)
        Dim aaa As String = ""

        Select Case findername
            Case "ShouhinMasterFinder"
                'CType(InputTable1.FindControl(resulttargetid), TextBox).Text = result("選択値")

                'logic.List(Text.CVal(resulttargetid) - 1).GetShohinMaster()
                logic.SetShouhinList(result("選択値"))

            Case "UriageMeisaiFinder"
                If Not result("選択値") Is Nothing Then
                    logic.ReadMeisai(result("選択値"))
                End If
        End Select
    End Sub

    Public Overrides Sub FinderOpen()
        Dim aaa As String = ""

    End Sub

    Public Overrides Sub MessageBoxClose(messageboxname As String, result As Dbs.Asphalt.UI.ModuleBase.MessageBoxResult)

    End Sub

    Public Overrides ReadOnly Property PageTitle As String
        Get
            Return logic.Title
        End Get
    End Property

    Public Overrides ReadOnly Property RunAuth As Dbs.Asphalt.Core.SystemLogic.Security.emUserAuth
        Get
            Return SystemLogic.Security.emUserAuth.一般
        End Get
    End Property

    Private Sub read_data(read_after_clear As Boolean)
        If Not logic.ReadMainData(True) Then
            Base.ErrorMessage = logic.LastError
        End If

    End Sub

    Private Sub InputTable1_RowRender(sender As Object, e As RowEventArgs) Handles InputTable1.RowRender
        ' ﾏﾈｰｼﾞｬのﾌｨｰﾙﾄﾞ関連付け
        logic.List(e.RowIndex - 1).計算区分.Control(Field.ControlTypeContents.ctDropDownList) = e.FindControl("DropKeisanKubun")
        logic.List(e.RowIndex - 1).商品コード.Control(Field.ControlTypeContents.ctTextBox) = e.FindControl("TextShouhinCD")
        logic.List(e.RowIndex - 1).商品名.Control(Field.ControlTypeContents.ctTextBox) = e.FindControl("TextShouhinName")
        logic.List(e.RowIndex - 1).数量.Control(Field.ControlTypeContents.ctTextBox) = e.FindControl("TextSuryo")
        logic.List(e.RowIndex - 1).単価.Control(Field.ControlTypeContents.ctTextBox) = e.FindControl("TextTanka")
        logic.List(e.RowIndex - 1).基本料金.Control(Field.ControlTypeContents.ctTextBox) = e.FindControl("TextKihonRyoukin")
        logic.List(e.RowIndex - 1).金額.Control(Field.ControlTypeContents.ctTextBox) = e.FindControl("TextKingaku")
        logic.List(e.RowIndex - 1).単位区分.Control(Field.ControlTypeContents.ctDropDownList) = e.FindControl("TextTaniKubun")
        logic.List(e.RowIndex - 1).備考明細.Control(Field.ControlTypeContents.ctTextBox) = e.FindControl("TextBikoMeisai")
        logic.List(e.RowIndex - 1).備考社内.Control(Field.ControlTypeContents.ctTextBox) = e.FindControl("TextBikoShanai")
        logic.List(e.RowIndex - 1).備考台帳.Control(Field.ControlTypeContents.ctTextBox) = e.FindControl("TextBikoDaicho")
        logic.List(e.RowIndex - 1).搬入番号.Control(Field.ControlTypeContents.ctTextBox) = e.FindControl("TextHannyuNO")
        logic.List(e.RowIndex - 1).開始日付.Control(Field.ControlTypeContents.ctTextBox) = e.FindControl("TextKaishiDate")
        logic.List(e.RowIndex - 1).終了日付.Control(Field.ControlTypeContents.ctTextBox) = e.FindControl("TextShuryoDate")
        logic.List(e.RowIndex - 1).返納日付.Control(Field.ControlTypeContents.ctTextBox) = e.FindControl("TextHennoDate")
        logic.List(e.RowIndex - 1).日数.Control(Field.ControlTypeContents.ctTextBox) = e.FindControl("TextNisu")

        ' ｺﾝﾄﾛｰﾙのﾌﾟﾛﾊﾟﾃｨ

        ' 商品CD
        CType(e.FindControl("TextShouhinCD"), TextBox).FinderButton = True
        CType(e.FindControl("TextShouhinCD"), TextBox).AutoPostBack = True
        AddHandler CType(e.FindControl("TextShouhinCD"), TextBox).TextChanged, AddressOf TextShouhinCD_TextChanged
        AddHandler CType(e.FindControl("TextShouhinCD"), TextBox).FinderButtonClicked, AddressOf TextShouhinCD_FinderButtonClicked

        ' 商品名
        CType(e.FindControl("TextShouhinName"), TextBox).Columns = 50
        CType(e.FindControl("TextShouhinName"), TextBox).AutoComplete = True
        CType(e.FindControl("TextShouhinName"), TextBox).AutoCompleteListMethod = "ShouhinCode"
        'CType(e.FindControl("TextShouhinName"), TextBox).AutoCompleteListMethod = "LeaseItem(" & "001017" & "," & "0000000008" & "," & "" & ")"
        'CType(e.FindControl("TextShouhinName"), Dbs.Asphalt.UI.TextBox).AutoCompleteListMethod = "LeaseItem(" & "'001017'" & "," & "'0000000008'" & "," & "''" & ")"
        CType(e.FindControl("TextShouhinName"), TextBox).AutoPostBack = True
        AddHandler CType(e.FindControl("TextShouhinName"), TextBox).TextChanged, AddressOf TextShouhinName_TextChanged

        ' 数量
        CType(e.FindControl("TextSuryo"), TextBox).AutoPostBack = True
        AddHandler CType(e.FindControl("TextSuryo"), TextBox).TextChanged, AddressOf TextCalc_TextChanged

        ' 単価
        CType(e.FindControl("TextTanka"), TextBox).AutoPostBack = True
        AddHandler CType(e.FindControl("TextTanka"), TextBox).TextChanged, AddressOf TextCalc_TextChanged

        ' 基本料金
        CType(e.FindControl("TextKihonRyoukin"), TextBox).AutoPostBack = True
        AddHandler CType(e.FindControl("TextKihonRyoukin"), TextBox).TextChanged, AddressOf TextCalc_TextChanged

        ' 開始日付
        CType(e.FindControl("TextKaishiDate"), TextBox).AutoPostBack = True
        AddHandler CType(e.FindControl("TextKaishiDate"), TextBox).TextChanged, AddressOf TextCalc_TextChanged

        ' 終了日付
        CType(e.FindControl("TextShuryoDate"), TextBox).AutoPostBack = True
        AddHandler CType(e.FindControl("TextShuryoDate"), TextBox).TextChanged, AddressOf TextCalc_TextChanged

        ' 搬入NO
        CType(e.FindControl("TextHannyuNO"), TextBox).FinderButton = True
        AddHandler CType(e.FindControl("TextHannyuNO"), TextBox).FinderButtonClicked, AddressOf TextHannyuNO_FinderButtonClicked

        ' 備考
        CType(e.FindControl("TextBikoMeisai"), TextBox).Columns = 50
        CType(e.FindControl("TextBikoShanai"), TextBox).Columns = 50

        CType(e.FindControl("TextBikoDaicho"), TextBox).Visible = False

        ' 日数
        CType(e.FindControl("TextNisu"), TextBox).ReadOnly = True
        CType(e.FindControl("TextNisu"), TextBox).TabIndex = -1
        CType(e.FindControl("TextNisu"), TextBox).AddText = "日"
        'CType(e.FindControl("TextNisu"), TextBox).Style = ""
    End Sub

    Protected Sub ButtonRegist_Click(sender As Object, e As System.Web.UI.ImageClickEventArgs) Handles ButtonRegist.Click
        If Not logic.WriteMainData() Then
            Base.ErrorMessage = logic.LastError
            Return
        End If

        Base.Message = logic.LastMessage

        read_data(RecordSelector.ClearItems)
    End Sub

    Protected Sub TextNo_TextChanged(sender As Object, e As System.EventArgs) Handles TextNo.TextChanged
        read_data(True)

        Dim str得意先名 As String = ""
        Dim str工事名 As String = ""
        str得意先名 = logic.MasterName.AnyTableName("M得意先", "得意先コード", "得意先名", TextTokuisakiCode.Text)
        str工事名 = logic.MasterName.AnyTableName("M工事", "工事内部コード", "工事名", TextKoujiNaibuCode.Text)
        TextTokuisakiName.Text = str得意先名
        TextKoujiName.Text = str工事名
    End Sub

    Private Sub TextKoujiCode_TextChanged(sender As Object, e As EventArgs) Handles TextKoujiCode.TextChanged, TextTokuisakiCode.TextChanged
        Dim str得意先名 As String = ""
        Dim str工事名 As String = ""
        Dim str内部CD As String = ""
        Dim str摘要1 As String = ""
        Dim str摘要2 As String = ""

        Dim strWHERE As String = ""
        Dim strAND As String = ""
        strWHERE += strAND & "削除区分     = '0'" : strAND = " AND "
        strWHERE += strAND & "完了区分    <> '1'" : strAND = " AND "
        strWHERE += strAND & "得意先コード = '" & TextTokuisakiCode.DbText & "'" : strAND = " AND "

        str得意先名 = logic.MasterName.AnyTableName("M得意先", "得意先コード", "得意先名", TextTokuisakiCode.Text)
        str工事名 = logic.MasterName.AnyTableName("M工事", "工事コード", "工事名", TextKoujiCode.Text, strWHERE)
        str内部CD = logic.MasterName.AnyTableName("M工事", "工事コード", "工事内部コード", TextKoujiCode.Text, strWHERE)
        str摘要1 = logic.MasterName.AnyTableName("M工事", "工事コード", "摘要1", TextKoujiCode.Text, strWHERE)
        str摘要2 = logic.MasterName.AnyTableName("M工事", "工事コード", "摘要2", TextKoujiCode.Text, strWHERE)

        TextTokuisakiName.Text = str得意先名
        TextKoujiName.Text = str工事名
        TextKoujiNaibuCode.Text = str内部CD
        TextTekiyou1.Text = str摘要1
        TextTekiyou2.Text = str摘要2

        TextKoujiName.AutoCompleteListMethod = "KoujiCode(" & TextTokuisakiCode.Text & ")"
    End Sub

    Private Sub TextTokuisakiName_TextChanged(sender As Object, e As EventArgs) Handles TextTokuisakiName.TextChanged

        If TextTokuisakiName.Text <> "" Then

            Dim str得意先名 As String = ""
            str得意先名 = logic.MasterName.AnyTableName("M得意先", "得意先コード", "得意先名", TextTokuisakiName.Text)

            If str得意先名 <> "" Then
                TextTokuisakiCode.Text = TextTokuisakiName.Text
                TextKoujiCode_TextChanged(sender, e)
            End If
        End If

    End Sub

    Private Sub TextKoujiName_TextChanged(sender As Object, e As EventArgs) Handles TextKoujiName.TextChanged
        'TextKoujiName.AutoCompleteListMethod = "TokuisakiCode"

        If TextKoujiName.Text <> "" Then

            Dim str工事名 As String = ""
            Dim strWHERE As String = ""
            Dim strAND As String = ""
            strWHERE += strAND & "削除区分     = '0'" : strAND = " AND "
            strWHERE += strAND & "完了区分    <> '1'" : strAND = " AND "
            strWHERE += strAND & "得意先コード = '" & TextTokuisakiCode.DbText & "'" : strAND = " AND "

            str工事名 = logic.MasterName.AnyTableName("M工事", "工事コード", "工事名", TextKoujiName.Text, strWHERE)

            If str工事名 <> "" Then
                TextKoujiCode.Text = TextKoujiName.Text
                TextKoujiCode_TextChanged(sender, e)
            End If
        End If
    End Sub

    Protected Sub TextShouhinCD_TextChanged(sender As Object, e As EventArgs)
        ' 行番号を取得(IDは"TextSuryo_1"のように自動定義される)
        Dim idx As Integer = sender.ID.Split("_")(1)

        ' 商品情報を取得する
        If Not logic.List(idx - 1).GetShohinMaster() Then
            Base.ErrorMessage = logic.List(sender.Parent.Parent.RowIndex).LastError
        End If
    End Sub

    Protected Sub TextShouhinName_TextChanged(sender As Object, e As EventArgs)
        ' 行番号を取得(IDは"TextSuryo_1"のように自動定義される)
        Dim idx As Integer = sender.ID.Split("_")(1)

        ' 商品情報を取得する
        If Not logic.List(idx - 1).GetShohinName() Then
            Base.ErrorMessage = logic.List(sender.Parent.Parent.RowIndex).LastError
        End If
        'If Not logic.List(idx - 1).ReadMeisai(TextDate.Text, TextTokuisakiCode.Text) Then
        '    Base.ErrorMessage = logic.List(sender.Parent.Parent.RowIndex).LastError
        'End If
    End Sub

    Protected Sub TextCalc_TextChanged(sender As Object, e As EventArgs)
        ' 行番号を取得(IDは"TextSuryo_1"のように自動定義される)
        Dim idx As Integer = sender.ID.Split("_")(1)

        ' 金額の計算
        If Not logic.List(idx - 1).CalcKingaku() Then
            Base.ErrorMessage = logic.List(sender.Parent.Parent.RowIndex).LastError
        End If

        ' 日数の表示
        If Not logic.List(idx - 1).GetNisu() Then
            Base.ErrorMessage = logic.List(sender.Parent.Parent.RowIndex).LastError
        End If
    End Sub

    Private Sub TextTokuisakiCode_FinderButtonClicked(sender As Object, e As ImageClickEventArgs) Handles TextTokuisakiCode.FinderButtonClicked
        ' 得意先検索を開く
        Base.OpenFinder("TokuisakiMasterFinder", , sender.ID)
    End Sub
    Private Sub TextKoujiCode_FinderButtonClicked(sender As Object, e As ImageClickEventArgs) Handles TextKoujiCode.FinderButtonClicked
        ' 工事検索を開く
        Dim param As New Hashtable

        Dim strWHERE As String = ""
        Dim strAND As String = ""
        strWHERE += strAND & "削除区分     = '0'" : strAND = " AND "
        strWHERE += strAND & "完了区分    <> '1'" : strAND = " AND "
        strWHERE += strAND & "得意先コード = '" & TextTokuisakiCode.Text & "'" : strAND = " AND "

        param("固定条件") = strWHERE
        Base.OpenFinder("KoujiMasterFinder", param, sender.ID)
    End Sub

    Protected Sub TextShouhinCD_FinderButtonClicked(sender As Object, e As ImageClickEventArgs)
        Dim param As New Hashtable

        ' 伝票区分:搬入の場合は納品ﾌｧｲﾝﾀﾞｰ
        ' それ以外は商品検索を開く
        If RadioDenpyoKubun.SelectedValue = 3 Then

            param("複数選択") = "可能"
            param("得意先コード") = TextTokuisakiCode.Text
            param("工事コード") = TextKoujiCode.Text
            param("工事内部コード") = TextKoujiNaibuCode.Text

            param("得意先名") = TextTokuisakiName.Text
            param("工事名") = TextKoujiName.Text

            ' 固定条件を可視化して変更可能なので開く前に初期化する
            Base.InitFinder("UriageMeisaiFinder")
            Base.OpenFinder("UriageMeisaiFinder", param)
        Else
            param("複数選択") = "可能"
            Base.OpenFinder("ShouhinMasterFinder", param)
        End If
    End Sub

    Protected Sub TextHannyuNO_FinderButtonClicked(sender As Object, e As ImageClickEventArgs)
        ' 売上検索を開く
        Dim param As New Hashtable

        Dim strWHERE As String = ""
        Dim strAND As String = ""
        strWHERE += strAND & "伝票区分     = '1'" : strAND = " AND "
        strWHERE += strAND & "工事内部コード = '" & TextKoujiNaibuCode.Text & "'" : strAND = " AND "

        param("固定条件") = strWHERE
        Base.OpenFinder("UriageDenpyoFinder", param, sender.ID)
    End Sub

    Protected Sub ButtonDelete_Click(sender As Object, e As ImageClickEventArgs) Handles ButtonDelete.Click
        If Not logic.DeleteMainData Then
            Base.ErrorMessage = logic.LastError
            Return
        End If

        Base.Message = logic.LastMessage

        TextNo.Text = ""
        read_data(True)
    End Sub

    Protected Sub ButtonNew_Click(sender As Object, e As ImageClickEventArgs) Handles ButtonNew.Click
        TextNo.Text = ""
        read_data(RecordSelector.ClearItems)
    End Sub

    Protected Sub ButtonFind_Click(sender As Object, e As ImageClickEventArgs) Handles ButtonFind.Click
        Dim param As New Hashtable
        Base.OpenFinder("UriageDenpyoFinder", param, "TextNo")
    End Sub
End Class
