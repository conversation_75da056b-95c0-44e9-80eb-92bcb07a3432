﻿<%@ Control Language="VB" AutoEventWireup="false" CodeFile="UriageDenpyoEditor2.ascx.vb" Inherits="UriageDenpyoEditor2" %>
<%@ Register Assembly="Dbs.Asphalt.UI" Namespace="Dbs.Asphalt.UI" TagPrefix="cc1" %>
<%@ Register Src="~/controls/common/RecordSelector.ascx" TagPrefix="uc1" TagName="RecordSelector" %>
<%@ Register Src="~/controls/common/ReportDataViewer.ascx" TagPrefix="uc1" TagName="ReportDataViewer" %>
<%@ Register Src="~/controls/common/InputTable.ascx" TagPrefix="uc1" TagName="InputTable" %>

<cc1:ItemFrame runat="server" ID="PanelBase" Text="基本情報">
  <div class="leftpane">
    <cc1:ItemPanel runat="server" Text="売上番号">
      <cc1:TextBox runat="server" ID="TextNo" AutoPostBack="true" ></cc1:TextBox><br />
      <span class="guide">売上番号を空白で登録すると、自動的にコードを割り当てて追加登録されます。</span>
    </cc1:ItemPanel>

    <cc1:ItemPanel runat="server" Text="登録">
      <cc1:TextBox runat="server" ID="TextRegDate" ViewOnly="true"></cc1:TextBox>&nbsp;
      <cc1:TextBox runat="server" ID="TextRegUserID" ViewOnly="true"></cc1:TextBox>
    </cc1:ItemPanel>
    <cc1:ItemPanel runat="server" Text="更新">
      <cc1:TextBox runat="server" ID="TextUpdDate" ViewOnly="true"></cc1:TextBox>&nbsp;
      <cc1:TextBox runat="server" ID="TextUpdUserID" ViewOnly="true"></cc1:TextBox>
    </cc1:ItemPanel>
  </div>
  <div class="rightpane">
    <cc1:EditMode runat="server" ID="EditMode"></cc1:EditMode><br />
    <uc1:RecordSelector runat="server" ID="RecordSelector" DataKeyControlID="TextNo" DataName="T売上HD" KeyIsNumeric="false" KeyName="売上番号" AllowClearCheckBox="true" />
  </div>
  <div class="clear"></div>

  <hr />

  <cc1:ItemPanel runat="server" Text="搬入日付">
    <cc1:TextBox runat="server" ID="TextDate"></cc1:TextBox>
  </cc1:ItemPanel>

  <cc1:ItemPanel runat="server" Text="伝票区分">
    <cc1:RadioButtonList runat="server" ID="RadioDenpyoKubun" RepeatDirection="Horizontal" RepeatLayout="Flow"></cc1:RadioButtonList>
  </cc1:ItemPanel>


  <cc1:ItemPanel runat="server" Text="得意先コード">
<%--      <cc1:TextBox runat="server" ID="TextTokuisakiCode" FinderButton="true" MasterTableName="M得意先" MasterGetName="得意先名" MasterKeyName="得意先コード" AutoPostBack="true"></cc1:TextBox><br />--%>
      <cc1:TextBox runat="server" ID="TextTokuisakiCode" FinderButton="true" AutoPostBack="true"></cc1:TextBox>
      <cc1:TextBox runat="server" ID="TextTokuisakiName" AutoPostBack="true" AutoComplete="true" AutoCompleteListMethod="TokuisakiCode" ></cc1:TextBox>
      <br />
  </cc1:ItemPanel>
  <cc1:ItemPanel runat="server" Text="工事コード">
      <cc1:TextBox runat="server" ID="TextKoujiCode" FinderButton="true" AutoPostBack="true"></cc1:TextBox>
      <cc1:TextBox runat="server" ID="TextKoujiName" AutoPostBack="true" AutoComplete="true" AutoCompleteListMethod="KoujiCode" ></cc1:TextBox>
      <br />
      <span class="guide">※得意先名、工事名は変更しても登録されません。</span>
  </cc1:ItemPanel>

  <hr />

  <cc1:ItemPanel runat="server" Text="摘要1">
    <cc1:TextBox runat="server" ID="TextTekiyou1" ></cc1:TextBox><br />
  </cc1:ItemPanel>

  <cc1:ItemPanel runat="server" Text="摘要2">
    <cc1:TextBox runat="server" ID="TextTekiyou2" ></cc1:TextBox><br />
  </cc1:ItemPanel>

  <cc1:TextBox runat="server" ID="TextKoujiNaibuCode" Visible="false" ></cc1:TextBox>
  <cc1:TextBox runat="server" ID="TextBiko" Visible="false" ></cc1:TextBox>
  <cc1:TextBox runat="server" ID="TextHakkoKubun" Visible="false" ></cc1:TextBox>
  <cc1:TextBox runat="server" ID="TextSeikyuNo" Visible="false" ></cc1:TextBox>
  <cc1:TextBox runat="server" ID="TextSakuseiKubun" Visible="false" ></cc1:TextBox>
</cc1:ItemFrame>

  <uc1:InputTable runat="server" ID="InputTable1" >
  </uc1:InputTable>

<asp:panel runat="server" ID="PanelToolbar">
  <cc1:ToolButton runat="server" ButtonImage="biSave" Text="保存" ID="ButtonRegist" PostbackTrriger="true" PostbackTrrigerFreeze="true" />
  <cc1:ToolButton runat="server" ButtonImage="biDelete" Text="削除" ID="ButtonDelete" PostbackTrriger="true" PostbackTrrigerFreeze="true" />
  <cc1:ToolButton runat="server" ButtonImage="biNew" Text="新規追加" ID="ButtonNew" />
  <cc1:ToolButton runat="server" ButtonImage="biFinder" Text="検索" ID="ButtonFind" />
</asp:panel>
