﻿<%@ Page Language="VB" Inherits="Dbs.Asphalt.UI.PageBase" %>
<%@ Register Assembly="Dbs.Asphalt.Core" Namespace="Dbs.Asphalt.Core" TagPrefix="cc1" %>
<%@ Register Assembly="Dbs.Asphalt.UI" Namespace="Dbs.Asphalt.UI" TagPrefix="cc2" %>
<%--<%@ Register src="controls/common/InstantMessenger.ascx" tagname="InstantMessenger" tagprefix="uc1" %>--%>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="ja" >
<head runat="server">
  <meta name="robots" content="noindex" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <title><%=IIf(PageTitle = "", "", PageTitle & " - ")%><%=Dbs.Asphalt.Core.Common.Defines.AppTitle%></title>
  <link href="css/default.css?20220415" rel="stylesheet" type="text/css" />
  <link href="css/calendar.css?20150820" rel="stylesheet" type="text/css" />
  <link href="css/dropdownex.css?20150820" rel="stylesheet" type="text/css" />
  <link href="css/dropdownmenu.css?20150820" rel="stylesheet" type="text/css" />
  <%--<link href="css/instantmessenger.css" rel="stylesheet" type="text/css" />--%>
  <%--<link href="css/jquery-ui.css?20150820" rel="stylesheet" type="text/css" />--%>
  <link media="(max-width: 640px)" href="css/default_mobile.css?20150820" rel="stylesheet" type="text/css" />
  <link href="css/custom.css" rel="stylesheet" type="text/css" />
  <link rel="shortcut icon" href="favicon.png" />
</head>
<body>
  <form id="form1" runat="server" autocomplete="off" >
    <asp:ScriptManager ID="ScriptManager" runat="server" EnableScriptGlobalization="true" AsyncPostBackTimeout="1800"></asp:ScriptManager>

    <asp:Panel runat="server" ID="PanelCommonBar" CssClass="common_bar">
      <asp:PlaceHolder ID="PlaceTitleBar" runat="server"></asp:PlaceHolder>
      <h1><%=PageTitle%></h1>
    </asp:Panel>

    <%--インスタントメッセージ用パネル--%>
    <%--<uc1:InstantMessenger ID="InstantMessenger" runat="server" />--%>

  <asp:UpdatePanel runat="server" ID="UpdateMainPanel" UpdateMode="Conditional">
    <ContentTemplate>

      <%--UpdatePanelのPostBack時表示スタイル(透明DIVで埋めて画面全体をクリック不可にする)--%>
      <asp:UpdateProgress runat="server" ID="UpdateProgress" AssociatedUpdatePanelID="UpdateMainPanel" DisplayAfter="0">
        <ProgressTemplate>
          <div class="progress_background">
          </div>
        </ProgressTemplate>
      </asp:UpdateProgress>
    
      <%--「処理中...お待ちください」表示--%>
      <div id="progress_message" class="progress_message">
        <div class="progress_background_wait"></div>
        <div class="progress_message_innner"></div>
      </div>

      <%--メッセージボックス(はい／いいえ)表示--%>
      <asp:Panel runat="server" ID="PanelMessageBox" Visible="False">
        <div class="messagebox_background"></div>
        <div class="messagebox_frame">
          <div class="messagebox_frame_inner">
            <div>
              <asp:label runat="server" ID="LabelMessageBox" Text=""></asp:label>
            </div>
            <hr />
            <div style="text-align: center">
              <asp:button runat="server" ID="ButtonYes" text="はい" CssClass="buttonb" onclick="MessageBoxButton_Click" />
              <asp:button runat="server" ID="ButtonNo" text="いいえ" CssClass="buttonr" onclick="MessageBoxButton_Click" />
            </div>
          </div>
        </div>
      </asp:Panel>

      <asp:Panel runat="server" ID="PanelMainHolder" CssClass="mainframe">

        <%--ツールバーロード用パネル--%>
        <asp:Panel runat="server" ID="PanelFixedToolbar" CssClass="toolbar_body">
          <asp:PlaceHolder runat="server" ID="PlaceToolbar"></asp:PlaceHolder>
        </asp:Panel>

        <asp:Panel ID="PlaceMainOuter" runat="server" CssClass="main_place_outer">
          <asp:Panel ID="PlaceMainInner" runat="server" CssClass="main_place">
            <asp:button runat="server" ID="ButtonJumpTop" CssClass="jumptopbutton" TabIndex="-1" />

            <%--エラーメッセージパネル--%>
            <asp:Panel runat="server" ID="PanelError" Visible="False" CssClass="errorframe">
              <div class="error_message_body">
                <div class="leftpane"><asp:Label runat="server" Text="" ID="LabelError"></asp:Label></div>
                <div class="rightpane"><asp:LinkButton runat="server" ID="ButtonCloseError" Text="×"></asp:LinkButton></div>
                <div class="clear"></div>
              </div>
            </asp:Panel>
            <%--メッセージパネル--%>
            <asp:Panel runat="server" ID="PanelMessage" Visible="False" CssClass="messageframe">
              <div class="message_body">
                <div class="leftpane"><asp:Label runat="server" Text="" ID="LabelMessage"></asp:Label></div>
                <div class="rightpane"><asp:LinkButton runat="server" ID="ButtonCloseMessage" Text="×"></asp:LinkButton></div>
                <div class="clear"></div>
              </div>
            </asp:Panel>
            <%--メインパネル(各種処理画面ロード用)--%>
            <asp:PlaceHolder ID="PlaceMain" runat="server">
            </asp:PlaceHolder>
            <%--各種検索画面ロード用パネル--%>
            <asp:PlaceHolder ID="PlaceFinder" runat="server">
            </asp:PlaceHolder>
          </asp:Panel>
        </asp:Panel>
        <div class="clear"></div>

      </asp:Panel>

    </ContentTemplate>
  </asp:UpdatePanel>

    <asp:Panel runat="server" ID="PanelFooter" CssClass="footerframe">
      <%=Common.Defines.AppTitle%>&nbsp;Version<%=Common.Defines.AppVersion%>&nbsp;-&nbsp;<%=Common.Defines.AppCopyright%><br /><asp:Label runat="server" ID="LabelPrevLoginInfo" TabIndex="-1"></asp:Label>
    </asp:Panel>
  </form>
</body>
</html>
