﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="AngleSharp" version="1.0.1" targetFramework="net48" />
  <package id="BouncyCastle.Cryptography" version="2.2.1" targetFramework="net48" />
  <package id="MailKit" version="4.3.0" targetFramework="net48" />
  <package id="MimeKit" version="4.3.0" targetFramework="net48" />
  <package id="Portable.BouncyCastle" version="1.9.0" targetFramework="net48" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net452" requireReinstallation="true" />
  <package id="System.Formats.Asn1" version="7.0.0" targetFramework="net48" />
  <package id="System.Memory" version="4.5.5" targetFramework="net48" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net48" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" targetFramework="net48" />
  <package id="System.Text.Encoding.CodePages" version="7.0.0" targetFramework="net48" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net48" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="net48" />
</packages>