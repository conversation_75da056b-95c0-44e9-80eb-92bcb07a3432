﻿<%@ Page Language="VB" Inherits="Dbs.Asphalt.UI.PageThread" %>
<%@ Register Assembly="Dbs.Asphalt.Core" Namespace="Dbs.Asphalt.Core" TagPrefix="cc1" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" >
<head runat="server">
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="robots" content="noindex" />
  <meta name="viewport" content="width=1100" />
  <title></title>
  <link href="css/default.css?20150911" rel="stylesheet" type="text/css" />
  <link href="css/custom.css" rel="stylesheet" type="text/css" />
</head>
<body>
  <form id="form1" runat="server" autocomplete="off">
    <asp:ScriptManager ID="ScriptManager" runat="server" EnableScriptGlobalization="true" AsyncPostBackTimeout="1800"></asp:ScriptManager>
    <asp:UpdatePanel runat="server" ID="UpdateMainPanel">
      <ContentTemplate>
        <asp:Timer runat="server" ID="TimerMain" Interval="1000"></asp:Timer>

        <asp:Panel runat="server" ID="PanelProgress" CssClass="progress_body" Visible="false">
          <div style="text-align:center; font-size: 120%; font-weight: bold;"><asp:Label runat="server" ID="LabelTitle"></asp:Label></div>
          <hr />
          <asp:Label runat="server" ID="LabelProgress"></asp:Label>
          <asp:Panel runat="server" ID="PanelProgressBarFrame" CssClass="progress_bar_frame">
            <asp:Panel runat="server" ID="PanelProgressBar" CssClass="progress_bar"></asp:Panel>
          </asp:Panel>
          <p style="text-align: center;"><asp:Button runat="server" ID="ButtonDownload" Visible="false" Text="ダウンロード" CssClass="button"></asp:Button></p>
        </asp:Panel>
        <asp:Label runat="server" ID="LabelNotice" CssClass="notice_message_mini">処理が完了するまで、このウィンドウを閉じないで下さい。<br />他の処理は実行できます。</asp:Label>
      </ContentTemplate>
    </asp:UpdatePanel>
  </form>
</body>
</html>
