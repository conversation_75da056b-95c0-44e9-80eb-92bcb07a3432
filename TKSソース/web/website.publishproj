<?xml version="1.0" encoding="utf-8"?>

<!--

***********************************************************************************************
website.publishproj

警告: このファイルは Web 公開操作に使用されるため、変更しないでください。

Copyright (C) Microsoft Corporation. All rights reserved.

***********************************************************************************************
-->

<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>10.0.30319</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{a6c224b8-1f72-4b8e-b0e0-2da8ca6fd02f}</ProjectGuid>
    <SourceWebPhysicalPath>$(MSBuildThisFileDirectory)</SourceWebPhysicalPath>
    <SourceWebVirtualPath>/web</SourceWebVirtualPath>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <SourceWebProject></SourceWebProject>
    <SourceWebMetabasePath></SourceWebMetabasePath>
  </PropertyGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <!-- for VS2010 we need to use 10.5 but for VS2012+ we should use VisualStudioVersion -->
    <WebPublishTargetsVersion Condition=" '$(WebPublishTargetsVersion)' =='' and '$(VisualStudioVersion)' == 10.0 ">10.5</WebPublishTargetsVersion>
    <WebPublishTargetsVersion Condition=" '$(WebPublishTargetsVersion)'=='' ">$(VisualStudioVersion)</WebPublishTargetsVersion>

    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(WebPublishTargetsVersion)</VSToolsPath>
    <_WebPublishTargetsPath Condition=" '$(_WebPublishTargetsPath)'=='' ">$(VSToolsPath)</_WebPublishTargetsPath>
    <AssemblyFileVersion Condition="'$(AssemblyFileVersion)' == ''">1.0.0.0</AssemblyFileVersion>
    <AssemblyVersion Condition="'$(AssemblyVersion)' == ''">1.0.0.0</AssemblyVersion>
  </PropertyGroup>
  <ItemGroup>
    <AssemblyAttributes Include="AssemblyFileVersion">
      <Value>$(AssemblyFileVersion)</Value>
    </AssemblyAttributes>
    <AssemblyAttributes Include="AssemblyVersion">
      <Value>$(AssemblyVersion)</Value>
    </AssemblyAttributes>
  </ItemGroup>
  <Import Project="$(_WebPublishTargetsPath)\Web\Microsoft.WebSite.Publishing.targets" />
</Project>